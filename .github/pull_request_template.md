## Summary
### Jira ticket
BUG=LSS-xxxxx,REVIEW-xxxxx

### Change Type
Feature / Bug fix / Maintenance

- [ ] Change is lixed by `lss-xxxxx` (If applicable)

### DMRC
<!--- For the areas of changes that require DMRC review,
please refer to [LSS DMRC Guide](http://go/lssdmrc) --->

- [ ] Schema change requires DMRC review.

### Background context / Issue Description


### Root Cause (Only required for Bug fix)


### This PR implements the following
- 

### Notes to the reviewers (e.g. any known defect/TODOs/special rollout plan)
- 


* * * * *
## ACLs update
<!--- (required) Please request permissions at http://go/aclin for any new or updated downstream calls. 
Make sure to deploy the ACL changes before deploying your PR to EI/Prod. --->

- [ ] ACLin has been updated with correct endpoint permissions

## Testing Done
<!--- (required) Please check off (`[x]` instead of `[ ]`) how this change was tested.
Please follow http://go/sales-api on how to test your changes. 
If the change was tested in an alternative way, please provide details. --->

- [ ] Unit test or Integration test added, especially for the corner case related to Bug fix.
- [ ] Manual Testing through local deploy (Please write down detailed steps and results below)
