name: <PERSON><PERSON><PERSON>

on:
  # Trigger GHA on pull request creation (opened/reopened) and on new commit to the HEAD ref of a pull request (synchronize)
  pull_request:
    types: [opened, synchronize, reopened]

  # Trigger GHA manually, such-as by using the CLI of `$ gh workflow run ...`
  workflow_dispatch: {}

jobs:
  Confetti:
    name: <PERSON><PERSON><PERSON>
    runs-on: [mariner2]
    permissions:
      pull-requests: write
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: <PERSON> <PERSON><PERSON><PERSON>
        uses: linkedin-actions/confetti-action/validate@v1

      - name: Summary Comment
        if: always() # Always run this step, even if the previous step fails
        uses: linkedin-actions/confetti-action/comment@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
