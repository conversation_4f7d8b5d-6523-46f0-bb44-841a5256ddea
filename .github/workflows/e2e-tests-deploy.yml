---
name: E2E Test Deploy

on:
  push:
    paths:
      - 'e2e-tests/**'
    branches:
      - main
      - master
  workflow_dispatch: {}

jobs:
  Post-merge:
    runs-on: linux
    steps:
      - uses: actions/checkout@v4
        with:
          ref: master
      - name: Deploy to Checkly
        uses: linkedin-actions/checkly-actions/checkly-post-merge@v0
        with:
          checklyAccountId: ${{ secrets.CHECKLY_ACCOUNT_ID }}
          checklyApiKey: ${{ secrets.CHECKLY_API_KEY }}
