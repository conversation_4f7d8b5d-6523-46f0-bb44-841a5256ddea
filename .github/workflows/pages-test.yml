name: Github-Pages-Test
on:
  pull_request:
    types: [opened, reopened, synchronize]
    branches:
      - main
      - master
    # This workflow only runs if there are changes to your documentation files
    # Modify the filter below if your documentation files are in a different location
    paths:
      - 'docs/**'
      - '.github/workflows/pages-test.yml'
      - '.linkedin/collaboration/pages.yaml'
  workflow_dispatch: {}

jobs:
  Test:
    name: Github Pages Test
    uses: linkedin-actions/github-pages/.github/workflows/test.yml@v0
