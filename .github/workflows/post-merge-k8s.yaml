# Post-merge workflow
name: Post-merge-k8s

# Controls when the workflow will run.
on:
  push:
    branches: [master, 'BR_HF_*', 'BR_REL_*']
    paths: ['.linkedin/kube/**']

jobs:
  Post-merge-k8s:
    concurrency:
      group: lss-mt
      cancel-in-progress: false
    uses: linkedin-actions/k8s-workflows/.github/workflows/post-merge-manifests.yaml@v1
    secrets: inherit
    with:
      clis: "kubernetes-validate"
