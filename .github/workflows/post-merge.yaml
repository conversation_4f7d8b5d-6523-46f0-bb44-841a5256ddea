# Post-merge workflow
name: Post-merge
run-name: ${{ github.event.inputs.id }}

# Controls when the workflow will run.
on:
  push:
    branches: [master, 'BR_HF_*', 'BR_REL_*']

  # When we open, reopen, synchronize a pull request, post-merge in dry-run mode will be triggered.
  pull_request:
    types: [ opened, reopened, synchronize ]

  workflow_dispatch:
    inputs:
      # These inputs are required for various use cases. Altering them will result in mint validation failure.
      consumer-version:
        description: Version of a consumer for compatibility testing.
        required: false
      execution-category:
        description: Type of execution. e.g. COMPATIBILITY_TESTING
        required: false
      id:
        description: Any string identifier to name this workflow execution.
        required: false
      producer-artifacts-cache-key:
        description: Key to download producer artifacts for compatibility testing.
        required: false
      producer-name:
        description: Name of a producer for compatibility testing.
        required: false
      producer-repo-cache-key:
        description: Key to download producer repository for compatibility testing.
        required: false
      producer-version:
        description: Version of a producer for compatibility testing.
        required: false
      workflow-dispatch-inputs-json:
        description: Workflow dispatch inputs in JSON format.
        required: false
      workflow-source:
        description: Source of the workflow initiation.
        required: false

jobs:
  Post-merge:
    uses: linkedin-actions/ci-workflows/.github/workflows/post-merge.yaml@v1
    secrets: inherit
    with:
      build-configs: true
      local-deploy: true
      build-images: true
      consumer-version: ${{ github.event.inputs.consumer-version }}
      execution-category: ${{ github.event.inputs.execution-category }}
      producer-artifacts-cache-key: ${{ github.event.inputs.producer-artifacts-cache-key }}
      producer-name: ${{ github.event.inputs.producer-name }}
      producer-repo-cache-key: ${{ github.event.inputs.producer-repo-cache-key }}
      producer-version: ${{ github.event.inputs.producer-version }}
      workflow-dispatch-inputs-json: ${{ github.event.inputs.workflow-dispatch-inputs-json }}
      workflow-source: ${{ github.event.inputs.workflow-source }}
