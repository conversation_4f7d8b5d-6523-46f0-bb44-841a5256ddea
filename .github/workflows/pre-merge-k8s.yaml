# Pre-merge workflow
name: Pre-merge-k8s

# Controls when the workflow will run.
on:
  pull_request:
    types: [opened, reopened, synchronize]
    paths: ['.linkedin/kube/**']

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  Pre-merge-validations-k8s:
    name: Pre-merge validations
    uses: linkedin-actions/k8s-workflows/.github/workflows/pre-merge-manifests.yaml@v1
    secrets: inherit
    with:
      clis: "kubernetes-validate"

  # DO NOT CHANGE THIS "Pre-merge" JOB!
  # This is a temporary solution until MPPCX-5837 is resolved on the GitHub side.
  # This job mirrors the result of the one above and is required in order to merge PRs.
  Pre-merge-k8s:
    if: always()

    runs-on: [linux]

    needs: [Pre-merge-validations-k8s]

    steps:
      - name: Pre-merge-k8s Succeeded
        if: ${{ !contains(needs.*.result, 'failure') && !contains(needs.*.result, 'cancelled') }}
        run: exit 0

      - name: Pre-merge-k8s Failed
        if: ${{ contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled') }}
        run: exit 1
