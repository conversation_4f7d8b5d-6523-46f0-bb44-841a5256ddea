# This file is owned by Product Security team. Please reach out to #ask_productsec if you need help.

name: Security Scan

on:
  pull_request:
    types: [opened, synchronize]
  schedule:
    # NOTE: Cron schedule is randomized between repos to spread weekly executions across weekdays.
    - cron: '19 15 * * 6'
env:
  VERSION: '1.0.0'
  LAST_UPDATED: '2024-10-22'
permissions:
  actions: read
  contents: read
  security-events: write
  pull-requests: read

jobs:
  sast-scan:
    uses: linkedin-actions/static-analysis-actions/.github/workflows/sast-scan.yaml@production
    secrets:
      WORKFLOW_GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      G<PERSON>_TOKEN_INFOSEC_SAST_SEMGREP_V1: ${{ secrets.GHA_TOKEN_INFOSEC_SAST_SEMGREP_V1 }}
      GHA_TOKEN_INFOSEC_SAST_CODEQL_V1: ${{ secrets.GHA_TOKEN_INFOSEC_SAST_CODEQL_V1 }}
