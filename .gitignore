.classpath
.gradle
.idea
.project
.settings
.shelf
*.DS_Store
*.iml
*.ipr
*.iws
*/bin/
*/build
**/var/identity.cert
**/var/identity.key
**/var/identity.p12
/.ideaDataSources
/build
/dataSources
/config/external
database/*/build
impl/ds/src/mainGeneratedInternalUrns/
impl/rest-impl/src/mainGeneratedInternalUrns/
impl/rest-impl/src/mainGeneratedRest/
logs/
out/
identity.cert
identity.key
identity.p12


# Include gradle wrapper jar as part of gradlew migration.Please check go/gradlew for more reference.
!gradle/wrapper/gradle-wrapper.jar

# Excluding gradlew.bat by default as part of gradlew migration.
# Users are free to remove this line and include the batch file if they wish.
# Please check go/gradlew for more references.
gradlew.bat

# SonarQube
sonar-reports/
.scannerwork

# These ignores for gRPC generated files are added by the gRPC migration
**/src/*/generatedServiceInteropJava/
**/src/*/idl/
**/src/*/snapshot/

build/

**/node_modules

e2e-tests/tests/snippets

e2e-tests/package.json

# Include c3o (go/c3o) configs
!.linkedin/coverage/*

# jooq generated files
**/src/mainGeneratedJooq/
