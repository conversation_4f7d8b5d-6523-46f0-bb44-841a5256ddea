# Configuration for go/adu.
# See go/adu-cfg for supported configuration properties and values.
# e.g. default_reviewers, ignored_updates, automerge_updates ...

version: 1

update_configs:
  - package_manager: java
    directory: /
    update_schedule: weekly
    automerged_updates:
      - match:
          dependency_name: entitlements-api
          update_type: semver:patch
      - match:
          dependency_name: ligradle-core
          update_type: semver:minor
      - match:
          dependency_name: metadata-utils
          update_type: semver:minor
      - match:
          dependency_name: ligradle-pegasus
          update_type: semver:minor
      - match:
          dependency_name: ligradle-epsilon
          update_type: semver:minor
      - match:
          dependency_name: espresso-util-plugins
          update_type: semver:minor
      - match:
          dependency_name: ucf
          update_type: semver:patch
      - match:
          dependency_name: testfwk
          update_type: semver:patch
      - match:
          dependency_name: security-member-token
          update_type: semver:minor
      - match:
          dependency_name: parseq-espresso-client
          update_type: semver:patch
      - match:
          dependency_name: omni-utils
          update_type: semver:patch
      - match:
          dependency_name: models
          update_type: semver:patch
      - match:
          dependency_name: lss-mt-api
          update_type: semver:patch
      - match:
          dependency_name: lss-admin-backend
          update_type: semver:patch
      - match:
          dependency_name: lsi-common
          update_type: semver:patch
      - match:
          dependency_name: interservice-auth
          update_type: semver:patch
      - match:
          dependency_name: realtime-dispatcher-api
          update_type: semver:patch
      - match:
          dependency_name: avro-schemas
          update_type: semver:patch
      - match:
          dependency_name: .*
          update_type: semver:minor
    grouped_updates:
      - match:
          dependency_name: ligradle-epsilon
          group: wildcards
      - match:
          dependency_name: models
          group: wildcards
      - match:
          dependency_name: entitlements-api
          group: wildcards
      - match:
          dependency_name: com.linkedin.pegasus:.*
          group: pegasus
      - match:
          dependency_name: org.apache.logging.log4j:.*
          group: log4j
      - match:
          dependency_name: org.apache.avro:.*
          group: avro
      - match:
          dependency_name: org.testng:.*
          group: testing
      - match:
          dependency_name: org.assertj:.*
          group: testing
      - match:
          dependency_name: org.mockito:.*
          group: testing
      - match:
          dependency_name: lss-mt-api
          group: wildcards
    ignored_updates:
      - match:
          dependency_name: org.apache.avro:.*
      - match:
          dependency_name: container-deprecated
      - match:
          dependency_name: org.glassfish.jersey.*
          group: jersey
    open_pull_requests_limit: 5
  - package_manager: java
    directory: /
    schedule:
      interval: daily
    automerged_updates:
      - match:
          dependency_name: groot-lss-mt
    open_pull_requests_limit: 5
