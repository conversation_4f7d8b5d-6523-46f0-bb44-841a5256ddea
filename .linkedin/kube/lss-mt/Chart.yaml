apiVersion: v2
name: lss-mt
description: A Helm chart for Kubernetes
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application
# This is the <PERSON><PERSON> chart version and is not related to the version of the multiproduct being deployed.
version: 0.0.1
annotations:
  tenant.linkedin.com/name: ksap
dependencies:
  - name: ksap-base-chart
    version: 4.0.43
    repository: https://artifactory.corp.linkedin.com:8083/artifactory/helm-local/
