# Please see go/lideployment/api-reference for full details of LiDeployment API.
# commented-out values represent the defaults by LiDeployment API.
ksap-base-chart:
  lideployments:
    lss-mt-0:
      spec:
        applicationMetadata:
          applicationName: lss-mt
          productName: lss-mt
          instanceDiscriminator: i001
        resources:
          cpu: 17
          memory: 52Gi
        dataPacks:
          - applicationName: "language-packs"
            productName: "language-packs"
        canary:
          configuration:
            distribution: 1
        # disruptionBudget:
        #   maxUnavailable: 5%
        # updateStrategy user guide http://go/ksap/user-guide/config-update-strategy.
        updateStrategy:
          # # If true, prevent underlying cloneset from rolling out new version to existing pods. Defaults to false.
          # paused: false
          # # The max number of pods that can be scheduled above the desired replicas during an update. Can be a percentage or whole number.
          # maxSurge: 5%
          # # The max number of pods that can be unavailable during an update or scaling.
          # maxUnavailable: 5%
          # # The max time in seconds that a lideployment has to make progress before it is considered as failed.
          # progressDeadlineSeconds: 600
          # # The minimum number of seconds for which a newly created Pod should be ready without any of its container crashing, for it to be considered available.
          # minReadySeconds: 0
          # The number of seconds to wait for a pod to initialize.
          startTimeoutSeconds: 3600
          # # The amount of replicas successfully updated to consider the deployment as successful. Can be a percentage or whole number.
          # successThreshold: 100%
          # The number of seconds to wait for a pod to gracefully shut down after receiving a SIGTERM signal.
          terminationGracePeriodSeconds: 3600
