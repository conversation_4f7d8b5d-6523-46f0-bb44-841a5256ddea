# To learn how to modify this file, please refer to the LCD documentation at https://go/lcd/docs
#
# Please note, unless explicitely configured, the following defaults are applied:
# - ekg is enabled in canary
# - smart canary is enabled for all prod fabrics
# - soak time is 40 minutes for all prod fabrics
# If you have any questions or need help, please reach out to the LCD team in #lcd-support on Slack.
name: lss-mt-default
version: v1
targets:
- clusters:
  - ei-ltx1.DEFAULT
  - ei4.DEFAULT
  - prod-lor1.DEFAULT
  - prod-ltx1.DEFAULT
  - prod-lva1.DEFAULT
  types:
  - k8s
  deployable_name: lss-mt
  actions:
    soak_time:
      custom_soak_time_seconds:
        prod-ltx1: 1800
        prod-lor1: 1800
        prod-lva1: 1800
    tmc:
    - tmc_task:
      - e2e-canary-validation
      targets:
      - prod-lva1.DEFAULT
      - prod-ltx1.DEFAULT
      - prod-lor1.DEFAULT
    - run_tmc: true
    - run_in_blocking_mode: false
notification:
  slack: lss-release
  notify_on_task: False
