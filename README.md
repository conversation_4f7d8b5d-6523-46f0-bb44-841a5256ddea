For the lss-mt developer guide, see [go/lss-mt](http://go/lss-mt)

Sample template files to demonstrate offspring usage for restli jetty services.

Due to `mint product` and other limitations, you still need several extra steps
to make the template files fully deployable. Check
[go/jetty-restli](http://go/jetty-restli)
for the instructions.

Please check [go/offspring](http://go/offspring) for offspring overview, [go/factory](http://go/factory) for existing
offspring factories.

http://rest.li for Restli documents.

`mint build-cfg` will NOT throw an exception if you don't include the -f (e.g. `mint build-cfg -f qei-lca1`) but
this is a silent failure. You _MUST_ pass the fabric if you want the _DefaultParSeqRestClient_ to be able to hit `d2://`
For example, to build the app, configs, and deploy the app in debug mode:
`mint build && mint build-cfg -f qei-ltx1 && mint deploy -f qei-ltx1 --debug-app`

Modify `lss-mt-int-test/build.gradle` & Execute specific integration test using below command.
`ligradle integTest -Dgroups=restli-filter`

or one time DB setup:
`mint-integration setup-only`
and execute specific integration test
`ligradle :test:lss-mt-int-test:integTest --tests com.linkedin.sales.ds.db.<class Name>`

When making changes to the resource APIs, make sure that the `API_MP_ROOT_LSS_MT_API` environment variable is set to the
root directory where the lss-mt-api MP is checked out. This will ensure that IDLs get generated into `lss-mt-api` when
running `mint build`. You can do this by adding the following to your `~/.bashrc` file:

`export API_MP_ROOT_LSS_MT_API=<dir_of_lss-mt-api>`