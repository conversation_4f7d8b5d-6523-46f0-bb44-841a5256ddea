owners: [p<PERSON><PERSON><PERSON>, ragshank, tpar<PERSON><PERSON>, skomarav]
paths: [
  'acl/lss-mt-admin.acl',
  'impl/factory/build.gradle',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/buyerengagement/.*',
  'impl/service/src/main/java/com/linkedin/sales/model/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/TrackingService.java',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/(buyerengagement|common|utils)/.*',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/buyerengagement/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'impl/rest-impl/build.gradle',
  'database/LssBuyer/.*',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssBuyerDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssBuyerDB.java',
  'rest-api/src/main/snapshot/.*',
  'product-spec.json',
  'gradle.properties'
]
