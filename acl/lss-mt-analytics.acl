owners: [ kk<PERSON><PERSON>, j<PERSON><PERSON><PERSON>, bhavpate ]
paths: [
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/analytics/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/analytics/.*',
  'impl/rest-impl/src/mainGeneratedRest/idl/.*',
  'impl/rest-impl/src/mainGeneratedRest/snapshot/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/.*',
  'impl/service/src/test/java/com/linkedin/sales/service/.*',
  'rest-api/src/main/snapshot/.*',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'product-spec.json',
  'gradle.properties'
]

