owners: [ vimenon, ryan]
paths: [
  'impl/client/src/main/java/com/linkedin/sales/client/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/.*',
  'impl/client/src/test/java/com/linkedin/sales/client/.*',
  'impl/service/src/test/java/com/linkedin/sales/service/.*',
  'rest-api/src/main/snapshot/.*',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'product-spec.json',
  'gradle.properties'
]
