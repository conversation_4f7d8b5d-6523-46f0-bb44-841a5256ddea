owners: [ ch<PERSON>e, xiaohzha, lojiang, dizhu, siddshar ]
paths: [
  'product-spec.json',
  'gradle.properties',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'rest-api/src/main/snapshot/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/leadaccount/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/leadaccount/.*',
  'impl/service/src/test/java/com/linkedin/sales/service/leadaccount/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/leadaccount/.*',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssSavedLeadAccountDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssLeadExtendedInfoDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/espresso/LssSavedLeadAccountSchemaRegisterDelegate.java',
  'database/LssSavedLeadAccount/.*',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssSavedLeadAccountDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssSavedLeadAccountDB.java'
]
