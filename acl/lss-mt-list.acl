owners: [ ch<PERSON>e, xiaohzha, lojiang, dizhu, siddshar ]
paths: [
  'database/LssList/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/list/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/list/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'rest-api/src/main/snapshot/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/list/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/acl/.*',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssListDB.java',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssListDB.java',
  'product-spec.json',
  'gradle.properties',
  'impl/service/src/main/resources/com/linkedin/sales/lss-mt_en_US.properties'
]
