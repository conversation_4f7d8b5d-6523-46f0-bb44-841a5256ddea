owners: [ n<PERSON><PERSON><PERSON>, an<PERSON><PERSON>, k<PERSON><PERSON>, chxie, pahlu<PERSON>, ndorofee ]
paths: [
  'product-spec.json',
  'gradle.properties',
  'rest-api/src/main/snapshot/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'rest-api/build.gradle',
  'database/LssCustomFilterView/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/customfilterview/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/customfilterview/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/SalesPinnedFiltersService.java',
  'impl/service/src/main/java/com/linkedin/sales/service/SalesFilterLayoutService.java',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/LixUtils.java',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/PinnedFiltersUtils.java',
  'impl/service/src/test/java/com/linkedin/sales/service/SalesPinnedFiltersServiceTest.java',
  'impl/service/src/test/java/com/linkedin/sales/service/SalesFilterLayoutServiceTest.java',
  'impl/service/src/test/java/com/linkedin/sales/service/utils/PinnedFiltersUtilsTest.java'
]
