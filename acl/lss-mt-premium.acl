owners: [ eachang, jyip, teams/lss-self-serve-growth ]
paths: [
  'impl/service/src/main/java/com/linkedin/sales/.*/(flagship|pymk)/.*',
  'impl/client/src/main/java/com/linkedin/sales/.*/(flagship|pymk)/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/.*/(flagship|pymk)/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/(flagship|pymk)/.*',
  'impl/client/src/test/java/com/linkedin/sales/.*/(flagship|pymk)/.*',
  'impl/service/src/test/java/com/linkedin/sales/.*/(flagship|pymk)/.*',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/LixUtils.java',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/PYMKUtils.java',
  'impl/rest-impl/src/main/java/proto/com/linkedin/sales/flagship/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'impl/client/build.gradle',
  'rest-api/build.gradle',
  'product-spec.json',
  'gradle.properties'
]
