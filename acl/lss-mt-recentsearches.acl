owners: [ n<PERSON><PERSON><PERSON>, an<PERSON><PERSON>, k<PERSON><PERSON>, ch<PERSON>e, pahlu<PERSON>, ndorofee ]
paths: [
  'product-spec.json',
  'gradle.properties',
  'rest-api/src/main/snapshot/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'rest-api/build.gradle',
  'impl/service/src/main/java/com/linkedin/sales/service/utils/LixUtils.java',
  'database/LssRecentViews/.*',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssRecentViewsDB.java',
  'impl/service/src/main/java/com/linkedin/sales/service/SalesRecentSearchesService.java',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/recentviews/SalesRecentSearchesServiceFactory.java',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/recentviews/SalesRecentSearchesResource.java',
  'impl/service/src/test/java/com/linkedin/sales/service/SalesRecentSearchesServiceTest.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssRecentViewsDB.java'
]