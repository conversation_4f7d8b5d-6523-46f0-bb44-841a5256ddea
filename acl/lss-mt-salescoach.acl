owners: [ atela, ajprakas, lojiang, nbavafa, viaggarw ]
paths: [
  'product-spec.json',
  'gradle.properties',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'rest-api/src/main/snapshot/.*',
  'database/LssCoach/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/coach/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/coach/.*',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssCoachDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssCoachDB.java',
  'impl/service/src/main/java/com/linkedin/sales/service/coach/.*',
  'impl/service/src/test/java/com/linkedin/sales/service/coach/.*'
]
