owners: [ xiaohzha, lojiang, dizhu, siddshar ]
paths: [
  'acl/lss-mt-sharing.acl',
  'impl/factory/src/main/java/com/linkedin/sales/factory/common/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/acl/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/alerts/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/bookmark/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/note/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/settings/.*',
  'impl/factory/src/main/java/com/linkedin/sales/factory/services/sharing/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/acl/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/alerts/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/bookmark/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/note/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/sharing/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/settings/.*',
  'impl/service/src/(main|test)/java/com/linkedin/sales/service/utils/*',
  'impl/client/src/main/java/com/linkedin/sales/client/.*',
  'rest-api/src/main/snapshot/.*',
  'impl/rest-impl/src/mainGeneratedRest/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/alerts/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/bookmarkes/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/note/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/settings/.*',
  'impl/rest-impl/src/main/java/com/linkedin/sales/rest/sharing/.*',
  'database/LssAlert/.*',
  'database/LssNote/.*',
  'database/LssSharing/.*',
  'database/LssSeatSetting/.*',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssAlertDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssBookmarkDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssNoteDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssSharingDB.java',
  'impl/ds/src/main/java/com/linkedin/sales/ds/db/LssSeatSettingDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssAlertDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssBookmarkDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssNoteDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssSharingDB.java',
  'test/lss-mt-int-test/src/integTest/java/com/linkedin/sales/ds/db/TestLssSeatSettingDB.java',
  'impl/client/build.gradle',
  'impl/ds/build.gradle',
  'impl/factory/build.gradle',
  'impl/rest-impl/build.gradle',
  'impl/service/build.gradle',
  'rest-api/build.gradle',
  'product-spec.json',
  'gradle.properties'
]
