apply plugin: 'org.sonarqube'
apply plugin: 'li-checkstyle-product'
apply plugin: 'li-pegasuslint'
apply plugin: 'li-product'
apply plugin: 'li-coverage-by-owner'

buildscript {
  // Make sure the product logging implementation doesn't conflict with the one of Gradle (TOOLS-211475)
  configurations.classpath.exclude group:'org.slf4j', module:'slf4j-log4j12'
}

lipegasuslintproduct.ignoreFailures = false

// prevent these from being updated during dependency testing as they cause build failures as
// mockito versions after the current one cause some unit tests to fail due to
// "Caused by: java.lang.ClassNotFoundException: org.testng.internal.ObjectFactoryImpl"
// Added to resolve UNSCREW-5866
allprojects {
  configurations.all {
    resolutionStrategy {
      force spec.external.'mockito-inline'
      force spec.external.'testng'
    }
  }
}

subprojects {

  // override cglib dependency brought in by talent (aka db-mapper)
  configurations.all {
    // LI has standardized on org.conscrypt:conscrypt-openjdk-uber, which
    // retains package and class names and thus conflicts
    exclude group: "org.conscrypt", module: "conscrypt-openjdk"
    // Exclude the dependencies on findbugs since we are now using spotbugs
    exclude group: "org.jetbrains", module: "annotations"
    exclude group: 'com.google.code.findbugs', module: 'findbugs-annotations'
    // The mockito-all artifact was produced for environments that do not use automatic dependency
    // management and includes all of its dependencies. We depend instead on mockito-inline, which
    // declares its dependencies in its artifact metadata. What we would like is to substitute all
    // requests for mockito-all with mockito-inline. Exclusion works here as well.
    exclude group: 'org.mockito', module: 'mockito-all'

    // all excluded in the course of ensuring no dependencies on container <= 38.4.x for protobuf upgrade.
    // at any given time these "excludes" should be candidates for removal if we develop an actual dependency on these
    // things. Until then, never hurts to have a smaller classpath.  2/25/2021
    exclude group: 'com.linkedin.container', module: 'cache-api'
    exclude group: 'com.linkedin.container', module: 'container-ic-api'
    exclude group: 'com.linkedin.container', module: 'container-ic-finder-factory'
    exclude group: 'com.linkedin.container', module: 'container-ic-handlers-api'
    exclude group: 'com.linkedin.container', module: 'container-ic-handlers-factory'
    exclude group: 'com.linkedin.container', module: 'container-rpcExecutorService-factory'
    exclude group: 'com.linkedin.container', module: 'container-servicecall-api'
    exclude group: 'com.linkedin.container', module: 'container-servicecall-factory'
    exclude group: 'com.linkedin.container', module: 'container-jmx-servlet-api'
    exclude group: 'com.linkedin.container', module: 'sensor-registry-factory'
    exclude group: 'com.linkedin.container', module: 'tracker-api'
    exclude group: 'com.linkedin.container', module: 'tracker-processor-api'
    exclude group: 'com.linkedin.container', module: 'tracker-processor-factory'
    exclude group: 'com.linkedin.container', module: 'tracker-producer-api'
    exclude group: 'com.linkedin.container', module: 'tracker-consumer-api'
    exclude group: 'com.linkedin.container', module: 'tracker-producer-factory'
    exclude group: 'com.linkedin.container', module: 'cache-memcache-impl'
    exclude group: 'com.linkedin.container', module: 'util-auto-commit-executor-pooled-factory'
    exclude group: 'com.linkedin.container', module: 'util-oracle-pooled-txmgr-factory'
    exclude group: 'com.linkedin.container', module: 'util-urlEncryptor-factory'

    // lss-cap-api is deprecated. Adding this to not get pulled in transitively for grpc bridge.
    exclude group: 'com.linkedin.lss-cap-api', module: 'cap-backend-api'

    resolutionStrategy.eachDependency { details ->
      if (details.requested.name == 'cglib-nodep') {
        details.useTarget spec.external.'cglib'
      }
      if (details.requested.group == 'asm') {
        details.useTarget spec.external.'asm'
      }
    }
  }

  plugins.withType(JavaPlugin) {
    dependencies {
      testImplementation spec.external.testng
      implementation spec.external.'spotbugs-annotations'
    }

    test {
      useTestNG()
    }
  }

  plugins.withId('li-spotbugs') {
    dependencies {
      spotbugsPlugins spec.external.'sb-contrib'
    }

    // Spotbug runs out of memory, hence increasing the heap size
    spotbugs {
      maxHeapSize = '2g'
    }
  }

  product {
    codeQuality {
      ignoreCodeStyleFailures = false // build fails with checkstyle errors.
      ignoreStaticAnalysisFailures = false
    }
  }
}

def ignoreCoverage = [
  '**/avro/generated/',
  '**/com/linkedin/sales/client/',
  '**/com/linkedin/sales/db/',
  '**/com/linkedin/sales/ds/',
  '**/com/linkedin/sales/espresso/',
  '**/com/linkedin/sales/factory/',
  '**/com/linkedin/sales/rest/',
  '**/com/linkedin/sales/model/',
  '**/com/linkedin/sales/monitoring/',
  '**/com/linkedin/sales/test',
  '**/com/linkedin/sales/service/utils/RestliUtils.class',
  '**/com/linkedin/sales/service/flagship/helpers/**',
  '**/com/linkedin/sales/utils/MemberUtils.class',
  '**/mainGeneratedJooq/**',
  '**/proto/com/linkedin/sales'
]

jacocoConfig.ignoreCoverage = ignoreCoverage

sonarqube {
  properties {
    property "sonar.host.url", "https://servicessonar.microsoft.com"
    // Intentionally left blank. The SonarQube token is provided via the SONAR_TOKEN environment variable.
    // If you need to be able to run this script locally, you can set the SONAR_TOKEN environment variable
    // or the sonar.login property to a user token.
    // See: https://servicessonar.microsoft.com/documentation/user-guide/user-token/
    // property "sonar.login", "xxxxxxxxxx"
    property "sonar.projectKey", "lss-mt_linkedin"
    property "sonar.projectName", "lss-mt"
    property "sonar.links.scm", "https://github.com/linkedin-multiproduct/lss-mt"
    property "sonar.coverage.jacoco.xmlReportPaths", "${rootDir}/build/reports/jacoco/jacocoRootReport/jacocoRootReport.xml"
    property "sonar.exclusions", ignoreCoverage.join(',')
    property "sonar.coverage.exclusions", ignoreCoverage.join(',')
  }
}

tasks.withType(JavaCompile).configureEach {
    options.fork = true
    options.forkOptions.memoryMaximumSize = '8g'
}
