<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serviceCallTrackerInterceptor.trackingMethods">
      <set>
        <value>proto.com.linkedin.sales.ContractSellerIdentitiesService/actionAddProduct</value>
        <value>proto.com.linkedin.sales.ContractSellerIdentitiesService/actionRemoveProduct</value>
        <value>proto.com.linkedin.sales.ContractSellerIdentitiesService/actionUpdateProduct</value>
        <value>proto.com.linkedin.sales.ContractSellerIdentitiesService/get</value>
        <value>proto.com.linkedin.sales.SalesAccountToListMappingsService/create</value>
        <value>proto.com.linkedin.sales.SalesAccountToListMappingsService/delete</value>
        <value>proto.com.linkedin.sales.SalesAccountToListMappingsService/findBySeatAndAccount</value>
        <value>proto.com.linkedin.sales.SalesAccountsSeatTransferActionsService/actionExecuteSeatTransferRequest</value>
        <value>proto.com.linkedin.sales.SalesCommunicationPluginService/actionFormatNotifications</value>
        <value>proto.com.linkedin.sales.SalesCommunicationPluginService/actionGenerateDecorator</value>
        <value>proto.com.linkedin.sales.SalesCustomListsSeatTransferActionsService/actionExecuteSeatTransferRequest</value>
        <value>proto.com.linkedin.sales.SalesEntityNotesSeatTransferActionsService/actionExecuteSeatTransferRequest</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsSeatTransferActionsService/actionExecuteSeatTransferRequest</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/batchCreate</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/batchDelete</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/create</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/delete</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/findBySeat</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/findBySeatAndAccounts</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountAssociationsService/findBySeatAndLeads</value>
        <value>proto.com.linkedin.sales.SalesLeadAccountMetadataService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesLeadEditableContactInfoService/get</value>
        <value>proto.com.linkedin.sales.SalesLeadEditableContactInfoService/update</value>
        <value>proto.com.linkedin.sales.SalesLeadProfileUnlockInfoService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesLeadProfileUnlockInfoService/create</value>
        <value>proto.com.linkedin.sales.SalesLeadsSeatTransferActionsService/actionExecuteSeatTransferRequest</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/actionCreateAndStart</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/delete</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/findByCreator</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/findByCsvImportTask</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/findByList</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/get</value>
        <value>proto.com.linkedin.sales.SalesListCsvImportsService/partialUpdate</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/batchCreate</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/batchDelete</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/batchPartialUpdate</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/create</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/delete</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/findByList</value>
        <value>proto.com.linkedin.sales.SalesListEntitiesService/findByLists</value>
        <value>proto.com.linkedin.sales.SalesListEntitySummariesService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesListsService/batchCreate</value>
        <value>proto.com.linkedin.sales.SalesListsService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesListsService/create</value>
        <value>proto.com.linkedin.sales.SalesListsService/delete</value>
        <value>proto.com.linkedin.sales.SalesListsService/findByEntity</value>
        <value>proto.com.linkedin.sales.SalesListsService/findByOrganizationForAccountMap</value>
        <value>proto.com.linkedin.sales.SalesListsService/findBySeat</value>
        <value>proto.com.linkedin.sales.SalesListsService/get</value>
        <value>proto.com.linkedin.sales.SalesListsService/partialUpdate</value>
        <value>proto.com.linkedin.sales.SalesListsService/update</value>
        <value>proto.com.linkedin.sales.SalesNotesService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesNotesService/create</value>
        <value>proto.com.linkedin.sales.SalesNotesService/delete</value>
        <value>proto.com.linkedin.sales.SalesNotesService/findByEntity</value>
        <value>proto.com.linkedin.sales.SalesNotesService/findBySeat</value>
        <value>proto.com.linkedin.sales.SalesNotesService/get</value>
        <value>proto.com.linkedin.sales.SalesNotesService/partialUpdate</value>
        <value>proto.com.linkedin.sales.SalesNotesService/update</value>
        <value>proto.com.linkedin.sales.SalesProfileViewsService/get</value>
        <value>proto.com.linkedin.sales.SalesProfileViewsService/update</value>
        <value>proto.com.linkedin.sales.SalesRealtimeRelationshipMapChangeLogAuthorizationService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesRealtimeRelationshipMapChangeLogAuthorizationService/get</value>
        <value>proto.com.linkedin.sales.SalesRealtimeSharedResourceViewerAuthorizationService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesRealtimeSharedResourceViewerAuthorizationService/get</value>
        <value>proto.com.linkedin.sales.SalesRelationshipMapChangeLogsService/create</value>
        <value>proto.com.linkedin.sales.SalesRelationshipMapChangeLogsService/findByRelationshipMap</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/batchCreate</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/batchDelete</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/batchPartialUpdate</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/create</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/delete</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/findByOwner</value>
        <value>proto.com.linkedin.sales.SalesSavedAccountsService/get</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/batchCreate</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/batchDelete</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/create</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/delete</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/findByOwner</value>
        <value>proto.com.linkedin.sales.SalesSavedLeadsService/get</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/actionCheckAccessDecision</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/batchDelete</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/batchGet</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/batchUpdate</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/findByResource</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/findByResourceContext</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/findBySubject</value>
        <value>proto.com.linkedin.sales.SalesSharingPoliciesService/partialUpdate</value>
        <value>proto.com.linkedin.salesgateway.SalesNavigatorSaveLeadService/actionSaveVieweeAsLead</value>
        <value>proto.com.linkedin.salesgateway.SalesNavigatorSaveLeadService/actionUnsaveVieweeAsLead</value>
      </set>
    </property>

    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serviceCallTrackerInterceptor.trackingAttributes" value="${restliServlet.dispatcher.trackingAttributes}"/>
    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.enableServiceCallTracking" value="${restliServlet.dispatcher.enableCallTracking}"/>
    <!--Set max inbound message size to: ********** bytes-->
    <property name="__defaultGrpcServer__.grpcServer.maxInboundMessageSize" value="**********"/>
    <!-- Increase header max size to accommodate QPROD requests - #si-35514 -->
    <property name="__defaultGrpcServer__.grpcServer.maxInboundMetadataSize" value="65535"/>
    <property name="avesServer.maxHeaderSize" value="16384"/>
    <property name="__defaultGrpcServer__.grpcServer.servicePackages">
      <set>
        <value>proto.com.linkedin.sales</value>
        <value>proto.com.linkedin.salesgateway</value>
        <value>proto.com.linkedin.salescolleagues</value>
        <value>proto.com.linkedin.salesanalytics</value>
        <value>proto.com.linkedin.salesautoprospecting</value>
      </set>
    </property>

    <!--Prefer ipv6 communication. go/ipv6-hi-2023-->
    <property name="opt.com.linkedin.jvml.net" value="-Djava.net.preferIPv6Addresses=true"/>

    <!--Enable QoS Hodor GC Detector. go/hodor-->
    <property name="overloadGCDetector.isMonitorMode" value="false"/>

    <!--Enable QoS Hodor overload detection and load shedding. go/hodor-->
    <property name="overloadHeartbeatDetector.heartbeatDetectorConfigs" value="${overloadHeartbeatDetector.aggressiveDetectionSettings}"/>
    <property name="restliServlet.filterDispatcher.includeFilterChainInCallTracking" value="true"/>
    <property name="overloadProtection.loadShedder.enableLixRamping" value="false"/>

    <property name="restliServlet.d2.clusterName" value="LssMtCluster"/>
    <property name="application.context" value="/lss-mt"/>
    <property name="restliServlet.d2.nodeUri" value="https://${com.linkedin.app.localhost}:${com.linkedin.metadata.application.port.https}${application.context}"/>
    <property name="sampleDB.maxKey" value="100"/>
    <property name="restliServlet.resourcePackageNames" value="com.linkedin.sales"/>

    <!-- grpc d2 announcer configuration -->
    <property name="__defaultGrpcServer__.grpcServer.d2.clusterName" value="LssMtClusterGrpc"/>

    <property name="maintenanceEventManager.eventFilePath" value="/export/content/data/pauseMaintenanceEvent"/>
    <!-- ESPRESSO OPTIMIZATION FITNESS-581 -->
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.connectTimeoutMs" value="500"/>
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.sslHandShakeTimeoutMs" value="500"/>
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.channelPoolWaiterTimeoutMs" value="500"/>
    <property name="__r2d2DefaultClient__.r2d2Client.retry" value="true"/>
    <property name="__r2d2DefaultClient__.r2d2Client.retryLimit" value="1"/>

    <!-- Avoid logging 404 and 412 from EspressoClient where 404 is by default ignored and 412 is expected response -->
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.loggingFilter.logIgnoreRestExceptionStatusPattern" value="(404|412)"/>

    <property name="restliServlet.d2.doNotStart" value="${d2.qd.localdeploy}"/>
    <property name="restliServlet.dispatcher.authFilter.accessClient.useDynamicAcls" value="true"/>
    <property name="restliServlet.dispatcher.authFilter.accessClient.actionWhenAclMissing" value="ALLOW"/>
    <!-- LSS Partner API Settings -->
    <property name="allowListedForAuthorizationCheck">
      <set>
        <value>salesAnalyticsExportJobs:get</value>
        <value>salesAnalyticsExportJobs:action:exportActivityData</value>
        <value>salesAnalyticsExportJobs:action:exportActivityOutcomeData</value>
        <value>salesAnalyticsExportJobs:action:exportTagData</value>
        <value>salesAnalyticsExportJobs:action:exportMemberIdentityMappingData</value>
        <value>salesAnalyticsExportJobs:action:exportSeatData</value>
        <value>salesAnalyticsExportJobs:action:retrieveActivityDataAvailability</value>
        <value>salesAnalyticsExportJobs:action:retrieveActivityOutcomeDataAvailability</value>
        <value>salesAnalyticsExportJobs:action:retrieveSeatDataAvailability</value>
        <!-- salesCommunicationPlugin is used by voyager-api as a standard notification rendering plugin, and is static without any downstream calls to other LSS endpoints -->
        <value>salesCommunicationPlugin:action:generateDecorator</value>
        <value>salesCommunicationPlugin:action:formatNotifications</value>
      </set>
    </property>
    <property name="salesAllowListedEndPointsForAuthorizationService.authorizationAllowListedEndpoints" value="${allowListedForAuthorizationCheck}"/>
    <!-- Renamed to above for the inclusive code HI, but needs to keep the old entry here for backward compatability -->
    <property name="whiteListedForAuthorizationCheck">
      <set>
        <value>salesAnalyticsExportJobs:get</value>
        <value>salesAnalyticsExportJobs:action:exportActivityData</value>
        <value>salesAnalyticsExportJobs:action:exportActivityOutcomeData</value>
        <value>salesAnalyticsExportJobs:action:exportTagData</value>
        <value>salesAnalyticsExportJobs:action:exportMemberIdentityMappingData</value>
        <value>salesAnalyticsExportJobs:action:exportSeatData</value>
        <value>salesAnalyticsExportJobs:action:retrieveActivityDataAvailability</value>
        <value>salesAnalyticsExportJobs:action:retrieveActivityOutcomeDataAvailability</value>
        <value>salesAnalyticsExportJobs:action:retrieveSeatDataAvailability</value>
      </set>
    </property>
    <property name="salesWhiteListedEndPointsForAuthorizationService.authorizationWhiteListedEndpoints" value="${whiteListedForAuthorizationCheck}"/>

    <!-- Non iterable profile id factory configuration -->
    <property name="profile.id.generator.configSecret" value="${profile.id.validator.configSecret}"/>
    <!-- Temporarily using lighthouse-frontend as the application name since sales-api hasn't been added to applicationId map-->
    <property name="profile.id.generator.appName" value="lighthouse-frontend"/>
    <property name="profile.id.generator.applicationIdMap" value="${non-iterable.profile.view.url.applicationIdMap}"/>
    <property name="salesAnalyticsExportJobService.ambryClient.ambryClientServiceInternalUrl" value="${ambryClient.service.internal.url}"/>

    <!--Trakcing related -->
    <property name="salesTrackingService.machineName" value="${com.linkedin.app.machine}"/>
    <property name="salesTrackingService.envName" value="${com.linkedin.app.env}"/>
    <property name="salesTrackingService.appName" value="${com.linkedin.app.name}"/>

    <!-- SN Email Service related-->
    <property name="salesInviteRegisterUrlService.inviteRegisterUrlPrefix" value="https://www.linkedin.com/sales/register"/>
    <property name="salesNavigatorEmailService.memberBindFlowRedirectUrl" value="${secureBaseUrl}/sales/activate"/>

    <!-- Total timeout to check for member to sales identity block with UCF -->
    <property name="salesMessagePreCreateValidatePluginService.memberBlockCheckTimeoutMillis" value="400"/>

    <!-- UCF configuration -->
    <property name="messagingUcfClient.ucfClient.validAnyRecordModels" value="${ucf.client.valid.anyrecords.list}"/>
    <property name="messagingUcfClient.ucfClient.appName" value="${com.linkedin.app.name}"/>

    <property name="salesCryptoService.secrets" value="${lss-mt.secrets}"/>
    <property name="lss-mt.secrets">
      <map>
        <!--  For encrypting the SeatRequest id and Contract id into registration url -->
        <entry key="invite.encryptionkey" value="${sales-secrets.invite.encryptionkey}"/>
      </map>
    </property>

    <!-- Espresso config for the shared factory -->
    <!-- Please override it in dev, ei and prod -->
    <property name="salesEspressoSd1.espresso.uriPrefix" value=""/>
    <property name="salesEspressoMd4.espresso.uriPrefix" value=""/>
    <property name="salesEspressoLd3.espresso.uriPrefix" value=""/>
    <property name="salesEspressoWaterloo2.espresso.uriPrefix" value=""/>
    <property name="salesEspressoLd6.espresso.uriPrefix" value=""/>
    <property name="salesEspressoMt3.espresso.uriPrefix" value=""/>
    <property name="lssSharing.espresso.uriPrefix" value=""/>
    <property name="lssList.espresso.uriPrefix" value=""/>
    <property name="lssColleagues.espresso.uriPrefix" value=""/>
    <property name="lssSavedLeadAccount.espresso.uriPrefix" value=""/>
    <property name="lssEditableContactInfo.espresso.uriPrefix" value=""/>
    <property name="lssLeadExtendedInfo.espresso.uriPrefix" value=""/>
    <property name="lssNote.espresso.uriPrefix" value=""/>
    <property name="lssSeatSetting.espresso.uriPrefix" value=""/>
    <property name="lssAlert.espresso.uriPrefix" value=""/>
    <property name="lssBookmark.espresso.uriPrefix" value=""/>
    <property name="lssBuyer.espresso.uriPrefix" value=""/>
    <property name="lssEntityView.espresso.uriPrefix" value=""/>

    <!-- Jetty thread pool size -->
    <property name="jettyThreadPool.maxThreads" value="900"/>
    <property name="adaptiveMaxThroughputShedder.maxThroughputRampUpWindowDurationMs" value="4000"/>

    <!-- Deprecated -->
    <property name="salesEspresso.espresso.uriPrefix" value="${espressoClient.uriPrefix}"/>
    <property name="defaultParSeqEspressoClient.espressoClient.uriPrefix" value="${espressoClient.uriPrefix}"/>
    <property name="salesListEntityService.espressoClient.uriPrefix" value="${lssListEspressoClient.uriPrefix}"/>
    <property name="salesListEntitySummaryService.espressoClient.uriPrefix" value="${lssListEspressoClient.uriPrefix}"/>
    <property name="salesListService.espressoClient.uriPrefix" value="${lssListEspressoClient.uriPrefix}"/>
    <property name="salesInvitationService.espresso.uriPrefix" value="d2://EDB-LssInvitation"/>

    <!-- Azkaban config -->
    <property name="lsseng.azkaban.privateKey" value=""/>
    <property name="crmDataValidationExportJobService.azkabanUrl" value="https://ltx1-faroaz01.grid.linkedin.com:8443/"/>
    <property name="crmDataValidationExportJobService.username" value="lsseng_az_svc"/>
    <property name="crmDataValidationExportJobService.privateKey" value="${lsseng.azkaban.privateKey}"/>
    <property name="crmDataValidationExportJobService.azkabanFlowName" value="data-validation-on-demand-export_azkabanFaro"/>

    <property name="crmDataValidationExportJobService.ambryClient.ambryClientServiceInternalUrl" value="${ambryClient.service.internal.url}"/>

    <property name="crmDataValidationExportJobService.trustedCrmInstanceIds">
      <set>
        <value>7dda308f-b6fc-4ea9-9113-bd6415921850</value>
        <value>00D6g000004ZTklEAG</value>
        <value>00DDn0000011dnSMAQ</value>
        <value>00D3t000005DUtEEAW</value>
      </set>
    </property>

    <property name="salesExternalizationService.ambryClient.ambryClientServiceInternalUrl" value="${ambryClient.service.internal.url}"/>

    <!-- UCF client properties -->
    <property name="memberFilterClient.ucfClient.appName" value="${com.linkedin.app.name}"/>
    <property name="memberFilterClient.ucfClient.validAnyRecordModels" value="${ucf.client.valid.anyrecords.list}"/>
    <!-- UCF tracking producer properties -->
    <property name="memberFilterClient.ucfClient.trackingProducer.schemaRegistryRestUrl" value="${schemaregistry.service.uri}"/>
    <property name="memberFilterClient.ucfClient.trackingProducer.metadataBrokerList" value="${tracker.producer.metadata.broker.list}"/>
    <property name="memberFilterClient.ucfClient.trackingProducer.compressionCodec" value="${kafka.producer.compression.codec}"/>
    <property name="memberFilterClient.ucfClient.trackingProducer.requestRequiredAcks" value="${kafka.producer.request.required.acks}"/>
    <property name="memberFilterClient.ucfClient.trackingProducer.batchNumMessages" value="${kafka.producer.batch.num.messages}"/>
    <property name="memberFilterClient.ucfClient.trackingProducer.useAvroProducerFactory" value="true"/>

    <!-- UCF queuing producer properties -->
    <property name="memberFilterClient.ucfClient.queueingProducer.schemaRegistryRestUrl" value="${schemaregistry.service.uri}"/>
    <property name="memberFilterClient.ucfClient.queueingProducer.metadataBrokerList" value="${kafka.queuing.metadata.broker.list}"/>
    <property name="memberFilterClient.ucfClient.queueingProducer.compressionCodec" value="${kafka.producer.compression.codec}"/>
    <property name="memberFilterClient.ucfClient.queueingProducer.requestRequiredAcks" value="${kafka.producer.request.required.acks}"/>
    <property name="memberFilterClient.ucfClient.queueingProducer.batchNumMessages" value="${kafka.producer.batch.num.messages}"/>
    <property name="memberFilterClient.ucfClient.queueingProducer.useAvroProducerFactory" value="true"/>

    <!-- Tracking producer config -->
    <property name="singletonTrackingProducer.trackingProducer.batchNumMessages" value="${kafka.producer.batch.num.messages}"/>
    <property name="singletonTrackingProducer.trackingProducer.compressionCodec" value="${kafka.producer.compression.codec}"/>
    <property name="singletonTrackingProducer.trackingProducer.metadataBrokerList" value="${tracker.producer.metadata.broker.list}"/>
    <property name="singletonTrackingProducer.trackingProducer.requestRequiredAcks" value="${kafka.producer.request.required.acks}"/>
    <property name="singletonTrackingProducer.trackingProducer.schemaRegistryRestUrl" value="${schemaregistry.service.uri}"/>
    <property name="singletonTrackingProducer.trackingProducer.useAvroProducerFactory" value="true"/>

    <property name="salesleadaccountTrackingClient.trackingProducer.queueBufferingMaxMs" value="500"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.schemaRegistryRestUrl" value="${schemaregistry.rest.uri}"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.compressionCodec" value="${kafka.producer.compression.codec}"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.requestRequiredAcks" value="${kafka.producer.request.required.acks}"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.useAvroProducerFactory" value="true"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.metadataBrokerList" value="${kafka.tracking-local.metadata.broker.list}"/>
    <property name="salesleadaccountTrackingClient.trackingProducer.batchNumMessages" value="${kafka.producer.batch.num.messages}"/>

    <!-- GaaP Polling Client config -->
    <property name="parseqPollingGaapClient.initialDelay" value="100"/>
    <property name="parseqPollingGaapClient.delay" value="100"/>
    <property name="parseqPollingGaapClient.timeout" value="20000"/>

    <!-- Method Level Metrics https://iwww.corp.linkedin.com/wiki/cf/display/ENGS/Method+level+metrics+for+RestLi+servers -->
    <property name="restliServlet.dispatcher.enableCallTracking" value="true"/>
    <property name="restliServlet.dispatcher.trackingOperations">
      <set>
        <value>salesMessagingPlugin_action_threadPreCreateValidate</value>
        <value>salesMessagingPlugin_action_messagePreCreateMutate</value>
        <value>salesMessagingPlugin_action_messagePreCreateValidate</value>
        <value>salesMessagingPlugin_action_messagePreCreateNotify</value>
        <value>salesMessagingPlugin_action_messagePostCreateNotify</value>
        <value>salesMessagingPlugin_action_messagePreDeliverMutate</value>
        <value>salesMessagingPlugin_action_messagePreDeliverNotify</value>
        <value>salesMessagingPlugin_action_messagePostDeliverNotify</value>

        <!--salesSavedLeads Rest.Li method level metrics settings -->
        <value>salesSavedLeads_create</value>
        <value>salesSavedLeads_get</value>
        <value>salesSavedLeads_delete</value>
        <value>salesSavedLeads_batch_get</value>
        <value>salesSavedLeads_batch_create</value>
        <value>salesSavedLeads_batch_delete</value>
        <value>salesSavedLeads_finder_owner</value>

        <!--salesSavedAccounts Rest.Li method level metrics settings -->
        <value>salesSavedAccounts_create</value>
        <value>salesSavedAccounts_get</value>
        <value>salesSavedAccounts_delete</value>
        <value>salesSavedAccounts_batch_get</value>
        <value>salesSavedAccounts_batch_create</value>
        <value>salesSavedAccounts_batch_delete</value>
        <value>salesSavedAccounts_finder_owner</value>
        <value>salesSavedAccounts_batch_partial_update</value>

        <!--salesLeadAccountAssociations Rest.Li method level metrics settings -->
        <value>salesLeadAccountAssociations_create</value>
        <value>salesLeadAccountAssociations_delete</value>
        <value>salesLeadAccountAssociations_batch_create</value>
        <value>salesLeadAccountAssociations_batch_delete</value>
        <value>salesLeadAccountAssociations_finder_seatAndAccounts</value>
        <value>salesLeadAccountAssociations_finder_seatAndLeads</value>
        <value>salesLeadAccountAssociations_finder_seat</value>

        <!--salesLeadExtendedInfo Rest.Li method level metrics settings -->
        <value>salesLeadEditableContactInfo_get</value>
        <value>salesLeadEditableContactInfo_update</value>
        <value>salesLeadProfileUnlockInfo_create</value>
        <value>salesLeadProfileUnlockInfo_batch_get</value>

        <!--salesLeadAccountMetadata Rest.Li method level metrics settings -->
        <value>salesLeadAccountMetadata_batch_get</value>

        <!--salesNavigatorSaveLead Rest.Li method level metrics settings -->
        <value>salesNavigatorSaveLead_action_saveVieweeAsLead</value>
        <value>salesNavigatorSaveLead_action_unsaveVieweeAsLead</value>

        <!--salesAccountToListMappings Rest.Li method level metrics settings -->
        <value>salesAccountToListMappings_create</value>
        <value>salesAccountToListMappings_delete</value>
        <value>salesAccountToListMappings_finder_seatAndAccount</value>

        <!--salesListCsvImports Rest.Li method level metrics settings -->
        <value>salesListCsvImports_get</value>
        <value>salesListCsvImports_delete</value>
        <value>salesListCsvImports_batch_get</value>
        <value>salesListCsvImports_partial_update</value>
        <value>salesListCsvImports_finder_creator</value>
        <value>salesListCsvImports_finder_csvImportTask</value>
        <value>salesListCsvImports_finder_list</value>
        <value>salesListCsvImports_action_createAndStart</value>

        <!--salesListEntities Rest.Li method level metrics settings -->
        <value>salesListEntities_create</value>
        <value>salesListEntities_delete</value>
        <value>salesListEntities_batch_get</value>
        <value>salesListEntities_batch_create</value>
        <value>salesListEntities_batch_delete</value>
        <value>salesListEntities_batch_partial_update</value>
        <value>salesListEntities_finder_list</value>
        <value>salesListEntities_finder_lists</value>

        <!--salesListEntitySummaries Rest.Li method level metrics settings -->
        <value>salesListEntitySummaries_batch_get</value>

        <!--salesLists Rest.Li method level metrics settings -->
        <value>salesLists_create</value>
        <value>salesLists_delete</value>
        <value>salesLists_get</value>
        <value>salesLists_update</value>
        <value>salesLists_partial_update</value>
        <value>salesLists_batch_get</value>
        <value>salesLists_batch_create</value>
        <value>salesLists_finder_entity</value>
        <value>salesLists_finder_organizationForAccountMap</value>
        <value>salesLists_finder_seat</value>

        <!--salesNotes Rest.Li method level metrics settings -->
        <value>salesNotes_create</value>
        <value>salesNotes_delete</value>
        <value>salesNotes_get</value>
        <value>salesNotes_update</value>
        <value>salesNotes_partial_update</value>
        <value>salesNotes_batch_get</value>
        <value>salesNotes_finder_entity</value>
        <value>salesNotes_finder_seat</value>

        <!--salesProfileViews Rest.Li method level metrics settings -->
        <value>salesProfileViews_get</value>
        <value>salesProfileViews_update</value>

        <!--salesSharingPolicies Rest.Li method level metrics settings -->
        <value>salesSharingPolicies_batch_delete</value>
        <value>salesSharingPolicies_batch_get</value>
        <value>salesSharingPolicies_batch_update</value>
        <value>salesSharingPolicies_partial_update</value>
        <value>salesSharingPolicies_finder_resource</value>
        <value>salesSharingPolicies_finder_resourceContext</value>
        <value>salesSharingPolicies_finder_subject</value>
        <value>salesSharingPolicies_action_checkAccessDecision</value>

        <!--salesRelationshipMapChangeLogs Rest.Li method level metrics settings -->
        <value>salesRelationshipMapChangeLogs_create</value>
        <value>salesRelationshipMapChangeLogs_finder_relationshipMap</value>

        <!--salesRealtimeSharedResourceViewerAuthorization Rest.Li method level metrics settings -->
        <value>salesRealtimeSharedResourceViewerAuthorization_get</value>
        <value>salesRealtimeSharedResourceViewerAuthorization_batch_get</value>

        <!--salesRealtimeRelationshipMapChangeLogAuthorization Rest.Li method level metrics settings -->
        <value>salesRealtimeRelationshipMapChangeLogAuthorization_get</value>
        <value>salesRealtimeRelationshipMapChangeLogAuthorization_batch_get</value>

        <!-- salesCommunicationPlugin Rest.Li method level metrics settings -->
        <value>salesCommunicationPlugin_action_generateDecorator</value>
        <value>salesCommunicationPlugin_action_formatNotifications</value>

        <!-- salesSeatTransfer -->
        <value>salesLeadsSeatTransferActions_action_executeSeatTransferRequest</value>
        <value>salesCustomListsSeatTransferActions_action_executeSeatTransferRequest</value>
        <value>salesEntityNotesSeatTransferActions_action_executeSeatTransferRequest</value>
        <value>salesLeadAccountAssociationsSeatTransferActions_action_executeSeatTransferRequest</value>
        <value>salesAccountsSeatTransferActions_action_executeSeatTransferRequest</value>

        <!--contractSellerIdentities Rest.Li method level metrics settings -->
        <value>contractSellerIdentities_get</value>
        <value>contractSellerIdentities_action_addProduct</value>
        <value>contractSellerIdentities_action_updateProduct</value>
        <value>contractSellerIdentities_action_removeProduct</value>
      </set>
    </property>
    <!--The following 2 configs make sure that the untouched resources qps are NOT limited. This is done by setting the qps limit as Integer.MAX_VALUE -->
    <!--All of these qps limits are per host limits, meaning if the lss-mt adds or removes some hosts in a fabric,
     the total qps in that fabric is subject to change.-->
    <property name="__r2d2DefaultClient__.r2d2Client.capacityAware.config.normalRequestPerSecond" value="**********"/>
    <property name="__r2d2DefaultClient__.r2d2Client.capacityAware.config.rateLimitedRequestPerSecond" value="**********"/>
    <property name="__r2d2DefaultClient__.r2d2Client.capacityAware.config.maxBuffered" value="1024"/>
    <property name="__r2d2DefaultClient__.r2d2Client.capacityAware.overrides">
      <list>
        <map>
          <entry key="service" value="salesSavedLeads"/>
          <entry key="normalRequestPerSecond" value="50"/>
          <entry key="rateLimitedRequestPerSecond" value="5"/>
        </map>
        <map>
          <entry key="service" value="salesSavedAccounts"/>
          <entry key="normalRequestPerSecond" value="50"/>
          <entry key="rateLimitedRequestPerSecond" value="5"/>
        </map>
        <map>
          <entry key="service" value="salesLeadAccountAssociations"/>
          <entry key="normalRequestPerSecond" value="50"/>
          <entry key="rateLimitedRequestPerSecond" value="5"/>
        </map>
      </list>
    </property>
    <property name="restliServlet.dispatcher.trackingAttributes">
      <set>
        <value>CallCountTotal</value>
        <value>CallTimeAvg</value>
        <value>CallTime50Pct</value>
        <value>CallTime90Pct</value>
        <value>CallTime99Pct</value>
        <value>ErrorRate</value>
        <value>ErrorCountTotal</value>
      </set>
    </property>
    <!-- Generate separate InGraphs for 4xx, 5xx -->
    <property name="restliServlet.dispatcher.responseStatusCategories">
      <map>
        <entry key="4xx" value="(4..)"/>
        <entry key="5xx" value="(5..)"/>
      </map>
    </property>

    <!--
     [SI-44869] This config controls the R2 timeout on the server (lss-mt in this case). The default value for this
     property is 30s post which the server terminates the request, regardless of the D2 timeout for the service. This
     timeout should be kept >= the longest timeout for any endpoint on this server. Setting to 360000 for now, even
     though some seat transfer endpoints are configured to take up to 600000 seconds. We'll update later if need be.
    -->
    <property name="restliServlet.r2Servlet.servletTimeout" value="360000"/>

    <!-- Timeouts for lss-mt downstream requests -->
    <property name="lss-mt.downstream.timeoutMs">
      <map>
        <entry key="*.*/*.*" value="2000"/>
        <entry key="*.*/salesActivityPinotStatistics.*" value="10000"/>
        <entry key="*.*/capPermissions.*" value="10000"/>
        <entry key="*.*/capContractPrefs.*" value="10000"/>
        <entry key="*.*/capUiPreferences.*" value="10000"/>
        <entry key="*.*/lssCapPermissions.*" value="10000"/>
        <entry key="*.*/lssCapContractPrefs.*" value="10000"/>
        <entry key="*.*/lssCapUiPreferences.*" value="10000"/>
        <entry key="*.*/salesSeatTransferCopyAssociations.*" value="10000"/>
      </map>
    </property>
    <property name="defaultParSeqRestClient.parseqRestClient.timeoutMs" value="${lss-mt.downstream.timeoutMs}"/>
    <!-- Tier specific lead limits are being deprecated. They will be removed shortly. -->
    <property name="savedLeadService.lssLeadLimitByTier">
      <map>
        <entry key="SALES_SEAT_TIER0" value="${lighthouse.tier.0.max.leads}"/>
        <entry key="SALES_SEAT_TIER1" value="${lighthouse.tier.1.max.leads}"/>
        <entry key="SALES_SEAT_TIER2" value="${lighthouse.tier.2.max.leads}"/>
        <entry key="SALES_SEAT_TIER3" value="${lighthouse.tier.3.max.leads}"/>
      </map>
    </property>

    <property name="savedLeadService.maxSavedLeadLimitAllTiers" value="${lighthouse.all.tiers.limit.max.leads}"/>
    <property name="saleAccountsService.maxSavedAccountLimitAllTiers" value="${lighthouse.all.tiers.limit.max.saved.accounts}"/>
    <property name="salesAccountsTransferService.maxSavedAccountLimitAllTiers" value="${lighthouse.all.tiers.limit.max.saved.accounts}"/>


    <property name="__r2d2DefaultClient__.r2d2Client.clientServicesConfig">
      <map>
        <entry key="EDB-LssAlert">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssBookmark">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssBuyer">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssEntityView">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssColleagues">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssCrmDataValidation">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssLeadExtendedInfo">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssList">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssNote">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssRecentViews">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssCustomFilterView">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssSavedLeadAccount">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssSeatSetting">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EDB-LssSharing">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="EmailSender">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="assetMediaArtifactPublicUrls">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="businessProspectSources">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="businessProspects">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="buyerInsightsPinotQuery">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="capContractPrefs">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="capPermissions">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="capQueues">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="capUiPreferences">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="clientAwareEmailAddresses">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="contentClassificationSystemSignatures">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="contracts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmContracts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmDerivedMappings">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmLinkedInEntityMappings">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmManualMappings">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmPairings">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="crmUserMappings">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="enterpriseBulkLicenseAssignmentPlugins">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="enterpriseLicenseAssignments">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="enterpriseProfileActivationLinks">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="enterpriseProfiles">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="entitlements">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="epsilonRecordRequests">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="gaapTasks">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lixTreatments">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssBusinessProspectSources">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssBusinessProspects">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssCapAccounts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssCapContractPrefs">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssCapPermissions">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssCapQueues">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssCapUiPreferences">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssContracts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssProspectAtBusinesses">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssProspectCommHistory">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssProspectPosts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssProspectSources">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="lssSeats">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="memberBadges">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="oauth2">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="permissionGroups">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="profiles">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="prospectAtBusinesses">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="prospectPosts">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="prospectSources">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="prospects">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="provisionedApplications">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesActivityPinotQuery">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesActivityPinotStatistics">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesContractsV2">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesEPMapping">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesIdentities">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesSeatProfileMapping">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="salesSeatsV2">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="seats">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="sequential">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="sloThresholds">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="snapPinotStatistics">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
        <entry key="torrent">
          <map>
            <entry key="http.protocolVersion" value="HTTP_2"/>
          </map>
        </entry>
      </map>
    </property>

    <!-- Enable E8 filter -->
    <w:wildcard name="^.*.enableClientSLAFilter" type="^.*/com.linkedin.r2.client.factory.TransportClientFactoryFactory$" value="true"/>
    <w:wildcard name="^.*.isSLATracked" type="^.*/com.linkedin.d2.client.factory.D2ClientFactory$" value="true"/>

    <!--  Config for BuyerIntentService  -->
    <property name="buyerIntentService.buyerIntentTrendStoreName" value="lssBuyerIntentTrend"/>
    <property name="buyerIntentService.buyerIntentTrendV2StoreName" value="lssBuyerIntentTrendV2"/>

    <!-- Enable parseq batching -->
    <!-- Here we whitelist backend resource api calls that we want to batch automatically to reduce fanout -->
    <!-- Downstream resources must also support batch_get with appropriate ACLs in place before enabling) -->
    <!-- Keep this list in alphabetical order! -->
    <!-- https://github.com/linkedin/parseq/tree/master/subprojects/parseq-batching    -->
    <property name="defaultParSeqRestClient.parseqRestClient.batchingEnabled">
      <map>
        <entry key="*.*/*.*" value="false"/>
        <entry key="*.*/salesContractsV2.*" value="true"/>
        <entry key="*.*/salesSeatsV2.*" value="true"/>
      </map>
    </property>

    <property name="defaultParSeqRestClient.parseqRestClient.perEndpointBatchingSensors" value="true"/>

    <property name="defaultParSeqRestClient.parseqRestClient.maxBatchSize">
      <map>
        <entry key="*.*/*.*" value="25"/>
        <entry key="*.*/salesContractsV2.*" value="25"/>
        <entry key="*.*/salesSeatsV2.*" value="25"/>
      </map>
    </property>

    <!-- UCF client properties -->
    <property name="salesMemberRestrictionClient.ucfClient.appName" value="${com.linkedin.app.name}"/>
    <property name="salesMemberRestrictionClient.ucfClient.validAnyRecordModels" value="${ucf.client.valid.anyrecords.list}"/>

    <!-- adding events notification i18n bundle names -->
    <property name="resourceBundleHandler.bundleNames">
      <list>
        <value>com/linkedin/sales/lss-mt</value>
      </list>
    </property>

    <!-- Account Map Info Couchbase Configs -->
    <property name="salesSavedAccountCache.couchbase.clientName" value="sales-api-frontend"/>
    <property name="salesSavedAccountCache.couchbase.clientPool.bucketName" value="sales-api-frontend-saved-account-cache"/>
    <property name="salesSavedAccountCache.couchbase.clientPool.bootstrapUris" value="${in-couchbase.bucket.sales-api-frontend-saved-account-cache.bootstrapUris}"/>
    <property name="salesSavedAccountCache.couchbase.clientPool.enableCertAuth" value="true"/>
    <property name="salesSavedAccountCache.couchbase.clientPool.enableSsl" value="true"/>
    <property name="salesSavedAccountCache.ttlInMinutes" value="30"/>


    <!-- Config for use of Groot bundle -->
    <property name="policyEnforcerContextProvider.enableUseOfBundle" value="true"/>
  </configuration-source>
</application>
