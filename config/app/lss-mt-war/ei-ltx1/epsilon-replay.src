<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>

    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.actionWhenAclMissing" value="${epsilonReplay.accessClient.actionWhenAclMissing}"/>
    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.useDynamicAcls" value="${epsilonReplay.accessClient.useDynamicAcls}"/>
    <property name="__defaultGrpcServer__.grpcServer.d2.doNotStart" value="true"/>
    <property name="epsilon.replayDriver.useStreamRequests" value="${restliServlet.filterDispatcher.useStreamFilterChain}"/>
    <property name="autoRtf.isGrpcServer" value="true"/>
    <!-- Disable all Xinfra Producers -->
    <w:wildcard name=".*\.useXinfraProducer" value="false"/>
    
    <!-- Proxy URL prefix config -->
    <!-- For each entry, key will be the scheme name, and value is proxy URL prefix -->
    <!-- Attention: the slash "/" at the end of D2 proxy URL prefix string is required -->
    <property name="epsilonProxyClient.proxyUrlPrefixMap">
      <map>
        <entry key="d2" value="https://epsilon-replay-proxy.epsilon-proxy.ei-ltx1.atd-ds.disco.linkedin.com:10525/d2/"/>
      </map>
    </property>


 <!-- Disable enableBucketWaitUntilReadyBehavior to ignore li-couchbase-client UnambiguousTimeoutException -->
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerProviderFactory" value="false"/>
 <property name="cacheClientManager.enableBucketWaitUntilReadyBehavior" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.cache.client.CouchbaseClientPoolFactory" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerFactory" value="false"/>

  </configuration-source>
</application>
