<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>

    <property name="autoRtf.epsilonRecordServerInterceptor.sendRecordingToEpsilonDataBackend" value="${epsilonRecord.autoRtf.recordFilter.sendRecordingToEpsilonDataBackend}"/>
    <!-- Enabled EPSILON Recording Mode -->
    <property name="autoRtf.mode" value="RECORD"/>
    <property name="autoRtf.recordFilter.sendRecordingToEpsilonDataBackend" value="true"/>
    <property name="autoRtf.recordRestliServer.sendRecordingToKafka" value="false"/>

    <!-- lix configs -->
    <property name="lixClient.recordReplayEnabled" value="true"/>
    <property name="lixClient.lixRecordReplayClient.recordReplayType" value="EPSILON"/>

    <!-- OPTIONAL: QPS DEFAULT TO 1000 ms  -->
    <property name="autoRtfRecordFilter.recordingIntervalMs" value="1000"/>

  </configuration-source>
</application>
