<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>

    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.actionWhenAclMissing" value="${epsilonReplay.accessClient.actionWhenAclMissing}"/>
    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.useDynamicAcls" value="${epsilonReplay.accessClient.useDynamicAcls}"/>
    <property name="__defaultGrpcServer__.grpcServer.d2.doNotStart" value="true"/>
    <property name="epsilon.replayDriver.useStreamRequests" value="${restliServlet.filterDispatcher.useStreamFilterChain}"/>
    <property name="autoRtf.isGrpcServer" value="true"/>
    <!-- Disable all Xinfra Producers -->
    <w:wildcard name=".*\.useXinfraProducer" value="false"/>
    
    <!-- Enable EPSILON Replay mode -->
    <property name="autoRtf.mode" value="REPLAY"/>
    <property name="autoRtf.replayDriver.isEpsilonReplayRun" value="true"/>
    <property name="autoRtf.replayDriver.isExperimentBackendMocked" value="false"/>

    <!-- lix configs -->
    <property name="lixClient.recordReplayEnabled" value="true"/>
    <property name="lixClient.lixRecordReplayClient.recordReplayType" value="EPSILON"/>

    <!-- Epsilon Experiment Backend Configs -->
    <property name="epsilonRecordRequestDataClient.endpointRecordRequestGetRequestTimeoutMs" value="30000"/>

    <!-- Don't announce service to D2 for Offspring Apps -->
    <property name="restliServlet.d2.doNotStart" value="true"/>
    <property name="d2.qd.localdeploy" value="true"/>
    <property name="restLiServlet#defaultAnnouncer#d2.qd.localdeploy" value="true"/> <!-- for LiSpring apps-->

    <!-- Disable Dark Canary for replay machines -->
    <property name="restliServlet.filterDispatcher.enableDarkCanary" value="false"/>

    <!--DISABLE all kafka producers other than eventbus and the epsilon producer that produces replays STATS-->
    <w:wildcard name=".*\.disableKafkaProducer" value="true"/>
    <property name="autoRtf.replayDriver.testStatusNotifier.kafkaMetricsProducer.disableKafkaProducer" value="false"/>
    <property name="eventBus.trackingProducer.disableKafkaProducer" value="false"/>
    <w:wildcard name="^.*\.disabled" type="^.*com.linkedin.kafka.clients.factory.AvroKafkaProducerFactory.*" value="true"/>
    <property name="autoRtf.replayDriver.testStatusNotifier.kafkaMetricsProducer.producer.disabled" value="false"/>
    <property name="eventBus.trackingProducer.TrackingProducer.producer.disabled" value="false"/>

    <!--Configure EventBus for Epsilon (Sends service metrics with special Epsilon tag) -->
    <property name="eventBus.epsilon.enabled" value="true"/>
    <property name="eventBus.secureEpsilon.enabled" value="true"/>

    <!--Disable Updater Threads (which do not work properly for Epsilon Replays)-->
    <property name="fuseClient.restrictionUpdater.fetchInterval" value="1000m"/>
    <property name="fuseClient.conditionUpdater.fetchInterval" value="1000m"/>
    <property name="memberRestrictionUpdaterFactory.fetchInterval" value="1000m"/>
    <property name="lixClient.timeBetweenUpdates" value="36000000"/>
    <property name="lixClient.initialRetryIntervalWhenFail" value="36000000"/>

    <!-- Disable Dynamic ACLs. At startup, some services tries to contact the ACLService. -->
    <!-- When the ACLService can not be reached (since replay disables outbound requests), the service shuts down.-->
    <!-- We disable dynamic acls (not needed during replays) to prevent this from happening.-->
    <property name="restliServlet.dispatcher.authFilter.accessClient.useDynamicAcls" value="false"/>

    <!-- Disable due to not whitelisted to communicated with Azkaban -->
    <property name="crmDataValidationExportJobService.enabled" value="false"/>

    <!-- Add service to the static acl list in replay mode -->
    <property name="staticAclWhitelist">
      <set>
        <value>epsilon-lss-mt</value>
      </set>
    </property>

    <!-- Disable Data Vault access control during replays (In epsilon replays, service sends requests to itself-->
    <!-- so the service is no longer receiving the requests from the regular upstream services). -->
    <property name="restliServlet.dispatcher.authFilter.accessClient.actionWhenAclMissing" value="ALLOW"/>
    <property name="restliServlet.dispatcher.accessValidator.accessControlList"/>

    <!-- Epsilon Proxy Settings -->
    <!-- Epsilon Proxy Client SSL config -->
    <!-- If you need to use other key instead of default r2d2 security key, change here-->
    <property name="epsilonProxyClient.keyStoreFilePath" value="${__r2d2DefaultClient__.r2d2Client.keyStoreFilePath}"/>
    <property name="epsilonProxyClient.keyStoreType" value="${__r2d2DefaultClient__.r2d2Client.keyStoreType}"/>
    <property name="epsilonProxyClient.keyStorePassword" value="${__r2d2DefaultClient__.r2d2Client.keyStorePassword}"/>
    <property name="epsilonProxyClient.trustStoreFilePath" value="${__r2d2DefaultClient__.r2d2Client.trustStoreFilePath}"/>
    <property name="epsilonProxyClient.trustStorePassword" value="${__r2d2DefaultClient__.r2d2Client.trustStorePassword}"/>


    <!-- Proxy URL prefix config -->
    <!-- For each entry, key will be the scheme name, and value is proxy URL prefix -->
    <!-- Attention: the slash "/" at the end of D2 proxy URL prefix string is required -->
    <property name="epsilonProxyClient.proxyUrlPrefixMap">
      <map>
        <entry key="d2" value="https://epsilon-replay-proxy.epsilon-proxy.ei-ltx1.atd-ds.disco.linkedin.com:10525/d2/"/>
      </map>
    </property>

    <!-- Config for enabling proxy during Epsilon Replay run -->
    <property name="autoRtf.replayD2.enableProxyD2Requests" value="true"/>


    <!-- URL authority to path prefixes config -->
    <!-- Config the request type you want to forward here -->
    <!-- For each entry, key is the D2 service name, value is a list of path prefixes -->
    <!--
        For example, if I want to forward requests for "d2://VeniceRouter/discover_cluster/myVeniceStore" and "d2://VeniceRouter/discover_cluster/myAnotherVeniceStore",
        then having an entry with "VeniceRouter" as key and "/discover_cluster" in the value list will forward both of those D2 requests. If I only want to forward
        request for "d2://VeniceRouter/discover_cluster/myVeniceStore", then I need to have "/discover_cluster/myVeniceStore" in value list under key "VeniceRouter"
    -->
    <!-- Below is an example for forwarding all Venice discover_cluster, key_schema and value_schema requests to proxy service.
         If your MP does not use Venice, you do not have to include this section
    -->
    <property name="autoRtf.replayD2.authorityPathPrefixesNeedProxy">
      <map>
        <entry key="VeniceRouter">
          <list>
            <value>/discover_cluster</value>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-1">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-2">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-3">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-4">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-5">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-6">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-7">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-8">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-9">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-10">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
        <entry key="venice-11">
          <list>
            <value>/key_schema</value>
            <value>/value_schema</value>
          </list>
        </entry>
      </map>
    </property>


 <!-- Disable enableBucketWaitUntilReadyBehavior to ignore li-couchbase-client UnambiguousTimeoutException -->
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerProviderFactory" value="false"/>
 <property name="cacheClientManager.enableBucketWaitUntilReadyBehavior" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.cache.client.CouchbaseClientPoolFactory" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerFactory" value="false"/>

  </configuration-source>
</application>
