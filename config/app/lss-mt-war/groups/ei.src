<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <!-- Deprecated -->
    <property name="salesMailboxEspresso.espressoClient.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="lssListEspressoClient.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="espressoClient.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>

    <!-- Li DB settings -->
    <property name="db.snav.login.credentials" value="snav_app/snav_app8uat"/>
    <property name="db.snav.dbname" value="EI_EITM_SNAV"/>
    <property name="salesEspressoSd1.espresso.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="salesEspressoMd4.espresso.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="salesEspressoLd3.espresso.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="salesEspressoLd6.espresso.uriPrefix" value="${nuagemanaged.espresso.emt2.uriPrefix}"/>
    <property name="salesEspressoMt3.espresso.uriPrefix" value="${nuagemanaged.espresso.emt3.ei.uriPrefix}"/>
    <property name="lssSharing.espresso.uriPrefix" value="d2://EDB-LssSharing"/>
    <property name="lssList.espresso.uriPrefix" value="d2://EDB-LssList"/>
    <property name="lssColleagues.espresso.uriPrefix" value="d2://EDB-LssColleagues"/>
    <property name="lssSavedLeadAccount.espresso.uriPrefix" value="d2://EDB-LssSavedLeadAccount"/>
    <property name="lssEditableContactInfo.espresso.uriPrefix" value="d2://EDB-LssEditableContactInfo"/>
    <property name="lssLeadExtendedInfo.espresso.uriPrefix" value="d2://EDB-LssLeadExtendedInfo"/>
    <property name="lssNote.espresso.uriPrefix" value="d2://EDB-LssNote"/>
    <property name="lssRecentViews.espresso.uriPrefix" value="d2://EDB-LssRecentViews"/>
    <property name="lssSeatSetting.espresso.uriPrefix" value="d2://EDB-LssSeatSetting"/>
    <property name="lssAlert.espresso.uriPrefix" value="d2://EDB-LssAlert"/>
    <property name="lssBookmark.espresso.uriPrefix" value="d2://EDB-LssBookmark"/>
    <property name="lssBuyer.espresso.uriPrefix" value="d2://EDB-LssBuyer"/>
    <property name="lssEntityView.espresso.uriPrefix" value="d2://EDB-LssEntityView"/>
    <property name="lssCustomFilterView.espresso.uriPrefix" value="d2://EDB-LssCustomFilterView"/>
    <property name="lssCoach.espresso.uriPrefix" value="d2://EDB-LssCoach"/>
    <property name="lssAutoFinder.espresso.uriPrefix" value="d2://EDB-LssAutoFinder"/>
    <property name="lssEntityMapping.espresso.uriPrefix" value="d2://EDB-LssEntityMapping"/>
    <property name="lssAutoProspecting.espresso.uriPrefix" value="d2://EDB-LssAutoProspecting"/>
    <property name="lssWarmIntros.espresso.uriPrefix" value="d2://EDB-LssWarmIntros"/>
    <property name="salesInviteRegisterUrlService.inviteRegisterUrlPrefix" value="https://www.linkedin-ei.com/sales/register"/>

    <!-- Enable Service Level Failouts -->
    <property name="__r2d2DefaultClient__.r2d2Client.clusterFailoutEnabled" value="true"/>
    <property name="failoutConfigProviderFactoryFactory.enableConnectionWarmUp" value="true"/>

    <!-- Enable IPV4 when IPV6 is not available, because k8s doesn't support IPV6 right now: APA-100220  -->
    <property name="opt.com.linkedin.jvml.net" value="-Djava.net.preferIPV6Addresses=false"/>
  </configuration-source>
</application>
