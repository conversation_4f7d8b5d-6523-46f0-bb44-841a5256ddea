<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <!-- Deprecated -->
    <property name="salesMailboxEspresso.espressoClient.uriPrefix" value="${nuagemanaged.espresso.md4.uriPrefix}"/>
    <property name="lssListEspressoClient.uriPrefix" value="d2://EDB-LssList"/>
    <property name="espressoClient.uriPrefix" value="${nuagemanaged.espresso.md4.uriPrefix}"/>

    <!-- Li DB settings -->
    <property name="db.snav.dbname" value="PROD_PCAP2_SNAV"/>
    <property name="salesEspressoSd1.espresso.uriPrefix" value="d2://EDB-LssCrmDataValidation"/>
    <property name="salesEspressoMd4.espresso.uriPrefix" value="${nuagemanaged.espresso.md4.uriPrefix}"/>
    <property name="salesEspressoLd3.espresso.uriPrefix" value="d2://EDB-LssRecentViews"/>
    <property name="salesEspressoWaterloo2.espresso.uriPrefix" value="d2://EDB-LssCustomFilterView"/>
    <property name="salesEspressoLd6.espresso.uriPrefix" value="${nuagemanaged.espresso.ld6.uriPrefix}"/>
    <property name="lssSharing.espresso.uriPrefix" value="d2://EDB-LssSharing"/>
    <property name="lssList.espresso.uriPrefix" value="d2://EDB-LssList"/>
    <property name="lssColleagues.espresso.uriPrefix" value="d2://EDB-LssColleagues"/>
    <property name="lssSavedLeadAccount.espresso.uriPrefix" value="d2://EDB-LssSavedLeadAccount"/>
    <property name="lssNote.espresso.uriPrefix" value="d2://EDB-LssNote"/>
    <property name="lssEditable.espresso.uriPrefix" value="d2://EDB-LssEditableContactInfo"/>
    <property name="lssLeadExtendedInfo.espresso.uriPrefix" value="d2://EDB-LssLeadExtendedInfo"/>
    <property name="lssRecentViews.espresso.uriPrefix" value="d2://EDB-LssRecentViews"/>
    <property name="lssSeatSetting.espresso.uriPrefix" value="d2://EDB-LssSeatSetting"/>
    <property name="lssAlert.espresso.uriPrefix" value="d2://EDB-LssAlert"/>
    <property name="lssBookmark.espresso.uriPrefix" value="d2://EDB-LssBookmark"/>
    <property name="lssBuyer.espresso.uriPrefix" value="d2://EDB-LssBuyer"/>
    <property name="lssEntityView.espresso.uriPrefix" value="d2://EDB-LssEntityView"/>
    <property name="lssCustomFilterView.espresso.uriPrefix" value="d2://EDB-LssCustomFilterView"/>
    <property name="lssCoach.espresso.uriPrefix" value="d2://EDB-LssCoach"/>
    <property name="lssEntityMapping.espresso.uriPrefix" value="d2://EDB-LssEntityMapping"/>
    <property name="lssAutoFinder.espresso.uriPrefix" value="d2://EDB-LssAutoFinder"/>
    <property name="lssAutoProspecting.espresso.uriPrefix" value="d2://EDB-LssAutoProspecting"/>
    <property name="lssWarmIntros.espresso.uriPrefix" value="d2://EDB-LssWarmIntros"/>
    <!-- Azkaban config -->
    <property name="crmDataValidationExportJobService.azkabanUrl" value="https://lva1-waraz01.grid.linkedin.com:8443/"/>
    <property name="crmDataValidationExportJobService.azkabanFlowName" value="data-validation-on-demand-export_azkabanWar"/>

    <property name="restliServlet.dispatcher.authFilter.accessClient.actionWhenAclMissing" value="DENY" />

  </configuration-source>
</application>
