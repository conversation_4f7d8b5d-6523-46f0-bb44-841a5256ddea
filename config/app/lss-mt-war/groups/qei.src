<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <property name="qdProfileController.skipQeiRoute" value="false"/>
    <property name="lssNote.espresso.uriPrefix" value="d2://EDB-LssNote"/>
    <property name="lssLeadExtendedInfo.espresso.uriPrefix" value="d2://EDB-LssLeadExtendedInfo"/>
    <property name="lssEntityView.espresso.uriPrefix" value="d2://EDB-LssEntityView"/>
    <property name="lssCustomFilterView.espresso.uriPrefix" value="d2://EDB-LssCustomFilterView"/>
    <property name="lssBuyer.espresso.uriPrefix" value="${nuagemanaged.espresso.dev.uriPrefix}"/>
    <property name="lssCoach.espresso.uriPrefix" value="d2://EDB-LssCoach"/>
    <property name="lssAutoFinder.espresso.uriPrefix" value="d2://EDB-LssAutoFinder"/>
    <property name="lssEntityMapping.espresso.uriPrefix" value="d2://EDB-LssEntityMapping"/>
    <property name="lssAutoProspecting.espresso.uriPrefix" value="d2://EDB-LssAutoProspecting"/>
  </configuration-source>
</application>
