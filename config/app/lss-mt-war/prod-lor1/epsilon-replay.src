<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>

    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.actionWhenAclMissing" value="${epsilonReplay.accessClient.actionWhenAclMissing}"/>
    <property name="__defaultGrpcServer__.grpcServer.serverInterceptors.serverDataVaultAuthInterceptor.accessClient.useDynamicAcls" value="${epsilonReplay.accessClient.useDynamicAcls}"/>
    <property name="__defaultGrpcServer__.grpcServer.d2.doNotStart" value="true"/>
    <property name="epsilon.replayDriver.useStreamRequests" value="${restliServlet.filterDispatcher.useStreamFilterChain}"/>
    <property name="autoRtf.isGrpcServer" value="true"/>
    <!-- Disable all Xinfra Producers -->
    <w:wildcard name=".*\.useXinfraProducer" value="false"/>
    
    <!-- Proxy URL prefix config -->
    <!-- For each entry, key will be the scheme name, and value is proxy URL prefix -->
    <!-- Attention: the slash "/" at the end of D2 proxy URL prefix string is required -->
    <property name="epsilonProxyClient.proxyUrlPrefixMap">
      <map>
        <entry key="d2" value="https://epsilon-replay-proxy.epsilon-proxy.prod-lor1.atd-ds.disco.linkedin.com:10525/d2/"/>
      </map>
    </property>


    <!-- Disable Data Vault access control during replays (In epsilon replays, service sends requests to itself-->
    <!-- so the service is no longer receiving the requests from the regular upstream services). -->
    <property name="restliServlet.dispatcher.authFilter.accessClient.actionWhenAclMissing" value="DENY"/>

    <!-- Enable static ACLs by endpoint for epsilon replay requests -->
    <!-- Duplicate each entry as necessary and replace urn:li:restli:endpoint with your application's endpoints -->
    <property name="restliServlet.dispatcher.accessValidator.accessControlList">
      <map>
        <entry key="urn:li:restli:crmDataValidationExportJobs">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:crmUserOnboardingBulkActionPlugin">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:enterpriseSalesSeats">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesAccessTokens">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesActivityTotals">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesAnalyticsExportJobs">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesColleagueRelationships">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesContracts">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesInvitations">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesListEntities">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesListEntitySummaries">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesLists">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesMessagingPlugin">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesNavigatorEnterpriseApplicationNotices">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesNavigatorProfileAssociations">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesNavigatorSaveLead">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesNotes">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesRecentActivities">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesRecentSearches">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesSeats">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesSharingPolicies">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesLeadAccountAssociations">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesLeadAccountMetadata">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesSavedAccounts">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
        <entry key="urn:li:restli:salesSavedLeads">
          <map>
            <entry key="currentAccessControlListState" value="ENABLED"/>
            <entry key="currentAccessControlList">
              <list>
                <map>
                  <entry key="action" value="ALLOW"/>
                  <entry key="methods">
                    <list>
                      <value>ALL</value>
                    </list>
                  </entry>
                  <entry key="principals">
                    <map>
                      <entry key="servicePrincipals">
                        <list>
                          <map>
                            <entry key="applicationName" value="epsilon-lss-mt"/>
                          </map>
                        </list>
                      </entry>
                    </map>
                  </entry>
                </map>
              </list>
            </entry>
          </map>
        </entry>
      </map>
    </property>

    <!-- Generate datavault cert for prod deployment (datavault restricted) -->
    <property name="autoRtf.replayDriver.useDataVaultCert" value="true"/>


 <!-- Disable enableBucketWaitUntilReadyBehavior to ignore li-couchbase-client UnambiguousTimeoutException -->
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerProviderFactory" value="false"/>
 <property name="cacheClientManager.enableBucketWaitUntilReadyBehavior" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.cache.client.CouchbaseClientPoolFactory" value="false"/>
 <w:wildcard name="^.*.enableBucketWaitUntilReadyBehavior" type="^.*/com.linkedin.couchbase.client.LiCouchbaseClientContainerFactory" value="false"/>

  </configuration-source>
</application>
