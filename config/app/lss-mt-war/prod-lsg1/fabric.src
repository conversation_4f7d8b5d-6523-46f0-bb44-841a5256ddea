<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <!-- Li <PERSON> settings -->
    <property name="db.snav.login.credentials" value="@SecretAlias:(urn:li:kmsSecret:362eed00-6a3b-4a03-b525-40c4c32d0cb7),@SecretVersion:(AAAAAAARMqk)"/>
    <property name="db.snav.hostname" value="lsg1-db-snav.prod.linkedin.com"/>

    <!-- Azkaban private key -->
    <property name="lsseng.azkaban.privateKey" value="@SecretAlias:(urn:li:kmsSecret:eebe51f2-0372-4858-86cc-e7f91040f261),@SecretVersion:(AAAAAAAUPAE)"/>
  </configuration-source>
</application>
