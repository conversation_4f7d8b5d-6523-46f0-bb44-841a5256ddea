<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0" xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0">
  <configuration-source>
    <!-- Li <PERSON> settings -->
    <property name="db.snav.hostname" value="ltx1-eitm-snav.stg.linkedin.com"/>
    <!-- Increase timeout for dev testing -->
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.connectTimeoutMs" value="10000"/>
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.sslHandShakeTimeoutMs" value="10000"/>
    <property name="__r2d2DefaultClient__.r2d2Client.r2Client.channelPoolWaiterTimeoutMs" value="10000"/>
    <!-- <PERSON>zkaban private key -->
    <property name="lsseng.azkaban.privateKey" value="MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCud/yZ8enBUYxqQ8Js9LCKx/m/ZonFA89XvyRo5agsybwoN6rSK5GJJd9Cmoub17GRSfVDg9l4xvu55eTIFbSOeB0hJ06IXvS/3cUIk7VPudFhlD35D77M2JNOmZGIX9+gmnHLCw1PpAQAvBbtsBcOpvFYn+eAesuYNQ3w5mO0NTnKfxo71DN3qQTVxIfJpoxF0MpRfdPPGJiVPhUhp+v4t+Gv4TkNQu4aMpjGF+S4azd0B+5EQLv7riXnUE9W9I4e+Fy9UyfbJpmNNyTOcYJ1Kez/DTcBB23oS7FTB+Nzd1PZXm4WsZJoBcpK2PUUsd4giP5ZgXx7o76Xxis3/L4tAgMBAAECggEAPMFf36QUiNODGEY8YIDdMZbt668jbs4i2CWh4j2HUQF3WnH4NHQjnCTqGGGFGGIGXdfpZvd1Nc/lPRlHkD2uwKYnTrJHcMnQZTgvMwts6jSFK3vaEO9gvCj3MznbKiho0Z623cQdgIxXMIJPYboebWXJSdl1XpJkb76Z+jfgpJIJG6xR0m8HnnRk6QSJ9FS8XPG2/I9vjKhK29BSgCq7ipNEeClP4hxwff2cESV3CEEeoYseImQTryKzMneObJJkkkxrpRvC3AvCYpJJjrKktuhDwApXHsn1kyz+mBLLp6P6m6hC0qwt8azUINQnnxtc3AbDU53vYSEvdIKpnG8mQQKBgQDcH+9/LLnq6AU6NrmgEHiK8mmaFrUDEm6HRyOTLI2T+9XiLBTF6ZyW6fqZeW0tvLWi52ck0e22DaVMugLTTDwIDhEWrznPpDUgVRFioDEgvvb4vZpmndQPeyAoYZKLa4Zhv+84Hc3aqHJSDFNQ6vqePkIfMHQZSmbGZ0zoRFDxOQKBgQDK5y91BioIKE5vdD5nY2enbzLTuqgD0btqNjhRzrng74wyZxW+JOzHMu6ixWoften0cwh7lPu1D/OCaOFXHP7pCgy9aW+9fHA5O4FP7uTsP++Tcoi97wXSn9fbWwT+tuO04TQuJ5CAVjcKs1AVx43KfbUOBZG3pXFHuRkyF1sYlQKBgQCilrgYsFmWx8/sorVTJDdVMh6MIQ+MeFXJd+Mv9u2QC6h8iWDCmn39/k8Kg2UALm3fKJrH2IODP+sDlDU4CwwgSvuWTNEfrMNM4WiY94eyDztumOVtvFABK0miageLEV3N3637PhBp3JesQMxdbWK19Xhzfx+R+qGVFIH64VMECQKBgQCxA6Xkaz9KJJfhJkG3AwMQELQAAYzI4DWoAKt7u2VwtqvR4RsX6rDjTCzitmXSSmtUBvN0lVJO8R6ZPBSzYimFd24L5StF3YUueRpkeN8guNqzAS4IvbmPukGgLjUaXsYjLIHQblXjkNOuMyeACLtL2AWXuQYaeGxujS5NBBFGTQKBgDumDqurmp8FHlBpJC9eQX1eRdF/S83EW/SMTh+hmeNnGIR48vEyQywLfRg25HnmO1jLrBkwxnOMve2bBlCsTjgfGG4Z9Ail4J1gh7ushgea1m2D1ltots8fN3/y5m58ZslBria79gQOGV01bznagRRKjriJg0Y+MyBBm772RPpd"/>
    <!-- Enable IPV4 when IPV6 is not available, because k8s doesn't support IPV6 right now: APA-100220  -->
    <property name="opt.com.linkedin.jvml.net" value="-Djava.net.preferIPV6Addresses=false"/>
  </configuration-source>
</application>
