<?xml version='1.0' encoding='UTF-8'?>
<application xmlns:w="urn:com:linkedin:ns:configuration:wildcard:1.0" xmlns="urn:com:linkedin:ns:configuration:source:1.0">
  <configuration-source>
    <property name="jvm_gc_opts">
      <list/>
    </property>
    <!-- jemalloc is helps with memory fragmentation on Java 11 -->
    <property name="env_vars">
      <map>
        <entry key="LD_PRELOAD" value="/usr/lib64/libjemalloc.so.2"/>
      </map>
    </property>
    <property name="jvm_app_info">
      <list/>
    </property>
    <property name="jvm_size">
      <list>
        <value>-server</value>
        <value>-Xms4g</value>
        <value>-Xmx4g</value>
      </list>
    </property>
    <property name="jvm_options">
      <list>
        <value>${jvm_gc_opts}</value>
        <value>${jvm_app_info}</value>
        <value>${jvm_size}</value>
        <value>${jvm_xtra_args}</value>
        <value>${jvm_gc_type}</value>
        <value>${jvm_debug}</value>
      </list>
    </property>
    <property name="jvm_xtra_args">
      <list>
        <value>-Djava.awt.headless=true</value>
      </list>
    </property>
    <property name="jvm_gc_type">
      <list>
        <value>-XX:+UseG1GC</value>
      </list>
    </property>
    <property name="jvm_debug">
      <list>
        </list>
    </property>
    <property name="arguments">
      <list>
        <value>components</value>
        <value>-Pcom.linkedin.container.port.https=${com.linkedin.metadata.application.port.https}</value>
        <value>-Pcontainer.server.type=H2_AND_H2C</value>
      </list>
    </property>
    <property name="user_jvm_options_11">
      <list/>
    </property>
    <property name="jvm_gc_log_11">
      <!--GC Logging has drastically changed in Java 9+. A default option
        "-Xlog:gc*,gc+age=trace,gc+phases=debug:file=logs/gc.log:tags,uptime,time,level:filecount=5,filesize=100M"
        is provided through global configs and also specified here.
        If you have custom settings today, please check go/jvm-option-11 on how you can override it.-->
      <list>
        <value>-Xlog:gc*,gc+age=trace,gc+phases=debug:file=logs/gc.log:tags,uptime,time,level:filecount=5,filesize=100M</value>
      </list>
    </property>
    <property name="jvm_gc_type_11">
      <list>
        <value>-XX:+UseG1GC</value>
      </list>
    </property>
    <property name="jvm_gc_type_17">
      <list>
        <value>-XX:+UseG1GC</value>
      </list>
    </property>
  </configuration-source>
</application>