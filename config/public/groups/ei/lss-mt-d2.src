<?xml version="1.0" encoding="UTF-8"?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0">
  <configuration-source>
    <property name="lssMtCluster.transportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="${d2::d2.http.requestTimeoutOld}"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccessTokensTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAnalyticsExportJobsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesContractsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.transportClientPropertiesShort">
      <map>
        <entry key="http.requestTimeout" value="6000"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNavigatorEnterpriseApplicationNoticesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNavigatorProfileAssociationsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNavigatorSaveLeadTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNotesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesRecentActivitiesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesRecentSearchesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSeatSettingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAlertsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesBookmarksTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadEditableContactInfoTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadProfileUnlockInfoTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSharingPoliciesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadAccountAssociationsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadAccountMetadataTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSavedAccountsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSavedLeadsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesMobileSettingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccountToListMappingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesDefaultListsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="buyerIntentTrendTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="productCategoryInterestsTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSellerIdentitiesTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSubscriptionBenefitsTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="1000"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesProfileViewsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesPinnedFiltersTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesCoachConversationTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="accountPlaysTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="accountPlaysMetadataTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="manualEmailToMemberMappingTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccountsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="100000"/>
      </map>
    </property>
    <property name="salesLeadsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="100000"/>
      </map>
    </property>
    <property name="salesLeadAccountAssociationsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="100000"/>
      </map>
    </property>
    <property name="salesEntityNotesSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="100000"/>
      </map>
    </property>
    <property name="salesCustomListsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="100000"/>
      </map>
    </property>
    <property name="salesUpsellFlowConfigApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="searchCriteriaTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunLeadsApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunQueueApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="WarmIntroRecommendationApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
  </configuration-source>
</application>
