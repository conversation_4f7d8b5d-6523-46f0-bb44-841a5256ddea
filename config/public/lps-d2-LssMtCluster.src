<?xml version="1.0" encoding="UTF-8"?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0">
  <configuration-source>
    <property name="LssMtCluster">
      <map>
        <entry key="services">
          <map>
            <entry key="crmDataValidationExportJobs">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/crmDataValidationExportJobs"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="crmUserOnboardingBulkActionPlugin">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/crmUserOnboardingBulkActionPlugin"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="enterpriseSalesSeats">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/enterpriseSalesSeats"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccessTokens">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesAccessTokens"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccessTokensTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesActivityTotals">
              <map>
                <entry key="path" value="/salesActivityTotals"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${d2::d2.defaultRelativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAnalyticsExportJobs">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesAnalyticsExportJobs"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAnalyticsExportJobsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesColleagueRelationships">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderProperties}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesColleagueRelationships"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesContracts">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesContracts"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesContractsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListEntities">
              <map>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="path" value="/salesListEntities"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShortAllowingRequestTimeoutOverride}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${d2::d2.defaultRelativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListEntitySummaries">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesListEntitySummaries"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLists">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLists"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesAllowingRequestTimeoutOverride}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorEnterpriseApplicationNotices">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesNavigatorEnterpriseApplicationNotices"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorEnterpriseApplicationNoticesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSolutionsEnterpriseEmailPlugins">
              <map>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSolutionsEnterpriseEmailPluginsTransportClientProperties}"/>
                <entry key="prioritizedSchemes" value="${lss-mt-d2::lssMtCluster.prioritizedSchemes}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="path" value="/salesSolutionsEnterpriseEmailPlugins"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorSeatDataExtensions">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesNavigatorSeatDataExtensions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorProfileAssociations">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesNavigatorProfileAssociations"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorProfileAssociationsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorSaveLead">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesNavigatorSaveLead"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorSaveLeadTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNotes">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesNotes"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNotesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRecentActivities">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesRecentActivities"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesRecentActivitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRecentSearches">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesRecentSearches"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesRecentSearchesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSeatSettings">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSeatSettings"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSeatSettingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAlerts">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesAlerts"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAlertsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesBookmarks">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesBookmarks"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesBookmarksTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadEditableContactInfo">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadEditableContactInfo"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadEditableContactInfoTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadProfileUnlockInfo">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadProfileUnlockInfo"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadProfileUnlockInfoTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSharingPolicies">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSharingPolicies"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSharingPoliciesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountAssociations">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadAccountAssociations"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountAssociationsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountMetadata">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadAccountMetadata"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountMetadataTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSavedAccounts">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSavedAccounts"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSavedAccountsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSavedLeads">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSavedLeads"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSavedLeadsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="buyerEngagementDailyActivities">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/buyerEngagementDailyActivities"/>
                <entry key="prioritizedSchemes" value="${lss-mt-d2::lssMtCluster.prioritizedSchemes}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesMobileSettings">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesMobileSettings"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesMobileSettingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListCsvImports">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesListCsvImports"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccountToListMappings">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesAccountToListMappings"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccountToListMappingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${salesAccountToListMappings.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesDefaultLists">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesDefaultLists"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRelationshipMapChangeLogs">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesRelationshipMapChangeLogs"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRealtimeSharedResourceViewerAuthorization">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesRealtimeSharedResourceViewerAuthorization"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRealtimeRelationshipMapChangeLogAuthorization">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesRealtimeRelationshipMapChangeLogAuthorization"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="buyerIntentTrend">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value="/buyerIntentTrend"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::buyerIntentTrendTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${buyerIntentTrend.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="contractSellerIdentities">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/contractSellerIdentities"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSellerIdentitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="productCategoryInterests">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/productCategoryInterests"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::productCategoryInterestsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSellerIdentities">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSellerIdentities"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSellerIdentitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadPositionChangeCommunicationPlugin">
              <map>
                <entry key="path" value="/salesLeadPositionChangeCommunicationPlugin"/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCommunicationPlugin">
              <map>
                <entry key="path" value="/salesCommunicationPlugin"/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesPeopleYouMayKnowRecommendations">
              <map>
                <entry key="path" value="/salesPeopleYouMayKnowRecommendations"/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSubscriptionBenefits">
              <map>
                <entry key="degraderProperties">
                  <map>
                    <entry key="degrader.highLatency" value="150"/>
                    <entry key="degrader.lowLatency" value="50"/>
                  </map>
                </entry>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.highWaterMark" value="750"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="500"/>
                  </map>
                </entry>
                <entry key="path" value="/salesSubscriptionBenefits"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSubscriptionBenefitsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesProfileViews">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesProfileViews"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesProfileViewsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesPinnedFilters">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesPinnedFilters"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesPinnedFiltersTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCoachConversation">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesCoachConversation"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesCoachConversationTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="accountPlays">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/accountPlays"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::accountPlaysTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="accountPlaysMetadata">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/accountPlaysMetadata"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::accountPlaysMetadataTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="manualEmailToMemberMapping">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/manualEmailToMemberMapping"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::manualEmailToMemberMappingTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccountsSeatTransferActions">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesAccountsSeatTransferActions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccountsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadsSeatTransferActions">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadsSeatTransferActions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountAssociationsSeatTransferActions">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesLeadAccountAssociationsSeatTransferActions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountAssociationsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesEntityNotesSeatTransferActions">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesEntityNotesSeatTransferActions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesEntityNotesSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCustomListsSeatTransferActions">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value="/salesCustomListsSeatTransferActions"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesCustomListsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
          </map>
        </entry>
        <entry key="sslSessionValidationStrings">
          <list>
            <value>lss-mt</value>
          </list>
        </entry>
      </map>
    </property>
    <property name="lssMtCluster.loadBalancerStrategyList">
      <list>
        <value>relative</value>
        <value>degrader</value>
      </list>
    </property>
    <property name="salesAccountToListMappings.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
    <property name="buyerIntentTrend.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
    <property name="lssMtCluster.default.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
  </configuration-source>
</application>
