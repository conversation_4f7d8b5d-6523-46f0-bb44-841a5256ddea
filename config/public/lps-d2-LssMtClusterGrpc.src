<?xml version='1.0' encoding='UTF-8'?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0">
  <configuration-source>
    <property name="LssMtClusterGrpc">
      <map>
        <entry key="coloVariants" value="${d2::peerColos}"/>
        <entry key="services">
          <map>
            <entry key="crmDataValidationExportJobsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="crmUserOnboardingBulkActionPluginGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="enterpriseSalesSeatsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccessTokensGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccessTokensTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesActivityTotalsGrpc">
              <map>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${d2::d2.defaultRelativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAnalyticsExportJobsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAnalyticsExportJobsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesColleagueRelationshipsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderProperties}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesContractsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesContractsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListEntitiesGrpc">
              <map>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShortAllowingRequestTimeoutOverride}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${d2::d2.defaultRelativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListEntitySummariesGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesAllowingRequestTimeoutOverride}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorEnterpriseApplicationNoticesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorEnterpriseApplicationNoticesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSolutionsEnterpriseEmailPluginsGrpc">
              <map>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSolutionsEnterpriseEmailPluginsTransportClientProperties}"/>
                <entry key="prioritizedSchemes" value="${lss-mt-d2::lssMtCluster.prioritizedSchemes}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="path" value=""/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorSeatDataExtensionsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorProfileAssociationsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorProfileAssociationsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNavigatorSaveLeadGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNavigatorSaveLeadTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesNotesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesNotesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRecentActivitiesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesRecentActivitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRecentSearchesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesRecentSearchesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSeatSettingsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSeatSettingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAlertsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAlertsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesBookmarksGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesBookmarksTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadEditableContactInfoGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadEditableContactInfoTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadProfileUnlockInfoGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadProfileUnlockInfoTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSharingPoliciesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSharingPoliciesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountAssociationsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountAssociationsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountMetadataGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountMetadataTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSavedAccountsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSavedAccountsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSavedLeadsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSavedLeadsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="buyerEngagementDailyActivitiesGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="prioritizedSchemes" value="${lss-mt-d2::lssMtCluster.prioritizedSchemes}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesMobileSettingsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesMobileSettingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesListCsvImportsGrpc">
              <map>
                <entry key="degraderProperties" value="${d2::d2.defaultDegraderPropertiesOld}"/>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMarkOld}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMarkOld}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccountToListMappingsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccountToListMappingsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${salesAccountToListMappings.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesDefaultListsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRelationshipMapChangeLogsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRealtimeSharedResourceViewerAuthorizationGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesRealtimeRelationshipMapChangeLogAuthorizationGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesDefaultListsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="buyerIntentTrendGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                    <entry key="http.loadBalancer.highWaterMark" value="${d2::d2.http.loadBalancer.highWaterMark}"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="${d2::d2.http.loadBalancer.lowWaterMark}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::buyerIntentTrendTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${buyerIntentTrend.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="contractSellerIdentitiesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSellerIdentitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="productCategoryInterestsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::productCategoryInterestsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSellerIdentitiesGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSellerIdentitiesTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadPositionChangeCommunicationPluginGrpc">
              <map>
                <entry key="path" value=""/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCommunicationPluginGrpc">
              <map>
                <entry key="path" value=""/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="loadBalancerStrategyProperties" value="${lss-mt-d2::lssMtCluster.loadBalancerStrategyPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesPeopleYouMayKnowRecommendationsGrpc">
              <map>
                <entry key="path" value=""/>
                <entry key="degraderProperties" value="${lss-mt-d2::lssMtCluster.degraderPropertiesShort}"/>
                <entry key="transportClientProperties" value="${lss-mt-d2::lssMtCluster.transportClientPropertiesShort}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesSubscriptionBenefitsGrpc">
              <map>
                <entry key="degraderProperties">
                  <map>
                    <entry key="degrader.highLatency" value="150"/>
                    <entry key="degrader.lowLatency" value="50"/>
                  </map>
                </entry>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.highWaterMark" value="750"/>
                    <entry key="http.loadBalancer.lowWaterMark" value="500"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesSubscriptionBenefitsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesProfileViewsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesProfileViewsTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesPinnedFiltersGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesPinnedFiltersTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCoachConversationGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesCoachConversationTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="accountPlaysGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::accountPlaysTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="accountPlaysMetadataGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::accountPlaysMetadataTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="manualEmailToMemberMappingGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::manualEmailToMemberMappingTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesAccountsSeatTransferActionsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesAccountsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadsSeatTransferActionsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesLeadAccountAssociationsSeatTransferActionsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesLeadAccountAssociationsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesEntityNotesSeatTransferActionsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesEntityNotesSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesCustomListsSeatTransferActionsGrpc">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesCustomListsSeatTransferActionsProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="salesUpsellFlowConfigApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::salesUpsellFlowConfigApiProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="searchCriteriaApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::searchCriteriaTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="LeadFindingRunApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::LeadFindingRunApiProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="campaignApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::campaignTransportClientProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="LeadFindingRunLeadsApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::LeadFindingRunLeadsApiProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="LeadFindingRunQueueApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::LeadFindingRunQueueApiProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
            <entry key="WarmIntroRecommendationApi">
              <map>
                <entry key="loadBalancerStrategyProperties">
                  <map>
                    <entry key="http.loadBalancer.consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
                  </map>
                </entry>
                <entry key="path" value=""/>
                <entry key="transportClientProperties" value="${lss-mt-d2::WarmIntroRecommendationApiProperties}"/>
                <entry key="loadBalancerStrategyList" value="${lssMtCluster.loadBalancerStrategyList}"/>
                <entry key="relativeStrategyProperties" value="${lssMtCluster.default.relativeStrategyProperties}"/>
              </map>
            </entry>
          </map>
        </entry>
        <entry key="sslSessionValidationStrings">
          <list>
            <value>lss-mt</value>
          </list>
        </entry>
      </map>
    </property>
    <property name="lssMtCluster.loadBalancerStrategyList">
      <list>
        <value>relative</value>
        <value>degrader</value>
      </list>
    </property>
    <property name="salesAccountToListMappings.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
    <property name="buyerIntentTrend.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
    <property name="lssMtCluster.default.relativeStrategyProperties">
      <map>
        <entry key="ringProperties">
          <map>
            <entry key="consistentHashAlgorithm" value="${lss-mt-d2::LssMtCluster.consistentHashAlgorithm}"/>
          </map>
        </entry>
      </map>
    </property>
  </configuration-source>
</application>