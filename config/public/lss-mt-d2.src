<?xml version="1.0" encoding="UTF-8"?>
<application xmlns="urn:com:linkedin:ns:configuration:source:1.0">
  <configuration-source>
    <property name="salesMobileSettingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadAccountMetadataTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSharingPoliciesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccountToListMappingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesDefaultListsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadProfileUnlockInfoTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSeatSettingsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.requestTimeout" value="5000"/>
      </map>
    </property>
    <property name="salesBookmarksTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesRecentSearchesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesRecentActivitiesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesContractsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSavedLeadsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNotesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNavigatorSaveLeadTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAlertsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAnalyticsExportJobsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="buyerIntentTrendTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="productCategoryInterestsTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSellerIdentitiesTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesLeadEditableContactInfoTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccessTokensTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesSavedAccountsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesNavigatorProfileAssociationsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesProfileViewsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesPinnedFiltersTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesCoachConversationTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="accountPlaysTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="accountPlaysMetadataTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="manualEmailToMemberMappingTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesAccountsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="600000"/>
      </map>
    </property>
    <property name="salesLeadsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="600000"/>
      </map>
    </property>
    <property name="salesLeadAccountAssociationsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="600000"/>
      </map>
    </property>
    <property name="salesEntityNotesSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="600000"/>
      </map>
    </property>
    <property name="salesCustomListsSeatTransferActionsProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="600000"/>
      </map>
    </property>
    <property name="lssMtCluster.transportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="${d2::d2.http.requestTimeoutOld}"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.transportClientPropertiesAllowingRequestTimeoutOverride">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="10000"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion,http.requestTimeout"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.loadBalancerStrategyPropertiesShort">
      <map>
        <entry key="consistentHashAlgorithm" value="${LssMtCluster.consistentHashAlgorithm}"/>
        <entry key="highWaterMark" value="3000"/>
        <entry key="lowWaterMark" value="500"/>
      </map>
    </property>
    <property name="lssMtCluster.loadBalancerStrategyPropertiesShortAllowingRequestTimeoutOverride">
      <map>
        <entry key="consistentHashAlgorithm" value="${LssMtCluster.consistentHashAlgorithm}"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion,http.requestTimeout"/>
        <entry key="highWaterMark" value="3000"/>
        <entry key="lowWaterMark" value="500"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.requestTimeout" value="6000"/>
      </map>
    </property>
    <property name="salesSolutionsEnterpriseEmailPluginsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.poolSize,http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="10000"/>
        <entry key="http.queryPostThreshold" value="3000"/>
        <entry key="http.maxResponseSize" value="8192000"/>
      </map>
    </property>
    <property name="salesNavigatorEnterpriseApplicationNoticesTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.transportClientPropertiesShort">
      <map>
        <entry key="http.requestTimeout" value="6000"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.degraderPropertiesShort">
      <map>
        <entry key="highLatency" value="1000"/>
        <entry key="lowLatency" value="500"/>
      </map>
    </property>
    <property name="LssMtCluster.consistentHashAlgorithm" value="distributionBased"/>
    <property name="salesLeadAccountAssociationsTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="lssMtCluster.prioritizedSchemes" value="${d2::d2-prioritizedSchemes.HTTPS_ONLY}"/>
    <property name="salesSubscriptionBenefitsTransportClientProperties">
      <map>
        <entry key="http.idleTimeout" value="********"/>
        <entry key="http.sslIdleTimeout" value="********"/>
        <entry key="http.requestTimeout" value="1000"/>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="salesUpsellFlowConfigApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="searchCriteriaTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="campaignTransportClientProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunLeadsApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="LeadFindingRunQueueApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
    <property name="WarmIntroRecommendationApiProperties">
      <map>
        <entry key="allowedClientOverrideKeys" value="http.protocolVersion"/>
        <entry key="http.protocolVersion" value="HTTP_2"/>
      </map>
    </property>
  </configuration-source>
</application>