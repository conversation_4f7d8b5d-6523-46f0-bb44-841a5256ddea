{"type": "record", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "namespace": "com.linkedin.sales.espresso", "schemaType": "DocumentSchema", "doc": "Stores the content of the recipient agnostic alerts", "version": 1, "fields": [{"name": "alertType", "type": "string", "indexType": "native", "doc": "The type of the alert, e.g. ACCOUNT_MENTIONED_IN_THE_NEWS", "compliance": "NONE"}, {"name": "contentUrn", "type": "string", "doc": "The urn that represents the content of the alert, could be the id of a member, a company, a linkedin post, or even complex urns", "compliance": [{"policy": "MEMBER_ID", "format": "URN"}, {"policy": "COMPANY_ID", "format": "URN"}, {"policy": "INGESTED_CONTENT_ID", "format": "URN"}, {"policy": "UGC_ID", "format": "URN"}, {"policy": "UNSTRUCTURED_PII"}]}, {"name": "createdTime", "type": "long", "indexType": "native", "doc": "The time that the alert was created, in milliseconds since epoch time", "compliance": {"policy": "EVENT_TIME"}}]}