{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "Table schema for recipient agnostic alerts, which stores related content on an entity that could be account, lead or custom list. For example, if a company is mentioned in a news post, we will catch this in offline and store its ingestedContentId in an alert record.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAlert/EntityAlert", "version": 1, "resourceKeyParts": [{"name": "entityUrn", "type": "STRING", "maxsize": 40, "compliance": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}, {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": true}]}, {"name": "alertId", "type": "LONG", "autoincrement": "LOCAL", "compliance": "NONE"}]}