{"name": "Account<PERSON>lay", "doc": "Document schema for storing information related to a specific AutoFinder play(search) under an account for a given seat.", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "playType", "type": "string", "indexType": "native", "doc": "the type of play, a play is a search ran on behalf of the seat to find leads", "compliance": "NONE"}, {"name": "personaLocalId", "type": ["null", "int"], "default": null, "doc": "the persona associated with the play", "compliance": "NONE"}, {"name": "currentLeads", "type": {"type": "array", "items": "long"}, "doc": "the current list of member ids(leads) that match this play", "compliance": {"policy": "MEMBER_ID", "format": "NUMERIC", "isPurgeKey": false}}, {"name": "pastLeads", "type": {"type": "array", "items": "long"}, "doc": "the past list of member ids(leads) that matched this play", "compliance": {"policy": "MEMBER_ID", "format": "NUMERIC", "isPurgeKey": false}}, {"name": "createdTime", "type": "long", "doc": "create time for this record in milliseconds", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "last modified time for this record in milliseconds", "compliance": "NONE"}]}