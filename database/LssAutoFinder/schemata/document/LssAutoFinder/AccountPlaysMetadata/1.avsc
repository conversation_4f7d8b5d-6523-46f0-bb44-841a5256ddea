{"name": "AccountPlaysMetadata", "doc": "Document schema for storing information related to AutoFinder plays(searches) under an account for a given seat.", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "dismissedLeads", "type": {"type": "array", "items": "long"}, "doc": "the current list of member ids(leads) that have been dismissed by the seat", "compliance": {"policy": "MEMBER_ID", "format": "NUMERIC", "isPurgeKey": false}}, {"name": "lastRunTimestamp", "type": "long", "doc": "Timestamp in milliseconds when the set of plays for AutoFinder was last run for this account for this seat", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "create time for this record in milliseconds", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "last modified time for this record in milliseconds", "compliance": "NONE"}]}