{"name": "Account<PERSON>lay", "doc": "Table for storing information related to a specific AutoFinder play(search) under an account.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoFinder/AccountPlay", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat this play is associated with", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "organizationUrn", "type": "STRING", "doc": "The company this play is associated with", "compliance": {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": true}}, {"name": "id", "type": "INT", "doc": "The id of the play associated to this record", "autoincrement": "LOCAL", "compliance": "NONE"}]}