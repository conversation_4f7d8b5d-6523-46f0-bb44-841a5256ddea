{"name": "AccountPlaysMetadataV2", "doc": "Table for storing information related to AutoFinder plays(searches) associated to a particular account.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoFinder/AccountPlaysMetadataV2", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat this record is associated with", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "organizationUrn", "type": "STRING", "doc": "The company this record is associated with", "compliance": {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": true}}, {"name": "dismissedLead", "type": "STRING", "doc": "The member (lead) that has been dismissed by the seat", "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}]}