{"name": "Campaign", "doc": "Document schema for campaign data", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract of the seat that owns the campaign", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "productId", "indexType": "native", "type": "string", "doc": "The product id of the campaign", "compliance": "NONE"}, {"name": "accountListUrns", "type": {"type": "array", "items": "string"}, "doc": "SalesList urn identifying the account list that we should prospect for.", "compliance": "NONE", "default": []}, {"name": "accountUrns", "type": {"type": "array", "items": "string"}, "doc": "Organization urns for accounts we want to prospect for", "compliance": {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": false}, "default": []}, {"name": "createdTime", "type": "long", "doc": "Epoch time when the campaign was created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "Last modified time for this record", "compliance": "NONE"}]}