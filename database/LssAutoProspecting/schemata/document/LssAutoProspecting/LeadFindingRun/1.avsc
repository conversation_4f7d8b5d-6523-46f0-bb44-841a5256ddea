{"name": "LeadFindingRun", "doc": "Document schema for storing and querying lead finding run data.", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract of the Sales Navigator user who owns this run", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "status", "type": "string", "doc": "Status of the run. This can be NOT_STARTED, IN_PROGRESS, COMPLETED etc", "indexType": "native", "compliance": "NONE"}, {"name": "type", "type": "string", "doc": "Type of run. Can be ON_DEMAND, SCHEDULED", "indexType": "native", "compliance": "NONE"}, {"name": "error", "type": ["null", {"type": "record", "name": "LeadFindingRunError", "fields": [{"name": "code", "type": "string", "doc": "Error code", "compliance": "NONE"}, {"name": "message", "type": "string", "doc": "Error message", "compliance": "NONE"}]}], "doc": "lead finding run error message", "compliance": "NONE", "default": null}, {"name": "createdTime", "type": "long", "doc": "Epoch time when the lead finding run was created", "indexType": "native", "compliance": "NONE"}, {"name": "scheduledStartTime", "type": ["null", "long"], "doc": "Epoch time when the lead finding run is scheduled to start", "compliance": "NONE", "default": null}, {"name": "startTime", "type": ["null", "long"], "doc": "Epoch time when the lead finding run was triggered to run", "compliance": "NONE"}, {"name": "projectedCompletionTime", "type": ["null", "long"], "doc": "Epoch time when the lead finding run is scheduled to complete", "compliance": "NONE", "default": null}, {"name": "completionTime", "type": ["null", "long"], "doc": "Epoch time when the lead finding run was complete", "compliance": "NONE", "default": null}, {"name": "lastModifiedTime", "type": "long", "doc": "Last modified time for this record", "compliance": "NONE"}, {"name": "trackingId", "type": ["null", {"type": "fixed", "name": "TrackingId", "size": 16}], "doc": "The tracking ID", "compliance": "NONE"}, {"name": "prospectedLeadsCount", "type": ["null", "int"], "doc": "Number of leads we found on this run", "compliance": "NONE", "default": null}, {"name": "leadLimit", "type": ["null", "int"], "doc": "the number of leads limit. This field comes from config", "compliance": "NONE", "default": null}]}