{"name": "LeadFindingRunLead", "doc": "Document schema for storing leads recommended from lead finding run", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "relevantPositionIds", "type": {"type": "array", "items": "long"}, "doc": "The list of relevant position IDs for the prospected lead.", "default": [], "compliance": "NONE"}, {"name": "rationale", "type": "string", "doc": "rationale on why this member is a good lead for the seller", "compliance": "NONE"}, {"name": "score", "type": "int", "doc": "On a scale 1-10 how good is this a match, as per LLM evaluation", "compliance": "NONE"}, {"name": "variant", "type": "string", "doc": "Describes the state of the Prospecting agent system when the lead was found through a lead finding run", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "The time when the lead was created, stored as EPOCH long", "compliance": "NONE"}, {"name": "modifiedTime", "type": "long", "doc": "The time when the lead was last modified, stored as EPOCH long", "compliance": "NONE"}, {"name": "status", "type": "string", "doc": "The status of the lead, whether they were autorejected, manually accepted, etc.", "compliance": "NONE"}, {"name": "conversationId", "doc": "Identifies the conversation between seat and agent specific to this lead", "type": ["null", "string"], "default": null, "compliance": "NONE"}, {"name": "keyStrengths", "type": {"type": "array", "items": "string"}, "doc": "List of key strengths providing more detail about why the lead was recommended to the seller", "default": [], "compliance": "NONE"}]}