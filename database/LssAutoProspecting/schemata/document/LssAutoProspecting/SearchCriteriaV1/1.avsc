{"name": "SearchCriteriaV1", "doc": "Document schema for storing querying understanding outputs, including facet selections and EBR search queries.", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "rawCriteria", "type": "string", "doc": "Criteria in natural language.", "compliance": "NONE"}, {"name": "facetSelections", "type": ["null", "string"], "doc": "Serialized facet selections suggested by query understanding.", "compliance": "NONE", "default": null}, {"name": "ebr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": ["null", "string"], "doc": "EBR search query suggested by query understanding.", "compliance": "NONE", "default": null}, {"name": "contractUrn", "type": "string", "doc": "The contract of the seat generated the search criteria. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "createdTime", "type": "long", "doc": "Create time for this record in milliseconds", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "Last modified time for this record in milliseconds", "compliance": "NONE"}]}