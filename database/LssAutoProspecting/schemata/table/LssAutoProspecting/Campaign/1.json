{"name": "Campaign", "doc": "Table for storing prospecting campaigns", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoProspecting/Campaign", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat that owns the campaign", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "campaignId", "type": "LONG", "doc": "The campaign id", "compliance": "NONE", "autoincrement": "GLOBAL"}]}