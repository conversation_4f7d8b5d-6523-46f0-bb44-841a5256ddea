{"name": "LeadFindingRun", "doc": "Table for storing LeadFindingRun details", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoProspecting/LeadFindingRun", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat triggering the lead finding run", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "campaignId", "type": "LONG", "doc": "The campaign id of the lead finding run", "compliance": "NONE"}, {"name": "runId", "type": "LONG", "doc": "The id for this run of lead finding", "compliance": "NONE", "autoincrement": "GLOBAL"}]}