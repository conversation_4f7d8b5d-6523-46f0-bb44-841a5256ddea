{"name": "LeadFindingRunLead", "doc": "Table for storing leads recommended from lead finding run", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoProspecting/LeadFindingRunLead", "version": 1, "resourceKeyParts": [{"name": "runId", "type": "LONG", "doc": "The runId from lead finding run through which the lead was generated", "compliance": "NONE"}, {"name": "memberUrn", "type": "STRING", "doc": "The memberUrn of the recommended lead", "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}]}