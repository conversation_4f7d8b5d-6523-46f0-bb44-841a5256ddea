{"name": "SearchCriteriaV1", "doc": "Table for storing hashed criteria and query understanding output.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssAutoProspecting/SearchCriteriaV1", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat entered the criteria", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "hashedCriteria", "type": "STRING", "doc": "The hash value of the criteria and prompt variant used for query understanding", "maxsize": 70, "compliance": "NONE"}]}