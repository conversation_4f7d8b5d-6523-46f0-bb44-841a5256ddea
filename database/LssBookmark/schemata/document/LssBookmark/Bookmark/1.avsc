{"type": "record", "name": "Bookmark", "namespace": "com.linkedin.sales.espresso", "schemaType": "DocumentSchema", "doc": "Document schema for the information about Bookmarks", "views": [{"name": "SeatToBookmarkMappingView", "destinationTable": "SeatToBookmarkView", "function": "MAP", "destinationKey": ["field.seatUrn", "field.type", "key.id"], "destinationValue": ["createdTime=field.createdTime", "contentUrn=field.contentUrn"]}], "version": 1, "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract that the bookmark belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "contentUrn", "type": "string", "doc": "The content that is bound to the bookmark. e.g. If bookmark an alert, then use NotificationV2Urn. Supported types: NotificationV2Urn", "compliance": [{"policy": "MEMBER_ID", "format": "CUSTOM", "pattern": "urn:li:member:([0-9]+)", "isPurgeKey": false}, {"policy": "CONTRACT_ID", "format": "CUSTOM", "pattern": "urn:li:contract:([0-9]+)", "isPurgeKey": false}]}, {"name": "createdTime", "type": "long", "indexType": "native", "doc": "the timestamp when this bookmark is created", "compliance": {"policy": "EVENT_TIME"}}, {"name": "seatUrn", "type": "string", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "type", "type": {"type": "enum", "name": "BookmarkType", "symbols": ["ALERT"], "symbolDocs": {"ALERT": "bookmark for alerts"}, "doc": "types of bookmarks that are supported"}, "doc": "type of bookmark", "compliance": "NONE"}]}