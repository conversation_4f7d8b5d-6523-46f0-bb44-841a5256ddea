{"name": "SeatToBookmarkView", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for the derived seat to bookmark mapping.", "fields": [{"name": "contentUrn", "type": "string", "indexType": "native", "doc": "The content that is bound to the bookmark. e.g. If bookmark an alert, then use NotificationV2Key. Supported contentUrn", "compliance": [{"policy": "MEMBER_ID", "format": "CUSTOM", "pattern": "urn:li:member:([0-9]+)", "isPurgeKey": false}, {"policy": "CONTRACT_ID", "format": "CUSTOM", "pattern": "urn:li:contract:([0-9]+)", "isPurgeKey": false}]}, {"name": "createdTime", "type": "long", "indexType": "native", "doc": "the timestamp when this bookmark is created", "compliance": {"policy": "EVENT_TIME"}}]}