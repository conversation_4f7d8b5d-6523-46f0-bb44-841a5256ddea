{"name": "SeatToBookmarkView", "doc": "Table schema for the seat view (derived from Bookmark table)", "schemaType": "TableSchema", "recordType": "/schemata/document/LssBookmark/SeatToBookmarkView", "derivedType": "VIEW", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "type", "type": "STRING", "maxsize": 30, "compliance": "NONE"}, {"name": "id", "type": "LONG", "compliance": "NONE"}]}