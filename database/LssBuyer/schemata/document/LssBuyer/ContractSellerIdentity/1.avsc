{"name": "ContractSellerIdentity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "The seller identity in the context of a contract that can be leveraged across sellers", "fields": [{"name": "products", "type": {"type": "array", "items": {"name": "Product", "type": "record", "doc": "A generic entity what the user sells including product, service and solution or anything", "fields": [{"name": "productId", "type": ["null", "string"], "doc": "SellerIdentityProduct ID", "compliance": "NONE", "default": null}, {"name": "productName", "type": ["null", "string"], "doc": "The name of the product", "compliance": "NONE", "default": null}, {"name": "standardizedProductUrn", "type": ["null", "string"], "doc": "The StandardizedProductUrn if there is a standardized product linked to this seller identity product", "compliance": "NONE", "default": null}, {"name": "productUrl", "type": ["null", "string"], "doc": "The user input url of the product", "compliance": "NONE", "default": null}, {"name": "productCategoryName", "type": ["null", "string"], "doc": "The category name of the product. It doesn't need to be a standardized product category.", "compliance": "NONE", "default": null}, {"name": "standardizedProductCategoryUrn", "type": ["null", "string"], "doc": "The standardizedProductCategoryUrn if there is a standardized product category selected for this seller identity product", "compliance": "NONE", "default": null}, {"name": "customerFunctionUrns", "doc": "The target customer job functions represented by function Urns.", "type": [{"type": "array", "items": "string"}, "null"], "compliance": "NONE", "default": []}, {"name": "productDescription", "type": ["null", "string"], "doc": "The description of the product.", "compliance": "NONE", "default": null}, {"name": "created<PERSON>y", "type": "string", "doc": "The seat urn that created this admin product.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}, {"name": "createdTime", "type": "long", "doc": "The time when the admin product is created.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBy", "type": "string", "doc": "The seat which made the last edit to admin product.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the admin product.", "compliance": {"policy": "EVENT_TIME"}}]}}, "doc": "Products that are created and managed by admins. Admin product can be directly leveraged across sellers in the contract to drive consistency and repeatability", "compliance": "NONE", "default": []}]}