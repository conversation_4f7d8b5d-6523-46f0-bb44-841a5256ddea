{"name": "ProductCategoryInterest", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "The product and its category that the seat holder shows interests", "fields": [{"name": "productName", "type": "string", "doc": "The full name of the product. It could be of a standardized or a non-standardized product. Different products could have identical names", "compliance": "NONE"}, {"name": "standardizedProductUrn", "type": "string", "doc": "If a product is certified by the Standardization team, it will be assigned with a unique product Id. Otherwise use a urn with -1 as product Id, like 'urn:li:standardizedProduct:-1'", "compliance": "NONE"}, {"name": "productUrl", "type": ["null", "string"], "doc": "The url of the product page provided by the user. The product owner could build this page with or without standardization certification.", "compliance": "NONE"}, {"name": "categoryName", "type": "string", "indexType": "native", "doc": "The name of product category. A category must have a name. Every standardized category has a unique name.", "compliance": "NONE"}, {"name": "standardizedProductCategoryUrn", "type": "string", "doc": "If a category is certified by the Standardization team, it will be assigned with a unique category Id. Otherwise use a urn with -1 as category Id, like 'urn:li:standardizedProductCategory:-1'", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "The timestamp on the creation of this interest record.", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "The timestamp on the last modification of this interest record.", "compliance": "NONE"}]}