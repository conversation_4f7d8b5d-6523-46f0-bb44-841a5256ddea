{"name": "SeatSellerIdentity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "The seller identity information of a seat holder", "fields": [{"name": "targetType", "type": {"type": "enum", "name": "TargetType", "symbols": ["ACCOUNT", "LEAD"], "symbolDocs": {"ACCOUNT": "The target buyer type is account", "LEAD": "The target buyer type is lead."}}, "doc": "The target buyer type of the seller.", "compliance": "NONE"}, {"name": "currentPosition", "type": ["null", {"name": "SellerIdentityPosition", "type": "record", "doc": "The working position data. Both LI position and user input position are accepted.", "fields": [{"name": "positionId", "type": ["null", "long"], "doc": "The id of a LI Position", "compliance": "NONE"}, {"name": "organizationUrn", "type": ["null", "string"], "doc": "The urn of the company that the seat holder is working for", "compliance": {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": false}}, {"name": "companyName", "type": ["null", "string"], "doc": "The name of the company that the seat holder is working for", "compliance": "NONE"}, {"name": "title", "type": ["null", "string"], "doc": "The current title of the position", "compliance": "NONE"}]}], "doc": "The current position of the seat holder", "compliance": "NONE"}, {"name": "products", "type": {"type": "array", "items": {"name": "SellerIdentityProduct", "type": "record", "doc": "Product information used in Seller Identity", "fields": [{"name": "productName", "type": ["null", "string"], "doc": "The name of the product", "compliance": "NONE"}, {"name": "productUrn", "type": ["null", "string"], "doc": "The urn for a standardized product", "compliance": "NONE"}, {"name": "productUrl", "type": ["null", "string"], "doc": "The user input url of the product", "compliance": "NONE"}, {"name": "productCategoryName", "type": ["null", "string"], "doc": "The category name of the product. It doesn't need to be a standardized product category.", "compliance": "NONE"}]}}, "doc": "The products that the seat holder sells", "compliance": "NONE"}, {"name": "services", "type": {"type": "array", "items": {"name": "SellerIdentityService", "type": "record", "doc": "Service information used in Seller Identity", "fields": [{"name": "serviceName", "type": "string", "doc": "The name of the service", "compliance": "NONE"}, {"name": "serviceUrl", "type": ["null", "string"], "doc": "The user input url of the service", "compliance": "NONE"}]}}, "doc": "The services that the seat holder sells", "compliance": "NONE"}]}