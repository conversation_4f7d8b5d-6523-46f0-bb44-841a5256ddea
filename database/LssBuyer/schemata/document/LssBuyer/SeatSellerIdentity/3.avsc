{"name": "SeatSellerIdentity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 3, "doc": "The seller identity information of a seat holder", "fields": [{"name": "targetType", "type": {"type": "enum", "name": "TargetType", "symbols": ["ACCOUNT", "LEAD"], "symbolDocs": {"ACCOUNT": "The target buyer type is account", "LEAD": "The target buyer type is lead."}}, "doc": "The target buyer type of the seller.", "compliance": "NONE"}, {"name": "currentPosition", "type": ["null", {"name": "SellerIdentityPosition", "type": "record", "doc": "The working position data. Both LI position and user input position are accepted.", "fields": [{"name": "positionId", "type": ["null", "long"], "doc": "The id of a LI Position", "compliance": "NONE"}, {"name": "organizationUrn", "type": ["null", "string"], "doc": "The urn of the company that the seat holder is working for", "compliance": {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": false}}, {"name": "companyName", "type": ["null", "string"], "doc": "The name of the company that the seat holder is working for", "compliance": "NONE"}, {"name": "title", "type": ["null", "string"], "doc": "The current title of the position", "compliance": "NONE"}]}], "doc": "The current position of the seat holder", "compliance": "NONE"}, {"name": "products", "type": {"type": "array", "items": {"name": "SellerIdentityProduct", "type": "record", "doc": "A generic Entity what the user sells including product, service and solution or anything", "fields": [{"name": "productId", "type": ["null", "string"], "doc": "SellerIdentityProduct ID. Unique within the seat holder", "compliance": "NONE", "default": null}, {"name": "productName", "type": ["null", "string"], "doc": "The name of the product", "compliance": "NONE", "default": null}, {"name": "productUrn", "type": ["null", "string"], "doc": "The urn for a standardized product", "deprecatedSymbols": {"NOTIFICATION_BOOKMARK": "replaced by standardizedProductUrn"}, "compliance": "NONE", "default": null}, {"name": "standardizedProductUrn", "type": ["null", "string"], "doc": "The StandardizedProductUrn if there is a standardized product linked to this seller identity product", "compliance": "NONE", "default": null}, {"name": "productUrl", "type": ["null", "string"], "doc": "The user input url of the product", "compliance": "NONE", "default": null}, {"name": "productCategoryName", "type": ["null", "string"], "doc": "The category name of the product. It doesn't need to be a standardized product category.", "compliance": "NONE", "default": null}, {"name": "standardizedProductCategoryUrn", "type": ["null", "string"], "doc": "The standardizedProductCategoryUrn if there is a standardized product category selected for this seller identity product", "compliance": "NONE", "default": null}, {"name": "customerFunctionUrns", "doc": "The target customer job functions represented by function Urns.", "type": [{"type": "array", "items": "string"}, "null"], "compliance": "NONE", "default": []}, {"name": "productDescription", "type": ["null", "string"], "doc": "The description of the product.", "compliance": "NONE", "default": null}, {"name": "createdTime", "type": ["null", "long"], "default": null, "doc": "The time when the product is created.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": ["null", "long"], "default": null, "doc": "A timestamp corresponding to the last modification of the product.", "compliance": {"policy": "EVENT_TIME"}}]}}, "doc": "The products that the seat holder sells", "compliance": "NONE", "default": []}, {"name": "services", "type": {"type": "array", "items": {"name": "SellerIdentityService", "type": "record", "doc": "[Deprecated] Service information used in Seller Identity", "deprecatedSymbols": {"NOTIFICATION_BOOKMARK": "replaced by generic Products field."}, "fields": [{"name": "serviceName", "type": "string", "doc": "The name of the service", "compliance": "NONE"}, {"name": "serviceUrl", "type": ["null", "string"], "doc": "The user input url of the service", "compliance": "NONE"}]}}, "doc": "The services that the seat holder sells", "compliance": "NONE"}, {"name": "defaultProductId", "type": ["null", "string"], "doc": "The ID for the default product.", "compliance": "NONE", "default": null}]}