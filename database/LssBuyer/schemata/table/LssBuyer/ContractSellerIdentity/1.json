{"name": "ContractSellerIdentity", "doc": "Table schema of Contract Seller Identity. It can include identity in the context of a contract that can be directly leveraged across sellers", "schemaType": "TableSchema", "recordType": "/schemata/document/LssBuyer/ContractSellerIdentity", "version": 1, "resourceKeyParts": [{"name": "contractUrn", "type": "STRING", "maxsize": 40, "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}]}