{"name": "ProductCategoryInterest", "doc": "Table schema for Product Category Interest. A LSS seat holder shows interests in a product belonging to a category.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssBuyer/ProductCategoryInterest", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "interestId", "type": "LONG", "autoincrement": "LOCAL", "compliance": "NONE"}]}