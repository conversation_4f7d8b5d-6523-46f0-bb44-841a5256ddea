{"name": "SeatSellerIdentity", "doc": "Table schema of Seller Identity for seats. A seat holder's seller identity includes its current role, target buyer type, and products & services.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssBuyer/SeatSellerIdentity", "version": 1, "resourceKeyParts": [{"name": "contractUrn", "type": "STRING", "maxsize": 40, "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "seatUrn", "type": "STRING", "maxsize": 40, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}]}