{"type": "record", "name": "CoachConversationHistory", "namespace": "com.linkedin.sales.espresso", "schemaType": "DocumentSchema", "doc": "Document schema for storing the details of chat history between a coach and a member of a session.", "version": 2, "evolutionSafetyMode": "IGNORE_WARNINGS", "fields": [{"name": "chatContent", "type": {"type": "array", "items": {"type": "record", "name": "ChatMessage", "doc": "Single chat message generated by the coach or written by a member.", "fields": [{"name": "message", "type": "string", "doc": "The text message of a single chat.", "compliance": "NONE"}, {"name": "author", "type": {"type": "enum", "name": "AuthorType", "symbols": ["UNKNOWN", "MEMBER", "SYSTEM"], "default": "UNKNOWN", "symbolDocs": {"UNKNOWN": "Default unknown AuthorType for forward compatibility.", "MEMBER": "The text message has been written by a member associated with this chat history.", "SYSTEM": "The text message has been generated by the LSS Coach backed by LLM (GPT)."}}, "doc": "The author of the given text message in a chat conversation.", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "The timestamp when this message has been created.", "compliance": {"policy": "EVENT_TIME"}}]}}, "doc": "The chat content of a single session conversation between the coach and a member.", "compliance": "NONE"}, {"name": "metadata", "type": ["null", {"name": "<PERSON><PERSON><PERSON>", "doc": "The general metadata about this given chat conversation history between the coach and a member.", "type": "record", "fields": [{"name": "useCase", "doc": "The use case for the chat conversation. For e.g. Lead search, Account Search etc...", "type": ["null", {"type": "enum", "name": "UseCaseType", "default": "UNKNOWN", "symbols": ["UNKNOWN", "LEAD_SEARCH", "ACCOUNT_SEARCH"], "symbolDocs": {"UNKNOWN": "Default unknown UseCaseType for forward compatibility.", "LEAD_SEARCH": "The use case for this chat history is to discover Leads.", "ACCOUNT_SEARCH": "The use case for this chat history is to discover Accounts."}}], "default": null, "compliance": "NONE"}, {"name": "summarizedIntent", "doc": "The summarized intent of the questions by the member in this chat conversation.", "type": ["null", "string"], "default": null, "compliance": "NONE"}, {"name": "summarizedContent", "doc": "The summary of the whole chat conversation between the AI and a member.", "type": ["null", "string"], "default": null, "compliance": "NONE"}]}], "default": null, "doc": "The meta data about the given chat conversion history between the coach and a member.", "compliance": "NONE"}, {"name": "updatedTime", "type": "long", "doc": "The timestamp when this conversation has been updated by either the coach and a member.", "compliance": {"policy": "EVENT_TIME"}}]}