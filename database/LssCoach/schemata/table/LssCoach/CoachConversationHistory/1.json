{"name": "CoachConversationHistory", "doc": "Table for storing coach conversation history.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssCoach/CoachConversationHistory", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxSize": 34, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "sessionId", "type": "STRING", "compliance": "NONE"}]}