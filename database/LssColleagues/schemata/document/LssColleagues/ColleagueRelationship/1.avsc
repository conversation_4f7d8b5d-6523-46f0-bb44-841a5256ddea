{"name": "ColleagueRelationship", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Represents colleague relationships for a lead on Sales Navigator", "views": [{"name": "ReverseRelationshipView", "destinationTable": "ReverseRelationshipView", "function": "MAP", "destinationKey": ["key.toMemberId", "key.contractId", "key.relationshipType", "key.fromMemberId"], "destinationValue": ["createdBySeatUrn=field.createdBySeatUrn", "updatedTime=field.updatedTime", "state=field.state"]}], "fields": [{"name": "createdBySeatUrn", "type": "string", "doc": "The seat urn that created this relationship.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}, {"name": "updatedTime", "type": "long", "indexType": "native", "doc": "The time this relationship was updated.", "compliance": {"policy": "TRANSACTION_TIME"}}, {"name": "state", "type": {"type": "enum", "name": "State", "symbols": ["ADDED", "REMOVED"], "symbolDocs": {"ADDED": "This relationship has been added in the contract.", "REMOVED": "This relationship has been explicitly removed in the contract and should override external sources which might have this relationship."}}, "indexType": "native", "doc": "The current state of the relationship.", "compliance": "NONE"}]}