{"name": "ColleagueRelationshipHistory", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Represents the history of colleague relationships for a lead on Sales Navigator", "fields": [{"name": "createdBySeatUrn", "type": "string", "doc": "The seat urn that created this relationship.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}, {"name": "updatedTime", "type": "long", "indexType": "native", "doc": "The time this relationship was updated.", "compliance": {"policy": "TRANSACTION_TIME"}}, {"name": "state", "type": {"type": "enum", "name": "State", "symbols": ["ADDED", "REMOVED"], "symbolDocs": {"ADDED": "This relationship has been added in the contract.", "REMOVED": "This relationship has been explicitly removed in the contract and should override external sources which might have this relationship."}}, "indexType": "native", "doc": "The state of the relationship at the time in history.", "compliance": "NONE"}]}