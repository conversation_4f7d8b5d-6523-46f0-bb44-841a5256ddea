{"name": "ColleagueRelationshipHistory", "doc": "Represents the history of colleague relationships for a lead on Sales Navigator. Format: [fromMemberId] [relationshipType] [toMemberId]", "schemaType": "TableSchema", "recordType": "/schemata/document/LssColleagues/ColleagueRelationshipHistory", "version": 1, "resourceKeyParts": [{"name": "fromMemberId", "type": "LONG"}, {"name": "contractId", "type": "LONG"}, {"name": "relationshipType", "type": "STRING", "maxsize": 50}, {"name": "toMemberId", "type": "LONG"}, {"name": "entryId", "type": "LONG", "autoincrement": "GLOBAL"}]}