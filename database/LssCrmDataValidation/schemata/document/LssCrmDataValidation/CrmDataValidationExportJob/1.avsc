{"schemaType": "DocumentSchema", "type": "record", "name": "CrmDataValidationExportJob", "namespace": "com.linkedin.sales.crm.avro.generated", "version": 1, "doc": "An Export Job is an audit log to track when an export job is initiated, the status of a job, and the error message associated with it.", "fields": [{"name": "executionId", "type": "long", "doc": "Execution id of the background job, which could be the Azkaban exec id."}, {"name": "exportStartTime", "type": "long", "doc": "Start detect time used in this job, which is from user request parameter."}, {"name": "exportEndTime", "type": "long", "doc": "End detect time used in this job, which is from user request parameter."}, {"name": "status", "type": {"type": "enum", "name": "CrmDataValidationExportJobStatus", "doc": "Enumerate types of data validation export job status.", "symbols": ["PROCESSING", "FAILED_DUE_TO_EXCEEDED_FILE_SIZE_ERROR", "FAILED_DUE_TO_GOBBLIN_ERROR", "COMPLETED"], "symbolDocs": {"PROCESSING": "The job is currently being processed.", "FAILED_DUE_TO_EXCEEDED_FILE_SIZE_ERROR": "The generated CSV zip has exceeded the max file size allowed. Please reduce the date range and try again.", "FAILED_DUE_TO_GOBBLIN_ERROR": "A Gobblin Bridge internal failure has caused the job to fail.", "COMPLETED": "The job has completed."}}, "doc": "Represents job status in a typical job lifecycle."}, {"name": "createdTime", "type": "long", "doc": "Date added for this record, which is used to purge the record."}, {"name": "ambryBlobId", "type": ["null", "string"], "doc": "Ambry blob id for the generated CSV file. The field will present when 'status' is COMPLETED."}, {"name": "nextStartTime", "type": ["null", "long"], "doc": "The end date of the exported data, the client shall pass the value of 'nextStartAt' as 'startAt' in the subsequent request."}, {"name": "errorMessage", "type": ["null", "string"], "doc": "Error message returned from Gobblin Bridge when the 'status' is FAIL_DUE_TO_GOBBLIN_ERROR."}]}