{"name": "FilterLayout", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Store LSS search custom filter layout. An entry is created or updated when a user's custom filter layout changes.  Doc: go/lsscustomfilterviewrfc", "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "filterOrder", "type": {"type": "map", "doc": "Keys in map denote the filterGroup and the values denote the filter order in that group. Index 0 in entityNames is the first (top) displayed filter in that group. entityColumn is used for a special case, where the key is \"OVERALL\" and each entityName is a filter group. 'filterGroup' Examples: \"COMPAN<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"OVERALL\"", "values": {"type": "record", "name": "FilterLayoutConfig", "doc": "Stores all filters (in display order) for a given filterGroup (map key), and the UI display column for the filterGroup. Also stores the overall order of the filterGroups", "fields": [{"name": "entityNames", "type": {"type": "array", "items": "string"}, "default": [], "doc": "Name of the filter or filterGroup. All elements in the array MUST be of the same category (either filters OR filterGroups).", "compliance": "NONE"}, {"name": "entityColumn", "type": "int", "optional": true, "doc": "Denotes 0 (LEFT) or 1 (RIGHT). Only used when the key is \"OVERALL\". If not present and key is \"OVERALL\", default to LEFT in sales-api. Integers used for future extensibility", "compliance": "NONE"}]}}, "compliance": {"key": "NONE", "values": "INHERITED"}}]}