{"name": "PinnedFilters", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Store LSS search pinned filters. An entry is created or updated when a user's pinned filters changes. Doc: go/lsscustomfilterviewrfc", "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "pinnedFilters", "type": {"type": "array", "items": "string"}, "doc": "The list of pinned filters defined by a user. Unordered array. Filter display order is defined by either the sales-api default or FilterLayout table", "compliance": "NONE"}]}