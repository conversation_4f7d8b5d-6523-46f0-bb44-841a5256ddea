{"name": "FilterLayout", "doc": "Used to store custom filter layouts in lss search. Doc: go/lsscustomfilterviewrfc", "schemaType": "TableSchema", "recordType": "/schemata/document/LssCustomFilterView/FilterLayout", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "Urn of the seat who is storing filter layout selections", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "searchViewType", "type": "STRING", "maxsize": 32, "compliance": "NONE", "doc": "Denotes the search view type. Ex: LEAD_COLLAPSED, ACCOUNT_COLLAPSED, LEAD_EXPANDED, or ACCOUNT_EXPANDED"}]}