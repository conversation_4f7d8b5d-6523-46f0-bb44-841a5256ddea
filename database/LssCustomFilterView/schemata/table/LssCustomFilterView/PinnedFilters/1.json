{"name": "PinnedFilters", "doc": "Used to store pinned filters in lss search. Doc: go/lsscustomfilterviewrfc", "schemaType": "TableSchema", "recordType": "/schemata/document/LssCustomFilterView/PinnedFilters", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "Urn of the seat who is storing pinned filter selections", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "searchType", "type": "STRING", "maxsize": 32, "compliance": "NONE", "doc": "Denotes the search type. Valid values are LEAD and ACCOUNT."}]}