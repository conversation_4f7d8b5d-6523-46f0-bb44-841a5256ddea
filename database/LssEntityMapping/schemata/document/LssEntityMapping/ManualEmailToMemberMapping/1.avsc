{"type": "record", "name": "ManualEmailToMemberMapping", "namespace": "com.linkedin.sales.espresso", "schemaType": "DocumentSchema", "doc": "Document schema for information of manual mapping of email to the respective Linkedin member", "version": 1, "fields": [{"name": "memberUrn", "type": "string", "doc": "The linkedin member that is matched to the email for the given contract", "compliance": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}]}, {"name": "associatedOrgUrn", "type": ["null", "string"], "default": null, "doc": "Identifies the organization context in which this match has been made", "compliance": [{"policy": "COMPANY_ID", "format": "URN"}]}, {"name": "seatUrn", "type": "string", "doc": "The urn of the latest seat that has set this match", "compliance": [{"policy": "SEAT_ID", "format": "URN"}]}, {"name": "createdAt", "type": "long", "doc": "The time at which the match was created, in milliseconds since epoch time", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedAt", "type": "long", "doc": "The time at which the match was last modified, in milliseconds since epoch time. If no modification has happened since creation, lastModified will be the same as created.", "compliance": {"policy": "EVENT_TIME"}}]}