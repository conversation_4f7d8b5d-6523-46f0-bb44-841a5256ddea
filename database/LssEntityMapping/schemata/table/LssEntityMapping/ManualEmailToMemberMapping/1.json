{"name": "ManualEmailToMemberMapping", "doc": "Table schema for information of manual mapping of email to the respective Linkedin member", "schemaType": "TableSchema", "recordType": "/schemata/document/LssEntityMapping/ManualEmailToMemberMapping", "version": 1, "resourceKeyParts": [{"name": "hashedEmail", "type": "STRING", "doc": "The hash of the email that is matched to the linkedin member", "maxsize": 70, "compliance": "NONE"}, {"name": "contractUrn", "type": "STRING", "doc": "The contract urn for which the email is matched to the linkedin member", "maxsize": 36, "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}]}