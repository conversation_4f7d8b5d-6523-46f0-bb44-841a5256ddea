{"type": "record", "name": "ProfileView", "namespace": "com.linkedin.sales.espresso", "schemaType": "DocumentSchema", "doc": "Document schema for the content of lead profile view by seat holder.", "version": 1, "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract of the Sales Navigator seat who owns the data record, such as urn:li:contract:1000", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "lastViewedTime", "type": "long", "doc": "the time that the lead was last viewed by seat holder", "compliance": {"policy": "EVENT_TIME"}}]}