{"name": "ProfileView", "doc": "Table schema for storing views of lead profiles in Sales Navigator of seat holders.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssEntityView/ProfileView", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "memberUrn", "type": "STRING", "maxsize": 34, "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}]}