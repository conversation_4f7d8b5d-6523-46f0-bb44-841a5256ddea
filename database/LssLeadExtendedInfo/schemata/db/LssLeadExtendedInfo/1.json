{"schemaType": "DBSchema", "name": "LssLeadExtendedInfo", "version": 1, "doc": "In LSS, lead is a Linkedin member that represents a potential or existing customer. LssLeadExtendInfo DB stores extended information about the sales leads of Sales Navigator users, such as lead contact information which manually entered by LSS user, and Lead profile unlock information (Sales seat could unlock certain lead's profile which out-of-network by consuming one profile unlock credit)", "partitionType": "HASH", "numBuckets": 4, "etlType": "LUMOS"}