{"name": "LeadEditableContactInfo", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for Sales Navigator lead editable contact information which is manually entered by LSS user", "fields": [{"name": "createdTime", "type": "long", "doc": "The time that the contact info is created", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": "long", "doc": "The time that the contact info is last modified", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat holder who last modified the contact info, such as urn:li:1000", "compliance": {"policy": "SEAT_ID", "format": "URN"}}, {"name": "creatorSeat<PERSON>rn", "type": "string", "doc": "The seat holder who create the contact info, such as urn:li:1000", "compliance": {"policy": "SEAT_ID", "format": "URN"}}, {"name": "addresses", "doc": "Array of various types of addresses of a lead", "type": {"type": "array", "items": {"type": "record", "name": "Address", "doc": "Address info of a lead", "fields": [{"name": "street", "type": ["string", "null"], "doc": "Street this address belongs to", "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "city", "type": ["string", "null"], "doc": "City this address belongs to", "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "country", "type": ["string", "null"], "doc": "Country this address belongs to", "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "state", "type": ["string", "null"], "doc": "State name for the address", "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "location", "type": ["string", "null"], "doc": "Location name for the address", "compliance": {"string": "NONE"}}, {"name": "postalCode", "doc": "Postal code of this address", "type": ["string", "null"], "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "fullAddress", "type": ["string", "null"], "doc": "Full address either from concating all fields, or from plain text address form", "compliance": {"string": {"policy": "ADDRESS"}}}, {"name": "addressType", "type": ["null", {"type": "enum", "name": "AddressType", "symbols": ["HOME", "SCHOOL", "WORK", "MAILING"], "symbolDocs": {"HOME": "An address describing where someone lives", "SCHOOL": "An address describing where someone studies at", "WORK": "An address describing where someone works", "MAILING": "Mailing Address of the a member"}}], "doc": "Type of Address", "compliance": "NONE"}]}}, "compliance": "INHERITED"}, {"name": "social<PERSON><PERSON><PERSON>", "doc": "Array of various categories of social handle info of a lead", "type": {"type": "array", "items": {"type": "record", "name": "SocialHandle", "doc": "SocialHandle info of a lead", "fields": [{"name": "username", "type": "string", "doc": "The social handle username", "compliance": {"policy": "SOCIAL_NETWORK_ID"}}, {"name": "provider", "doc": "Social handle provider that provides an social handle application", "type": {"type": "enum", "name": "SocialHandleProvider", "doc": "Enum describing the different social handle provider that provides an social handle application", "symbols": ["AIM", "HANGOUTS", "ICQ", "QQ", "SKYPE", "TWITTER", "WECHAT", "YAHOO"], "symbolDocs": {"AIM": "AIM messaging system.", "HANGOUTS": "Google hangouts messaging system.", "ICQ": "ICQ messaging system.", "QQ": "QQ messaging system.", "SKYPE": "Skype messaging system.", "TWITTER": "Twitter social system.", "WECHAT": "WeChat messaging system.", "YAHOO": "Yahoo messaging system."}}, "compliance": "NONE"}]}}, "compliance": "INHERITED"}, {"name": "websites", "doc": "Array of various categories of websites of a lead", "type": {"type": "array", "items": {"type": "record", "name": "Website", "doc": "Website info for a lead", "fields": [{"name": "url", "type": "string", "doc": "The website url", "compliance": {"policy": "UNSTRUCTURED_PII"}}, {"name": "category", "type": {"type": "enum", "name": "WebsiteCategory", "doc": "Represent the category of the website member has added on their profile", "symbols": ["PERSONAL", "COMPANY", "BLOG", "RSSFEED", "PORTFOLIO", "OTHER"], "symbolDocs": {"PERSONAL": "Website is a personal website", "COMPANY": "Website is a company Website", "BLOG": "Website is a blog", "RSSFEED": "Website is a RSS feed", "PORTFOLIO": "Website is a portfolio website", "OTHER": "Website type is none of the other types"}}, "compliance": "NONE"}]}}, "compliance": "INHERITED"}, {"name": "typedPhoneNumbers", "doc": "Array of various types of phone number of a lead", "type": {"type": "array", "items": {"name": "TypedPhoneNumber", "doc": "Phone number with type of a lead", "type": "record", "fields": [{"name": "number", "type": "string", "doc": "Phone Number", "compliance": {"policy": "PHONE"}}, {"name": "type", "doc": "A label describing what kind of phone number this is.", "type": {"name": "PhoneNumberType", "type": "enum", "symbols": ["HOME", "MOBILE", "WORK", "FAX"], "symbolDocs": {"HOME": "Home phone number", "MOBILE": "Mobile phone number", "WORK": "Work phone number", "FAX": "Fax phone number"}}, "compliance": "NONE"}]}}, "compliance": "INHERITED"}, {"name": "emails", "doc": "Array of emails of a sales lead", "type": {"type": "array", "items": {"name": "Email", "doc": "Email info for a lead", "type": "record", "fields": [{"name": "email", "doc": "<PERSON><PERSON> Address for a lead", "type": "string", "compliance": {"policy": "EMAIL"}}]}}, "compliance": "INHERITED"}]}