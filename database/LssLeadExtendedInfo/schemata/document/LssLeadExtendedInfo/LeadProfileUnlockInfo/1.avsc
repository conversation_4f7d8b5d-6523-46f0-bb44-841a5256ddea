{"name": "LeadProfileUnlockInfo", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for Sales Navigator Lead profile unlock info. Sales Navigator users could view an out-of-network profile after unlocking the profile by consuming one profile unlock credit.", "fields": [{"name": "unlockedBySeatUrn", "type": "string", "doc": "the seat of the Sales Navigator user who unlock the profile, such as urn:li:seat:1000", "compliance": {"policy": "SEAT_ID", "format": "URN"}}, {"name": "unlockedTime", "type": "long", "doc": "the time that the sales lead profile be unlocked", "compliance": {"policy": "EVENT_TIME"}}]}