{"name": "LeadProfileUnlockInfo", "doc": "Store Sales Navigator Lead profile unlock info, the memberUrn represent the sales lead and the contractUrn represent the contract of the sales user who create this lead profile unlock info", "schemaType": "TableSchema", "recordType": "/schemata/document/LssLeadExtendedInfo/LeadProfileUnlockInfo", "version": 1, "resourceKeyParts": [{"name": "memberUrn", "type": "STRING", "maxSize": 34, "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}, {"name": "contractUrn", "type": "STRING", "maxSize": 36, "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}]}