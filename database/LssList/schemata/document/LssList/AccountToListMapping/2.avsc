{"name": "AccountToListMapping", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "doc": "Document schema for the mapping between an account map and the custom list. The account map is a hierarchical view of the important contacts within an account and is represented by a custom list with each entity belonging to a particular tier. See the tier field on ListEntity document schema for more details on tier.", "fields": [{"name": "contractUrn", "type": ["null", "string"], "default": null, "doc": "The contract that the account map belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}]}