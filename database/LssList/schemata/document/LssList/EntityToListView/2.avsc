{"name": "EntityToListView", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "doc": "Document schema for the derived entity to list mapping.", "fields": [{"name": "createdTime", "type": "long", "indexType": "native", "doc": "the time when the item is added into the list", "compliance": "NONE"}, {"name": "saved", "type": ["null", "boolean"], "default": null, "doc": "the flag to indicate if the entity is a saved lead or account of the corresponding list owner. In Sales Navigator, user could save a person or company as lead or account for sales business purpose", "compliance": "NONE"}]}