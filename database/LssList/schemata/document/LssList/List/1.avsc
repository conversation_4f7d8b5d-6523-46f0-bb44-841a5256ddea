{"name": "List", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for the information of the list", "fields": [{"name": "creatorSeatId", "type": "long", "doc": "the seatId of the creator for the list"}, {"name": "listType", "type": {"type": "enum", "name": "ListType", "doc": "different type for the list, representing the entity inside the list", "symbols": ["LEAD", "ACCOUNT"], "symbolDocs": {"LEAD": "the entities inside the list are leads", "ACCOUNT": "the entities inside the list are accounts"}, "version": 1}, "indexType": "native", "doc": "the role of the list, indicating what operation the user can do to this list"}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified"}, {"name": "contractId", "type": "long", "doc": "the contract that the list belongs to."}, {"name": "name", "type": "string", "doc": "name of the list"}]}