{"name": "List", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 12, "evolutionSafetyMode": "IGNORE_WARNINGS", "doc": "Document schema for the information of the list", "views": [{"name": "SeatToListMappingView", "destinationTable": "SeatToListView", "function": "MAP", "destinationKey": ["field.creatorSeatId", "field.listType", "field.listSource", "key.listId"], "destinationValue": ["contractId=field.contractId", "name=field.name", "description=field.description", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "lastModifiedBySeatUrn=field.lastModifiedBySeatUrn", "lastViewedTime=field.lastViewedTime"]}], "fields": [{"name": "creatorSeatId", "type": "long", "doc": "the seatId of the creator for the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "listType", "type": {"type": "enum", "name": "ListType", "symbols": ["LEAD", "ACCOUNT", "ACCOUNT_MAP", "NOTIFICATION_BOOKMARK", "PINNED_NOTIFICATION"], "symbolDocs": {"LEAD": "the entities inside the list are leads", "ACCOUNT": "the entities inside the list are accounts", "ACCOUNT_MAP": "the entities inside the list are leads within an account map", "NOTIFICATION_BOOKMARK": "deprecated, do not use. This value passed 20 char limit, thus would not have a MV copy", "PINNED_NOTIFICATION": "the entities inside a singular per seat list are notification bookmark/pin that users saved"}, "deprecatedSymbols": {"NOTIFICATION_BOOKMARK": "replaced by PINNED_NOTIFICATION"}, "doc": "represents the type of the list. Must keep all values within 20 char because the MV table SeatToListView limits it to 20 char"}, "doc": "the role of the list, indicating what operation the user can do to this list", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified", "compliance": "NONE"}, {"name": "contractId", "type": "long", "doc": "the contract that the list belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "name", "type": "string", "doc": "name of the list", "compliance": {"policy": "FREEFORMED_UGC"}}, {"name": "description", "type": ["null", "string"], "default": null, "doc": "the description of the list", "compliance": {"string": {"policy": "FREEFORMED_UGC"}}}, {"name": "lastModifiedBySeatUrn", "type": ["null", "string"], "default": null, "doc": "the seat urn that makes the last modification on the list", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}}, {"name": "lastViewedTime", "type": ["null", "long"], "default": null, "doc": "the last time the list is viewed by the seat", "compliance": "NONE"}, {"name": "listSource", "type": ["null", {"type": "enum", "name": "ListSource", "symbols": ["MANUAL", "SYSTEM", "CRM_SYNC", "LINKEDIN_SALES_INSIGHTS", "TAGS_MIGRATION", "CSV_IMPORT", "CRM_BLUEBIRD", "BUYER_INTEREST", "CRM_AT_RISK_OPPORTUNITY", "RECOMMENDATION"], "symbolDocs": {"MANUAL": "the custom lists defined by user", "SYSTEM": "the auto created default system lists of each user for out-of-the-box use cases", "CRM_SYNC": "the lists auto generated by CRM Sync", "LINKEDIN_SALES_INSIGHTS": "the lists auto generated by LinkedIn Sales Insights(LSI)", "TAGS_MIGRATION": "the lists that are created as a result of tags migration", "CSV_IMPORT": "the lists that are created through the List CSV Import workflow", "CRM_BLUEBIRD": "an auto-generated list consisting of leads associated with closed won opportunities in CRM and are now working at a different company from the opportunity's account", "BUYER_INTEREST": "An auto-generated account list consisting of all high priority accounts based on Buyer Interest score.", "CRM_AT_RISK_OPPORTUNITY": "an auto-generated list consisting of Sales Nav leads/accounts with open opportunities in CRM that are associated with the CRM accounts from which the CRM contacts left", "RECOMMENDATION": "an auto-generated list consisting of lead/account recommendations based on user sales preferences, search history, and past lead/account interactions"}, "doc": "different data source of the list, representing the source that generates list"}], "default": null, "doc": "the data source of the list, indicating the list is generated by which data source", "compliance": "NONE"}]}