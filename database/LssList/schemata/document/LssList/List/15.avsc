{"name": "List", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 14, "evolutionSafetyMode": "IGNORE_WARNINGS", "doc": "Document schema for the information of the list.", "views": [{"name": "SeatToListMappingView", "destinationTable": "SeatToListView", "function": "MAP", "destinationKey": ["field.creatorSeatId", "field.listType", "field.listSource", "key.listId"], "destinationValue": ["contractId=field.contractId", "name=field.name", "description=field.description", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "lastModifiedBySeatUrn=field.lastModifiedBySeatUrn", "lastViewedTime=field.lastViewedTime"]}], "fields": [{"name": "creatorSeatId", "type": "long", "doc": "the seatId of the creator for the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "listType", "type": {"type": "enum", "name": "ListType", "symbols": ["LEAD", "ACCOUNT", "ACCOUNT_MAP", "NOTIFICATION_BOOKMARK", "PINNED_NOTIFICATION", "BI_DISCOVER_ACCOUNTS"], "symbolDocs": {"LEAD": "the entities inside the list are leads", "ACCOUNT": "the entities inside the list are accounts", "ACCOUNT_MAP": "the entities inside the list are leads within an account map", "NOTIFICATION_BOOKMARK": "deprecated, do not use. This value passed 20 char limit, thus would not have a MV copy", "PINNED_NOTIFICATION": "the entities inside a singular per seat list are notification bookmark/pin that users saved", "BI_DISCOVER_ACCOUNTS": "the entities inside the list are accounts that display in the new buyer interest dashboard's discover new accounts tab"}, "deprecatedSymbols": {"NOTIFICATION_BOOKMARK": "replaced by PINNED_NOTIFICATION"}, "doc": "represents the type of the list. Must keep all values within 20 char because the MV table SeatToListView limits it to 20 char."}, "doc": "The list type. Only ACCOUNT/LEAD types will be treated as custom lists and are shown on the list hub. Counts that indicate the number of custom lists that an entity belongs to will not consider list types that are not ACCOUNT/LEAD. Save to list models will only show lists that are ACCOUNT/LEAD types. See go/lss/listcapability for more information.", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified", "compliance": "NONE"}, {"name": "contractId", "type": "long", "doc": "the contract that the list belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "name", "type": "string", "doc": "name of the list", "compliance": {"policy": "FREEFORMED_UGC"}}, {"name": "description", "type": ["null", "string"], "default": null, "doc": "the description of the list", "compliance": {"string": {"policy": "FREEFORMED_UGC"}}}, {"name": "lastModifiedBySeatUrn", "type": ["null", "string"], "default": null, "doc": "the seat urn that makes the last modification on the list", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}}, {"name": "lastViewedTime", "type": ["null", "long"], "default": null, "doc": "the last time the list is viewed by the seat", "compliance": "NONE"}, {"name": "listSource", "type": ["null", {"type": "enum", "name": "ListSource", "symbols": ["MANUAL", "SYSTEM", "CRM_SYNC", "LINKEDIN_SALES_INSIGHTS", "TAGS_MIGRATION", "CSV_IMPORT", "CRM_BLUEBIRD", "BUYER_INTEREST", "CRM_AT_RISK_OPPORTUNITY", "RECOMMENDATION", "FLAGSHIP", "CRM_PERSON_ACCOUNT"], "symbolDocs": {"MANUAL": "the custom lists defined by user", "SYSTEM": "the auto created default system lists of each user for out-of-the-box use cases", "CRM_SYNC": "the account and lead lists auto generated by CRM Sync. Except Salesforce person account related contacts", "LINKEDIN_SALES_INSIGHTS": "the lists auto generated by LinkedIn Sales Insights(LSI)", "TAGS_MIGRATION": "the lists that are created as a result of tags migration", "CSV_IMPORT": "the lists that are created through the List CSV Import workflow", "CRM_BLUEBIRD": "an auto-generated list consisting of leads associated with closed won opportunities in CRM and are now working at a different company from the opportunity's account", "BUYER_INTEREST": "An auto-generated account list consisting of all high priority accounts based on Buyer Interest score.", "CRM_AT_RISK_OPPORTUNITY": "an auto-generated list consisting of Sales Nav leads/accounts with open opportunities in CRM that are associated with the CRM accounts from which the CRM contacts left", "RECOMMENDATION": "an auto-generated list consisting of lead/account recommendations based on user sales preferences, search history, and past lead/account interactions", "FLAGSHIP": "The lists that are created and used by Flagship pages, such as LinkedIn profile page, to save entities, such as leads. There should be only one list per seat. This list should not be deleted. More details could be found on go/saveLeadOnFlagshipRFC.", "CRM_PERSON_ACCOUNT": "an auto-generated lead list consisting of contacts associated with Salesforce person accounts. In Salesforce, when a person account is created (or an existing business account is changed to a person account), a corresponding contact record is also created."}, "doc": "different data source of the list, representing the source that generates list"}], "default": null, "doc": "The list source is used to distinguish between lists that show up on the list hub. These could be system generated such as CRM_SYNC or created via special ways such as CSV_IMPORT. All lists created normally by the user will have list source as MANUAL. This field should never be null. See go/lss/listcapability for more information.", "compliance": "NONE"}]}