{"name": "List", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "upconvertVersion": 2, "doc": "Document schema for the information of the list", "fields": [{"name": "creatorSeatId", "type": "long", "doc": "the seatId of the creator for the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "listType", "type": {"type": "enum", "name": "ListType", "symbols": ["LEAD", "ACCOUNT"], "symbolDocs": {"LEAD": "the entities inside the list are leads", "ACCOUNT": "the entities inside the list are accounts"}, "doc": "different type for the list, representing the entity inside the list", "version": 1}, "indexType": "native", "doc": "the role of the list, indicating what operation the user can do to this list", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified", "compliance": "NONE"}, {"name": "contractId", "type": "long", "doc": "the contract that the list belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "name", "type": "string", "doc": "name of the list", "compliance": "NONE"}, {"name": "description", "type": ["null", "string"], "default": null, "doc": "the description of the list", "compliance": {"string": "NONE"}}, {"name": "lastModifiedBySeatUrn", "type": ["null", "string"], "default": null, "doc": "the seat urn that makes the last modification on the list", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}}]}