{"name": "List", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 4, "evolutionSafetyMode": "IGNORE_WARNINGS", "doc": "Document schema for the information of the list", "views": [{"name": "SeatToListMappingView", "destinationTable": "SeatToListView", "function": "MAP", "destinationKey": ["field.creatorSeatId", "field.listType", "field.listSource", "key.listId"], "destinationValue": ["contractId=field.contractId", "name=field.name", "description=field.description", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "lastModifiedBySeatUrn=field.lastModifiedBySeatUrn", "lastViewedTime=field.lastViewedTime"]}], "fields": [{"name": "creatorSeatId", "type": "long", "doc": "the seatId of the creator for the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "listType", "type": {"type": "enum", "name": "ListType", "symbols": ["LEAD", "ACCOUNT"], "symbolDocs": {"LEAD": "the entities inside the list are leads", "ACCOUNT": "the entities inside the list are accounts"}, "doc": "different type for the list, representing the entity inside the list", "version": 1}, "doc": "the role of the list, indicating what operation the user can do to this list", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified", "compliance": "NONE"}, {"name": "contractId", "type": "long", "doc": "the contract that the list belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "name", "type": "string", "doc": "name of the list", "compliance": "NONE"}, {"name": "description", "type": ["null", "string"], "default": null, "doc": "the description of the list", "compliance": {"string": "NONE"}}, {"name": "lastModifiedBySeatUrn", "type": ["null", "string"], "default": null, "doc": "the seat urn that makes the last modification on the list", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}}, {"name": "lastViewedTime", "type": ["null", "long"], "default": null, "doc": "the last time the list is viewed by the seat", "compliance": "NONE"}, {"name": "listSource", "type": ["null", {"type": "enum", "name": "ListSource", "symbols": ["MANUAL", "SYSTEM", "CRM_SYNC", "LINKEDIN_SALES_INSIGHTS", "TAGS_MIGRATION"], "symbolDocs": {"MANUAL": "the custom lists defined by user", "SYSTEM": "the auto created default system lists of each user for out-of-the-box use cases", "CRM_SYNC": "the lists auto generated by CRM Sync", "LINKEDIN_SALES_INSIGHTS": "the lists auto generated by LinkedIn Sales Insights(LSI)", "TAGS_MIGRATION": "the lists that are created as a result of tags migration"}, "doc": "different data source of the list, representing the source that generates list"}], "default": null, "doc": "the data source of the list, indicating the list is generated by which data source", "compliance": "NONE"}]}