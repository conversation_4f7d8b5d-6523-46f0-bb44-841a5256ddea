{"name": "ListComment", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for the content and other information for the list comment", "fields": [{"name": "commentary", "doc": "The message content of this comment.", "type": {"name": "AttributedText", "doc": "A text with attributes within it.", "type": "record", "fields": [{"name": "text", "doc": "The text content that may be attributed", "type": "string"}, {"name": "attributes", "doc": "User generated attributes in the text.", "type": {"type": "array", "items": {"name": "Attribute", "doc": "A property of a condition, for example, RESOURCE_TYPE, MEMBER_ID, etc. Attributes are used to determine whether a condition applies to a request", "type": "record", "fields": [{"name": "start", "doc": "Starting position of the range within the referenced ordered collection.", "type": "int"}, {"name": "end", "doc": "Ending position of the range within the referenced ordered collection.", "type": "int"}, {"name": "value", "doc": "The value of the attribute that applies to given range.", "type": "string"}]}}}]}}, {"name": "createdTime", "type": "long", "doc": "the time that the list is created"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the list is last modified", "indexType": "native"}, {"name": "contractUrn", "type": "string", "doc": "the contract that the list belongs to."}, {"name": "creatorSeat<PERSON>rn", "type": "string", "doc": "the seat urn which created this comment", "indexType": "native"}]}