{"name": "ListComment", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "upconvertVersion": 2, "doc": "Document schema for the content and other information for the list comment", "fields": [{"name": "commentary", "type": {"type": "record", "name": "AttributedText", "fields": [{"name": "text", "type": "string", "doc": "The text content that may be attributed", "compliance": "NONE"}, {"name": "attributes", "type": {"type": "array", "items": {"type": "record", "name": "Attribute", "fields": [{"name": "start", "type": "int", "doc": "Starting position of the range within the referenced ordered collection.", "compliance": "NONE"}, {"name": "end", "type": "int", "doc": "Ending position of the range within the referenced ordered collection.", "compliance": "NONE"}, {"name": "value", "type": "string", "doc": "The value of the attribute that applies to given range.", "compliance": "NONE"}], "doc": "A property of a condition, for example, RESOURCE_TYPE, MEMBER_ID, etc. Attributes are used to determine whether a condition applies to a request"}}, "doc": "User generated attributes in the text.", "compliance": "INHERITED"}], "doc": "A text with attributes within it."}, "doc": "The message content of this comment.", "compliance": "INHERITED"}, {"name": "createdTime", "type": "long", "indexType": "native", "doc": "the time that the list is created", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": "long", "indexType": "native", "doc": "the time that the list is last modified", "compliance": "NONE"}, {"name": "contractUrn", "type": "string", "doc": "the contract that the list belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "creatorSeat<PERSON>rn", "type": "string", "indexType": "native", "doc": "the seat urn which created this comment", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}]}