{"name": "ListCsvImport", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "doc": "Document schema for a List CSV Import", "views": [{"name": "CreatorToListCsvImportView", "destinationTable": "CreatorToListCsvImportView", "function": "MAP", "destinationKey": ["field.creator<PERSON><PERSON><PERSON><PERSON>", "key.listCsvImportId"], "destinationValue": ["state=field.state", "contractUrn=field.contractUrn", "csvImportTaskUrn=field.csvImportTaskUrn", "listId=field.listId", "listName=field.listName", "listDescription=field.listDescription", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "deletedTime=field.deletedTime"]}, {"name": "ImportTaskToListCsvImportView", "destinationTable": "ImportTaskToListCsvImportView", "function": "MAP", "destinationKey": ["field.csvImportTaskUrn"], "destinationValue": ["listCsvImportId=key.listCsvImportId", "state=field.state", "creatorSeatUrn=field.creatorSeatUrn", "contractUrn=field.contractUrn", "listId=field.listId", "listName=field.listName", "listDescription=field.listDescription", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "deletedTime=field.deletedTime"]}, {"name": "ListToListCsvImportView", "destinationTable": "ListToListCsvImportView", "function": "MAP", "destinationKey": ["field.listId"], "destinationValue": ["listCsvImportId=key.listCsvImportId", "state=field.state", "creatorSeatUrn=field.creatorSeatUrn", "contractUrn=field.contractUrn", "csvImportTaskUrn=field.csvImportTaskUrn", "listName=field.listName", "listDescription=field.listDescription", "createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "deletedTime=field.deletedTime"]}], "fields": [{"name": "state", "type": {"type": "enum", "name": "ListCsvImportState", "symbols": ["IN_PROGRESS", "SUCCEEDED", "FAILED"], "symbolDocs": {"IN_PROGRESS": "The import workflow is in progress", "SUCCEEDED": "The import workflow has succeeded", "FAILED": "The import workflow has failed"}, "doc": "Possible states the SalesListCsvImport workflow may be in", "version": 1}, "doc": "The state for this instance of the CSV Import workflow", "compliance": "NONE"}, {"name": "creatorSeat<PERSON>rn", "type": "string", "doc": "the seat urn of user that triggered the CSV Import and will be the creator of the List", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "contractUrn", "type": "string", "doc": "The URN of the contract in which the CSV Import was triggered and the List will be created", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "csvImportTaskUrn", "type": "string", "doc": "The URN that identifies the CSV Import Task that is processing the CSV provided to create the List. This can be used to fetch the status and result of the CSV Import process.", "compliance": "NONE"}, {"name": "listId", "type": "long", "doc": "the ID of the list that will be created by the CSV import workflow", "compliance": "NONE"}, {"name": "listName", "type": "string", "doc": "The name of the list to be created if the import succeeds", "compliance": {"policy": "OTHER_PII"}}, {"name": "listDescription", "type": ["null", "string"], "default": null, "doc": "The description of the list to be created if the import succeeds", "compliance": {"string": {"policy": "OTHER_PII"}}}, {"name": "createdTime", "type": "long", "doc": "the time that the List CSV Import was created", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the List CSV Import was last modified", "compliance": "NONE"}, {"name": "deletedTime", "type": ["null", "long"], "default": null, "doc": "the time that the List CSV Import was deleted", "compliance": "NONE"}, {"name": "csvImportListSource", "type": ["null", {"type": "enum", "name": "CsvImportListSource", "symbols": ["CSV_IMPORT", "MANUAL", "BOOK_OF_BUSINESS"], "symbolDocs": {"CSV_IMPORT": "The workflow is for importing a list of regular accounts. The import process is initiated via the default CSV Import flow.", "MANUAL": "The workflow is for importing a list of regular accounts into a list that was previously created manually.", "BOOK_OF_BUSINESS": "The workflow is to import accounts that will be designated for a book of business."}, "doc": "Possible CSV import list source of the SalesListCsvImport workflow"}], "default": null, "doc": "The CSV import list source for this instance of the CSV Import workflow. If not specified, it implies that the workflow is for importing a list of regular accounts where the list source will be set to CSV_IMPORT", "compliance": "NONE"}]}