{"name": "ListEntity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for an entity in a certain list", "views": [{"name": "EntityToListMappingView", "destinationTable": "EntityToListView", "function": "MAP", "destinationKey": ["key.entityUrn", "field.contractId", "field.ownerSeatId", "key.listId"], "destinationValue": ["createdTime=field.createdTime"]}], "aggregates": [{"name": "totalCount", "destination": "ListEntityCount.totalCount", "function": "count()", "groupBy": ["table.listId"]}], "fields": [{"name": "contractId", "type": "long", "doc": "contractId for the seat who owns the list"}, {"name": "ownerSeatId", "type": "long", "doc": "seatId which indicates who is the person that adds this item into the list"}, {"name": "createdTime", "type": "long", "doc": "the time when the item is added into the list"}, {"name": "sortOrder", "type": "long", "doc": "the customized sort order that can be modified by the user. The default value will be createdTime", "indexType": "native"}]}