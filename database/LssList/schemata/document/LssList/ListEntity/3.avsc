{"name": "ListEntity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 3, "doc": "Document schema for an entity in a certain list", "views": [{"name": "EntityToListMappingView", "destinationTable": "EntityToListView", "function": "MAP", "destinationKey": ["key.entityUrn", "field.contractId", "field.ownerSeatId", "key.listId"], "destinationValue": ["createdTime=field.createdTime", "saved=field.saved"]}], "aggregates": [{"name": "totalCount", "destination": "ListEntityCount.totalCount", "function": "count()", "groupBy": ["table.listId"]}], "fields": [{"name": "contractId", "type": "long", "doc": "contractId for the seat who owns the list", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "ownerSeatId", "type": "long", "doc": "seatId which indicates who is the person that adds this item into the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "createdTime", "type": "long", "doc": "the time when the item is added into the list", "compliance": "NONE"}, {"name": "sortOrder", "type": "long", "indexType": "native", "doc": "the customized sort order that can be modified by the user. The default value will be createdTime", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": ["null", "long"], "default": null, "doc": "the time that the list entity is last modified", "compliance": {"long": "NONE"}}, {"name": "saved", "type": ["null", "boolean"], "default": null, "doc": "the flag to indicate if the entity is a saved lead or account of the corresponding list owner. In Sales Navigator, user could save a person or company as lead or account for sales business purpose", "compliance": "NONE"}]}