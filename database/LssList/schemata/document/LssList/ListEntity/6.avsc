{"name": "ListEntity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 6, "doc": "Document schema for an entity in a certain list", "views": [{"name": "EntityToListMappingView", "destinationTable": "EntityToListView", "function": "MAP", "destinationKey": ["key.entityUrn", "field.contractId", "field.ownerSeatId", "key.listId"], "destinationValue": ["createdTime=field.createdTime", "saved=field.saved"]}], "aggregates": [{"name": "totalCount", "destination": "ListEntityCount.totalCount", "function": "count()", "groupBy": ["table.listId"]}], "fields": [{"name": "contractId", "type": "long", "doc": "contractId for the seat who owns the list", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "ownerSeatId", "type": "long", "doc": "seatId which indicates who is the person that adds this item into the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "createdTime", "type": "long", "doc": "the time when the item is added into the list", "compliance": "NONE"}, {"name": "sortOrder", "type": "long", "indexType": "native", "doc": "the customized sort order that can be modified by the user. The default value will be createdTime", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": ["null", "long"], "default": null, "doc": "the time that the list entity is last modified", "compliance": {"long": "NONE"}}, {"name": "saved", "type": ["null", "boolean"], "default": null, "doc": "the flag to indicate if the entity is a saved lead or account of the corresponding list owner. In Sales Navigator, user could save a person or company as lead or account for sales business purpose", "compliance": "NONE"}, {"name": "priority", "type": ["null", {"type": "enum", "name": "PriorityType", "symbols": ["HIGH", "CLEARED"], "symbolDocs": {"HIGH": "the entity is tagged as a HIGH priority", "CLEARED": "the priority assigned to this entity earlier has been cleared"}, "doc": "Different priority types", "version": 1}], "default": null, "doc": "The priority of the list entity", "compliance": "NONE"}, {"name": "priorityLastModifiedSeatUrn", "type": ["null", "string"], "default": null, "doc": "seatUrn which indicates who the person that modified the priority of the list entity is", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}}, {"name": "priorityLastModifiedTime", "type": ["null", "long"], "default": null, "doc": "the time at which the priority of the list entity was last modified", "compliance": {"long": {"policy": "EVENT_TIME"}}}, {"name": "tier", "type": ["null", "int"], "default": null, "doc": "the tier of the entity within an account map. This field is only applicable for entities in the list that is associated with an account map (defined by AccountToListMapping). Currently, tier can be either 1, 2, or 3 and this maps to TIER_1, TIER_2, and TIER_3 respectively in the Rest.li schema for AccountMapTier: https://jarvis.corp.linkedin.com/codesearch/result/?name=AccountMapTier.pdl. More tiers may be added as product requirements change.", "compliance": "NONE"}, {"name": "searchIndexUpdatePriority", "type": ["null", {"type": "enum", "name": "SearchIndexUpdatePriorityType", "symbols": ["LOW", "HIGH", "SKIP"], "symbolDocs": {"LOW": "The change capture message for the entity will be added to low_priority queue used by liveupdators. This results in the change being reflected with some delay in search results.", "HIGH": "The change capture message for the entity will be added to high_priority queue used by liveupdators. This results in the change being reflected immediately in search results.", "SKIP": "The change capture message for the entity will be skipped by liveupdators"}, "doc": "Different priority types for updating entries in search index using liveupdators"}], "default": null, "doc": "The priority type for list entity when updating search index. When the field is null, it will be treated as HIGH in downstream pipeline. This allows backward compatibility.", "compliance": "NONE"}]}