{"name": "ListEntity", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 8, "doc": "Document schema for an entity in a certain list", "views": [{"name": "EntityToListMappingView", "destinationTable": "EntityToListView", "function": "MAP", "destinationKey": ["key.entityUrn", "field.contractId", "field.ownerSeatId", "key.listId"], "destinationValue": ["createdTime=field.createdTime", "saved=field.saved"]}], "aggregates": [{"name": "totalCount", "destination": "ListEntityCount.totalCount", "function": "count()", "groupBy": ["table.listId"]}], "fields": [{"name": "contractId", "type": "long", "doc": "contractId for the seat who owns the list", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "ownerSeatId", "type": "long", "doc": "seatId which indicates who is the person that adds this item into the list", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "createdTime", "type": "long", "doc": "the time when the item is added into the list", "compliance": "NONE"}, {"name": "sortOrder", "type": "long", "indexType": "native", "doc": "the customized sort order that can be modified by the user. The default value will be createdTime", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": ["null", "long"], "default": null, "doc": "the time that the list entity is last modified", "compliance": {"long": "NONE"}}, {"name": "saved", "type": ["null", "boolean"], "default": null, "doc": "the flag to indicate if the entity is a saved lead or account of the corresponding list owner. In Sales Navigator, user could save a person or company as lead or account for sales business purpose", "compliance": "NONE"}, {"name": "priority", "type": ["null", {"type": "enum", "name": "PriorityType", "symbols": ["HIGH", "CLEARED"], "symbolDocs": {"HIGH": "the entity is tagged as a HIGH priority", "CLEARED": "the priority assigned to this entity earlier has been cleared"}, "doc": "Different priority types", "version": 1}], "default": null, "doc": "The priority of the list entity", "compliance": "NONE"}, {"name": "priorityLastModifiedSeatUrn", "type": ["null", "string"], "default": null, "doc": "seatUrn which indicates who the person that modified the priority of the list entity is", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}}, {"name": "priorityLastModifiedTime", "type": ["null", "long"], "default": null, "doc": "the time at which the priority of the list entity was last modified", "compliance": {"long": {"policy": "EVENT_TIME"}}}, {"name": "tier", "type": ["null", "int"], "default": null, "doc": "the tier of the entity within an account map. This field is only applicable for entities in the list that is associated with an account map (defined by AccountToListMapping). Currently, tier can be either 1, 2, or 3 and this maps to TIER_1, TIER_2, and TIER_3 respectively in the Rest.li schema for AccountMapTier: https://jarvis.corp.linkedin.com/codesearch/result/?name=AccountMapTier.pdl. More tiers may be added as product requirements change.", "compliance": "NONE"}, {"name": "searchIndexUpdatePriority", "type": ["null", {"type": "enum", "name": "SearchIndexUpdatePriorityType", "symbols": ["LOW", "HIGH", "SKIP"], "symbolDocs": {"LOW": "The change capture message for the entity will be added to low_priority queue used by liveupdators. This results in the change being reflected with some delay in search results.", "HIGH": "The change capture message for the entity will be added to high_priority queue used by liveupdators. This results in the change being reflected immediately in search results.", "SKIP": "The change capture message for the entity will be skipped by liveupdators"}, "doc": "Different priority types for updating entries in search index using liveupdators"}], "default": null, "doc": "The priority type for list entity when updating search index. When the field is null, it will be treated as HIGH in downstream pipeline. This allows backward compatibility.", "compliance": "NONE"}, {"name": "leadManager", "type": ["null", {"name": "LeadManager", "type": "record", "doc": "It stores the details of assigned manager for the lead in Relationship Map.", "fields": [{"name": "memberUrn", "type": ["null", "string"], "doc": "It is null when manager has been removed or manager is placeholderUrn, else it would be the memberUrn of assigned manager.", "compliance": {"string": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": false}}}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the leadManager field.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat which made the last edit to lead<PERSON><PERSON><PERSON> field.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}, {"name": "salesListEntityPlaceholderUrn", "type": ["null", "string"], "default": null, "doc": "It is null when manager has been removed or manager is a memberUrn, else it would be the salesListEntityPlaceholderUrn of a placeholder entity that belongs in the list.", "compliance": "NONE"}]}], "default": null, "doc": "The manager information of the lead in a relationship map. This field is only applicable for Relationship Map lead entities. ListType -> ACCOUNT_MAP. If the value of memberUrn is null, this indicates the value has been removed by user.", "compliance": {"LeadManager": "INHERITED"}}, {"name": "leadRelationshipStrength", "type": ["null", {"name": "LeadRelationshipStrength", "type": "record", "doc": "Details regarding the relationship strength assigned by users to this lead.", "fields": [{"name": "relationshipStrengthType", "type": ["null", {"type": "enum", "name": "RelationshipStrengthType", "symbols": ["UNKNOWN", "STRONG", "MEDIUM", "WEAK", "NONE"], "symbolDocs": {"UNKNOWN": "Added for schema evolution, to make field forward compatible. This value means a reader has encountered a record produced by a newer schema with the upload state unknown to it.", "STRONG": "The relationship between sales team and lead is strong. Engagement is high.", "MEDIUM": "The relationship between sales team and lead is medium.", "WEAK": "The relationship between sales team and lead is weak. This signals the sales team to improve the engagement with lead.", "NONE": "User knows there is no relationship between lead and sales team. The sales team hasn't engaged with lead yet."}, "default": "UNKNOWN", "doc": "The relationship strength between the sales team and this lead. Sales team here represent collaborators of this Relationship Map."}], "doc": "The type of relationship strength. Will be null if assigned strength has been removed.", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the leadRelationshipStrength field.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat which made the last edit to leadRelationshipStrength field.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}]}], "default": null, "doc": "The field is used to set relationship strength details for the list entity. Is used in Relationship Map. ListType -> ACCOUNT_MAP. If the value of relationshipStrengthType is null, this indicates the value has been removed by user", "compliance": {"LeadRelationshipStrength": "INHERITED"}}, {"name": "leadOwner", "type": ["null", {"name": "LeadOwner", "type": "record", "fields": [{"name": "seatUrn", "type": ["null", "string"], "doc": "SeatUrn for the member which is responsible for this lead. Seat can belong to same contract or different contract within the same CAP account. Seat is null when user has removed the assigned owner.", "compliance": {"string": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the leadOwner field.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat which made the last edit to <PERSON><PERSON><PERSON><PERSON> field.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}]}], "doc": "The field is used to set owner details for the list entity. Is used in Relationship Map. ListType -> ACCOUNT_MAP. If the value of seatUrn is null, this indicates the value has been removed by user.", "default": null, "compliance": {"LeadOwner": "INHERITED"}}, {"name": "leadRole", "type": ["null", {"name": "LeadRole", "type": "record", "fields": [{"name": "roleType", "type": ["null", {"name": "RoleType", "type": "enum", "doc": "represents the type of a lead role.", "symbols": ["UNKNOWN", "DECISION_MAKER", "CHAMPION", "EVALUATOR", "PROCUREMENT", "INFLUENCER", "NONE"], "default": "UNKNOWN", "symbolDocs": {"UNKNOWN": "Added for schema evolution, to make field forward compatible. This value means a reader has encountered a record produced by a newer schema with the upload state unknown to it", "DECISION_MAKER": "A lead who has final say over whether a product or service is ultimately purchased", "CHAMPION": "A lead who is a strong supporter of the product/service, has power in the deal, and can make intros for a rep", "EVALUATOR": "A lead who is involved in the active investigation/exploration of the product/service", "PROCUREMENT": "A lead responsible for purchasing and buying. They give financial sign-off in a deal", "INFLUENCER": "A lead in a deal who does not have economic responsibility, but their input carries weight", "NONE": "A lead who has no role, which is explicitly specified by user. This means user knows the lead has no role defined yet. It is different from empty state where user doesn't know the role yet."}}], "doc": "The type of a lead role. RoleType is null when user has removed the assigned role.", "compliance": "NONE"}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the leadRole field.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat which made the last edit to lead<PERSON><PERSON> field.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}]}], "doc": "The role information of the lead in a relationship map. This field is only applicable for Relationship Map lead entities. ListType -> ACCOUNT_MAP. If the value of roleType is null, this indicates the value has been removed by user.", "default": null, "compliance": {"LeadRole": "INHERITED"}}, {"name": "positionInLevel", "type": ["null", "int"], "default": null, "doc": "The relative position of the lead among peers at the same level (inferred from manager information) in a relationship map list view. If the lead has a manager who is also in the list, peers refer to other leads in the list who report to the same manager as the lead; otherwise, peers refer to other leads who either do not have a manager or the manager is not in the list (i.e. top level leads in a hierarchy view). This field is only applicable for Relationship Map lead entities. This field may be null if the position is never set, or this is not a relationship map lead entity.", "compliance": "NONE"}, {"name": "leadText", "doc": "Field to store details regarding free flow text inputted by user's for this lead. Currently only applicable for placeholder entity type for Account Map. Will be null when the text has been removed or cleared.", "type": ["null", {"name": "LeadText", "type": "record", "fields": [{"name": "textBody", "type": ["null", "string"], "default": null, "doc": "Field to store and free flow text inputted by user for this lead. Currently only applicable for placeholder entity type.", "compliance": {"string": {"policy": "FREEFORMED_UGC"}}}, {"name": "lastModifiedTime", "type": "long", "doc": "A timestamp corresponding to the last modification of the leadText field.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedBySeatUrn", "type": "string", "doc": "The seat which made the last edit to lead<PERSON><PERSON>t field.", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}}]}], "default": null, "compliance": {"LeadText": "INHERITED"}}]}