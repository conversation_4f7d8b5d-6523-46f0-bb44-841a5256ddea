{"name": "RelationshipMapChangeLog", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "doc": "Document schema for logging a change done to a particular relationship map by a user.", "fields": [{"name": "changeType", "type": {"type": "enum", "name": "ChangeTypes", "symbols": ["UNKNOWN", "EDIT_LEAD", "UPDATE_MANAGER", "UPDATE_ROLE", "UPDATE_RELATIONSHIP_STRENGTH", "UPDATE_OWNER", "UPDATE_MAP_NAME", "UPDATE_MAP_COLLABORATORS", "UPDATE_LEAD_POSITION_IN_MAP"], "symbolDocs": {"UNKNOWN": "Added as default value.", "EDIT_LEAD": "Lead was added/removed/replaced in the map.", "UPDATE_MANAGER": "Manager was added/removed/updated for the lead", "UPDATE_ROLE": "Role was added/removed/updated for the lead", "UPDATE_RELATIONSHIP_STRENGTH": "RelationshipStrength was added/removed/updated for the lead", "UPDATE_OWNER": "Owner was added/removed/updated for the lead.", "UPDATE_MAP_NAME": "Map name was updated.", "UPDATE_MAP_COLLABORATORS": "Collaborators were added/removed/updated", "UPDATE_LEAD_POSITION_IN_MAP": "Lead's position in map was added/replaced."}, "default": "UNKNOWN"}, "compliance": "NONE", "doc": "Type of change performed in Relationship Map"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "doc": "the seat urn of relationship map collaborator that performed the change. Supports seatUrn. Example urn:li:seat:1234", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "actor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string", "doc": "the contract urn of seat that performed the change. Supports contractUrn. Example urn:li:contract:1234", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "targetMemberUrn", "type": ["null", "string"], "doc": "The member urn for targeted lead on which the change was performed. Will be null in case change type is EDIT_LEAD, UPDATE_MAP_NAME & UPDATE_MAP_COLLABORATORS. Supports memberUrn. Example: urn:li:member:1234", "compliance": {"string": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": false}}}, {"name": "changeValue", "type": ["null", "string"], "default": null, "doc": "The value of change that was performed. It will be memberUrn for EDIT_LEAD/UPDATE_MANAGER actionType, seatUrn for UPDATE_OWNER and enum values for UPDATE_RELATIONSHIP_STRENGTH and UPDATE_ROLE. It will be null for remove operation of any change type. Example seatUrn: urn:li:seat:1234, memberUrn: urn:li:member:1234. Non urn data example: relationshipStrengthType: STRONG, leadRole: DECISION_MAKER mapName: TestName, leadPositionInMap: 1", "compliance": {"string": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": false}, {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}]}}, {"name": "previousChangeValue", "type": ["null", "string"], "default": null, "doc": "The previous value of change that was performed. It will be memberUrn for EDIT_LEAD/UPDATE_MANAGER actionType, seatUrn for UPDATE_OWNER and enum values for UPDATE_RELATIONSHIP_STRENGTH and UPDATE_ROLE. It will be null for remove operation of any change type. Example seatUrn: urn:li:seat:1234, memberUrn: urn:li:member:1234. Non urn data example: relationshipStrengthType: STRONG, leadRole: DECISION_MAKER, mapName: TestName, leadPositionInMap: 1", "compliance": {"string": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": false}, {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}]}}, {"name": "eventTime", "doc": "The timestamp at which the change event was performed.", "type": "long", "compliance": "NONE", "indexType": "native"}, {"name": "addedV<PERSON>ues", "type": ["null", {"type": "array", "items": "string"}], "default": null, "doc": "The values added in the change. This is used when partial operation is done and values are appended to field. Applicable for field storing array of values. It will be array of seatUrn for UPDATE_MAP_COLLABORATORS", "compliance": {"array": [{"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}]}}, {"name": "removedV<PERSON><PERSON>", "type": ["null", {"type": "array", "items": "string"}], "default": null, "doc": "The values removed in the change. This is used when partial operation is done and values are removed from the field. Applicable for field storing array of values. It will be array of seatUrn for UPDATE_MAP_COLLABORATORS", "compliance": {"array": [{"policy": "SEAT_ID", "format": "URN", "isPurgeKey": false}]}}]}