{"name": "SeatList", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for custom list for all seats. An entry in this table represents the relationship of a seat and a list that is either owned by the seat or has been shared with that seat", "fields": [{"name": "contractId", "type": "long", "doc": "contractId for the seat who owns the list", "compliance": {"policy": "CONTRACT_ID", "format": "NUMERIC"}}, {"name": "role", "type": {"type": "enum", "name": "SeatListRole", "symbols": ["OWNER", "CONTRIBUTOR", "VIEWER"], "symbolDocs": {"OWNER": "The list is created by this seat, which means they can delete the list", "CONTRIBUTOR": "the role that can help adding/removing entity to the list", "VIEWER": "the role that can only view this list."}, "doc": "different role type for the list", "version": 1}, "indexType": "native", "doc": "the role of the list, indicating what operation the user can do to this list", "compliance": "NONE"}, {"name": "ownedByMe", "type": "boolean", "doc": "to indicate if the creator of the list is the seat in the primary key", "compliance": "NONE"}, {"name": "lastViewedTime", "type": "long", "indexType": "native", "doc": "the last time the list is viewed by the seat", "compliance": "NONE"}]}