{"name": "AccountToListMapping", "doc": "Table Schema for the mapping between an account map and the custom list. The account map is a hierarchical view of the important contacts within an account and is represented by a custom list with each entity belonging to a particular tier.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/AccountToListMapping", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "organizationUrn", "type": "STRING", "maxsize": 40, "compliance": {"policy": "COMPANY_ID", "format": "URN"}}, {"name": "listId", "type": "LONG", "compliance": "NONE"}]}