{"name": "CreatorToListCsvImportView", "doc": "Table Schema for a view of ListCsvImport keyed by creator", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/CreatorToListCsvImportView", "derivedType": "VIEW", "version": 1, "resourceKeyParts": [{"name": "creatorSeat<PERSON>rn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "listCsvImportId", "type": "LONG", "compliance": "NONE"}]}