{"name": "EntityToListView", "doc": "Table schema for the entity to the list mapping (derived from ListEntity table)", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/EntityToListView", "derivedType": "VIEW", "version": 1, "resourceKeyParts": [{"name": "entityUrn", "type": "STRING", "maxsize": 100}, {"name": "contractId", "type": "LONG"}, {"name": "ownerSeatId", "type": "LONG"}, {"name": "listId", "type": "LONG"}]}