{"name": "ListComment", "doc": "Table Schema for comments in custom lists", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/ListComment", "version": 1, "resourceKeyParts": [{"name": "entityUrn", "type": "STRING", "maxsize": 50, "compliance": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}, {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": true}]}, {"name": "listId", "type": "LONG", "compliance": "NONE"}, {"name": "commentId", "type": "LONG", "autoincrement": "LOCAL", "compliance": "NONE"}]}