{"name": "RelationshipMapChangeLog", "doc": "Table Schema for capturing all the changes done by users in relationship maps.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/RelationshipMapChangeLog", "version": 1, "resourceKeyParts": [{"name": "relationshipMapId", "type": "LONG", "compliance": "NONE"}, {"name": "changeLogId", "type": "LONG", "autoincrement": "LOCAL", "compliance": "NONE"}]}