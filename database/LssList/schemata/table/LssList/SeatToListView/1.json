{"name": "SeatToListView", "doc": "Table schema for the seat to the list mapping (derived from List table)", "schemaType": "TableSchema", "recordType": "/schemata/document/LssList/SeatToListView", "derivedType": "VIEW", "version": 1, "resourceKeyParts": [{"name": "seatId", "type": "LONG", "compliance": {"policy": "SEAT_ID", "format": "NUMERIC", "isPurgeKey": true}}, {"name": "listType", "type": "STRING", "maxsize": 20, "compliance": "NONE"}, {"name": "listSource", "type": "STRING", "maxsize": 30, "compliance": "NONE"}, {"name": "listId", "type": "LONG", "compliance": "NONE"}]}