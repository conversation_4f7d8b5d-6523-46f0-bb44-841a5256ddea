{"name": "Note", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 3, "doc": "Document schema for the information of the note", "fields": [{"name": "createdTime", "type": "long", "doc": "the time that the note was created, in milliseconds since epoch time", "indexType": "native", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the note was last modified, in milliseconds since epoch time", "indexType": "native", "compliance": {"policy": "EVENT_TIME"}}, {"name": "contractUrn", "type": "string", "doc": "the contract that the note belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "body", "type": {"type": "record", "name": "AttributedText", "fields": [{"name": "text", "type": "string", "doc": "The raw text content that may be attributed, from user input", "compliance": {"policy": "FREEFORMED_UGC"}}, {"name": "attributes", "type": {"type": "array", "items": {"type": "record", "name": "Attribute", "fields": [{"name": "start", "type": "int", "doc": "Starting position of the range within the referenced ordered collection.", "compliance": "NONE"}, {"name": "end", "type": "int", "doc": "Ending position of the range within the referenced ordered collection.", "compliance": "NONE"}, {"name": "value", "type": "string", "doc": "The value of the attribute that applies to given range. e.g. a MEMBER_ID", "compliance": "NONE"}], "doc": "A property of a condition, for example, RESOURCE_TYPE, MEMBER_ID, etc. Attributes are used to determine whether a condition applies to a request"}}, "doc": "A text with attributes within it. Contains text: the raw user input, and attributes: starting and ending index with attribute value.", "compliance": "INHERITED"}], "doc": "A text with attributes within it."}, "doc": "The content body of this note.", "compliance": "INHERITED"}, {"name": "migrationId", "type": ["null", "long"], "doc": "The ID of migrated oracle note entry. Plan to deprecate when all oracle note usage are migrated.", "indexType": "native", "compliance": "NONE", "default": null}, {"name": "expireTime", "type": ["null", "long"], "doc": "The timestamp when the note expires. When a note related to a notification expires the note has to be deleted.", "compliance": {"long": "NONE"}, "default": null}]}