{"name": "Note", "doc": "Table schema for the note. A note is user customized text on an entity.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssNote/Note", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 93, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "entityUrn", "type": "STRING", "maxsize": 100, "compliance": [{"policy": "MEMBER_ID", "format": "URN"}, {"policy": "COMPANY_ID", "format": "URN"}]}, {"name": "noteId", "type": "LONG", "autoincrement": "LOCAL", "compliance": "NONE"}]}