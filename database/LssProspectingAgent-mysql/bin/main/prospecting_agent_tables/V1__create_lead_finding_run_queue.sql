CREATE TABLE LEAD_FINDING_RUN_QUEUE (
  REQUEST_ID BIGINT NOT NULL PRIMARY KEY COMMENT 'Primary key ID of the queue request. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  RUN_ID BIGINT NOT NULL COMMENT 'Lead finding run Id of the queue request. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  CAMPAIGN_ID BIGINT NOT NULL COMMENT 'The campaign id of the lead finding run. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  SEAT_ID BIGINT NOT NULL COMMENT 'The seat triggering the lead finding run. <compliance> {"dataType": "SEAT_ID", "fieldFormat": "NUMERIC",, "nonOwner": false} </compliance>',
  CONTRACT_ID BIGINT NOT NULL COMMENT 'The contract of the Sales Navigator user who owns this run. <compliance> {"dataType": "CONTRACT_ID", "fieldFormat": "NUMERIC", "nonOwner": false} </compliance>',
  MEMBER_ID BIGINT NOT NULL COMMENT 'The member triggering the lead finding run. <compliance> {"dataType": "MEMBER_ID", "fieldFormat": "NUMERIC", "nonOwner": false } </compliance>',
  STATUS ENUM('PENDING', 'PROCESSED', 'COMPLETED') NOT NULL DEFAULT 'PENDING' COMMENT 'The status of the lead finding run. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  PRIORITY TINYINT NOT NULL DEFAULT 10 COMMENT 'The priority of the lead finding run. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  RUN_TYPE ENUM('ON_DEMAND', 'SCHEDULED') NOT NULL COMMENT 'The type of the lead finding run. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  TRACKING_ID VARCHAR(50) NOT NULL COMMENT 'The tracking id of the lead finding run. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  QUEUED_TIME DATETIME (6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT 'The time when the lead finding run was queued. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  GG_MODI_TS DATETIME (6) DEFAULT CURRENT_TIMESTAMP(6) COMMENT 'GG replication modification timestamp <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  GG_STATUS VARCHAR(1) DEFAULT 'o' COMMENT 'GG replication status <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  DELETED_TS DATETIME (6) COMMENT 'The soft deletion timestamp. <compliance> {"dataType": "NONE", "nonOwner": false} </compliance>',
  ETL_TS DATETIME (6) DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT 'ETL column required by Opal'
) ENGINE=InnoDB COMMENT 'Store lead finding run queue request records';

-- Creating indexes
/* Search by STATUS and Order by PRIORITY, QUEUED_TIME*/
CREATE INDEX LEAD_FINDING_RUN_QUEUE_IDX_1 on LEAD_FINDING_RUN_QUEUE (STATUS, PRIORITY, QUEUED_TIME);

/* Search by CAMPAIGN_ID*/
CREATE INDEX LEAD_FINDING_RUN_QUEUE_IDX_2 on LEAD_FINDING_RUN_QUEUE (CAMPAIGN_ID);

/* Index on GG timestamp */
CREATE INDEX LEAD_FINDING_RUN_QUEUE_gg_ts on LEAD_FINDING_RUN_QUEUE (GG_MODI_TS);

/* Soft Delete Index */
CREATE INDEX LEAD_FINDING_RUN_QUEUE_dt on LEAD_FINDING_RUN_QUEUE (DELETED_TS);