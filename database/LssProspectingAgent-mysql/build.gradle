apply plugin: 'li-java'
apply plugin: 'li-mysql' // li-database
apply plugin: 'java-library'

// no need to check jooq generated files
spotbugsMain.enabled = false

mysql {
  dbName = "lss_prospecting_agent_ei"
  // dummy user name
  userName = "lss_prospecting_agent"
  // a random port that is unlikely to conflict with an existing app
  port = 2759
  migrationsDirectoryPath = project.layout.projectDirectory.dir("src/main/resources/prospecting_agent_tables")
  // base package path for generated schema bindings
  packageName = "com.linkedin.sales.database.mysql.generated"

  // Configure code gen to exclude columns from JooQ codegen, the intent here is to exclude internal fields needed for
  // Golden Gate replication. Those fields should not show up in the generated POJOs as app logic should never read / write
  // to those fields
  excludeColumns = true

  // Exclude Flyway tables from codegen task
  // Exclusion pattern for JooQ codegen to skip code gen for Golden Gate replication fields
  // Doc: https://www.jooq.org/doc/3.18/manual/code-generation/codegen-advanced/codegen-config-database/codegen-database-includes-excludes/
  tableExcludePattern = "flyway_.*|gg_modi_ts|gg_status|etl_ts"

  codeGenerate.get().with {
    immutablePojos = false
    varargSetters = false
  }

  // required for li-database (li-mysql) starting v12.*
  forcedTypes {
    forcedType {
      name = "BOOLEAN"
      includeTypes = "TINYINT?\\(1\\)"
    }
  }

  provisionerImplementation = "NEW"
}

dependencies {
  api spec.external.'jooq'
}

def generatedJooqDirPath = projectDir.toPath().resolve('src/mainGeneratedJooq/java')
sourceSets.main.java.srcDir generatedJooqDirPath
idea.module.sourceDirs += file(generatedJooqDirPath)

// Configure task settings for this project
tasks.named('compileJava') {
  dependsOn tasks.named('generateJooq')
}
tasks.named('spotbugsMain') {
  // Disable SpotBugs because the code in this subproject is autogenerated.
  enabled = false
}

tasks.findByName('forkedCheckstyleMain')?.enabled = false
