{"name": "RecentSearches", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Store LSS recent searches queried by the member. Every time a member makes a new search or updates an existing search, an entry is created or updated. Doc: go/lssrecentsearches", "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "searchQuery", "type": "string", "doc": "Serialized SearchQuery. For Eg. {keywords='test',facetSelections=[{valuesWithSelections=[{value=123, selected=true}]}]}", "compliance": "NONE"}, {"name": "lastSearchedTime", "type": "long", "indexType": "native", "doc": "The time when the search entry was created or last updated", "compliance": {"policy": "EVENT_TIME"}}]}