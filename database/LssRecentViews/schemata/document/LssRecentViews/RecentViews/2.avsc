{"name": "RecentViews", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 2, "doc": "Store LSS recent views", "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "entities", "type": {"type": "array", "items": {"type": "record", "name": "RecentViewEntity", "fields": [{"name": "entity", "type": ["null", "string"], "default": null, "doc": "Can be memberUrn, organizationUrn, salesListUrn or SearchQuery", "compliance": {"string": {"policy": "UNSTRUCTURED_PII", "isPurgeKey": true}}}, {"name": "lastViewedTime", "type": "long", "doc": "The last viewed time.", "compliance": {"policy": "EVENT_TIME"}}, {"name": "searchRequestId", "type": ["null", "string"], "default": null, "doc": "The request id associated with the current search. This is set if the entity is of type SearchQuery. This value is a UUID converted to Base64 string. For eg. eRzAcN12S+Oag3TYoVlnzA==", "compliance": "NONE"}], "doc": "Recently viewed entity stored without any order"}}, "compliance": "INHERITED"}]}