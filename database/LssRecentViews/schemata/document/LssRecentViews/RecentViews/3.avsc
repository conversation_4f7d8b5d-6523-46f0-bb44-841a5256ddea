{"name": "RecentViews", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 3, "doc": "Store LSS recent views. An entry is created or updated when a member views a profile, company page or list page. Doc: go/lsshomepageredesignrfc", "fields": [{"name": "contractUrn", "type": "string", "doc": "The contract which the seat belongs to. Example: urn:li:contract:123", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "entities", "type": {"type": "array", "items": {"type": "record", "name": "RecentViewEntity", "fields": [{"name": "entity", "type": ["null", "string"], "default": null, "doc": "Can be memberUrn, organizationUrn, salesListUrn. For eg. urn:li:member:123, urn:li:company:1035, urn:li:salesList:1035", "compliance": {"string": [{"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}, {"policy": "COMPANY_ID", "format": "URN", "isPurgeKey": true}]}}, {"name": "lastViewedTime", "type": "long", "doc": "The last viewed time.", "compliance": {"policy": "EVENT_TIME"}}], "doc": "Recently viewed entity stored without any order"}}, "compliance": "INHERITED"}]}