{"name": "RecentSearchesV2", "doc": "Used to store LSS recent searches. Every time a member makes a new search or updates an existing search, an entry is created or updated. Doc: go/lssrecentsearches", "schemaType": "TableSchema", "recordType": "/schemata/document/LssRecentViews/RecentSearchesV2", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32}, {"name": "searchType", "type": "STRING", "maxsize": 18}, {"name": "recentSearchId", "type": "LONG", "autoincrement": "GLOBAL"}]}