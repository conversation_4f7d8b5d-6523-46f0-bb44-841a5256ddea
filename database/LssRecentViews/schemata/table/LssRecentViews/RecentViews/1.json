{"name": "RecentViews", "doc": "Used to store LSS recent views for account, list and profile of a seat holder", "schemaType": "TableSchema", "recordType": "/schemata/document/LssRecentViews/RecentViews", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32, "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "entityType", "type": "STRING", "maxsize": 14, "compliance": "NONE"}]}