{"schemaType": "DBSchema", "name": "LssSavedLeadAccount", "version": 1, "doc": "The LssSavedLeadAccount database models LSS's saved leads/accounts behavior. A lead/account is a Linkedin member/organization that represents a potential or existing customer of a Sales Navigator user. A lead/account becomes saved when the member/organization is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync. This database also models other attributes of lead/account, such as the association between lead and account.", "partitionType": "HASH", "numBuckets": 8, "etlType": "LUMOS"}