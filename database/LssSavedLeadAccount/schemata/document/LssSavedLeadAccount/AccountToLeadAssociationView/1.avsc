{"name": "AccountToLeadAssociationView", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Materialized view that keeps the association between LSS saved account and LSS saved lead, the association be created by LSS seat holder", "fields": [{"name": "createdTime", "type": "long", "doc": "the time that the accountToLeadAssociation is created", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the accountToLeadAssociation is last modified", "compliance": {"policy": "EVENT_TIME"}}, {"name": "contractUrn", "type": "string", "doc": "the contract of the Sales Navigator user who owns the data record.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}]}