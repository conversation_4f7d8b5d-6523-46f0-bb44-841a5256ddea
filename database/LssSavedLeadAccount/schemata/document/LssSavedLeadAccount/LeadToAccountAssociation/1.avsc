{"name": "LeadToAccountAssociation", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Associates saved lead with saved account, the association be created by LSS seat holder", "views": [{"name": "AccountToLeadAssociationMappingView", "destinationTable": "AccountToLeadAssociationView", "function": "MAP", "destinationKey": ["key.seatUrn", "key.organizationUrn", "key.memberUrn"], "destinationValue": ["createdTime=field.createdTime", "lastModifiedTime=field.lastModifiedTime", "contractUrn=field.contractUrn"]}], "aggregates": [{"name": "totalCount", "destination": "LeadCountInAccount.totalCount", "function": "count()", "groupBy": ["table.seatUrn", "table.organizationUrn"]}], "fields": [{"name": "createdTime", "type": "long", "doc": "the time that the leadToAccountAssociation is created", "compliance": {"policy": "EVENT_TIME"}}, {"name": "lastModifiedTime", "type": "long", "doc": "the time that the leadToAccountAssociation is last modified", "compliance": {"policy": "EVENT_TIME"}}, {"name": "contractUrn", "type": "string", "doc": "the contract of the Sales Navigator user who owns the data record", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}]}