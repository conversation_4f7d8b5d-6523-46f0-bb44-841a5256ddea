{"name": "SavedLead", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for LSS saved lead. In LSS, lead is a Linkedin member that represents a potential or existing customer. A lead becomes a saved lead when the organization is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync.", "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract of the Sales Navigator user who owns the data record, such as urn:li:contract:1000", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "createdTime", "type": "long", "doc": "the time that the lead is saved", "compliance": {"policy": "EVENT_TIME"}}]}