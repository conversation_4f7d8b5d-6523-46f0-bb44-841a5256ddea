{"name": "LeadToAccountAssociation", "doc": "Used to store LSS association data between LSS saved lead and LSS saved account, the association created by LSS seat holder", "schemaType": "TableSchema", "recordType": "/schemata/document/LssSavedLeadAccount/LeadToAccountAssociation", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32}, {"name": "memberUrn", "type": "STRING", "maxsize": 34}, {"name": "organizationUrn", "type": "STRING", "maxsize": 40}]}