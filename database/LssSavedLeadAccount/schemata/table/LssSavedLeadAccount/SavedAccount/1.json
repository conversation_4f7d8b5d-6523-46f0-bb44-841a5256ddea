{"name": "SavedAccount", "doc": "Table schema for LSS saved account. In LSS, account is a Linkedin organization that represents a potential or existing customer. An account becomes a saved account when the organization is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssSavedLeadAccount/SavedAccount", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32}, {"name": "organizationUrn", "type": "STRING", "maxsize": 40}]}