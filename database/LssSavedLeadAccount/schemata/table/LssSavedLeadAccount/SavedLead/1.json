{"name": "SavedLead", "doc": "Table schema for LSS saved lead. In LSS, lead is a Linkedin member that represents a potential or existing customer. A lead becomes a saved lead when the organization is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync.", "schemaType": "TableSchema", "recordType": "/schemata/document/LssSavedLeadAccount/SavedLead", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "maxsize": 32}, {"name": "memberUrn", "type": "STRING", "maxsize": 34}]}