{"name": "MobileSettings", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for mobile setting of a given seat", "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract that the setting belongs to", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "calendarSyncSettings", "type": ["null", {"type": "record", "name": "CalendarSyncSettings", "fields": [{"name": "isEnabled", "type": "boolean", "doc": "boolean flag to control if the calendar sync feature is enabled or not", "compliance": "NONE"}, {"name": "isEventWithoutAttendeesShown", "type": "boolean", "doc": "boolean flag to control if events without attendees will be shown", "compliance": "NONE"}, {"name": "isAllEventsForTodayShown", "type": "boolean", "doc": "boolean flag to control if homepage should show all of events for today or the events only occuring right now and up to one next event", "compliance": "NONE"}, {"name": "syncedCalendars", "type": {"type": "array", "items": "string"}, "doc": "The list of unique calendar ids to sync", "compliance": [{"policy": "UNSTRUCTURED_PII"}]}]}], "doc": "Stores calendar sync settings for Sales Navigator Mobile", "default": null, "compliance": {"CalendarSyncSettings": "INHERITED"}}, {"name": "isCallLoggingEnabled", "type": "boolean", "doc": "Whether the call logging is enabled or not for Sales Navigator Mobile", "default": false, "compliance": "NONE"}, {"name": "rateTheAppLastShowAt", "type": ["null", "long"], "doc": "Last time when the AskForRating dialog shows up", "compliance": "NONE"}]}