{"name": "SeatSetting", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 5, "doc": "Document schema for setting of a given seat", "fields": [{"name": "contractUrn", "type": "string", "doc": "the contract that the preference belongs to", "compliance": {"policy": "CONTRACT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "visibilityAsProfileViewer", "type": ["null", {"type": "enum", "name": "VisibilityAsProfileViewer", "symbols": ["FULL", "ANONYMOUS", "HIDE"]}], "doc": "Visibility setting, define what others see when you’ve viewed their profile in Sales Navigator", "default": null, "compliance": "NONE"}, {"name": "emailPreferences", "type": ["null", {"type": "map", "values": "boolean"}], "doc": "Email references are about email settings, which type of email notifications user wants to subscribe, all the types are defined in sales-api com.linkedin.sales.services.settings.EmailSettingsService", "default": null, "compliance": "NONE"}, {"name": "pushNotificationEnabled", "type": ["null", "boolean"], "doc": "Mobile push notification setting, whether it is enabled or not", "default": null, "compliance": "NONE"}, {"name": "inMailMessageSignature", "type": ["null", "string"], "doc": "Message signature for Sales Navigator inMail message", "default": null, "compliance": {"string": {"policy": "NAME"}}}, {"name": "usageReportingFlagshipDataOptOut", "type": ["null", "boolean"], "doc": "Whether to allow your company to see select data about your LinkedIn.com activities, including number of days active, number of searches performed, and number of profiles viewed on LinkedIn.com.", "default": null, "compliance": "NONE"}, {"name": "teamlinkOptedOut", "type": ["null", "boolean"], "doc": "TeamLink lets you view and search your team’s connections. Your teammates will have the same access to your connections. User can choose to opt it out, inactivate it.", "default": null, "compliance": "NONE"}, {"name": "onboardingCompletedTime", "type": ["null", "long"], "doc": "Timestamp indicating when the seat completed the onboarding flow. This field will be null when the seat is invited, but has not yet completed onboarding. Timestamp -> epoch in milliseconds", "default": null, "compliance": "NONE"}, {"name": "lastLoggedInTime", "type": ["null", "long"], "doc": "Timestamp of the last login time of the seat. This field will be null if the seat has never been logged into. Timestamp -> epoch in milliseconds", "default": null, "compliance": "NONE"}, {"name": "dismissedBuyerIntentCardTime", "type": ["null", "long"], "doc": "Timestamp of the last time the seat dismissed the homepage insight alert card for buyer intent. This field will be null if the seat has never dismissed the insight alert for buyer intent. Timestamp -> epoch in milliseconds", "default": null, "compliance": "NONE"}, {"name": "crmAutoSavePreferences", "doc": "crm auto save settings provided by the user which type of crm records are eligible to a add and save into System generated Lists, all of settings types are defined in crm-api com.linkedin.crm.SalesConnectedCrmSetting", "type": ["null", {"type": "array", "items": {"name": "CrmAutoSaveSettings", "doc": "record containing setting and its value", "type": "record", "fields": [{"name": "CrmAutoSaveSettingType", "doc": "Represent the type of a crm setting.", "type": {"type": "enum", "name": "CrmAutoSaveSettingType", "symbols": ["AUTO_SAVE_ACCOUNTS_WITH_OWNED_OPEN_OPPORTUNITIES_ENABLED", "AUTO_SAVE_OWNED_ACCOUNTS_ENABLED", "AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED", "AUTO_SAVE_ACCOUNTS_WITH_OWNER_TEAM_ENABLED", "AUTO_SAVE_ACCOUNTS_WITH_OWNER_TEAM_OPEN_OPPORTUNITIES_ENABLED", "AUTO_SAVE_CONTACTS_WITH_OWNED_OPEN_OPPORTUNITIES_ENABLED", "AUTO_SAVE_CONTACTS_WITH_OWNED_ACCOUNTS_ENABLED", "AUTO_SAVE_CONTACTS_WITH_OWNER_TEAM_ENABLED", "AUTO_SAVE_CONTACTS_WITH_OWNER_TEAM_OPEN_OPPORTUNITIES_ENABLED", "AUTO_SAVE_CONTACTS_WITH_ACCOUNT_TEAMS_ENABLED", "AUTO_SAVE_OWNED_CONTACTS_OR_LEADS_ENABLED"], "symbolDocs": {"AUTO_SAVE_ACCOUNTS_WITH_OWNED_OPEN_OPPORTUNITIES_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Accounts associated with user’s owned Open Opportunities.", "AUTO_SAVE_OWNED_ACCOUNTS_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Accounts owned by user.", "AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Accounts that the user has an affiliation with via Account Team membership\nThis only applies to Salesforce Account Team.", "AUTO_SAVE_ACCOUNTS_WITH_OWNER_TEAM_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Accounts that a user has an affiliation with via owner Team membership\nThis only applies to Microsoft Dynamics owner Team.", "AUTO_SAVE_ACCOUNTS_WITH_OWNER_TEAM_OPEN_OPPORTUNITIES_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Accounts associated with Open Opportunities that a user has an affiliation with via owner Team membership\nThis only applies to Microsoft Dynamics owner Team.", "AUTO_SAVE_CONTACTS_WITH_OWNED_OPEN_OPPORTUNITIES_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts associated with user’s owned Open Opportunities.", "AUTO_SAVE_CONTACTS_WITH_OWNED_ACCOUNTS_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts associated with user’s owned Accounts.", "AUTO_SAVE_CONTACTS_WITH_OWNER_TEAM_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts within acounts that a user has an affiliation with via owner Team membership\nThis only applies to Microsoft Dynamics owner Team.", "AUTO_SAVE_CONTACTS_WITH_OWNER_TEAM_OPEN_OPPORTUNITIES_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts associated with Open Opportunities that a user has an affiliation with via owner Team membership\nThis only applies to Microsoft Dynamics owner Team.", "AUTO_SAVE_CONTACTS_WITH_ACCOUNT_TEAMS_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts within acounts that a user has an affiliation with via Account Team membership\nThis only applies to Salesforce Account Team.", "AUTO_SAVE_OWNED_CONTACTS_OR_LEADS_ENABLED": "Boolean flag to indicate whether enabling CRM auto save for CRM Contacts and Leads owned by user."}}, "compliance": "NONE"}, {"name": "value", "type": "boolean", "doc": "Boolean flag to indicate whether or not setting is enabled.", "compliance": "NONE"}]}}], "default": null, "compliance": {"array": "INHERITED"}}, {"name": "aiMessagingPreferences", "doc": "Stores users AI Messaging assistant preferences", "default": null, "compliance": {"AIMessagingPreferences": "INHERITED"}, "type": ["null", {"type": "record", "name": "AIMessagingPreferences", "fields": [{"name": "lastUsedProductUrn", "type": ["null", "string"], "doc": "Urn of the product (Eg: urn:li:fs_salesProduct:12345) that was selected by the user explicitly last time to generate AI message", "default": null, "compliance": "NONE"}, {"name": "lastUsedMessageType", "doc": "Represent the type of message last used by the user.", "type": ["null", {"type": "enum", "name": "AIMessageType", "symbols": ["UNKNOWN", "INTRODUCTION", "SALES"], "symbolDocs": {"UNKNOWN": "Default value", "INTRODUCTION": "Introduction type message (Saying hi)", "SALES": "Sales pitch type message"}, "default": "UNKNOWN"}], "compliance": "NONE"}]}]}]}