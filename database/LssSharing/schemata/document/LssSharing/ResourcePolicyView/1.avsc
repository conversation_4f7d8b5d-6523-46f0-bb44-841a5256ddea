{"name": "ResourcePolicyView", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 1, "doc": "Document schema for the information of sharing policy about access level of a subject on a resource. This is materialized view of SubjectPolicy. Partitioned by resource", "fields": [{"name": "role", "type": {"type": "enum", "name": "ShareRole", "symbols": ["READER", "WRITER", "OWNER"], "symbolDocs": {"READER": "have access to only read shared entity", "WRITER": "have access to read and write shared entity", "OWNER": "have access to read, write, share, delete shared entity"}, "doc": "different roles that are supported by platform"}, "indexType": "native", "doc": "access role the subject has on given resource", "compliance": "NONE"}, {"name": "contractUrn", "type": "string", "doc": "the contract Urn that current subject and resource belong", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}]}