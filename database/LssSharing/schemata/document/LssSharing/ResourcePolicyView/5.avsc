{"name": "ResourcePolicyView", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 5, "doc": "Document schema for the information of sharing policy about access level of a subject on a resource. This is materialized view of SubjectPolicy. Partitioned by resource", "fields": [{"name": "role", "type": {"type": "enum", "name": "ShareRole", "symbols": ["READER", "WRITER", "OWNER"], "symbolDocs": {"READER": "have access to only read shared entity", "WRITER": "have access to read and write shared entity", "OWNER": "have access to read, write, share, delete shared entity"}, "doc": "different roles that are supported by platform"}, "indexType": "native", "doc": "access role the subject has on given resource", "compliance": "NONE"}, {"name": "contractUrn", "type": ["null", "string"], "default": null, "doc": "The contract Urn that current subject and resource belong. This field is optional for resources that do not have contracts.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "isSubscribed", "type": ["null", "boolean"], "doc": "Indicates whether the subject will receive updates when the resource is modified by collaborators or the system.", "default": null, "compliance": "NONE"}, {"name": "createdTime", "type": ["null", "long"], "doc": "Time in ms after epoch when the subject policy was created.", "default": null, "compliance": "NONE"}, {"name": "acceptedTime", "type": ["null", "long"], "doc": "Time in ms after epoch when the subject accepted the policy. Accepting the policy means having write access to resource. Applicable for relationship map.", "default": null, "compliance": "NONE"}]}