{"name": "SubjectPolicy", "schemaType": "DocumentSchema", "namespace": "com.linkedin.sales.espresso", "type": "record", "version": 3, "evolutionSafetyMode": "IGNORE_WARNINGS", "doc": "Document schema for the information of sharing policy about access level of a subject on a resource. Partitioned by subject(e.g. seat holder)", "views": [{"name": "ResourcePolicyMappingView", "destinationTable": "ResourcePolicyView", "function": "MAP", "destinationKey": ["key.resourceUrn", "key.resourceType", "key.subjectUrn"], "destinationValue": ["role=field.role", "contractUrn=field.contractUrn"]}], "fields": [{"name": "role", "type": {"type": "enum", "name": "ShareRole", "symbols": ["READER", "WRITER", "OWNER"], "symbolDocs": {"READER": "have access to only read shared entity", "WRITER": "have access to read and write shared entity", "OWNER": "have access to read, write, share, delete shared entity"}, "doc": "different roles that are supported by platform"}, "indexType": "native", "doc": "access role the subject has on given resource", "compliance": "NONE"}, {"name": "contractUrn", "type": ["null", "string"], "default": null, "doc": "The contract Urn that current subject and resource belong. This field is optional for resources that do not have contracts.", "compliance": {"policy": "CONTRACT_ID", "format": "URN"}}, {"name": "resourceContext", "type": ["null", "string"], "default": null, "indexType": "native", "doc": "for resources such as notes (PolicyType=NOTE) that are in context of an entity, we denormalize the entity reference here for performance.", "compliance": "NONE"}, {"name": "expirationTime", "type": ["null", "long"], "doc": "Time in ms after epoch that indicates the share policy expires. A data clean up is performed periodically. Null if the policy doesn't expire", "default": null, "compliance": "NONE"}]}