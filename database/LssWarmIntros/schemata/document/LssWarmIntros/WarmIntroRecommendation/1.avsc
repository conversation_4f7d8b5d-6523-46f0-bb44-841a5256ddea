{"name": "WarmIntroRecommendation", "doc": "Document schema for storing list of warm introducer recommendations", "schemaType": "DocumentSchema", "type": "record", "namespace": "com.linkedin.sales.espresso", "version": 1, "fields": [{"name": "introducerRecommendations", "type": {"type": "array", "items": {"name": "IntroducerRecommendation", "type": "record", "doc": "A warm introducer recommendation for the lead", "fields": [{"name": "introducer", "type": "string", "doc": "The memberUrn of the recommended introducer", "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}, {"name": "rationale", "type": "string", "doc": "<PERSON><PERSON><PERSON> on why this member is a good introducer to the lead", "compliance": "NONE"}, {"name": "score", "type": "int", "doc": "Score of the warm intro path recommendation", "compliance": "NONE"}, {"name": "variant", "type": "string", "doc": "Describes the variant of the system when the introducer was found", "compliance": "NONE"}, {"name": "createdTime", "type": "long", "doc": "The time when the introducer recommendation was created, stored as EPOCH long", "compliance": "NONE"}, {"name": "modifiedTime", "type": "long", "doc": "The time when the introducer recommendation was last modified, stored as EPOCH long", "compliance": "NONE"}, {"name": "status", "type": "string", "doc": "The status of the warm intro recommendation, whether they were recommended, messaged, etc.", "compliance": "NONE"}]}}, "doc": "The list of introducers that can help with a warm introduction to the recommended lead", "compliance": "NONE", "default": []}]}