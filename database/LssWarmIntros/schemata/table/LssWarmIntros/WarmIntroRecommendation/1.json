{"name": "WarmIntroRecommendation", "doc": "Table for storing warm introducer recommendation", "schemaType": "TableSchema", "recordType": "/schemata/document/LssWarmIntros/WarmIntroRecommendation", "version": 1, "resourceKeyParts": [{"name": "seatUrn", "type": "STRING", "doc": "The seat requesting for a warm introduction", "compliance": {"policy": "SEAT_ID", "format": "URN", "isPurgeKey": true}}, {"name": "leadMemberUrn", "type": "STRING", "doc": "The memberUrn of the lead for which the introducer was generated", "compliance": {"policy": "MEMBER_ID", "format": "URN", "isPurgeKey": true}}]}