/* WIKI FEEDBACK BUTTON Styles */
.wiki_feedback2 {
  transform: rotate(-90deg);
  transform-origin: right bottom;
  right: 0px;
  vertical-align: baseline;
  position: fixed;
  font-size: 100%;
  padding: 0px;
  margin: 0px;
  display: block;
  outline-width: 0px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
}
.tester::part(button) {
  color: #fff;
  background-color: #0073b1;
  padding: 8px 9px;
  cursor: pointer;
  border: 0px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
  text-transform: uppercase !important;
  font-weight: 500;
  margin-top: -8;
}
.tester::part(button):hover {
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.4);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.4), 0 1px 1px rgba(0, 0, 0, 0.2);
  background: #1c4b72;
  color: #fff;
  text-decoration: none;
}
