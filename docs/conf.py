# -*- coding: utf-8 -*-
import datetime

# Add any Sphinx extension module names here, as strings. They can be extensions
# coming with Sphinx (named 'sphinx.ext.*') or your custom ones.
extensions = [
    'notfound.extension',
    'sphinx.ext.autodoc',
    'sphinx.ext.intersphinx',
    'sphinx_design',
    'myst_parser',
    'sphinx_api_sidebar',
]

# Add any paths that contain templates here, relative to this directory.
templates_path = ['_templates']

# The suffix of source filenames.
source_suffix = ['.rst', '.md']

# The root toctree document.
root_doc = 'index'

# General information about the project.
copyright = u'{year}, LinkedIn'.format(year=datetime.datetime.today().year)

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
exclude_patterns = ['_build']

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = 'sphinx'

# -- Options for HTML output ---------------------------------------------------

html_title = 'GitHub Pages Documentation'
html_theme = 'furo'
html_static_path = ['_static']

html_sidebars = {
    '**': [
        'sidebar/scroll-start.html',
        'sidebar/brand.html',
        'sidebar/search.html',
        'sidebar/navigation.html',
        'sidebar/api_docs_sidebar.html',
        'sidebar/scroll-end.html',
],
}



# Example configuration for intersphinx: refer to the Python standard library.
intersphinx_mapping = {
    'python': ('http://docs.python.org/', 'https://lerna.tools.corp.linkedin.com/intersphinx/inventory/python/'),
}

api_docs_generators = [
    {
        'command': './gradlew javadocJar',
        'outputs': [
            {
                'name': 'LssNote',
                'path': '../build/LssNote/docs/javadoc',
            },
            {
                'name': 'LssSeatSetting',
                'path': '../build/LssSeatSetting/docs/javadoc',
            },
            {
                'name': 'LssLeadExtendedInfo',
                'path': '../build/LssLeadExtendedInfo/docs/javadoc',
            },
            {
                'name': 'LssColleagues',
                'path': '../build/LssColleagues/docs/javadoc',
            },
            {
                'name': 'LssList',
                'path': '../build/LssList/docs/javadoc',
            },
            {
                'name': 'service_2.11',
                'path': '../build/service_2.11/docs/javadoc',
            },
            {
                'name': 'LssCrmDataValidation',
                'path': '../build/LssCrmDataValidation/docs/javadoc',
            },
            {
                'name': 'LssAlert',
                'path': '../build/LssAlert/docs/javadoc',
            },
            {
                'name': 'LssEntityView',
                'path': '../build/LssEntityView/docs/javadoc',
            },
            {
                'name': 'LssSavedLeadAccount',
                'path': '../build/LssSavedLeadAccount/docs/javadoc',
            },
            {
                'name': 'LssCustomFilterView',
                'path': '../build/LssCustomFilterView/docs/javadoc',
            },
            {
                'name': 'test-fwk',
                'path': '../build/test-fwk/docs/javadoc',
            },
            {
                'name': 'LssCoach',
                'path': '../build/LssCoach/docs/javadoc',
            },
            {
                'name': 'LssBookmark',
                'path': '../build/LssBookmark/docs/javadoc',
            },
            {
                'name': 'client',
                'path': '../build/client/docs/javadoc',
            },
            {
                'name': 'LssBuyer',
                'path': '../build/LssBuyer/docs/javadoc',
            },
            {
                'name': 'factory',
                'path': '../build/factory/docs/javadoc',
            },
            {
                'name': 'LssSharing',
                'path': '../build/LssSharing/docs/javadoc',
            },
            {
                'name': 'ds',
                'path': '../build/ds/docs/javadoc',
            },
            {
                'name': 'LssRecentViews',
                'path': '../build/LssRecentViews/docs/javadoc',
            },
        ],
    },
]
