# Checkly End to End tests

Checkly is an End to End testing tool that allows the running of Playwright tests
from an external location on both cron and CD events.

The e2e-test folder contains the Monitoring as Code configuration on the cron probing, and playwright test files. See [go/checkly](https://go/checkly) for docs.

## Onboarding

- [Getting Access to the Checkly Console](https://go/pages%20checkly-tools/docs/getting-started/getting-access)
- [Writing a Playwright Test](https://go/pages%20checkly-tools/docs/getting-started/writing-checks)

## Testing tests locally

The `checkli` is auto installed on all dev machines, and will handle authentication and scheduling of [Test Sessions](https://www.checklyhq.com/docs/testing/#test-sessions).

Here's a few example uses of `checkli test` to check the test prior to opening a PR.

```bash
# Test every check configured
checkli test

# Regex pattern match a specific test (feedList.spec.js)
# and a specific target colo
checkli test --filter="feedList.*:lva1"

# Run the check outside the default geo-location
# See this page for location names:
#   https://www.checklyhq.com/docs/monitoring/global-locations/
checkli test --filter="feedList.*:lor1" --location=us-west-1
```

## Auto formatting

To apply the prettier ruleset against tests in the e2e-tests dir, simply run the `checkli format` command.

## Using tests for LiX validation

The configured tests can be manually triggered outside the cron window to get insight on how a LiX impacts the test suite. See [go/checkly/lix](https://go/checkly/lix) on validation steps.

## Need Help

If you run into any issues or need help, feel free to reach out to the
E2E Validation team via Slack [#checkly](https://linkedin-randd.slack.com/archives/C077V7PJDB4)
