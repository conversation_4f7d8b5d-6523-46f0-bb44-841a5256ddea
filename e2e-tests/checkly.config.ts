import { defineConfig } from 'checkly';
import { Frequency } from 'checkly/constructs';

const config = defineConfig({
  projectName: 'lss-mt',
  logicalId: '/lss-mt',
  repoUrl: 'https://github.com//lss-mt',
  checks: {
    frequency: Frequency.EVERY_24H,
    checkMatch: 'tests/checks.ts',
    playwrightConfig: {
      // max time a specific test() function can execute for
      timeout: 150 * 1000, // 150 seconds

      // timeout to wait for a query selector to resolve
      expect: { timeout: 10 * 1000 }, // 10 seconds

      use: {
        // timeout to wait between browser actions
        actionTimeout: 10 * 1000, // 10 seconds

        // timeout to wait for page load events
        navigationTimeout: 45 * 1000, // 45 seconds

        // Disables Content-Security-Policy
        bypassCSP: true,

        isMobile: false,

        viewport: { width: 1920, height: 1080 },
      },
    },
  },
  cli: {
    reporters: ['list'],
  },
});

export default config;
