/*
* This is a sample check that demonstrates how to use the MultiStepCheck class.
* This check can be safely deleted.
*/

import axios from 'axios';
import { expect, test } from '@playwright/test';
import { getHTTPSAgent } from './snippets/utils';

const ADMIN_ENDPOINT = "https://1.lss-mt.prod-ltx1.atd.disco.linkedin.com:4429/admin";
const TIMEOUT = 5000;

test('lss-mt-admin-healthcheck', async () => {
    const httpsAgent = getHTTPSAgent(false);
    let response;
    try{
        response = await axios.get(ADMIN_ENDPOINT, { httpsAgent, timeout: TIMEOUT });
    } catch (error) {
        response = error.response;
    }
    expect(response.status).toBeGreaterThanOrEqual(200);
    expect(response.data).toBeTruthy();
})