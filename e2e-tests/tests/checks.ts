/*
This MP has been onboarded to the Checkly E2E test platform using minimal required setup.

To enable standard setup and ability to write BrowserChecks, uncomment the commented code.
Documentation: go/checkly/docs
Support: <EMAIL> or #checkly
*/

// import fs from 'fs';
// import { Region } from 'checkly';
// import { Fabric } from './snippets/constants';

import {
  CheckGroup,
  Frequency,
  RetryStrategyBuilder,
  MultiStepCheck
} from "checkly/constructs";
import * as path from 'path';

// Tags applied to all checks in this group
// const globalCheckTags = new Set(['mp:lss-mt']);

// This is the default group for the MP and only allows running the checks from LinkedIn's internal network.
// Guide: https://urban-potato-yrpk54q.pages.github.io/docs/getting-started/writting-checks/configure-group
const group = new CheckGroup('/lss-mt/private', {
  name: "lss-mt",
  activated: true,
  muted: false,
  frequency: Frequency.EVERY_24H,
  runParallel: false,
  tags: ["mp:lss-mt"],
  retryStrategy: RetryStrategyBuilder.linearStrategy({
    baseBackoffSeconds: 30,
    maxRetries: 1,
    sameRegion: true,
  }),
  privateLocations: ["prod-ltx1-apicheck", "prod-lva1-apicheck", "prod-lor1-apicheck"],
});

// This is a test check to onboard the MP to Checkly.
// This check can be safely removed.
new MultiStepCheck('lss-mt-admin-healthcheck', {
  group,
  name: 'lss-mt-admin-healthcheck',
  frequency: Frequency.EVERY_24H,
  code: {
    entrypoint: path.join(__dirname, 'admin.spec.ts')
  },
  tags: ['admin-check', 'mp:lss-mt'],
});

// const group = new CheckGroup('/lss-mt', {
//   name: 'lss-mt',
//   activated: true,
//   muted: false,
//   locations: ['us-east-1'],
//   frequency: Frequency.EVERY_15M,
//   runParallel: false,
//   tags: ['mp:lss-mt'],
//   retryStrategy: RetryStrategyBuilder.linearStrategy({ baseBackoffSeconds: 30, maxRetries: 1, sameRegion: true }),
// });

// const locationTarget: { [key in Fabric]: keyof Region } = {
//   [Fabric.PROD_LVA1]: 'us-east-1',
//   [Fabric.PROD_LTX1]: 'us-east-2',
//   [Fabric.PROD_LOR1]: 'us-west-1',
//   [Fabric.EI4]: 'us-west-1',
//   [Fabric.EI_LTX1]: 'us-east-1',
// };

// interface TestConfigType {
//   tags: Set<string>;
//   muted: boolean;
//   activated: boolean;
// }

// /* Define the test path and build a list of tests to process
//  * - removed support for .js files, we standardized on TypeScript
//  */
// const testPath = path.join(__dirname);
// const filenames = fs
//   .readdirSync(testPath)
//   .filter((filename: string) => filename.match(/\.spec\.(ts)$/));
// filenames.map(async (filename: string) => {
//   let testConfig: TestConfigType = {
//     tags: new Set(),
//     muted: false,
//     activated: true,
//   };
//   /* eslint-disable @typescript-eslint/no-unused-vars */
//   const [testName, _, extension] = filename.split('.');
//   const importPath = `${testPath}/${testName}.config.${extension}`;
//   /* eslint-enable @typescript-eslint/no-unused-vars */

//   if (fs.existsSync(importPath)) {
//     testConfig = (await import(`${importPath}`)) as TestConfigType;
//   }

//   [Fabric.PROD_LVA1, Fabric.PROD_LTX1, Fabric.PROD_LOR1].map(
//     (fabric: Fabric) => {
//       return new BrowserCheck(`${filename}.${fabric}`, {
//         name: `${filename}:${fabric}`,
//         group,
//        tags: Array.from(
//           new Set<string>([
//             ...globalCheckTags,
//             `fabric:${fabric}`,
//             ...(testConfig.tags ?? [])
//           ]).values()
//         ),
//         muted: testConfig.muted ?? false,
//         activated: testConfig.activated ?? true,
//         locations: [locationTarget[fabric]],
//         environmentVariables: [{ key: 'fabric', value: fabric }],
//         code: {
//           entrypoint: path.join(__dirname, filename),
//         },
//       });
//     }
//   );
// });
