#-------------------------------------------------------------
#For details on recommended settings, see go/gradle.properties
#-------------------------------------------------------------

#long-running Gradle process speeds up local builds
#to stop the daemon run './gradlew --stop'
org.gradle.daemon=true

#configures only relevant projects to speed up the configuration of large projects
#useful when specific project/task is invoked e.g: ./gradlew :cloud:cloud-api:build
org.gradle.configureondemand=true

#Gradle will run tasks from subprojects in parallel
#Higher CPU usage, faster builds
org.gradle.parallel=true

#Gradle 5 reduced the default to 512MB which is not suitable for lss-mt
org.gradle.jvmargs=-Xms8g -Xmx60g -XX:-UseGCOverheadLimit -XX:+HeapDumpOnOutOfMemoryError


#Allows generation of idea/eclipse metadata for a specific subproject and its upstream project dependencies
ide.recursive=true

#Enable thinArchives (Optimizes build and publish times)
#See go/thinarchives for details
thinArchive = true

ligradle.enableScalaCrossBuild=true

# Fix for the gradle build error due to command line exceeding operating system limits, per
# https://linkedin.stackenterprise.co/questions/25882
pegasusPlugin.enableArgFile=true
# Enable the processing of advanced resolution rules coming from linkedin-resolution-rules MP
linkedin.enableAdvancedResolutionRules=true
# Max parallel workers num
org.gradle.workers.max=12
# LI IDEA
ligradle.native.import.enabled=true
# do not down compile to java 8 as JooQ requires java 11
javaBytecodeLevel=11