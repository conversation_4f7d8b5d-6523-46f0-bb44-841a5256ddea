ext.apiMpName = 'lss-mt-api'
ext.apiMpApiDir = 'lss-mt-api'
ext.apiProject = project(':rest-api')

apply plugin: 'li-java'
apply plugin: 'li-urnUtils'

dependencies {

  api spec.external.'spotbugs-annotations'

  api spec.product.'eis-backend-api'.'rest-api-restClient'
  api spec.product.'eis-backend-api'.'rest-api-dataTemplate'
  api spec.product.'eis-backend-api'.'rest-models'
  api spec.product.'eis-backend-api'.'rest-models-dataTemplate'
  api spec.product.'entitlements-api'.'rest-client-api-restClient'

  api spec.product.'handles-midtier-api'.'handles-midtier-api-restClient'
  api spec.product.'handles-midtier-api'.'handles-midtier-api-dataTemplate'

  api spec.product.'subs-api'.'rest-client-api'
  api spec.product.'cloud-session-api'.'cloud-network-api-restClient'
  api spec.product.'cloud-session-api'.'cloud-network-api-dataTemplate'
  api spec.product.flock.'flock-api'
  api spec.product.flock.'flock-api-dataTemplate'
  api (spec.product.'lsi-common'.'api-restClient') {
    exclude group: 'org.apache.avro'
  }
  api spec.product.'lss-mt-api'.'lss-mt-api-dataTemplate'
  api spec.product.'lighthouse-bps-api'.'rest-api-restClient'
  api spec.external.'restli-client-parseq'
  api spec.product.'resource-identity'.'gradle-urn-util'
  urnModels spec.product.models.models
  urnModelsCompile spec.product.models.'models-dataTemplate'
  api spec.external.'restli-common'
  api spec.product.money.'money-shared'
  api spec.product.identity.'profile-rest-api-restClient'
  api spec.product.'crm-api'.'crm-backend-api-restClient'
  api spec.product.'crm-api'.'crm-backend-api-dataTemplate'
  api spec.product.'third-party-authentication'.'third-party-authorizedscope-client'
  api spec.product.'dev-apps-mgmt-mt'.'dev-apps-mgmt-mt-api-dataTemplate'
  api spec.product.'dev-apps-mgmt-mt'.'dev-apps-mgmt-mt-api-client'
  api spec.product.'login-server-api'.'oauth2-client-restClient'
  api spec.product.ucf.'ucf-client-api'
  api spec.product.ucf.'ucf-client-factory'
  api spec.product.ucf.'ucf-viewerinfo-util-factory'
  api spec.product.'lss-admin-backend'.'lss-admin-backend-api-restClient'
  api spec.product.'lss-growth-samza'.'venice-avro-schema'
  api spec.product.'venice-thin-client'.'venice-thin-client'
  api spec.product.'realtime-dispatcher-api'.'realtime-dispatcher-api-restClient'
  api spec.product.'lss-connect-mt'.'api-restClient'
  api spec.product.'application-infrastructure-common'.'application-testing'
  api spec.product.'application-infrastructure-common'.'application-utilities'
  api spec.product.'lss-reporting'.'lss-reporting-grpc-api-protoImplementation'
  api spec.product.'lss-reporting'.'lss-reporting-grpc-api-proto-data-model'
  api spec.external.'parseq-guava-interop'

  // Used for upsells
  api spec.product.'subs-mt'.'subs-mt-api-restClient'

  testImplementation spec.external.testng
  testImplementation spec.external.'mockito-inline'
  testImplementation spec.external.'restli-client-testutils'
  testImplementation project(':test:test-fwk')

}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.memoryMaximumSize = '4g'
}
