package com.linkedin.sales.client;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.crm.Channel;
import com.linkedin.crm.CrmDerivedMappingsRequestBuilders;
import com.linkedin.crm.CrmLinkedInEntityMapping;
import com.linkedin.crm.CrmLinkedInEntityMappingKey;
import com.linkedin.crm.CrmLinkedInEntityMappingsBatchGetRequestBuilder;
import com.linkedin.crm.CrmLinkedInEntityMappingsRequestBuilders;
import com.linkedin.crm.DerivedMapping;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class CrmMappingClient {
  private static final Logger LOG = LoggerFactory.getLogger(CrmMappingClient.class);
  private static final CrmLinkedInEntityMappingsRequestBuilders CRM_LINKED_IN_ENTITY_MAPPINGS_REQUEST_BUILDERS =
      new CrmLinkedInEntityMappingsRequestBuilders();
  private static final CrmDerivedMappingsRequestBuilders CRM_DERIVED_MAPPINGS_REQUEST_BUILDERS = new CrmDerivedMappingsRequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public CrmMappingClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Batch get the best entity match results for the given crm entities
   * @param contractUrn
   * @param crmInstanceUrn
   * @param crmEntityIds
   * @return
   */
  public Task<Map<String, CrmLinkedInEntityMapping>> batchGetMappingsByCrmEntity(@NonNull ContractUrn contractUrn,
      @NonNull CrmInstanceUrn crmInstanceUrn, @NonNull Set<String> crmEntityIds) {
    Set<ComplexResourceKey<CrmLinkedInEntityMappingKey, EmptyRecord>> keys = crmEntityIds.stream()
        .map(crmEntityId -> new ComplexResourceKey<>(new CrmLinkedInEntityMappingKey().setContract(contractUrn)
            .setCrmEntityId(crmEntityId)
            .setCrmInstance(crmInstanceUrn), new EmptyRecord()))
        .collect(Collectors.toSet());
    CrmLinkedInEntityMappingsBatchGetRequestBuilder builder =
        CRM_LINKED_IN_ENTITY_MAPPINGS_REQUEST_BUILDERS.batchGet().ids(keys);

    return _parSeqRestClient.createTask(builder.build())
        .map(response -> response.getEntity()
            .getResults()
            .entrySet()
            .stream()
            .filter(entry -> !entry.getValue().hasError())
            .collect(Collectors.toMap(entry -> entry.getKey().getKey().getCrmEntityId(),
                entry -> entry.getValue().getEntity())));
  }

  /**
   * Find Derived Mapping By Crm EntityId such as lead id, contact id or account id)
   * @param contractUrn
   * @param crmInstance
   * @param channel
   * @param crmEntityId This is raw crm id, such as "0016000000m2S1eAAE" from SFDC or "bf4d718b-38e4-e711-a82a-000d3a37c0db" from Dynamics
   * @return
   */
  @Deprecated
  public Task<List<DerivedMapping>> findDerivedMappingByCrmEntityId(@NonNull ContractUrn contractUrn, @NonNull CrmInstanceUrn crmInstance,
      @NonNull Channel channel, @NonNull String crmEntityId) {
    FindRequest<DerivedMapping> request = CRM_DERIVED_MAPPINGS_REQUEST_BUILDERS.findByCrmEntityId()
        .contractParam(contractUrn)
        .crmInstanceParam(crmInstance)
        .channelParam(channel)
        .crmEntityIdParam(crmEntityId)
        .build();
    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity)
        .map(CollectionResponse::getElements)
        .onFailure(t -> LOG.error("Failed to findDerivedMappingByCrmEntityId contractUrn {} crmInstance {} crmEntityId {}",
            contractUrn, crmInstance, crmEntityId, t));
  }
}
