package com.linkedin.sales.client;

import com.linkedin.crm.CrmUserMapping;
import com.linkedin.crm.CrmUserMappingKey;
import com.linkedin.crm.CrmUserMappingsRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchGetEntityRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * Client to get for CrmUserMapping.
 */
public class CrmUserMappingClient {
  private static final CrmUserMappingsRequestBuilders CRM_USER_MAPPINGS_REQUEST_BUILDERS =
      new CrmUserMappingsRequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public CrmUserMappingClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get Crm User Mappings by {@code crmUserMappingKeys}.
   *
   * @param crmUserMappingKeys keys to get mappings for.
   */
  public Task<Map<CrmUserMappingKey, CrmUserMapping>> getCrmUserMappings(
      @NonNull Collection<CrmUserMappingKey> crmUserMappingKeys) {

    BatchGetEntityRequest<ComplexResourceKey<CrmUserMappingKey, EmptyRecord>, CrmUserMapping> request =
        CRM_USER_MAPPINGS_REQUEST_BUILDERS.batchGet()
            .ids(crmUserMappingKeys.stream()
                .map(crmUserMappingKey -> new ComplexResourceKey<>(crmUserMappingKey, new EmptyRecord()))
                .collect(Collectors.toList()))
            .build();

    return _parSeqRestClient.createTask(request)
        .map(response -> response.getEntity()
            .getResults()
            .entrySet()
            .stream()
            .filter(entry -> !entry.getValue().hasError())
            .collect(Collectors.toMap(entry -> entry.getKey().getKey(),
                entry -> entry.getValue().getEntity())));
  }
}
