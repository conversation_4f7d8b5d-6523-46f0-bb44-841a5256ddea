package com.linkedin.sales.client;

import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.handle.EmailAddress;
import com.linkedin.handlesmidtier.ClientAwareEmailAddressesRequestBuilders;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchCreateIdRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.common.CreateIdStatus;
import com.linkedin.restli.common.HttpStatus;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Client to handles-midtier resources: ClientAwareEmailAddresses.
 */
public class EmailAddressClient {
  private static final Logger LOG = LoggerFactory.getLogger(EmailAddressClient.class);
  private static final ClientAwareEmailAddressesRequestBuilders CLIENT_AWARE_EMAIL_ADDRESSES_REQUEST_BUILDERS =
      new ClientAwareEmailAddressesRequestBuilders();
  private final ParSeqRestClient _parSeqRestClient;
  private final Engine _engine;

  public EmailAddressClient(ParSeqRestClient parSeqRestClient, Engine engine) {
    this._engine = engine;
    this._parSeqRestClient = parSeqRestClient;
  }

 /**
   * Task to find or create EmailAddress based on email string.
   * The result status code will be:
   *  201 : If created
   *  409 : If it already exists
   */
  public Task<List<CreateIdStatus<Long>>> batchCreate(List<String> emailStrings) {
    if (CollectionUtils.isEmpty(emailStrings)) {
      return Task.value(Collections.emptyList());
    }

    List<EmailAddress> emailAddresses = emailStrings.stream()
        .map(emailString -> new EmailAddress().setEmailAddress(emailString))
        .collect(Collectors.toList());

    BatchCreateIdRequest<Long, EmailAddress> batchCreateIdRequest = CLIENT_AWARE_EMAIL_ADDRESSES_REQUEST_BUILDERS
        .batchCreate()
        .inputs(emailAddresses)
        .build();

    return _parSeqRestClient.createTask(batchCreateIdRequest)
        .map(response -> response.getEntity().getElements())
        .onFailure(t -> LOG.error("Cannot find email IDs: {}", emailAddresses, t));
  }

  /**
   * Map the email address strings to email address URNs in handles mid-tier.
   * If email address urn can't be found, no mapping will be returned.
   *
   * @param emailStrings email address string array
   * @return A map from email string to Email Address URN, or null if not found.
   */
  public Task<Map<String, CreateOrGetResult>> batchCreateOrGetEmailAddressUrns(List<String> emailStrings) {
    return batchCreate(emailStrings).map(batchCreateIdResponse -> {
      Map<String, CreateOrGetResult> createOrGetResults = new HashMap<>();

      // either we trust the batchCreateIdResponse sequence, or we send another batchGet to form a Map<Email, EmailAddressUrn>
      IntStream.range(0, batchCreateIdResponse.size()).forEach(i -> {
        CreateIdStatus<Long> createIdStatus = batchCreateIdResponse.get(i);
        String emailString = emailStrings.get(i);
        CreateOrGetResult result = new CreateOrGetResult(createIdStatus);
        createOrGetResults.put(emailString, result);
      });

      return createOrGetResults;

    });
  }

  public static class CreateOrGetResult {
    private final CreateIdStatus<Long> _status;
    public CreateOrGetResult(CreateIdStatus<Long> status) {
      _status = status;
    }

    @Nullable
    public EmailAddressUrn getEmailAddressUrnIfAny() {
      if (_status.getStatus() == HttpStatus.S_201_CREATED.getCode()
          || _status.getStatus() == HttpStatus.S_409_CONFLICT.getCode()) {
        return new EmailAddressUrn(_status.getKey());
      } else {
        return null;
      }
    }

    public CreateIdStatus<Long> getStatus() {
      return _status;
    }

    @Override
    public String toString() {
      return "CreateOrGetResult{" + "_status=" + _status + '}';
    }
  }

}
