package com.linkedin.sales.client;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CorporateMemberUrn;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.email.internal.CorporateMemberIdentity;
import com.linkedin.email.internal.Email;
import com.linkedin.email.internal.EmailSenderRequestBuilders;
import com.linkedin.email.internal.EnterpriseIdentity;
import com.linkedin.email.internal.GuestIdentity;
import com.linkedin.email.internal.Identity;
import com.linkedin.email.internal.MemberIdentity;
import com.linkedin.email.internal.Recipient;
import com.linkedin.email.internal.Sender;
import com.linkedin.handle.EmailAddress;
import com.linkedin.handlesmidtier.ClientAwareEmailAddressesGetRequestBuilder;
import com.linkedin.handlesmidtier.ClientAwareEmailAddressesRequestBuilders;
import com.linkedin.handlesmidtier.SecurityChecks;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.CreateIdRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * An email client designed to send email in Sales navigator
 * This email client are for welcome/invitation types, users are not able to set email settings for these email types
 * So no need to check user settings for email preferences for now, but be cautious when adding new types
 */
public class EmailClient {
  private static final Logger LOG = LoggerFactory.getLogger(EmailClient.class);
  private static final PathSpec[] EMAIL_FIELDS = new PathSpec[]{
      EmailAddress.fields().id(),
      EmailAddress.fields().emailAddress()
  };
  /**
   *  in Sales Navigator, a seated recipient is a legit seat owner
   *
   */
  public static class SeatedRecipient {
    private final MemberUrn memberUrn;
    private final ContractUrn contractUrn;
    private final SeatUrn seatUrn;
    //the address sent to this SeatedRecipient before, optional
    private final String provisionedEmailAddress;
    private final EnterpriseProfileUrn enterpriseProfileUrn;

    public SeatedRecipient(MemberUrn memberUrn, ContractUrn contractUrn, SeatUrn seatUrn,
        String provisionedEmailAddress) {
      this.memberUrn = memberUrn;
      this.contractUrn = contractUrn;
      this.seatUrn = seatUrn;
      this.provisionedEmailAddress = provisionedEmailAddress;
      this.enterpriseProfileUrn = null;
    }

    public SeatedRecipient(EnterpriseProfileUrn enterpriseProfileUrn) {
      this.enterpriseProfileUrn = enterpriseProfileUrn;
      this.memberUrn = null;
      this.contractUrn = null;
      this.seatUrn = null;
      this.provisionedEmailAddress = null;
    }

    @Override
    public String toString() {
      if (enterpriseProfileUrn == null) {
        return "SeatedRecipient{" + "memberUrn=" + memberUrn + ", contractUrn=" + contractUrn + ", seatUrn=" + seatUrn
            + ", provisionedEmailAddress=" + provisionedEmailAddress + '}';
      } else {
        return "SeatedRecipient{enterpriseProfileUrn=" + enterpriseProfileUrn + "}";
      }
    }

    Identity getIdentity() {
      if (enterpriseProfileUrn == null) {
        MemberIdentity recipientMemberIdentity = new MemberIdentity().setMember(memberUrn);
        if (provisionedEmailAddress != null) {
          recipientMemberIdentity.setEmailAddress(provisionedEmailAddress);
        }
        return Identity.create(recipientMemberIdentity);
      } else {
        EnterpriseIdentity enterpriseIdentity = new EnterpriseIdentity().setEnterpriseProfile(enterpriseProfileUrn);
        return Identity.create(enterpriseIdentity);
      }
    }
  }

  private final ParSeqRestClient _parSeqRestClient;
  /**
   * @param parSeqRestClient for sending Restli request to email service
   */
  public EmailClient(ParSeqRestClient parSeqRestClient) {
    this._parSeqRestClient = parSeqRestClient;
  }

  /**
   * send email by given address
   *
   *
   * @param emailContext contains all necessary info to build email body
   * @param emailAddress the email address, no validation here, please make sure the email is valid
   *
   * @return email result
   */
  public Task<EmailResult> sendEmail(
      EmailContext emailContext,
      String emailAddress) {

    Identity identity = Identity.create(new GuestIdentity().setEmailAddress(emailAddress));
    Email emailObj = makeEmail(identity, emailContext);
    return sendEmail(emailObj, 0);
  }

  /**
   * send email by given {@link EmailAddressUrn}
   * @param emailContext contains all necessary info(Key - value pairs along with template key) to build email body
   * @param emailAddressUrn the emailAddressUrn linkedin's {@link EmailAddressUrn} which pointed to a designated email address
   * @return
   */
  public Task<EmailResult> sendEmail(
      EmailContext emailContext,
      EmailAddressUrn emailAddressUrn,
      Urn viewerUrn) {
    return getEmail(emailAddressUrn, viewerUrn)
        .flatMap(emailAddress -> sendEmail(emailContext, emailAddress));
  }

  private Task<String> getEmail(EmailAddressUrn emailAddressUrn, Urn viewerUrn) {
    ClientAwareEmailAddressesGetRequestBuilder builder = new ClientAwareEmailAddressesRequestBuilders().get()
        .id(emailAddressUrn.getEmailAddressIdEntity())
        .addBypassSecurityChecksParam(SecurityChecks.ALL)
        .viewerParam(viewerUrn)
        .fields(EMAIL_FIELDS);

    GetRequest<EmailAddress> request = builder.build();
    return _parSeqRestClient.createTask(request)
        .map(response -> response.getEntity().getEmailAddress())
        .onFailure(t -> {
          LOG.error("Cannot get EmailAddress by urn: {}", emailAddressUrn, t);
        });
  }


  /**
   * send email to a seat owner
   *
   * @param emailContext contains all necessary info(Key - value pairs along with template key) to build email body
   * @param seatedRecipient a recipient who already has a seat
   * @return email result
   */
  public Task<EmailResult> sendEmail(
      EmailContext emailContext,
      SeatedRecipient seatedRecipient) {
    Email emailObj = makeEmail(seatedRecipient.getIdentity(), emailContext);
    return sendEmail(emailObj, 0);
  }


  /**
   * Originated from lighthouse-frontend/services.clients.email.EmailClientComponent/DefaultEmailClient::makeEmail
   */
  private Email makeEmail(Identity recipientIdentity, EmailContext emailContext) {
    Recipient recipient = new Recipient()
        .setIdentity(recipientIdentity);

    Email email = new Email()
        .setRecipient(recipient)
        .setEmailKey(emailContext.getTemplateKey())
        .setData(emailContext.getBodyMetaInfo());

    if (emailContext.hasSenderUrn()) {
      getSenderIdentity(emailContext.getSenderUrn()).ifPresent(
          senderId -> email.setSender(new Sender().setIdentity(senderId)));
    }

    return email;
  }


  //FIXME need to figure out a way to prevent Jar pollution from lighthouse-bps, otherwise EnterpriseIdentity may never works
  private static Optional<Identity> getSenderIdentity(Urn urn) {
    String entityType = urn.getEntityType();
    if (EnterpriseProfileUrn.ENTITY_TYPE.equals(entityType)) {
      try {
        return Optional.of(Identity.create(new EnterpriseIdentity().setEnterpriseProfile(urn)));
      } catch (NoSuchMethodError error) {
        LOG.warn("cannot generate EnterpriseIdentity for:{}, "
            + "because current env is using stork-data-data-template-*.jar, not flock-api-data-template-*.jar! ",
            urn, error);
        return Optional.empty();
      }
    } else if (CorporateMemberUrn.ENTITY_TYPE.equals(entityType)) {
      return Optional.of(Identity.create(new CorporateMemberIdentity().setCorpMember(urn)));
    } else if (MemberUrn.ENTITY_TYPE.equals(entityType)) {
      return Optional.of(Identity.create(new MemberIdentity().setMember(urn)));
    } else {
      LOG.warn("ignore senderUrn because it's type is unsupported:{}, ", urn);
      return Optional.empty();
    }
  }


  private Task<EmailResult> sendEmail(Email emailObj, int sendCount) {
    LOG.info("send out email: {}, faileoverCount: {}", emailObj, sendCount);
    CreateIdRequest<String, Email> emailRequest = new EmailSenderRequestBuilders().create()
        .kafkaParam(Boolean.TRUE)
        .input(emailObj)
        .build();
    return _parSeqRestClient.createTask(emailRequest)
        .map(resp -> EmailResult.byResponse(emailObj, resp))
        .recover(ex -> EmailResult.bySendInvocationError(emailObj, ex, sendCount));
  }

}
