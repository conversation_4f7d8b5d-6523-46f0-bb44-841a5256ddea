package com.linkedin.sales.client;

import com.linkedin.common.any.AnyRecord;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.DataMap;
import com.linkedin.sales.admin.SeatRole;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
public class EmailContext {

  // TEMPATE_KEY below are migrated from lighthouse-frontend/seatProvisioningService.scala
  public static final String TEMPLATE_LSS_SEAT_INVITE_ACCEPTED = "lss_seat_invite_accepted";
  public static final String TEMPLATE_LSS_INVITE_EMAIL_V2 = "lss_invite_email_v2";
  public static final String TEMPLATE_EMAIL_LSS_INVITE_TLE = "email_lss_invite_tle";
  public static final String TEMPLATE_LSS_WELCOME = "lss_welcome";
  public static final String TEMPLATE_EMAIL_LSS_WELCOME_TLE = "email_lss_welcome_tle";

  private final AnyRecord bodyMetaInfo;
  private final String templateKey;
  private final Urn senderUrn;

  /**
   *
   * @param templateKey the key to allocate template for generating email body
   * @param bodyMetaInfo the body context paired with template Key to generate email body
   * @param senderUrn Sender of this email, optional
   */
  protected EmailContext(String templateKey, AnyRecord bodyMetaInfo, @Nullable Urn senderUrn) {
    this.templateKey = templateKey;
    this.senderUrn = senderUrn;
    this.bodyMetaInfo = bodyMetaInfo;
  }

  public String getTemplateKey() {
    return templateKey;
  }

  public Urn getSenderUrn() {
    return senderUrn;
  }

  public boolean hasSenderUrn() {
    return senderUrn != null;
  }

  public AnyRecord getBodyMetaInfo() {
    return bodyMetaInfo;
  }




  public static TLEInviteEmailBuilder buildTLEInviteEmail() {
    return new TLEInviteEmailBuilder();
  }

  public static TLEWelcomeEmailBuilder buildTLEWelcomeEmail() {
    return new TLEWelcomeEmailBuilder();
  }

  public static InviteV2EmailBuilder buildInviteV2Email() {
    return new InviteV2EmailBuilder();
  }

  public static WelcomeEmailBuilder buildWelcomeEmail() {
    return new WelcomeEmailBuilder();
  }


  /**
   * for building an invitation email to a none LinkedIn member which account is linked to a normal
   * Sales Navigator contract
   */
  public static class InviteV2EmailBuilder extends Builder<InviteV2EmailBuilder> {

    private static final String KEY_ACCOUNT_NAME = "accountName";
    private static final String KEY_URL = "url";
    private static final String KEY_IS_CONTRACT_ONLINE = "isContractOnline";
    private static final String KEY_COMPANY = "company";

    public InviteV2EmailBuilder() {
      super(KEY_ACCOUNT_NAME, KEY_URL, KEY_IS_CONTRACT_ONLINE);
    }

    public InviteV2EmailBuilder setAccountName(String accountName) {
      set(KEY_ACCOUNT_NAME, accountName);
      return this;
    }

    public InviteV2EmailBuilder setContractOnline(boolean isContractOnline) {
      set(KEY_IS_CONTRACT_ONLINE, isContractOnline);
      return this;
    }

    public InviteV2EmailBuilder setUrl(String url) {
      set(KEY_URL, url);
      return this;
    }

    public InviteV2EmailBuilder setCompany(Urn company) {
      set(KEY_COMPANY, company);
      return this;
    }

    @Override
    protected String getTemplateKey() {
      return TEMPLATE_LSS_INVITE_EMAIL_V2;
    }
  }

  /**
   * for building an invitation email to a none LinkedIn member which account is linked to a
   * Sales Navigator - TeamLink contract
   */
  public static class TLEInviteEmailBuilder extends Builder<TLEInviteEmailBuilder> {
    private static final String KEY_ACCOUNT_NAME = "accountName";
    private static final String KEY_REGISTER_URL = "registerUrl";

    public TLEInviteEmailBuilder() {
      super(KEY_ACCOUNT_NAME);
    }

    public TLEInviteEmailBuilder setAccountName(String accountName) {
      set(KEY_ACCOUNT_NAME, accountName);
      return this;
    }

    public TLEInviteEmailBuilder setRegisterUrl(String registerUrl) {
      set(KEY_REGISTER_URL, registerUrl);
      return this;
    }

    @Override
    protected String getTemplateKey() {
      return TEMPLATE_EMAIL_LSS_INVITE_TLE;
    }
  }

  /**
   *
   */
  public static class WelcomeEmailBuilder extends Builder<WelcomeEmailBuilder> {
    private static final String KEY_MEMBER_ID = "memberId";
    private static final String KEY_SEAT_TIER = "seatTier";
    private static final String KEY_IS_ONLINE = "isOnline";
    private static final String KEY_COMPANY = "company";

    public WelcomeEmailBuilder() {
      super(KEY_MEMBER_ID, KEY_SEAT_TIER, KEY_IS_ONLINE);
    }

    public WelcomeEmailBuilder setMemberId(long memberId) {
      set(KEY_MEMBER_ID, memberId);
      return this;
    }
    public WelcomeEmailBuilder setSeatTier(SeatRole seatTier) {
      set(KEY_SEAT_TIER, seatTier.name());
      return this;
    }

    public WelcomeEmailBuilder setContractOnline(boolean isContractOnline) {
      set(KEY_IS_ONLINE, isContractOnline);
      return this;
    }

    public WelcomeEmailBuilder setCompany(Urn company) {
      set(KEY_COMPANY, company);
      return this;
    }

    @Override
    protected String getTemplateKey() {
      return TEMPLATE_LSS_WELCOME;
    }
  }

  /**
   * for building a welcome email to a TeamLink contract Seat
   */
  public static class TLEWelcomeEmailBuilder extends Builder<TLEWelcomeEmailBuilder> {
    private static final String KEY_ACCOUNT_NAME = "accountName";
    private static final String KEY_MEMBER_ID = "memberId";
    private static final String KEY_SEAT_ID = "seatId";
    private static final String KEY_CONTRACT_ID = "contractId";
    private static final String KEY_ACTIVATE_URL = "activateUrl";

    public TLEWelcomeEmailBuilder() {
      super(KEY_ACCOUNT_NAME, KEY_MEMBER_ID, KEY_SEAT_ID, KEY_CONTRACT_ID, KEY_ACTIVATE_URL);
    }

    public TLEWelcomeEmailBuilder setAccountName(String accountName) {
      set(KEY_ACCOUNT_NAME, accountName);
      return this;
    }
    public TLEWelcomeEmailBuilder setMemberId(long memberId) {
      set(KEY_MEMBER_ID, memberId);
      return this;
    }
    public TLEWelcomeEmailBuilder setSeatId(long seatId) {
      set(KEY_SEAT_ID, seatId);
      return this;
    }
    public TLEWelcomeEmailBuilder setContractId(long contractId) {
      set(KEY_CONTRACT_ID, contractId);
      return this;
    }
    public TLEWelcomeEmailBuilder setActivateUrl(String activateUrl) {
      set(KEY_ACTIVATE_URL, activateUrl);
      return this;
    }
    @Override
    protected String getTemplateKey() {
      return TEMPLATE_EMAIL_LSS_WELCOME_TLE;
    }
  }

  static abstract class Builder<T extends Builder> {
    private final DataMap data;
    private final String[] mustExistFields;

    Builder(String... mustExistFields) {
      this.data = new DataMap();
      this.mustExistFields = mustExistFields;
    }

    protected abstract String getTemplateKey();

    /**
     * check if data contains all fields
     * @throws IllegalArgumentException if fields not all contained
     */
    protected void validateFieldsExists() {
      List<String> requiredButNotSetList = Arrays.stream(mustExistFields)
          .filter(key -> !data.containsKey(key))
          .collect(Collectors.toList());
      if (!requiredButNotSetList.isEmpty()) {
        throw new IllegalArgumentException("not all required fields have value: " + requiredButNotSetList);
      }
    }

    protected void set(String key, Object value) {
      Optional.ofNullable(value)
          .map(Object::toString)
          .filter(str -> !str.isEmpty())
          .ifPresent(str ->  data.put(key, str));
    }

    protected void set(String key, int value) {
      data.put(key, value);
    }

    protected void set(String key, long value) {
      data.put(key, value);
    }

    protected void set(String key, boolean value) {
      data.put(key, value);
    }

    public EmailContext build(@Nullable Urn senderUrn) {
      validateFieldsExists();
      DataMap map = new DataMap();
      map.put("data", data);
      AnyRecord body = new AnyRecord(map);
      return new EmailContext(getTemplateKey(), body, senderUrn);
    }
  }
}





