package com.linkedin.sales.client;

import com.linkedin.email.internal.Email;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.IdResponse;


/**
 * represent any outcome could result in EmailClient::sendEmail
 */
public final class EmailResult {


  /**
   *
   * @param emailObj
   * @param reasonMesg the reason why EmailClient didn't send the email
   * @return
   */
  public static EmailResult notSent(Email emailObj, String reasonMesg) {
    return new EmailResult(emailObj, null, reasonMesg,
        HttpStatus.S_200_OK.getCode(), null, null, 0);
  }

  /**
   * any internal error during email composition.
   * create a EmailResult with email object and Error to let caller of EmailClient decide how to handle(resend or what)
   * @param emailObj the emailObject, contains every info, good to resend
   * @param e the error
   * @param seatedRecipient
   * @param sendCount
   * @return EmailResult contains error and emailObject
   */
  public static EmailResult byInternalError(Email emailObj, Throwable e, EmailClient.SeatedRecipient seatedRecipient, int sendCount) {
    return new EmailResult(emailObj, null, "error occur while composing email",
        HttpStatus.S_500_INTERNAL_SERVER_ERROR.getCode(), e, seatedRecipient, sendCount);
  }

  /**
   * create a EmailResult with email object and Error to let caller of EmailClient decide how to handle(resend or what)
   * @param emailObj the emailObject, contains every info, good to resend
   * @param e the error
   * @param sendCount
   * @return EmailResult contains error and emailObject
   */
  public static EmailResult bySendInvocationError(Email emailObj, Throwable e, int sendCount) {
    return new EmailResult(emailObj, null, "error occur while send invocation",
        HttpStatus.S_500_INTERNAL_SERVER_ERROR.getCode(), e, null, sendCount);
  }


  private final Email emailObj;
  private final String emailResponseId;
  private final String message;
  private final int httpStatus;
  private final Throwable error;
  private final EmailClient.SeatedRecipient seatedRecipient;
  private final int sendCount;

  private EmailResult(Email emailObj, String emailResponseId, String message, int httpStatus, Throwable error,
      EmailClient.SeatedRecipient seatedRecipient, int sendCount) {
    this.emailObj = emailObj;
    this.emailResponseId = emailResponseId;
    this.message = message;
    this.httpStatus = httpStatus;
    this.error = error;
    this.seatedRecipient = seatedRecipient;
    this.sendCount = sendCount;
  }

  public EmailClient.SeatedRecipient getSeatedRecipient() {
    return seatedRecipient;
  }

  public boolean hasSeatedRecipient() {
    return seatedRecipient != null;
  }

  public int getSendCount() {
    return sendCount;
  }

  public Email getEmailObj() {
    return emailObj;
  }

  public String getEmailResponseId() {
    return emailResponseId;
  }

  public String getMessage() {
    return message;
  }

  public int getHttpStatus() {
    return httpStatus;
  }

  public boolean hasError() {
    return error != null;
  }

  public Throwable getError() {
    return error;
  }

  @Override
  public String toString() {
    return "EmailResult{" + "emailObj=" + emailObj + ", emailResponseId='" + emailResponseId + '\'' + ", message='"
        + message + '\'' + ", httpStatus=" + httpStatus + ", error=" + error + '}';
  }


  //TODO resend mechanism is not done yet

  public static EmailResult byResponse(Email emailObj, Response<IdResponse<String>> response) {

    String message = "response status:" + response.getStatus();
    Throwable error = null;
    String emailResponseId = null;
    if (response.hasError()) {
      message += ", error:" + response.getError();
      error = response.getError();
    } else {
      emailResponseId = response.getEntity().getId();
    }
    return new EmailResult(emailObj, emailResponseId, message, response.getStatus(), error, null, 0);
  }


//TODO old implementation on LHFE, in case newer version not working
//  public static EmailResult byResponse(Email emailObj, Response<EmptyRecord> response) {
//
//    String message = "response status:" + response.getStatus();
//    Throwable error = null;
//    String emailResponseId = null;
//    if (response.hasError()) {
//      message += ", error:" + response.getError();
//      error = response.getError();
//    } else {
//      emailResponseId = response.getId();
//    }
//    return new EmailResult(emailObj, emailResponseId, message, response.getStatus(), error);
//  }
}
