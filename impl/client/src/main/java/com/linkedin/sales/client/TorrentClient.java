package com.linkedin.sales.client;

import com.linkedin.data.template.LongArray;
import com.linkedin.globalkeygen.TorrentRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ActionRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * class for Torrent Client. Used for generating global unique Id.
 * <AUTHOR>
 */
public class TorrentClient {
  private static final Logger LOG = LoggerFactory.getLogger(TorrentClient.class);
  private final ParSeqRestClient _parSeqRestClient;
  private static final TorrentRequestBuilders TORRENT_REQUEST_BUILDERS = new TorrentRequestBuilders();

  public TorrentClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * generate global unique Id
   *
   * @return Task with generated Id
   */
  public Task<Long> generateNextId() {
    ActionRequest<Long> request = TORRENT_REQUEST_BUILDERS.actionNext().build();
    return _parSeqRestClient.createTask(request).map(Response::getEntity)
        .onFailure(t -> LOG.error("Error in getting next torrent id.", t));
  }

  public Task<LongArray> generateNextIds(int count) {
    ActionRequest<LongArray> request = TORRENT_REQUEST_BUILDERS.actionBatch().countParam(count).build();
    return _parSeqRestClient.createTask(request).map(Response::getEntity)
        .onFailure(t -> LOG.error("Error in getting next torrent ids.", t));
  }
}
