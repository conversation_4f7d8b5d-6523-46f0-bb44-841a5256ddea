package com.linkedin.sales.client.analytics;

import com.linkedin.grpc.d2.D2ManagedChannelProvider;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.guava.ListenableFutureUtil;
import com.linkedin.util.concurrent.Memoizer;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.concurrent.ExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.ContractUrn;
import proto.com.linkedin.common.MemberUrn;
import proto.com.linkedin.sales.report.SalesAnalyticsExportDataAvailability;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJob;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJobType;
import proto.com.linkedin.sales.resources.report.RetrieveSalesAnalyticsDataAvailabilityRequest;
import proto.com.linkedin.sales.resources.report.RetrieveSalesAnalyticsDataAvailabilityResponse;
import proto.com.linkedin.sales.resources.report.RetrieveSalesAnalyticsExportJobRequest;
import proto.com.linkedin.sales.resources.report.RetrieveSalesAnalyticsExportJobResponse;
import proto.com.linkedin.sales.resources.report.SalesAnalyticsExportJobsApiGrpc;
import proto.com.linkedin.sales.resources.report.SubmitSalesAnalyticsExportJobRequest;
import proto.com.linkedin.sales.resources.report.SubmitSalesAnalyticsExportJobResponse;

// Suppressing warning for grpc generated proto classes
@SuppressFBWarnings("SSCU_SUSPICIOUS_SHADED_CLASS_USE")
public class SalesAnalyticsExportJobsApiClient {

  private static final Logger LOG = LoggerFactory.getLogger(SalesAnalyticsExportJobsApiClient.class);
  private static final String SALES_ANALYTICS_EXPORT_JOBS_API_NAME = "salesAnalyticsExportJobsApi";

  private final Memoizer<SalesAnalyticsExportJobsApiGrpc.SalesAnalyticsExportJobsApiFutureStub>
      salesAnalyticsExportJobsMemoizer;

  public SalesAnalyticsExportJobsApiClient(D2ManagedChannelProvider d2ManagedChannelProvider) {
    this.salesAnalyticsExportJobsMemoizer = Memoizer.create(() -> SalesAnalyticsExportJobsApiGrpc.newFutureStub(
        d2ManagedChannelProvider.forServiceName(SALES_ANALYTICS_EXPORT_JOBS_API_NAME)
    ));
  }

  /**
   * Retrieves the job details for the give jobId
   *
   * @param jobId jobId associated with the report
   * @param contractUrn contractUrn of the user requesting the report
   * @param requesterMemberUrn memberUrn of the user requesting the report
   * @return SalesAnalyticsExportJob containing the job details
   */
  public Task<SalesAnalyticsExportJob> retrieveJobDetails(long jobId, com.linkedin.common.urn.ContractUrn contractUrn,
      com.linkedin.common.urn.MemberUrn requesterMemberUrn) throws ExecutionException {

    ContractUrn protoContractUrn = ContractUrn.newBuilder().setContractId(contractUrn.getIdAsLong()).build();
    MemberUrn protoMemberUrn = MemberUrn.newBuilder().setMemberId(requesterMemberUrn.getIdAsLong()).build();

    RetrieveSalesAnalyticsExportJobRequest.Builder requestBuilder = RetrieveSalesAnalyticsExportJobRequest.newBuilder()
        .setJobId(jobId)
        .setContract(protoContractUrn)
        .setViewerMemberUrn(protoMemberUrn);
    
    return ListenableFutureUtil.fromListenableFuture(
        salesAnalyticsExportJobsMemoizer.get().retrieveJobDetails(requestBuilder.build())).map(
        RetrieveSalesAnalyticsExportJobResponse::getValue);
  }

  /**
   * Submit a job to generate report for the given criterion
   *
   * @param jobType type of job report the user is requesting
   * @param contractUrn contractUrn of the user requesting the report
   * @param requesterMemberUrn memberUrn of the user requesting the report
   * @param startTime The start date of the time range of the exported data
   * @param endTime The end data of the time range of the exported data
   * @return SalesAnalyticsExportJob containing the job details
   */
  public Task<SalesAnalyticsExportJob> submitJobRequest(SalesAnalyticsExportJobType jobType, com.linkedin.common.urn.ContractUrn contractUrn,
      com.linkedin.common.urn.MemberUrn requesterMemberUrn, long startTime, long endTime) throws ExecutionException {

    ContractUrn protoContractUrn = ContractUrn.newBuilder().setContractId(contractUrn.getIdAsLong()).build();
    MemberUrn protoMemberUrn = MemberUrn.newBuilder().setMemberId(requesterMemberUrn.getIdAsLong()).build();

    SubmitSalesAnalyticsExportJobRequest.Builder requestBuilder = SubmitSalesAnalyticsExportJobRequest.newBuilder()
        .setJobType(jobType.toString())
        .setStartTime(startTime)
        .setEndTime(endTime)
        .setContract(protoContractUrn)
        .setViewerMemberUrn(protoMemberUrn);

    return ListenableFutureUtil.fromListenableFuture(
        salesAnalyticsExportJobsMemoizer.get().submitJobRequest(requestBuilder.build())).map(
        SubmitSalesAnalyticsExportJobResponse::getValue);
  }

  /**
   * Retrieve data availability for a contract
   *
   * @param jobType type of job report the user is requesting
   * @param contractUrn contractUrn of the user requesting the report
   * @return SalesAnalyticsExportDataAvailability containing the data availability info
   */
  public Task<SalesAnalyticsExportDataAvailability> retrieveDataAvailability(SalesAnalyticsExportJobType jobType,
      com.linkedin.common.urn.ContractUrn contractUrn) throws ExecutionException {
    ContractUrn protoContractUrn = ContractUrn.newBuilder().setContractId(contractUrn.getIdAsLong()).build();

    RetrieveSalesAnalyticsDataAvailabilityRequest.Builder requestBuilder = RetrieveSalesAnalyticsDataAvailabilityRequest.newBuilder()
        .setJobType(jobType.toString())
        .setContract(protoContractUrn);

    return ListenableFutureUtil.fromListenableFuture(
        salesAnalyticsExportJobsMemoizer.get().retrieveDataAvailability(requestBuilder.build())).map(
        RetrieveSalesAnalyticsDataAvailabilityResponse::getValue);
  }
}
