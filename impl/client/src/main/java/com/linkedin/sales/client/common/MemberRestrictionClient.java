package com.linkedin.sales.client.common;

import com.google.common.collect.Maps;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.ucf.client.UcfClient;
import com.linkedin.ucf.client.ViewerInfoBuilder;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;


public class MemberRestrictionClient {
  private final UcfClient _ucfClient;
  private final ViewerInfoBuilder _viewerInfoBuilder;

  public MemberRestrictionClient(UcfClient ucfClient, ViewerInfoBuilder viewerInfoBuilder) {
    _ucfClient = ucfClient;
    _viewerInfoBuilder = viewerInfoBuilder;
  }

  /**
   * Check whether any kind of restriction (like M2M blocking) exists between each viewee and the viewer.
   * @return A map from each viewee's memberId to a boolean indicating if the viewee is restricted
   */
  public Map<Long, Boolean> areMembersRestricted(Collection<Long> vieweeMemberIds, Long viewerMemberId) {
    // Note: Cannot stream over vieweeMemberIds and collect into a map because
    // Collectors#toMap does not allow null values in the map.
    Map<Urn, Urn> authorContent = Maps.newHashMapWithExpectedSize(vieweeMemberIds.size());
    for (Long vieweeMemberId : vieweeMemberIds) {
      authorContent.put(new MemberUrn(vieweeMemberId), null);
    }

    return _ucfClient.filterMembers(_viewerInfoBuilder.buildViewerInfo(new MemberUrn(viewerMemberId)), authorContent)
        .entrySet()
        .stream()
        .collect(Collectors.toMap(entry -> entry.getKey().getIdAsLong(), Map.Entry::getValue));
  }
}
