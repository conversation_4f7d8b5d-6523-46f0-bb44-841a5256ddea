package com.linkedin.sales.client.common;

import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.sales.admin.SalesAccount;
import com.linkedin.sales.admin.SalesAccountsV2RequestBuilders;
import javax.annotation.Nonnull;


/**
 * Client interface for communication with the /salesAccountsV2 restli services.
 */
public class SalesAccountsV2Client {

  private static final SalesAccountsV2RequestBuilders SALES_ACCOUNTS_V_2_REQUEST_BUILDERS = new SalesAccountsV2RequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public SalesAccountsV2Client(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * get SalesAccount by unique ID
   * @param accountId the account identifier
   * @param projections the PathSpecs to indicate what fields are going to be populated
   * @return GetRequest for SalesAccountV2
   */
  public Task<SalesAccount> getAccount(long accountId, EnterpriseApplicationUsageUrn viewer, @Nonnull PathSpec... projections) {
    GetRequest<SalesAccount> getRequest = SALES_ACCOUNTS_V_2_REQUEST_BUILDERS.get()
        .id(accountId)
        .viewerParam(viewer)
        .fields(projections)
        .build();
    return _parSeqRestClient.createTask(getRequest).map(Response::getEntity);
  }
}
