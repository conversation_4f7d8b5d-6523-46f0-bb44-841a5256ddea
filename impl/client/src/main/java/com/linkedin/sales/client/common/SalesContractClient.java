package com.linkedin.sales.client.common;

import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchGetEntityRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.client.response.BatchKVResponse;
import com.linkedin.restli.common.EntityResponse;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesContractsV2RequestBuilders;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collection;


public class SalesContractClient {

  private static final SalesContractsV2RequestBuilders CONTRACTS_REQUEST_BUILDERS = new SalesContractsV2RequestBuilders();
  private static final EnterpriseApplicationUsageUrn USAGE_URN =
      new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(), "lss-mt");

  private final ParSeqRestClient _parSeqRestClient;

  public SalesContractClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get a contract by its id
   * @param contractId the unique contract identifier
   * @param fields the PathSpecs to indicate what fields are going to be populated
   */
  public Task<SalesContract> getContract(@NonNull Long contractId, @NonNull PathSpec... fields) {
    GetRequest<SalesContract> contractGetRequest = CONTRACTS_REQUEST_BUILDERS.get()
        .id(contractId)
        .viewerParam(USAGE_URN)
        .fields(fields)
        .build();

    return _parSeqRestClient.createTask(contractGetRequest).map(Response::getEntity);
  }

  /**
   * Fetches multiple contracts at once
   * @param contractIds the unique contract identifiers
   * @param fields the PathSpecs to indicate what fields are going to be populated
   */
  public Task<BatchKVResponse<Long, EntityResponse<SalesContract>>> getContracts(@NonNull Collection<Long> contractIds,
                                                                                 @NonNull PathSpec... fields) {
    BatchGetEntityRequest<Long, SalesContract> request = CONTRACTS_REQUEST_BUILDERS.batchGet()
        .ids(contractIds)
        .viewerParam(USAGE_URN)
        .fields(fields)
        .build();

    return _parSeqRestClient.createTask(request).map(Response::getEntity);
  }
}
