package com.linkedin.sales.client.common;

import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.sales.admin.ContractSetting;
import com.linkedin.sales.admin.ContractSettingType;
import com.linkedin.sales.admin.SalesContractSettingsRequestBuilders;
import edu.umd.cs.findbugs.annotations.NonNull;

/**
 * Rest.li client for /salesContractsV2/salesContractSettings
 */
public class SalesContractSettingsClient {

  private static final SalesContractSettingsRequestBuilders
      SALES_CONTRACT_SETTINGS_REQUEST_BUILDERS = new SalesContractSettingsRequestBuilders();
  private final ParSeqRestClient _parSeqRestClient;

  public SalesContractSettingsClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  public Task<ContractSetting> get(@NonNull Long contractId, @NonNull ContractSettingType settingType) {
    GetRequest<ContractSetting> request =
        SALES_CONTRACT_SETTINGS_REQUEST_BUILDERS.get().idKey(contractId).id(settingType).build();
    return _parSeqRestClient.createTask(request).map(Response::getEntity);
  }
}
