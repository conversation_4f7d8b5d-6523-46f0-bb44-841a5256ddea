package com.linkedin.sales.client.common;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.sales.admin.CreditGrant;
import com.linkedin.sales.admin.CreditedActivity;
import com.linkedin.sales.admin.SalesCreditGrantsRequestBuilders;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.List;


/**
 * Rest.li client for d2://salesCreditGrants
 */
public class SalesCreditGrantsClient {
  private final ParSeqRestClient _parSeqRestClient;

  private static final SalesCreditGrantsRequestBuilders REQUEST_BUILDERS =
      new SalesCreditGrantsRequestBuilders();

  public SalesCreditGrantsClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Gets the InMail Credit Grants for the given sales navigator user
   *
   * @param contractUrn The contract of the current user
   * @param seatUrn The seat of the current user
   * @return The InMailCredit Grants
   */
  @NonNull
  public Task<List<CreditGrant>> getInmailCredits(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn) {
    FindRequest<CreditGrant> creditsFindRequest = REQUEST_BUILDERS.findByActivityAndSeat()
        .activityParam(CreditedActivity.SEND_INMAIL)
        .contractParam(contractUrn)
        .seatParam(seatUrn)
        .build();

    return _parSeqRestClient.createTask(creditsFindRequest)
        .map(response -> response.getEntity().getElements());
  }
}