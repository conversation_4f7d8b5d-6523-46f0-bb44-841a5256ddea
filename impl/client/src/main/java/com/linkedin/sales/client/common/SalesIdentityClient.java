package com.linkedin.sales.client.common;

import com.linkedin.data.schema.PathSpec;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.sales.SalesIdentitiesRequestBuilders;
import com.linkedin.restli.client.Response;
import com.linkedin.sales.SalesIdentity;
import edu.umd.cs.findbugs.annotations.NonNull;


public class SalesIdentityClient {
  private final ParSeqRestClient _parSeqRestClient;
  private static final SalesIdentitiesRequestBuilders REQUEST_BUILDERS = new SalesIdentitiesRequestBuilders();

  public SalesIdentityClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Finds the SalesIdentity for the given salesIdentityId
   */
  public Task<SalesIdentity> get(long salesIdentityId, @NonNull PathSpec... projection) {
    GetRequest<SalesIdentity> getSeatRequest = REQUEST_BUILDERS.get().id(salesIdentityId).fields(projection).build();

    return _parSeqRestClient.createTask(getSeatRequest).map(Response::getEntity);
  }
}
