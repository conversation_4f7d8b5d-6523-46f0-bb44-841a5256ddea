package com.linkedin.sales.client.common;

import com.linkedin.common.ContractUrnArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SalesSeatsV2FindByCriteriaRequestBuilder;
import com.linkedin.sales.admin.SalesSeatsV2RequestBuilders;
import com.linkedin.sales.admin.SeatStatus;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Collection;
import java.util.EnumSet;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;


public class SalesSeatClient {

  private static final SalesSeatsV2RequestBuilders SALES_SEATS_REQUEST_BUILDERS = new SalesSeatsV2RequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  private static final Integer DEFAULT_COUNT = 10;
  private static final Integer DEFAULT_START = 0;

  public SalesSeatClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Returns the seat designated by its unique ID.
   * @param seatId the unique ID of the seat to fetch
   * @param contractUrn optional contractUrn that owns the seat. This is used for LIX'ing in the remote service
   * @param fields required projection (remote service will return 400 if missing)
   */
  @NonNull
  public Task<SalesSeat> getSeat(@NonNull Long seatId, @Nullable ContractUrn contractUrn,
      @NonNull EnterpriseApplicationUsageUrn viewer, @NonNull PathSpec... fields) {
    GetRequest<SalesSeat> request = SALES_SEATS_REQUEST_BUILDERS.get()
        .id(seatId)
        .contractParam(contractUrn)
        .viewerParam(viewer)
        .fields(fields)
        .build();

    return _parSeqRestClient.createTask(request).map(Response::getEntity);
  }


  /**
   * Get the list of valid seats by member, this method can be use to
   *  1 - Get all seats of a given member across all contracts.
   *  2 - Get seats of a given member from given contract.
   * @param memberUrn the member for which to fetch the seats
   * @param contracts optional contracts to filter the seats by
   * @param fields required projection (remote service will return 400 if missing)
   */
  public Task<List<SalesSeat>> findByMember(@NonNull MemberUrn memberUrn, @Nullable Collection<ContractUrn> contracts,
      @NonNull EnterpriseApplicationUsageUrn viewer, @Nullable PagingContext pagingContext, @NonNull PathSpec... fields) {
    SalesSeatsV2FindByCriteriaRequestBuilder requestBuilder = SALES_SEATS_REQUEST_BUILDERS.findByCriteria()
        .statusesParam(EnumSet.of(SeatStatus.ACTIVE))
        .contractActiveParam(Boolean.TRUE)
        .addMembersParam(memberUrn)
        .contractsParam(CollectionUtils.isEmpty(contracts) ? null : contracts)
        .viewerParam(viewer)
        .fields(fields);

    if (pagingContext != null) {
      requestBuilder.paginateStart(pagingContext.hasStart() ? pagingContext.getStart() : DEFAULT_START)
          .paginateCount(pagingContext.hasCount() ? pagingContext.getCount() : DEFAULT_COUNT);
    }

    return _parSeqRestClient.createTask(requestBuilder.build()).map(Response::getEntity).map(CollectionResponse::getElements);
  }

  /**
   * Returns a at-most-1-seat list containing the Seat last used by the given member
   * @param memberUrn the Member for which to retrieve the seat
   * @param fields required projection (remote service will return 400 if missing)
   */
  public Task<List<SalesSeat>> findByMemberLastUsed(@NonNull MemberUrn memberUrn, @NonNull PathSpec... fields) {
    FindRequest<SalesSeat> request = SALES_SEATS_REQUEST_BUILDERS.findByMemberLastUsed()
        .memberParam(memberUrn)
        .fields(fields)
        .build();

    return _parSeqRestClient.createTask(request).map(Response::getEntity).map(CollectionResponse::getElements);
  }

  /**
   * Get the list of seats with the given email id
   *
   * @param emailId for which seats to be fetched
   * @param contractUrn contract identifier from which seats to be fetched
   * @param seatUrn viewer's seat urn
   * @param fields list of fields from sales seat to be fetched.
   * @return list of {@link SalesSeat} with the given email id in that contract
   */
  public Task<List<SalesSeat>> findBySeatEmail(String emailId, ContractUrn contractUrn,
      SeatUrn seatUrn, @NonNull PathSpec... fields) {
    FindRequest<SalesSeat> request = SALES_SEATS_REQUEST_BUILDERS.findByCriteria()
        .emailsParam(new StringArray(emailId))
        .contractActiveParam(true)
        .contractsParam(new ContractUrnArray(contractUrn))
        .addStatusesParam(SeatStatus.ACTIVE)
        .viewerParam(seatUrn)
        .fields(fields)
        .build();
    return _parSeqRestClient.createTask(request).map(Response::getEntity).map(CollectionResponse::getElements);
  }
}
