package com.linkedin.sales.client.ep;

import com.linkedin.common.AuditStamp;
import com.linkedin.common.EnterpriseProfileUrnArray;
import com.linkedin.common.urn.CrmUserUrn;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplicationInstancesRequestBuilders;
import com.linkedin.enterprise.EnterpriseBulkLicenseAssignmentPluginsRequestBuilders;
import com.linkedin.enterprise.EnterpriseLicenseAssignmentsRequestBuilders;
import com.linkedin.enterprise.EnterpriseProfilesRequestBuilders;
import com.linkedin.enterprise.account.ApplicationInstance;
import com.linkedin.enterprise.account.ApplicationInstanceKey;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.bulk.BulkLicenseAssignmentContext;
import com.linkedin.enterprise.identity.Profile;
import com.linkedin.enterprise.identity.ProfileKey;
import com.linkedin.enterprise.identity.ProfileSource;
import com.linkedin.enterprise.license.LicenseAssignment;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ActionRequest;
import com.linkedin.restli.client.CreateIdRequest;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Request;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Represents the client interface for communication with the Sales-to-EP Identity mapping restli services.
 */
public class EnterprisePlatformClient {

  private static final Logger LOG = LoggerFactory.getLogger(EnterprisePlatformClient.class.getName());

  protected static final EnterpriseProfilesRequestBuilders ENTERPRISE_PROFILES_REQUEST_BUILDERS =
      new EnterpriseProfilesRequestBuilders();
  protected static final EnterpriseBulkLicenseAssignmentPluginsRequestBuilders ENTERPRISE_BULK_LICENSE_ASSIGNMENT_PLUGINS_REQUEST_BUILDERS =
      new EnterpriseBulkLicenseAssignmentPluginsRequestBuilders();
  protected static final EnterpriseApplicationInstancesRequestBuilders ENTERPRISE_APPLICATION_INSTANCES_REQUEST_BUILDERS
      = new EnterpriseApplicationInstancesRequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public EnterprisePlatformClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get mapped contract of an application instance
   * @param applicationInstanceUrn app instance urn of the enterprise application instance
   * @param viewer viewer urn to be used to call enterpriseApplicationInstances endpoint
   * @return mapped contract of the provided application instance
   */
  @NonNull
  public Task<Optional<ApplicationInstance>> getAppInstanceWithMappedContract(@NonNull EnterpriseApplicationInstanceUrn applicationInstanceUrn,
      @NonNull Urn viewer) {
    ApplicationInstanceKey applicationInstanceKey = new ApplicationInstanceKey()
        .setId(applicationInstanceUrn.getApplicationInstanceIdEntity())
        .setAccount(applicationInstanceUrn.getAccountEntity());

    GetRequest<ApplicationInstance> getRequest = ENTERPRISE_APPLICATION_INSTANCES_REQUEST_BUILDERS.get()
        .id(new ComplexResourceKey<>(applicationInstanceKey, new EmptyRecord()))
        .viewerParam(viewer)
        .fields(new PathSpec[]{ApplicationInstance.fields().contractId()})
        .build();

    return makeRequest(getRequest).map(resp -> Optional.ofNullable(resp.getEntity()));
  }

  /**
   * find license assignments by profile and app instance
   */
  public Task<List<LicenseAssignment>> findLicenseAssignments(
      EnterpriseApplicationInstanceUrn appInstance,
      EnterpriseProfileUrn enterpriseProfile) {

    EnterpriseAccountUrn accountUrn = appInstance.getAccountEntity();

    FindRequest<LicenseAssignment> request = new EnterpriseLicenseAssignmentsRequestBuilders()
        .findByAccount()
        .accountParam(accountUrn)
        .applicationInstanceParam(appInstance)
        .profileParam(enterpriseProfile)
        .viewerParam(enterpriseProfile)
        .build();

    return makeRequest(request).map(response -> response.getEntity().getElements());
  }

  public Task<Profile> getEnterpriseProfile(
      EnterpriseProfileUrn enterpriseProfile,
      EnterpriseApplicationInstanceUrn applicationInstanceUrn,
      Urn viewerUrn) {
    ProfileKey pKey = new ProfileKey()
        .setAccount(enterpriseProfile.getAccountEntity())
        .setProfileId(enterpriseProfile.getProfileIdEntity());

    GetRequest<Profile> request = ENTERPRISE_PROFILES_REQUEST_BUILDERS
        .get()
        .id(new ComplexResourceKey<>(pKey, new EmptyRecord()))
        .applicationInstanceParam(applicationInstanceUrn)
        .viewerParam(viewerUrn)
        .build();
    return makeRequest(request).map(Response::getEntity);
  }

  /**
   * Find EnterpriseProfiles based on primary email address. It is a List of Profiles because
   * the service uses md5 to hash the primary email address, in case of collision, irrelevant profiles
   * might be returned
   */
  public Task<List<Profile>> findEnterpriseProfileByEmail(
      EnterpriseApplicationInstanceUrn applicationInstanceUrn,
      String emailAddress,
      Urn viewer) {
    FindRequest<Profile> profileFindRequest = ENTERPRISE_PROFILES_REQUEST_BUILDERS
        .findByExternalIdOrEmail()
        .accountParam(applicationInstanceUrn.getAccountEntity())
        .applicationInstanceParam(applicationInstanceUrn)
        .primaryEmailAddressParam(emailAddress)
        .viewerParam(viewer)
        .build();
    return makeRequest(profileFindRequest).map(response -> response.getEntity().getElements());
  }

  /**
   * Bulk assign licenses to the specified list of profiles.
   */
  public Task<BulkActionBatchResult> assignLicenses(Collection<EnterpriseProfileUrn> profiles, BulkLicenseAssignmentContext context, Urn viewer) {
    ActionRequest<BulkActionBatchResult> request = ENTERPRISE_BULK_LICENSE_ASSIGNMENT_PLUGINS_REQUEST_BUILDERS
        .actionProcessBatch()
        .contextParam(context)
        .viewerParam(viewer)
        .inputKeysParam(new EnterpriseProfileUrnArray(profiles))
        .build();
    return makeRequest(request).map(Response::getEntity);
  }

  /**
   * Validate that the BulkLicenseAssignmentContext is valid.
   */
  public Task<Void> validateLicenseAssignmentBulkActionContext(BulkLicenseAssignmentContext context, Urn viewer) {
    ActionRequest<Void> actionRequest = ENTERPRISE_BULK_LICENSE_ASSIGNMENT_PLUGINS_REQUEST_BUILDERS
        .actionValidate().contextParam(context).viewerParam(viewer).build();
    return makeRequest(actionRequest).map(Response::getEntity);
  }

  /**
   * Create EnterpriseProfile.
   * One of email, firstname, or last name has to be provided, otherwise it will be skipped.
   */
  public Task<Pair<CrmUserUrn, ProfileKey>> createEnterpriseProfileFromCrmUser(
      CrmUserUrn crmUserUrn,
      @Nullable String firstName,
      @Nullable String lastName,
      @Nullable EmailAddressUrn emailAddressUrn,
      EnterpriseApplicationInstanceUrn applicationInstanceUrn,
      Urn viewer) {
    // If member does not already exist, MemberUrn and ProfileMemberBindingStatus info may not be set until Seat processing later.
    if (emailAddressUrn == null && StringUtils.isEmpty(firstName) && StringUtils.isEmpty(lastName)) {
      return Task.value(null);
    }

    AuditStamp auditStamp = new AuditStamp()
        .setActor(viewer)
        .setTime(System.currentTimeMillis());
    Profile profile = new Profile()
        .setAccount(applicationInstanceUrn.getAccountEntity())
        .setApplicationInstance(applicationInstanceUrn)
        .setSource(ProfileSource.REST_API)
        .setPrimaryEmailAddress(emailAddressUrn, SetMode.IGNORE_NULL)
        .setPreferredFirstName(firstName, SetMode.IGNORE_NULL)
        .setPreferredLastName(lastName, SetMode.IGNORE_NULL)
        .setCreated(auditStamp)
        .setLastModified(auditStamp);

    CreateIdRequest<ComplexResourceKey<ProfileKey, EmptyRecord>, Profile> request = ENTERPRISE_PROFILES_REQUEST_BUILDERS.create()
        .input(profile)
        .applicationInstanceParam(applicationInstanceUrn)
        .viewerParam(viewer)
        .build();
    return makeRequest(request).map(response -> new Pair<>(crmUserUrn, response.getEntity().getId().getKey()));
  }

  protected <T> Task<Response<T>> makeRequest(Request<T> request) {
    return _parSeqRestClient.createTask(request);
  }
}
