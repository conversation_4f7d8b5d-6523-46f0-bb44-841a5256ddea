package com.linkedin.sales.client.ep;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.EnterpriseProfileActivationLinksDoGenerateRequestBuilder;
import com.linkedin.enterprise.EnterpriseProfileActivationLinksRequestBuilders;
import com.linkedin.enterprise.identity.ActivationLink;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestliClient;
import com.linkedin.restli.client.Response;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;


/** Client to interact with Enterprise Platform's backend resources */
public class EnterpriseProfileActivationLinksClient {
  private static final EnterpriseProfileActivationLinksRequestBuilders ACTIVATION_LINKS_REQUEST_BUILDERS =
      new EnterpriseProfileActivationLinksRequestBuilders();

  private final ParSeqRestliClient _parSeqRestliClient;

  public EnterpriseProfileActivationLinksClient(ParSeqRestliClient parSeqRestliClient) {
    _parSeqRestliClient = parSeqRestliClient;
  }

  /**
   * Generate an activation link for an enterprise profile.
   *
   * @param enterpriseApplication the enterprise application associated with the enterprise profile
   * @param enterpriseApplicationInstance the enterprise application instance associated with the
   *     enterprise application
   * @param enterpriseProfile the enterprise profile to generate the activation link for
   * @param lighthouseWebRedirectUrl the url to redirect the user to after a successful login
   * @param ttlSeconds The number of seconds for the activation link to remain valid
   * @param viewer an enterprise profile with sufficient permissions or an allowed application
   * @return a url that takes you to checkpoint where you will be required to sign into LinkedIn
   */
  public Task<ActivationLink> generateCheckpointEnterpriseLoginUrl(
      @NonNull EnterpriseApplicationUrn enterpriseApplication,
      @Nullable EnterpriseApplicationInstanceUrn enterpriseApplicationInstance,
      @NonNull EnterpriseProfileUrn enterpriseProfile,
      @NonNull Url lighthouseWebRedirectUrl,
      @Nullable Long ttlSeconds,
      @NonNull Urn viewer) {

    EnterpriseProfileActivationLinksDoGenerateRequestBuilder builder =
        ACTIVATION_LINKS_REQUEST_BUILDERS.actionGenerate()
        .applicationUrnParam(enterpriseApplication)
        .enterpriseProfileUrnParam(enterpriseProfile)
        .redirectUrlParam(lighthouseWebRedirectUrl)
        .viewerParam(viewer);

    if (enterpriseApplicationInstance != null) {
      builder.enterpriseApplicationInstanceUrnParam(enterpriseApplicationInstance);
    }

    if (ttlSeconds != null) {
      builder.ttlSecondsParam(ttlSeconds);
    }

    return _parSeqRestliClient.createTask(builder.build()).map(Response::getEntity);
  }
}

