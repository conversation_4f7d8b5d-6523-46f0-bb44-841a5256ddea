package com.linkedin.sales.client.externalization;

import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.parseq.Task;
import com.linkedin.platform.ProvisionedApplication;
import com.linkedin.platform.ProvisionedApplicationsGetRequestBuilder;
import com.linkedin.platform.ProvisionedApplicationsRequestBuilders;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Client for {@link ProvisionedApplication} which track the information of the provisioned app.
 * <AUTHOR>
 */
public class ProvisionedApplicationClient {
  private static final ProvisionedApplicationsRequestBuilders PROVISIONED_APPLICATIONS_REQUEST_BUILDERS =
      new ProvisionedApplicationsRequestBuilders();
  private static final Logger LOG = LoggerFactory.getLogger(ProvisionedApplicationClient.class);

  private final ParSeqRestClient _parSeqRestClient;

  public ProvisionedApplicationClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get {@link ProvisionedApplication} by the given {@link DeveloperApplicationUrn}
   * @param developerApplicationUrn urn of the developer application
   * @return
   */
  public Task<ProvisionedApplication> get(@NonNull DeveloperApplicationUrn developerApplicationUrn) {
    ProvisionedApplicationsGetRequestBuilder builder =
        PROVISIONED_APPLICATIONS_REQUEST_BUILDERS.get().id(developerApplicationUrn);

    return _parSeqRestClient.createTask(builder.build())
        .map(Response::getEntity)
        .onFailure(t -> LOG.error("Unable to get ProvisionedApplication for {}", developerApplicationUrn, t));
  }
}
