package com.linkedin.sales.client.externalization;

import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.d2.discovery.util.D2Utils;
import com.linkedin.data.template.StringArray;
import com.linkedin.developers.ApiPermissionType;
import com.linkedin.oauth2.internal.OAuth2AccessTokenData;
import com.linkedin.oauth2.internal.Oauth2RequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ActionRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.security.identitytoken.api.MemberIdentityToken;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScopeWithPermissionInfo;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScopesRequestBuilders;
import com.linkedin.util.envinfo.EnvInfo;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesOauthAuthenticationClient {

  private static final Logger LOG = LoggerFactory.getLogger(SalesOauthAuthenticationClient.class);

  private static final int ISSUING_COLO_VERIFICATION_WINDOW_IN_SECONDS = 300;

  private final ParSeqRestClient _parSeqRestClient;

  public SalesOauthAuthenticationClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  public Task<ThirdPartyAuthorizedScopeWithPermissionInfo> generateThirdPartyAuthorizedScopeForApp(String names,
      DeveloperApplicationUrn applicationUrn) {

    StringArray stringArray = new StringArray();
    stringArray.add(names);

    ActionRequest<ThirdPartyAuthorizedScopeWithPermissionInfo> generateAuthorizedScopeRequest =
        new ThirdPartyAuthorizedScopesRequestBuilders().actionGenerateThirdPartyAuthorizedScopeForApp()
            .applicationUrnParam(applicationUrn)
            .permissionsParam(stringArray)
            .typeParam(ApiPermissionType.MEMBER_PERMISSION)
            .build();

    return _parSeqRestClient.createTask(generateAuthorizedScopeRequest)
        .map(collectionResponse -> collectionResponse.getEntity())
        .recover(throwable -> {
          throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
              "Couldn't generated authorized scope request. Does " + applicationUrn + " have " + names + " permissions enabled?: "
                  + throwable.getMessage(), throwable);
        });
  }

  public Task<OAuth2AccessTokenData> generateOauth2SalesAccessToken(long appId, long memberId, int permissionGroup, long salesAccessTokenTtl,
      MemberIdentityToken memberIdentityToken, @Nullable EnvInfo accessTokenIssuedEnvInfo) {

    Oauth2RequestBuilders oauth2Builders;
    /**
     * Two conditions being checked here:
     * a) The token was issued less than 5 minutes ago
     * b) accessToken indeed has the issuing colo info
     */
    if ((accessTokenIssuedEnvInfo != null) && (System.currentTimeMillis() - memberIdentityToken.getLoginTimestamp()
        < TimeUnit.SECONDS.toMillis(ISSUING_COLO_VERIFICATION_WINDOW_IN_SECONDS))) {
      LOG.info(
          "Oauth token was issued less than 5 minutes ago in colo: {}. DAT generation request should be routed to the same colo.",
          accessTokenIssuedEnvInfo.getEnv());
      oauth2Builders = new Oauth2RequestBuilders(
          D2Utils.addSuffixToBaseName(Oauth2RequestBuilders.getPrimaryResource(), accessTokenIssuedEnvInfo.getEnv()));
    } else {
      oauth2Builders = new Oauth2RequestBuilders();
    }

    ActionRequest<OAuth2AccessTokenData> salesAccessTokenRequest =
        oauth2Builders.actionGenDegradedAccessToken()
            .appIdParam((int) appId)
            .memberIdParam(memberId)
            .scopeIdParam(permissionGroup)
            .tokenTtlParam(salesAccessTokenTtl)
            .build();

    return _parSeqRestClient.createTask(salesAccessTokenRequest)
        .map(oAuth2AccessTokenDataResponse -> oAuth2AccessTokenDataResponse.getEntity())
        .recover(throwable -> {
          throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, throwable);
        });
  }
}
