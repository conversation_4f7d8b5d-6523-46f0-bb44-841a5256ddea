package com.linkedin.sales.client.flagship;

import com.linkedin.entitlement.internal.Entitlements;
import com.linkedin.entitlements.client.EntitlementsRequestBuilders;
import com.linkedin.mnybe.shared.identity.Entitlement;
import com.linkedin.mnybe.shared.identity.Party;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * Client used to fetch premium entitlements
 */
public class PremiumEntitlementsClient {
  private static final EntitlementsRequestBuilders ENTITLEMENTS_REQUEST_BUILDERS = new EntitlementsRequestBuilders();
  private final ParSeqRestClient _parSeqRestClient;

  public PremiumEntitlementsClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Fetches entitlements for a party
   * @param party Party to get entitlements for
   * @param entitlementsToGet List of entitlements to check for
   * @return A set of entitlements that this user has, being a subset of entitlementsToGet
   */
  public Task<Set<Entitlement>> getEntitlements(Party party, Set<Entitlement> entitlementsToGet) {
    Set<Integer> entitlementCodes = entitlementsToGet.stream().map(Entitlement::encode).collect(Collectors.toSet());
    GetRequest<Entitlements> request =
        ENTITLEMENTS_REQUEST_BUILDERS.get().id(party.toURN()).entsToGetParam(entitlementCodes).build();
    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity)
        .map(Entitlements::getEntitlements)
        .map(entitlements -> entitlements.entrySet().stream()
            .filter(entry -> entry.getValue() > 0)
            .map(Map.Entry::getKey)
            .map(Integer::parseInt)
            .map(Entitlement::decode)
            .collect(Collectors.toSet()));
  }
}
