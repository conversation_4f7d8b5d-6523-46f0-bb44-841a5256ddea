package com.linkedin.sales.client.flagship;

import com.google.common.util.concurrent.ListenableFuture;
import proto.com.linkedin.common.MemberUrn;
import com.linkedin.grpc.d2.D2ManagedChannelProvider;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.guava.ListenableFutureUtil;
import io.grpc.ManagedChannel;
import java.util.NoSuchElementException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.premium.FindBySlotTypePremiumUpsellFlowsRequest;
import proto.com.linkedin.premium.FindBySlotTypePremiumUpsellFlowsResponse;
import proto.com.linkedin.premium.PremiumUpsellCardData;
import proto.com.linkedin.premium.PremiumUpsellFlowContext;
import proto.com.linkedin.premium.PremiumUpsellFlowsServiceGrpc;
import proto.com.linkedin.premium.PremiumUpsellSlot;


/**
 * Client used to fetch upsell slot metadata from Premium Upsell platform
 */
public class PremiumUpsellFlowsClient {
  private static final Logger LOG = LoggerFactory.getLogger(PremiumUpsellFlowsClient.class);
  private static final String PREMIUM_UPSELL_FLOWS_SERVICE_NAME = "PremiumUpsellFlowsService";
  private final D2ManagedChannelProvider d2ManagedChannelProvider;

  public PremiumUpsellFlowsClient(D2ManagedChannelProvider d2ManagedChannelProvider) {
    this.d2ManagedChannelProvider = d2ManagedChannelProvider;
  }

  /**
   * Get upsell metadata
   */
  public Task<PremiumUpsellCardData> getPremiumUpsellCardData(MemberUrn viewerMember,
      PremiumUpsellSlot upsellSlot, PremiumUpsellFlowContext flowContext) {
    ManagedChannel managedChannel =
        d2ManagedChannelProvider.forServiceName(PREMIUM_UPSELL_FLOWS_SERVICE_NAME);
    PremiumUpsellFlowsServiceGrpc.PremiumUpsellFlowsServiceFutureStub futureStub =
        PremiumUpsellFlowsServiceGrpc.newFutureStub(managedChannel);
    ListenableFuture<FindBySlotTypePremiumUpsellFlowsResponse> listenableFuture = futureStub.findBySlotType(
        FindBySlotTypePremiumUpsellFlowsRequest.newBuilder()
            .setSlotType(upsellSlot)
            .setFlowContext(flowContext).build()
    );
    return ListenableFutureUtil.fromListenableFuture(listenableFuture)
        .onFailure(e -> LOG.warn("Unable to fetch the Upsell slot data for member {}, slot {} and context {}",
            viewerMember, upsellSlot, flowContext, e))
        .map(response -> {
          if (response.hasElements()) {
            return response.getElements().getItems(0).getUpsellCardData();
          }
          LOG.warn("Unable to fetch the Upsell slot data for member {}, slot {} and context {}",
              viewerMember, upsellSlot, flowContext);
          throw new NoSuchElementException();
        });
  }
}
