package com.linkedin.sales.client.integration;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.CrmPairingsFindByContractRequestBuilder;
import com.linkedin.crm.CrmPairingsFindBySeatRequestBuilder;
import com.linkedin.crm.CrmPairingsRequestBuilders;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.server.PagingContext;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Client for CrmPairing which track the lifecycle and meta for a CRM connection to a Sales Navigator contract.
 * <AUTHOR>
 */
public class CrmPairingClient {
  private static final Logger LOG = LoggerFactory.getLogger(CrmPairingClient.class);
  private static final CrmPairingsRequestBuilders CRM_PAIRINGS_REQUEST_BUILDERS = new CrmPairingsRequestBuilders();
  private static final PathSpec[] FIND_BY_INSTANCE_DEFAULT_PROJECTION =
      {CrmPairing.fields().contract(), CrmPairing.fields().crmInstanceUrn(), CrmPairing.fields().crmPairingId(),
          CrmPairing.fields().status(), CrmPairing.fields().crmResourceUrl()};

  private final ParSeqRestClient _parSeqRestClient;

  public CrmPairingClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Find {@link CrmPairing}s with paging by crm instance. note that this would query the materialized view
   * of CrmPairing, which does not contain all the fields from {@link CrmPairing} pdsc.
   * @param crmInstanceUrn urn of the crm instance
   * @param activeOnly flag to indicate if only fetching active ones
   * @return {@link CrmPairing}s with paging
   */
  @NonNull
  public Task<CollectionResponse<CrmPairing>> findByCrmInstance(@NonNull CrmInstanceUrn crmInstanceUrn,
      boolean activeOnly) {
    FindRequest<CrmPairing> request = CRM_PAIRINGS_REQUEST_BUILDERS.findByCrmInstance()
        .activeOnlyParam(activeOnly)
        .crmInstanceParam(crmInstanceUrn)
        .fields(FIND_BY_INSTANCE_DEFAULT_PROJECTION)
        .build();

    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity)
        .onFailure(t -> LOG.error("Unable to get CrmPairings for {}", crmInstanceUrn, t));
  }

  /**
   * Find the default paging limit count of active and inactive pairings for contract.
   * Currently maxCount of Crm Pairings is a single digit no.
   * @param contractUrn contact urn to find pairing for
   * @param activeOnly fetch only active crm pairing or not
   * @param pagingContext nullable paging context passed in
   * @return crm pairings for the contract
   */
  @NonNull
  public Task<List<CrmPairing>> findCrmPairingsByContract(@NonNull ContractUrn contractUrn, @NonNull boolean activeOnly,
      @Nullable PagingContext pagingContext) {
    CrmPairingsFindByContractRequestBuilder request = CRM_PAIRINGS_REQUEST_BUILDERS.findByContract()
        .contractParam(contractUrn)
        .activeOnlyParam(activeOnly);

    if (pagingContext != null) {
      request.paginate(pagingContext.getStart(), pagingContext.getCount());
    }

    return _parSeqRestClient.createTask(request.build())
        .map(Response::getEntity)
        .map(CollectionResponse::getElements);
  }

  public Task<List<CrmPairing>> findCrmPairingsBySeat(ContractUrn contractUrn, SeatUrn seatUrn, boolean activeOnly,
      @Nullable PagingContext pagingContext) {
    CrmPairingsFindBySeatRequestBuilder request = CRM_PAIRINGS_REQUEST_BUILDERS.findBySeat()
        .contractParam(contractUrn)
        .seatParam(seatUrn)
        .activeOnlyParam(activeOnly);
    if (pagingContext != null) {
      request.paginate(pagingContext.getStart(), pagingContext.getCount());
    }

    return _parSeqRestClient.createTask(request.build())
        .map(Response::getEntity)
        .map(CollectionResponse::getElements);
  }

}
