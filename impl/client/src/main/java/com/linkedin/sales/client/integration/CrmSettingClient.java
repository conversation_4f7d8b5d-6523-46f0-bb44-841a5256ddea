package com.linkedin.sales.client.integration;

import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.crm.CrmPairingKey;
import com.linkedin.crm.SalesConnectedCrmSetting;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.crm.SalesConnectedCrmSettingsRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;


/**
 * Client to fetch crm settings under the scope of a crm pairing
 * <AUTHOR>
 */
public class CrmSettingClient {
  private static final SalesConnectedCrmSettingsRequestBuilders CRM_SETTINGS_REQUEST_BUILDERS =
      new SalesConnectedCrmSettingsRequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public CrmSettingClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get the crm setting for a specific type under the scope of a crm pairing
   */
  public Task<SalesConnectedCrmSetting> get(CrmPairingUrn crmPairingUrn, SalesConnectedCrmSettingType crmSettingType) {
    CrmPairingKey crmPairingKey = new CrmPairingKey().setCrmPairingId(crmPairingUrn.getCrmPairingIdEntity())
        .setContract(crmPairingUrn.getContractEntity());
    GetRequest<SalesConnectedCrmSetting> request = CRM_SETTINGS_REQUEST_BUILDERS.get()
        .keyKey(new ComplexResourceKey<>(crmPairingKey, new EmptyRecord()))
        .id(crmSettingType)
        .build();
    return _parSeqRestClient.createTask(request).map(Response::getEntity);
  }
}
