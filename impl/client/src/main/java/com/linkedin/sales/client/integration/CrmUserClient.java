package com.linkedin.sales.client.integration;

import com.linkedin.common.CrmUserUrnArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.crm.CrmUser;
import com.linkedin.crm.UsersFindByQueryRequestBuilder;
import com.linkedin.crm.UsersRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CollectionResponse;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.List;


/**
 * Client for interacting with crm users.
 */
public class CrmUserClient {
  private static final UsersRequestBuilders USERS_REQUEST_BUILDERS = new UsersRequestBuilders();

  private final ParSeqRestClient _parSeqRestClient;

  public CrmUserClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Find crm users by query
   */
  public Task<List<CrmUser>> findByQuery(@NonNull ContractUrn contractUrn, @NonNull String query,
      @Nullable CrmUserUrnArray userUrns, boolean includeAssigned, int start, int count) {
    UsersFindByQueryRequestBuilder findRequest = USERS_REQUEST_BUILDERS.findByQuery()
        .contractKey(contractUrn)
        .queryParam(query)
        .includeUsersParam(userUrns)
        .includeAssignedParam(includeAssigned)
        .paginateStart(start)
        .paginateCount(count);

    return _parSeqRestClient.createTask(findRequest.build())
        .map(Response::getEntity)
        .map(CollectionResponse::getElements);
  }
}
