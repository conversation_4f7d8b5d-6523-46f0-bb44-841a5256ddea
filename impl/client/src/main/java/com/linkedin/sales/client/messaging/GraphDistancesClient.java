package com.linkedin.sales.client.messaging;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.GetMode;
import com.linkedin.graph.Distance;
import com.linkedin.graph.DistanceEnum;
import com.linkedin.graph.GraphDistancesGetRequestBuilder;
import com.linkedin.graph.GraphDistancesRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchGetEntityRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.common.CompoundKey;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by meliu
 * Client to interact with all graphdistance endpoints
 */
public class GraphDistancesClient {
  private static final Logger LOG = LoggerFactory.getLogger(GraphDistancesClient.class);

  private final ParSeqRestClient _parSeqRestClient;
  private static final String FROM_FIELD = "from";
  private static final String TO_FIELD = "to";
  private static final String MEMBER_URN_STRING_FORMAT = "urn:li:member:%d";

  private static final Map<DistanceEnum, Integer> DISTANCE_ENUM_INTEGER_MAP =
      new ImmutableMap.Builder<DistanceEnum, Integer>().put(DistanceEnum.SELF, 0)
          .put(DistanceEnum.DISTANCE_1, 1)
          .put(DistanceEnum.DISTANCE_2, 2)
          .put(DistanceEnum.DISTANCE_3, 3)
          .put(DistanceEnum.OUT_OF_NETWORK, -1)
          .put(DistanceEnum.$UNKNOWN, -1)
          .build();

  public GraphDistancesClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get distance on the graph between members represented by from and to urns in terms of DistanceEnum
   * @param fromUrn Member Urn from which the graph-distance should be computed
   * @param toUrn Member Urn to which the graph-distance should be computed
   * @return Distance between the members in the graph
   */
  @NonNull
  public Task<Integer> getGraphDistance(@NonNull Urn fromUrn, @NonNull Urn toUrn) {

    GraphDistancesRequestBuilders.Key key = new GraphDistancesRequestBuilders.Key().setFrom(fromUrn).setTo(toUrn);

    GraphDistancesGetRequestBuilder graphDistanceRequestBuilder = new GraphDistancesRequestBuilders().get().id(key);
    GetRequest<Distance> getRequest = graphDistanceRequestBuilder.build();

    return _parSeqRestClient.createTask(getRequest).map(response -> DISTANCE_ENUM_INTEGER_MAP.get(response.getEntity().getDistance()))
        .recoverWith(t -> {
          LOG.error("Unable to get distance between between {} and {}", fromUrn, toUrn, t);
          return Task.failure(t);
        });
  }

  @NonNull
  public Task<Map<Long, Integer>> batchGetGraphDistances(@NonNull List<Long> vieweeIds, @NonNull Long viewerId) {

    if (!vieweeIds.isEmpty()) {
      String viewerUrnString = String.format(MEMBER_URN_STRING_FORMAT, viewerId);
      List<CompoundKey> compoundKeys = vieweeIds.stream()
          .map(memberId -> {
            String memberUrnString = String.format(MEMBER_URN_STRING_FORMAT, memberId);
            return new CompoundKey().append(FROM_FIELD, viewerUrnString).append(TO_FIELD, memberUrnString);
          })
          .collect(Collectors.toList());
      BatchGetEntityRequest<CompoundKey, Distance> distancesRequest = new GraphDistancesRequestBuilders().batchGet().ids(compoundKeys)
          .maxDistanceParam(DistanceEnum.DISTANCE_3)
          .build();

      return _parSeqRestClient.createTask(distancesRequest).map(response -> {
        Map<CompoundKey, Distance> distances = response.getEntity().getResults().entrySet().stream()
            .filter(e -> e.getValue().hasEntry())
            .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getEntity()));
        return compoundKeys.stream()
            .filter(key -> Optional.ofNullable(distances.get(key))
                .map(distance -> distance.getDistance(GetMode.NULL))
                .map(DISTANCE_ENUM_INTEGER_MAP::get)
                .isPresent())
            .collect(Collectors.toMap(key -> {
                  try {
                    return (new Urn(key.getPartAsString(TO_FIELD))).getIdAsLong();
                  } catch (URISyntaxException e) {
                    LOG.error("Error in getting Urn from key: " + key, e);
                    return null;
                  }
                },
                key -> DISTANCE_ENUM_INTEGER_MAP.get(distances.get(key).getDistance())));
      });
    } else {
      return Task.value(Collections.emptyMap());
    }
  }
}