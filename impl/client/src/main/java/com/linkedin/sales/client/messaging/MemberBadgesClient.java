package com.linkedin.sales.client.messaging;

import com.linkedin.badge.internal.BadgeTypeArray;
import com.linkedin.badge.internal.MemberBadges;
import com.linkedin.badge.internal.MemberBadgesBuilders;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * Client to interact with the member badges API.
 */
public class MemberBadgesClient {

  private final ParSeqRestClient _parSeqRestClient;

  private static final MemberBadgesBuilders MEMBER_BADGES_BUILDERS = new MemberBadgesBuilders();

  public MemberBadgesClient(ParSeqRestClient parSeqRestClient) {
    this._parSeqRestClient = parSeqRestClient;
  }

  /**
   * Gets all badges belonging to specified member
   * @param memberId ID of member to be queries
   * @return All badges specified member possesses
   */
  public Task<MemberBadges> getMemberBadges(@NonNull Long memberId) {
    return _parSeqRestClient.createTask(MEMBER_BADGES_BUILDERS
        .get()
        .id(new MemberUrn(memberId)).build())
        .map(Response::getEntity);
  }

  /**
   * Gets member badges belonging to specified members
   * @param memberIds IDs of members to fetch the member badges for
   * @return Task with a mapping of String memberUrns to BadgeTypeArray with badges the members have
   */
  public Task<Map<String, BadgeTypeArray>> batchGetMemberBadges(@NonNull List<Long> memberIds) {
    return _parSeqRestClient.createTask(MEMBER_BADGES_BUILDERS.batchGet()
        .ids(memberIds.stream().map(id -> new MemberUrn(id)).collect(Collectors.toList()))
        .build())
        .map(resp -> resp.getEntity()
            .getResults()
            .entrySet()
            .stream()
            .filter(entry -> entry.getValue().hasBadges())
            .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getBadges())));
  }
}
