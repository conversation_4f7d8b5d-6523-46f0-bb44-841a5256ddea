package com.linkedin.sales.client.pymk;

import com.linkedin.common.ScoredEntity;
import com.linkedin.common.ScoredEntityArray;
import com.linkedin.common.TrackingId;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.data.ByteString;
import com.linkedin.parseq.Task;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsKey;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsValue;
import com.linkedin.salespymk.GroupReason;
import com.linkedin.salespymk.PymkRecommendationsGroup;
import com.linkedin.venice.client.store.AvroSpecificStoreClient;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesPymkVeniceClient {
  private static final Logger LOG = LoggerFactory.getLogger(SalesPymkVeniceClient.class);
  private final AvroSpecificStoreClient<PYMKFreemiumRecommendationsKey, PYMKFreemiumRecommendationsValue> _veniceClient;
  private final int _timeoutMs;

  public SalesPymkVeniceClient(
      AvroSpecificStoreClient<PYMKFreemiumRecommendationsKey, PYMKFreemiumRecommendationsValue> veniceClient, int timeoutMs) {
    _veniceClient = veniceClient;
    _timeoutMs = timeoutMs;
  }

  /**
   * This method is for converting the venice store response to the common data model of ScoredEntityArray.
   * @param memberUrn the member urn we are getting recommendations for.
   * @return Instance of PymkRecommendationsGroup
   */
  public Task<PymkRecommendationsGroup> getFreemiumRecommendations(
      MemberUrn memberUrn, GroupReason groupReason) {
    Long memberId = memberUrn.getIdAsLong();
    Task<PYMKFreemiumRecommendationsValue> veniceClientTask =
        Task.fromCompletionStage(() -> _veniceClient.get(new PYMKFreemiumRecommendationsKey(memberId)));
    PymkRecommendationsGroup blankGroup = new PymkRecommendationsGroup()
        .setRecommendations(new ScoredEntityArray())
        .setGroupReason(groupReason);
    return veniceClientTask.map(pymkFreemiumRecommendationsValue -> {
          if (pymkFreemiumRecommendationsValue == null) {
            LOG.warn("Failed to get FreemiumRecommendationsValue for member: {}", memberId);
            return blankGroup;
          }
          List<ScoredEntity> scoredEntities = pymkFreemiumRecommendationsValue.getRecommendations()
              .stream()
              .map(recommendation -> new ScoredEntity().setScore((float) recommendation.getScore())
                  .setRecommendationTrackingId(convertToCommonTrackingId(recommendation.getRecommendationTrackingId()))
                  .setEntity(new MemberUrn(recommendation.getMemberId())))
              .sorted((p1, p2) -> p2.getScore().compareTo(p1.getScore()))
              .collect(Collectors.toList());

          return new PymkRecommendationsGroup()
              .setRecommendations(new ScoredEntityArray(scoredEntities))
              .setGroupReason(groupReason);
        })
        .withTimeout(_timeoutMs, TimeUnit.MILLISECONDS)
        .recover(e -> {
          LOG.error("Failed to get FreemiumRecommendationsValue for member: {}", memberId, e);
          return blankGroup;
        });
  }

  @SuppressFBWarnings(value = "OCP_OVERLY_CONCRETE_PARAMETER",
      justification = "This helper function is used to convert from Venice TrackingId to common TrackingId")
  private TrackingId convertToCommonTrackingId(com.linkedin.events.common.TrackingId trackingId) {
    return new TrackingId(ByteString.copy(trackingId.bytes()));
  }
}
