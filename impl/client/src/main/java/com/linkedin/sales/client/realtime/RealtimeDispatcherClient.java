package com.linkedin.sales.client.realtime;

import com.linkedin.common.urn.Urn;
import com.linkedin.common.any.AnyRecord;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.template.RecordTemplate;
import com.linkedin.parseq.Task;
import com.linkedin.realtimedispatcher.Event;
import com.linkedin.realtimedispatcher.RealtimeDispatcherEventsRequestBuilders;
import com.linkedin.restli.client.CreateIdRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.common.HttpStatus;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;


public class RealtimeDispatcherClient {
  private final ParSeqRestClient _parSeqRestClient;

  public RealtimeDispatcherClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Publish event to realtime.
   * For successful publish, HTTP 202 is returned.
   */
  public Task<HttpStatus> publishEvent(@NonNull Urn topic, @NonNull RecordTemplate payload,
      @Nullable String publisherTrackingId) {
    Event event = new Event().setPayload(new AnyRecord().setValue(payload))
        .setPublisherTrackingId(publisherTrackingId, SetMode.IGNORE_NULL);
    CreateIdRequest<Long, Event> createRequest =
        new RealtimeDispatcherEventsRequestBuilders().create().addTopicsParam(topic).input(event).build();

    return _parSeqRestClient.createTask(createRequest)
        .map(idResponseResponse -> HttpStatus.fromCode(idResponseResponse.getStatus()));
  }
}
