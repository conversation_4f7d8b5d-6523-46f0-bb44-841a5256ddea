package com.linkedin.sales.client.salesinsights;

import com.linkedin.data.schema.PathSpec;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ActionRequest;
import com.linkedin.restli.client.CreateIdEntityRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.salesinsights.CsvImportTask;
import com.linkedin.salesinsights.SalesInsightsCsvImportTasksGetRequestBuilder;
import com.linkedin.salesinsights.SalesInsightsCsvImportTasksPartialUpdateRequestBuilder;
import com.linkedin.salesinsights.SalesInsightsCsvImportTasksRequestBuilders;
import edu.umd.cs.findbugs.annotations.Nullable;
import org.apache.commons.lang3.ArrayUtils;


/**
 * Client for interacting with {@link CsvImportTask}
 */
public class CsvImportTaskClient {

  private final ParSeqRestClient _parSeqRestClient;

  private static final SalesInsightsCsvImportTasksRequestBuilders CSV_IMPORT_TASKS_REQUEST_BUILDERS =
      new SalesInsightsCsvImportTasksRequestBuilders();

  public CsvImportTaskClient(final ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Create the csv import task
   * @param csvImportTask csv import task to be created
   * @return The ID of the created CSV import task
   */
  public Task<Long> create(CsvImportTask csvImportTask) {
    CreateIdEntityRequest<Long, CsvImportTask> request = CSV_IMPORT_TASKS_REQUEST_BUILDERS
        .createAndGet()
        .input(csvImportTask)
        .build();

    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity)
        .map(IdResponse::getId);
  }

  /**
   * Start the csv import task
   * @param csvImportTaskId the ID of the csv import task to start
   * @return void
   */
  public Task<Void> start(Long csvImportTaskId) {
    ActionRequest<Void> request = CSV_IMPORT_TASKS_REQUEST_BUILDERS
        .actionStart()
        .csvImportTaskIdParam(csvImportTaskId)
        .build();

    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity);
  }

  /**
   * Get csv import task using its ID
   * @param csvImportTaskId the ID of the CSV Import Task
   * @param fields the optional fields to retrieve
   * @return the CSV Import Task
   */
  public Task<CsvImportTask> get(long csvImportTaskId, @Nullable PathSpec... fields) {
    SalesInsightsCsvImportTasksGetRequestBuilder getRequestBuilder = CSV_IMPORT_TASKS_REQUEST_BUILDERS
        .get()
        .id(csvImportTaskId);

    if (ArrayUtils.isNotEmpty(fields)) {
      getRequestBuilder.fields(fields);
    }

    return _parSeqRestClient.createTask(getRequestBuilder.build())
        .map(Response::getEntity);
  }

  /**
   * Update the csv import task
   * @param csvImportTaskId the ID of the CSV Import Task
   * @param csvImportTask task containing the values that need to be updated
   * @return boolean indicating if the update was successful
   */
  public Task<Boolean> update(long csvImportTaskId, CsvImportTask csvImportTask) {
    SalesInsightsCsvImportTasksPartialUpdateRequestBuilder requestBuilder =
        CSV_IMPORT_TASKS_REQUEST_BUILDERS.partialUpdate()
            .id(csvImportTaskId)
            .input(PatchGenerator.diffEmpty(csvImportTask));
    return _parSeqRestClient.createTask(requestBuilder.build())
        .map(response -> response.getStatus() == HttpStatus.S_204_NO_CONTENT.getCode());
  }
}
