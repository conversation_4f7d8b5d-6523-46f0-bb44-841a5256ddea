package com.linkedin.sales.client.salesinsights;

import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.salesinsights.MetricsReport;
import com.linkedin.salesinsights.SalesInsightsMetricsReportsRequestBuilders;


/**
 * Client for interacting with {@link com.linkedin.salesinsights.MetricsReport}
 */
public class SalesInsightsMetricsReportClient {

  private final ParSeqRestClient _parSeqRestClient;

  private static final SalesInsightsMetricsReportsRequestBuilders SALES_INSIGHTS_METRICS_REPORTS_REQUEST_BUILDERS =
      new SalesInsightsMetricsReportsRequestBuilders();

  public SalesInsightsMetricsReportClient(final ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  /**
   * Get the metrics report give the report urn
   * @param metricsReportUrn metrics report urn to retrieve
   * @return metrics report
   */
  public Task<MetricsReport> get(SalesInsightsMetricsReportUrn metricsReportUrn) {
    GetRequest<MetricsReport> request = SALES_INSIGHTS_METRICS_REPORTS_REQUEST_BUILDERS
        .get()
        .id(metricsReportUrn.getIdAsLong())
        .build();

    return _parSeqRestClient.createTask(request)
        .map(Response::getEntity);
  }
}
