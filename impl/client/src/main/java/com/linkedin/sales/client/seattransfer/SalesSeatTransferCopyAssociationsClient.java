package com.linkedin.sales.client.seattransfer;

import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.SourceEntityCriteria;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchCreateIdRequest;
import com.linkedin.restli.client.BatchFindRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CreateIdStatus;
import com.linkedin.sales.admin.SalesSeatTransferCopyAssociationsRequestBuilders;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesSeatTransferCopyAssociationsClient {
  private static final Logger LOG = LoggerFactory.getLogger(SalesSeatTransferCopyAssociationsClient.class);
  private final SalesSeatTransferCopyAssociationsRequestBuilders copyAssociationsClientBuilders = new SalesSeatTransferCopyAssociationsRequestBuilders();
  private final ParSeqRestClient _restClient;
  private final RateLimiter _rateLimiter;
  private static final PathSpec[] COPY_ASSOCIATION_FIELDS = new PathSpec[]{OwnershipTransferCopyAssociation.fields().sourceEntity()};
  // refer the description for https://github.com/linkedin-multiproduct/lss-mt/pull/3083
  // for the reasoning for setting ALLOWED_CALLS_PER_SECOND to 1.5
  private static final double ALLOWED_CALLS_PER_SECOND = 1.5;
  private static final int BATCH_SIZE = 100;

  public SalesSeatTransferCopyAssociationsClient(ParSeqRestClient restClient) {
    _restClient = restClient;
    // allow ALLOWED_CALLS_PER_SECOND calls per second across all threads
    _rateLimiter = RateLimiter.create(ALLOWED_CALLS_PER_SECOND);
  }

  /**
   * Finds copy association records for LSS Pages entities involved in seat transfer: SalesLead, SalesAccount, Note, Custom lists, LeadAccountAssociation
   * @param salesEntityUrns list of SalesLeadsUrns
   * @param targetContract target contract URN
   * @return list of discovered association records
   */
  public Task<List<OwnershipTransferCopyAssociation>> findPreviousTransfers(List<Urn> salesEntityUrns,
      ContractUrn targetContract) {
    List<List<Urn>> batches = getUrnBatches(salesEntityUrns);

    if (batches.isEmpty()) {
      return Task.value(new ArrayList<>());
    }
    Task<List<OwnershipTransferCopyAssociation>> currentTask = batchFindBySalesEntityUrnAndTargetContract(
        batches.get(0), targetContract);

    List<OwnershipTransferCopyAssociation> accumulator = new ArrayList<>();
    for (int i = 1; i < batches.size(); i++) {
      int k = i;
      currentTask = currentTask.flatMap(ownershipTransferCopyAssociations -> {
        accumulator.addAll(ownershipTransferCopyAssociations);
        return batchFindBySalesEntityUrnAndTargetContract(batches.get(k), targetContract);
      });
    }

    return currentTask.map(ownershipTransferCopyAssociations -> {
      accumulator.addAll(ownershipTransferCopyAssociations);
      if (!ownershipTransferCopyAssociations.isEmpty()) {
        LOG.info("Accumulator for request has {} new copyAssociations for urns similar to {}",
            accumulator.size(),
            ownershipTransferCopyAssociations.get(0)
        );
      } else {
        LOG.info("No previous ownershipTransferCopyAssociations found for targetContract {} for urns such as {}",
            targetContract, salesEntityUrns.get(0));
      }
      return accumulator;
    });
  }

  /**
   * Creates entity copy associations for a source and target seat for an entity type to ensure idempotency
   * @param ownershipTransferCopyAssociations List of copy association records for a target and source seat
   * @return
   */
  public Task<List<Long>> createCopyAssociations(List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations) {
    List<List<OwnershipTransferCopyAssociation>> batches = getOwnershipTransferCopyAssociationsBatches(ownershipTransferCopyAssociations);
    if (batches.isEmpty()) {
      return Task.value(new ArrayList<>());
    }
    Task<List<Long>> currentTask = batchCreate(batches.get(0));

    List<Long> accumulator = new ArrayList<>();
    for (int i = 1; i < batches.size(); i++) {
      int k = i;
      currentTask = currentTask.flatMap(associationIds -> {
        accumulator.addAll(associationIds);
        return batchCreate(batches.get(k));
      });
    }

    return currentTask.map(associations -> {
      accumulator.addAll(associations);
      if (!ownershipTransferCopyAssociations.isEmpty()) {
        OwnershipTransferCopyAssociation firstTransferRecord = ownershipTransferCopyAssociations.get(0);
        LOG.info("Accumulator for request {} has {} new copyAssociations of type {} for sourceSeat {}",
            firstTransferRecord.getOwnershipTransferRequest().getId(),
            accumulator.size(),
            firstTransferRecord.getOwnershipTransferEntityType(),
            firstTransferRecord.getSourceSeat()
            );
      }
      return accumulator;
    });
  }

  public Task<List<Long>> batchCreate(@NonNull List<OwnershipTransferCopyAssociation> seatTransferCopyAssociations) {
    BatchCreateIdRequest<Long, OwnershipTransferCopyAssociation> copyAssociationsBatchCreateTask =
        copyAssociationsClientBuilders.batchCreate().inputs(seatTransferCopyAssociations).build();
    _rateLimiter.acquire();
    return _restClient.createTask(copyAssociationsBatchCreateTask)
        .map(response -> response.getEntity()
            .getElements()
            .stream()
            .map(CreateIdStatus::getKey)
            .collect(Collectors.toList()));
  }

  public Task<List<OwnershipTransferCopyAssociation>> batchFindBySalesEntityUrnAndTargetContract(
      @NonNull List<Urn> sourceEntityUrns, @NonNull ContractUrn contractUrn) {
    List<SourceEntityCriteria> sourceEntityCriteriaList = sourceEntityUrns.stream()
        .map(sourceEntityUrn -> new SourceEntityCriteria().setSourceEntity(sourceEntityUrn))
        .collect(Collectors.toList());
    BatchFindRequest<OwnershipTransferCopyAssociation> batchFindTask =
        copyAssociationsClientBuilders.batchFindBySourceEntityAndTargetContractCriteria()
            .sourceEntityCriteriaListParam(sourceEntityCriteriaList)
            .targetContractParam(contractUrn)
            .fields(COPY_ASSOCIATION_FIELDS)
            .build();
    _rateLimiter.acquire();
    return _restClient.createTask(batchFindTask)
        .map(Response::getEntity)
        .map(batchCollectionResponse -> batchCollectionResponse.getResults().stream()
            .flatMap(batchFinderResult -> batchFinderResult.getElements().stream())
            .collect(Collectors.toList()));
  }

  public static List<List<Urn>> getUrnBatches(List<Urn> originalList) {
    return Lists.partition(originalList, Math.max(BATCH_SIZE, 1));
  }

  static List<List<OwnershipTransferCopyAssociation>> getOwnershipTransferCopyAssociationsBatches(
      List<OwnershipTransferCopyAssociation> originalList) {
    return Lists.partition(originalList, Math.max(BATCH_SIZE, 1));
  }
}
