package com.linkedin.sales.client.seattransfer;

import com.linkedin.ownershiptransfer.OwnershipTransferKey;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestliClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.sales.admin.SalesSeatTransferRequestsRequestBuilders;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Client to interact with /salesSeatTransferRequests.
 *
 */
public class SalesSeatTransferRequestsClient {
  private static final Logger LOG = LoggerFactory.getLogger(SalesSeatTransferRequestsClient.class);
  private static final SalesSeatTransferRequestsRequestBuilders BUILDER =
      new SalesSeatTransferRequestsRequestBuilders();
  private final ParSeqRestliClient _restClient;

  public SalesSeatTransferRequestsClient(ParSeqRestliClient restClient) {
    _restClient = restClient;
  }

  /**
   * Retrieves the ownership transfer request corresponding to the specified key.
   *
   * @param key the key to fetch the request object by.
   * @return the ownership transfer request object if it exists.
   */
  public Task<Optional<OwnershipTransferRequest>> get(OwnershipTransferKey key) {
    GetRequest<OwnershipTransferRequest> getRequest = BUILDER.get()
        .id(new ComplexResourceKey<>(key, new EmptyRecord()))
        .build();
    return _restClient.createTask(getRequest)
        .map(Response::getEntity)
        .map(Optional::of)
        .recoverWith(ex -> {
          LOG.error("Unable to fetch record for key: {}", key, ex);
          return Task.failure(ex);
        });
  }
}
