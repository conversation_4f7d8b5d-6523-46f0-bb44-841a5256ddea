package com.linkedin.sales.client;

import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.BatchCreateIdResponse;
import com.linkedin.restli.common.CreateIdStatus;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.internal.common.AllProtocolVersions;
import com.linkedin.sales.test.MockUtils;
import com.linkedin.sales.test.RestliUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.testng.Assert;
import org.testng.annotations.Test;


public class EmailAddressClientTest extends BaseEngineParTest {

  @Test
  public void testFindEmailAddressUrns() throws Exception {
    List<String> emailStrings = Arrays.asList("<EMAIL>");
    Long emailAddressUrnValue = 1234L;
    BatchCreateIdResponse<Long> result = new BatchCreateIdResponse<Long>(Arrays.asList(
        new CreateIdStatus<>(200, emailAddressUrnValue, null, AllProtocolVersions.RESTLI_PROTOCOL_2_0_0.getProtocolVersion())));
    Response<BatchCreateIdResponse<Long>> response = RestliUtils.newSuccessResponse(result);
    EmailAddressClient emailAddressClient =
        new EmailAddressClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response), getEngine());

    Task<List<CreateIdStatus<Long>>> task = emailAddressClient.batchCreate(emailStrings);

    List<CreateIdStatus<Long>> taskResult = super.runAndWait(task);

    Assert.assertNotNull(taskResult);
    Assert.assertEquals(taskResult.size(), 1);
    Assert.assertEquals(taskResult.get(0).getKey(), emailAddressUrnValue);
  }

  @Test
  public void testFindEmailAddressUrnsCreated() throws Exception {
    List<String> emailStrings = Arrays.asList("<EMAIL>");
    Long emailAddressUrnValue = 1234L;
    BatchCreateIdResponse<Long> result = new BatchCreateIdResponse<Long>(Arrays.asList(
        new CreateIdStatus<>(HttpStatus.S_201_CREATED.getCode(), emailAddressUrnValue, null, AllProtocolVersions.RESTLI_PROTOCOL_2_0_0.getProtocolVersion())));
    Response<BatchCreateIdResponse<Long>> response = RestliUtils.newSuccessResponse(result);
    EmailAddressClient emailAddressClient =
        new EmailAddressClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response), getEngine());

    Map<String, EmailAddressClient.CreateOrGetResult> resultMap = runAndWait(emailAddressClient.batchCreateOrGetEmailAddressUrns(emailStrings));

    Assert.assertNotNull(resultMap);
    Assert.assertEquals(resultMap.size(), 1);
    Assert.assertEquals(resultMap.get(emailStrings.get(0)).getEmailAddressUrnIfAny().getIdAsLong(), emailAddressUrnValue);
  }

  @Test
  public void testFindEmailAddressUrnsExisting() throws Exception {
    List<String> emailStrings = Arrays.asList("<EMAIL>");
    Long emailAddressUrnValue = 1234L;
    BatchCreateIdResponse<Long> result = new BatchCreateIdResponse<Long>(Arrays.asList(
        new CreateIdStatus<>(HttpStatus.S_409_CONFLICT.getCode(), emailAddressUrnValue, null, AllProtocolVersions.RESTLI_PROTOCOL_2_0_0.getProtocolVersion())));
    Response<BatchCreateIdResponse<Long>> response = RestliUtils.newSuccessResponse(result);
    EmailAddressClient emailAddressClient =
        new EmailAddressClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response), getEngine());

    Map<String, EmailAddressClient.CreateOrGetResult> resultMap = runAndWait(emailAddressClient.batchCreateOrGetEmailAddressUrns(emailStrings));

    Assert.assertNotNull(resultMap);
    Assert.assertEquals(resultMap.size(), 1);
    Assert.assertEquals(resultMap.get(emailStrings.get(0)).getEmailAddressUrnIfAny().getIdAsLong(), emailAddressUrnValue);
  }
}
