package com.linkedin.sales.client;

import com.linkedin.common.any.AnyRecord;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.handle.EmailAddress;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.CreateIdRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.sales.test.MockUtils;
import com.linkedin.sales.test.ParSeqRestClientMockBuilder;
import com.linkedin.sales.test.RestliUtils;
import org.testng.annotations.Test;


/**
 * <AUTHOR>
 */
public class EmailClientTest extends BaseEngineParTest {


  @Test
  public void test_sendEmailByEmailStr_normal() {
    //prepare mocks
    Response<IdResponse<String>> response = RestliUtils.newSuccessResponse(new IdResponse("email_resp_001"));
    EmailClient emailClient = new EmailClient(MockUtils.mockParSeqRestClientCreateTaskWithAny(response));

    //prepare args & perform test
    String emailAddress = "<EMAIL>";
    String templateKey = "email_client_test";

    AnyRecord anyRecord = new AnyRecord();

    // perform subject function
    EmailResult emailResult = super.runAndWait(emailClient.sendEmail(
        new EmailContext(templateKey, anyRecord, new MemberUrn(12345L)),
        emailAddress));

    //asserts
    System.out.println("emailResult: " + emailResult);
  }


  @Test
  public void test_sendEmailByEmailAddressUrn_normal() {
    Response<IdResponse<String>> response = RestliUtils.newSuccessResponse(new IdResponse("email_resp_001"));
    Response<EmailAddress> emailAddressResponse = RestliUtils.newSuccessResponse(new EmailAddress()
        .setId(1234L)
        .setEmailAddress("<EMAIL>"));


    EmailClient emailClient = new EmailClient(ParSeqRestClientMockBuilder.getInstance()
            .mockCreateTask(GetRequest.class, Task.value(emailAddressResponse))
            .mockCreateTask(CreateIdRequest.class, Task.value(response)).build());

    //prepare args & perform test
    EmailAddressUrn emailAddressUrn = new EmailAddressUrn(1234L);
    String templateKey = "email_client_test";

    AnyRecord anyRecord = new AnyRecord();

    // perform subject function
    EmailResult emailResult = super.runAndWait(emailClient.sendEmail(
            new EmailContext(templateKey, anyRecord, new MemberUrn(12345L)),
            emailAddressUrn, new MemberUrn(12L)));

    //asserts
    System.out.println("emailResult: " + emailResult);
  }

}
