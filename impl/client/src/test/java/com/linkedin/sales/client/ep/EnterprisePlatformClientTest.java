package com.linkedin.sales.client.ep;

import com.linkedin.common.urn.CrmUserUrn;
import com.linkedin.common.urn.CsUserUrn;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.CrmUser;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResult;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResultArray;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyStatus;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.bulk.BulkLicenseAssignmentContext;
import com.linkedin.enterprise.identity.Profile;
import com.linkedin.enterprise.identity.ProfileKey;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.sales.test.MockUtils;
import com.linkedin.sales.test.RestliUtils;
import com.linkedin.util.Pair;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.apache.http.HttpStatus;
import org.testng.Assert;
import org.testng.annotations.Test;


public class EnterprisePlatformClientTest extends BaseEngineParTest {
  @Test
  public void testFindEnterpriseProfileByEmail() throws Exception {
    Profile profile = new Profile();
    profile.setPreferredFirstName("MyFirstName");
    profile.setPreferredLastName("MyLastName");
    Response<CollectionResponse> response = RestliUtils.newSuccessResponse(new CollectionResponse(profile.getClass()));
    response.getEntity().getElements().add(profile);
    EnterprisePlatformClient enterprisePlatformClient =
        new EnterprisePlatformClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response));

    EnterpriseAccountUrn accountUrn = EnterpriseAccountUrn.deserialize("urn:li:enterpriseAccount:1");
    EnterpriseApplicationInstanceUrn applicationInstanceUrn = new EnterpriseApplicationInstanceUrn(accountUrn, 123L);
    Urn viewer = CsUserUrn.deserialize("urn:li:csUser:1");
    String emailAddressStr = "<EMAIL>";

    Task<List<Profile>> task = enterprisePlatformClient.findEnterpriseProfileByEmail(applicationInstanceUrn, emailAddressStr, viewer);
    List<Profile> profiles = super.runAndWait(task);

    Assert.assertNotNull(profiles);
    Assert.assertEquals(profiles.size(), 1);
    Assert.assertEquals(profiles.get(0), profile);
  }

  @Test
  public void testAssignLicenses() throws Exception {
    Urn viewer = CsUserUrn.deserialize("urn:li:csUser:1");
    EnterpriseProfileUrn profileUrn = EnterpriseProfileUrn.deserialize("urn:li:enterpriseProfile:(urn:li:enterpriseAccount:1,111)");

    BulkActionBatchInputKeyResult keyResult = new BulkActionBatchInputKeyResult();
    keyResult.setInputKey(profileUrn);
    keyResult.setStatus(BulkActionBatchInputKeyStatus.SUCCEEDED);
    BulkActionBatchInputKeyResultArray keyResults = new BulkActionBatchInputKeyResultArray();
    keyResults.add(keyResult);
    BulkActionBatchResult batchResult = new BulkActionBatchResult();
    batchResult.setInputKeyResults(keyResults);

    Response<BulkActionBatchResult> response = RestliUtils.newSuccessResponse(batchResult);
    EnterprisePlatformClient enterprisePlatformClient =
        new EnterprisePlatformClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response));

    BulkLicenseAssignmentContext licenseAssignmentContext = new BulkLicenseAssignmentContext();
    Task<BulkActionBatchResult> task = enterprisePlatformClient.assignLicenses(Arrays.asList(profileUrn), licenseAssignmentContext, viewer);

    BulkActionBatchResult result = super.runAndWait(task);

    Assert.assertNotNull(result);
    BulkActionBatchInputKeyResultArray actualResults = result.getInputKeyResults();
    Assert.assertEquals(actualResults.size(), 1);
    Assert.assertEquals(actualResults.get(0).getInputKey(), profileUrn);
  }

  @Test
  public void testValidateLicenseAssignmentBulkActionContext() throws Exception {
    Urn viewer = CsUserUrn.deserialize("urn:li:csUser:1");

    Response<Void> response = RestliUtils.newVoidResponse(HttpStatus.SC_OK, null);
    EnterprisePlatformClient enterprisePlatformClient =
        new EnterprisePlatformClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response));

    BulkLicenseAssignmentContext licenseAssignmentContext = new BulkLicenseAssignmentContext();
    Task<Void> task = enterprisePlatformClient.validateLicenseAssignmentBulkActionContext(licenseAssignmentContext, viewer);

    super.runAndWait(task);
  }

  @Test
  public void testCreateProfileFromCrmUser() throws Exception {
    CrmUserUrn crmUserUrn = CrmUserUrn.deserialize("urn:li:crmUser:(urn:li:crmInstance:(DYNAMICS,salesnavdev),ecf67a6e-70aa-49ed-b0d9-b7d4783ca2cd)");
    String firstName = "MyFirstName";
    String lastName = "MyLastName";

    EmailAddressUrn emailAccountUrn = EmailAddressUrn.deserialize("urn:li:emailAddress:111111");
    ProfileKey profileKey = new ProfileKey();
    profileKey.setProfileId(123456L);
    ComplexResourceKey<ProfileKey, EmptyRecord> complexResourceKey = new ComplexResourceKey<>(profileKey, null);

    IdResponse<ComplexResourceKey<ProfileKey, EmptyRecord>> idResponse = new IdResponse<>(complexResourceKey);

    Response<IdResponse> response = RestliUtils.newSuccessResponse(idResponse);
    EnterprisePlatformClient enterprisePlatformClient =
        new EnterprisePlatformClient(MockUtils.mockParSeqRestClientCreateTaskWithAnyCustomResponse(response));

    EnterpriseAccountUrn accountUrn = EnterpriseAccountUrn.deserialize("urn:li:enterpriseAccount:1");
    EnterpriseApplicationInstanceUrn applicationInstanceUrn = new EnterpriseApplicationInstanceUrn(accountUrn, 123L);
    Urn viewer = CsUserUrn.deserialize("urn:li:csUser:1");

    Task<Pair<CrmUserUrn, ProfileKey>> task = enterprisePlatformClient.createEnterpriseProfileFromCrmUser(crmUserUrn,
        firstName, lastName, emailAccountUrn, applicationInstanceUrn, viewer);
    Pair<CrmUserUrn, ProfileKey> result = super.runAndWait(task);

    Assert.assertNotNull(result);
    Assert.assertEquals(result.getFirst(), crmUserUrn);
    Assert.assertEquals(result.getSecond(), profileKey);
  }
}
