package com.linkedin.sales.client.ep;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.enterprise.EnterpriseProfileActivationLinksDoGenerateRequestBuilder;
import com.linkedin.enterprise.EnterpriseProfileActivationLinksRequestBuilders;
import com.linkedin.enterprise.identity.ActivationLink;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ActionRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Request;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.client.testutils.MockResponseBuilder;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.*;
import static org.testng.Assert.*;

public class EnterpriseProfileActivationLinksClientTest {
  private static final long ACCOUNT_ID = 1L;
  private static final long PROFILE_ID = 111L;
  private static final long APPLICATION_INSTANCE_ID = 1L;
  private static final long TTL_SECONDS = 100L;
  private static final EnterpriseAccountUrn ENTERPRISE_ACCOUNT =
      new EnterpriseAccountUrn(ACCOUNT_ID);
  private static final EnterpriseProfileUrn enterpriseProfile =
      new EnterpriseProfileUrn(ENTERPRISE_ACCOUNT, PROFILE_ID);
  private static final String REDIRECT_URL_NAME = "linkedin.com/redirect";
  private static final Url REDIRECT_URL = new Url(REDIRECT_URL_NAME);
  private static final String APPLICATION_NAME = "businessManager";
  private static final EnterpriseApplicationUrn ENTERPRISE_APPLICATION =
      new EnterpriseApplicationUrn(APPLICATION_NAME);

  private EnterpriseProfileActivationLinksClient _epActivationLinksClient;

  private @Mock ParSeqRestClient _parSeqRestClient;

  @BeforeMethod
  public void setup() {
    initMocks(this);
    _epActivationLinksClient = new EnterpriseProfileActivationLinksClient(_parSeqRestClient);
  }

  @Test
  public void testGenerateActivationLink() {
    Long ttlSeconds = TTL_SECONDS;
    EnterpriseApplicationInstanceUrn enterpriseApplicationInstance =
        new EnterpriseApplicationInstanceUrn(ENTERPRISE_ACCOUNT, APPLICATION_INSTANCE_ID);
    EnterpriseProfileActivationLinksDoGenerateRequestBuilder expectedRequest =
        new EnterpriseProfileActivationLinksRequestBuilders()
            .actionGenerate()
            .applicationUrnParam(ENTERPRISE_APPLICATION)
            .enterpriseApplicationInstanceUrnParam(enterpriseApplicationInstance)
            .enterpriseProfileUrnParam(enterpriseProfile)
            .redirectUrlParam(REDIRECT_URL)
            .ttlSecondsParam(ttlSeconds)
            .viewerParam(ENTERPRISE_APPLICATION);
    ActivationLink expectedResult = new ActivationLink().setUrl(REDIRECT_URL);
    Response<ActivationLink> expectedResponse =
        new MockResponseBuilder<Object, ActivationLink>().setEntity(expectedResult).build();
    when(_parSeqRestClient.createTask(expectedRequest.build()))
        .thenReturn(Task.value(expectedResponse));
    _epActivationLinksClient.generateCheckpointEnterpriseLoginUrl(
        ENTERPRISE_APPLICATION,
        enterpriseApplicationInstance,
        enterpriseProfile,
        REDIRECT_URL,
        ttlSeconds,
        ENTERPRISE_APPLICATION);
    ArgumentCaptor<ActionRequest> activationLinkRequestCaptor =
        ArgumentCaptor.forClass(ActionRequest.class);
    verify(_parSeqRestClient).createTask(activationLinkRequestCaptor.capture());
    Request capturedRequest = activationLinkRequestCaptor.getValue();
    assertEquals(capturedRequest, expectedRequest.build());
  }
}
