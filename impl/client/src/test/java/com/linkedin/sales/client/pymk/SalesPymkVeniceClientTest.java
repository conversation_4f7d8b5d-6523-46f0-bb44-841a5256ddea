package com.linkedin.sales.client.pymk;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.common.ScoredEntity;
import com.linkedin.common.TrackingId;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsKey;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsValue;
import com.linkedin.sales.pymk.Recommendation;
import com.linkedin.sales.service.utils.PYMKUtils;
import com.linkedin.salespymk.GroupReason;
import com.linkedin.salespymk.PymkRecommendationsGroup;
import com.linkedin.venice.client.store.AvroSpecificStoreClient;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


public class SalesPymkVeniceClientTest extends BaseEngineParTest {
  private static final MemberUrn TEST_MEMBER_URN = new MemberUrn(123L);
  private static final MemberUrn VIEWEE_URN = new MemberUrn(456L);
  private static final Float SCORE = 0.9F;
  private static final TrackingId TRACKING_ID = PYMKUtils.createTrackingId();
  private static final com.linkedin.events.common.TrackingId EVENT_TRACKING_ID =
      new com.linkedin.events.common.TrackingId(TRACKING_ID.data().copyBytes());

  @Mock
  private AvroSpecificStoreClient<PYMKFreemiumRecommendationsKey, PYMKFreemiumRecommendationsValue> _veniceClient;
  private SalesPymkVeniceClient _pymkVeniceClient;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _pymkVeniceClient = new SalesPymkVeniceClient(_veniceClient, 1000);
  }

  @Test(description = "Test that it converts the pymk recommendations from venice correctly")
  public void testGetFreemiumRecommendations() throws Exception {
    PYMKFreemiumRecommendationsKey mockKey = new PYMKFreemiumRecommendationsKey();
    mockKey.setMemberId(TEST_MEMBER_URN.getIdAsLong());

    PYMKFreemiumRecommendationsValue mockRecord = new PYMKFreemiumRecommendationsValue();
    List<Recommendation> recommendations = ImmutableList.of(
        Recommendation.newBuilder()
            .setScore(SCORE)
            .setRecommendationTrackingId(EVENT_TRACKING_ID)
            .setMemberId(VIEWEE_URN.getIdAsLong())
            .build()
    );
    mockRecord.setRecommendations(recommendations);
    when(_veniceClient.get(mockKey)).thenReturn(CompletableFuture.completedFuture(mockRecord));

    PymkRecommendationsGroup results = runAndWait(_pymkVeniceClient.getFreemiumRecommendations(TEST_MEMBER_URN, GroupReason.DECISION_MAKERS));

    Assert.assertEquals(results.getGroupReason(), GroupReason.DECISION_MAKERS);

    ScoredEntity recommendation = results.getRecommendations().get(0);
    Assert.assertEquals(recommendation.getEntity(), VIEWEE_URN);
    Assert.assertEquals(recommendation.getRecommendationTrackingId(), TRACKING_ID);
    Assert.assertEquals(recommendation.getScore(), SCORE);
  }
}
