ext.apiMpName = 'lss-mt-api'
ext.apiMpApiDir = 'lss-mt-api'
ext.apiProject = project(':rest-api')

apply plugin: 'li-java'
apply plugin: 'li-urnUtils'

dependencies {
  api project(':database')
  api spec.external.'spotbugs-annotations'
  api spec.product.'espresso-pub'.'espresso-client-impl'
  api spec.product.'gaap-api'.'task-restClient'
  api spec.product.'gaap-api'.script
  api spec.product.'parseq-espresso-client'.'parseq-espresso-client-factory'
  api spec.product.'parseq-espresso-client'.'parseq-espresso-client-impl'
  api spec.product.'gaap-tasks-client-java'.'polling-client'
  api spec.product.'gaap-tasks-client-java'.'polling-client-factory'
  api spec.product.'lss-mt-api'.'lss-mt-api-dataTemplate'
  api spec.product.money.'money-util'
  api spec.external.'restli-client-parseq'
  api spec.external.'restli-server'
  api spec.external.'restli-common'
  api spec.product.identity.'profile-rest-api-restClient'
  api spec.product.'resource-identity'.'gradle-urn-util'
  api spec.product.talent.'db-mapper'
  // needed by db-mapper
  api spec.external.cglib
  api spec.product.'lss-common'.'common-utils'
  api spec.product.'lss-common'.'crm-clients'

  urnModels spec.product.models.models
  urnModelsCompile spec.product.models.'models-dataTemplate'

  api spec.product.'lighthouse-bps-api'.'rest-api-restClient'
  api spec.product.'vector-oss'.'vector-common-data-templates-restClient'
}
