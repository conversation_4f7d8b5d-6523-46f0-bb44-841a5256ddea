package com.linkedin.sales.ds.db;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Direct DB access to CrmDataValidationExportJob
 */
public class CrmDataValidationExportJobDB {
  public static final String DB = "LssCrmDataValidation";
  public static final String TABLE = "CrmDataValidationExportJob";
  public static final int SCHEMA_VERSION = 4;

  private static final SpecificDatumReader<CrmDataValidationExportJob> CRM_DATA_VALIDATION_EXPORT_JOB_READER =
      new SpecificDatumReader<>(CrmDataValidationExportJob.SCHEMA$);
  private static final SpecificDatumWriter<CrmDataValidationExportJob> CRM_DATA_VALIDATION_EXPORT_JOB_WRITER =
      new SpecificDatumWriter<>(CrmDataValidationExportJob.SCHEMA$);
  private static final int RECORD_TTL_IN_DAYS = 7;

  private final ParSeqEspressoClient _parSeqEspressoClient;

  private final static Logger LOG = LoggerFactory.getLogger(CrmDataValidationExportJobDB.class);

  public CrmDataValidationExportJobDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Creates a data validation export job
   * @param crmInstanceId the CRM instance Id of the exported data, which is an identifier for the requester.
   * @param exportJob export job to create
   * @return The ID of the newly-created job
   */
  public Task<Long> create(@NonNull String crmInstanceId, @NonNull CrmDataValidationExportJob exportJob) {
    byte[] serializedData = EspressoUtil.serializeAvroRecord(CRM_DATA_VALIDATION_EXPORT_JOB_WRITER, exportJob);
    PutRequest.Builder builder = PutRequest.builder()
        .setDatabase(DB)
        .setTable(TABLE)
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, serializedData)
        .setKey(crmInstanceId)
        .setExpires(Date.from(Instant.now().plus(RECORD_TTL_IN_DAYS, ChronoUnit.DAYS))) /*Auto purge the record*/;

    return _parSeqEspressoClient.execute(builder.build()).map(response -> {
      if (response.getResponseStatus() != ResponseStatus.CREATED) {
        throw new RestLiServiceException(HttpStatus.fromCode(response.getResponseStatus().getCode()),
            String.format("Unexpected response from espresso while create(%s) for %s: %s", exportJob, crmInstanceId,
                response));
      }
      assert !response.getParts().isEmpty();
      String[] keys = response.getPart(0).getContentLocation().getKey();
      assert keys.length == 2;
      return Long.valueOf(keys[1]);
    });
  }

  /**
   * Get the avro record by keys
   * @param crmInstanceId the CRM instance Id of the exported data, which is an identifier for the requester.
   * @param jobId id of the export job
   * @return the deserialized avro record for the export job
   */
  public Task<CrmDataValidationExportJob> get(@NonNull String crmInstanceId, long jobId) {
    GetRequest.Builder builder = GetRequest.builder()
        .setDatabase(DB)
        .setTable(TABLE)
        .setKey(crmInstanceId, String.valueOf(jobId))
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION);

    return _parSeqEspressoClient.execute(builder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        assert !response.getParts().isEmpty();
        return createAvroCrmDataValidationExportJob(response.getPart(0));
      } else {
        throw new RestLiServiceException(HttpStatus.fromCode(response.getResponseStatus().getCode()),
            String.format("Unexpected response from espresso while performing get(%s, %s): %s", crmInstanceId, jobId,
                response));
      }
    });
  }

  /**
   * Find the Espresso documents created based on crmInstanceId
   * @param crmInstanceId crmInstanceId
   * @return Task of List of CrmDataValidationExportJob
   */
  public Task<List<CrmDataValidationExportJob>> find(@NonNull String crmInstanceId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB)
        .setTable(TABLE)
        .setKey(crmInstanceId)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        assert !response.getParts().isEmpty();
        return response.getParts()
            .stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .map(this::createAvroCrmDataValidationExportJob)
            .collect(Collectors.toList());
      } else if (response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        LOG.warn("No document found for crm instance id {}", crmInstanceId);
        return Collections.<CrmDataValidationExportJob>emptyList();
      } else {
        throw new RestLiServiceException(HttpStatus.fromCode(response.getResponseStatus().getCode()),
            String.format("Unexpected response from espresso while performing find(%s): %s", crmInstanceId, response));
      }
    }).onFailure(t -> LOG.error("Failed to find Espresso document", t));
  }

  @NonNull
  private CrmDataValidationExportJob createAvroCrmDataValidationExportJob(@NonNull GetResponse.Part part) {
    return EspressoUtil.deserializeSingleAvroRecord(CRM_DATA_VALIDATION_EXPORT_JOB_READER, part);
  }
}
