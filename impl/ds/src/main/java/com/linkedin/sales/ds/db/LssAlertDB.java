package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.EntityAlert;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This is the class to directly talk to espresso Database LssAlert
 */
public class LssAlertDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssAlertDB.class);

  static final String DB_LSS_ALERT = "LssAlert";
  static final String TABLE_ENTITY_ALERT = "EntityAlert";
  private static final int SCHEMA_VERSION = 1;

  private static final String ALERT_FIELD_ALERT_TYPE = "alertType";
  private static final String ALERT_FIELD_CONTENT_URN = "contentUrn";
  private static final String ALERT_FIELD_CREATED_TIME = "createdTime";
  private static final String QUERY_DELIMITER_OR = " OR ";
  private static final String DEFAULT_TIME_RANGE = String.format("[%d TO %d]", 0, Long.MAX_VALUE);

  private static final SpecificDatumReader<EntityAlert> ALERT_READER = new SpecificDatumReader<>(EntityAlert.SCHEMA$);

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure */
        ResponseStatus.GATEWAY_TIMEOUT /* Gateway timed out */));
    // As P95 get/write/delete/query are 10 ms at most. We think 250 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(250L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssAlertDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

   /**
    * Create an alert entry on an entity, will only be used from samza job lss-alerts-consumer
    * @param entityUrn the subject of the alert
    * @param entityAlert the alert body
    * @param expireAt the expiring time
    * @return HTTP status we want the clients to receive, otherwise exception will be thrown
   */
  public Task<HttpStatus> createAlert(@NonNull Urn entityUrn, @NonNull EntityAlert entityAlert, @NonNull Long expireAt) {
    if (entityAlert.alertType == null || entityAlert.contentUrn == null) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Required field alertType and contentUrn.");
    }

    // Can not use AVRO_BINARY, as the current serialization requires avro 1.4 which is not in samza mp
    String contentJson = getOrderedJsonString(entityAlert);
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_ALERT)
        .setTable(TABLE_ENTITY_ALERT)
        .setKey(entityUrn.toString())
        .setContent(ContentType.AVRO_JSON, SCHEMA_VERSION, contentJson)
        .setExpires(new Date(expireAt))
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else {
        String errMsg = String.format("Unexpected response code when creating alert for %s, %s", entityUrn, putResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * This could help preserve the order in json so that we could use avro/json during espresso CREATE operations.
   * Normally we use avro/binary for creation, but this one needs to be used by samza and have an avro version conflict.
   * @param entityAlert the alert body
   * @return the json string that preserves order used for espresso PUT request
   */
  private String getOrderedJsonString(EntityAlert entityAlert) {
    StringBuilder sb = new StringBuilder();
    sb.append("{\"").append(ALERT_FIELD_ALERT_TYPE).append("\":\"").append(entityAlert.alertType).append('\"');
    sb.append(",\"").append(ALERT_FIELD_CONTENT_URN).append("\":\"").append(entityAlert.contentUrn).append('\"');
    sb.append(",\"").append(ALERT_FIELD_CREATED_TIME).append("\":").append(entityAlert.createdTime).append('}');
    return sb.toString();
  }

  /**
   * Delete an alert entry on an entity
   * @param entityUrn the subject of the alert
   * @param alertId the local-incremental id of the alert
   * @return HTTP status we want the clients to receive, otherwise exception will be thrown
   */
  public Task<HttpStatus> deleteAlert(@NonNull Urn entityUrn, @NonNull Long alertId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_ALERT)
        .setTable(TABLE_ENTITY_ALERT)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(entityUrn.toString(), alertId.toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_204_NO_CONTENT;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return HttpStatus.S_404_NOT_FOUND;
      } else {
        String errMsg = String.format("Unexpected response code when deleting alert (entity, id): (%s, %d), %s",
            entityUrn, alertId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find all or some types of the alerts on this entity, sorted by creation time in desc order
   * @param entityUrn the subject of the alert
   * @param alertTypes the alert types to fetch, empty set means to fetch all types
   * @param start paging start
   * @param count paging count
   * @return HTTP status we want the clients to receive, otherwise exception will be thrown
   */
  public Task<List<Pair<Long, EntityAlert>>> findByCriteria(@NonNull Urn entityUrn, @NonNull Set<String> alertTypes,
      int start, int count) {
    String queryFilter;
    if (alertTypes.isEmpty()) {
      // default query filter to fetch all alert types
      queryFilter = ALERT_FIELD_CREATED_TIME + ':' + DEFAULT_TIME_RANGE;
    } else {
      // or clause to fetch specified alert types only
      queryFilter = alertTypes.stream()
          .map(type -> ALERT_FIELD_ALERT_TYPE + ':' + type)
          .collect(Collectors.joining(QUERY_DELIMITER_OR));
    }
    String sortQuery = ALERT_FIELD_CREATED_TIME + " DESC";
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_ALERT)
        .setTable(TABLE_ENTITY_ALERT)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(entityUrn.toString())
        .setQuery(queryFilter)
        .setSort(sortQuery)
        .setStart(start)
        .setCount(count)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
          || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of alerts. deserialize all of them.
        return getResponse.getParts().stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .map(this::convertEspressoPartToEspressoAlert)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        // Not expected. Parsing error indicates data corruption of some sort
        String errMsg = String.format("Unexpected response code when finding alert for %s, %s", entityUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * convert the response part from espresso to alertId and espresso.EntityAlert
   * @param part Serialized alert retrieved from espresso
   * @return A pair of alertId and espresso.EntityAlert
   */
  @Nullable
  private Pair<Long, EntityAlert> convertEspressoPartToEspressoAlert(@NonNull GetResponse.Part part) {
    try {
      Long alertId = Long.valueOf(part.getContentLocation().getKey()[1]);
      EntityAlert alert = EspressoUtil.deserializeSingleAvroRecord(ALERT_READER, part);
      return Pair.of(alertId, alert);
    } catch (Exception e) {
      // NullPointerException，NumberFormatException, etc., should not happen unless corrupted espresso data
      LOG.error("failed to parse the alert for {}", part, e);
      return null;
    }
  }
}
