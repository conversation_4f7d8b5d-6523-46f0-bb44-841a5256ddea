package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.PutResponse;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.TxMultiPutRequest;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.AccountPlay;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.javatuples.Triplet;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.linkedin.sales.espresso.AccountPlaysMetadata;
import com.linkedin.sales.espresso.AccountPlaysMetadataV2;


/**
 * Class that handles interactions with LssAutoFinder espresso table
 */
public class LssAutoFinderDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssAutoFinderDB.class);
  protected static final String DB_LSS_AUTO_FINDER = "LssAutoFinder";
  protected static final String TABLE_ACCOUNT_PLAYS_METADATA = "AccountPlaysMetadata";
  protected static final String TABLE_ACCOUNT_PLAYS_METADATA_V2 = "AccountPlaysMetadataV2";
  protected static final String TABLE_ACCOUNT_PLAY = "AccountPlay";
  private static final int ACCOUNT_PLAYS_METADATA_SCHEMA_VERSION = 1;
  private static final int ACCOUNT_PLAY_SCHEMA_VERSION = 1;
  private static final int ACCOUNT_PLAYS_METADATA_V2_SCHEMA_VERSION = 1;
  private static final int DEFAULT_GET_PAGE_SIZE = 1_000;
  private static final SpecificDatumWriter<AccountPlaysMetadata> ACCOUNT_PLAYS_METADATA_WRITER = new SpecificDatumWriter<>(AccountPlaysMetadata.SCHEMA$);
  private static final SpecificDatumReader<AccountPlaysMetadata> ACCOUNT_PLAYS_METADATA_READER = new SpecificDatumReader<>(AccountPlaysMetadata.SCHEMA$);
  private static final SpecificDatumWriter<AccountPlaysMetadataV2> ACCOUNT_PLAYS_METADATA_V2_WRITER = new SpecificDatumWriter<>(AccountPlaysMetadataV2.SCHEMA$);
  private static final SpecificDatumReader<AccountPlaysMetadataV2> ACCOUNT_PLAYS_METADATA_V2_READER = new SpecificDatumReader<>(AccountPlaysMetadataV2.SCHEMA$);
  private static final SpecificDatumWriter<AccountPlay> ACCOUNT_PLAY_WRITER = new SpecificDatumWriter<>(AccountPlay.SCHEMA$);
  private static final SpecificDatumReader<AccountPlay> ACCOUNT_PLAY_READER = new SpecificDatumReader<>(AccountPlay.SCHEMA$);

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE,
        ResponseStatus.INTERNAL_SERVER_ERROR,
        ResponseStatus.GATEWAY_TIMEOUT,
        ResponseStatus.TOO_MANY_REQUESTS));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG_MULTI_PUT;

  static {
    RETRY_REQUEST_CONFIG_MULTI_PUT = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
            ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
            ResponseStatus.GATEWAY_TIMEOUT));
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetryTimeoutMillis(1000L);
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssAutoFinderDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create AccountPlaysMetadata for seat and company pair
   * @param seatUrn - seat this record is associated with
   * @param organizationUrn - company this record is associated with
   * @param accountPlaysMetadata - AccountPlaysMetadata object to insert
   * @return - http status of result
   */
  public Task<HttpStatus> createAccountPlaysMetadata(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
      @NonNull AccountPlaysMetadata accountPlaysMetadata) {
    long currentTime = System.currentTimeMillis();
    accountPlaysMetadata.setCreatedTime(currentTime);
    accountPlaysMetadata.setLastModifiedTime(currentTime);
    byte[] bytes = EspressoUtil.serializeAvroRecord(ACCOUNT_PLAYS_METADATA_WRITER, accountPlaysMetadata);
    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .setContent(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setIfNoneMatchEtag("*");

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "AccountPlaysMetadata already exists with the same key");
      } else {
        String errMsg = String.format(
            "Create AccountPlaysMetadata failed for seat %s and company %s and AccountPlaysMetadata %s: %s", seatUrn, organizationUrn,
            accountPlaysMetadata, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create AccountPlaysMetadataV2 for seat and company on member to be dismissed
   * @param seatUrn - seat this record is associated with
   * @param organizationUrn - company this record is associated with
   * @param memberUrn - member to be dismissed
   * @return - http status of result
   */
  public Task<HttpStatus> createAccountPlaysMetadataV2(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
                                                       @NonNull MemberUrn memberUrn) {
    long currentTime = System.currentTimeMillis();
    AccountPlaysMetadataV2 accountPlaysMetadataV2 = new AccountPlaysMetadataV2();
    accountPlaysMetadataV2.setCreatedTime(currentTime);
    byte[] bytes = EspressoUtil.serializeAvroRecord(ACCOUNT_PLAYS_METADATA_V2_WRITER, accountPlaysMetadataV2);

    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA_V2)
        .setKey(seatUrn.toString(), organizationUrn.toString(), memberUrn.toString())
        .setContent(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_V2_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setIfNoneMatchEtag("*");

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "AccountPlaysMetadataV2 already exists with the same key");
      } else {
        String errMsg = String.format(
            "Create AccountPlaysMetadataV2 failed for seat %s and company %s and member %s: %s", seatUrn, organizationUrn,
            memberUrn, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create AccountPlaysMetadataV2 for seat and company on multiple members to be dismissed
   * @param seatUrn - seat this record is associated with
   * @param organizationUrnToMemberUrns - map of organizationUrn to memberUrns to be dismissed
   * @return - result map of organizationUrn to pairs of memberUrn and http status
   */
  public Task<Map<OrganizationUrn, Map<MemberUrn, HttpStatus>>> createAccountPlaysMetadataV2s(
          @NonNull SeatUrn seatUrn, @NonNull Map<OrganizationUrn, Set<MemberUrn>> organizationUrnToMemberUrns) {
    if (organizationUrnToMemberUrns.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    TxMultiPutRequest.Builder multiPutRequestBuilder = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setRetryConfig(RETRY_REQUEST_CONFIG_MULTI_PUT)
        .setKey(seatUrn.toString());

    Map<OrganizationUrn, Map<MemberUrn, HttpStatus>> resultMap = new HashMap<>();
    long currentTime = System.currentTimeMillis();
    AccountPlaysMetadataV2 accountPlaysMetadataV2 = new AccountPlaysMetadataV2();
    accountPlaysMetadataV2.setCreatedTime(currentTime);

    organizationUrnToMemberUrns.entrySet().stream()
        .filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
        .forEach(entry -> entry.getValue().forEach(memberUrn -> {
          byte[] bytes = EspressoUtil.serializeAvroRecord(ACCOUNT_PLAYS_METADATA_V2_WRITER, accountPlaysMetadataV2);
          TxMultiPutRequest.Part.Builder partBuilder = TxMultiPutRequest.Part.builder()
                .setTable(TABLE_ACCOUNT_PLAYS_METADATA_V2)
                .setSubkey(entry.getKey().toString(), memberUrn.toString())
                .setContent(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_V2_SCHEMA_VERSION, bytes);
          multiPutRequestBuilder.addPart(partBuilder.build());
        }));

    return _parSeqEspressoClient.execute(multiPutRequestBuilder.build()).map(multiPutResponse -> {
      for (PutResponse.Part putResponse : multiPutResponse.getParts()) {
        // Add each MultiPutResponse.Part (for each memberUrn) to the result map
        String organizationUrnString = putResponse.getContentLocation().getKey()[1];
        String memberUrnString = putResponse.getContentLocation().getKey()[2];
        if (putResponse.getResponseStatus() == ResponseStatus.OK || putResponse.getResponseStatus() == ResponseStatus.CREATED) {
          resultMap.putIfAbsent(OrganizationUrn.deserialize(organizationUrnString), new HashMap<>());
          resultMap.get(OrganizationUrn.deserialize(organizationUrnString))
                  .put(MemberUrn.deserialize(memberUrnString), HttpStatus.S_201_CREATED);
        } else {
          LOG.error("Unexpected response to put member {} from organization {} into AccountPlaysMetadataV2 Espresso for seat {}: {}",
                  memberUrnString, organizationUrnString, seatUrn, putResponse.getResponseStatus());
          resultMap.putIfAbsent(OrganizationUrn.deserialize(organizationUrnString), new HashMap<>());
          resultMap.get(OrganizationUrn.deserialize(organizationUrnString))
                  .put(MemberUrn.deserialize(memberUrnString), HttpStatus.fromCode(putResponse.getResponseStatus().getCode()));
        }
      }
      return resultMap;
    });
  }

  /**
   * Partial update AccountPlaysMetadata for seat and company pair
   * @param seatUrn - seat this record is associated with
   * @param organizationUrn - company this record is associated with
   * @param accountPlaysMetadata - AccountPlaysMetadata object to update
   * @return - http status of result
   */
  public Task<HttpStatus> partialUpdateAccountPlaysMetadata(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
      @NonNull AccountPlaysMetadata accountPlaysMetadata) {
    // Can not use AVRO_BINARY, as it does not support partial_update
    JSONObject obj = new JSONObject();
    if (accountPlaysMetadata.getDismissedLeads() != null) {
      obj.put("dismissedLeads", accountPlaysMetadata.getDismissedLeads());
    }
    if (accountPlaysMetadata.getLastRunTimestamp() != 0) {
      obj.put("lastRunTimestamp", accountPlaysMetadata.getLastRunTimestamp());
    }
    obj.put("lastModifiedTime", System.currentTimeMillis());

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating "
          + "AccountPlaysMetadata for seat %s company %s", seatUrn, organizationUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .setContent(ContentType.AVRO_JSON, ACCOUNT_PLAYS_METADATA_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setIfMatchEtag("*")
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else if (postResponse.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "AccountPlaysMetadata does not exist to update");
      } else {
        String errMsg = String.format(
            "Partial Update for AccountPlaysMetadata failed for seat %s and company %s and AccountPlaysMetadata %s: %s", seatUrn, organizationUrn,
            accountPlaysMetadata, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Delete AccountPlaysMetadataV2 for given seat and company on dimissed lead
   * @param seatUrn - seat this record is associated with
   * @param organizationUrn - company this record is associated with
   * @param memberUrn - lead had been dismissed
   * @return - http status of result
   */
  public Task<HttpStatus> deleteAccountPlaysMetadataV2(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
                                                       @NonNull MemberUrn memberUrn) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA_V2)
        .setKey(seatUrn.toString(), organizationUrn.toString(), memberUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_200_OK;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, "AccountPlaysMetadataV2 does not exist to delete");
      } else {
        String errMsg = String.format(
            "Delete AccountPlaysMetadataV2 failed for seat %s and company %s and member %s: %s", seatUrn, organizationUrn,
            memberUrn, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get AccountPlaysMetadata for seat and company
   * @param seatUrn - seat associated to record
   * @param organizationUrn - company associated to record
   * @return - AccountPlaysMetadata associated to key
   */
  public Task<AccountPlaysMetadata> getAccountPlaysMetadata(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA)
        .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_PLAYS_METADATA_READER, response.getPart(0));
      } else if (response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("Cannot find AccountPlaysMetadata for seat %s and company %s",
            seatUrn, organizationUrn));
      } else {
        String errMsg = String.format("Unexpected error when getting AccountPlaysMetadata for seat %s and company %s",
            seatUrn, organizationUrn);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get AccountPlaysMetadata for given seat
   * @param seatUrn - seat associated to record
   * @return - List of OrganizationUrn and AccountPlaysMetadata pair
   */
  public Task<Map<OrganizationUrn, AccountPlaysMetadata>> getAccountPlaysMetadataForSeat(@NonNull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
            .setDatabase(DB_LSS_AUTO_FINDER)
            .setTable(TABLE_ACCOUNT_PLAYS_METADATA)
            .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_SCHEMA_VERSION)
            .setRetryConfig(RETRY_REQUEST_CONFIG)
            .setKey(seatUrn.toString())
            .setStart(0)
            .setCount(DEFAULT_GET_PAGE_SIZE)
            .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
              || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of AccountPlayMetadatas. Deserialize all of them.
        List<Pair<OrganizationUrn, AccountPlaysMetadata>> responsePairs =
            getResponse.getParts()
                .stream()
                .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
                .map(this::convertEspressoPartToAccountPlaysMetadata)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<OrganizationUrn, AccountPlaysMetadata> responseMap = new HashMap<>();
        responsePairs.forEach(pair -> responseMap.put(pair.getFirst(), pair.getSecond()));
        return responseMap;
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
              || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyMap();
      } else {
        // Not expected.
        String errMsg = String.format("Unexpected response code when finding AccountPlaysMetadata for seat %s: %s",
                seatUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get AccountPlaysMetadataV2s for seat and company
   * @param seatUrn - seat associated to record
   * @param organizationUrn - company associated to record
   * @return - List of dismissed MemberUrns and AccountPlaysMetadataV2 pair
   */
  public Task<List<Pair<MemberUrn, AccountPlaysMetadataV2>>> getAccountPlaysMetadataV2(@NonNull SeatUrn seatUrn,
                                                                                       @NonNull OrganizationUrn organizationUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAYS_METADATA_V2)
        .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_V2_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .setStart(0)
        .setCount(DEFAULT_GET_PAGE_SIZE)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
          || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of AccountPlayMetadataV2s. Deserialize all of them.
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .map(this::convertEspressoPartToAccountPlaysMetadataV2Pair)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        // Not expected.
        String errMsg = String.format("Unexpected response code when finding AccountPlaysMetadata for seat %s and company %s: %s",
            seatUrn, organizationUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get all AccountPlaysMetadataV2s for seat
   * @param seatUrn - seat associated to record
   * @return - Map of companies to dismissed MemberUrns and AccountPlaysMetadataV2 pair
   */
  public Task<Map<OrganizationUrn, List<Pair<MemberUrn, AccountPlaysMetadataV2>>>> getAccountPlaysMetadataV2ForSeat(@NonNull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
            .setDatabase(DB_LSS_AUTO_FINDER)
            .setTable(TABLE_ACCOUNT_PLAYS_METADATA_V2)
            .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_PLAYS_METADATA_V2_SCHEMA_VERSION)
            .setRetryConfig(RETRY_REQUEST_CONFIG)
            .setKey(seatUrn.toString())
            .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
              || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of AccountPlayMetadataV2s. Deserialize all of them.
        return getResponse.getParts()
                .stream()
                .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
                .map(this::convertEspressoPartsToAccountPlaysMetadataV2Triplet)
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(
                        triplet -> triplet.getValue0(),
                        Collectors.mapping(triplet -> Pair.of(triplet.getValue1(), triplet.getValue2()), Collectors.toList())
                ));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
              || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyMap();
      } else {
        // Not expected.
        String errMsg = String.format("Unexpected response code when finding AccountPlaysMetadata for seat %s: %s",
                seatUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create AccountPlay
   * @param seatUrn - seat this record is associated to
   * @param organizationUrn - company this record is associated to
   * @param accountPlay - AccountPlay to insert
   * @return - Id of newly inserted AccountPlay
   */
  public Task<Integer> createAccountPlay(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
      @NonNull AccountPlay accountPlay) {
    long timestamp = System.currentTimeMillis();
    accountPlay.setCreatedTime(timestamp);
    accountPlay.setLastModifiedTime(timestamp);
    byte[] bytes = EspressoUtil.serializeAvroRecord(ACCOUNT_PLAY_WRITER, accountPlay);
    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAY)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .setContent(ContentType.AVRO_BINARY, ACCOUNT_PLAY_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setIfNoneMatchEtag("*");

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        String[] accountPlaysMetadataKey = response.getPart(0).getContentLocation().getKey();
        return Integer.valueOf(accountPlaysMetadataKey[2]);
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "AccountPlay already exists with the same key");
      } else {
        String errMsg = String.format(
            "Create AccountPlay failed for seat %s and company %s and AccountPlay: %s: %s", seatUrn,
            organizationUrn, accountPlay, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Partial update AccountPlay
   * @param seatUrn - seat this record is associated to
   * @param organizationUrn - company this record is associated to
   * @param id - id of AccountPlay to update
   * @param accountPlay - AccountPlay to update
   * @return - http status of result
   */
  public Task<HttpStatus> partialUpdateAccountPlay(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
      @NonNull Integer id, @NonNull AccountPlay accountPlay) {

    // Can not use AVRO_BINARY, as it does not support partial_update
    JSONObject obj = new JSONObject();
    if (accountPlay.getPastLeads() != null) {
      obj.put("pastLeads", accountPlay.getPastLeads());
    }
    if (accountPlay.getCurrentLeads() != null) {
      obj.put("currentLeads", accountPlay.getCurrentLeads());
    }
    obj.put("lastModifiedTime", System.currentTimeMillis());

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating AccountPlay for seat %s company %s", seatUrn, organizationUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAY)
        .setKey(seatUrn.toString(), organizationUrn.toString(), id.toString())
        .setContent(ContentType.AVRO_JSON, ACCOUNT_PLAY_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setIfMatchEtag("*")
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else if (postResponse.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "AccountPlay does not exist to update");
      } else {
        String errMsg = String.format(
            "Partial Update for AccountPlay failed for seat %s and company %s and AccountPlay %s: %s", seatUrn, organizationUrn,
            accountPlay, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get all AccountPlay records associated to seat and company
   * @param seatUrn - seat associated to records
   * @param organizationUrn - company associated to records
   * @param start - paging start
   * @param count - paging count
   * @return - List of AccountPlay found
   */
  public Task<List<Pair<Integer, AccountPlay>>> getAccountPlays(@NonNull SeatUrn seatUrn, @NonNull OrganizationUrn organizationUrn,
      int start, int count) {

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_FINDER)
        .setTable(TABLE_ACCOUNT_PLAY)
        .setKey(seatUrn.toString(), organizationUrn.toString())
        .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_PLAY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setStart(start)
        .setCount(count)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
          || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of AccountPlays. Deserialize all of them.
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .map(this::convertEspressoPartToAccountPlay)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        // Not expected.
        String errMsg = String.format("Unexpected response code when finding account plays for seat %s and company %s: %s",
            seatUrn, organizationUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Convert the response part from espresso to id and AccountPlay
   * @param part - serialized AccountPlay retrieved from Espresso
   * @return a pair of id and AccountPlay
   */
  @Nullable
  private Pair<Integer, AccountPlay> convertEspressoPartToAccountPlay(@NonNull GetResponse.Part part) {
    try {
      Integer playId = Integer.valueOf(part.getContentLocation().getKey()[2]);
      AccountPlay accountPlay = EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_PLAY_READER, part);
      return Pair.of(playId, accountPlay);
    } catch (Exception e) {
      LOG.error("Failed to parse AccountPlay for {}", part, e);
      return null;
    }
  }

  /**
   * Convert the response part from espresso to OrganizationUrn and AccountPlaysMetadata
   * @param part - serialized Espresso GetResponse
   * @return a pair of organization and AccountPlaysMetadata
   */
  @Nullable
  private Pair<OrganizationUrn, AccountPlaysMetadata> convertEspressoPartToAccountPlaysMetadata(@NonNull GetResponse.Part part) {
    try {
      OrganizationUrn organizationUrn = OrganizationUrn.deserialize(part.getContentLocation().getKey()[1]);
      AccountPlaysMetadata accountPlaysMetadata = EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_PLAYS_METADATA_READER, part);
      return Pair.of(organizationUrn, accountPlaysMetadata);
    } catch (Exception e) {
      LOG.error("Failed to parse AccountPlaysMetadata GetResponse for {}", part, e);
      return null;
    }
  }

  /**
   * Convert the response part from espresso to MemberUrn and AccountPlaysMetadataV2
   * @param part - serialized Espresso GetResponse
   * @return a pair of memberUrn and AccountPlaysMetadataV2
   */
  @Nullable
  private Pair<MemberUrn, AccountPlaysMetadataV2> convertEspressoPartToAccountPlaysMetadataV2Pair(@NonNull GetResponse.Part part) {
    try {
      MemberUrn memberUrn = MemberUrn.deserialize(part.getContentLocation().getKey()[2]);
      AccountPlaysMetadataV2 accountPlaysMetadataV2 = EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_PLAYS_METADATA_V2_READER, part);
      return Pair.of(memberUrn, accountPlaysMetadataV2);
    } catch (Exception e) {
      LOG.error("Failed to parse AccountPlaysMetadataV2 GetResponse for {}", part, e);
      return null;
    }
  }

  /**
   * Convert the response part from espresso to OrganizationUrn, MemberUrn and AccountPlaysMetadataV2 triple
   * @param part - serialized Espresso GetResponse
   * @return a triplet of organizationUrn, memberUrn and AccountPlaysMetadataV2
   */
  @Nullable
  private Triplet<OrganizationUrn, MemberUrn, AccountPlaysMetadataV2> convertEspressoPartsToAccountPlaysMetadataV2Triplet(
          @NonNull GetResponse.Part part) {
    try {
      OrganizationUrn organizationUrn = OrganizationUrn.deserialize(part.getContentLocation().getKey()[1]);
      MemberUrn memberUrn = MemberUrn.deserialize(part.getContentLocation().getKey()[2]);
      AccountPlaysMetadataV2 accountPlaysMetadataV2 = EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_PLAYS_METADATA_V2_READER, part);
      return new Triplet<>(organizationUrn, memberUrn, accountPlaysMetadataV2);
    } catch (Exception e) {
      LOG.error("Failed to parse AccountPlaysMetadataV2 GetResponse for {}", part, e);
      return null;
    }
  }
}
