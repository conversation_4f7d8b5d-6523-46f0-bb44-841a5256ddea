package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.query.LuceneQuery;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.util.Pair;
import com.linkedin.sales.espresso.Campaign;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import com.linkedin.sales.espresso.SearchCriteriaV1;
import com.linkedin.sales.espresso.LeadFindingRun;
import com.linkedin.sales.espresso.LeadFindingRunLead;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.commons.lang.StringUtils;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey;
import pegasus.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey;

/**
 * Espresso client for LssAutoProspecting DB.
 */
public class LssAutoProspectingDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssAutoProspectingDB.class);
  protected static final String DB_LSS_AUTO_PROSPECTING = "LssAutoProspecting";
  protected static final String TABLE_SEARCH_CRITERIA_V1 = "SearchCriteriaV1";
  protected static final String TABLE_LEAD_FINDING_RUN = "LeadFindingRun";
  protected static final String TABLE_LEAD_FINDING_RUN_LEAD = "LeadFindingRunLead";
  protected static final String TABLE_CAMPAIGN = "Campaign";
  private static final SpecificDatumWriter<SearchCriteriaV1> SEARCH_CRITERIA_WRITER = new SpecificDatumWriter<>(SearchCriteriaV1.SCHEMA$);
  private static final SpecificDatumReader<SearchCriteriaV1> SEARCH_CRITERIA_READER = new SpecificDatumReader<>(SearchCriteriaV1.SCHEMA$);
  private static final SpecificDatumWriter<LeadFindingRun> LEAD_FINDING_RUN_WRITER = new SpecificDatumWriter<>(LeadFindingRun.SCHEMA$);
  private static final SpecificDatumReader<LeadFindingRun> LEAD_FINDING_RUN_READER = new SpecificDatumReader<>(LeadFindingRun.SCHEMA$);
  private static final SpecificDatumWriter<LeadFindingRunLead> LEAD_FINDING_RUN_LEAD_SPECIFIC_DATUM_WRITER =
      new SpecificDatumWriter<>(LeadFindingRunLead.SCHEMA$);
  private static final SpecificDatumReader<LeadFindingRunLead> LEAD_FINDING_RUN_LEAD_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(LeadFindingRunLead.SCHEMA$);
  private static final int LEAD_FINDING_RUN_LEAD_SCHEMA_VERSION = 1;
  private static final SpecificDatumWriter<Campaign> CAMPAIGN_WRITER = new SpecificDatumWriter<>(Campaign.SCHEMA$);
  private static final SpecificDatumReader<Campaign> CAMPAIGN_READER = new SpecificDatumReader<>(Campaign.SCHEMA$);
  private static final int SEARCH_CRITERIA_SCHEMA_VERSION = 1;
  private static final int LEAD_FINDING_RUN_SCHEMA_VERSION = 1;
  private static final int CAMPAIGN_SCHEMA_VERSION = 1;
  private static final int RECORD_TTL_IN_DAYS = 30;
  private static final String SORT_BY_CREATED_TIME_DESC = "createdTime DESC";

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE,
        ResponseStatus.INTERNAL_SERVER_ERROR,
        ResponseStatus.GATEWAY_TIMEOUT,
        ResponseStatus.TOO_MANY_REQUESTS));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssAutoProspectingDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create SearchCriteria record for given seat and hashed criteria.
   * @param seatUrn seat that the criteria belongs to
   * @param hashedCriteria hash value of raw criteria and prompt variant string
   * @param searchCriteria query understanding outputs
   * @return http status of creation result
   */
  public Task<HttpStatus> createSearchCriteria(@Nonnull SeatUrn seatUrn, @Nonnull String hashedCriteria,
      @Nonnull SearchCriteriaV1 searchCriteria) {
    long currentTime = System.currentTimeMillis();
    searchCriteria.setCreatedTime(currentTime);
    searchCriteria.setLastModifiedTime(currentTime);
    byte[] bytes = EspressoUtil.serializeAvroRecord(SEARCH_CRITERIA_WRITER, searchCriteria);

    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_SEARCH_CRITERIA_V1)
        .setKey(seatUrn.toString(), hashedCriteria)
        .setContent(ContentType.AVRO_BINARY, SEARCH_CRITERIA_SCHEMA_VERSION, bytes)
        .setExpires(Date.from(Instant.now().plus(RECORD_TTL_IN_DAYS, ChronoUnit.DAYS))) // Auto purge the record
        .setIfNoneMatchEtag("*")  // Create only if not exists
        .setRetryConfig(RETRY_REQUEST_CONFIG);

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "SearchCriteria already exists");
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else {
        String errMsg = String.format("Create SearchCriteria failed for seat: %s, criteria: %s, %s", seatUrn, hashedCriteria, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Partial update SearchCriteria record for given seat and hashed criteria.
   * @param seatUrn seat that the criteria belongs to
   * @param hashedCriteria hash value of raw criteria and prompt variant string
   * @param searchCriteria updated query understanding outputs
   * @return http status of update result
   */
  public Task<HttpStatus> partialUpdateSearchCriteria(@Nonnull SeatUrn seatUrn, @Nonnull String hashedCriteria,
      @Nonnull SearchCriteriaV1 searchCriteria) {
    // Can not use AVRO_BINARY, as it does not support partial update
    JSONObject obj = new JSONObject();
    obj.put("facetSelections", searchCriteria.getFacetSelections());
    obj.put("ebrSearchQuery", searchCriteria.getEbrSearchQuery());
    obj.put("lastModifiedTime", System.currentTimeMillis());

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating SearchCriteria for seat %s", seatUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest postRequest = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_SEARCH_CRITERIA_V1)
        .setKey(seatUrn.toString(), hashedCriteria)
        .setContent(ContentType.AVRO_JSON, SEARCH_CRITERIA_SCHEMA_VERSION, contentJson)
        .setExpires(Date.from(Instant.now().plus(RECORD_TTL_IN_DAYS, ChronoUnit.DAYS))) // Update record auto purge time
        .setIfMatchEtag("*")  // Update only if exists
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(postRequest).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "SearchCriteria does not exist");
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else {
        String errMsg = String.format("Update SearchCriteria failed for seat: %s, criteria: %s, %s", seatUrn, hashedCriteria, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get SearchCriteria for seat and the hashed criteria.
   * @param seatUrn seat that the criteria belongs to
   * @return query understanding outputs
   */
  public Task<Optional<SearchCriteriaV1>> getSearchCriteria(@Nonnull SeatUrn seatUrn, @Nonnull String hashedCriteria) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_SEARCH_CRITERIA_V1)
        .setAcceptType(ContentType.AVRO_BINARY, SEARCH_CRITERIA_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), hashedCriteria)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return Optional.of(EspressoUtil.deserializeSingleAvroRecord(SEARCH_CRITERIA_READER, response.getPart(0)));
      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT
          || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return Optional.empty();
      } else {
        String errMsg = String.format("Get SearchCriteria failed for seat: %s, criteria: %s, %s", seatUrn, hashedCriteria, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create Campaign record for given seat and campaign id.
   * @param seatUrn seat that the campaign belongs to
   * @param campaign campaign data
   * @return db-generated campaign id
   */
  public Task<Long> createCampaign(@Nonnull SeatUrn seatUrn, @Nonnull Campaign campaign) {
    // Set timestamps if provided, otherwise they will be set by the DB layer
    long currentTime = System.currentTimeMillis();

    // TODO: remove after we've migrated from APSettings
    //  we typically should not set create time and last modified time based on client input. This is in order for us to
    //  copy modified and created time from APSettings to Campaign
    if (campaign.getCreatedTime() <= 0) {
      campaign.setCreatedTime(currentTime);
    }
    if (campaign.getLastModifiedTime() <= 0) {
      campaign.setLastModifiedTime(currentTime);
    }

    byte[] bytes = EspressoUtil.serializeAvroRecord(CAMPAIGN_WRITER, campaign);
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_CAMPAIGN)
        .setKey(seatUrn.toString())
        .setContent(ContentType.AVRO_BINARY, CAMPAIGN_SCHEMA_VERSION, bytes)
        .setIfNoneMatchEtag("*")  // Create only if not exists
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        assert !response.getParts().isEmpty();
        String[] keyParts = response.getPart(0).getContentLocation().getKey(); // searUrn, db-generated campaignId
        assert keyParts.length == 2;
        return Long.valueOf(keyParts[1]);
      }

      String errMsg = String.format("Create Campaign failed for seat: %s, %s", seatUrn, response);
      LOG.error(errMsg);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Partial update Campaign record for given seat and campaign id.
   * @param seatUrn seat that the campaign belongs to
   * @param campaignId unique identifier for the campaign
   * @param campaign updated campaign data
   * @return http status of update result
   */
  public Task<Campaign> partialUpdateCampaign(@Nonnull SeatUrn seatUrn, long campaignId,
      @Nonnull Campaign campaign) {
    // Can not use AVRO_BINARY, as it does not support partial update
    JSONObject obj = new JSONObject();
    if (campaign.getProductId() != null && StringUtils.isNotBlank(campaign.getProductId().toString())) {
      obj.put("productId", campaign.getProductId());
    }
    if (campaign.getAccountListUrns() != null && !campaign.getAccountListUrns().isEmpty()) {
      obj.put("accountListUrns", campaign.getAccountListUrns());
    }
    if (campaign.getAccountUrns() != null && !campaign.getAccountUrns().isEmpty()) {
      obj.put("accountUrns", campaign.getAccountUrns());
    }
    obj.put("lastModifiedTime", System.currentTimeMillis());

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg =
          String.format("Failed to convert JSONObject to String when updating Campaign for seat %s", seatUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest postRequest = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_CAMPAIGN)
        .setKey(seatUrn.toString(), String.valueOf(campaignId))
        .setContent(ContentType.AVRO_JSON, CAMPAIGN_SCHEMA_VERSION, contentJson)
        .setIfMatchEtag("*")  // Update only if exists
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(postRequest).flatMap(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        return getCampaign(seatUrn, campaignId);
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Campaign does not exist");
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else {
        String errMsg =
            String.format("Update Campaign failed for seat: %s, campaignId: %d, %s", seatUrn, campaignId, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get Campaign for seat and campaign id.
   * @param seatUrn seat that the campaign belongs to
   * @param campaignId unique identifier for the campaign
   * @return campaign data
   */
  public Task<Campaign> getCampaign(@Nonnull SeatUrn seatUrn, long campaignId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_CAMPAIGN)
        .setAcceptType(ContentType.AVRO_BINARY, CAMPAIGN_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), String.valueOf(campaignId))
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(CAMPAIGN_READER, response.getPart(0));
      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT
          || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("Cannot find Campaign for seat: %s, campaignId: %d, response: %s", seatUrn, campaignId,
                response));
      } else {
        String errMsg =
            String.format("Get Campaign failed for seat: %s, campaignId: %d, %s", seatUrn, campaignId, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Delete Campaign record for given seat and campaign id.
   * @param seatUrn seat that the campaign belongs to
   * @param campaignId campaign id
   * @return http status of deletion result
   */
  public Task<HttpStatus> deleteCampaign(@Nonnull SeatUrn seatUrn, long campaignId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_CAMPAIGN)
        .setKey(seatUrn.toString(), String.valueOf(campaignId))
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_204_NO_CONTENT;
      } else if (response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, "Campaign does not exist");
      } else {
        String errMsg = String.format("Delete Campaign failed for seat: %s, campaignId: %d, %s", seatUrn, campaignId, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find Campaigns for seat
   * @param seatUrn seat that the campaigns belong to
   * @return list of pairs of id and campaign data
   */
  public Task<List<Pair<Long, Campaign>>> findCampaignsBySeat(
      @Nonnull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_CAMPAIGN)
        .setAcceptType(ContentType.AVRO_BINARY, CAMPAIGN_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return response.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              Long campaignId = Long.valueOf(part.getContentLocation().getKey()[1]);
              Campaign campaign = EspressoUtil.deserializeSingleAvroRecord(CAMPAIGN_READER, part);
              return new Pair<>(campaignId, campaign);
            })
            .collect(Collectors.toList());
      }

      if (response.getResponseStatus() == ResponseStatus.NO_CONTENT
          || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return Collections.emptyList();
      }

      String errMsg = String.format("Get Campaign failed for seat: %s, %s", seatUrn, response);
      LOG.error(errMsg);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Create a lead finding run record for given seat and campaignId.
   * @param seatUrn the seat urn
   * @param campaignId the campaign id
   * @param leadFindingRun the leadFindingRun object to create
   * @return the run id
   */
  public Task<Long> createLeadFindingRun(@Nonnull SeatUrn seatUrn, @Nonnull Long campaignId, @Nonnull LeadFindingRun leadFindingRun) {
    long currentTime = System.currentTimeMillis();
    leadFindingRun.setCreatedTime(currentTime);
    leadFindingRun.setLastModifiedTime(currentTime);
    byte[] bytes = EspressoUtil.serializeAvroRecord(LEAD_FINDING_RUN_WRITER, leadFindingRun);

    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN)
        .setKey(seatUrn.toString(), campaignId.toString())
        .setContent(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_SCHEMA_VERSION, bytes)
        .setIfNoneMatchEtag("*")  // Create only if not exists
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        // validate the key and return the run id
        assert !response.getParts().isEmpty();
        String[] keyParts = response.getPart(0).getContentLocation().getKey();
        assert keyParts.length == 3;
        return Long.valueOf(keyParts[2]);
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "the lead finding run record already exists");
      } else {
        String errMsg = String.format("Unexpected response when creating lead finding run for seat: %s, campaignId: %s, response: %s",
            seatUrn, campaignId, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get a lead finding run record
   * @param seatUrn the seat urn
   * @param campaignId the campaign id
   * @param runId the lead finding run id
   * @return a LeadFindingRun espresso record
   */
  public Task<LeadFindingRun> getLeadFindingRun(@Nonnull SeatUrn seatUrn, @Nonnull Long campaignId, @Nonnull Long runId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), campaignId.toString(), runId.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        if (response.getPartCount() > 0) {
          GetResponse.Part part = response.getPart(0);
          // validate the key and deserialize the record
          if (part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3) {
            return EspressoUtil.deserializeSingleAvroRecord(LEAD_FINDING_RUN_READER, part);
          }
        }
        // if returned key parts are not correct, still throw exception
        throw new EntityNotFoundException(null,
            String.format("Empty part for the LeadFindingRun response for seat:%s, campaignId:%d, runId:%d",
                seatUrn, campaignId, runId));
      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT
          || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("Cannot find LeadFindingRun for, seat:%s, campaignId:%d, runId:%d, response: %s",
                seatUrn, campaignId, runId, response.getResponseStatus().toString()));
      } else {
        throw new RuntimeException(String.format("Get LeadFindingRun failed for seat: %s, campaignId:%d, runId: %s, %s",
            seatUrn, campaignId, runId, response));
      }
    });
  }

  /**
   * Partial update LeadFindingRun record for given seat, campaignId and runId.
   * @param seatUrn the seat urn
   * @param campaignId the campaign id
   * @param runId the run id
   * @param leadFindingRun the LeadFindingRun object to update
   * @return the LeadFindingRun espresso object after update
   */
  // HttpStatus
  public Task<LeadFindingRun> partialUpdateLeadFindingRun(@Nonnull SeatUrn seatUrn, @Nonnull Long campaignId, @Nonnull Long runId,
      @Nonnull LeadFindingRun leadFindingRun) {
    JSONObject obj = new JSONObject();
    if (leadFindingRun.contractUrn != null) {
      obj.put("contractUrn", leadFindingRun.getContractUrn());
    }
    if (leadFindingRun.status != null) {
      obj.put("status", leadFindingRun.getStatus());
    }
    if (leadFindingRun.type != null) {
      obj.put("type", leadFindingRun.getType());
    }
    if (leadFindingRun.error != null) {
      obj.put("error", leadFindingRun.getError());
    }
    if (leadFindingRun.scheduledStartTime != null) {
      obj.put("scheduledStartTime", leadFindingRun.getScheduledStartTime());
    }
    if (leadFindingRun.startTime != null) {
      obj.put("startTime", leadFindingRun.getStartTime());
    }
    if (leadFindingRun.projectedCompletionTime != null) {
      obj.put("projectedCompletionTime", leadFindingRun.getProjectedCompletionTime());
    }
    if (leadFindingRun.completionTime != null) {
      obj.put("completionTime", leadFindingRun.getCompletionTime());
    }
    if (leadFindingRun.trackingId != null) {
      obj.put("trackingId", Base64.getEncoder().encodeToString(leadFindingRun.getTrackingId().bytes()));
    }
    if (leadFindingRun.prospectedLeadsCount != null) {
      obj.put("prospectedLeadsCount", leadFindingRun.getProspectedLeadsCount());
    }
    if (leadFindingRun.leadLimit != null) {
      obj.put("leadLimit", leadFindingRun.getLeadLimit());
    }
    obj.put("lastModifiedTime", System.currentTimeMillis());

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating LeadFindingRun for seat: %s, campaignId: %d, runId: %d",
          seatUrn, campaignId, runId);
      LOG.error(errMsg, e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest postRequest = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN)
        .setKey(seatUrn.toString(), campaignId.toString(), runId.toString())
        .setContent(ContentType.AVRO_JSON, LEAD_FINDING_RUN_SCHEMA_VERSION, contentJson)
        .setIsPartialUpdate()
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(postRequest).flatMap(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        return getLeadFindingRun(seatUrn, campaignId, runId);
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "LeadFindingRun does not exist");
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        String msg = String.format("Unable to process the request, contentJson: %s, response: %s", contentJson, response);
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, msg);
      } else {
        String errMsg = String.format("Update LeadFindingRun failed for seat: %s, campaignId: %d, runId: %d, response: %s",
            seatUrn, campaignId, runId, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find LeadFindingRun records for given seat, campaignId and status.
   * @param seatUrn the seat urn
   * @param campaignId the campaign id
   * @param status the status of the run
   * @param start the start index of the result
   * @param count the number of records to return
   * @return Pairs of LeadFindingRunKey and LeadFindingRun where the first element is the key and the second element is the espresso record
   */
  public Task<List<Pair<LeadFindingRunKey, LeadFindingRun>>> findLeadFindingRunByQuery(@Nonnull SeatUrn seatUrn, @Nullable Long campaignId,
      @Nullable String status, @Nullable String type, @Nonnull int start, @Nonnull int count) {
    ArrayList<String> keyStr = new ArrayList<>();
    keyStr.add(seatUrn.toString());
    if (campaignId != null && campaignId > 0) {
      keyStr.add(campaignId.toString());
    }

    GetRequest.Builder requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keyStr.toArray(new String[0]))
        .setStart(start)
        .setCount(count)
        .setSort(SORT_BY_CREATED_TIME_DESC);

    if (Objects.nonNull(status) || Objects.nonNull(type)) {
      LuceneQuery query = new LuceneQuery();
      if (Objects.nonNull(status)) {
        query.addSubQuery(LuceneQuery.Operator.AND, new LuceneQuery().setTermQuery("status", status));
      }
      if (Objects.nonNull(type)) {
        query.addSubQuery(LuceneQuery.Operator.AND, new LuceneQuery().setTermQuery("type", type));
      }
      requestBuilder.setQuery(query);
    }


    return _parSeqEspressoClient.execute(requestBuilder.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)
            .map(part -> {
              String[] key = part.getContentLocation().getKey();
              LeadFindingRun value = EspressoUtil.deserializeSingleAvroRecord(LEAD_FINDING_RUN_READER, part);
              LeadFindingRunKey leadFindingRunKey = new LeadFindingRunKey()
                  .setCampaignId(Integer.parseInt(key[1]))
                  .setRunId(Integer.parseInt(key[2]));
              try {
                leadFindingRunKey.setSeatUrn(SeatUrn.deserialize(key[0]));
              } catch (URISyntaxException e) {
                LOG.error("fail to deserialize seatUrn {}", seatUrn, e);
              }
              return Pair.of(leadFindingRunKey, value);
            }).collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        // no record found
        return Collections.emptyList();
      } else {
        String errMsg = String.format("Unexpected response when finding lead finding run for %s, %s",
            seatUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Creates a new LeadFindingRunLead record in the database.
   * @param runId The ID of the lead finding run.
   * @param memberUrn The unique member URN associated with the lead.
   * @param leadFindingRunLead The LeadFindingRunLead object to be created.
   * @return A Task that resolves to the HTTP status of the operation.
   * @throws RuntimeException If serialization or database operation fails.
   */
  public Task<HttpStatus> createLeadFindingRunLead(@Nonnull long runId, @Nonnull MemberUrn memberUrn, @Nonnull LeadFindingRunLead leadFindingRunLead) {
    long currentTime = System.currentTimeMillis();
    leadFindingRunLead.setCreatedTime(currentTime);
    leadFindingRunLead.setModifiedTime(currentTime);

    // Serialize the LeadFindingRunLead object
    byte[] bytes;
    try {
      bytes = EspressoUtil.serializeAvroRecord(LEAD_FINDING_RUN_LEAD_SPECIFIC_DATUM_WRITER, leadFindingRunLead);
    } catch (Exception e) {
      String errMsg = String.format("Failed to serialize LeadFindingRunLead for runId: %s, memberUrn: %s", runId, memberUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg, e);
    }

    // Build the PutRequest
    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN_LEAD)
        .setKey(String.valueOf(runId), memberUrn.toString())
        .setContent(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_LEAD_SCHEMA_VERSION, bytes)
        .setIfNoneMatchEtag("*") // Create only if not exists
        .setRetryConfig(RETRY_REQUEST_CONFIG);

    // Execute the request and handle the response
    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        LOG.info("Successfully created LeadFindingRunLead for runId: {}, memberUrn: {}", runId, memberUrn);
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "LeadFindingRunLead already exists");
      } else if (response.getResponseStatus() == ResponseStatus.FORBIDDEN) {
        LOG.error("Permission denied while creating LeadFindingRunLead record: {}", response);
        throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "Access denied for creating LeadFindingRunLead record");
      } else if (response.getResponseStatus() == ResponseStatus.INTERNAL_SERVER_ERROR) {
        LOG.error("Internal server error while creating LeadFindingRunLead record: {}", response);
        throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
            "Server encountered an error while creating LeadFindingRunLead record");
      } else {
        LOG.error("Unexpected error while creating LeadFindingRunLead record: {}", response);
        throw new RuntimeException("Unexpected error occurred: " + response.getResponseStatus());
      }
    });
  }

  /**
   * Partial update for LeadFindingRunLead record.
   * @param runId the run ID
   * @param memberUrn the member URN
   * @return HTTP status of the operation
   */
  public Task<LeadFindingRunLead> partialUpdateLeadFindingRunLead(@Nonnull long runId, @Nonnull MemberUrn memberUrn,
      @Nonnull LeadFindingRunLead leadFindingRunLead) {
    // Can not use AVRO_BINARY, as it does not support partial update
    JSONObject obj = buildPartialUpdateJsonLeadFindingRunLead(leadFindingRunLead);

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating LeadFindingRunLead for runId %s", String.valueOf(runId));
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest postRequest = PostRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN_LEAD)
        .setKey(String.valueOf(runId), memberUrn.toString())
        .setContent(ContentType.AVRO_JSON, LEAD_FINDING_RUN_LEAD_SCHEMA_VERSION, contentJson)
        .setIfMatchEtag("*")  // Update only if exists
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(postRequest).flatMap(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        return getLeadByLeadFindingRunIdAndMemberUrn(runId, memberUrn).flatMap(optionalLead -> {
          if (optionalLead.isPresent()) {
            return Task.value(optionalLead.get());
          } else {
            // If the lead is not found after the update, throw an exception
            throw new IllegalStateException("Updated LeadFindingRunLead not found in the database");
          }
        });
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "LeadingFindingRunLead does not exist");
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new RestLiServiceException(HttpStatus.S_422_UNPROCESSABLE_ENTITY, "Unable to process the request");
      } else {
        String errMsg = String.format("Update LeadFindingRunLead failed for runId: %d, memberUrn: %s, %s", runId, memberUrn, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Retrieves a LeadFindingRunLead record based on the specified run ID and member URN.
   *
   * @param runId The ID of the lead finding run.
   * @param memberUrn The unique member URN associated with the lead.
   * @return A task that resolves to an Optional containing the LeadFindingRunLead record if found, or an empty Optional if not found.
   * @throws RuntimeException If an unexpected error occurs during the retrieval process.
   */
  public Task<Optional<LeadFindingRunLead>> getLeadByLeadFindingRunIdAndMemberUrn(@Nonnull long runId, @Nonnull MemberUrn memberUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN_LEAD)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_LEAD_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(runId), memberUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return Optional.of(EspressoUtil.deserializeSingleAvroRecord(LEAD_FINDING_RUN_LEAD_SPECIFIC_DATUM_READER, response.getPart(0)));
      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        LOG.warn("No LeadFindingRunLead found for runId: {}, memberUrn: {}", runId, memberUrn);
        return Optional.empty();
      } else {
        String errMsg = String.format("Get LeadFindingRunLead failed for runId: %s, memberUrn: %s, response: %s", runId, memberUrn, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   Retrieves a list of LeadFindingRunLead records based on the specified run ID, start index, and count.

   @param runId The ID of the lead finding run to search for.
   @param start The starting index for pagination.
   @param count The number of records to retrieve.
   @return A task that resolves to a list of LeadFindingRunLead records. Returns an empty list if no records are found.
   @throws RuntimeException If an unexpected error occurs during the retrieval process. */
  public Task<List<Pair<LeadFindingRunLeadKey, LeadFindingRunLead>>> findLeadFindingRunLeadByParams(
      @Nonnull long runId, @Nonnull int start, @Nonnull int count) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_AUTO_PROSPECTING)
        .setTable(TABLE_LEAD_FINDING_RUN_LEAD)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_FINDING_RUN_LEAD_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(runId))
        .setStart(start)
        .setCount(count)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        // Deserialize all parts into a list of LeadFindingRunLead
        return response.getParts().stream()
            .map(part -> {

              LeadFindingRunLead leadFindingRunLead = EspressoUtil.deserializeSingleAvroRecord(LEAD_FINDING_RUN_LEAD_SPECIFIC_DATUM_READER, part);
              String[] key = part.getContentLocation().getKey();
              LeadFindingRunLeadKey leadFindingRunLeadKey = new LeadFindingRunLeadKey()
                  .setRunId(Long.parseLong(key[0]));
              try {
                leadFindingRunLeadKey.setMemberUrn(MemberUrn.deserialize(key[1]));
              } catch (URISyntaxException e) {
                LOG.error("fail to deserialize memberUrn {}", key[1], e);
              }
              return Pair.of(leadFindingRunLeadKey, leadFindingRunLead);
            }).collect(Collectors.toList());

      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT
          || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return Collections.emptyList(); // Return an empty list if no content is found
      } else {
        String errMsg = String.format("Get LeadFindingRunLead failed for runId: %s, %s", String.valueOf(runId), response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  private JSONObject buildPartialUpdateJsonLeadFindingRunLead(LeadFindingRunLead leadFindingRunLead) {
    long currentTime = System.currentTimeMillis();
    JSONObject obj = new JSONObject();
    obj.put("modifiedTime", currentTime);

    if (leadFindingRunLead.contractUrn != null) {
      obj.put("contractUrn", leadFindingRunLead.contractUrn);
    }
    if (leadFindingRunLead.status != null) {
      obj.put("status", leadFindingRunLead.status);
    }
    if (leadFindingRunLead.variant != null) {
      obj.put("variant", leadFindingRunLead.variant);
    }
    if (leadFindingRunLead.keyStrengths != null) {
      obj.put("keyStrengths", leadFindingRunLead.keyStrengths);
    }
    if (leadFindingRunLead.rationale != null) {
      obj.put("rationale", leadFindingRunLead.rationale);
    }
    if (leadFindingRunLead.relevantPositionIds != null) {
      obj.put("relevantPositionIds", leadFindingRunLead.relevantPositionIds);
    }
    if (leadFindingRunLead.conversationId != null) {
      obj.put("conversationId", leadFindingRunLead.conversationId);
    }
    obj.put("score", leadFindingRunLead.score);
    return obj;
  }
}