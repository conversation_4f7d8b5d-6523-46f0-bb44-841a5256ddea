package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.Bookmark;
import com.linkedin.sales.espresso.BookmarkType;
import com.linkedin.sales.espresso.SeatToBookmarkView;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by hchang at 12/02/2020
 * This is the class that directly talk to espresso Database LssBookmark
 */
public class LssBookmarkDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssBookmarkDB.class);
  private static final int SCHEMA_VERSION = 1;
  private static final String TABLE_SEAT_TO_BOOKMARK_VIEW = "SeatToBookmarkView";
  private static final String CONTENT_URN_FIELD_STRING = "contentUrn:";
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  private static final SpecificDatumWriter<Bookmark> BOOKMARK_WRITER = new SpecificDatumWriter<>(Bookmark.SCHEMA$);
  private static final SpecificDatumReader<Bookmark> BOOKMARK_READER = new SpecificDatumReader<>(Bookmark.SCHEMA$);
  private static final SpecificDatumReader<SeatToBookmarkView> SEAT_TO_BOOKMARK_VIEW_READER = new SpecificDatumReader<>(SeatToBookmarkView.SCHEMA$);

  static final String DB_LSS_BOOKMARK = "LssBookmark";
  static final String TABLE_BOOKMARK = "Bookmark";
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure */
        ResponseStatus.GATEWAY_TIMEOUT /* Gateway timed out */));
    // As P95 get/write/delete/query are 20 ms at most. We think 250 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(250L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssBookmarkDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create a bookmark in db
   * @param bookmark the document content of db entry
   * @param expirationTime
   * @param id optional ID if user wish to specify
   * @return the id of the bookmark just created
   */
  public Task<Long> createBookmark(@NonNull Bookmark bookmark, @Nullable Long expirationTime, @Nullable Long id) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(BOOKMARK_WRITER, bookmark);
    PostRequest.Builder requestBuilder = PostRequest.builder()
        .setDatabase(DB_LSS_BOOKMARK)
        .setTable(TABLE_BOOKMARK)
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG);
    if (expirationTime != null) {
      requestBuilder.setExpires(new Date(expirationTime));
    }
    if (id != null) {
      requestBuilder.setKey(Long.toString(id));
    }

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return Long.valueOf(response.getPart(0).getContentLocation().getKey()[0]);
      } else {
        String errMsg =
            String.format("Unexpected response code for call to to create bookmark: %s", response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get an espresso bookmark
   * @param bookmarkId bookmark id
   * @return an espresso bookmark if exists
   */
  public Task<Bookmark> getBookmark(long bookmarkId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BOOKMARK)
        .setTable(TABLE_BOOKMARK)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(bookmarkId))
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(BOOKMARK_READER, response.getPart(0));
      } else {
        LOG.error("Unexpected response when getting bookmark {}: {}", bookmarkId, response);
        return null;
      }
    });
  }

  /**
   * Delete an espresso bookmark
   * @param bookmarkId bookmark id
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<HttpStatus> deleteBookmark(long bookmarkId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_BOOKMARK)
        .setTable(TABLE_BOOKMARK)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(bookmarkId))
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_204_NO_CONTENT;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return HttpStatus.S_404_NOT_FOUND;
      } else {
        String errMsg = String.format("Unexpected response code when deleting bookmark (id): (%d), %s",
            bookmarkId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find bookmarks that fall under given seat and type
   */
  public Task<List<Pair<Long, Bookmark>>> getBySeatAndType(@NonNull SeatUrn seatUrn,
      @NonNull BookmarkType type, int start, int count) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BOOKMARK)
        .setTable(TABLE_SEAT_TO_BOOKMARK_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), type.toString())
        .setStart(start)
        .setCount(count)
        .build();
    return getByRequest(request);
  }

  /**
   * Find bookmarks that fall under given seat and type
   */
  public Task<List<Pair<Long, Bookmark>>> getBySeatAndTypeAndContent(@NonNull SeatUrn seatUrn,
      @NonNull BookmarkType type, @NonNull String bookmarkContentString) {
    // Espresso parser complains about special characters. Add \ in front of them.
    String queryFilter = CONTENT_URN_FIELD_STRING + bookmarkContentString
        .replaceAll(":", "\\\\:")
        .replaceAll("\\(", "\\\\(")
        .replaceAll("\\)", "\\\\)");
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BOOKMARK)
        .setTable(TABLE_SEAT_TO_BOOKMARK_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), type.toString())
        .setQuery(queryFilter)
        .build();
    return getByRequest(request);
  }

  /**
   * Helper function that takes a GetRequest as input, return list of bookmark pairs
   * @param request a valid GetRequest
   * @return List of bookmark pairs
   */
  private Task<List<Pair<Long, Bookmark>>> getByRequest(GetRequest request) {
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
          || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        // the response will return a list of bookmarks. deserialize all of them.
        return getResponse.getParts().stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .map(this::convertEspressoPartToEspressoBookmark)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        // Not expected. Parsing error indicates data corruption of some sort
        throw new RuntimeException(getResponse.getResponseStatus().getText());
      }
    });
  }

  /**
   * convert the response part from espresso to alertId and espresso.EntityAlert
   * @param part Serialized alert retrieved from espresso
   * @return A pair of alertId and espresso.EntityAlert
   */
  @Nullable
  private Pair<Long, Bookmark> convertEspressoPartToEspressoBookmark(@NonNull GetResponse.Part part) {
    try {
      // Key is in order of seatUrn, BookmarkType, id
      String[] keys = part.getContentLocation().getKey();
      Long bookmarkId = Long.valueOf(keys[2]);
      SeatToBookmarkView seatToBookmarkView =
          EspressoUtil.deserializeSingleAvroRecord(SEAT_TO_BOOKMARK_VIEW_READER, part);
      Bookmark bookmark = new Bookmark();
      bookmark.contentUrn = seatToBookmarkView.contentUrn;
      bookmark.createdTime = seatToBookmarkView.createdTime;
      bookmark.seatUrn = keys[0];
      bookmark.type = BookmarkType.valueOf(keys[1]);
      return Pair.of(bookmarkId, bookmark);
    } catch (Exception e) {
      // NullPointerException，NumberFormatException, etc., should not happen unless corrupted espresso data
      LOG.error("failed to parse the alert for {}", part, e);
      return null;
    }
  }
}
