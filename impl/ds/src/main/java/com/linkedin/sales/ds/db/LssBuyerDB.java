package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.query.LuceneQuery;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.ContractSellerIdentity;
import com.linkedin.sales.espresso.ProductCategoryInterest;
import com.linkedin.sales.espresso.SeatSellerIdentity;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Created by guanwang at 10/2022
 * This is the class interacting with the Espresso database LssBuyer.
 */
public class LssBuyerDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssBuyerDB.class);

  static final String DB_LSS_BUYER = "LssBuyer";

  // definitions of table ProductCategoryInterest
  static final String TABLE_PCI = "ProductCategoryInterest";
  static final String TABLE_SELLER_IDENTITY = "SeatSellerIdentity";
  private static final int PCI_SCHEMA_VERSION = 1;
  private static final int SELLER_IDENTITY_SCHEMA_VERSION = 5;
  private static final String PCI_FIELD_CATEGORY_NAME = "categoryName";

  private static final SpecificDatumReader<ProductCategoryInterest> PCI_READER = new SpecificDatumReader<>(ProductCategoryInterest.SCHEMA$);
  private static final SpecificDatumReader<SeatSellerIdentity> SELLER_IDENTITY_READER = new SpecificDatumReader<>(SeatSellerIdentity.SCHEMA$);
  private static final SpecificDatumReader<ContractSellerIdentity> CONTRACT_SELLER_IDENTITY_READER = new SpecificDatumReader<>(ContractSellerIdentity.SCHEMA$);
  private static final SpecificDatumWriter<ProductCategoryInterest> PCI_WRITER = new SpecificDatumWriter<>(ProductCategoryInterest.SCHEMA$);
  private static final SpecificDatumWriter<SeatSellerIdentity> SELLER_IDENTITY_WRITER = new SpecificDatumWriter<>(SeatSellerIdentity.SCHEMA$);
  private static final SpecificDatumWriter<ContractSellerIdentity> CONTRACT_SELLER_IDENTITY_WRITER = new SpecificDatumWriter<>(ContractSellerIdentity.SCHEMA$);

  private static final int MAX_FETCH_COUNT = 1000;
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  private static final String TABLE_CONTRACT_SELLER_IDENTITY = "ContractSellerIdentity";
  private static final int CONTRACT_SELLER_IDENTITY_SCHEMA_VERSION = 1;

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(
        ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssBuyerDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create a Product Category Interest record.
   * @param seatUrn the urn of the user
   * @param interest the Product Category Interest record to be created.
   * @return auto-generated interest Id
   */
  public Task<Long> createProductCategoryInterest(@NonNull SeatUrn seatUrn, @NonNull ProductCategoryInterest interest) {
    // build the create request, without the interest Id
    byte[] bytes = EspressoUtil.serializeAvroRecord(PCI_WRITER, interest);
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_PCI)
        .setContent(ContentType.AVRO_BINARY, PCI_SCHEMA_VERSION, bytes)
        .setKey(seatUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    // The interest record uses an auto-generated Id. It should always succeed.
    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        // validate the key parts. return interest Id.
        assert !putResponse.getParts().isEmpty();
        String[] keyParts = putResponse.getPart(0).getContentLocation().getKey();
        assert keyParts.length == 2;
        return Long.valueOf(keyParts[1]);
      } else {
        String errMsg = String.format("Unexpected response when creating product category interest for %s, %s", seatUrn, putResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get a Product Category Interest record with the full key.
   * @param seatUrn the seat urn of the user
   * @param interestId the interest Id of the record
   * @return the Espresso document of the target record
   */
  public Task<ProductCategoryInterest> getProductCategoryInterest(@NonNull SeatUrn seatUrn, @NonNull Long interestId) {
    // build the get request with the full key parts.
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_PCI)
        .setAcceptType(ContentType.AVRO_BINARY, PCI_SCHEMA_VERSION)
        .setKey(seatUrn.toString(), interestId.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    // If found, return the document. If not found, return a not found exception.
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          GetResponse.Part part = getResponse.getPart(0);
          // validate key parts first.
          if (part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 2) {
            return EspressoUtil.deserializeSingleAvroRecord(PCI_READER, part);
          }
        }
        // if returned key parts are not correct, still throw exception.
        throw new EntityNotFoundException(null,
            String.format("Failed to get Product Category Interest for, seat:%s, interestId:%d", seatUrn, interestId));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null,
            String.format("Cannot find Product Category Interest for, seat:%s, interestId:%d", seatUrn, interestId));
      } else {
        String errMsg = String.format("Unexpected response when getting Product Category Interest for seat:%s, interestId:%d, %s",
            seatUrn, interestId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Update or create a Product Category Interest record
   * @param seatUrn the seat urn of the user
   * @param interestId the interest Id of the record
   * @param interest the patch interest to be saved
   * @return http status code of the result: 200 for updated; 201 for created.
   */
  public Task<HttpStatus> updateProductCategoryInterest(@NonNull SeatUrn seatUrn, @NonNull Long interestId,
      @NonNull ProductCategoryInterest interest) {
    // build the update request
    byte[] bytes = EspressoUtil.serializeAvroRecord(PCI_WRITER, interest);
    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_PCI)
        .setContent(ContentType.AVRO_BINARY, PCI_SCHEMA_VERSION, bytes)
        .setKey(seatUrn.toString(), interestId.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        // existing record is updated
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        // target record not exist, created new record.
        return HttpStatus.S_201_CREATED;
      } else {
        String errMsg = String.format("Unexpected response when updating product category interest for (seatUrn, interestId): (%s, %d), %s",
            seatUrn, interestId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Delete an existing Product Category Interest record.
   * @param seatUrn the seat urn of the user
   * @param interestId the interest Id of the record
   * @return http status code of the result: 204 for deletion success; 404 for no found
   */
  public Task<HttpStatus> deleteProductCategoryInterest(@NonNull SeatUrn seatUrn, @NonNull Long interestId) {
    // build delete request
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_PCI)
        .setKey(seatUrn.toString(), interestId.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        // delete target record successfully
        return HttpStatus.S_204_NO_CONTENT;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        // target record not found
        return HttpStatus.S_404_NOT_FOUND;
      } else {
        String errMsg = String.format("Unexpected response when deleting product category interest for (seatUrn, interestId): (%s, %d), %s",
            seatUrn, interestId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find Product Category Interest records for a seat by given categories
   * @param seatUrn seat urn of the user
   * @param categoryNames chosen category names to find
   * @param start the start of page context
   * @param count the number of record to retrieve
   * @return a list of pairs of interest Id and interest record
   */
  public Task<List<Pair<Long, ProductCategoryInterest>>> findProductCategoryInterests(@NonNull SeatUrn seatUrn,
      @NonNull String[] categoryNames, int start, int count) {
    // limit the count to max count
    if (count > MAX_FETCH_COUNT) {
      LOG.warn("Trying to fetch {} items for {} exceeds limit, will only get {}. ", count, seatUrn, MAX_FETCH_COUNT);
    }
    int fetchCount = Math.min(count, MAX_FETCH_COUNT);

    // build get request
    GetRequest.Builder requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_PCI)
        .setAcceptType(ContentType.AVRO_BINARY, PCI_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString())
        .setStart(start)
        .setCount(fetchCount);
    if (ArrayUtils.isNotEmpty(categoryNames)) {
      LuceneQuery stateQuery = new LuceneQuery();
      Stream.of(categoryNames).forEach(categoryName ->
          stateQuery.addSubQuery(
              LuceneQuery.Operator.OR,
              new LuceneQuery().setTermQuery(PCI_FIELD_CATEGORY_NAME, categoryName)));
      requestBuilder.setQuery(stateQuery);
    }

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              // valid result. get Id and document.
              Long interestId = Long.valueOf(part.getContentLocation().getKey()[1]);
              ProductCategoryInterest interest = EspressoUtil.deserializeSingleAvroRecord(PCI_READER, part);
              return new Pair<>(interestId, interest);
            })
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        // no record found
        return Collections.emptyList();
      } else {
        String errMsg = String.format("Unexpected response when getting product category interests for %s, %s",
            seatUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create and Update a Seat Seller Identity record
   * @param contractUrn the contract urn of the seat
   * @param seatUrn the seat urn of the seat
   * @param identity patched espresso record
   * @return http status code of the result: 200 for updated, 201 for created
   */
  public Task<HttpStatus> updateSeatSellerIdentity(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn, @NonNull SeatSellerIdentity identity) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(SELLER_IDENTITY_WRITER, identity);
    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_SELLER_IDENTITY)
        .setContent(ContentType.AVRO_BINARY, SELLER_IDENTITY_SCHEMA_VERSION, bytes)
        .setKey(contractUrn.toString(), seatUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else {
        String errMsg = String.format("Unexpected response when updating Seat Seller Identity for Contract:%s, Seat:%s, %s",
            contractUrn, seatUrn, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   *
   * @param contractUrn the contract urn of the seat
   * @param seatUrn the seat urn of seat
   * @return a single Espresso document of the target record
   */
  public Task<SeatSellerIdentity> getSellerIdentity(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_SELLER_IDENTITY)
        .setAcceptType(ContentType.AVRO_BINARY, SELLER_IDENTITY_SCHEMA_VERSION)
        .setKey(contractUrn.toString(), seatUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          GetResponse.Part part = getResponse.getPart(0);
          if (part.getContentLocation() != null && part.getContentLocation().getKey() != null
          && part.getContentLocation().getKey().length == 2) {
            return EspressoUtil.deserializeSingleAvroRecord(SELLER_IDENTITY_READER, part);
          }
        }
        // no part is returned, consider as a not found and throw exception
        throw new EntityNotFoundException(null, String.format("Failed to get Seller Identity for contract: %s, seat: %s", contractUrn, seatUrn));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("Failed to get Seller Identity for contract: %s, seat: %s", contractUrn, seatUrn));
      } else {
        //all other exceptions
        String errMsg = String.format("Unexpected response when getting Seller Identity for contract: %s, seat: %s", contractUrn, seatUrn);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create and Update a Contract Seller Identity record
   * @param contractUrn the contract urn of the seat
   * @param identity patched espresso record
   * @return http status code of the result: 200 for updated, 201 for created
   */
  public Task<HttpStatus> createOrUpdateContractSellerIdentity(@NonNull ContractUrn contractUrn,  @NonNull ContractSellerIdentity identity) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(CONTRACT_SELLER_IDENTITY_WRITER, identity);
    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_CONTRACT_SELLER_IDENTITY)
        .setContent(ContentType.AVRO_BINARY, CONTRACT_SELLER_IDENTITY_SCHEMA_VERSION, bytes)
        .setKey(contractUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else {
        String errMsg = String.format("Unexpected response when updating Contract Seller Identity for Contract:%s, %s",
            contractUrn, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   *
   * @param contractUrn the contract urn of the seat
   * @return a single Espresso document of the target record
   */
  public Task<Optional<ContractSellerIdentity>> getContractSellerIdentity(@NonNull ContractUrn contractUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_CONTRACT_SELLER_IDENTITY)
        .setAcceptType(ContentType.AVRO_BINARY, CONTRACT_SELLER_IDENTITY_SCHEMA_VERSION)
        .setKey(contractUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            // There should be only one part
            .findFirst()
            .map(part -> EspressoUtil.deserializeSingleAvroRecord(CONTRACT_SELLER_IDENTITY_READER, part));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        // No content has been stored for the given key
        return Optional.empty();
      } else {
        String error = String.format("Unexpected response code from Espresso when getting Contract Seller Identity for contract: %s, %s",
            contractUrn, getResponse);
        LOG.error(error);
        throw new RuntimeException(error);
      }
    });
  }

  /**
   * Get a List of Seat Seller Identity by Contact
   * @param contractUrn the contract urn of the seats
   * @param seatUrn the seat urn of the seat
   * @param start
   * @param count
   * @return a list of Espresso documents of target record
   */
  public Task<List<Pair<SeatUrn, SeatSellerIdentity>>> findSellerIdentities(@NonNull ContractUrn contractUrn, @Nullable SeatUrn seatUrn,
      int start, int count) {

    if (count > MAX_FETCH_COUNT) {
      LOG.warn("Trying to fetch {} Seat Seller Identity for Contract: {} which exceeds limit, will only get {}.", count,
          contractUrn, MAX_FETCH_COUNT);
    }
    int fetchCount = Math.min(count, MAX_FETCH_COUNT);

    GetRequest.Builder requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_BUYER)
        .setTable(TABLE_SELLER_IDENTITY)
        .setAcceptType(ContentType.AVRO_BINARY, SELLER_IDENTITY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(contractUrn.toString())
        .setStart(start)
        .setCount(fetchCount);

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            // part response status and key validation
          .filter(part -> part.getResponseStatus() == ResponseStatus.OK && part.getContentLocation() != null
              && part.getContentLocation().getKey() != null && part.getContentLocation().getKey().length == 2)
          .map(part -> {
            String seatUrnString = part.getContentLocation().getKey()[1];
            try {
              SeatUrn seat = SeatUrn.deserialize(seatUrnString);
              return new Pair<>(seat, EspressoUtil.deserializeSingleAvroRecord(SELLER_IDENTITY_READER, part));
            } catch (URISyntaxException e) {
              LOG.error("Fail to generate the SeatUrn for the rawString {}", seatUrnString, e);
              return null;
            }
          })
          .filter(Objects::nonNull) // filter out seat urn deserialize fail case, collect all possible records
          .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        // no result found under the query contract urn, return an empty list
        return Collections.emptyList();
      } else {
        // all other exceptions
        String errMsg = String.format("Unexpected response when finding Seller Identities for contract: %s, %s",
            contractUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }
}
