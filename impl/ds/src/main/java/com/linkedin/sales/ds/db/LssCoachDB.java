package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.CoachConversationHistory;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.io.IOException;
import java.io.StringWriter;
import java.util.Optional;
import org.apache.avro.specific.SpecificDatumReader;
import java.util.Arrays;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * Espresso client for LSS Coach DB.
 */
public class LssCoachDB {

  private static final Logger LOG = LoggerFactory.getLogger(LssCoachDB.class);

  public static final String DB_LSS_COACH = "LssCoach";
  public static final String TABLE_COACH_CONVERSATION_HISTORY = "CoachConversationHistory";
  private static final int COACH_CONVERSATION_HISTORY_SCHEMA_VERSION = 3;


  private static final String COACH_CONVERSATION_HISTORY_FIELD_UPDATED_TIME = "updatedTime";
  private static final String COACH_CONVERSATION_HISTORY_FIELD_CHAT_CONTENT = "chatContent";
  private static final String COACH_CONVERSATION_HISTORY_FIELD_METADATA = "metadata";

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(
        Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, ResponseStatus.INTERNAL_SERVER_ERROR, ResponseStatus.GATEWAY_TIMEOUT));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private static final SpecificDatumReader<CoachConversationHistory> COACH_CHAT_HISTORY_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(CoachConversationHistory.SCHEMA$);
  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssCoachDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }


  /**
   * update or create coach chat history for a given seat and session id.
   * @param seatUrn The member's seat urn.
   * @param sessionId The session id of the coach chat history.
   * @param coachConversationHistory The coach chat history to be updated or created.
   * @return True if the coach chat history is updated or created successfully.
   */
  public Task<Boolean> upsertCoachConversationHistory(
      @NonNull SeatUrn seatUrn,
      @NonNull String sessionId,
      @NonNull CoachConversationHistory coachConversationHistory) {

    JSONObject obj = new JSONObject();

    if (coachConversationHistory.chatContent != null) {
      obj.put(COACH_CONVERSATION_HISTORY_FIELD_CHAT_CONTENT, coachConversationHistory.chatContent);
    }

    if (coachConversationHistory.metadata != null) {
      obj.put(COACH_CONVERSATION_HISTORY_FIELD_METADATA, coachConversationHistory.metadata);
    }

    obj.put(COACH_CONVERSATION_HISTORY_FIELD_UPDATED_TIME, coachConversationHistory.updatedTime);
    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error("Failed to write JSON Object in to String.", e);
      return Task.failure(e);
    }

    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_COACH)
        .setTable(TABLE_COACH_CONVERSATION_HISTORY)
        .setContent(ContentType.AVRO_JSON, COACH_CONVERSATION_HISTORY_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), sessionId)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK || response.getResponseStatus() == ResponseStatus.CREATED) {
        return TRUE;
      } else if (response.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return FALSE;
      } else {
        String error =
            String.format("Unexpected response code while updating chat history of seat urn: %s session id: %s response: %s",
                seatUrn, sessionId, response);
        LOG.error(error);
        throw new RuntimeException(error);
      }
    });
  }

  /**
   * Get an optional {@link CoachConversationHistory} for a given {@link SeatUrn} and {@link String} sessionId.
   * @param seatUrn The member's seatUrn
   * @param sessionId The session id uniquely identifying the chat history
   * @return An optional {@link CoachConversationHistory}
   */
  public Task<Optional<CoachConversationHistory>> getCoachConversationHistory(
      @NonNull SeatUrn seatUrn,
      @NonNull String sessionId) {

    String[] keys =  new String[]{seatUrn.toString(), sessionId};

    GetRequest requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_COACH)
        .setTable(TABLE_COACH_CONVERSATION_HISTORY)
        .setAcceptType(ContentType.AVRO_BINARY, COACH_CONVERSATION_HISTORY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .build();

    return _parSeqEspressoClient.execute(requestBuilder)
        .map(getResponse -> {
            if (getResponse.getResponseStatus() == ResponseStatus.OK) {
              return getResponse.getParts()
                  .stream()
                  .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
                  // There should be only one part
                  .findFirst()
                  .flatMap(this::convertEspressoPartToEspressoCoachConversationHistory);
            } else if (getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
                || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
              // No content has been stored for the given key
              return Optional.empty();
            } else {
              String error = String.format("Unexpected response code from Espresso when getting Chat History for seat urn: %s and session id: %s, %s",
                      seatUrn, sessionId, getResponse);
              LOG.error(error);
              throw new RuntimeException(error);
            }
      });
  }

  private Optional<CoachConversationHistory> convertEspressoPartToEspressoCoachConversationHistory(@NonNull GetResponse.Part part) {
    try {
      CoachConversationHistory coachConversationHistory =
          EspressoUtil.deserializeSingleAvroRecord(COACH_CHAT_HISTORY_SPECIFIC_DATUM_READER, part);
      return Optional.of(coachConversationHistory);
    } catch (Exception e) {
      LOG.error("Failed to parse Espresso Coach chat history part {}", part, e);
      return Optional.empty();
    }
  }


}
