package com.linkedin.sales.ds.db;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.TxMultiPutRequest;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.ColleagueRelationship;
import com.linkedin.sales.espresso.ColleagueRelationshipHistory;
import com.linkedin.sales.espresso.ReverseRelationshipView;
import com.linkedin.sales.espresso.State;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class LssColleaguesDB {

  private static final Logger LOG = LoggerFactory.getLogger(LssColleaguesDB.class);

  static final String DB_LSS_COLLEAGUES = "LssColleagues";
  static final String TABLE_COLLEAGUE_RELATIONSHIP = "ColleagueRelationship";
  static final String TABLE_COLLEAGUE_RELATIONSHIP_HISTORY = "ColleagueRelationshipHistory";
  static final String TABLE_REVERSE_RELATIONSHIP_VIEW = "ReverseRelationshipView";
  private static final String DEFAULT_TIME_RANGE_START = "0";
  private static final String DEFAULT_TIME_RANGE_END = String.valueOf(TimeUnit.DAYS.toMillis(1000000L));
  private static final String DEFAULT_TIME_RANGE =
      "[" + DEFAULT_TIME_RANGE_START + " TO " + DEFAULT_TIME_RANGE_END + "]";
  private static final String FLD_STATE = "state";
  private static final String FLD_UPDATED_TIME = "updatedTime";
  private static final String DEFAULT_SORT = FLD_UPDATED_TIME + " DESC";
  private static final int SCHEMA_VERSION = 1;

  private static final SpecificDatumReader<ColleagueRelationship> COLLEAGUE_RELATIONSHIP_READER =
      new SpecificDatumReader<>(ColleagueRelationship.SCHEMA$);

  private static final SpecificDatumReader<ColleagueRelationshipHistory> COLLEAGUE_RELATIONSHIP_HISTORY_READER =
      new SpecificDatumReader<>(ColleagueRelationshipHistory.SCHEMA$);

  private static final SpecificDatumReader<ReverseRelationshipView> REVERSE_RELATIONSHIP_VIEW_READER =
      new SpecificDatumReader<>(ReverseRelationshipView.SCHEMA$);

  private static final SpecificDatumWriter<ColleagueRelationship> COLLEAGUE_RELATIONSHIP_WRITER =
      new SpecificDatumWriter<>(ColleagueRelationship.SCHEMA$);

  private static final SpecificDatumWriter<ColleagueRelationshipHistory> COLLEAGUE_RELATIONSHIP_HISTORY_WRITER =
      new SpecificDatumWriter<>(ColleagueRelationshipHistory.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssColleaguesDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Gets the toMemberId of the latest 'ADDED' relationship with the given parameters
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract to add the relationship to.
   * @param relationshipType The type of relationship between the two members, in the format [fromMember] [relationshipType] [toMember]
   * @return A Long of the toMemberId if there is an entry, null if no entry
   */
  public Task<Long> loadToMemberIdForLatestAdded(Long fromMemberId, Long contractId, String relationshipType) {
    String queryFilter = FLD_STATE + ":" + State.ADDED.name();
    GetRequest getRequest = GetRequest.builder()
        .setDatabase(DB_LSS_COLLEAGUES)
        .setTable(TABLE_COLLEAGUE_RELATIONSHIP)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setKey(String.valueOf(fromMemberId), String.valueOf(contractId), relationshipType)
        .setQuery(queryFilter)
        .setSort(DEFAULT_SORT)
        .setStart(0)
        .setCount(1)
        .build();

    return _parSeqEspressoClient.execute(getRequest).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          // 4 keys: fromMemberId, contractId, relationshipType, toMemberId
          String returnedToMemberId = getResponse.getPart(0).getContentLocation().getKey()[3];
          return Long.valueOf(returnedToMemberId);
        }
      }
      return null;
    });
  }

  /**
   * Adds an entry to both the ColleagueRelationship table and the ColleagueRelationshipHistory table.
   * This entry can be of either the ADDED or REMOVED state.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract to add the relationship to.
   * @param toMemberId The member on the other side of the relationship.
   * @param relationshipType The type of relationship between the two members, in the format [fromMember] [relationshipType] [toMember]
   * @param colleagueRelationship The espresso document for colleagueRelationship to be stored in the db.
   * @return True if succeeded, false if failed.
   */
  public Task<Void> addColleagueRelationshipEntry(Long fromMemberId, Long contractId, Long toMemberId,
      String relationshipType, ColleagueRelationship colleagueRelationship) {

    byte[] colleagueRelationshipBytes =
        EspressoUtil.serializeAvroRecord(COLLEAGUE_RELATIONSHIP_WRITER, colleagueRelationship);
    byte[] colleagueRelationshipHistoryBytes = EspressoUtil.serializeAvroRecord(COLLEAGUE_RELATIONSHIP_HISTORY_WRITER,
        buildHistoryObjectFromColleagueRelationship(colleagueRelationship));

    TxMultiPutRequest.Part.Builder mainPartBuilder = TxMultiPutRequest.Part.builder()
        .setTable(TABLE_COLLEAGUE_RELATIONSHIP)
        .setSubkey(String.valueOf(contractId), relationshipType, String.valueOf(toMemberId))
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, colleagueRelationshipBytes);

    TxMultiPutRequest.Part.Builder historyPartBuilder = TxMultiPutRequest.Part.builder()
        .setTable(TABLE_COLLEAGUE_RELATIONSHIP_HISTORY)
        .setSubkey(String.valueOf(contractId), relationshipType, String.valueOf(toMemberId))
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, colleagueRelationshipHistoryBytes);

    TxMultiPutRequest multiPutRequest = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_COLLEAGUES)
        .setKey(String.valueOf(fromMemberId))
        .addPart(mainPartBuilder.build())
        .addPart(historyPartBuilder.build())
        .build();

    return executeMultiPutRequest(multiPutRequest);
  }

  private Task<Void> executeMultiPutRequest(TxMultiPutRequest multiPutRequest) {
    return _parSeqEspressoClient.execute(multiPutRequest).map(multiPutResponse -> {
      if (multiPutResponse.getResponseStatus() == ResponseStatus.OK
          || multiPutResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return null;
      } else {
        String errMsg =
            String.format("Unexpected response code for call to addColleagueRelationshipEntry for member:%s, %s",
                multiPutRequest.getKey(), multiPutResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets the ColleagueRelationships in the database that match the given {fromMemberId, contractId, [relationshipType]} tuple.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract the relationship belongs to.
   * @param relationshipType The type of relationship between two members, in the format [fromMember] [relationshipType] [toMember]
   * @param start The paging start
   * @param count The paging count
   * @return List of Pairs of Pairs in the following format: Pair<Pair<relationshipType, toMemberId>, colleagueRelationship>.
   */
  public Task<List<Pair<Pair<String, Long>, ColleagueRelationship>>> getColleagueRelationships(Long fromMemberId,
      Long contractId, @Nullable String relationshipType, int start, int count) {
    String queryFilter = FLD_UPDATED_TIME + ":" + DEFAULT_TIME_RANGE; // we need this to sort the results
    ArrayList<String> s = new ArrayList<>();
    s.add(String.valueOf(fromMemberId));
    s.add(String.valueOf(contractId));
    if (relationshipType != null && !relationshipType.isEmpty()) {
      s.add(relationshipType);
    }
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_COLLEAGUES)
        .setTable(TABLE_COLLEAGUE_RELATIONSHIP)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setKey(s.toArray(new String[0]))
        .setQuery(queryFilter)
        .setSort(DEFAULT_SORT)
        .setStart(start)
        .setCount(count)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null)
            .map(part -> {
              // 4 keys: fromMemberId, contractId, relationshipType, toMemberId
              String returnedRelationshipType = part.getContentLocation().getKey()[2];
              String toMemberId = part.getContentLocation().getKey()[3];
              ColleagueRelationship colleagueRelationship =
                  EspressoUtil.deserializeSingleAvroRecord(COLLEAGUE_RELATIONSHIP_READER, part);
              return new Pair<>(new Pair<>(returnedRelationshipType, Long.valueOf(toMemberId)), colleagueRelationship);
            })
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList(); // Don't throw an error, this is expected when no colleague information has been added
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get colleagueRelationship for member:%d, contract:%d, relationshipType:%s, %s",
            fromMemberId, contractId, relationshipType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets the ColleagueRelationshipHistory objects in the database that match the given {fromMemberId, contractId, [relationshipType]} tuple.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract the relationship belongs to.
   * @param relationshipType The type of relationship between two members, in the format [fromMember] [relationshipType] [toMember]
   * @param start The paging start
   * @param count The paging count
   * @return List of Pairs of Pairs in the following format: Pair<Pair<relationshipType, toMemberId>, colleagueRelationshipHistory>.
   */
  public Task<List<Pair<Pair<String, Long>, ColleagueRelationshipHistory>>> getColleagueRelationshipHistory(
      Long fromMemberId, Long contractId, @Nullable String relationshipType, int start, int count) {
    String queryFilter = FLD_UPDATED_TIME + ":" + DEFAULT_TIME_RANGE; // we need this to sort the results
    ArrayList<String> s = new ArrayList<>();
    s.add(String.valueOf(fromMemberId));
    s.add(String.valueOf(contractId));
    if (relationshipType != null && !relationshipType.isEmpty()) {
      s.add(relationshipType);
    }
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_COLLEAGUES)
        .setTable(TABLE_COLLEAGUE_RELATIONSHIP_HISTORY)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setKey(s.toArray(new String[0]))
        .setQuery(queryFilter)
        .setSort(DEFAULT_SORT)
        .setStart(start)
        .setCount(count)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null)
            .map(part -> {
              // 5 keys: fromMemberId, contractId, relationshipType, toMemberId, entryId
              String returnedRelationshipType = part.getContentLocation().getKey()[2];
              String toMemberId = part.getContentLocation().getKey()[3];
              ColleagueRelationshipHistory colleagueRelationshipHistory =
                  EspressoUtil.deserializeSingleAvroRecord(COLLEAGUE_RELATIONSHIP_HISTORY_READER, part);
              return new Pair<>(new Pair<>(returnedRelationshipType, Long.valueOf(toMemberId)),
                  colleagueRelationshipHistory);
            })
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList(); // Don't throw an error, this is expected when no colleague information has been added
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get colleagueRelationshipHistory for member:%d, contract:%d, relationshipType:%s, %s",
            fromMemberId, contractId, relationshipType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  private static ColleagueRelationshipHistory buildHistoryObjectFromColleagueRelationship(
      ColleagueRelationship colleagueRelationship) {
    ColleagueRelationshipHistory historyObject = new ColleagueRelationshipHistory();
    historyObject.updatedTime = colleagueRelationship.updatedTime;
    historyObject.createdBySeatUrn = colleagueRelationship.createdBySeatUrn;
    historyObject.state = colleagueRelationship.state;
    return historyObject;
  }
}
