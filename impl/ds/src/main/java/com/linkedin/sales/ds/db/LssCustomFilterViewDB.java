package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableMap;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.FilterLayout;
import java.util.Arrays;
import java.util.Map;
import javax.annotation.Nonnull;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import com.linkedin.sales.espresso.PinnedFilters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Class that directly talks to espresso Database LssCustomFilterView
 * <AUTHOR>
 */
public class LssCustomFilterViewDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssCustomFilterViewDB.class);

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;

  public static final int PINNED_FILTERS_SCHEMA_VERSION = 1;
  public static final int FILTER_LAYOUT_SCHEMA_VERSION = 1;

  static final String DB_CUSTOM_FILTER_VIEW = "LssCustomFilterView";
  static final String TABLE_PINNED_FILTERS = "PinnedFilters";
  static final String TABLE_FILTER_LAYOUT = "FilterLayout";

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(
        ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR,
        ResponseStatus.GATEWAY_TIMEOUT));
    // As P95 get/write/query are 10 ms at most. We think 250 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(250L);
  }

  /**
   * Infers resource name via the table the operation is performing on.
   */
  private static final Map<String, String> TABLE_NAME_TO_RESOURCE_NAME_MAP = new ImmutableMap.Builder<String, String>()
      .put(TABLE_FILTER_LAYOUT, "filterLayout")
      .put(TABLE_PINNED_FILTERS, "pinnedFilters")
      .build();

  private static final SpecificDatumReader<PinnedFilters> PINNED_FILTERS_READER =
      new SpecificDatumReader<>(PinnedFilters.SCHEMA$);

  private static final SpecificDatumWriter<PinnedFilters> PINNED_FILTERS_WRITER =
      new SpecificDatumWriter<>(PinnedFilters.SCHEMA$);

  private static final SpecificDatumReader<FilterLayout> FILTER_LAYOUT_READER =
      new SpecificDatumReader<>(FilterLayout.SCHEMA$);

  private static final SpecificDatumWriter<FilterLayout> FILTER_LAYOUT_WRITER =
      new SpecificDatumWriter<>(FilterLayout.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssCustomFilterViewDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Gets the stored {@link PinnedFilters} record for a given seat and searchType
   *
   * @param seatUrn seat of the user to get the record for
   * @param searchType searchType the record is for
   * @return {@link PinnedFilters}
   */
  public Task<PinnedFilters> getPinnedFilters(@Nonnull String seatUrn, @Nonnull String searchType) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_CUSTOM_FILTER_VIEW)
        .setTable(TABLE_PINNED_FILTERS)
        .setAcceptType(ContentType.AVRO_BINARY, PINNED_FILTERS_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, searchType)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return EspressoUtil.deserializeSingleAvroRecord(PINNED_FILTERS_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null, String.format("Cannot find pinned filters for seatUrn: %s and searchType: %s",
              seatUrn, searchType));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("Cannot find pinned filters for seatUrn: %s and searchType: %s",
            seatUrn, searchType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get pinned filters" + " for seatUrn :%s and searchType :%s, %s", seatUrn,
            searchType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets the stored {@link FilterLayout} record for a given seat and searchType
   *
   * @param seatUrn seat of the user to get the record for
   * @param searchViewType searchType the record is for
   * @return {@link FilterLayout}
   */
  public Task<FilterLayout> getFilterLayout(@Nonnull String seatUrn, @Nonnull String searchViewType) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_CUSTOM_FILTER_VIEW)
        .setTable(TABLE_FILTER_LAYOUT)
        .setAcceptType(ContentType.AVRO_BINARY, FILTER_LAYOUT_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, searchViewType)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return EspressoUtil.deserializeSingleAvroRecord(FILTER_LAYOUT_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null, String.format("Cannot find filter layout for seatUrn: %s and searchViewType: %s",
              seatUrn, searchViewType));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("Cannot find filter layout for seatUrn: %s and searchViewType: %s",
            seatUrn, searchViewType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get filter layout" + " for seatUrn :%s and searchType :%s, %s", seatUrn,
            searchViewType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Upsert a record containing {@link PinnedFilters} for the given seat and searchType
   *
   * @param seatUrn seat of the user to upsert the record for
   * @param searchType searchType the record is for
   * @param pinnedFilters {@link PinnedFilters} to store
   * @return TRUE if record was updated/created successfully, FALSE otherwise.
   */
  public Task<Boolean> upsertPinnedFilters(@Nonnull String seatUrn, @Nonnull String searchType,
      @Nonnull PinnedFilters pinnedFilters) {
    return upsertRecord(seatUrn, searchType, TABLE_PINNED_FILTERS,
        EspressoUtil.serializeAvroRecord(PINNED_FILTERS_WRITER, pinnedFilters), PINNED_FILTERS_SCHEMA_VERSION);
  }

  /**
   * Upsert a record containing {@link FilterLayout} for the given seat and searchType
   *
   * @param seatUrn seat of the user to upsert the record for
   * @param searchViewType searchType the record is for
   * @param filterLayout {@link FilterLayout} to store
   * @return TRUE if record was updated/created successfully, FALSE otherwise.
   */
  public Task<Boolean> upsertFilterLayout(@Nonnull String seatUrn, @Nonnull String searchViewType,
      @Nonnull FilterLayout filterLayout) {
    return upsertRecord(seatUrn, searchViewType, TABLE_FILTER_LAYOUT,
        EspressoUtil.serializeAvroRecord(FILTER_LAYOUT_WRITER, filterLayout), FILTER_LAYOUT_SCHEMA_VERSION);
  }

  /**
   * Common code for upsert a record. Shared with PinnedFilters and FilterLayout tables.
   * If record does not exist, will create a record. PutRequest will override any exists data in the table.
   */
  private Task<Boolean> upsertRecord(@Nonnull String seatUrn, @Nonnull String searchType, String table,
      @Nonnull byte[] recordBytes, int schemaVersion) {
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_CUSTOM_FILTER_VIEW)
        .setTable(table)
        .setContent(ContentType.AVRO_BINARY, schemaVersion, recordBytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, searchType)
        .build();
    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.OK
          || putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return Boolean.TRUE;
      }  else {
        String errMsg = String.format("Unexpected response code for call to update %s for seatUrn: %s, searchType: %s, %s",
            TABLE_NAME_TO_RESOURCE_NAME_MAP.get(table), seatUrn, searchType, putResponse);
        LOG.error(errMsg);
        return Boolean.FALSE;
      }
    });
  }

  /**
   * Deletes the stored {@link PinnedFilters} record for a given seat and searchType
   *
   * @param seatUrn seat of the user to get the record for
   * @param searchType searchType the record is for
   * @return {@link PinnedFilters}
   */
  public Task<HttpStatus> deletePinnedFilters(@Nonnull String seatUrn, @Nonnull String searchType) {
    return deleteRecord(seatUrn, searchType, TABLE_PINNED_FILTERS);
  }

  /**
   * Deletes the stored {@link FilterLayout} record for a given seat and searchType
   *
   * @param seatUrn seat of the user to get the record for
   * @param searchViewType searchType the record is for
   * @return {@link FilterLayout}
   */
  public Task<HttpStatus> deleteFilterLayout(@Nonnull String seatUrn, @Nonnull String searchViewType) {
    return deleteRecord(seatUrn, searchViewType, TABLE_FILTER_LAYOUT);
  }

  /**
   * Common code for delete a record. Shared with PinnedFilters and FilterLayout tables.
   */
  private Task<HttpStatus> deleteRecord(@Nonnull String seatUrn, @Nonnull String searchType, String table) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_CUSTOM_FILTER_VIEW)
        .setTable(table)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, searchType)
        .build();
    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_200_OK;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("Cannot find %s for seatUrn: %s and searchType: %s in table: %s",
                TABLE_NAME_TO_RESOURCE_NAME_MAP.get(table), seatUrn, searchType, TABLE_PINNED_FILTERS));
      } else {
        String errMsg = String.format("Unexpected response code for call to delete %s for seatUrn: %s and searchType: %s "
            + "in table: %s, %s", TABLE_NAME_TO_RESOURCE_NAME_MAP.get(table), seatUrn, searchType, TABLE_PINNED_FILTERS, deleteResponse);
        LOG.error(errMsg);
        return HttpStatus.S_500_INTERNAL_SERVER_ERROR;
      }
    });
  }
}
