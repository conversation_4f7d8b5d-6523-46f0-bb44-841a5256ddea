package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.ManualEmailToMemberMapping;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Class that handles interaction with the database for LSS entity mappings.
 */
public class LssEntityMappingDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssEntityMappingDB.class);
  protected static final String DB_NAME = "LssEntityMapping";
  protected static final String TABLE_NAME = "ManualEmailToMemberMapping";
  private static final int SCHEMA_VERSION = 1;
  private static final SpecificDatumWriter<ManualEmailToMemberMapping> WRITER =
      new SpecificDatumWriter<>(ManualEmailToMemberMapping.SCHEMA$);
  private static final SpecificDatumReader<ManualEmailToMemberMapping> READER =
      new SpecificDatumReader<>(ManualEmailToMemberMapping.SCHEMA$);

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE,
        ResponseStatus.INTERNAL_SERVER_ERROR,
        ResponseStatus.GATEWAY_TIMEOUT,
        ResponseStatus.TOO_MANY_REQUESTS));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssEntityMappingDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Upsert a manual member mapping for a hashed email
   * @param hashedEmail the hashed email for which the mapping is created or updated
   * @param contractUrn the contract urn for which the mapping is created or updated
   * @param manualEmailToMemberMapping the mapping to be created or updated
   * @return The HTTP status of the upsert operation
   */
  public Task<HttpStatus> upsertManualEmailToMemberMapping(@NonNull final String hashedEmail,
      @NonNull final ContractUrn contractUrn, @NonNull final ManualEmailToMemberMapping manualEmailToMemberMapping) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(WRITER, manualEmailToMemberMapping);
    // Using put request to update or create ManualEmailToMemberMapping
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_NAME)
        .setTable(TABLE_NAME)
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(hashedEmail, contractUrn.toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else {
        String errMsg =
            String.format("Unexpected response code for call to upsert the manualEmailToMemberMapping for contract: %s, %s",
                contractUrn, putResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Batch get manual member mappings for a list of hashed emails
   * @param emailMatchingKeys a list of keys that need to be fetched
   * @return A list of pairs of hashed emails and their corresponding member mappings
   */
  public Task<List<Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping>>> batchGetManualEmailMappings(
      @NonNull final List<Pair<String, ContractUrn>> emailMatchingKeys) {
    List<String []> keys = emailMatchingKeys.stream()
        .map(key -> convertToEspressoKeyString(key.getFirst(), key.getSecond()))
        .collect(Collectors.toList());
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_NAME)
        .setTable(TABLE_NAME)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setMultigetKeyList(keys)
        .build();
    return _parSeqEspressoClient.execute(request).map(this::parseEspressoResponse);
  }

  /**
   * Delete a manual member mapping for a hashed email
   * @param hashedEmail the hashed email for which the mapping is deleted
   * @param contractUrn the contract urn for which the mapping is deleted
   * @return The HTTP status of the delete operation
   */
  public Task<HttpStatus> deleteManualEmailMapping(String hashedEmail, ContractUrn contractUrn) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_NAME)
        .setTable(TABLE_NAME)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(hashedEmail, contractUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_204_NO_CONTENT;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return HttpStatus.S_404_NOT_FOUND;
      } else {
        String errMsg =
            String.format("Unexpected response code while deleting ManualEmailMapping for email: %s and contract: %s, %s",
                hashedEmail, contractUrn, deleteResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  private String[] convertToEspressoKeyString(@Nonnull final String hashedEmail,
      @Nonnull final ContractUrn contractUrn) {
    return new String[] { hashedEmail, contractUrn.toString() };
  }

  @Nonnull
  private List<Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping>> parseEspressoResponse(@Nonnull final GetResponse getResponse) {
    if (getResponse.getResponseStatus() == ResponseStatus.OK
        || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
      // The response will return a list of manual matches. Deserialize all of them.
      return getResponse.getParts().stream()
          .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 2) // The key length should be 2, i.e., email and contract
          .map(this::formatManualEmailToMemberMapping)
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
    } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
        || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
      return Collections.emptyList();
    } else {
      // Not expected. Parsing error indicates data corruption of some sort
      throw new RuntimeException(getResponse.getResponseStatus().getText());
    }
  }

  /**
   * convert the response part from espresso to ManualEmailToMemberMapping
   * @param part Serialized mapping record retrieved from espresso
   * @return A pair of hashed email and contract urn and the corresponding manual email to member mapping
   */
  @Nullable
  private Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping> formatManualEmailToMemberMapping(
      @NonNull GetResponse.Part part) {
    try {
      ManualEmailToMemberMapping matchedMemberMapping = EspressoUtil.deserializeSingleAvroRecord(READER, part);
      // Based on the table schema, the keys are hashedEmail and contract
      String hashedEmail = part.getContentLocation().getKey()[0];
      ContractUrn contractUrn = ContractUrn.deserialize(part.getContentLocation().getKey()[1]);
      return Pair.of(Pair.of(hashedEmail, contractUrn), matchedMemberMapping);
    } catch (Exception e) {
      LOG.error("Corrupted espresso record part {}", part, e);
      return null;
    }
  }

}
