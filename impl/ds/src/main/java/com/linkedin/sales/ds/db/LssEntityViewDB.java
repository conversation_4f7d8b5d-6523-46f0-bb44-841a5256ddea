package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.ProfileView;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This is the class to directly talk to espresso Database LssEntityView
 */
public class LssEntityViewDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssEntityViewDB.class);

  static final String DB_LSS_ENTITY_VIEW = "LssEntityView";
  static final String TABLE_PROFILE_VIEW = "ProfileView";
  private static final int PROFILE_VIEW_SCHEMA_VERSION = 1;
  // The Record TTL is 2 YEARS (365 * 2)
  private static final int RECORD_TTL_IN_DAYS = 730;

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // TODO: Temporarily set to 500 ms and tune based on P95 get/put call time to make sure the setting is good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private static final SpecificDatumReader<ProfileView> PROFILE_VIEW_READER =
      new SpecificDatumReader<>(ProfileView.SCHEMA$);

  private static final SpecificDatumWriter<ProfileView> PROFILE_VIEW_WRITER =
      new SpecificDatumWriter<>(ProfileView.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssEntityViewDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * update or create profileView record
   * @param viewerSeat seatUrn of the LSS user
   * @param viewedMember memberUrn of the viewed profile
   * @param profileView espresso ProfileView record
   * @return http status 200 if create or update successfully
   * otherwise throw exception
   */
  public Task<HttpStatus> upsertProfileView(@NonNull SeatUrn viewerSeat, @NonNull MemberUrn viewedMember,
      @NonNull ProfileView profileView) {
    if (profileView.contractUrn == null || profileView.lastViewedTime == 0) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          String.format("Required fields not set when updating the profile view %s", profileView));
    }

    byte[] bytes = EspressoUtil.serializeAvroRecord(PROFILE_VIEW_WRITER, profileView);
    //Using put request to update or create profileView
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_ENTITY_VIEW)
        .setTable(TABLE_PROFILE_VIEW)
        .setContent(ContentType.AVRO_BINARY, PROFILE_VIEW_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(viewerSeat.toString(), viewedMember.toString())
        .setExpires(
            Date.from(Instant.ofEpochMilli(profileView.lastViewedTime).plus(RECORD_TTL_IN_DAYS, ChronoUnit.DAYS)))
        .build();
    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.OK
          || putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_200_OK;
      } else {
        String errMsg =
            String.format("Unexpected response code for call to upsert the profileView for seat: %s, member: %s, %s",
                viewerSeat, viewedMember, putResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso ProfileView record
   * @param viewerSeat seatUrn of the LSS user
   * @param viewedMember memberUrn of the viewed profile
   * @return an espresso ProfileView record if it exists
   */
  public Task<ProfileView> getProfileView(@NonNull SeatUrn viewerSeat, @NonNull MemberUrn viewedMember) {

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_ENTITY_VIEW)
        .setTable(TABLE_PROFILE_VIEW)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, PROFILE_VIEW_SCHEMA_VERSION)
        .setKey(viewerSeat.toString(), viewedMember.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(PROFILE_VIEW_READER, getResponse.getPart(0));
      } else if ((getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() <= 0)
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not get ProfileView for viewerSeat :%s and viewedMember :%s", viewerSeat, viewedMember));
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get ProfileView " + "by: %s, %s with response: %s",
                viewerSeat, viewedMember, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }
}
