package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.LeadEditableContactInfo;
import com.linkedin.sales.espresso.LeadProfileUnlockInfo;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfoKey;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Arrays;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class LssLeadExtendedInfoDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssLeadExtendedInfoDB.class);

  static final String DB_LSS_LEAD_EXTENDED_INFO = "LssLeadExtendedInfo";
  static final String TABLE_LEAD_EDITABLE_CONTACT_INFO = "LeadEditableContactInfo";
  static final String TABLE_LEAD_PROFILE_UNLOCK_INFO = "LeadProfileUnlockInfo";
  private static final int LEAD_EDITABLE_CONTACT_INFO_SCHEMA_VERSION = 1;
  private static final int LEAD_PROFILE_UNLOCK_INFO_VERSION = 1;

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // As P95 get/write/delete/query are 70 ms at most. We think 500 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private static final SpecificDatumReader<LeadEditableContactInfo> LEAD_EDITABLE_CONTACT_INFO_READER =
      new SpecificDatumReader<>(LeadEditableContactInfo.SCHEMA$);

  private static final SpecificDatumReader<LeadProfileUnlockInfo> LEAD_PROFILE_UNLOCK_INFO_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(LeadProfileUnlockInfo.SCHEMA$);

  private static final SpecificDatumWriter<LeadEditableContactInfo> LEAD_EDITABLE_CONTACT_INFO_WRITER =
      new SpecificDatumWriter<>(LeadEditableContactInfo.SCHEMA$);

  private static final SpecificDatumWriter<LeadProfileUnlockInfo> LEAD_PROFILE_UNLOCK_INFO_SPECIFIC_DATUM_WRITER =
      new SpecificDatumWriter<>(LeadProfileUnlockInfo.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssLeadExtendedInfoDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * update or create leadEditableContactInfo
   * @param key the unique identifier of a leadeditablecontactinfo record
   * @param leadEditableContactInfo espresso LeadEditableContactInfo
   * @return http status 201 if create successfully, 200 if key already exits , otherwise throw exception
   */
  public Task<HttpStatus> upsertLeadEditableContactInfo(@NonNull SalesLeadEditableContactInfoKey key,
      @NonNull LeadEditableContactInfo leadEditableContactInfo) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(LEAD_EDITABLE_CONTACT_INFO_WRITER, leadEditableContactInfo);
    //Using put request to update or create leadEditableContactInfo
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_LEAD_EXTENDED_INFO)
        .setTable(TABLE_LEAD_EDITABLE_CONTACT_INFO)
        .setContent(ContentType.AVRO_BINARY, LEAD_EDITABLE_CONTACT_INFO_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key.getMember().toString(), key.getContract().toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return HttpStatus.S_422_UNPROCESSABLE_ENTITY;
      } else {
        String errMsg =
            String.format("Unexpected response code for call to upsert the leadEditableContactInfo for %s, %s", key,
                postResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso LeadProfileUnlockInfo
   * @param member memberUrn of the sales lead
   * @param contract contract contractUrn of the lss user
   * @return an espresso LeadProfileUnlockInfo if it exists
   */
  public Task<LeadProfileUnlockInfo> getLeadProfileUnlockInfo(@NonNull MemberUrn member,
      @NonNull ContractUrn contract) {

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LEAD_EXTENDED_INFO)
        .setTable(TABLE_LEAD_PROFILE_UNLOCK_INFO)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_PROFILE_UNLOCK_INFO_VERSION)
        .setKey(member.toString(), contract.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(LEAD_PROFILE_UNLOCK_INFO_SPECIFIC_DATUM_READER,
            getResponse.getPart(0));
      } else if ((getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() <= 0)
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not get LeadProfileUnlockInfo for memberUrn :%s and contractUrn :%s", member, contract));
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get LeadProfileUnlockInfo " + "by:%s, %s, %s", member,
                contract, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso LeadEditableContactInfo
   * @param key lead editable contact info key
   * @return an espresso LeadEditableContactInfo if it exists
   */
  public Task<LeadEditableContactInfo> getLeadEditableContactInfo(@NonNull SalesLeadEditableContactInfoKey key) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LEAD_EXTENDED_INFO)
        .setTable(TABLE_LEAD_EDITABLE_CONTACT_INFO)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_EDITABLE_CONTACT_INFO_SCHEMA_VERSION)
        .setKey(key.getMember().toString(), key.getContract().toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() > 0) {
        return EspressoUtil.deserializeSingleAvroRecord(LEAD_EDITABLE_CONTACT_INFO_READER, getResponse.getPart(0));
      } else if ((getResponse.getResponseStatus() == ResponseStatus.OK && getResponse.getPartCount() <= 0)
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("can not find leadEditableContactInfo:%s", key));
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get LeadEditableContactInfo" + " for list:%s, %s", key,
                getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete an espresso LeadEditableContactInfo
   * @param key lead editable contact info key
   * @return http status if delete response status is 204/404, otherwise throw exception
   */
  public Task<HttpStatus> deleteEditableContactInfo(@NonNull SalesLeadEditableContactInfoKey key) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LEAD_EXTENDED_INFO)
        .setTable(TABLE_LEAD_EDITABLE_CONTACT_INFO)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key.getMember().toString(), key.getContract().toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      // https://iwww.corp.linkedin.com/wiki/cf/display/ENGS/ESPRESSO+REST+API#ESPRESSORESTAPI-DELETE
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return HttpStatus.fromCode(deleteResponse.getResponseStatus().getCode());
      } else {
        String errMsg =
            String.format("Unexpected response code for call to delete LeadEditableContactInfo" + " for list:%s, %s", key,
                deleteResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create an espresso leadProfileUnlockInfo
   * @param member the memberUrn of sales lead
   * @param contract the contractUrn of the lss user who is the owner
   * @param leadProfileUnlockInfo espresso LeadProfileUnlockInfo to be saved in DB
   * @return 201 if create successfully, 200 if there is conflict, otherwise throw exception
   */
  public Task<HttpStatus> createLeadProfileUnlockInfo(@NonNull MemberUrn member, @NonNull ContractUrn contract,
      @NonNull LeadProfileUnlockInfo leadProfileUnlockInfo) {
    byte[] bytes =
        EspressoUtil.serializeAvroRecord(LEAD_PROFILE_UNLOCK_INFO_SPECIFIC_DATUM_WRITER, leadProfileUnlockInfo);
    String[] keys = new String[]{member.toString(), contract.toString()};

    //Set If-None-Match etag to PUT request, will perform the operation only if the resource does not exist
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_LEAD_EXTENDED_INFO)
        .setTable(TABLE_LEAD_PROFILE_UNLOCK_INFO)
        .setIfNoneMatchEtag("*")
        .setContent(ContentType.AVRO_BINARY, LEAD_PROFILE_UNLOCK_INFO_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .build();

    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      switch (putResponse.getResponseStatus()) {
        case CREATED:
          return HttpStatus.S_201_CREATED;
        // Since If-None-Match etag has been set for the PUT request, response would be 412_PRECONDITION_FAILED if there is any existing resource
        case PRECONDITION_FAILED:
          return HttpStatus.S_200_OK;
        default: {
          String errMsg = String.format(
              "Unexpected response code for call to create leadProfileUnlockInfo " + "for member %s, contract %s: %s",
              member, contract, putResponse);
          throw new RuntimeException(errMsg);
        }
      }
    });
  }
}
