package com.linkedin.sales.ds.db;

import com.google.common.base.Preconditions;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.query.LuceneQuery;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.Header;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.TxMultiPutRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.AccountToListMapping;
import com.linkedin.sales.espresso.CreatorToListCsvImportView;
import com.linkedin.sales.espresso.DefaultList;
import com.linkedin.sales.espresso.EntityToListView;
import com.linkedin.sales.espresso.ImportTaskToListCsvImportView;
import com.linkedin.sales.espresso.List;
import com.linkedin.sales.espresso.ListCsvImport;
import com.linkedin.sales.espresso.ListEntity;
import com.linkedin.sales.espresso.ListEntityCount;
import com.linkedin.sales.espresso.ListSource;
import com.linkedin.sales.espresso.ListToListCsvImportView;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.SeatList;
import com.linkedin.sales.espresso.SeatToListView;
import com.linkedin.sales.espresso.RelationshipMapChangeLog;
import com.linkedin.saleslist.ListCsvImportState;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.commons.lang3.ArrayUtils;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.ds.rest.ClientConstants.*;
import static java.lang.Boolean.*;

/**
 * Created by hacao at 6/13/2018
 * This is the class to directly talk to espresso Database LssList
 */
public class LssListDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssListDB.class);

  static final String DB_LSS_LIST = "LssList";
  static final String TABLE_LIST = "List";
  static final String TABLE_LIST_ENTITY = "ListEntity";
  static final String TABLE_LIST_ENTITY_COUNT = "ListEntityCount";
  static final String TABLE_SEAT_TO_LIST_VIEW = "SeatToListView";
  static final String TABLE_SEAT_LIST = "SeatList";
  static final String TABLE_ENTITY_TO_LIST_VIEW = "EntityToListView";
  static final String TABLE_LIST_CSV_IMPORT = "ListCsvImport";
  static final String TABLE_CREATOR_TO_LIST_CSV_IMPORT_VIEW = "CreatorToListCsvImportView";
  static final String TABLE_IMPORT_TASK_TO_LIST_CSV_IMPORT_VIEW = "ImportTaskToListCsvImportView";
  static final String TABLE_LIST_TO_LIST_CSV_IMPORT_VIEW = "ListToListCsvImportView";
  static final String TABLE_ACCOUNT_TO_LIST_MAPPING = "AccountToListMapping";
  static final String TABLE_DEFAULT_LIST = "DefaultList";
  static final String TABLE_RELATIONSHIP_MAP_CHANGE_LOG = "RelationshipMapChangeLog";

  // the default time range is used for sort by timestamp, the range should contain all available timestamps
  private static final String DEFAULT_TIME_RANGE = String.format("[%d TO %d]", 0, Long.MAX_VALUE);
  private static final String SEAT_LIST_FIELD_LAST_VIEWED_TIME = "lastViewedTime";
  private static final String LIST_FIELD_NAME = "name";
  private static final String LIST_FIELD_LAST_MODIFIED_TIME = "lastModifiedTime";
  private static final String LIST_FIELD_LAST_MODIFIED_BY_SEAT_URN = "lastModifiedBySeatUrn";
  private static final String LIST_FIELD_DESCRIPTION = "description";
  private static final String LIST_FIELD_LAST_VIEWED_TIME = "lastViewedTime";
  private static final String LIST_ENTITY_FIELD_LAST_MODIFIED_TIME = "lastModifiedTime";
  private static final String LIST_ENTITY_FIELD_PRIORITY = "priority";
  private static final String LIST_ENTITY_FIELD_PRIORITY_LAST_MODIFICATION_TIME = "priorityLastModifiedTime";
  private static final String LIST_ENTITY_FIELD_PRIORITY_LAST_MODIFICATION_BY_SEAT_URN = "priorityLastModifiedSeatUrn";
  private static final String LIST_ENTITY_FIELD_SORT_ORDER = "sortOrder";
  private static final String LIST_CSV_IMPORT_FIELD_STATE = "state";
  private static final String LIST_CSV_IMPORT_FIELD_CREATED_TIME = "createdTime";
  private static final String LIST_CSV_IMPORT_FIELD_LAST_MODIFIED_TIME = "lastModifiedTime";
  private static final String CREATOR_TO_LIST_CSV_IMPORT_DEFAULT_SORT = LIST_CSV_IMPORT_FIELD_CREATED_TIME + " DESC";

  private static final int SCHEMA_VERSION = 1;
  private static final int LIST_SCHEMA_VERSION = 19;
  private static final int LIST_ENTITY_SCHEMA_VERSION = 8;
  private static final int ENTITY_TO_LIST_VIEW_SCHEMA_VERSION = 2;
  private static final int SEAT_TO_LIST_VIEW_SCHEMA_VERSION = 1;
  private static final int LIST_CSV_IMPORT_SCHEMA_VERSION = 5;
  private static final int CREATOR_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION = 4;
  private static final int ACCOUNT_TO_LIST_MAPPING_SCHEMA_VERSION = 3;
  private static final int IMPORT_TASK_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION = 3;
  private static final int LIST_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION = 3;
  private static final int DEFAULT_LIST_SCHEMA_VERSION = 1;
  private static final int RELATIONSHIP_MAP_CHANGE_LOG_SCHEMA_VERSION = 3;

  private static final int RELATIONSHIP_MAP_CHANGE_LOG_TTL_DAYS = 180;

  private static final int MAXIMUM_ESPRESSO_FETCH_COUNT = 1000;

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG_MULTI_PUT;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(
        ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // As P95 get/write/delete/query are 70 ms at most. We think 500 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);

    RETRY_REQUEST_CONFIG_MULTI_PUT = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetryTimeoutMillis(1000L);
  }

  private static final SpecificDatumReader<List> LIST_READER =
      new SpecificDatumReader<>(List.SCHEMA$);

  private static final SpecificDatumReader<ListEntity> LIST_ENTITY_READER =
      new SpecificDatumReader<>(ListEntity.SCHEMA$);

  private static final SpecificDatumReader<ListEntityCount> LIST_ENTITY_COUNT_READER =
      new SpecificDatumReader<>(ListEntityCount.SCHEMA$);

  private static final SpecificDatumReader<EntityToListView> ENTITY_TO_LIST_VIEW_READER =
      new SpecificDatumReader<>(EntityToListView.SCHEMA$);

  private static final SpecificDatumReader<SeatList> SEAT_LIST_READER =
      new SpecificDatumReader<>(SeatList.SCHEMA$);

  private static final SpecificDatumWriter<List> LIST_WRITER =
      new SpecificDatumWriter<>(List.SCHEMA$);

  private static final SpecificDatumWriter<ListEntity> LIST_ENTITY_WRITER =
      new SpecificDatumWriter<>(ListEntity.SCHEMA$);

  private static final SpecificDatumWriter<SeatList> SEAT_LIST_WRITER =
      new SpecificDatumWriter<>(SeatList.SCHEMA$);

  private static final SpecificDatumReader<SeatToListView> SEAT_TO_LIST_VIEW_READER =
      new SpecificDatumReader<>(SeatToListView.SCHEMA$);

  private static final SpecificDatumWriter<ListCsvImport> LIST_CSV_IMPORT_WRITER =
      new SpecificDatumWriter<>(ListCsvImport.SCHEMA$);

  private static final SpecificDatumReader<ListCsvImport> LIST_CSV_IMPORT_READER =
      new SpecificDatumReader<>(ListCsvImport.SCHEMA$);

  private static final SpecificDatumReader<CreatorToListCsvImportView> CREATOR_TO_LIST_CSV_IMPORT_READER =
      new SpecificDatumReader<>(CreatorToListCsvImportView.SCHEMA$);

  private static final SpecificDatumReader<ImportTaskToListCsvImportView> IMPORT_TASK_TO_LIST_CSV_IMPORT_READER =
      new SpecificDatumReader<>(ImportTaskToListCsvImportView.SCHEMA$);

  private static final SpecificDatumReader<ListToListCsvImportView> LIST_TO_LIST_CSV_IMPORT_READER =
      new SpecificDatumReader<>(ListToListCsvImportView.SCHEMA$);

  private static final SpecificDatumReader<AccountToListMapping> ACCOUNT_TO_LIST_MAPPING_READER =
      new SpecificDatumReader<>(AccountToListMapping.SCHEMA$);

  private static final SpecificDatumWriter<AccountToListMapping> ACCOUNT_TO_LIST_MAPPING_WRITER =
      new SpecificDatumWriter<>(AccountToListMapping.SCHEMA$);

  private static final SpecificDatumReader<DefaultList> DEFAULT_LIST_READER =
      new SpecificDatumReader<>(DefaultList.SCHEMA$);

  private static final SpecificDatumWriter<DefaultList> DEFAULT_LIST_WRITER =
      new SpecificDatumWriter<>(DefaultList.SCHEMA$);

  private static final SpecificDatumWriter<RelationshipMapChangeLog> RELATIONSHIP_MAP_CHANGE_LOG_WRITER =
      new SpecificDatumWriter<>(RelationshipMapChangeLog.SCHEMA$);

  private static final SpecificDatumReader<RelationshipMapChangeLog> RELATIONSHIP_MAP_CHANGE_LOG_READER =
      new SpecificDatumReader<>(RelationshipMapChangeLog.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssListDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }


  /**
   * create an espresso list.
   * @param listId list id
   * @param list espresso list that to be saved in DB
   * @return id of the created list
   */
  public Task<Long> createList(long listId, @NonNull List list) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(LIST_WRITER, list);

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST)
        .setContent(ContentType.AVRO_BINARY, LIST_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return listId;
      } else {
        String errMsg = String.format("Unexpected response code for call to to create list for %d: %s", listId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete an espresso list
   * @param listId list id
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteList(long listId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to delete list for list:%d, %s", listId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete all list entities under a certain list
   * @param listId list id
   * @return if the deletion succeed. True if succeed.
   */
  public Task<Boolean> deleteAllListEntities(long listId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();
    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      // If the delete succeeds or delete accept(meaning the request is received and acknowledged by the server, and will continue to be
      // processed after the client received this response.) or if there is nothing to delete (meaning there is no entity in the list),
      // we all return true
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || deleteResponse.getResponseStatus() == ResponseStatus.ACCEPTED || deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return TRUE;
      } else {
        String errMsg = String.format("Unexpected response code for call to delete all list entities for list:%d, %s", listId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso list
   * @param listId list Id
   * @return an espresso list if exist
   */
  public Task<List> getList(long listId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST)
        .setAcceptType(ContentType.AVRO_BINARY, LIST_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return EspressoUtil.deserializeSingleAvroRecord(LIST_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null, String.format("can not find list:%d", listId));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("can not find list:%d", listId));
      } else {
        String errMsg = String.format("Unexpected response code for call to get list for list:%d, %s", listId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * update name, last modified time, last modifier of a list.
   * @param listId list Id
   * @param list espresso list that contains update fields
   * @return True if succeed. False if not found
   */
  public Task<Boolean> updateList(long listId, @NonNull List list) {
    JSONObject obj = new JSONObject();
    if (list != null) {
      if (list.name != null) {
        obj.put(LIST_FIELD_NAME, list.name);
      }

      if ((list.lastModifiedTime != 0) || !obj.isEmpty()) {
        // if lastModifiedTime is not set, but name is set, need to update last modified time to current time.
        long lastModifiedTime = (list.lastModifiedTime != 0) ? list.lastModifiedTime : System.currentTimeMillis();
        obj.put(LIST_FIELD_LAST_MODIFIED_TIME, lastModifiedTime);
      }

      if (list.lastViewedTime != null) {
        obj.put(LIST_FIELD_LAST_VIEWED_TIME, list.lastViewedTime);
      }

      if (list.description != null) {
        obj.put(LIST_FIELD_DESCRIPTION, list.description);
      }

      if (list.lastModifiedBySeatUrn != null) {
        obj.put(LIST_FIELD_LAST_MODIFIED_BY_SEAT_URN, list.lastModifiedBySeatUrn);
      }
    }

    if (obj.isEmpty()) {
      // There is no change, no need to do DB update, return true because idempotent behavior.
      return Task.value(TRUE);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error(String.format("fail to convert JSONObject to String when updating list:%d", listId), e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST)
        .setContent(ContentType.AVRO_JSON, LIST_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        // when the list does not exist, it will try to create and fail because of incomplete data
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to update list for %d: %s", listId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get list entity count for a list
   * @param listId list Id
   * @return the entity count
   */
  public Task<Long> getListEntityCount(long listId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY_COUNT)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId))
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          // even for the case that no such count for listId, it will still return 0
          return EspressoUtil.deserializeSingleAvroRecord(LIST_ENTITY_COUNT_READER, getResponse.getPart(0)).totalCount;
        } else {
          throw new EntityNotFoundException(null, String.format("can not find list entity count:%d", listId));
        }
      } else {
        String errMsg = String.format("Unexpected response code for call to get list entity count for list:%d, %s", listId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * create an espresso seatList.
   * @param seatId seat id
   * @param listId list id
   * @param listType type of the list, LEAD or ACCOUNT
   * @param seatList espresso seatList that to be saved in DB
   * @return if the creation succeed. True if succeed. False means the record has been created before
   */
  public Task<Boolean> createSeatList(long seatId, long listId, @NonNull ListType listType, @NonNull SeatList seatList) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(SEAT_LIST_WRITER, seatList);

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_LIST)
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(new String[] {String.valueOf(seatId), listType.toString(), String.valueOf(listId)})
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.OK) { // conflict happens, eg: create same obj for twice
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to create seatList for seat:%d - list:%d, %s", seatId, listId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * update the lastViewedTime of seatList
   * @param seatId seat Id
   * @param listId list Id
   * @param listType list type
   * @param seatList espresso seatList that contains update fields
   * @return if the update succeed. True if succeed. False if not found
   */
  public Task<Boolean> updateSeatList(
      long seatId,
      long listId,
      @NonNull ListType listType,
      @NonNull SeatList seatList) {
    // if lastViewedTime is not set, no need to do update
    if (seatList.lastViewedTime == 0) {
      return Task.value(TRUE);
    }

    JSONObject obj = new JSONObject();
    obj.put(SEAT_LIST_FIELD_LAST_VIEWED_TIME, seatList.lastViewedTime);
    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_LIST)
        .setContent(ContentType.AVRO_JSON, SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(new String[] {String.valueOf(seatId), listType.toString(), String.valueOf(listId)})
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to update seatList for seat:%d - list:%d, %s", seatId, listId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso seatList
   * @param seatId seat Id
   * @param listId list Id
   * @param listType list type
   * @return an espresso seatList if exist
   */
  public Task<SeatList> getSeatList(long seatId, long listId, @NonNull ListType listType) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_LIST)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(new String[] {String.valueOf(seatId), listType.toString(), String.valueOf(listId)})
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return EspressoUtil.deserializeSingleAvroRecord(SEAT_LIST_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null,
              String.format("can not find seatList for seat:%d, list:%d, listType:%s", seatId, listId, listType.toString()));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not find seatList for seat:%d, list:%d, listType:%s", seatId, listId, listType.toString()));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get seatList for seat:%d, listType:%s, list:%d, %s", seatId,
            listType.toString(), listId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get all espresso seatLists of a seat
   * @param seatId seat Id
   * @param listType list type
   * @param start paging start
   * @param count paging count
   * @return list of seatLists with totalHits in Pair<totalHists, List<Pair<listId, seatLIst>>>
   */
  public Task<Pair<Integer, java.util.List<Pair<Long, SeatList>>>> getSeatLists(
      long seatId,
      @NonNull ListType listType,
      int start,
      int count) {
    String defaultSort = "lastViewedTime DESC";
    String queryFilter = "lastViewedTime:" + DEFAULT_TIME_RANGE;
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_LIST)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(new String[]{String.valueOf(seatId), listType.toString()})
        .setQuery(queryFilter)
        .setSort(defaultSort)
        .setStart(start)
        .setCount(count)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        Integer totalHits = getResponse.getTotalHits() != null ? getResponse.getTotalHits() : getResponse.getParts().size();

        java.util.List<Pair<Long, SeatList>> pairs = getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)
            .map(part -> {
              String listId = part.getContentLocation().getKey()[2];
              SeatList seatList = EspressoUtil.deserializeSingleAvroRecord(SEAT_LIST_READER, part);
              return new Pair<>(Long.valueOf(listId), seatList);
            })
            .collect(Collectors.toList());
        return new Pair<>(totalHits, pairs);
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("can not find seatLists for seat:%d", seatId));
      } else {
        String errMsg = String.format("Unexpected response code for call to get seatLists for seat:%d, %s", seatId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get all espresso lists of a seat with certain listType and listSource, read from table SeatToListView
   * @param seatId seat Id
   * @param listType list type
   * @param listSource could be CRM_SYNC, LINKEDIN_SIALES_INSIGHT, MANUAL, SYSTEM
   * @param start paging start
   * @param count paging count
   * @return list of seatLists with totalHits in Pair<totalHists, List<Pair<listId, seatLIst>>>
   */
  public Task<Pair<Integer, java.util.List<Pair<Long, SeatToListView>>>> getListsForSeatToListView(long seatId,
      @NonNull ListType listType, @Nullable ListSource listSource, int start, int count) {

    int effectiveCount = count;
    if (count > MAXIMUM_ESPRESSO_FETCH_COUNT) {
      LOG.warn("The count: {} in the call to get lists for a seat:{} is greater than 1000. ", count, seatId);
      effectiveCount = MAXIMUM_ESPRESSO_FETCH_COUNT;
    }

    String[] queryKeys = listSource == null ? new String[]{String.valueOf(seatId), listType.toString()}
        : new String[]{String.valueOf(seatId), listType.toString(), listSource.toString()};
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_TO_LIST_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, SEAT_TO_LIST_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(queryKeys)
        .setStart(start)
        .setCount(effectiveCount)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      // getResponse.getResponseStatus returns 200 even when no lists for a given source type is found.
      // Therefore, we need to check the response type of the part.
      if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || getResponse.getParts().get(0).getResponseStatus().getCode() == HttpStatus.S_404_NOT_FOUND.getCode()) {
        throw new EntityNotFoundException(null,
            String.format("cannot find lists from seatToListView for seat:%d", seatId));
      } else if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        Integer totalHits =
            getResponse.getTotalHits() != null ? getResponse.getTotalHits() : getResponse.getParts().size();

        java.util.List<Pair<Long, SeatToListView>> pairs = getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 4)
            .map(part -> {
              String listId = part.getContentLocation().getKey()[3];
              SeatToListView seatToListView = EspressoUtil.deserializeSingleAvroRecord(SEAT_TO_LIST_VIEW_READER, part);
              return new Pair<>(Long.valueOf(listId), seatToListView);
            })
            .collect(Collectors.toList());
        return new Pair<>(totalHits, pairs);
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get lists from seatToListView for seat:%d, %s", seatId,
                getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get all list ids to which a list entity belongs
   * @param entityUrn list entity, member or organization
   * @param contractId contract id of list
   * @param seatId id of seat that create the list entity. If null, get all lists a entity belongs to in a contract.
   * @return list of listIds
   */
  public Task<java.util.List<Long>> getListIdsForEntity(@NonNull Urn entityUrn, long contractId, @Nullable Long seatId) {
    String filterQuery = "createdTime:" + DEFAULT_TIME_RANGE;
    String[] queryKeys = seatId == null ? new String[]{entityUrn.toString(), String.valueOf(contractId)}
        : new String[]{entityUrn.toString(), String.valueOf(contractId), String.valueOf(seatId)};

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_ENTITY_TO_LIST_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, ENTITY_TO_LIST_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(queryKeys)
        .setQuery(filterQuery) // need the query to get result, otherwise it returns 404 not found
        .setStart(0)
        .setCount(MAXIMUM_ESPRESSO_FETCH_COUNT) // Setting count to -1 should be avoided according to go/espressoapi
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 4) //4 keys: entityUrn, contractId, ownerSeatId, listId
            .map(part -> {
              String listId = part.getContentLocation().getKey()[3];
              return Long.valueOf(listId);
            }).collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("can not find listIds for entity:%s, contract: %d, ownerSeat: %d",
            entityUrn.toString(), contractId, seatId));
      } else {
        String errMsg = String.format("Unexpected response code for call to get listIds for entity:%s, contract: %d, ownerSeat: %d, %s",
            entityUrn.toString(), contractId, seatId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete an espresso seatList
   * @param seatId seat Id
   * @param listId list Id
   * @param listType list type
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteSeatList(long seatId, long listId, @NonNull ListType listType) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_SEAT_LIST)
        .setKey(new String[]{String.valueOf(seatId), listType.toString(), String.valueOf(listId)})
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to delete seatList for seat:%d, list:%d, listType:%s, %s",
            seatId, listId, listType.toString(), deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * create an espresso list entity.
   * @param listId list id
   * @param entityUrn entity urn, member or organization
   * @param listEntity the list entity
   * @return if the creation succeeds. true means successfully created. false means there is conflict. Others will throw exception
   */
  public Task<Boolean> createListEntity(long listId, @NonNull Urn entityUrn, @NonNull ListEntity listEntity) {

    byte[] bytes = EspressoUtil.serializeAvroRecord(LIST_ENTITY_WRITER, listEntity);

    PostRequest request = PostRequest.builder().
        setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setContent(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION, bytes)
        .setKey(String.valueOf(listId), entityUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      //in espresso. Conflict will return ResponseStatus.OK for the creation
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return FALSE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return TRUE;
      }
      throw new RuntimeException(
          String.format("Unexpected response code for call to to create entry for %s-->%s: %s", listId, entityUrn,
              postResponse));
    });
  }

  /**
   * Delete a list entity from the list
   * @param listId the list id
   * @param entityUrn the entity Urn
   * @param isLowPriorityRequest use to set x-espresso-priority header to low.
   * @return true means delete successful. False means not found. The other will throw exception
   */
  public Task<Boolean> deleteListEntity(long listId, @NonNull Urn entityUrn, boolean isLowPriorityRequest) {
    DeleteRequest.Builder requestBuilder = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setKey(String.valueOf(listId), entityUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG);
    if (isLowPriorityRequest) {
      requestBuilder.setRequestPriority(Header.RequestPriority.PRIORITY_LOW);
    }

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(deleteResponse -> {
      //Espresso will return no content if deletion succeeds, and not found if there is no record to delete.
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      }
      throw new RuntimeException(
          String.format("Unexpected response code for call to to delete entry for %s-->%s: %s", listId, entityUrn,
              deleteResponse));
    });
  }

  /**
   * Get all the list entities given a certain list id, sorted by field of sortOrder.
   * @param listId list id
   * @param start start
   * @param count count
   * @param sortOrder sort order
   * @return the pair, including the totalHits. Using the map since espresso listEntity does not contain the entityUrn (from key)
   */
  public Task<Pair<Integer, java.util.List<Pair<Urn, ListEntity>>>> getListEntities(long listId, int start, int count,
      @NonNull SortOrder sortOrder) {
    //Support sort by SortOrder field only. We will not change this for getting the data. other sorts can be supported after decoration.
    String sort = (sortOrder == SortOrder.ASCENDING) ? "sortOrder ASC" : "sortOrder DESC";

    //This will not filter out anything. But without this query string, the total hits cannot be returned.
    String filterQuery = "sortOrder:" + DEFAULT_TIME_RANGE;

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setKey(String.valueOf(listId))
        .setAcceptType(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setStart(start)
        .setCount(count)
        .setSort(sort)
        .setQuery(filterQuery)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      Integer totalHits =
          getResponse.getTotalHits() != null ? getResponse.getTotalHits() : getResponse.getParts().size();
      //We cannot use map since it cannot keep the sorting order
      java.util.List<Pair<Urn, ListEntity>> listEntityPair = getResponse.getParts()
          .stream()
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 2)
          .map(part -> {
            Urn entityUrn = createUrnFromString(part.getContentLocation().getKey()[1]);
            return new Pair<>(entityUrn, EspressoUtil.deserializeSingleAvroRecord(LIST_ENTITY_READER, part));
          })
          .collect(Collectors.toList());
      return new Pair<>(totalHits, listEntityPair);
    });
  }


  /**
   * Batch get list entities given the associated keys
   * @param keyList keys of the entities
   * @return a map of pair(sales list urn, entity urn) to ListEntity mappings
   */
  public Task<Map<Pair<SalesListUrn, Urn>, ListEntity>> batchGetListEntities(@NonNull java.util.List<Pair<SalesListUrn, Urn>> keyList) {
    java.util.List<Task<AbstractMap.SimpleEntry<Pair<SalesListUrn, Urn>, ListEntity>>> getListEntityTasks =
        keyList.stream().map(key -> {
          GetRequest request = GetRequest.builder()
              .setDatabase(DB_LSS_LIST)
              .setTable(TABLE_LIST_ENTITY)
              .setAcceptType(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION)
              .setRetryConfig(RETRY_REQUEST_CONFIG)
              .setKey(key.getFirst().getId(), key.getSecond().toString())
              .build();

          return _parSeqEspressoClient.execute(request).map(getResponse -> {
            AbstractMap.SimpleEntry<Pair<SalesListUrn, Urn>, ListEntity> resultEntry =
                new AbstractMap.SimpleEntry<>(new Pair<>(key.getFirst(), key.getSecond()), null);

            if (getResponse.getResponseStatus() == ResponseStatus.OK) {
              if (getResponse.getPartCount() > 0) {
                GetResponse.Part part = getResponse.getPart(0);
                if (part != null && part.getContentLocation() != null && part.getContentLocation().getKey() != null
                    && part.getContentLocation().getKey().length == 2 && part.getResponseStatus() == ResponseStatus.OK) {
                  resultEntry.setValue(EspressoUtil.deserializeSingleAvroRecord(LIST_ENTITY_READER, part));
                  return resultEntry;
                }
              }
              LOG.warn("failed to get list entity, list: {}, entity: {}", key.getFirst(), key.getSecond());
            } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
              LOG.warn("failed to find list entity, list: {}, entity: {}", key.getFirst(), key.getSecond());
            } else {
              LOG.warn("Unexpected response code for call to get listEntity for list: {}, entity: {}", key.getFirst(), key.getSecond());
            }

            return resultEntry;
          });
        }).collect(Collectors.toList());

    return Task.par(getListEntityTasks)
        .map(entryList -> entryList.stream().filter(entry -> entry.getValue() != null).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /**
   * Get the list of entityUrns for the given set of list Ids
   * @param listIds set of list id's
   * @param start start
   * @param count count
   * @return the list of entity Urns
   */
  public Task<java.util.List<Urn>> getEntityUrnsForListEntitiesForMultipleLists(Set<Long> listIds, int start,
      int count) {
    if (listIds.isEmpty()) {
      return Task.value(Collections.emptyList());
    }
    // This is used to avoid returning doc data since we are only interested in keys.
    // Without this query string, the espresso responds with Bad Request.
    String noDocData = "noDocData";
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setMultigetKeyList(
            listIds.stream().map(listId -> new String[]{String.valueOf(listId)}).collect(Collectors.toList()))
        .setAcceptType(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setStart(start)
        .setCount(count)
        .setViewFields(noDocData)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK
          || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> createUrnFromString(part.getContentLocation().getKey()[1]))
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg = String.format("Unexpected response code for call to get list entities: %s", getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get list entity by given list id and entity urn
   * @param listId list ID
   * @param entityUrn entity urn
   * @return an espresso list entity if exist
   */
  public Task<ListEntity> getListEntity(long listId, @NonNull Urn entityUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setAcceptType(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId), entityUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          GetResponse.Part part = getResponse.getPart(0);
          if (part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 2) {
            return EspressoUtil.deserializeSingleAvroRecord(LIST_ENTITY_READER, part);
          }
        }
        throw new EntityNotFoundException(null,
            String.format("failed to get listEntity, list:%d, entity:%s", listId, entityUrn));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not find listEntity, list:%d, entity:%s", listId, entityUrn));
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get listEntity for list:%d, entity:%s, %s", listId,
                entityUrn, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * update last modified time and sort order of a list entity
   * @param listId list ID
   * @param entityUrn entity urn
   * @param listEntity an espresso list entity, if the lastModifiedTime of it is null, will return true directly with no update.
   * @return true if succeed, False if not found
   */
  public Task<Boolean> updateListEntity(long listId, @NonNull Urn entityUrn, @NonNull ListEntity listEntity) {
    if (listEntity.lastModifiedTime == null) {
      // No change
      return Task.value(TRUE);
    }

    JSONObject obj = new JSONObject();
    obj.put(LIST_ENTITY_FIELD_LAST_MODIFIED_TIME, listEntity.lastModifiedTime);
    // set last modified time to sort order.
    // todo, update sortOrder comment in file ListEntity/2.avsc, the default value will be last modified time but not created time.
    obj.put(LIST_ENTITY_FIELD_SORT_ORDER, listEntity.lastModifiedTime);

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error(String.format("fail to convert JSONObject to String when updating listEntity, list:%d, entity:%s",
          listId, entityUrn), e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_ENTITY)
        .setContent(ContentType.AVRO_JSON, LIST_ENTITY_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listId), entityUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        // when the list entity does not exist, it will try to create and fail because of incomplete data
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to update listEntity for list:%d entity:%s response:%s",
            listId, entityUrn, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get the entity count for lists (how many lists an entity belongs to)
   * @param entityUrns the entity urn
   * @param contractId contractId for the user
   * @param seatId seatId for the user
   * @return pair of urn and the entity count
   */
  public Task<Map<Urn, Integer>> getEntityCounts(@NonNull Set<Urn> entityUrns, long contractId, long seatId) {
    //This will not filter out anything. But without this query string, the total hits cannot be returned.
    String filterQuery = "createdTime:" + DEFAULT_TIME_RANGE;

    //For now since we do not have share, we will specify the seatId also to get the count.
    //In the future when we have sharing, we need to change the behavior to only get contract result and intersect with the seatList table
    java.util.List<Task<AbstractMap.SimpleEntry<Urn, Integer>>> taskList = entityUrns.stream().filter(Objects::nonNull)
        .map(entityUrn -> {
          GetRequest request =
              GetRequest.builder()
                  .setDatabase(DB_LSS_LIST)
                  .setTable(TABLE_ENTITY_TO_LIST_VIEW)
                  .setAcceptType(ContentType.AVRO_BINARY, ENTITY_TO_LIST_VIEW_SCHEMA_VERSION)
                  .setRetryConfig(RETRY_REQUEST_CONFIG)
                  .setKey(entityUrn.toString(), String.valueOf(contractId), String.valueOf(seatId))
                  .setStart(0)
                  .setCount(1)
                  .setQuery(filterQuery)
                  .setQueryReturnTotalHits(TRUE)
                  .build();
          return _parSeqEspressoClient.execute(request)
              .map(getResponse -> {
                if (getResponse.getResponseStatus() == ResponseStatus.OK
                    || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
                  return new AbstractMap.SimpleEntry<>(entityUrn, getResponse.getTotalHits());
                } else {
                  LOG.error("Unexpected response from espresso while performing get list count for listEntity {} {}", entityUrn,
                      getResponse.getResponseStatus());
                  return new AbstractMap.SimpleEntry<>(entityUrn, 0);
                }
              });
        }).collect(Collectors.toList());
    return Task.par(taskList)
        .map(entryList -> entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /**
   * Create/update multiple Espresso ListEntities with multi-put
   * It is recommended not to batch writes with size larger than 100
   * @param listUrn list to which the list entities belong to.
   * @param entityUrnToListEntityMap map of entityUrn to espresso ListEntities to be saved in the DB
   * @param isLowPriorityRequest use to set x-espresso-priority header to low.
   * @return map of entityUrn to Http Status
   * return 201 if create successfully, 200 if there is conflict (or update), otherwise return 500
   */
  public Task<Map<Urn, HttpStatus>> upsertListEntities(SalesListUrn listUrn,
      Map<Urn, ListEntity> entityUrnToListEntityMap, boolean isLowPriorityRequest) {
    Preconditions.checkArgument(entityUrnToListEntityMap.size() <= ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE,
        String.format("Espresso multi-put batch size %d is larger than recommended size %d",
            entityUrnToListEntityMap.size(), ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE));

    if (entityUrnToListEntityMap.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    TxMultiPutRequest.Builder multiPutRequestBuilder = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setRetryConfig(RETRY_REQUEST_CONFIG_MULTI_PUT)
        .setKey(listUrn.getIdAsLong().toString());

    //Test header is present in outbound request.
    if (isLowPriorityRequest) {
      multiPutRequestBuilder.setRequestPriority(Header.RequestPriority.PRIORITY_LOW);
    }

    entityUrnToListEntityMap.forEach((key, value) -> {
      TxMultiPutRequest.Part.Builder partBuilder = TxMultiPutRequest.Part.builder()
          .setTable(TABLE_LIST_ENTITY)
          .setSubkey(key.toString())
          .setContent(ContentType.AVRO_BINARY, LIST_ENTITY_SCHEMA_VERSION,
              EspressoUtil.serializeAvroRecord(LIST_ENTITY_WRITER, value));

      multiPutRequestBuilder.addPart(partBuilder.build());
    });

    Map<Urn, HttpStatus> resultMap = new HashMap<>();

    return _parSeqEspressoClient.execute(multiPutRequestBuilder.build()).map(multiPutResponse -> {
      // Successful multi-put operation returns 200, each individual sub response may have 200 OK or 201 Created
      // If there is only one sub-request, multi-put response would be same as sub-request response
      if (multiPutResponse.getResponseStatus() == ResponseStatus.OK
          || multiPutResponse.getResponseStatus() == ResponseStatus.CREATED) {
        Map<Urn, HttpStatus> responseMap = multiPutResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              String listIdString = part.getContentLocation().getKey()[0];
              String entityUrnString = part.getContentLocation().getKey()[1];
              int status = part.getResponseStatus().getCode();
              try {
                return new AbstractMap.SimpleEntry<>(new Urn(entityUrnString), HttpStatus.fromCode(status));
              } catch (URISyntaxException e) {
                LOG.error("Fail to generate the listId or entityUrn for list {}, entity {}", listIdString,
                    entityUrnString, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        entityUrnToListEntityMap.keySet().forEach(key -> {
          HttpStatus status = responseMap.get(key);
          if (status != null) {
            resultMap.put(key, status);
          } else {
            LOG.error("Cannot get response when saving list entity {} for list {}", key, listUrn);
            resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR);
          }
        });
      } else {
        LOG.error("Unexpected response code for call to batch put list entities to Espresso for list {}: {}", listUrn,
            multiPutResponse.getResponseStatus());
        entityUrnToListEntityMap.keySet().forEach(key -> resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      }
      return resultMap;
    });
  }

  /**
   * create an espresso list csv import.
   * @param listCsvImport espresso list csv import to be saved in DB
   * @return id of the created list csv import
   */
  public Task<Long> createListCsvImport(@NonNull ListCsvImport listCsvImport) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(LIST_CSV_IMPORT_WRITER, listCsvImport);

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_CSV_IMPORT)
        .setContent(ContentType.AVRO_BINARY, LIST_CSV_IMPORT_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        assert !postResponse.getParts().isEmpty();
        String[] keys = postResponse.getPart(0).getContentLocation().getKey();
        assert keys.length == 1;
        return Long.valueOf(keys[0]);
      } else {
        String errMsg = String.format("Unexpected response code for call to to create list csv import %s: %s", listCsvImport, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete an espresso list csv import.
   * @param listCsvImportId list csv import id
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteListCsvImport(long listCsvImportId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_CSV_IMPORT)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listCsvImportId))
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to delete list csv import %d, %s", listCsvImportId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * get an espresso list csv import
   * @param listCsvImportId list csv import id
   * @return an espresso list csv import if it exists
   */
  public Task<ListCsvImport> getListCsvImport(long listCsvImportId) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_CSV_IMPORT)
        .setAcceptType(ContentType.AVRO_BINARY, LIST_CSV_IMPORT_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listCsvImportId))
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return EspressoUtil.deserializeSingleAvroRecord(LIST_CSV_IMPORT_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null, String.format("can not find list csv import :%d", listCsvImportId));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format("can not find list csv import:%d", listCsvImportId));
      } else {
        String errMsg = String.format("Unexpected response code for call to get list csv import:%d, %s", listCsvImportId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * update state of a list csv import.
   * only the "state" and "lastModifiedTime" field may be updated. all other fields are ignored.
   * @param listCsvImport list csv import Id
   * @param listCsvImport espresso list csv import that contains update fields
   * @return True if succeed. False if not found
   */
  public Task<Boolean> updateListCsvImport(long listCsvImportId, @NonNull ListCsvImport listCsvImport) {
    JSONObject obj = new JSONObject();

    if (listCsvImport.state != null) {
      obj.put(LIST_CSV_IMPORT_FIELD_STATE, listCsvImport.state.toString());
    }
    if (listCsvImport.lastModifiedTime != 0) {
      obj.put(LIST_CSV_IMPORT_FIELD_LAST_MODIFIED_TIME, listCsvImport.lastModifiedTime);
    }

    if (obj.isEmpty()) {
      // There is no change, no need to do DB update, return true because idempotent behavior.
      return Task.value(TRUE);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error(String.format("fail to convert JSONObject to String when updating list csv import:%d", listCsvImportId), e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_CSV_IMPORT)
        .setContent(ContentType.AVRO_JSON, LIST_CSV_IMPORT_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(String.valueOf(listCsvImportId))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        // when the list csv import does not exist, it will try to create and fail because of incomplete data
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to update list csv import for %d: %s", listCsvImportId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get CreatorToListCsvImportView records created by the provided seat
   * @param creator the seat of the creator
   * @param start paging start
   * @param count paging count
   * @param states the list of states to filter by. if the parameter is null or empty, all states will be returned.
   * @return list of CreatorToListCsvImportView with totalHits in Pair<totalHists, List<Pair<listCsvImportId, CreatorToListCsvImportView>>>
   */
  public Task<Pair<Integer, java.util.List<Pair<Long, CreatorToListCsvImportView>>>> getListCsvImportsByCreator(
      SeatUrn creator,
      int start,
      int count,
      ListCsvImportState[] states) {

    GetRequest.Builder request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_CREATOR_TO_LIST_CSV_IMPORT_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, CREATOR_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(creator.toString())
        .setStart(start)
        .setCount(count)
        .setSort(CREATOR_TO_LIST_CSV_IMPORT_DEFAULT_SORT)
        .setQueryReturnTotalHits(TRUE);

    if (ArrayUtils.isNotEmpty(states)) {
      LuceneQuery stateQuery = new LuceneQuery();
      Stream.of(states).forEach(state ->
          stateQuery.addSubQuery(
              LuceneQuery.Operator.OR,
              new LuceneQuery().setTermQuery(LIST_CSV_IMPORT_FIELD_STATE, state.toString())));
      request.setQuery(stateQuery);
    }

    return _parSeqEspressoClient.execute(request.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        Integer totalHits = getResponse.getTotalHits() != null ? getResponse.getTotalHits() : getResponse.getParts().size();

        java.util.List<Pair<Long, CreatorToListCsvImportView>> pairs = getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              String listCsvImportId = part.getContentLocation().getKey()[1];
              CreatorToListCsvImportView creatorToListCsvImportView = EspressoUtil.deserializeSingleAvroRecord(CREATOR_TO_LIST_CSV_IMPORT_READER, part);
              return new Pair<>(Long.valueOf(listCsvImportId), creatorToListCsvImportView);
            })
            .collect(Collectors.toList());
        return new Pair<>(totalHits, pairs);
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("can not find CreatorToListCsvImportView for creator:%s", creator));
      } else {
        String errMsg = String.format("Unexpected response code for call to get CreatorToListCsvImportView for creator:%s, %s", creator, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get ImportTaskToListCsvImportView records associated with the given csvImportTask
   * @param csvImportTask the ImportTaskToListCsvImportView associated with the given csvImportTask
   * @return a pair of the CsvImportTask ID and the ImportTaskToListCsvImportView
   */
  public Task<ImportTaskToListCsvImportView> getListCsvImportsByCsvImportTask(
      CsvImportTaskUrn csvImportTask) {

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_IMPORT_TASK_TO_LIST_CSV_IMPORT_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, IMPORT_TASK_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(csvImportTask.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        GetResponse.Part responsePart = getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 1)
            .findFirst()
            .orElseThrow(() -> new RuntimeException(String.format(
                "There should be exactly one ImportTaskToListCsvImportView for import task:%s", csvImportTask)));

        return EspressoUtil.deserializeSingleAvroRecord(IMPORT_TASK_TO_LIST_CSV_IMPORT_READER, responsePart);

      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("can not find ImportTaskToListCsvImportView for import task:%s", csvImportTask));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get ImportTaskToListCsvImportView for import task:%s, %s", csvImportTask, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get ImportTaskToListCsvImportView records associated with the given csvImportTask
   * @param salesListId the ID of the sales list created by the list csv import
   * @return a pair of the CsvImportTask ID and the ImportTaskToListCsvImportView
   */
  public Task<ListToListCsvImportView> getListCsvImportsBySalesList(long salesListId) {

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_LIST_TO_LIST_CSV_IMPORT_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, LIST_TO_LIST_CSV_IMPORT_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(Long.toString(salesListId))
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        GetResponse.Part responsePart = getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 1)
            .findFirst()
            .orElseThrow(() -> new RuntimeException(String.format("There should be exactly one ListToListCsvImportView for list:%d", salesListId)));

        return EspressoUtil.deserializeSingleAvroRecord(LIST_TO_LIST_CSV_IMPORT_READER, responsePart);

      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        throw new EntityNotFoundException(null, String.format("can not find ListToListCsvImportView for list:%d", salesListId));
      } else {
        String errMsg = String.format("Unexpected response code for call to get ListToListCsvImportView for list:%d, %s", salesListId, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create an espresso AccountToListMapping
   * @param seat creator's seatUrn
   * @param organization account's organizationUrn
   * @param listId id of the list
   * @param accountToListMapping
   * @return creation HttpStatus. 201 means successfully created. 412 means there is conflict, resource already exits.
   * Others will throw exception
   */
  public Task<HttpStatus> createAccountToListMapping(@NonNull SeatUrn seat, @NonNull OrganizationUrn organization,
      long listId, @NonNull AccountToListMapping accountToListMapping) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(ACCOUNT_TO_LIST_MAPPING_WRITER, accountToListMapping);
    //Set If-None-Match etag to PUT request, will perform the operation only if the resource does not exist
    PutRequest request = PutRequest.builder()
        .setIfNoneMatchEtag("*")
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_ACCOUNT_TO_LIST_MAPPING)
        .setContent(ContentType.AVRO_BINARY, ACCOUNT_TO_LIST_MAPPING_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seat.toString(), organization.toString(), String.valueOf(listId))
        .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      //in espresso. Conflict will return ResponseStatus.PRECONDITION_FAILED for the creation
      if (postResponse.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        return HttpStatus.S_412_PRECONDITION_FAILED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      }
      String errMsg = String.format(
          "Create failed for seat:%s; company:%s; listId: %d; %s", seat,
          organization, listId, postResponse);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Find list id's created for account maps by seat and organization
   *
   * @param creator seat holder who own this mapping
   * @param account account's organizationUrn
   *
   * @return a collection of (list id, AccountToListMapping) pairs
   */
  public Task<java.util.List<Pair<Long, AccountToListMapping>>> getAccountMapListIdsForGivenSeatAndAccount(
      @NonNull SeatUrn creator, @NonNull OrganizationUrn account, int start, int count) {
    String[] keys = new String[]{creator.toString(), account.toString()};
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_ACCOUNT_TO_LIST_MAPPING)
        .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_TO_LIST_MAPPING_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .setStart(start)
        .setCount(count)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)  // 3 keys: creator, account, listId
            .map(part -> {
              String listId = part.getContentLocation().getKey()[2];
              AccountToListMapping accountToListMapping =
                  EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_TO_LIST_MAPPING_READER, part);
              return new Pair<>(Long.valueOf(listId), accountToListMapping);
            })
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get AccountToListMapping for seat:%s; account: %s; %s",
                creator, account, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Delete an espresso accountToListMapping
   *
   * @param creator seat holder who own this mapping
   * @param account account's organizationUrn
   * @param listId id of the list
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteAccountToListMapping(@NonNull SeatUrn creator, @NonNull OrganizationUrn account,
      long listId) {
    String[] key = new String[]{creator.toString(), account.toString(), String.valueOf(listId)};
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_ACCOUNT_TO_LIST_MAPPING)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format("Delete failed for seat: %s; account: %s; listId: %d", creator, account, listId,
            deleteResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get a default list given the key, (seatUrn, listType).
   *
   * @param seatUrn seatUrn that the default list is associated with
   * @param listType list type of the default list
   * @return a default list
   */
  public Task<DefaultList> getDefaultList(@NonNull SeatUrn seatUrn, @NonNull ListType listType) {
    String[] key = new String[]{seatUrn.toString(), listType.toString()};
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_DEFAULT_LIST)
        .setAcceptType(ContentType.AVRO_BINARY, DEFAULT_LIST_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      ResponseStatus responseStatus = getResponse.getResponseStatus();
      if (responseStatus == ResponseStatus.NOT_FOUND || (responseStatus == ResponseStatus.OK
          && getResponse.getPartCount() == 0)) {
        throw new EntityNotFoundException(null,
            String.format("Default list not found for seat: %s and list type: %s", seatUrn, listType));
      } else if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return EspressoUtil.deserializeSingleAvroRecord(DEFAULT_LIST_READER, getResponse.getPart(0));
      } else {
        String errMsg = String.format(
            "Unexpected response code: %s for the call to get the default list for seat: %s and list type: %s",
            getResponse, seatUrn, listType);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Upsert a default list.
   *
   * @param defaultList the default list to be upserted.
   * @return the http status of the upsert operation.
   * 200 -> An existing default list is updated
   * 201 -> A new default list is created
   * exception -> DB failure
   */
  public Task<HttpStatus> upsertDefaultList(@NonNull SeatUrn seatUrn, @NonNull ListType listType,
      @NonNull DefaultList defaultList) {
    String[] key = new String[]{seatUrn.toString(), listType.toString()};
    byte[] bytes = EspressoUtil.serializeAvroRecord(DEFAULT_LIST_WRITER, defaultList);
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_DEFAULT_LIST)
        .setContent(ContentType.AVRO_BINARY, DEFAULT_LIST_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();
    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      }

      String errMsg = String.format(
          "Unexpected response code: %s for the call to upsert the default list for seat: %s and list type: %s",
          putResponse, seatUrn, listType);
      LOG.error(errMsg);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Delete a default list.
   *
   * @param seatUrn seatUrn that the default list is associated with
   * @param listType list type of the default list
   * @return true if delete succeeded and false if the default list did not exist before the delete operation.
   */
  public Task<Boolean> deleteDefaultList(@NonNull SeatUrn seatUrn, @NonNull ListType listType) {
    String[] key = new String[]{seatUrn.toString(), listType.toString()};
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_DEFAULT_LIST)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format(
            "Unexpected response code: %s for the call to delete the default list for seat: %s and list type: %s",
            deleteResponse, seatUrn, listType);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create change log for a relationship map list. No updates are supported.
   * @return ID of change log for successful creation. Raise exception otherwise. The ID is unique for a list ID.
   */
  public Task<Long> createRelationshipMapChangeLog(long listId, @NonNull RelationshipMapChangeLog changeLog) {
    String key = Long.toString(listId);
    byte[] bytes = EspressoUtil.serializeAvroRecord(RELATIONSHIP_MAP_CHANGE_LOG_WRITER, changeLog);
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_RELATIONSHIP_MAP_CHANGE_LOG)
        .setContent(ContentType.AVRO_BINARY, RELATIONSHIP_MAP_CHANGE_LOG_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .setExpires(Date.from(Instant.now().plus(RELATIONSHIP_MAP_CHANGE_LOG_TTL_DAYS, ChronoUnit.DAYS)))
        .setIfNoneMatchEtag("*")
        .build();

    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.CREATED) {
        //We are expected to get 1 part, and it should contain the location key.
        String[] changeLogKey = putResponse.getPart(0).getContentLocation().getKey();
        return Long.valueOf(changeLogKey[1]);
      } else if (putResponse.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        throw new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED);
      }

      String errMsg = String.format("Unexpected response code: %s for the call to upsert the change log for listId: %s",
          putResponse, listId);
      LOG.error(errMsg);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Delete all change logs for a relationship map list.
   */
  public Task<Boolean> deleteAllRelationshipMapChangeLogsForListId(long listId) {
    String key = Long.toString(listId);
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_RELATIONSHIP_MAP_CHANGE_LOG)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || deleteResponse.getResponseStatus() == ResponseStatus.ACCEPTED) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg =
            String.format("Unexpected response code: %s for the call to delete the change log for listId: %s",
                deleteResponse, listId);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets all change logs for a listId with eventTime time between startTime and endTime.
   * Returns empty list if no change logs are found for given query.
   * @return Pair containing total number of change logs found in DB meeting the criteria and List of changeLogs.
   * A changelog will be represented by a pair containing the ID and record.
   */
  public Task<Pair<Integer, java.util.List<Pair<Long, RelationshipMapChangeLog>>>> findRelationshipMapChangeLogsByListIdAndEventTime(
      long listId, long startTime, long endTime, int start, int count) {
    if (startTime > System.currentTimeMillis()) {
      return Task.value(Pair.make(0, Collections.emptyList()));
    }
    String key = Long.toString(listId);
    String timeRange = String.format("[%d TO %d]", startTime, endTime);
    String defaultSort = "eventTime DESC";
    String queryFilter = "eventTime:" + timeRange;
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_LIST)
        .setTable(TABLE_RELATIONSHIP_MAP_CHANGE_LOG)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, RELATIONSHIP_MAP_CHANGE_LOG_SCHEMA_VERSION)
        .setKey(key)
        .setQuery(queryFilter)
        .setSort(defaultSort)
        .setStart(start)
        .setCount(count)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          int total = getResponse.getTotalHits() != null ? getResponse.getTotalHits() : getResponse.getPartCount();
          java.util.List<Pair<Long, RelationshipMapChangeLog>> changeRecordPairs =
              getResponse.getParts().stream().map(part -> {
                //Id is second part of key.
                Long changeLogId = Long.valueOf(part.getContentLocation().getKey()[1]);
                RelationshipMapChangeLog record =
                    EspressoUtil.deserializeSingleAvroRecord(RELATIONSHIP_MAP_CHANGE_LOG_READER, part);
                return Pair.make(changeLogId, record);
              }).collect(Collectors.toList());
          return Pair.make(total, changeRecordPairs);
        }
        return Pair.make(0, Collections.emptyList());
      }
      if (getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return Pair.make(0, Collections.emptyList());
      }
      String errMsg = String.format("Unexpected response code: %s for the call to query the change log for listId: %s",
          getResponse.getResponseStatus(), listId);
      LOG.error(errMsg);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Converts a given String to Urn. Throws a RuntimeException error if it fails to generate an Urn.
   * @param urnString urn in string format
   * @return Urn
   */
  private static Urn createUrnFromString(String urnString) {
    try {
      return Urn.createFromString(urnString);
    } catch (URISyntaxException e) {
      throw new RuntimeException("Failed to generate the Urn for the urnString " + urnString, e);
    }
  }
}
