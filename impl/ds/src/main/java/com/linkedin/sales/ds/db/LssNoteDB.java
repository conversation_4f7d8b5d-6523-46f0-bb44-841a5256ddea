package com.linkedin.sales.ds.db;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesSharedSearchUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PostResponse;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.Note;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.NoteKey;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import edu.umd.cs.findbugs.annotations.Nullable;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.TRUE;
import static java.lang.Boolean.FALSE;

/**
 * Created by yanxu at 9/26/2019
 * This is the class to directly talk to espresso Database LssNote
 *
 * Important note: In api model, we use AnnotatableEntityUrn. In DB table key, it's general urn type, not
 * AnnotatableEntityUrn. We need to do transformation between general urn stored in DB and AnnotatableEntityUrn
 * used in api model.
 */
public class LssNoteDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssNoteDB.class);
  private static final int SCHEMA_VERSION = 3;

  static final String DB_LSS_NOTE = "LssNote";
  static final String TABLE_NOTE = "Note";
  private static final String NOTE_FIELD_LAST_MODIFIED_TIME = "lastModifiedTime";
  private static final String NOTE_FIELD_BODY = "body";
  private static final String DEFAULT_TIME_RANGE = String.format("[%d TO %d]", 0, Long.MAX_VALUE);
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure */
        ResponseStatus.GATEWAY_TIMEOUT /* Gateway timed out */));
    // As P95 get/write/delete/query are 20 ms at most. We think 250 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(250L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private static final SpecificDatumWriter<Note> NOTE_WRITER = new SpecificDatumWriter<>(Note.SCHEMA$);

  private static final SpecificDatumReader<Note> NOTE_READER = new SpecificDatumReader<>(Note.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssNoteDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create a note
   * @param seat the seat who created the note
   * @param entity the entity which the note associates
   * @param note the note object
   * @return the id of the note just created
   */
  public Task<Long> createNote(@NonNull SeatUrn seat, @NonNull AnnotatableEntityUrn entity, @NonNull Note note,
      @Nullable Long retentionDaysFromEntity) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(NOTE_WRITER, note);
    PostRequest.Builder builder = PostRequest.builder()
          .setDatabase(DB_LSS_NOTE)
          .setTable(TABLE_NOTE)
          .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, bytes)
          .setRetryConfig(RETRY_REQUEST_CONFIG)
          .setKey(seat.toString(), convertAnnotatableEntityUrnToGeneralUrn(entity).toString());

    if (retentionDaysFromEntity != null) {
          builder = builder.setExpires(Date.from(Instant.now().plus(retentionDaysFromEntity, ChronoUnit.DAYS)));
    }
    PostRequest request = builder.build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() != ResponseStatus.CREATED) {
        String errMsg =
            String.format("Unexpected response code for call to create note for %s to the seat %s: %s", note, seat,
                postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
      List<PostResponse.Part> parts = postResponse.getParts()
          .stream()
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3) //3 keys: seatUrn, entityUrn, noteId
          .collect(Collectors.toList());
      if (parts.isEmpty()) {
        String errMsg =
            String.format("Create failed for seat %s and entity %s, with the given note %s: %s", seat, entity, note,
                postResponse);
        throw new RuntimeException(errMsg);
      }
      String noteIdString = parts.get(0).getContentLocation().getKey()[2];
      try {
        return Long.valueOf(noteIdString);
      } catch (NumberFormatException e) {
        String errMsg = String.format("fail to get the note id for %s %s: %s", seat, entity, postResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Update the content of the given note
   * @return true if succeed
   */
  public Task<Boolean> updateNote(@NonNull SeatUrn seat, @NonNull AnnotatableEntityUrn entity, @NonNull Long noteId,
      @NonNull Note note) {
    JSONObject obj = new JSONObject();
    if (note.body == null || note.body.text == null || note.body.attributes == null) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Required note body fields not set when updating list"));
    }
    obj.put(NOTE_FIELD_BODY, note.body);
    long lastModifiedTime = System.currentTimeMillis();
    obj.put(NOTE_FIELD_LAST_MODIFIED_TIME, lastModifiedTime);
    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error("fail to convert JSON Object to String when updating note: {}", noteId, e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setContent(ContentType.AVRO_JSON, SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seat.toString(), convertAnnotatableEntityUrnToGeneralUrn(entity).toString(), String.valueOf(noteId))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        // when the note does not exist, it will try to create and fail because of incomplete data
        return FALSE;
      } else {
        String errMsg =
            String.format("Unexpected response code for call to to update note: seat:%s noteId:%d response:%s", seat,
                noteId, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Delete a note
   * @param seat the seat who created the note
   * @param entity the entity which the note associates
   * @param noteId id of the note
   * @return true if succeed
   */
  public Task<Boolean> deleteNote(SeatUrn seat, AnnotatableEntityUrn entity, Long noteId) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seat.toString(), convertAnnotatableEntityUrnToGeneralUrn(entity).toString(), noteId.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to delete note for seatId: %d, entity: %s and "
            + "noteId: %d with response: %s", seat.getIdAsLong(), entity.toString(), noteId, deleteResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find notes on a given entity created by a given seat
   */
  public Task<List<Pair<NoteKey, Note>>> findBySeatAndEntity(@NonNull SeatUrn seatUrn,
      @NonNull AnnotatableEntityUrn entityUrn, int start, int count) {
    String sortQuery = "lastModifiedTime DESC";
    String queryFilter = "lastModifiedTime:" + DEFAULT_TIME_RANGE;
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), convertAnnotatableEntityUrnToGeneralUrn(entityUrn).toString())
        .setQuery(queryFilter)
        .setSort(sortQuery)
        .setStart(start)
        .setCount(count)
        .build();
    return _parSeqEspressoClient.execute(request).map(this::parseEspressoResponse);
  }

  /**
   * Find notes created by a given seat
   */
  public Task<List<Pair<NoteKey, Note>>> findBySeat(@NonNull SeatUrn seatUrn, int start, int count) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString())
        .setStart(start)
        .setCount(count)
        .build();
    return _parSeqEspressoClient.execute(request).map(this::parseEspressoResponse);
  }

  /**
   * get an espresso note by key
   * @param key note key
   * @return an espresso note if it exists
   */
  public Task<Note> getNote(@NonNull NoteKey key) {
    GetRequest request = singleNoteGetRequest(key);
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        assert !getResponse.getParts().isEmpty();
        return EspressoUtil.deserializeSingleAvroRecord(NOTE_READER, getResponse.getPart(0));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, "cannot find note for " + key);
      } else {
        String errMsg = String.format("Unexpected response code for call to get note for %s, %s", key, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  @NonNull
  // Create single get request
  private GetRequest singleNoteGetRequest(@NonNull NoteKey key) {
    String seat = key.getSeat().toString();
    String entity = convertAnnotatableEntityUrnToGeneralUrn(key.getEntity()).toString();
    String noteId = key.getNoteId().toString();
    return GetRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setKey(seat, entity, noteId)
        .build();
  }

  /**
   * get espresso notes by keys
   * @param keys note keys
   * @return espresso notes under given keys
   */
  public Task<List<Pair<NoteKey, Note>>> batchGetNotes(@NonNull List<NoteKey> keys) {
    List<String []> noteKeys = keys.stream().map(this::convertNoteKeyToEspressoKeyString).collect(Collectors.toList());
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_NOTE)
        .setTable(TABLE_NOTE)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setMultigetKeyList(noteKeys)
        .build();
    return _parSeqEspressoClient.execute(request).map(this::parseEspressoResponse);
  }

  @VisibleForTesting
  protected static AnnotatableEntityUrn convertStringToAnnotatableEntityUrn(String entity) throws URISyntaxException {
    AnnotatableEntityUrn annotatableEntityUrn = new AnnotatableEntityUrn();
    Urn urn;
    try {
      urn = Urn.createFromString(entity);
    } catch (Exception e) {
      throw new IllegalArgumentException("Could not parse Urn from " + entity);
    }

    if (MemberUrn.ENTITY_TYPE.equals(urn.getEntityType())) {
      annotatableEntityUrn.setMemberUrn(MemberUrn.createFromUrn(urn));
      return annotatableEntityUrn;
    } else if (SalesSharedSearchUrn.ENTITY_TYPE.equals(urn.getEntityType())) {
      annotatableEntityUrn.setSalesSharedSearchUrn(SalesSharedSearchUrn.createFromUrn(urn));
      return annotatableEntityUrn;
    } else if (OrganizationUrn.ENTITY_TYPE.equals(urn.getEntityType())) {
      annotatableEntityUrn.setOrganizationUrn(OrganizationUrn.createFromUrn(urn));
      return annotatableEntityUrn;
    } else {
      throw new IllegalArgumentException("Unexpected urn type parsed from " + entity);
    }
  }

  @VisibleForTesting
  private String [] convertNoteKeyToEspressoKeyString(NoteKey noteKey) {
    String seat = noteKey.getSeat().toString();
    String entity = convertAnnotatableEntityUrnToGeneralUrn(noteKey.getEntity()).toString();
    String noteId = noteKey.getNoteId().toString();
    return new String [] {seat, entity, noteId};
  }

  @VisibleForTesting
  public static Urn convertAnnotatableEntityUrnToGeneralUrn(AnnotatableEntityUrn entityUrn) {
    if (entityUrn.isMemberUrn()) {
      return entityUrn.getMemberUrn();
    } else if (entityUrn.isOrganizationUrn()) {
      return entityUrn.getOrganizationUrn();
    } else if (entityUrn.isSalesSharedSearchUrn()) {
      return entityUrn.getSalesSharedSearchUrn();
    } else {
      throw new IllegalArgumentException("Unexpected urn type" + entityUrn);
    }
  }

  /**
   * parse espresso response to get notekeys and notes
   * @param getResponse response from espresso
   * @return a list contains mappings from NoteKey to Espresso Note
   */
  private List<Pair<NoteKey, Note>> parseEspressoResponse(GetResponse getResponse) {
    if (getResponse.getResponseStatus() == ResponseStatus.OK
        || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
      // the response will return a list of notes. deserialize all of them.
      return getResponse.getParts().stream()
          .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3)
          .map(this::convertEspressoPartToEspressoNote)
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
    } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
        || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
      return Collections.emptyList();
    } else {
      // Not expected. Parsing error indicates data corruption of some sort
      throw new RuntimeException(getResponse.getResponseStatus().getText());
    }
  }

  /**
   * convert the response part from espresso to NoteKey and espresso.Note
   * @param part Serialized note retrieved from espresso
   * @return A pair of noteKey and espresso.Note
   */
  @Nullable
  private Pair<NoteKey, Note> convertEspressoPartToEspressoNote(@NonNull GetResponse.Part part) {
    NoteKey noteKey;
    Note note;
    String[] key;
    try {
      note = EspressoUtil.deserializeSingleAvroRecord(NOTE_READER, part);
      key = part.getContentLocation().getKey();
    } catch (Exception e) {
      LOG.error("corrupted espresso note record", e);
      return null;
    }
    try {
      // Based on the table schema, the keys are seatID, entity and noteID
      SeatUrn seatUrn = SeatUrn.deserialize(key[0]);
      AnnotatableEntityUrn entityUrn = convertStringToAnnotatableEntityUrn(key[1]);
      long noteId = Long.parseLong(key[2]);
      noteKey = new NoteKey().setNoteId(noteId).setEntity(entityUrn).setSeat(seatUrn);
    } catch (Exception e) {
      LOG.error("failed to parse the note key for {}", note, e);
      return null;
    }
    return Pair.of(noteKey, note);
  }

}
