package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.query.LuceneQuery;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.Header;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PostResponse;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.PutResponse;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.db.exception.TooManyRequestsException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.RecentSearchesV2;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.io.IOException;
import java.io.StringWriter;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import edu.umd.cs.findbugs.annotations.Nullable;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.linkedin.sales.espresso.RecentViews;

import static java.lang.Boolean.*;


/**
 * This is the class to directly talk to espresso Database LssRecentViews
 * <AUTHOR>
 */
public class LssRecentViewsDB {

  private static final Logger LOG = LoggerFactory.getLogger(LssRecentViewsDB.class);
  private static final String RECENT_VIEW_ENTITIES_NAME = "entities";
  private static final String RECENT_VIEW_ENTITY_NAME = "entity";
  private static final String LAST_VIEWED_TIME_NAME = "lastViewedTime";
  private static final String CONTRACT_URN_NAME = "contractUrn";
  private static final int RECORD_TTL_IN_DAYS = 90;
  private static final int RECENT_SEARCHES_RECORD_TTL_IN_DAYS = 30;
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;

  static final String DB_RECENT_VIEW = "LssRecentViews";
  static final String TABLE_RECENT_VIEW = "RecentViews";
  static final String TABLE_RECENT_SEARCHES = "RecentSearchesV2";
  public static final int SCHEMA_VERSION = 3;
  public static final int RECENT_SEARCHES_SCHEMA_VERSION = 1;
  private static final String FLD_LAST_SEARCHED_TIME = "lastSearchedTime";
  private static final String DEFAULT_SORT = FLD_LAST_SEARCHED_TIME + " DESC";
  private static final String WILD_CARD = "*";

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // As P95 get/write/delete/query are 70 ms at most. We think 500 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private static final SpecificDatumReader<RecentViews> RECENT_VIEWS_READER =
      new SpecificDatumReader<>(RecentViews.SCHEMA$);

  private static final SpecificDatumWriter<RecentViews> RECENT_VIEWS_WRITER =
      new SpecificDatumWriter<>(RecentViews.SCHEMA$);

  private static final SpecificDatumReader<RecentSearchesV2> RECENT_SEARCHES_READER =
      new SpecificDatumReader<>(RecentSearchesV2.SCHEMA$);

  private static final SpecificDatumWriter<RecentSearchesV2> RECENT_SEARCHES_WRITER =
      new SpecificDatumWriter<>(RecentSearchesV2.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssRecentViewsDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create RecentView record in DB
   * @param seatUrn seat Urn of the user
   * @param entityType recent view entity type
   * @param recentViews the Espresso RecentViews data to be stored
   * @return True if record created successfully, False otherwise
   */
  public Task<Boolean> createRecentView(@NonNull String seatUrn, @NonNull String entityType,
      @NonNull RecentViews recentViews) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(RECENT_VIEWS_WRITER, recentViews);

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_VIEW)
        .setContent(ContentType.AVRO_BINARY, SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, entityType)
        .setExpires(Date.from(Instant.now().plus(RECORD_TTL_IN_DAYS, ChronoUnit.DAYS)))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      switch (postResponse.getResponseStatus()) {
        case CREATED: return TRUE;
        case OK: return FALSE;
        default:
          String errMsg = String.format(
            "Unexpected response code for call to create recent view" + " for seat:%s - entityType:%s, %s", seatUrn,
            entityType, postResponse);
          LOG.error(errMsg);
          throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Create RecentSearchesV2 record in DB
   * @param seatUrn seat Urn of the user
   * @param espressoRecentSearch the Espresso RecentSearchesV2 record data to be stored
   * @param searchType type of recent search.
   * @return id of the newly created recent searches record
   */
  public Task<Long> createRecentSearch(@NonNull SeatUrn seatUrn,
      @NonNull RecentSearchesV2 espressoRecentSearch, @NonNull String searchType) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(RECENT_SEARCHES_WRITER, espressoRecentSearch);

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_SEARCHES)
        .setContent(ContentType.AVRO_BINARY, RECENT_SEARCHES_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), searchType)
        .setExpires(Date.from(Instant.now().plus(RECENT_SEARCHES_RECORD_TTL_IN_DAYS, ChronoUnit.DAYS)))
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.TOO_MANY_REQUESTS) {
        throw new TooManyRequestsException("Too many requests creating recent searches");
      }
      if (postResponse.getResponseStatus() != ResponseStatus.CREATED) {
        throw new RuntimeException(
            String.format("Unexpected response for call to create recent search %s", postResponse));
      }
      List<PostResponse.Part> parts = postResponse.getParts()
        .stream()
        .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
            && part.getContentLocation().getKey().length == 3) //3 keys: seatUrn, searchType, recentSearchId
        .collect(Collectors.toList());
      if (parts.isEmpty()) {
        String errMsg = String.format("Create failed for seat %s searchType %s: %s", seatUrn, searchType, postResponse);
        throw new RuntimeException(errMsg);
      }
      return Long.valueOf(parts.get(0).getContentLocation().getKey()[2]);
    });
  }

  /**
   * Gets the RecentViews data for a seat and entity type
   * @param seatUrn the seat of the user
   * @param entityType the type of recent view entity
   * @return Espresso RecentViews data
   */
  public Task<RecentViews> getRecentViews(@NonNull String seatUrn, @NonNull String entityType) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, entityType)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          // only one row per seatUrn and entityType
          return EspressoUtil.deserializeSingleAvroRecord(RECENT_VIEWS_READER, getResponse.getPart(0));
        } else {
          throw new EntityNotFoundException(null,
              String.format("can not find recent view for seatUrn :%s and entityType :%s", seatUrn, entityType));
        }
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not find recent view for seatUrn :%s and entityType :%s", seatUrn, entityType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get recent view" + " for seatUrn :%s and entityType :%s, %s", seatUrn,
            entityType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Deletes the RecentViews data for a seat and entity type
   * @param seatUrn the seat of the user
   * @param entityType the type of recent view entity
   * @return true if deletion successful, false otherwise
   */
  public Task<Boolean> deleteRecentViews(@NonNull String seatUrn, @NonNull String entityType) {
    return deleteRecentActivity(seatUrn, entityType, TABLE_RECENT_VIEW);
  }

  /**
   * Deletes the RecentSearches data for a seat and entity type
   * @param seatUrn the seat of the user
   * @param entityType the type of recent search entity
   * @return true if deletion successful, false otherwise
   */
  public Task<Boolean> deleteRecentSearches(@NonNull String seatUrn, @NonNull String entityType) {
    return deleteRecentActivity(seatUrn, entityType, TABLE_RECENT_SEARCHES);
  }

  /**
   * Internal helper function for deleting recent activities.
   */
  private Task<Boolean> deleteRecentActivity(@NonNull String seatUrn, @NonNull String entityType,
      String tableRecentActivity) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(tableRecentActivity)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, entityType)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("can not find recent activity for seatUrn: %s and entityType: %s in table: %s",
                seatUrn, entityType, tableRecentActivity));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to delete recent activity for seatUrn: %s and entityType: %s in table: %s"
                + ", %s", seatUrn, entityType, tableRecentActivity, deleteResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets the RecentSearchesV2 data for a seat and search type
   * @param seatUrn the seat of the user
   * @param searchType the type of recent view entity
   * @param start paging start
   * @param count paging count
   * @return List of results containing the the id, search type and the Espresso RecentSearchesV2 record data
   */
  public Task<List<Pair<Pair<Long, String>, RecentSearchesV2>>> getRecentSearches(@NonNull SeatUrn seatUrn,
      @Nullable String searchType, int start, int count) {
    Calendar cal = Calendar.getInstance();
    long endTime = cal.getTimeInMillis();
    cal.add(Calendar.DATE, -RECENT_SEARCHES_RECORD_TTL_IN_DAYS);
    long beginTime = cal.getTimeInMillis();

    String queryFilter = new LuceneQuery()
        .setRangeQuery(FLD_LAST_SEARCHED_TIME, String.valueOf(beginTime), String.valueOf(endTime)).toString(); // we need this to sort the results
    ArrayList<String> keys = new ArrayList<>();
    keys.add(seatUrn.toString());
    if (searchType != null) {
      keys.add(searchType);
    }
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_SEARCHES)
        .setAcceptType(ContentType.AVRO_BINARY, RECENT_SEARCHES_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys.toArray(new String[keys.size()]))
        .setQuery(queryFilter)
        .setSort(DEFAULT_SORT)
        .setStart(start)
        .setCount(count)
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3) //3 keys: seatUrn, searchType, recentSearchId
            .map(part -> {
              String responseSearchType = part.getContentLocation().getKey()[1];
              String recentSearchId = part.getContentLocation().getKey()[2];
              RecentSearchesV2 recentSearch = EspressoUtil.deserializeSingleAvroRecord(RECENT_SEARCHES_READER, part);
              Pair<Long, String> key = new Pair<>(Long.valueOf(recentSearchId), responseSearchType);
              return new Pair<>(key, recentSearch);
            }).collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get recent search" + " for seatUrn :%s and searchType :%s, %s", seatUrn,
            searchType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Gets the RecentSearchesV2 single record data for the id, seat and search type
   * @param recentSearchId the unique id of the record data
   * @param seatUrn the seat of the user
   * @param searchType the type of recent view entity
   * @return Espresso RecentSearchesV2 record data
   */
  public Task<RecentSearchesV2> getRecentSearch(@NonNull Long recentSearchId, @NonNull SeatUrn seatUrn, @NonNull String searchType) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_SEARCHES)
        .setAcceptType(ContentType.AVRO_BINARY, RECENT_SEARCHES_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), searchType, String.valueOf(recentSearchId))
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return EspressoUtil.deserializeSingleAvroRecord(RECENT_SEARCHES_READER, getResponse.getPart(0));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null,
            String.format("Cannot find recent search for id: %d seatUrn :%s and searchType :%s", recentSearchId, seatUrn, searchType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get recent search" + " for seatUrn :%s and searchType :%s, %s", seatUrn,
            searchType, getResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Upsert a recent view
   * @param seatUrn seat Urn of the viewer
   * @param entityType type of viewed entity
   * @param recentViews recent view data to update with
   * @return True if successfully updated, False otherwise
   */
  public Task<Boolean> updateRecentViews(@NonNull String seatUrn, @NonNull String entityType,
      @NonNull RecentViews recentViews) {
    JSONObject obj = new JSONObject();
    JSONArray entities = new JSONArray();
    if (recentViews.entities == null || recentViews.entities.isEmpty()
        || recentViews.contractUrn == null) {
      // need either entities or contractUrn to update DB
      return Task.value(TRUE);
    }
    recentViews.entities.forEach(recentViewEntity -> {
      JSONObject entityObj = new JSONObject();
      entityObj.put(RECENT_VIEW_ENTITY_NAME, recentViewEntity.entity.toString());
      entityObj.put(LAST_VIEWED_TIME_NAME, recentViewEntity.lastViewedTime);
      entities.add(entityObj);
    });
    if (!entities.isEmpty()) {
      obj.put(RECENT_VIEW_ENTITIES_NAME, entities);
    }
    if (!recentViews.contractUrn.toString().isEmpty()) {
      obj.put(CONTRACT_URN_NAME, recentViews.contractUrn.toString());
    }

    if (obj.isEmpty()) {
      return Task.value(TRUE);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error("Failed to convert JSONObject to String when updating RecentViews"
              + " for seatUrn {} and entityType {} - {}", seatUrn, entityType, e.getMessage());
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_VIEW)
        .setContent(ContentType.AVRO_JSON, SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn, entityType)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        throw new EntityNotFoundException(null,
            String.format("can not update recent view for seatUrn :%s and entityType :%s", seatUrn, entityType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to to update RecentViews for seatUrn: %s, entityType: %s, %s",
            seatUrn, entityType, postResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Update RecentSearchesV2 record in DB
   * @param seatUrn seat Urn of the user
   * @param espressoRecentSearch the Espresso RecentSearchesV2 record data to be stored
   * @param searchType type of recent search.
   * @param recentSearchId the unique id of the record data
   * @return id of the updated recent searches record
   */
  public Task<Long> updateRecentSearch(@NonNull SeatUrn seatUrn,
      @NonNull RecentSearchesV2 espressoRecentSearch, @NonNull String searchType, @NonNull long recentSearchId) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(RECENT_SEARCHES_WRITER, espressoRecentSearch);

    PutRequest request = PutRequest.builder()
        .setDatabase(DB_RECENT_VIEW)
        .setTable(TABLE_RECENT_SEARCHES)
        .setContent(ContentType.AVRO_BINARY, RECENT_SEARCHES_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), searchType, String.valueOf(recentSearchId))
        .setExpires(Date.from(Instant.now().plus(RECENT_SEARCHES_RECORD_TTL_IN_DAYS, ChronoUnit.DAYS)))
        .setHeader(Header.IF_MATCH, WILD_CARD)
        .build();

    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      if (putResponse.getResponseStatus() == ResponseStatus.TOO_MANY_REQUESTS) {
        throw new TooManyRequestsException("Too many requests updating recent search with id " + recentSearchId);
      }
      if (putResponse.getResponseStatus() != ResponseStatus.OK) {
        throw new RuntimeException(
            String.format("Unexpected response for call to update recent search %s", putResponse));
      }
      List<PutResponse.Part> parts = putResponse.getParts()
          .stream()
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3) //3 keys: seatUrn, searchType, recentSearchId
          .collect(Collectors.toList());
      if (parts.isEmpty()) {
        String errMsg = String.format("Update failed for recentSearchId %d seat %s searchType %s: %s", recentSearchId, seatUrn, searchType, putResponse);
        throw new RuntimeException(errMsg);
      }
      return Long.valueOf(parts.get(0).getContentLocation().getKey()[2]);
    });
  }
}
