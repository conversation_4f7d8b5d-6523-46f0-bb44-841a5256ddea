package com.linkedin.sales.ds.db;

import com.google.common.base.Preconditions;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.TxMultiPutRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.model.LeadAccountAssociationKey;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.AccountToLeadAssociationView;
import com.linkedin.sales.espresso.LeadCountInAccount;
import com.linkedin.sales.espresso.LeadToAccountAssociation;
import com.linkedin.sales.espresso.SavedAccount;
import com.linkedin.sales.espresso.SavedAccountCount;
import com.linkedin.sales.espresso.SavedLead;
import com.linkedin.sales.espresso.SavedLeadCount;
import com.linkedin.salesleadaccount.SalesAccountFilter;
import com.linkedin.salesleadaccount.SalesAccountSortCriteria;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.ds.rest.ClientConstants.*;
import static java.lang.Boolean.*;


/**
 * This is the class to directly talk to espresso Database LssSavedLeadAccount
 */
public class LssSavedLeadAccountDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssSavedLeadAccountDB.class);

  static final String DB_LSS_SAVED_LEAD_ACCOUNT = "LssSavedLeadAccount";
  static final String TABLE_LEAD_TO_ACCOUNT_ASSOCIATION = "LeadToAccountAssociation";
  static final String TABLE_SAVED_ACCOUNT = "SavedAccount";
  static final String TABLE_SAVED_LEAD = "SavedLead";
  private static final String TABLE_ACCOUNT_TO_LEAD_ASSOCIATION_VIEW = "AccountToLeadAssociationView";
  private static final String TABLE_LEAD_COUNT_IN_ACCOUNT = "LeadCountInAccount";
  private static final String TABLE_SAVED_ACCOUNT_COUNT = "SavedAccountCount";
  private static final String TABLE_SAVED_LEAD_COUNT = "SavedLeadCount";
  private static final int LEAD_ACCOUNT_ASSOCIATION_SCHEMA_VERSION = 1;
  private static final int ACCOUNT_LEAD_ASSOCIATION_SCHEMA_VERSION = 1;
  private static final int LEAD_COUNT_IN_ACCOUNT_SCHEMA_VERSION = 1;
  private static final int SAVED_ACCOUNT_SCHEMA_VERSION = 5;
  private static final int SAVED_LEAD_SCHEMA_VERSION = 4;
  private static final int SAVED_ACCOUNT_COUNT_SCHEMA_VERSION = 1;
  private static final int SAVED_LEAD_COUNT_SCHEMA_VERSION = 1;
  private static final int MINIMUM_ESPRESSO_FETCH_COUNT = 1;

  private static final String SAVED_ACCOUNT_FIELD_STAR = "starred";
  private static final String SAVED_ACCOUNT_FIELD_STAR_TIME = "starLastModifiedTime";
  private static final String SAVED_ACCOUNT_FIELD_CREATE_TIME = "createdTime";

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  private static final String DEFAULT_TIME_RANGE = String.format("[%d TO %d]", 0, Long.MAX_VALUE);

  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // As P95 get/write/delete/query are 70 ms at most. We think 500 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
  }

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG_MULTI_PUT;

  static {
    RETRY_REQUEST_CONFIG_MULTI_PUT = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT));
    // TODO: Set the retry timeout to 1000ms for now and tune the value based on multi-put latency afterward
    RETRY_REQUEST_CONFIG_MULTI_PUT.setRetryTimeoutMillis(1000L);
  }

  private static final SpecificDatumReader<LeadToAccountAssociation> LEAD_TO_ACCOUNT_ASSOCIATION_READER =
      new SpecificDatumReader<>(LeadToAccountAssociation.SCHEMA$);

  private static final SpecificDatumReader<AccountToLeadAssociationView> ACCOUNT_TO_LEAD_ASSOCIATION_VIEW_READER =
      new SpecificDatumReader<>(AccountToLeadAssociationView.SCHEMA$);

  private static final SpecificDatumReader<LeadCountInAccount> LEAD_COUNT_IN_ACCOUNT_READER =
      new SpecificDatumReader<>(LeadCountInAccount.SCHEMA$);

  private static final SpecificDatumReader<SavedAccount> SAVED_ACCOUNT_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(SavedAccount.SCHEMA$);

  private static final SpecificDatumReader<SavedLead> SAVED_LEAD_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(SavedLead.SCHEMA$);

  private static final SpecificDatumReader<SavedAccountCount> SAVED_ACCOUNT_COUNT_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(SavedAccountCount.SCHEMA$);

  private static final SpecificDatumReader<SavedLeadCount> SAVED_LEAD_COUNT_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(SavedLeadCount.SCHEMA$);

  private static final SpecificDatumWriter<LeadToAccountAssociation> LEAD_TO_ACCOUNT_ASSOCIATION_WRITER =
      new SpecificDatumWriter<>(LeadToAccountAssociation.SCHEMA$);

  private static final SpecificDatumWriter<SavedAccount> SAVED_ACCOUNT_SPECIFIC_DATUM_WRITER =
      new SpecificDatumWriter<>(SavedAccount.SCHEMA$);

  private static final SpecificDatumWriter<SavedLead> SAVED_LEAD_SPECIFIC_DATUM_WRITER =
      new SpecificDatumWriter<>(SavedLead.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssSavedLeadAccountDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * create an espresso leadToAccountAssociation
   * @param organization account's organizationUrn
   * @param member lead's memberUrn
   * @param seat creator's seatUrn
   * @param leadToAccountAssociation
   * @return creation HttpStatus. 201 means successfully created. 412 means there is conflict, resource already exits.
   * Others will throw exception
   */
  public Task<HttpStatus> createLeadAccountAssociation(@NonNull OrganizationUrn organization, @NonNull MemberUrn member, @NonNull SeatUrn seat,
      @NonNull LeadToAccountAssociation leadToAccountAssociation) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(LEAD_TO_ACCOUNT_ASSOCIATION_WRITER, leadToAccountAssociation);
    //Set If-None-Match etag to PUT request, will perform the operation only if the resource does not exist
    PutRequest request = PutRequest.builder()
        .setIfNoneMatchEtag("*")
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_LEAD_TO_ACCOUNT_ASSOCIATION)
        .setContent(ContentType.AVRO_BINARY, LEAD_ACCOUNT_ASSOCIATION_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seat.toString(), member.toString(), organization.toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      //in espresso. Conflict will return ResponseStatus.PRECONDITION_FAILED for the creation
      if (postResponse.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        return HttpStatus.S_412_PRECONDITION_FAILED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      }
      String errMsg = String.format(
          "Create failed for seat %s and member %s and company %s, with the given leadAccountAssociation %s: %s", seat,
          member, organization, leadToAccountAssociation, postResponse);
      throw new RuntimeException(errMsg);
    });
  }

  /**
   * Create multiple Espresso leadToAccountAssociations with multi-put
   * It is recommended not to batch write with size larger than 100
   * @param leadAccountAssociationMap pairs of compoundKey and espresso LeadToAccountAssociation to be saved in DB
   * @return map of memberUrn and organizationUrn to Http status.
   * Return 201 if record is created successfully or exists already, otherwise return 500
   */
  public Task<Map<Pair<MemberUrn, OrganizationUrn>, HttpStatus>> createLeadAccountAssociations(
      @NonNull Map<Pair<MemberUrn, OrganizationUrn>, LeadToAccountAssociation> leadAccountAssociationMap, @NonNull SeatUrn creator) {
    Preconditions.checkArgument(leadAccountAssociationMap.size() <= ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE,
        String.format("Espresso multi-put batch size %d is larger than recommended size %d",
            leadAccountAssociationMap.size(), ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE));

    Map<Pair<MemberUrn, OrganizationUrn>, HttpStatus> resultMap = new HashMap<>();
    TxMultiPutRequest.Builder multiPutRequestBuilder = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setRetryConfig(RETRY_REQUEST_CONFIG_MULTI_PUT)
        .setKey(creator.toString());
    leadAccountAssociationMap.entrySet().forEach(entry -> {
      byte[] bytes = EspressoUtil.serializeAvroRecord(LEAD_TO_ACCOUNT_ASSOCIATION_WRITER, entry.getValue());
      MemberUrn memberUrn = entry.getKey().getFirst();
      OrganizationUrn organizationUrn = entry.getKey().getSecond();
      TxMultiPutRequest.Part.Builder partBuilder = TxMultiPutRequest.Part.builder()
          .setTable(TABLE_LEAD_TO_ACCOUNT_ASSOCIATION)
          .setSubkey(memberUrn.toString(), organizationUrn.toString())
          .setContent(ContentType.AVRO_BINARY, LEAD_ACCOUNT_ASSOCIATION_SCHEMA_VERSION, bytes);
      multiPutRequestBuilder.addPart(partBuilder.build());
    });
    return _parSeqEspressoClient.execute(multiPutRequestBuilder.build()).map(multiPutResponse -> {
      // Successful multi-put operation returns 200, each individual sub response may have 200 OK or 201 Created.
      // If there is only one sub-request, multi-put response would be same as sub-request response
      if (multiPutResponse.getResponseStatus() == ResponseStatus.OK || multiPutResponse.getResponseStatus() == ResponseStatus.CREATED) {
        leadAccountAssociationMap.entrySet().forEach(entry -> resultMap.put(entry.getKey(), HttpStatus.S_201_CREATED));
      } else {
        LOG.error("Unexpected response code for call to batch put leadAccountAssociations to Espresso for seat {}: {}",
            creator, multiPutResponse.getResponseStatus());
        leadAccountAssociationMap.entrySet().forEach(
            pair -> resultMap.put(pair.getKey(), HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      }
      return resultMap;
    });
  }

  /**
   * Delete an espresso leadToAccountAssociation
   * @param organization organizationUrn of the account that the lead is associated with
   * @param member memberUrn of the lead
   * @param seat seat holder who own this association
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteLeadAccountAssociation(@Nullable OrganizationUrn organization, @NonNull MemberUrn member,
      @NonNull SeatUrn seat) {
    String[] key = organization == null ? new String[]{seat.toString(), member.toString()}
        : new String[]{seat.toString(), member.toString(), organization.toString()};
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_LEAD_TO_ACCOUNT_ASSOCIATION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return TRUE;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return FALSE;
      } else {
        String errMsg = String.format(
            "Delete failed for seat %s and member %s and company %s: %s",
            seat, member, organization, deleteResponse);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find LeadToAccountAssociations by organization
   * @param organization account's organizationUrn
   * @param seat seat holder who own this association
   * @return a collection of matched lead's memberUrn to AccountToLeadAssociationView pair. One account could be associated with mutiple leads
   */
  public Task<List<Pair<MemberUrn, AccountToLeadAssociationView>>> getLeadsAssociatedWithGivenAccount(
      @NonNull OrganizationUrn organization, @NonNull SeatUrn seat, @NonNull int start, @NonNull int count) {
    String[] keys = new String[]{seat.toString(), organization.toString()};
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_ACCOUNT_TO_LEAD_ASSOCIATION_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, ACCOUNT_LEAD_ASSOCIATION_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .setStart(start)
        .setCount(count)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)
            .map(part -> {
              String memberUrnString = part.getContentLocation().getKey()[2];
              try {
                AccountToLeadAssociationView accountToLeadAssociationView =
                    EspressoUtil.deserializeSingleAvroRecord(ACCOUNT_TO_LEAD_ASSOCIATION_VIEW_READER, part);
                return new Pair<>(MemberUrn.deserialize(memberUrnString),
                    accountToLeadAssociationView);
              } catch (URISyntaxException e) {
                LOG.error("fail to generate the MemberUrn for the rawString {}", memberUrnString, e);
                return null;
              }
            })
            .filter(Objects::nonNull).collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get AccountToLeadAssociationView for seat:%s, %s", seat,
                getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Find AccountToLeadAssociationView by memberUrn
   * @param memberUrn lead's memberUrn
   * @param seat seat holder who own this association
   * @return a collection of matched account's organizationUrn to LeadToAccountAssociation pair. One lead could be associated with only one account.
   */
  public Task<List<Pair<OrganizationUrn, LeadToAccountAssociation>>> getAccountsAssociatedWithGivenLead(
      @NonNull MemberUrn memberUrn, @NonNull SeatUrn seat, int start, int count) {
    if (count <= 0 || count > ESPRESSO_RECOMMENDED_PAGE_LIMIT) {
      throw new IllegalArgumentException("Count should be between 1 and " + ESPRESSO_RECOMMENDED_PAGE_LIMIT);
    }
    String[] keys = new String[]{seat.toString(), memberUrn.toString()};
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_LEAD_TO_ACCOUNT_ASSOCIATION)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_ACCOUNT_ASSOCIATION_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setStart(start)
        .setCount(count)
        .setKey(keys)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)
            .map(part -> {
              String organizationString = part.getContentLocation().getKey()[2];
              try {
                LeadToAccountAssociation leadToAccountAssociation =
                    EspressoUtil.deserializeSingleAvroRecord(LEAD_TO_ACCOUNT_ASSOCIATION_READER, part);
                return new Pair<>(OrganizationUrn.deserialize(organizationString),
                    leadToAccountAssociation);
              } catch (URISyntaxException e) {
                LOG.error("fail to generate the OrganizationUrn for the rawString {}", organizationString, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get LeadToAccountAssociation for seat: %s, %s", seat,
                getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get lead account associations owned by given seat with pagination.
   */
  public Task<List<Pair<LeadAccountAssociationKey, LeadToAccountAssociation>>> getLeadAccountAssociations(
      @NonNull SeatUrn seat,
      int start,
      int count) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_LEAD_TO_ACCOUNT_ASSOCIATION)
        .setAcceptType(ContentType.AVRO_BINARY, LEAD_ACCOUNT_ASSOCIATION_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setStart(start)
        .setCount(count)
        .setKey(seat.toString())
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 3)
            .map(part -> {
              String seatString = part.getContentLocation().getKey()[0];
              String memberString = part.getContentLocation().getKey()[1];
              String organizationString = part.getContentLocation().getKey()[2];
              LeadToAccountAssociation leadToAccountAssociation =
                  EspressoUtil.deserializeSingleAvroRecord(LEAD_TO_ACCOUNT_ASSOCIATION_READER, part);
              LeadAccountAssociationKey key = null;
              try {
                key = new LeadAccountAssociationKey(
                    SeatUrn.deserialize(seatString),
                    MemberUrn.deserialize(memberString),
                    OrganizationUrn.deserialize(organizationString)
                );
              } catch (URISyntaxException e) {
                LOG.error("Fail to generate the SeatUrn or MemberUrn or OrganizationUrn for seat {}, member {}, and organization {}",
                    seatString, memberString, organizationString, e);
                return null;
              }
              return new Pair<>(key, leadToAccountAssociation);
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
          || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return Collections.emptyList();
      } else {
        String errMsg =
            String.format("Unexpected response code for call to get LeadToAccountAssociation for seat: %s, %s", seat,
                getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get the total number of leads associated with each account for a given seat
   * @param urns set of Organization Urns
   * @param requester the seat Urn of the user in Sales Navigator
   * @return the map between Organization urn and associated lead count
   */
  public Task<Map<OrganizationUrn, Integer>> getAssociatedLeadCounts(@NonNull Set<OrganizationUrn> urns,
      SeatUrn requester) {

    List<Task<AbstractMap.SimpleEntry<OrganizationUrn, Integer>>> getCountTasks =
        urns.stream().filter(Objects::nonNull).map(urn -> {
          GetRequest request =
              GetRequest.builder()
                  .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
                  .setTable(TABLE_LEAD_COUNT_IN_ACCOUNT)
                  .setAcceptType(ContentType.AVRO_BINARY, LEAD_COUNT_IN_ACCOUNT_SCHEMA_VERSION)
                  .setRetryConfig(RETRY_REQUEST_CONFIG)
                  .setKey(requester.toString(), urn.toString())
                  .build();

          return _parSeqEspressoClient.execute(request).map(getResponse -> {
            AbstractMap.SimpleEntry<OrganizationUrn, Integer> resultEntry = new AbstractMap.SimpleEntry<>(urn, 0);

            if (getResponse.getResponseStatus() == ResponseStatus.OK) {
              if (getResponse.getPartCount() > 0) {
                // the return value would be 0 even there is no resource in LeadToAccountAssociation table
                Integer totalCount = (int) EspressoUtil.deserializeSingleAvroRecord(LEAD_COUNT_IN_ACCOUNT_READER,
                    getResponse.getPart(0)).totalCount;
                resultEntry.setValue(totalCount);
              }
            } else {
              LOG.error("Unexpected response code for call to get associated lead count for seat: {}, account: {}, {}",
                  requester.getIdAsLong(), urn.getIdAsLong(), getResponse.getResponseStatus());
            }
            return resultEntry;
          });
        }).collect(Collectors.toList());

    return Task.par(getCountTasks)
        .map(entryList -> entryList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /**
   * Create an espresso savedAccount
   * @param owner owner's seatUrn
   * @param organization account's organizationUrn
   * @param savedAccount espresso savedAccount to be saved in DB
   * @return 201 if create successfully, 200 if there is conflict, otherwise throw exception
   */
  public Task<HttpStatus> createSavedAccount(@NonNull SeatUrn owner, @NonNull OrganizationUrn organization, @NonNull SavedAccount savedAccount) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(SAVED_ACCOUNT_SPECIFIC_DATUM_WRITER, savedAccount);
    String[] keys = new String[]{owner.toString(), organization.toString()};

    //Set If-None-Match etag to PUT request, will perform the operation only if the resource does not exist
    PutRequest request = PutRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_ACCOUNT)
        .setIfNoneMatchEtag("*")
        .setContent(ContentType.AVRO_BINARY, SAVED_ACCOUNT_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .build();

    return _parSeqEspressoClient.execute(request).map(putResponse -> {
      switch (putResponse.getResponseStatus()) {
        case CREATED:
          return HttpStatus.S_201_CREATED;
        // Since If-None-Match etag has been set for the PUT request, response would be 412_PRECONDITION_FAILED if there is any existing resource
        case PRECONDITION_FAILED:
          return HttpStatus.S_200_OK;
        default: {
          String errMsg = String.format("Unexpected response code for call to create savedAccount %s for seat %d, account %d: %s",
              savedAccount, owner.getIdAsLong(), organization.getIdAsLong(), putResponse);
          LOG.error(errMsg);
          throw new RuntimeException(errMsg);
        }
      }
    });
  }

  /**
   * Create multiple Espresso savedAccounts with multi-put
   * It is recommended not to batch write with size larger than 100
   * @param owner owner's seatUrn
   * @param organizationToSavedAccountMap map of OrganizationUrn and espresso savedAccounts to be saved in DB
   * @return map of OrganizationUrn to Http Status
   * return 201 if create successfully, 200 if there is conflict, otherwise return 500
   */
  public Task<Map<OrganizationUrn, HttpStatus>> createSavedAccounts(@NonNull SeatUrn owner,
      @NonNull Map<OrganizationUrn, SavedAccount> organizationToSavedAccountMap) {
    Preconditions.checkArgument(organizationToSavedAccountMap.size() <= ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE,
        String.format("Espresso multi-put batch size %d is larger than recommended size %d",
            organizationToSavedAccountMap.size(), ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE));

    Map<OrganizationUrn, HttpStatus> resultMap = new HashMap<>();
    TxMultiPutRequest.Builder multiPutRequestBuilder = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setRetryConfig(RETRY_REQUEST_CONFIG_MULTI_PUT)
        .setKey(owner.toString());

    organizationToSavedAccountMap.forEach((key, value) -> {
      byte[] bytes = EspressoUtil.serializeAvroRecord(SAVED_ACCOUNT_SPECIFIC_DATUM_WRITER, value);
      String subKey = key.toString();
      TxMultiPutRequest.Part.Builder partBuilder = TxMultiPutRequest.Part.builder()
          .setTable(TABLE_SAVED_ACCOUNT)
          .setSubkey(subKey)
          .setContent(ContentType.AVRO_BINARY, SAVED_ACCOUNT_SCHEMA_VERSION, bytes);
      multiPutRequestBuilder.addPart(partBuilder.build());
    });

    return _parSeqEspressoClient.execute(multiPutRequestBuilder.build()).map(multiPutResponse -> {
      // Successful multi-put operation returns 200, each individual sub response may have 200 OK or 201 Created
      // If there is only one sub-request, multi-put response would be same as sub-request response
      if (multiPutResponse.getResponseStatus() == ResponseStatus.OK
          || multiPutResponse.getResponseStatus() == ResponseStatus.CREATED) {
        Map<OrganizationUrn, HttpStatus> responseMap = multiPutResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              String seatString = part.getContentLocation().getKey()[0];
              String organizationString = part.getContentLocation().getKey()[1];
              int status = part.getResponseStatus().getCode();
              try {
                SeatUrn seatUrn = SeatUrn.deserialize(seatString);
                OrganizationUrn organizationUrn = OrganizationUrn.deserialize(organizationString);
                HttpStatus httpStatus = HttpStatus.S_201_CREATED;
                if (status == HttpStatus.S_200_OK.getCode()) {
                  LOG.warn("The organization {} has already been saved for seat {}", organizationUrn, seatUrn);
                  httpStatus = HttpStatus.S_200_OK;
                }
                return new AbstractMap.SimpleEntry<>(organizationUrn, httpStatus);
              } catch (URISyntaxException e) {
                LOG.error("Fail to generate the SeatUrn or OrganizationUrn for seat {}, organization {}", seatString,
                    organizationString, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        organizationToSavedAccountMap.keySet().forEach(key -> {
          HttpStatus status = responseMap.get(key);
          if (status != null) {
            resultMap.put(key, status);
          } else {
            LOG.error("Cannot get response when saving organization {} for seat {}", key, owner);
            resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR);
          }
        });
      } else {
        LOG.error("Unexpected response code for call to batch put savedAccounts to Espresso for seat {}: {}", owner,
            multiPutResponse.getResponseStatus());
        organizationToSavedAccountMap.keySet().forEach(
            key -> resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      }
      return resultMap;
    });
  }

  /**
   * Delete an espresso savedAccount
   * @param owner owner's seatUrn
   * @param organization account's organizationUrn
   * @return 204 if delete successfully, 404 if the resource to be deleted is not found, otherwise throw exception
   */
  public Task<HttpStatus> deleteSavedAccount(@NonNull SeatUrn owner, @NonNull OrganizationUrn organization) {
    String[] keys = new String[]{owner.toString(), organization.toString()};

    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_ACCOUNT)
        .setKey(keys)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      switch (deleteResponse.getResponseStatus()) {
        case NO_CONTENT:
          return HttpStatus.S_204_NO_CONTENT;
        case NOT_FOUND:
          return HttpStatus.S_404_NOT_FOUND;
        default: {
          String errMsg = String.format("Unexpected response code for call to delete savedAccount for seat %d, account %d: %s",
              owner.getIdAsLong(), organization.getIdAsLong(), deleteResponse);
          LOG.error(errMsg);
          throw new RuntimeException(errMsg);
        }
      }
    });
  }

  /**
   * update starred, starLastModifiedTime of a savedAccount.
   * @param owner owner's seatUrn
   * @param organization account's organizationUrn
   * @param savedAccount savedAccount which contains the field that needs to be updated
   * @return True if succeed. False if not found
   */
  public Task<Boolean> updateSavedAccount(@NonNull SeatUrn owner, @NonNull OrganizationUrn organization, @NonNull SavedAccount savedAccount) {
    String[] keys = new String[]{owner.toString(), organization.toString()};
    JSONObject obj = new JSONObject();
    if (savedAccount.starred != null) {
      obj.put(SAVED_ACCOUNT_FIELD_STAR, savedAccount.starred);
      long starLastModifiedTime = savedAccount.starLastModifiedTime == null
          ? System.currentTimeMillis()
          : savedAccount.starLastModifiedTime;
      obj.put(SAVED_ACCOUNT_FIELD_STAR_TIME, starLastModifiedTime);
    }
    if (obj.isEmpty()) {
      // no change, return true
      return Task.value(TRUE);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error("fail to convert JSONObject to String when updating savedAccount for ({}, {})", owner, organization, e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_ACCOUNT)
        .setContent(ContentType.AVRO_JSON, SAVED_ACCOUNT_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys)
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return TRUE;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        // when the savedAccount does not exist, it will try to create and fail because of incomplete data
        return FALSE;
      } else {
        String errMsg = String.format("Unexpected response code for call to to update savedAccount for (%s, %s): %s", owner, organization, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get Saved Accounts with given owner seatUrn and/or account's organizationUrn
   * @param owner owner's seatUrn
   * @param organization account's organizationUrn, return all savedAccounts belong to the owner if null
   * @param start paging start
   * @param count paging count
   * @param salesAccountFilter filtering criteria to apply when fetching savedAccounts
   * @param sortCriteria the order with which to sort savedAccounts
   * @param sortOrder sort order
   * @return list of organizationUrn to SavedAccount pairs
   */
  public Task<List<Pair<OrganizationUrn, SavedAccount>>> getSavedAccounts(
      @NonNull SeatUrn owner,
      @Nullable OrganizationUrn organization,
      int start,
      int count,
      @Nullable SalesAccountFilter salesAccountFilter,
      @Nullable SalesAccountSortCriteria sortCriteria,
      @Nullable SortOrder sortOrder
  ) {
    String[] keys =
        organization == null ? new String[]{owner.toString()} : new String[]{owner.toString(), organization.toString()};
    GetRequest.Builder requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_ACCOUNT)
        .setAcceptType(ContentType.AVRO_BINARY, SAVED_ACCOUNT_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys);

    if (organization == null) {
      requestBuilder.setStart(start)
          // Setting count to -1 or 0 should be avoided according to go/espressoapi
          .setCount(count > 0 ? count : MINIMUM_ESPRESSO_FETCH_COUNT);
    }

    if (salesAccountFilter != null) {
      Boolean starred;
      switch (salesAccountFilter) {
        case STAR_ONLY:
          starred = TRUE;
          break;
        case UN_STAR_ONLY:
          starred = FALSE;
          break;
        default:
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Unknown filter to get savedAccounts");
      }
      requestBuilder.setQuery(SAVED_ACCOUNT_FIELD_STAR + ":" + starred);
    }

    if (sortCriteria != null) {
      String sort;
      String order = (sortOrder == SortOrder.ASCENDING) ? "ASC" : "DESC";
      switch (sortCriteria) {
        case STAR_LAST_MODIFIED:
          sort = SAVED_ACCOUNT_FIELD_STAR_TIME + " " + order;
          break;
        case CREATED:
          sort = SAVED_ACCOUNT_FIELD_CREATE_TIME + " " + order;
          break;
        default:
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Unknown sort criteria to get savedAccounts");
      }
      requestBuilder.setSort(sort);
      if (requestBuilder.getQuery() == null) {
        // Set default query if missing because sort only works when query string exists.
        String filterQuery = "createdTime:" + DEFAULT_TIME_RANGE;
        requestBuilder.setQuery(filterQuery);
      }
    }

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2 && part.getContent() != null)
            .map(part -> {
              String organizationString = part.getContentLocation().getKey()[1];
              try {
                SavedAccount savedAccount =
                    EspressoUtil.deserializeSingleAvroRecord(SAVED_ACCOUNT_SPECIFIC_DATUM_READER, part);
                return new Pair<>(OrganizationUrn.deserialize(organizationString), savedAccount);
              } catch (URISyntaxException e) {
                LOG.warn("Fail to generate OrganizationUrn from rawString {}", organizationString, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        // Don't throw an error because this is expected happen often especially for newly onboarded users
        return Collections.emptyList();
      } else {
        String errMsg = organization == null ? String.format(
            "Unexpected response code for call to get savedAccount for seat %d: %s", owner.getIdAsLong(), getResponse)
            : String.format("Unexpected response code for call to get savedAccount for seat %d, account %d, %s",
                owner.getIdAsLong(), organization.getIdAsLong(), getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get total saved account count for a given seat
   * @param seatUrn seat urn of the owner
   * @return count of saved account
   */
  public Task<Integer> getSavedAccountCountForSeat(@NonNull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_ACCOUNT_COUNT)
        .setAcceptType(ContentType.AVRO_BINARY, SAVED_ACCOUNT_COUNT_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return (int) EspressoUtil.deserializeSingleAvroRecord(SAVED_ACCOUNT_COUNT_SPECIFIC_DATUM_READER,
              getResponse.getPart(0)).totalCount;
        } else {
          LOG.warn("Invalid response received when getting account count for seat: {} with response {}", seatUrn,
              getResponse);
        }
      } else if (getResponse.getResponseStatus() != ResponseStatus.NOT_FOUND) {
        LOG.warn("Unexpected response code received when getting account count for seat: {} with response {}", seatUrn,
            getResponse);
      }
      return 0;
    });
  }

  /**
   * Get saved leads with given owner seatUrn and/or lead's memberUrn
   * @param owner owner's seatUrn
   * @param member lead's memberUrn, return all saved leads of the owner if null
   * @param start paging start
   * @param count paging count
   * @return list of organizationUrn to SavedLead pairs
   */
  public Task<List<Pair<MemberUrn, SavedLead>>> getSavedLeads(@NonNull SeatUrn owner,
      @Nullable MemberUrn member, int start, int count) {
    String[] keys =
        member == null ? new String[]{owner.toString()} : new String[]{owner.toString(), member.toString()};
    GetRequest.Builder requestBuilder = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_LEAD)
        .setAcceptType(ContentType.AVRO_BINARY, SAVED_LEAD_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(keys);

    if (member == null) {
      requestBuilder.setStart(start)
          // Setting count to -1 or 0 should be avoided according to go/espressoapi
          .setCount(count > 0 ? count : MINIMUM_ESPRESSO_FETCH_COUNT);
    }

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        return getResponse.getParts().stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2 && part.getContent() != null)
            .map(part -> {
              String memberUrnStr = part.getContentLocation().getKey()[1];
              try {
                SavedLead savedLead =
                    EspressoUtil.deserializeSingleAvroRecord(SAVED_LEAD_SPECIFIC_DATUM_READER, part);
                return new Pair<>(MemberUrn.deserialize(memberUrnStr), savedLead);
              } catch (URISyntaxException e) {
                LOG.warn("Fail to generate memberUrn from rawString {}", memberUrnStr, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
      } else if (getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        // Don't throw an error because this is expected happen often especially for newly onboarded users
        return Collections.emptyList();
      } else {
        String errMsg =
            String.format("Unexpected response code from Espresso when getting savedLead for seat %s and lead %s, %s",
                owner, member, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get total saved lead count for a given seat
   * @param seatUrn seat urn of the owner
   * @return count of saved lead
   */
  public Task<Integer> getSavedLeadCountForSeat(@NonNull SeatUrn seatUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_LEAD_COUNT)
        .setAcceptType(ContentType.AVRO_BINARY, SAVED_LEAD_COUNT_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() > 0) {
          return (int) EspressoUtil.deserializeSingleAvroRecord(SAVED_LEAD_COUNT_SPECIFIC_DATUM_READER,
              getResponse.getPart(0)).totalCount;
        } else {
          LOG.warn("Invalid response received when getting lead count for seat: {} with response {}", seatUrn,
              getResponse);
        }
      } else if (getResponse.getResponseStatus() != ResponseStatus.NOT_FOUND) {
        LOG.warn("Unexpected response code received when getting lead count for seat: {} with response {}", seatUrn,
            getResponse);
      }
      return 0;
    });
  }

  /**
   * Create a saved lead in SavedLead Espresso table
   * @param ownerSeatUrn seat urn of the lead owner
   * @param leadMemberUrn member urn of the lead
   * @param savedLead savedLead espresso document
   * @return HttpStatus, 201 for success, 200 for key conflict and 500 for any other exception
   */
  public Task<HttpStatus> createSavedLead(@NonNull SeatUrn ownerSeatUrn, @NonNull MemberUrn leadMemberUrn,
      @NonNull SavedLead savedLead) {
    byte[] bytes = EspressoUtil.serializeAvroRecord(SAVED_LEAD_SPECIFIC_DATUM_WRITER, savedLead);

    PutRequest request = PutRequest.builder()
        .setIfNoneMatchEtag("*")
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_LEAD)
        .setContent(ContentType.AVRO_BINARY, SAVED_LEAD_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(ownerSeatUrn.toString(), leadMemberUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.PRECONDITION_FAILED) {
        // Since we set IfNoneMatchEtag to avoid createdTime being overwritten, Espresso returns
        // ResponseStatus.PRECONDITION_FAILED if the same lead already exists. We return 200 OK as we don't treat it as
        // an error case.
        return HttpStatus.S_200_OK;
      } else {
        LOG.error("Failed to create lead {} for seat {}: {}", leadMemberUrn, ownerSeatUrn, response);
        return HttpStatus.S_500_INTERNAL_SERVER_ERROR;
      }
    });
  }

  /**
   * Create multiple Espresso savedLeads with multi-put
   * It is recommended not to batch write with size larger than 100
   * @param ownerSeatUrn owner's seatUrn
   * @param memberToSavedLeadMap map of MemberUrn and espresso savedLeads to be saved in DB
   * @return map of MemberUrn to Http Status
   * return 201 if create successfully, 200 if there is conflict, otherwise return 500
   */
  public Task<Map<MemberUrn, HttpStatus>> createSavedLeads(@NonNull SeatUrn ownerSeatUrn,
      @NonNull Map<MemberUrn, SavedLead> memberToSavedLeadMap) {
    Preconditions.checkArgument(memberToSavedLeadMap.size() <= ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE,
        String.format("Espresso multi-put batch size %d is larger than recommended size %d",
            memberToSavedLeadMap.size(), ESPRESSO_MULTI_PUT_RECOMMENDED_BATCH_SIZE));

    Map<MemberUrn, HttpStatus> resultMap = new HashMap<>();
    TxMultiPutRequest.Builder multiPutRequestBuilder = TxMultiPutRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setRetryConfig(RETRY_REQUEST_CONFIG_MULTI_PUT)
        .setKey(ownerSeatUrn.toString());

    memberToSavedLeadMap.forEach((key, value) -> {
      byte[] bytes = EspressoUtil.serializeAvroRecord(SAVED_LEAD_SPECIFIC_DATUM_WRITER, value);
      String subKey = key.toString();
      TxMultiPutRequest.Part.Builder partBuilder = TxMultiPutRequest.Part.builder()
          .setTable(TABLE_SAVED_LEAD)
          .setSubkey(subKey)
          .setContent(ContentType.AVRO_BINARY, SAVED_LEAD_SCHEMA_VERSION, bytes);
      multiPutRequestBuilder.addPart(partBuilder.build());
    });

    return _parSeqEspressoClient.execute(multiPutRequestBuilder.build()).map(multiPutResponse -> {
      // Successful multi-put operation returns 200, each individual sub response may have 200 OK or 201 Created
      // If there is only one sub-request, multi-put response would be same as sub-request response
      if (multiPutResponse.getResponseStatus() == ResponseStatus.OK
          || multiPutResponse.getResponseStatus() == ResponseStatus.CREATED) {
        Map<MemberUrn, HttpStatus> responseMap = multiPutResponse.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
                && part.getContentLocation().getKey().length == 2)
            .map(part -> {
              String seatString = part.getContentLocation().getKey()[0];
              String memberString = part.getContentLocation().getKey()[1];
              int status = part.getResponseStatus().getCode();
              try {
                SeatUrn seatUrn = SeatUrn.deserialize(seatString);
                MemberUrn memberUrn = MemberUrn.deserialize(memberString);
                HttpStatus httpStatus = HttpStatus.S_201_CREATED;
                if (status == HttpStatus.S_200_OK.getCode()) {
                  LOG.warn("The lead {} has already been saved for seat {}", memberUrn, seatUrn);
                  httpStatus = HttpStatus.S_200_OK;
                }
                return new AbstractMap.SimpleEntry<>(memberUrn, httpStatus);
              } catch (URISyntaxException e) {
                LOG.error("Fail to generate the SeatUrn or MemberUrn for seat {}, member {}", seatString, memberString,
                    e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        memberToSavedLeadMap.keySet().forEach(key -> {
          HttpStatus status = responseMap.get(key);
          if (status != null) {
            resultMap.put(key, status);
          } else {
            LOG.error("Cannot get response when saving lead {} for seat {}", key, ownerSeatUrn);
            resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR);
          }
        });
      } else {
        LOG.error("Unexpected response code for call to batch put savedLeads to Espresso for seat {}: {}", ownerSeatUrn,
            multiPutResponse.getResponseStatus());
        memberToSavedLeadMap.keySet().forEach(key -> resultMap.put(key, HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      }
      return resultMap;
    });
  }

  /**
   * Delete a saved lead in SavedLead Espresso table
   * @param ownerSeatUrn seat urn of the lead owner
   * @param leadMemberUrn member urn of the lead
   * @return HttpStatus, 204 for success, 200 in case of nonexistent key and 500 for any other exception
   */
  public Task<HttpStatus> deleteSavedLead(@NonNull SeatUrn ownerSeatUrn, @NonNull MemberUrn leadMemberUrn) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_SAVED_LEAD_ACCOUNT)
        .setTable(TABLE_SAVED_LEAD)
        .setKey(ownerSeatUrn.toString(), leadMemberUrn.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();
    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      //Espresso will return no content if deletion succeeds, and not found if there is no record to delete.
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        return HttpStatus.S_204_NO_CONTENT;
      } else if (deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return HttpStatus.S_200_OK;
      } else {
        LOG.error("Failed to delete lead {} for seat {}: {}", leadMemberUrn, ownerSeatUrn, deleteResponse);
        return HttpStatus.S_500_INTERNAL_SERVER_ERROR;
      }
    });
  }
}