package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.MobileSettings;
import com.linkedin.sales.espresso.SeatSetting;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.IOException;
import java.io.StringWriter;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This is the class to directly talk to espresso Database LssSeatSetting
 */
public class LssSeatSettingDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssSeatSettingDB.class);

  static final String DB_LSS_SEAT_SETTING = "LssSeatSetting";
  static final String TABLE_SEAT_SETTING = "SeatSetting";
  static final String TABLE_MOBILE_SETTINGS = "MobileSettings";
  private static final int SEAT_SETTING_SCHEMA_VERSION = 9;
  private static final int MOBILE_SETTINGS_SCHEMA_VERSION = 2;
  private static final String SETTING_FIELD_CONTRACT_URN = "contractUrn";
  // fields for seat setting
  private static final String SETTING_FIELD_VISIBILITY_AS_PROFILE_VIEWER = "visibilityAsProfileViewer";
  private static final String SETTING_FIELD_EMAIL_PREFERENCES = "emailPreferences";
  private static final String SETTING_FIELD_PUSH_NOTIFICATION_ENABLED = "pushNotificationEnabled";
  private static final String SETTING_FIELD_INMAIL_MESSAGE_SIGNATURE = "inMailMessageSignature";
  private static final String SETTING_FIELD_USAGE_REPORTING_FLAGSHIP_DATA_OPT_OUT = "usageReportingFlagshipDataOptOut";
  private static final String SETTING_FIELD_TEAMLINK_OPTED_OUT = "teamlinkOptedOut";
  private static final String SETTING_FIELD_ONBOARDING_COMPLETED_TIME = "onboardingCompletedTime";
  private static final String SETTING_FIELD_LAST_LOGGED_IN_TIME = "lastLoggedInTime";
  private static final String SETTING_DISMISSED_BUYER_INTENT_CARD_TIME = "dismissedBuyerIntentCardTime";
  private static final String SETTING_DISMISSED_ACCOUNT_IQ_CARD_TIME = "dismissedAccountIqCardTime";
  private static final String SETTING_FIELD_CRM_AUTOSAVE_PREFERENCES = "crmAutoSavePreferences";

  private static final String SETTING_FIELD_SALES_AI_MESSAGING_SETTINGS = "aiMessagingPreferences";
  private static final String SETTING_FIELD_ACCOUNT_DASHBOARD_SETTINGS = "accountDashboardSettings";
  private static final String SETTING_FIELD_TRANSFORM_ONBOARDING_COMPLETED_TIME = "transformOnboardingCompletedTime";
  private static final String SETTING_FIELD_MESSAGE_LEARNING_ENABLED = "messageLearningEnabled";

  // fields for mobile setting
  private static final String SETTING_FIELD_CALENDAR_SYNC_SETTINGS = "calendarSyncSettings";
  private static final String SETTING_FIELD_IS_CALL_LOGGING_ENABLED = "isCallLoggingEnabled";
  private static final String SETTING_FIELD_RATE_THE_APP_LAST_SHOW_AT = "rateTheAppLastShowAt";
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure */
        ResponseStatus.GATEWAY_TIMEOUT /* Gateway timed out */));
    // As P95 get/write/delete/query are 10 ms at most. We think 250 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(250L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private static final SpecificDatumWriter<SeatSetting> SETTING_WRITER = new SpecificDatumWriter<>(SeatSetting.SCHEMA$);
  private static final SpecificDatumReader<SeatSetting> SETTING_READER = new SpecificDatumReader<>(SeatSetting.SCHEMA$);
  private static final SpecificDatumWriter<MobileSettings> MOBILE_SETTINGS_WRITER =
          new SpecificDatumWriter<>(MobileSettings.SCHEMA$);
  private static final SpecificDatumReader<MobileSettings> MOBILE_SETTINGS_READER =
          new SpecificDatumReader<>(MobileSettings.SCHEMA$);
  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssSeatSettingDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Partial update the user setting of the given seat, also used for creation when there is no record on this seat
   * @param seat the owner seat urn
   * @param setting the new setting of the seat
   * @return HTTP status we want the clients to receive, otherwise exception will be thrown
   */
  public Task<HttpStatus> updateSeatSetting(@NonNull SeatUrn seat, @NonNull SeatSetting setting) {
    // Can not use AVRO_BINARY, as it does not support partial_update
    JSONObject obj = new JSONObject();
    if (setting.contractUrn != null) {
      obj.put(SETTING_FIELD_CONTRACT_URN, setting.contractUrn);
    } else {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Required field contractUrn.");
    }
    if (setting.visibilityAsProfileViewer != null) {
      obj.put(SETTING_FIELD_VISIBILITY_AS_PROFILE_VIEWER, setting.visibilityAsProfileViewer.name());
    }
    if (setting.emailPreferences != null) {
      obj.put(SETTING_FIELD_EMAIL_PREFERENCES, setting.emailPreferences);
    }
    if (setting.pushNotificationEnabled != null) {
      obj.put(SETTING_FIELD_PUSH_NOTIFICATION_ENABLED, setting.pushNotificationEnabled);
    }
    if (setting.inMailMessageSignature != null) {
      obj.put(SETTING_FIELD_INMAIL_MESSAGE_SIGNATURE, setting.inMailMessageSignature);
    }
    if (setting.usageReportingFlagshipDataOptOut != null) {
      obj.put(SETTING_FIELD_USAGE_REPORTING_FLAGSHIP_DATA_OPT_OUT, setting.usageReportingFlagshipDataOptOut);
    }
    if (setting.teamlinkOptedOut != null) {
      obj.put(SETTING_FIELD_TEAMLINK_OPTED_OUT, setting.teamlinkOptedOut);
    }
    if (setting.onboardingCompletedTime != null) {
      obj.put(SETTING_FIELD_ONBOARDING_COMPLETED_TIME, setting.onboardingCompletedTime);
    }
    if (setting.lastLoggedInTime != null) {
      obj.put(SETTING_FIELD_LAST_LOGGED_IN_TIME, setting.lastLoggedInTime);
    }
    if (setting.dismissedBuyerIntentCardTime != null) {
      obj.put(SETTING_DISMISSED_BUYER_INTENT_CARD_TIME, setting.dismissedBuyerIntentCardTime);
    }
    if (setting.dismissedAccountIqCardTime != null) {
      obj.put(SETTING_DISMISSED_ACCOUNT_IQ_CARD_TIME, setting.dismissedAccountIqCardTime);
    }
    if (setting.crmAutoSavePreferences != null) {
      obj.put(SETTING_FIELD_CRM_AUTOSAVE_PREFERENCES, setting.crmAutoSavePreferences);
    }

    if (setting.aiMessagingPreferences != null) {
      obj.put(SETTING_FIELD_SALES_AI_MESSAGING_SETTINGS, setting.aiMessagingPreferences);
    }

    if (setting.accountDashboardSettings != null) {
      obj.put(SETTING_FIELD_ACCOUNT_DASHBOARD_SETTINGS, setting.accountDashboardSettings);
    }
    if (setting.transformOnboardingCompletedTime != null) {
      obj.put(SETTING_FIELD_TRANSFORM_ONBOARDING_COMPLETED_TIME, setting.transformOnboardingCompletedTime);
    }
    if (setting.messageLearningEnabled != null) {
      obj.put(SETTING_FIELD_MESSAGE_LEARNING_ENABLED, setting.messageLearningEnabled);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating setting for %s", seat);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_SEAT_SETTING)
        .setTable(TABLE_SEAT_SETTING)
        .setKey(seat.toString())
        .setContent(ContentType.AVRO_JSON, SEAT_SETTING_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return HttpStatus.S_422_UNPROCESSABLE_ENTITY;
      } else {
        String errMsg = String.format("Unexpected response code when updating setting for %s, %s", seat, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get the espresso setting record owned by the seat
   * @param seat the owner seat urn
   * @return seat's setting if it exists
   */
  public Task<SeatSetting> getSeatSetting(@NonNull SeatUrn seat) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SEAT_SETTING)
        .setTable(TABLE_SEAT_SETTING)
        .setKey(seat.toString())
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, SEAT_SETTING_SCHEMA_VERSION)
        .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() == 0) {
          throw new EntityNotFoundException(null, "Cannot find seat setting for " + seat);
        }
        SeatSetting result = EspressoUtil.deserializeSingleAvroRecord(SETTING_READER, getResponse.getPart(0));
        convertEmailPreferencesKeyToCharSequence(result);
        return result;
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, "Cannot find seat setting for " + seat);
      } else {
        String errMsg = String.format("Unexpected response code when getting setting for %s, %s", seat, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Converts Map<Utf8, Boolean> to Map<CharSequence, Boolean> for SeatSetting.emailPreferences
   * @param seatSetting SeatSetting object to be modified
   */
  private void convertEmailPreferencesKeyToCharSequence(SeatSetting seatSetting) {
    // avro 1.4.1 would give us Map<Utf8, Boolean>, though Utf8 implements CharSequence, we still want to convert it
    // to Map<CharSequence, Boolean>, otherwise we would need to use new UTF("xx") as map key all the time
    if (seatSetting.emailPreferences != null) {
      seatSetting.emailPreferences = seatSetting.emailPreferences.entrySet().stream()
          .collect(Collectors.toMap(e -> (CharSequence) (e.getKey().toString()), e -> e.getValue()));
    }
  }

  /**
   * get SeatSettings for a list of Seat Urn's
   * @param keys set of Seat Urn's
   * @return Task with mapping of Seat Urn's to SeatSettings
   */
  public Task<Map<SeatUrn, SeatSetting>> batchGetSeatSettings(@NonNull Set<SeatUrn> keys) {
    List<String []> seatUrnList = keys.stream().map(seatUrn -> new String [] {seatUrn.toString()}).collect(Collectors.toList());
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SEAT_SETTING)
        .setTable(TABLE_SEAT_SETTING)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setAcceptType(ContentType.AVRO_BINARY, SEAT_SETTING_SCHEMA_VERSION)
        .setMultigetKeyList(seatUrnList)
        .build();
    return _parSeqEspressoClient.execute(request).map(this::parseEspressoResponse);
  }

  /**
   * parse espresso response to get a Map of Seat Urn's and SeatSettings
   * @param getResponse response from espresso
   * @return Mapping of Seat Urn's to SeatSetting's
   */
  private Map<SeatUrn, SeatSetting> parseEspressoResponse(GetResponse getResponse) {
    if (getResponse.getResponseStatus() == ResponseStatus.OK
        || getResponse.getResponseStatus() == ResponseStatus.MULTI_STATUS) {
      // the response will return a list of SeatSettings. deserialize all of them.
      return getResponse.getParts().stream()
          .filter(part -> part.getResponseStatus() == ResponseStatus.OK)
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null)
          .map(this::convertEspressoPartToEspressoSeatSetting)
          .filter(Objects::nonNull)
          .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND
        || getResponse.getResponseStatus() == ResponseStatus.NO_CONTENT) {
      return Collections.emptyMap();
    } else {
      // Not expected. Parsing error indicates data corruption of some sort
      throw new RuntimeException(getResponse.getResponseStatus().getText());
    }
  }

  /**
   * convert the response part from espresso to Seat Urn and SeatSetting
   * @param part Serialized SeatSetting retrieved from espresso
   * @return A pair of Seat Urn and SeatSetting
   */
  @Nullable
  private Map.Entry<SeatUrn, SeatSetting> convertEspressoPartToEspressoSeatSetting(@NonNull GetResponse.Part part) {
    SeatSetting seatSetting;
    String[] key;
    SeatUrn seatUrn;
    try {
      seatSetting = EspressoUtil.deserializeSingleAvroRecord(SETTING_READER, part);
      key = part.getContentLocation().getKey();
      convertEmailPreferencesKeyToCharSequence(seatSetting);
    } catch (Exception e) {
      LOG.error("corrupted espresso seat setting record", e);
      return null;
    }
    try {
      // Based on the table schema, the key is seat urn
      seatUrn = SeatUrn.deserialize(key[0]);
    } catch (Exception e) {
      LOG.error("failed to parse the SeatSetting key for {}", seatSetting, e);
      return null;
    }
    return new AbstractMap.SimpleEntry<>(seatUrn, seatSetting);
  }

  /**
   * Partial update the mobile setting of the given seat, also used for creation when there is no record on this seat
   * @param seat the owner seat urn
   * @param setting the new mobile setting of the seat
   * @return HTTP status we want the clients to receive, otherwise exception will be thrown
   */
  public Task<HttpStatus> updateMobileSetting(@NonNull SeatUrn seat, @NonNull MobileSettings setting) {
    // Can not use AVRO_BINARY, as it does not support partial_update
    JSONObject obj = new JSONObject();
    if (setting.contractUrn != null) {
      obj.put(SETTING_FIELD_CONTRACT_URN, setting.contractUrn);
    } else {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Required field contractUrn.");
    }
    if (setting.calendarSyncSettings != null) {
      obj.put(SETTING_FIELD_CALENDAR_SYNC_SETTINGS, setting.calendarSyncSettings);
    }
    if (setting.isCallLoggingEnabled != null) {
      obj.put(SETTING_FIELD_IS_CALL_LOGGING_ENABLED, setting.isCallLoggingEnabled);
    }
    if (setting.rateTheAppLastShowAt != null) {
      obj.put(SETTING_FIELD_RATE_THE_APP_LAST_SHOW_AT, setting.rateTheAppLastShowAt);
    }

    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      String errMsg = String.format("Failed to convert JSONObject to String when updating setting for %s", seat);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
            .setDatabase(DB_LSS_SEAT_SETTING)
            .setTable(TABLE_MOBILE_SETTINGS)
            .setKey(seat.toString())
            .setContent(ContentType.AVRO_JSON, MOBILE_SETTINGS_SCHEMA_VERSION, contentJson)
            .setRetryConfig(RETRY_REQUEST_CONFIG)
            .build();
    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return HttpStatus.S_422_UNPROCESSABLE_ENTITY;
      } else {
        String errMsg = String.format("Unexpected response code when updating setting for %s, %s", seat, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * Get the espresso mobile setting record owned by the seat
   * @param seat the owner seat urn
   * @return seat's mobile settings if it exists
   */
  public Task<MobileSettings> getMobileSetting(@NonNull SeatUrn seat) {
    GetRequest request = GetRequest.builder()
            .setDatabase(DB_LSS_SEAT_SETTING)
            .setTable(TABLE_MOBILE_SETTINGS)
            .setKey(seat.toString())
            .setRetryConfig(RETRY_REQUEST_CONFIG)
            .setAcceptType(ContentType.AVRO_BINARY, MOBILE_SETTINGS_SCHEMA_VERSION)
            .build();
    return _parSeqEspressoClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {
        if (getResponse.getPartCount() == 0) {
          throw new EntityNotFoundException(null, "Cannot find mobile setting for " + seat);
        }
        return EspressoUtil.deserializeSingleAvroRecord(MOBILE_SETTINGS_READER, getResponse.getPart(0));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, "Cannot find mobile setting for " + seat);
      } else {
        String errMsg = String.format("Unexpected response code when getting setting for %s, %s", seat, getResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

}
