package com.linkedin.sales.ds.db;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PostRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.ResourcePolicyView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.ds.rest.ClientConstants.*;
import static java.lang.Boolean.*;


/**
 * Created by hchang at 10/26/2018
 * This is the class to directly talk to espresso Database LssSharing
 */
// TODO: Make resource type into an enum above this level. It should be string only at db level
// TODO: Add batch get, by resource and by subject.
public class LssSharingDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssSharingDB.class);
  private static final int SUBJECT_POLICY_TABLE_SCHEMA_VERSION = 6;
  private static final int RESOURCE_POLICY_VIEW_SCHEMA_VERSION = 5;

  static final String DB_LSS_SHARING = "LssSharing";
  static final String TABLE_SUBJECT_POLICY = "SubjectPolicy";
  static final String TABLE_RESOURCE_POLICY_VIEW = "ResourcePolicyView";
  private static final String SUBJECT_POLICY_FIELD_CONTRACT_URN = "contractUrn";
  private static final String SUBJECT_POLICY_FIELD_ROLE = "role";
  private static final String SUBJECT_POLICY_FIELD_RESOURCE_CONTEXT = "resourceContext";
  private static final String SUBJECT_POLICY_FIELD_SUBSCRIBED = "isSubscribed";
  private static final String SUBJECT_POLICY_FIELD_LAST_VIEWED_TIME = "lastViewedTime";
  private static final String SUBJECT_POLICY_FIELD_CREATED_TIME = "createdTime";
  private static final String SUBJECT_POLICY_FIELD_CREATOR_SEAT_URN = "creatorSeatUrn";
  private static final String SUBJECT_POLICY_FIELD_ACCEPTED_TIME = "acceptedTime";
  private static final String QUERY_DELIMITER_OR = " OR ";
  private static final String QUERY_DELIMITER_AND = " AND ";
  private static final String QUERY_RESOURCE_CONTEXT = "resourceContext:";
  private static final String QUERY_POLICY_FIELD_ROLE = "role:";

  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(
        ResponseStatus.SERVICE_UNAVAILABLE, /* state transition */
        ResponseStatus.INTERNAL_SERVER_ERROR, /* Commit fail or some unknown failure*/
        ResponseStatus.GATEWAY_TIMEOUT /* Gateway timed out */));
    // As P95 get/write/delete/query are 70 ms at most. We think 500 ms should be good enough to retry 5 times.
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(500L);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private static final SpecificDatumReader<ResourcePolicyView> RESOURCE_POLICY_VIEW_READER =
      new SpecificDatumReader<>(ResourcePolicyView.SCHEMA$);

  private static final SpecificDatumReader<SubjectPolicy> SUBJECT_POLICY_READER =
      new SpecificDatumReader<>(SubjectPolicy.SCHEMA$);

  private static final SpecificDatumWriter<SubjectPolicy> SUBJECT_POLICY_WRITER =
      new SpecificDatumWriter<>(SubjectPolicy.SCHEMA$);

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssSharingDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * update or create role on a policy
   * @param key the unique identifier of a policy
   * @param subjectPolicy espresso SubjectPolicy that contains role and contract fields
   * @return True if succeed. False otherwise
   */
  public Task<HttpStatus> updateSubjectPolicy(@NonNull PolicyKey key, @NonNull SubjectPolicy subjectPolicy) {

    if (subjectPolicy.role == null && subjectPolicy.contractUrn == null && subjectPolicy.isSubscribed == null
        && subjectPolicy.lastViewedTime == null && subjectPolicy.createdTime == null && subjectPolicy.creatorSeatUrn == null
        && subjectPolicy.acceptedTime == null) { // we need at least one to update
      return Task.value(HttpStatus.S_400_BAD_REQUEST);
    }

    JSONObject obj = new JSONObject();
    if (subjectPolicy.contractUrn != null) {
      obj.put(SUBJECT_POLICY_FIELD_CONTRACT_URN, subjectPolicy.contractUrn);
    }
    if (subjectPolicy.role != null) {
      obj.put(SUBJECT_POLICY_FIELD_ROLE, subjectPolicy.role.name());
    }
    if (subjectPolicy.resourceContext != null) {
      obj.put(SUBJECT_POLICY_FIELD_RESOURCE_CONTEXT, subjectPolicy.resourceContext.toString());
    }
    if (subjectPolicy.lastViewedTime != null) {
      obj.put(SUBJECT_POLICY_FIELD_LAST_VIEWED_TIME, subjectPolicy.lastViewedTime);
    }
    if (subjectPolicy.isSubscribed != null) {
      obj.put(SUBJECT_POLICY_FIELD_SUBSCRIBED, subjectPolicy.isSubscribed);
    }
    if (subjectPolicy.createdTime != null) {
      obj.put(SUBJECT_POLICY_FIELD_CREATED_TIME, subjectPolicy.createdTime);
    }
    if (subjectPolicy.creatorSeatUrn != null) {
      obj.put(SUBJECT_POLICY_FIELD_CREATOR_SEAT_URN, subjectPolicy.creatorSeatUrn.toString());
    }
    if (subjectPolicy.acceptedTime != null) {
      obj.put(SUBJECT_POLICY_FIELD_ACCEPTED_TIME, subjectPolicy.acceptedTime);
    }
    StringWriter out = new StringWriter();
    try {
      obj.writeJSONString(out);
    } catch (IOException e) {
      LOG.error("fail to convert JSONObject to String when updating policy: {}", key, e);
      return Task.failure(e);
    }
    String contentJson = out.toString();

    PostRequest request = PostRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_SUBJECT_POLICY)
        .setContent(ContentType.AVRO_JSON, SUBJECT_POLICY_TABLE_SCHEMA_VERSION, contentJson)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key.getSubject().toString(), key.getPolicyType().name(), key.getResource().toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(postResponse -> {
      if (postResponse.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else if (postResponse.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (postResponse.getResponseStatus() == ResponseStatus.UNPROCESSABLE_ENTITY) {
        return HttpStatus.S_422_UNPROCESSABLE_ENTITY;
      } else {
        String errMsg = String.format("Unexpected response code for call to update subject policy for %s, %s", key, postResponse);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * delete one policy
   * @param key the unique identifier of a policy
   * @return whether a deletion is successful
   */
  public Task<Boolean> deleteSubjectPolicy(PolicyKey key) {
    DeleteRequest request = DeleteRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_SUBJECT_POLICY)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(key.getSubject().toString(), key.getPolicyType().name(), key.getResource().toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(deleteResponse -> {
      // Also return success when deleting an already deleted entity
      if (deleteResponse.getResponseStatus() == ResponseStatus.NO_CONTENT
          || deleteResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return TRUE;
      } else {
        LOG.error("Unexpected response code for call to delete subject policy for key:{}, {}", key, deleteResponse);
        return FALSE;
      }
    });
  }

  /**
   * get a subject policy. Throw EntityNotFoundException if the subject policy can not be found
   * @param subjectUrn subject urn
   * @param policyType policy type
   * @param resourceUrn resource urn
   * @return a matching subject policy
   */
  public Task<SubjectPolicy> getSubjectPolicy(
      @NonNull Urn subjectUrn,
      @NonNull String policyType,
      @NonNull Urn resourceUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_SUBJECT_POLICY)
        .setAcceptType(ContentType.AVRO_BINARY, SUBJECT_POLICY_TABLE_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(subjectUrn.toString(), policyType, resourceUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        if (response.getPartCount() > 0 && response.getPart(0).getResponseStatus() != ResponseStatus.NOT_FOUND) {
          return EspressoUtil.deserializeSingleAvroRecord(SUBJECT_POLICY_READER, response.getPart(0));
        } else {
          throw new EntityNotFoundException(null, String.format(
              "can not find subjectPolicy for subjectUrn:%s, resourceUrn:%s, policyType:%s", subjectUrn, resourceUrn,
              policyType));
        }
      } else if (response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        throw new EntityNotFoundException(null, String.format(
            "can not find subjectPolicy for subjectUrn:%s, resourceUrn:%s, policyType:%s", subjectUrn, resourceUrn,
            policyType));
      } else {
        String errMsg = String.format(
            "Unexpected response code for call to get subjectPolicy for subjectUrn:%s, resourceUrn:%s, policyType:%s, %s",
            subjectUrn, resourceUrn, policyType, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  public Task<PaginatedList<Pair<Urn, ShareRole>>> getPoliciesByResource(@NonNull Urn resourceUrn,
      @NonNull PolicyType policyType, @Nullable Set<ShareRole> roles, int start, int count) {
    return getPolicyViewsByResource(resourceUrn, policyType, roles, start, count).map(policyPairs -> {
      List<Pair<Urn, ShareRole>> policyRoles = policyPairs.getResult()
          .stream()
          .map(pair -> Pair.make(pair.getFirst(), pair.getSecond().role))
          .collect(Collectors.toList());
      return PaginatedList.createForPage(policyRoles, policyPairs.getOffset(), policyPairs.getLimit(), policyPairs.getTotal());
    });
  }

  /**
   * get all subjects that have a policy associated with the given resource and policy type
   * @param resourceUrn the resource urn to look up
   * @param policyType the policy type to look up
   * @param roles optional roles, if specified then looks only for those roles, if null then looks for all roles. If
   *              empty, raise exception.
   * @param start paging start
   * @param count paging count, count=-1 means get all
   * @return a list of pair of (subjectUrn, ResourcePolicyView)
   */
  public Task<PaginatedList<Pair<Urn, ResourcePolicyView>>> getPolicyViewsByResource(@NonNull Urn resourceUrn,
      @NonNull PolicyType policyType, @Nullable Set<ShareRole> roles, int start, int count) {
    // if empty, raise exception
    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RuntimeException("roles can not be empty"));
    }
    // add all roles if roles is null
    Collection<ShareRole> filteredRoles = roles == null ? Arrays.asList(ShareRole.values()) : roles;
    String queryFilter =
        filteredRoles.stream().map(role -> "role:" + role.toString()).collect(Collectors.joining(QUERY_DELIMITER_OR));

    GetRequest.Builder builder = GetRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_RESOURCE_POLICY_VIEW)
        .setAcceptType(ContentType.AVRO_BINARY, RESOURCE_POLICY_VIEW_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(resourceUrn.toString(), policyType.toString())
        .setQuery(queryFilter)
        .setQueryReturnTotalHits(TRUE)
        .setStart(start)
        .setCount(count);

    return _parSeqEspressoClient.execute(builder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK) {
        int totalHits = response.getTotalHits() != null ? response.getTotalHits() : response.getParts().size();
        List<Pair<Urn, ResourcePolicyView>> returnPairs = response.getParts()
            .stream()
            .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null)
            .map(part -> {
              ResourcePolicyView resourcePolicyView =
                  EspressoUtil.deserializeSingleAvroRecord(RESOURCE_POLICY_VIEW_READER, part);
              // 3 keys: resourceUrn, resourceType, subjectUrn
              String subjectUrnKey = part.getContentLocation().getKey()[2];
              try {
                Urn subjectUrn = Urn.createFromString(subjectUrnKey);
                return new Pair<>(subjectUrn, resourcePolicyView);
              } catch (URISyntaxException e) {
                LOG.error("fail to generate the subjectUrn for the key {}", subjectUrnKey, e);
                return null;
              }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        return PaginatedList.createForPage(returnPairs, start, count, totalHits);
      } else if (response.getResponseStatus() == ResponseStatus.NOT_FOUND
          || response.getResponseStatus() == ResponseStatus.NO_CONTENT) {
        // Don't throw an error, this is expected if no owner has been previously added
        return PaginatedList.createForPage(Collections.emptyList(), DEFAULT_START, DEFAULT_COUNT, DEFAULT_TOTAL);
      } else {
        String errMsg =
            String.format("Unexpected response code for call to getPoliciesByResource resourceUrn:%s, %s", resourceUrn,
                response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }

  /**
   * find policies for subject
   * @param subjectUrn subject urn
   * @param resourceType resource type
   * @param roles roles that want to fetch, if null, default to all roles. If empty, raise exception.
   * @param start paging start
   * @param count paging count
   * @return paginated list of pair(resourceUrn, SubjectPolicy)
   */
  public Task<PaginatedList<Pair<Urn, SubjectPolicy>>> getPoliciesBySubject(
      @NonNull Urn subjectUrn,
      @NonNull String resourceType,
      @Nullable Set<ShareRole> roles,
      int start,
      int count) {
    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RuntimeException("roles can not be empty"));
    }
    Set<ShareRole> filteredRoles =
        roles == null ? Arrays.stream(ShareRole.values()).collect(Collectors.toSet()) : roles;
    String filterQuery =
        filteredRoles.stream().map(role -> "role:" + role.toString()).collect(Collectors.joining(QUERY_DELIMITER_OR));

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_SUBJECT_POLICY)
        .setAcceptType(ContentType.AVRO_BINARY, SUBJECT_POLICY_TABLE_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(subjectUrn.toString(), resourceType)
        .setStart(start)
        .setCount(count)
        .setQuery(filterQuery)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      Integer totalHits = response.getTotalHits() != null ? response.getTotalHits() : response.getParts().size();
      List<Pair<Urn, SubjectPolicy>> pairs = response.getParts()
          .stream()
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3) // make sure we have 3 keys here
          .map(part -> {
            // 3 keys: subjectUrn, policyType, resourceUrn
            String resourceUrnStr = part.getContentLocation().getKey()[2];
            try {
              Urn resourceUrn = Urn.createFromString(resourceUrnStr);
              return new Pair<>(resourceUrn, EspressoUtil.deserializeSingleAvroRecord(SUBJECT_POLICY_READER, part));
            } catch (URISyntaxException e) {
              LOG.warn("fail to create Urn from resource {}", resourceUrnStr, e);
              return null;
            }
          })
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
      return PaginatedList.createForPage(pairs, start, count, totalHits);
    });
  }


  /**
   * find policies by resource context
   * @param subjectUrn subject urn, For eg. the contractUrn is the subjectUrn for notes shared across contract
   * @param resourceContext the shared entity is related to this resource. For eg. For notes it can be memberUrn
   * @param entityType the shared entity type, eg: Note, list, etc
   * @param roles roles that want to fetch, if null, default to all roles. If empty, raise exception.
   * @param start paging start
   * @param count paging count
   * @return paginated list of pair(resourceUrn, SubjectPolicy)
   */
  public Task<PaginatedList<Pair<Urn, SubjectPolicy>>> getPoliciesByResourceContextAndSubject(
      @NonNull Urn subjectUrn,
      @NonNull Policy.ResourceContext resourceContext,
      @NonNull PolicyType entityType,
      @Nullable Set<ShareRole> roles,
      int start,
      int count) {
    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RuntimeException("roles can not be empty"));
    }
    Set<ShareRole> filteredRoles =
        roles == null ? Arrays.stream(ShareRole.values()).collect(Collectors.toSet()) : roles;

    String resourceContextQuery = convertResourceContextToQueryString(resourceContext);
    if (resourceContextQuery == null) {
      return Task.failure(new RuntimeException("Error converting resourceContext to resourceContext query."));
    }

    String query;
    // Espresso native index does not support using AND and OR in the same query, so only apply filter on role if there
    // is one role. See https://iwww.corp.linkedin.com/wiki/cf/display/ENGS/Espresso+Secondary+Indexes#EspressoSecondaryIndexes-Query
    // for more details.
    if (filteredRoles.size() == 1) {
      String rolesQuery = "role:" + filteredRoles.stream().iterator().next();
      query = resourceContextQuery + QUERY_DELIMITER_AND + rolesQuery;
    } else {
      query = resourceContextQuery;
    }

    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_SHARING)
        .setTable(TABLE_SUBJECT_POLICY)
        .setAcceptType(ContentType.AVRO_BINARY, SUBJECT_POLICY_TABLE_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(subjectUrn.toString(), entityType.toString())
        .setStart(start)
        .setCount(count)
        .setQuery(query)
        .setQueryReturnTotalHits(TRUE)
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      Integer totalHits = response.getTotalHits() != null ? response.getTotalHits() : response.getParts().size();
      List<Pair<Urn, SubjectPolicy>> pairs = response.getParts()
          .stream()
          .filter(part -> part.getContentLocation() != null && part.getContentLocation().getKey() != null
              && part.getContentLocation().getKey().length == 3) // make sure we have 3 keys here
          .map(part -> {
            // 3 keys: subjectUrn, policyType, resourceUrn
            String resourceUrnStr = part.getContentLocation().getKey()[2];
            try {
              Urn resourceUrn = Urn.createFromString(resourceUrnStr);
              SubjectPolicy policy = EspressoUtil.deserializeSingleAvroRecord(SUBJECT_POLICY_READER, part);
              // If there is more than one role, then we did not filter by them using the Espresso query,
              // so filter them out now
              if (!filteredRoles.contains(policy.getRole())) {
                return null;
              }
              return new Pair<>(resourceUrn, policy);
            } catch (URISyntaxException e) {
              LOG.warn("fail to create Urn from resource {}", resourceUrnStr, e);
              return null;
            }
          })
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
      return PaginatedList.createForPage(pairs, start, count, totalHits);
    });
  }

  /**
   * get the number of sharing policies for a given resource
   * @param resourceUrn the resource urn to look up
   * @param policyType the policy type of the resource to look up
   * @return a task of integer indicating the number of sharing policies,
   */
  public Task<Integer> getResourceSharingPolicyTotal(@NonNull Urn resourceUrn, @NonNull PolicyType policyType) {
    // if getPoliciesByResource failure, return 0 as default value
    return getPoliciesByResource(resourceUrn, policyType, null, 0, 1)
        .map(paginatedList -> paginatedList.getTotal())
        .recover(t -> 0);
  }

  /**
   * batch get the number of sharing policies for given resources
   * @param resourceUrns the resource urns to look up
   * @param policyType the policy type of the resource to look up
   * @return a task of resourceUrn to integer map map indicating the number of sharing for each resource,
   */
  public Task<Map<Urn, Integer>>  batchGetResourceSharingPolicyTotal(@NonNull Set<Urn> resourceUrns, @NonNull PolicyType policyType) {
    Set<Task<AbstractMap.SimpleEntry<Urn, Integer>>> batchGetResourceSharingPolicyTotalTask = resourceUrns.stream()
        .map(resourceUrn -> getResourceSharingPolicyTotal(resourceUrn, policyType).map(
            sharingPolicyTotal -> new AbstractMap.SimpleEntry<>(resourceUrn, sharingPolicyTotal)))
        .collect(Collectors.toSet());

    return Task.par(batchGetResourceSharingPolicyTotalTask)
        .map(sharingPolicyTotals -> sharingPolicyTotals.stream()
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  @VisibleForTesting
  String convertResourceContextToQueryString(Policy.ResourceContext resourceContext) {
    if (resourceContext.isNull()) {
      return null;
    }
    if (resourceContext.isMember()) {
      return "resourceContext:\\{member\\=urn\\:li\\:member\\:" + resourceContext.getMember().getId() + "\\}";
    } else if (resourceContext.isOrganization()) {
      return "resourceContext:\\{organization\\=urn\\:li\\:organization\\:"
          + resourceContext.getOrganization().getId() + "\\}";
    } else {
      LOG.error("Resource context type not defined. Resource Context : {}", resourceContext);
      return null;
    }
  }
}
