package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ContentType;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.PutRequest;
import com.linkedin.espresso.pub.operations.RetryRequestConfig;
import com.linkedin.espresso.pub.operations.retrypolicy.BackoffPolicies;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.espresso.utils.EspressoUtil;
import com.linkedin.sales.espresso.WarmIntroRecommendation;
import com.linkedin.util.annotation.TypesAreNonnullByDefault;
import java.util.Arrays;
import java.util.Optional;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Espresso client for LssWarmIntros DB.
 */
@TypesAreNonnullByDefault
public class LssWarmIntrosDB {
  private static final Logger LOG = LoggerFactory.getLogger(LssWarmIntrosDB.class);

  protected static final String DB_LSS_WARM_INTRO_RECOMMENDATION = "LssWarmIntros";
  protected static final String TABLE_WARM_INTRO_RECOMMENDATION = "WarmIntroRecommendation";
  private static final SpecificDatumWriter<WarmIntroRecommendation> WARM_INTRO_RECOMMENDATION_SPECIFIC_DATUM_WRITER =
      new SpecificDatumWriter<>(WarmIntroRecommendation.SCHEMA$);
  private static final SpecificDatumReader<WarmIntroRecommendation> WARM_INTRO_RECOMMENDATION_SPECIFIC_DATUM_READER =
      new SpecificDatumReader<>(WarmIntroRecommendation.SCHEMA$);
  private static final int WARM_INTRO_RECOMMENDATION_SCHEMA_VERSION = 1;

  private static final long RETRY_TIMEOUT_MILLIS = 500L;
  private static final RetryRequestConfig RETRY_REQUEST_CONFIG;
  static {
    RETRY_REQUEST_CONFIG = new RetryRequestConfig();
    RETRY_REQUEST_CONFIG.setRetriableResponses(Arrays.asList(ResponseStatus.SERVICE_UNAVAILABLE,
        ResponseStatus.INTERNAL_SERVER_ERROR,
        ResponseStatus.GATEWAY_TIMEOUT,
        ResponseStatus.TOO_MANY_REQUESTS));
    RETRY_REQUEST_CONFIG.setRetryTimeoutMillis(RETRY_TIMEOUT_MILLIS);
    RETRY_REQUEST_CONFIG.setRetryBackOffPolicy(BackoffPolicies.exponentialDelay());
  }

  private final ParSeqEspressoClient _parSeqEspressoClient;

  public LssWarmIntrosDB(ParSeqEspressoClient parSeqEspressoClient) {
    _parSeqEspressoClient = parSeqEspressoClient;
  }

  /**
   * Create or update a new WarmIntroRecommendation record in the database.
   * @param seatUrn The seatUrn of the seller.
   * @param leadMemberUrn The unique member URN associated with the lead.
   * @param warmIntroRecommendation The WarmIntroRecommendation object to be created.
   * @return A Task that resolves to the HTTP status of the operation.
   * @throws RuntimeException If serialization or database operation fails.
   */
  public Task<HttpStatus> createOrUpdateWarmIntroRecommendation(SeatUrn seatUrn, MemberUrn leadMemberUrn,
      WarmIntroRecommendation warmIntroRecommendation) {
    byte[] bytes;
    try {
      bytes = EspressoUtil.serializeAvroRecord(WARM_INTRO_RECOMMENDATION_SPECIFIC_DATUM_WRITER, warmIntroRecommendation);
    } catch (Exception e) {
      String errMsg = String.format("Failed to serialize WarmIntroRecommendation for seat: %s, lead: %s", seatUrn, leadMemberUrn);
      LOG.error(errMsg, e);
      throw new RuntimeException(errMsg, e);
    }

    PutRequest.Builder requestBuilder = PutRequest.builder()
        .setDatabase(DB_LSS_WARM_INTRO_RECOMMENDATION)
        .setTable(TABLE_WARM_INTRO_RECOMMENDATION)
        .setKey(seatUrn.toString(), leadMemberUrn.toString())
        .setContent(ContentType.AVRO_BINARY, WARM_INTRO_RECOMMENDATION_SCHEMA_VERSION, bytes)
        .setRetryConfig(RETRY_REQUEST_CONFIG);

    return _parSeqEspressoClient.execute(requestBuilder.build()).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.CREATED) {
        return HttpStatus.S_201_CREATED;
      } else if (response.getResponseStatus() == ResponseStatus.OK) {
        return HttpStatus.S_200_OK;
      } else {
        LOG.error("Unexpected error while creating WarmIntroRecommendation record for seat:{} leadMember:{}, {}",
            seatUrn, leadMemberUrn, response);
        throw new RuntimeException("Unexpected error occurred: " + response.getResponseStatus());
      }
    });
  }

  /**
   * Get a warm intro recommendation record
   * @param seatUrn the seat urn
   * @param leadMemberUrn the lead member urn
   * @return A task that resolves to an Optional containing the WarmIntroRecommendation record if found, or an empty Optional if not found.
   * @throws RuntimeException If an unexpected error occurs during the retrieval process.
   */
  public Task<Optional<WarmIntroRecommendation>> getWarmIntroRecommendation(SeatUrn seatUrn, MemberUrn leadMemberUrn) {
    GetRequest request = GetRequest.builder()
        .setDatabase(DB_LSS_WARM_INTRO_RECOMMENDATION)
        .setTable(TABLE_WARM_INTRO_RECOMMENDATION)
        .setAcceptType(ContentType.AVRO_BINARY, WARM_INTRO_RECOMMENDATION_SCHEMA_VERSION)
        .setRetryConfig(RETRY_REQUEST_CONFIG)
        .setKey(seatUrn.toString(), leadMemberUrn.toString())
        .build();

    return _parSeqEspressoClient.execute(request).map(response -> {
      if (response.getResponseStatus() == ResponseStatus.OK && response.getPartCount() > 0) {
        return Optional.of(EspressoUtil.deserializeSingleAvroRecord(WARM_INTRO_RECOMMENDATION_SPECIFIC_DATUM_READER, response.getPart(0)));
      } else if (response.getResponseStatus() == ResponseStatus.NO_CONTENT || response.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        LOG.warn("No WarmIntroRecommendation found for seat: {}, lead: {}", seatUrn, leadMemberUrn);
        return Optional.empty();
      } else {
        String errMsg = String.format("Get WarmIntroRecommendation failed for seat: %s, lead: %s, response: %s", seatUrn, leadMemberUrn, response);
        LOG.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    });
  }
}
