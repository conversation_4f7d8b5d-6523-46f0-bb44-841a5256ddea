package com.linkedin.sales.ds.db.exception;

import org.springframework.dao.EmptyResultDataAccessException;
import org.xeril.util.tx.TransactionException;


/**
 * Utility class providing useful methods relating to db exceptions
 *
 * <AUTHOR>
 */
public final class ExceptionUtils {
  private ExceptionUtils() {

  }

  /**
   * Checks if the nested exception in a transaction exception is an {@link EmptyResultDataAccessException}
   * This exception is thrown by the spring transaction fwk when an object expected to be found isn't found.
   *
   * @param te transaction exception
   * @return boolean representing whether the exception is because of a empty result data access exception
   */
  public static boolean isEmptyDataAccessException(TransactionException te) {
    return EmptyResultDataAccessException.class == te.getCause().getClass();
  }

  /**
   * check if the throwable is an EntityNotFoundException
   * @param t throwable object
   * @return if the throwable object is because of EntityNotFoundException
   */
  public static boolean isEntityNotFoundException(Throwable t) {
    return t instanceof EntityNotFoundException;
  }

  /**
   * check if the throwable is a TooManyRequestsException
   * @param t throwable object
   * @return if the throwable object is because of TooManyRequestsException
   */
  public static boolean isTooManyRequestsException(Throwable t) {
    return t instanceof TooManyRequestsException;
  }
}
