package com.linkedin.sales.ds.db.model;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import java.util.Objects;


/**
 * Represents the Espresso key for the LeadAccountAssociation document.
 * <AUTHOR>
 */
public class LeadAccountAssociationKey {
  private final SeatUrn _seatUrn;
  private final MemberUrn _memberUrn;
  private final OrganizationUrn _orgUrn;

  public LeadAccountAssociationKey(SeatUrn seatUrn, MemberUrn memberUrn, OrganizationUrn orgUrn) {
    _seatUrn = seatUrn;
    _memberUrn = memberUrn;
    _orgUrn = orgUrn;
  }

  public SeatUrn getSeatUrn() {
    return _seatUrn;
  }

  public MemberUrn getMemberUrn() {
    return _memberUrn;
  }

  public OrganizationUrn getOrgUrn() {
    return _orgUrn;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LeadAccountAssociationKey that = (LeadAccountAssociationKey) o;
    return Objects.equals(_seatUrn, that._seatUrn) && Objects.equals(_memberUrn, that._memberUrn) && Objects.equals(
        _orgUrn, that._orgUrn);
  }

  @Override
  public int hashCode() {
    return Objects.hash(_seatUrn, _memberUrn, _orgUrn);
  }
}
