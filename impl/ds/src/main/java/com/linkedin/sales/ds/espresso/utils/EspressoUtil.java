package com.linkedin.sales.ds.espresso.utils;

import com.google.common.base.Preconditions;
import com.google.common.escape.CharEscaperBuilder;
import com.google.common.escape.Escaper;
import com.linkedin.avroutil1.compatibility.AvroCompatibilityHelper;
import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.pub.ResponseStatus;
import com.linkedin.espresso.pub.operations.EspressoClientRequest;
import com.linkedin.espresso.pub.operations.EspressoClientResponse;
import com.linkedin.espresso.pub.operations.GetRequest;
import com.linkedin.espresso.pub.operations.GetResponse;
import com.linkedin.parseq.Task;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import org.apache.avro.io.BinaryDecoder;
import org.apache.avro.io.BinaryEncoder;
import org.apache.avro.specific.SpecificDatumReader;
import org.apache.avro.specific.SpecificDatumWriter;
import org.apache.avro.specific.SpecificRecord;


/**
 * Collection of static utility functions for use with Activity and ActivityEvent
 */
public final class EspressoUtil {

  /**
   * For escaping reserved chars as documented on Espresso Quickstart. Regex is overpowered for escaping, which is
   * one reason Guava's Escaper was written.
   */
  public static final Escaper ESCAPER;

  static {
    CharEscaperBuilder builder = new CharEscaperBuilder();
    char[] chars = "+-&|!()\\{\\}[]^\"~*?:".toCharArray();
    for (char aChar : chars) {
      builder.addEscape(aChar, new String(new char[]{'\\', aChar}));
    }
    ESCAPER = builder.toEscaper();
  }

  // should never be instantiated.
  private EspressoUtil() {
  }

  /**
   * Serializes an Avro Specific Record
   *
   * @return The avro-serialized bytes appropriate for content-type "avro/binary"
   * @throws RuntimeException in the event of a serialization-related error
   */
  public static <SR extends SpecificRecord> byte[] serializeAvroRecord(@NonNull SpecificDatumWriter<SR> writer,
      @NonNull SR record) {
    byte[] bytes;
    try {
      ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
      BinaryEncoder encoder = AvroCompatibilityHelper.newBinaryEncoder(outputStream);
      writer.write(record, encoder);
      encoder.flush();
      bytes = outputStream.toByteArray();
    } catch (IOException e) {
      throw new RuntimeException("Failed to serialize " + record);
    }
    return bytes;
  }

  /**
   * Deserializes a given {@link GetResponse.Part} with the given reader.  Input is expressed as a Part (rather than
   * a {@code byte[]}) only for the purpose of error handling; the part's location will be reported in the exception.
   *
   * @param reader The record reader
   * @param part The part to read
   * @return The decoded avro record
   * @see #deserializeAvroRecords(SpecificDatumReader, List, Consumer) for efficient deserialization of lists
   * @see #deserializeAvroRecords(SpecificDatumReader, List) for efficient deserialization of lists
   */
  public static <SR extends SpecificRecord> SR deserializeSingleAvroRecord(@NonNull SpecificDatumReader<SR> reader,
      @NonNull GetResponse.Part part) {
    Preconditions.checkNotNull(part.getContent(), "part content must not be null.");
    BinaryDecoder decoder = AvroCompatibilityHelper.newBinaryDecoder(part.getContent());
    try {
      return reader.read(null, decoder);
    } catch (IOException e) {
      throw new RuntimeException("failed to deserialize " + part.getContentLocation(), e);
    }
  }

  public static <SR extends SpecificRecord> List<SR> deserializeAvroRecords(@NonNull SpecificDatumReader<SR> reader,
      @NonNull List<GetResponse.Part> parts) {

    BinaryDecoder decoder = null;
    List<SR> results = new ArrayList<>(parts.size());
    for (GetResponse.Part part : parts) {
      decoder = AvroCompatibilityHelper.newBinaryDecoder(part.getContent());
      try {
        results.add(reader.read(null, decoder));
      } catch (IOException e) {
        throw new RuntimeException("failed to deserialize " + part.getContentLocation(), e);
      }
    }
    return results;
  }

  /**
   * The most efficient way to deserialize a list of avro records. {@code uponDeserialize()} is called once for every Part,
   * and pases the same record object, but with the current Part's values.
   *
   * Your callback must not store the record passed to the callback! Intended usage is that values be read off the callback object.
   *
   * This is not an async call.
   *
   * @param reader The reader
   * @param parts The parts
   * @param uponDeserialize Is called once for every part. All calls pass the same object, so passed consumer must operate
   *                        by reading values off the passed record.
   */
  public static <SR extends SpecificRecord> void deserializeAvroRecords(@NonNull SpecificDatumReader<SR> reader,
      @NonNull List<GetResponse.Part> parts, Consumer<SR> uponDeserialize) {
    BinaryDecoder decoderForReuse = null;
    SR recordForReuse = null;
    for (GetResponse.Part part : parts) {
      decoderForReuse = AvroCompatibilityHelper.newBinaryDecoder(part.getContent());
      try {
        recordForReuse = reader.read(recordForReuse, decoderForReuse);
        uponDeserialize.accept(recordForReuse);
      } catch (IOException e) {
        throw new RuntimeException("failed to deserialize " + part.getContentLocation(), e);
      }
    }
  }

  /**
   * Executes a GetRequest for a single response.
   * @return The Part, or empty if no part was found.
   */
  public static Task<Optional<GetResponse.Part>> executeForSingleResponse(ParSeqEspressoClient parSeqClient, GetRequest request) {
    return parSeqClient.execute(request).map(getResponse -> {
      if (getResponse.getResponseStatus() == ResponseStatus.OK) {

        // deserialize
        return Optional.of(getResponse.getPart(0));
      } else if (getResponse.getResponseStatus() == ResponseStatus.NOT_FOUND) {
        return Optional.empty();
      } else {
        throw new RuntimeException("Unexpected response code from response" + getResponse);
      }
    });
  }

  /**
   * Wraps IOExceptions thrown by  {@link EspressoClient#execute(GetRequest)} with a runtime exception
   * @throws RuntimeException in the event of an IOException during "execute"
   */
  public static <R extends EspressoClientResponse> R doExecute(EspressoClient espressoClient,
      EspressoClientRequest request) {
    try {
      return (R) espressoClient.execute(request);
    } catch (IOException e) {
      throw new RuntimeException("Failed to execute " + request, e);
    }
  }
}
