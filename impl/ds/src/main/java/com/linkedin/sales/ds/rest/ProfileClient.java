package com.linkedin.sales.ds.rest;

import com.linkedin.data.schema.PathSpec;
import com.linkedin.identity.Profile;
import com.linkedin.identity.ProfileKey;
import com.linkedin.identity.ProfileParam;
import com.linkedin.identity.ProfilesRequestBuilders;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchGetEntityRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EntityResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This client is used to communicate with the ISB
 */
public class ProfileClient {

  private static final ProfilesRequestBuilders PROFILES_REQUEST_BUILDERS = new ProfilesRequestBuilders();
  private static final Logger LOG = LoggerFactory.getLogger(ProfileClient.class);

  private final ParSeqRestClient _parseqRestClient;

  public ProfileClient(ParSeqRestClient parSeqRestClient) {
    _parseqRestClient = parSeqRestClient;
  }

  public Task<List<Profile>> batchGet(Set<Long> memberIds, Long viewerId, PathSpec[] fields) {
    BatchGetEntityRequest<ComplexResourceKey<ProfileKey, ProfileParam>, Profile> request =
        PROFILES_REQUEST_BUILDERS.batchGet()
            .ids(memberIds.stream()
                .map(id -> new ComplexResourceKey<>(new ProfileKey().setId(id), new ProfileParam()))
                .collect(Collectors.toList()))
            .viewerIdParam(viewerId)
            .fields(fields)
            .build();
    return _parseqRestClient.createTask(request)
        .map(Response::getEntity)
        .map(profileResourceResponse -> profileResourceResponse.getResults()
            .values()
            .stream()
            .map(EntityResponse::getEntity)
            .collect(Collectors.toList()))
        .recover(t -> {
          LOG.error("Unexpected error while issuing the following request to /profiles -> {}", request);
          throw new RuntimeException(t);
        });
  }

  public Task<Profile> getProfile(Long viewer, Long viewee, PathSpec[] fields) {
    GetRequest<Profile> getRequest = PROFILES_REQUEST_BUILDERS.get()
        .viewerIdParam(viewer)
        .id(toProfileComplexKey(viewee))
        .fields(fields)
        .build();
    return _parseqRestClient.createTask(getRequest)
        .map(Response::getEntity);
  }

  public static ComplexResourceKey<ProfileKey, ProfileParam> toProfileComplexKey(Long id) {
    return new ComplexResourceKey<>(
        new ProfileKey().setId(id), new ProfileParam());
  }

}