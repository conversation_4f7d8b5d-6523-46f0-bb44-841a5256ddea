package com.linkedin.sales.ds.rest;

import com.linkedin.common.TimeSpan;
import com.linkedin.common.TimeUnit;
import com.linkedin.common.UrnArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.digitalmedia.AssetMediaArtifactFileIdentifiers;
import com.linkedin.digitalmedia.playablestream.BestMatch;
import com.linkedin.digitalmedia.playablestream.SizeMatch;
import com.linkedin.identity.Profile;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.FindRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.vector.assetmanager.AssetMediaArtifactPublicUrlsRequestBuilders;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This utility class exists to provide convenience methods as they pertain to Vector
 */
public class VectorClient {
  private static final AssetMediaArtifactPublicUrlsRequestBuilders VECTOR_PROFILE_PIC_BATCH_GET_REQUEST_BUILDERS =
      new AssetMediaArtifactPublicUrlsRequestBuilders();
  private static final Long ONE_DAY_IN_SECONDS = 86400L;
  private static final Logger LOG = LoggerFactory.getLogger(VectorClient.class);

  private final ParSeqRestClient _parSeqRestClient;

  public VectorClient(ParSeqRestClient parSeqRestClient) {
    _parSeqRestClient = parSeqRestClient;
  }

  public Task<List<AssetMediaArtifactFileIdentifiers>> batchGetLinkedinProfilePhotoUrl(@NonNull List<Profile> profiles,
      int height, int width) {
    // get all the digitalMediaUrns that are nested in each profile (id of the profile pics)
    UrnArray mediaAssets = profiles.stream()
        .filter(Objects::nonNull)
        .filter(profile -> profile.hasProfilePicture() && profile.getProfilePicture().hasDisplayImage())
        .map(profile -> profile.getProfilePicture().getDisplayImage())
        .collect(Collectors.toCollection(UrnArray::new));

    // nothing to fetch if assetUrns list is empty
    if (mediaAssets.isEmpty()) {
      return Task.value(Collections.emptyList());
    }

    // indicate the preferred size of the images based on the passed in params
    BestMatch preferredDisplaySize = new BestMatch().setHeight(height).setWidth(width);
    SizeMatch size = new SizeMatch();
    size.setBestMatch(preferredDisplaySize);
    TimeSpan ttl = new TimeSpan()
        .setDuration(ONE_DAY_IN_SECONDS) // expire this link in 24 hours, very important for legal purposes
        .setUnit(TimeUnit.SECOND);

    //build the request
    FindRequest<AssetMediaArtifactFileIdentifiers> findRequest =
        VECTOR_PROFILE_PIC_BATCH_GET_REQUEST_BUILDERS.findByPlayableStreams()
            .assetUrnsParam(mediaAssets)
            .sizeParam(size)
            .ttlParam(ttl)
            .viewerParam(new MemberUrn(-1L))
            .build();

    return _parSeqRestClient.createTask(findRequest)
        .map(response -> response.getEntity().getElements())
        .onFailure(t -> LOG.error("Failed to batch get vectors for profiles", t));
  }
}
