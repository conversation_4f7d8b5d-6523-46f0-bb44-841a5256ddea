import com.github.spotbugs.snom.SpotBugsTask


ext.apiMpName = 'lss-mt-api'
ext.apiMpApiDir = 'lss-mt-api'
ext.apiProject = project(':rest-api')

apply plugin: 'li-java'
apply plugin: 'li-component'


configurations {
  testArtifacts
}

// The following transitive dependencies bring a wrong implementation of javax.ws.rs.core. implementation, which is used by azkaban.client.AzkabanClient,
// resulting in "java.lang.NoSuchMethodError: 'void javax.ws.rs.core.MultivaluedMap.addAll"
configurations.all { configuration ->
  exclude group: 'com.sun.jersey.contribs', module: 'jersey-guice'
  exclude group: 'com.sun.jersey', module: 'jersey-client'
  exclude group: 'com.sun.jersey', module: 'jersey-core'
  exclude group: 'com.sun.jersey', module: 'jersey-json'
  exclude group: 'com.sun.jersey', module: 'jersey-server'
}

dependencies {
  api project(":impl:rest-impl$scalaSuffix")
  api project(':impl:client')
  api project(":impl:service$scalaSuffix")
  api spec.external.'spotbugs-annotations'
  api spec.product.container.'pegasus-rest-client-factory'
  api spec.product.container.'pegasus-restli-server-factory'
  api spec.product.'container-dds'.'util-auto-commit-executor-pooled-factory'
  api spec.product.'groot-lss-mt'.'groot-lss-mt-impl'
  api spec.product.'gaap-tasks-client-java'.'polling-client'
  api spec.product.'parseq-rest-client'.'parseq-rest-client-factory'
  api spec.product.util.'util-core'
  api spec.product.util.'util-core-factory'
  api spec.product.util.'util-factory'
  api spec.product.util.'util-factory-tools'
  api spec.product.util.'util-servlet-factory'
  api spec.product.frameworks.'url-factory-factory'
  api spec.product.'parseq-espresso-client'.'parseq-espresso-client-factory'
  api spec.product.autortf.'autortf-injector'
  api spec.product.'i18n-core'.'i18n-core-factory'
  api spec.product.'venice-thin-client'.'venice-thin-client'

  api spec.product.container.'healthcheck-factory'
  api spec.product.'container-core'.'sensor-registry-factory'
  api spec.product.'container-deprecated'.'container-rpcExecutorService-factory'

  testImplementation spec.product.container.'container-infrastructure-factory-test'
  testImplementation spec.external.'parseq-test-api'
}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.jvmArgs = ['-Xms8g', '-Xmx8g']
}

tasks.withType(SpotBugsTask).configureEach {
  maxHeapSize = '4g'
}

// BEGIN: grpc migration script changes
dependencies {
  implementation spec.product.'grpc-infra'.'proto-pegasus-service-interop'
  implementation spec.product.'container'.'container-logging-factory'
  implementation spec.product.'container'.'grpc-server-factory'
}
// END: grpc migration script changes

