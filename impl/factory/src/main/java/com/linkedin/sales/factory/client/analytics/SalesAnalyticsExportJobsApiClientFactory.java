package com.linkedin.sales.factory.client.analytics;

import com.linkedin.grpc.client.factory.DefaultD2ManagedChannelProviderFactory;
import com.linkedin.sales.client.analytics.SalesAnalyticsExportJobsApiClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesAnalyticsExportJobsApiClientFactory extends SimpleSingletonFactory<SalesAnalyticsExportJobsApiClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesAnalyticsExportJobsApiClient");

  @Import(clazz = DefaultD2ManagedChannelProviderFactory.class)
  @Override
  protected SalesAnalyticsExportJobsApiClient createInstance(ConfigView view) {
    return new SalesAnalyticsExportJobsApiClient(getBean(DefaultD2ManagedChannelProviderFactory.class));
  }
}
