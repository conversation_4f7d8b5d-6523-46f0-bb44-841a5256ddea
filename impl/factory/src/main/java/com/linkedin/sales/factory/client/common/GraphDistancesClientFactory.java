package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.messaging.GraphDistancesClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class GraphDistancesClientFactory extends SimpleSingletonFactory<GraphDistancesClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("graphDistancesClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected GraphDistancesClient createInstance(ConfigView view) {
    return new GraphDistancesClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}

