package com.linkedin.sales.factory.client.common;

import com.linkedin.sales.client.common.MemberRestrictionClient;
import com.linkedin.ucf.client.factory.UcfClientFactory;
import com.linkedin.ucf.viewerinfoutil.factory.ViewerInfoBuilderFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class MemberRestrictionClientFactory extends SimpleSingletonFactory<MemberRestrictionClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesMemberRestrictionClient");
  private static final String UCF_CLIENT_SCOPE = "ucfClient";

  @Import(clazz = UcfClientFactory.class, prefix = UCF_CLIENT_SCOPE)
  @Import(clazz = ViewerInfoBuilderFactory.class)
  @Override
  protected MemberRestrictionClient createInstance(ConfigView view) {
    return new MemberRestrictionClient(
        getBean(UcfClientFactory.class, view.getScope().child(UCF_CLIENT_SCOPE)),
        getBean(ViewerInfoBuilderFactory.class)
    );
  }
}