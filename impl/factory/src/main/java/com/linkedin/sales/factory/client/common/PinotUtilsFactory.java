package com.linkedin.sales.factory.client.common;

import com.linkedin.ibrik.factory.AnalyticsQueryClientFactory;
import com.linkedin.sales.factory.common.DefaultExecutorServiceFactory;
import com.linkedin.sales.service.utils.PinotUtils;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class PinotUtilsFactory extends SimpleSingletonFactory<PinotUtils> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesPinotUtils");

  @Config
  private static class Cfg {
    long timeoutInSeconds = 5;
  }

  @Import(clazz = AnalyticsQueryClientFactory.class)
  @Import(clazz = DefaultExecutorServiceFactory.class)
  @Override
  protected PinotUtils createInstance(ConfigView view) {
    final Cfg cfg = view.fill(Cfg.class);
    return new PinotUtils(
        getBean(AnalyticsQueryClientFactory.class),
        getBean(DefaultExecutorServiceFactory.class),
        cfg.timeoutInSeconds
    );
  }
}
