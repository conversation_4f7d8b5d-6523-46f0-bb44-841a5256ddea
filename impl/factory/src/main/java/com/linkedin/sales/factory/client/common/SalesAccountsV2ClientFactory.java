package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesAccountsV2Client;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesAccountsV2ClientFactory extends SimpleSingletonFactory<SalesAccountsV2Client> {
  private static final Scope SCOPE = Scope.ROOT.child("salesAccountsV2Client");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesAccountsV2Client createInstance(ConfigView view) {
    return new SalesAccountsV2Client(getBean(DefaultParSeqRestClientFactory.class));
  }
}
