package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesContractClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesContractClientFactory extends SimpleSingletonFactory<SalesContractClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesContractClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesContractClient createInstance(ConfigView view) {
    return new SalesContractClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
