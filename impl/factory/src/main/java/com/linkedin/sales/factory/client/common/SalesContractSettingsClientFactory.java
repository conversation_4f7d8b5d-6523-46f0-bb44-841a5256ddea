package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesContractSettingsClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesContractSettingsClientFactory extends SimpleSingletonFactory<SalesContractSettingsClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesContractSettingsClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesContractSettingsClient createInstance(ConfigView view) {
    return new SalesContractSettingsClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
