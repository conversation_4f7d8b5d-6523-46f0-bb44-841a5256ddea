package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesCreditGrantsClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory to create SalesCreditGrantsClient.
 */
public class SalesCreditGrantsClientFactory extends SimpleSingletonFactory<SalesCreditGrantsClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesCreditGrantsClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesCreditGrantsClient createInstance(ConfigView view) {
    return new SalesCreditGrantsClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}