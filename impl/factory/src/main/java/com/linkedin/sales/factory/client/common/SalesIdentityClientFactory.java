package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesIdentityClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesIdentityClientFactory extends SimpleSingletonFactory<SalesIdentityClient> {
  private static final Scope SCOPE = Scope.ROOT.child("salesIdentityClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesIdentityClient createInstance(ConfigView view) {
    return new SalesIdentityClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
