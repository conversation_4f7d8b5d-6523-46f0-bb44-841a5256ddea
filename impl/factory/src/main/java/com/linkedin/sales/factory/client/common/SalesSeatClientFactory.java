package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesSeatClientFactory extends SimpleSingletonFactory<SalesSeatClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesSeatClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesSeatClient createInstance(ConfigView view) {
    return new SalesSeatClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
