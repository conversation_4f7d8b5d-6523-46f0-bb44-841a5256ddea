package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.TorrentClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class TorrentClientFactory extends SimpleSingletonFactory<TorrentClient> {
  private static final Scope SCOPE = Scope.ROOT.child("TorrentClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected TorrentClient createInstance(ConfigView view) {
    return new TorrentClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
