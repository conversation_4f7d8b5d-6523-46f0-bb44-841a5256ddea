package com.linkedin.sales.factory.client.common;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.ds.rest.VectorClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class VectorClientFactory extends SimpleSingletonFactory<VectorClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesVectorClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected VectorClient createInstance(ConfigView view) {
    return new VectorClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
