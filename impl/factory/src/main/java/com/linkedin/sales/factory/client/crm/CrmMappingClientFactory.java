package com.linkedin.sales.factory.client.crm;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.CrmMappingClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class CrmMappingClientFactory extends SimpleSingletonFactory<CrmMappingClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmMappingClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CrmMappingClient createInstance(ConfigView view) {
    return new CrmMappingClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}