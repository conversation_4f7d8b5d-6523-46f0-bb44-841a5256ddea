package com.linkedin.sales.factory.client.crm;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.CrmUserMappingClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory for {@link CrmUserMappingClient}.
 */
public class CrmUserMappingClientFactory extends SimpleSingletonFactory<CrmUserMappingClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmUserMappingClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CrmUserMappingClient createInstance(ConfigView view) {
    return new CrmUserMappingClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}