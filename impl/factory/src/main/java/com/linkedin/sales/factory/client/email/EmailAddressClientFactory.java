package com.linkedin.sales.factory.client.email;

import com.linkedin.parseq.factory.DefaultEngineFactory;
import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.EmailAddressClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class EmailAddressClientFactory extends SimpleSingletonFactory<EmailAddressClient> {
  private static final Scope SCOPE = Scope.ROOT.child("handlesClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Import(clazz = DefaultEngineFactory.class)
  @Override
  protected EmailAddressClient createInstance(ConfigView view) {
    return new EmailAddressClient(
        getBean(DefaultParSeqRestClientFactory.class),
        getBean(DefaultEngineFactory.class)
    );
  }
}
