package com.linkedin.sales.factory.client.email;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.EmailClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class EmailClientFactory extends SimpleSingletonFactory<EmailClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("emailClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected EmailClient createInstance(ConfigView view) {
    return new EmailClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
