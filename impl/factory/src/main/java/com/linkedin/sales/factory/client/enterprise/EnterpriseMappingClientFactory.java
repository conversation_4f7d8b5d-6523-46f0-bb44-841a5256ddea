package com.linkedin.sales.factory.client.enterprise;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class EnterpriseMappingClientFactory extends SimpleSingletonFactory<EnterprisePlatformClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("enterpriseMappingClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected EnterprisePlatformClient createInstance(ConfigView view) {
    return new EnterprisePlatformClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
