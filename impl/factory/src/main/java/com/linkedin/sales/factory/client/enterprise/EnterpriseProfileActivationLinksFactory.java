package com.linkedin.sales.factory.client.enterprise;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.ep.EnterpriseProfileActivationLinksClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class EnterpriseProfileActivationLinksFactory extends SimpleSingletonFactory<EnterpriseProfileActivationLinksClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("enterpriseProfileActivationLinkClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected EnterpriseProfileActivationLinksClient createInstance(ConfigView view) {
    return new EnterpriseProfileActivationLinksClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
