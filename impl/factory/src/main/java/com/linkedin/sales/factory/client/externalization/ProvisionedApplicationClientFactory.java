package com.linkedin.sales.factory.client.externalization;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.externalization.ProvisionedApplicationClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory for {@link ProvisionedApplicationClient}
 * <AUTHOR>
 */
public class ProvisionedApplicationClientFactory extends SimpleSingletonFactory<ProvisionedApplicationClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("provisionedApplicationClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected ProvisionedApplicationClient createInstance(ConfigView view) {
    return new ProvisionedApplicationClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
