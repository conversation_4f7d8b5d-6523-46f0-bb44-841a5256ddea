package com.linkedin.sales.factory.client.externalization;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.externalization.SalesOauthAuthenticationClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesOauthAuthenticationClientFactory extends SimpleSingletonFactory<SalesOauthAuthenticationClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesOauthClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesOauthAuthenticationClient createInstance(ConfigView view) {
    return new SalesOauthAuthenticationClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
