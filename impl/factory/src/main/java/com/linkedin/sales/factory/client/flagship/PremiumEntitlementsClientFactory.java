package com.linkedin.sales.factory.client.flagship;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.flagship.PremiumEntitlementsClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link PremiumEntitlementsClient}
 */
public class PremiumEntitlementsClientFactory extends SimpleSingletonFactory<PremiumEntitlementsClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("premiumEntitlementsClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected PremiumEntitlementsClient createInstance(ConfigView view) {
    return new PremiumEntitlementsClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
