package com.linkedin.sales.factory.client.flagship;

import com.linkedin.grpc.client.factory.DefaultD2ManagedChannelProviderFactory;
import com.linkedin.sales.client.flagship.PremiumUpsellFlowsClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class PremiumUpsellFlowsClientFactory extends SimpleSingletonFactory<PremiumUpsellFlowsClient> {

  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("premiumUpsellFlowsClient");
  @Import(clazz = DefaultD2ManagedChannelProviderFactory.class)
  @Override
  protected PremiumUpsellFlowsClient createInstance(ConfigView view) {
    return new PremiumUpsellFlowsClient(getBean(DefaultD2ManagedChannelProviderFactory.class));
  }
}
