package com.linkedin.sales.factory.client.integration;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link CrmPairingClient}
 * <AUTHOR>
 */
public class CrmPairingClientFactory extends SimpleSingletonFactory<CrmPairingClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmPairingClientFactory");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CrmPairingClient createInstance(ConfigView view) {
    return new CrmPairingClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
