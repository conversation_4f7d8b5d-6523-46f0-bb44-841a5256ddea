package com.linkedin.sales.factory.client.integration;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.integration.CrmSettingClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link CrmSettingClient}
 * <AUTHOR>
 */
public class CrmSettingClientFactory extends SimpleSingletonFactory<CrmSettingClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmSettingClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CrmSettingClient createInstance(ConfigView view) {
    return new CrmSettingClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
