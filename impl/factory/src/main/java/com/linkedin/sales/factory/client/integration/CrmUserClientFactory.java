package com.linkedin.sales.factory.client.integration;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.integration.CrmUserClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory for {@link CrmUserClient}
 */
public class CrmUserClientFactory extends SimpleSingletonFactory<CrmUserClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmUserClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CrmUserClient createInstance(ConfigView view) {
    return new CrmUserClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
