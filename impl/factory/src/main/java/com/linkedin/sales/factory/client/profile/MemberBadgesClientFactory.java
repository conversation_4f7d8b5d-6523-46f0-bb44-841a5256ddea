package com.linkedin.sales.factory.client.profile;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.messaging.MemberBadgesClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class MemberBadgesClientFactory extends SimpleSingletonFactory<MemberBadgesClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("memberBadgesClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected MemberBadgesClient createInstance(ConfigView view) {
    return new MemberBadgesClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
