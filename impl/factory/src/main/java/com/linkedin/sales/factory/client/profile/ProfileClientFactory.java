package com.linkedin.sales.factory.client.profile;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.ds.rest.ProfileClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class ProfileClientFactory extends SimpleSingletonFactory<ProfileClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesProfileClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected ProfileClient createInstance(ConfigView view) {
    return new ProfileClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
