package com.linkedin.sales.factory.client.pymk;

import com.linkedin.client.factory.VeniceStoreClientFactoryFactory;
import com.linkedin.sales.client.pymk.SalesPymkVeniceClient;
import com.linkedin.sales.factory.monitoring.CounterMetricsSensorFactory;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsKey;
import com.linkedin.sales.pymk.PYMKFreemiumRecommendationsValue;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import com.linkedin.venice.client.store.AvroSpecificStoreClient;


public class SalesPymkVeniceClientFactory extends SimpleSingletonFactory<SalesPymkVeniceClient> {
  private static final Scope SCOPE = Scope.ROOT.child("salesPymkVeniceClient");
  private static final String VENICE_CLIENT = "SalesPymkVeniceClient";
  private static final String VENICE_STORE_NAME = "pymk_freemium_recommendations";

  @Config
  private static class Cfg {
    private static int timeoutMs = 1000;
  }

  @Import(clazz = CounterMetricsSensorFactory.class)
  @Import(clazz = VeniceStoreClientFactoryFactory.class, prefix = VENICE_CLIENT)
  @Override
  protected SalesPymkVeniceClient createInstance(ConfigView view) {
    AvroSpecificStoreClient<PYMKFreemiumRecommendationsKey, PYMKFreemiumRecommendationsValue> veniceStoreClient =
        getBean(VeniceStoreClientFactoryFactory.class, view.getScope().child(VENICE_CLIENT))
            .getAndStartAvroSpecificStoreClient(VENICE_STORE_NAME, PYMKFreemiumRecommendationsValue.class);
    return new SalesPymkVeniceClient(veniceStoreClient, Cfg.timeoutMs);
  }

}
