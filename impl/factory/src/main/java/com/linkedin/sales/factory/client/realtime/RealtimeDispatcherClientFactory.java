package com.linkedin.sales.factory.client.realtime;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.realtime.RealtimeDispatcherClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class RealtimeDispatcherClientFactory extends SimpleSingletonFactory<RealtimeDispatcherClient> {
  private static final Scope SCOPE = Scope.ROOT.child("realtimeDispatcherClient");
  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected RealtimeDispatcherClient createInstance(ConfigView view) {
    return new RealtimeDispatcherClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
