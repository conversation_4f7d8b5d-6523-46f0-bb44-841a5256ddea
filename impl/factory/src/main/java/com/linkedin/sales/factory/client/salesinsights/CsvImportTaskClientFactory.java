package com.linkedin.sales.factory.client.salesinsights;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.salesinsights.CsvImportTaskClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class to create {@link CsvImportTaskClient}
 */
public class CsvImportTaskClientFactory extends SimpleSingletonFactory<CsvImportTaskClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("csvImportTaskClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected CsvImportTaskClient createInstance(ConfigView view) {
    return new CsvImportTaskClient(
        getBean(DefaultParSeqRestClientFactory.class));
  }
}