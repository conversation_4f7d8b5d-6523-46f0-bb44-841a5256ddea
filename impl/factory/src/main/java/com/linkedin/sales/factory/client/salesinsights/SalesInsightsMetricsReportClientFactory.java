package com.linkedin.sales.factory.client.salesinsights;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.salesinsights.SalesInsightsMetricsReportClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class to create {@link com.linkedin.sales.client.salesinsights.SalesInsightsMetricsReportClient}
 */
public class SalesInsightsMetricsReportClientFactory extends SimpleSingletonFactory<SalesInsightsMetricsReportClient> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesInsightsMetricsReportClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesInsightsMetricsReportClient createInstance(ConfigView view) {
    return new SalesInsightsMetricsReportClient(
        getBean(DefaultParSeqRestClientFactory.class));
  }
}