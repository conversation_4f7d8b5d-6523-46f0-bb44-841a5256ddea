package com.linkedin.sales.factory.client.seattransfer;

import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class SalesSeatTransferCopyAssociationsClientFactory extends SimpleSingletonFactory<SalesSeatTransferCopyAssociationsClient> {
  private static final Scope SCOPE = Scope.ROOT.child("salesSeatTransferCopyAssociationsClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesSeatTransferCopyAssociationsClient createInstance(ConfigView view) {
    return new SalesSeatTransferCopyAssociationsClient(
        getBean(DefaultParSeqRestClientFactory.class)
    );
  }
}
