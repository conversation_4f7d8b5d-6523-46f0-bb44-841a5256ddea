package com.linkedin.sales.factory.client.seattransfer;

import com.linkedin.restli.client.factory.DefaultParSeqRestClientFactory;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferRequestsClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesSeatTransferRequestsClientFactory extends SimpleSingletonFactory<SalesSeatTransferRequestsClient> {
  private static final Scope SCOPE = Scope.ROOT.child("salesSeatTransferRequestsClient");

  @Import(clazz = DefaultParSeqRestClientFactory.class)
  @Override
  protected SalesSeatTransferRequestsClient createInstance(ConfigView view) {
    return new SalesSeatTransferRequestsClient(getBean(DefaultParSeqRestClientFactory.class));
  }
}
