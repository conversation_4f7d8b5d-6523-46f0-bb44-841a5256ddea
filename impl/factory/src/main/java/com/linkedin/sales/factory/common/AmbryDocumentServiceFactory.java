package com.linkedin.sales.factory.common;

import com.linkedin.ambry.client.DefaultAsyncAmbryClientFactory;
import com.linkedin.omni.utils.ambry.AmbryDocumentService;
import com.linkedin.util.factory.SimpleFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


public class AmbryDocumentServiceFactory extends SimpleFactory<AmbryDocumentService> {

  @Config
  public static class Cfg {
    // Ambry account name.
    @Required
    public String ambryAccountName;

    // Ambry container name.
    @Required
    public String ambryContainerName;

    // Mime type for the container
    @Required
    public String mimeType;

    // TTL for the uploaded file
    public Long _ttlInSeconds = 0L;
  }

  @Override
  @Import(clazz = DefaultAsyncAmbryClientFactory.class)
  protected AmbryDocumentService createInstance(ConfigView view) {
    Cfg config = view.fill(Cfg.class);
    return new AmbryDocumentService(getBean(DefaultAsyncAmbryClientFactory.class), config.ambryAccountName,
        config.ambryContainerName, config.mimeType, config._ttlInSeconds);
  }
}