package com.linkedin.sales.factory.common;

import com.linkedin.alcantara.interop.InteropServerBridgeLoader;
import com.linkedin.autortf.injector.EpsilonInjectorFactory;
import com.linkedin.container.logging.EventBusAppenderFactory;
import com.linkedin.container.logging.log4j2.InLogAppendersFactory;
import com.linkedin.container.logging.log4j2.Log4j2LogEventFactoryFactory;
import com.linkedin.grpc.interop.LocalRestClient;
import com.linkedin.grpc.interop.SkipFilterUtil;
import com.linkedin.grpc.server.factory.DefaultGrpcServerFactory;
import com.linkedin.parseq.factory.DefaultEngineFactory;
import com.linkedin.restli.server.RestLiServer;
import com.linkedin.restli.server.factories.SettableBeanProvider;
import com.linkedin.restli.server.factory.RestLiServerFactory;
import com.linkedin.restli.server.factory.RestliServletFactory;
import com.linkedin.sales.factory.services.autoprospecting.LeadFindingRunQueueServiceFactory;
import com.linkedin.sales.factory.services.autoprospecting.LeadFindingRunServiceFactory;
import com.linkedin.sales.factory.services.autoprospecting.CampaignServiceFactory;
import com.linkedin.sales.factory.services.autoprospecting.SearchCriteriaServiceFactory;
import com.linkedin.sales.factory.services.autoprospecting.LeadFindingRunLeadServiceFactory;
import com.linkedin.sales.factory.services.flagship.SalesUpsellFlowConfigServiceFactory;
import com.linkedin.sales.factory.services.list.SalesAdminCsvImportServiceFactory;
import com.linkedin.sales.factory.services.warmintros.WarmIntroRecommendationServiceFactory;
import com.linkedin.util.factory.Generator;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.lifecycle.StartupAware;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.grpc.Server;
import java.util.ArrayList;

/**
 * The application boot listener.
 *
 * This class needs to extend an existing Jetty boot listener that configures and creates a {@link RestLiServer}
 * bean instance.
 *
 * This is because a bridged gRPC/Rest.li service requires the {@link RestLiServer} to be properly initialized
 * first with user-defined configs before it can be reused as the server for in-process (bridge) calls.
 */
public class BridgedGrpcServiceBootListener extends OffspringBootListener {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("bridgedGrpcServiceBootListener");

  private static final String RESTLI_SERVER_PREFIX = "restliServlet.dispatcher.restLiServer";

  @Import(clazz = DefaultGrpcServerFactory.class)
  @ConfigOverride(clazz = DefaultGrpcServerFactory.class)
  @Import(
      clazz = RestLiServerFactory.class,
      prefix = RESTLI_SERVER_PREFIX
  )
  @Import(
      clazz = EpsilonInjectorFactory.class
  )
  @ConfigOverride(
      clazz = EpsilonInjectorFactory.class
  )
  private static void configureGrpc(final Generator generator) {
    SettableBeanProvider grpcBeanProvider = registerGrpcBeans(generator);
    DefaultGrpcServerFactory.Cfg grpcCfg = new DefaultGrpcServerFactory.Cfg();
    grpcCfg.beanProvider = grpcBeanProvider;
    final Server grpcServer = generator.configOverrideAndGetBean(DefaultGrpcServerFactory.class, grpcCfg);

    // Set up Epsilon for gRPC.
    final EpsilonInjectorFactory.Cfg epsilonInjectorCfg = new EpsilonInjectorFactory.Cfg();
    epsilonInjectorCfg.grpcServer = grpcServer;
    generator.configOverrideAndGetBean(EpsilonInjectorFactory.class, epsilonInjectorCfg);

    final RestLiServer restLiServer = generator.getBean(RestLiServerFactory.class, Scope.ROOT.child(RESTLI_SERVER_PREFIX));
    LocalRestClient.SHARED_INSTANCE.setRestLiServer(restLiServer);

    // Wrap the default in-process skippable filters.
    SkipFilterUtil.skipFiltersForInProcess(restLiServer.getRestliConfig().getFilters());

    // Warm up interop bridges and SI gRPC classes.
    generator.registerLifeCycleAware(
        (StartupAware)
            () -> {
              InteropServerBridgeLoader.warmup();
            },
        SCOPE);
  }

  @Import(
      clazz = Log4j2LogEventFactoryFactory.class
  )
  @Import(
      clazz = InLogAppendersFactory.class
  )
  @Import(
      clazz = EventBusAppenderFactory.class
  )
  @Override
  public void onBoot(final Generator generator) throws Exception {
    super.onBoot(generator);

    // Reinstate the container facilities that gRPC requires.
    generator.getBean(Log4j2LogEventFactoryFactory.class);
    generator.getBean(EventBusAppenderFactory.class);
    generator.getBean(InLogAppendersFactory.class);

    configureGrpc(generator);
  }

  @SuppressFBWarnings("RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE")
  @Override
  public RestliServletFactory.Overrides createOverrides(final Generator generator) {
    RestliServletFactory.Overrides overrides = super.createOverrides(generator);
    if (overrides == null) {
      overrides = new RestliServletFactory.Overrides();
    }
    if (overrides.resourceDefinitionListeners == null) {
      overrides.resourceDefinitionListeners = new ArrayList<>();
    }
    overrides.resourceDefinitionListeners.add(LocalRestClient.SHARED_INSTANCE);
    return overrides;
  }

  /**
   * Initialize injected beans for GRPC API services
   * You must add the corresponding factory here for any @Import annotations used in a GRPC API service
   */
  @Import(clazz = DefaultEngineFactory.class)
  @Import(clazz = SalesUpsellFlowConfigServiceFactory.class)
  @Import(clazz = SearchCriteriaServiceFactory.class)
  @Import(clazz = CampaignServiceFactory.class)
  @Import(clazz = SalesAdminCsvImportServiceFactory.class)
  @Import(clazz = LeadFindingRunLeadServiceFactory.class)
  @Import(clazz = LeadFindingRunServiceFactory.class)
  @Import(clazz = LeadFindingRunQueueServiceFactory.class)
  @Import(clazz = WarmIntroRecommendationServiceFactory.class)
  private static SettableBeanProvider registerGrpcBeans(Generator generator) {
    SettableBeanProvider provider = new SettableBeanProvider();
    provider.registerBean("parseqEngine",
        generator.getBean(DefaultEngineFactory.class));
    provider.registerBean("salesUpsellFlowConfigService",
        generator.getBean(SalesUpsellFlowConfigServiceFactory.class));
    provider.registerBean("searchCriteriaService",
            generator.getBean(SearchCriteriaServiceFactory.class));
    provider.registerBean("campaignService",
            generator.getBean(CampaignServiceFactory.class));
    provider.registerBean("salesAdminCsvImportService",
        generator.getBean(SalesAdminCsvImportServiceFactory.class));
    provider.registerBean("leadFindingRunLeadService",
        generator.getBean(LeadFindingRunLeadServiceFactory.class));
    provider.registerBean("LeadFindingRunService",
        generator.getBean(LeadFindingRunServiceFactory.class));
    provider.registerBean("LeadFindingRunQueueService",
        generator.getBean(LeadFindingRunQueueServiceFactory.class));
    provider.registerBean("warmIntroRecommendationService",
        generator.getBean(WarmIntroRecommendationServiceFactory.class));
    return provider;
  }
}
