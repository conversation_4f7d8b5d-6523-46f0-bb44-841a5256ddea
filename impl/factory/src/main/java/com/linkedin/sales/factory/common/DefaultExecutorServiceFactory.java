package com.linkedin.sales.factory.common;

import com.linkedin.container.executor.factory.ExecutorServiceFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import java.util.concurrent.ExecutorService;


/**
 * Factory to create instances of {@link ExecutorService}
 */
public class DefaultExecutorServiceFactory extends SimpleSingletonFactory<ExecutorService> {

  private static final Scope SCOPE = Scope.ROOT.child("executorService");
  private static final String PREFIX_EXECUTOR_SERVICE = "executorService";

  @Import(clazz = ExecutorServiceFactory.class, prefix = PREFIX_EXECUTOR_SERVICE)
  @Override
  protected ExecutorService createInstance(ConfigView view) {
    return getBean(ExecutorServiceFactory.class, view.getScope().child(PREFIX_EXECUTOR_SERVICE));
  }
}
