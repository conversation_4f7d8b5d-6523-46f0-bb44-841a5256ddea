package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Factory to create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssAutoFinderEspressoClientFactory}
 */
public class LssAutoFinderParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  private static final Scope SCOPE = Scope.ROOT.child("lssAutoFinderParSeqEspressoClient");
  @Import(clazz = LssAutoFinderEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssAutoFinderEspressoClientFactory.class);
  }
}
