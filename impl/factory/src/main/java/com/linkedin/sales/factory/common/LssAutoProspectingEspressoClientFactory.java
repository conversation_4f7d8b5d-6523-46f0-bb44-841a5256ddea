package com.linkedin.sales.factory.common;

import com.linkedin.util.factory.Scope;


/**
 * Factory to create {@link com.linkedin.espresso.client.EspressoClient} for LssAutoProspecting db in ei and prod.
 */
public class LssAutoProspectingEspressoClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssAutoProspecting");
}
