package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Factory to create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssAutoProspectingEspressoClientFactory}
 */
public class LssAutoProspectingParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  private static final Scope SCOPE = Scope.ROOT.child("lssAutoProspectingParSeqEspressoClient");

  @Import(clazz = LssAutoProspectingEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssAutoProspectingEspressoClientFactory.class);
  }
}
