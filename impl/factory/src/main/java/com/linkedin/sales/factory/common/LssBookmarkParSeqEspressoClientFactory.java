package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Created by hchang at 12/02/2020
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 *  * {@link LssBookmarkEspressoClientFactory}
 */
public class LssBookmarkParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssBookmarkParSeqEspressoClient");

  @Import(clazz = LssBookmarkEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssBookmarkEspressoClientFactory.class);
  }
}
