package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssCoachEspressoClientFactory}
 */
public class LssCoachParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssCoachParSeqEspressoClient");

  @Import(clazz = LssCoachEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssCoachEspressoClientFactory.class);
  }
}
