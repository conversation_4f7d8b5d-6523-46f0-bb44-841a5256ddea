package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;


/**
 * This is to help create {@link EspressoClient} in for LssEntityView DB in ei and prod.
 */

public class LssEntityViewEspressoClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssEntityView");
}
