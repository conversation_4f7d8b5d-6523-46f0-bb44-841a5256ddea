package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link ParSeqEspressoClient} using
 * {@link LssEntityViewEspressoClientFactory}
 */
public class LssEntityViewParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssEntityViewParSeqEspressoClient");

  @Import(clazz = LssEntityViewEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssEntityViewEspressoClientFactory.class);
  }
}
