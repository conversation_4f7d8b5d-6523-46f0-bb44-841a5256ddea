package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssLeadExtendedInfoEspressoClientFactory}
 */
public class LssLeadExtendedInfoParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("LssLeadExtendedInfoParSeqEspressoClient");

  @Import(clazz = LssLeadExtendedInfoEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssLeadExtendedInfoEspressoClientFactory.class);
  }
}
