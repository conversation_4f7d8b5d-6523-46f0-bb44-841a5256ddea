package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;

/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssNoteEspressoClientFactory}
 */
public class LssNoteParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssNoteParSeqEspressoClient");

  @Import(clazz = LssNoteEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssNoteEspressoClientFactory.class);
  }
}
