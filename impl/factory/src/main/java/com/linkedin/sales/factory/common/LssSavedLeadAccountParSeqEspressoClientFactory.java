package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssSavedLeadAccountEspressoClientFactory}
 */

public class LssSavedLeadAccountParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssSavedLeadAccountParSeqEspressoClient");

  @Import(clazz = LssSavedLeadAccountEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssSavedLeadAccountEspressoClientFactory.class);
  }
}
