package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;

/**
 * Created by <PERSON><PERSON><PERSON> at 11/10/2019
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssSharingEspressoClientFactory}
 */

public class LssSharingParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssSharingParSeqEspressoClient");

  @Import(clazz = LssSharingEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssSharingEspressoClientFactory.class);
  }
}
