package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Factory to create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link LssWarmIntrosEspressoClientFactory}
 */
public class LssWarmIntrosParSeqEspressoClientFactory extends SalesParSeqEspressoBaseClientFactory {
  private static final Scope SCOPE = Scope.ROOT.child("lssWarmIntrosParSeqEspressoClient");

  @Import(clazz = LssWarmIntrosEspressoClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(LssWarmIntrosEspressoClientFactory.class);
  }
}
