package com.linkedin.sales.factory.common;

import com.google.common.collect.ImmutableList;
import com.linkedin.autortf.injector.AutoRtfInjectorFactory;
import com.linkedin.healthcheck.HealthCheckFactory;
import com.linkedin.healthcheck.pub.SensorRegistry;
import com.linkedin.parseq.Engine;
import com.linkedin.resourceidentity.common.UrnConstants;
import com.linkedin.restli.server.factories.SettableBeanProvider;
import com.linkedin.restli.server.factory.RestliBootListener;
import com.linkedin.restli.server.factory.RestliServletFactory;
import com.linkedin.restli.server.validation.RestLiValidationFilter;
import com.linkedin.restligateway.util.GatewayCallerFinderFactory;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.factory.services.analytics.SalesAnalyticsExportJobServiceV2Factory;
import com.linkedin.sales.factory.services.buyerengagement.ContractSellerIdentityServiceFactory;
import com.linkedin.sales.factory.services.seattransfer.LssSeatTransferActionsServiceFactory;
import com.linkedin.sales.service.SalesAnalyticsExportJobServiceV2;
import com.linkedin.sales.service.buyerengagement.ContractSellerIdentityService;
import com.linkedin.sales.service.entitymappings.ManualEmailToMemberMappingService;
import com.linkedin.sales.factory.client.enterprise.EnterpriseMappingClientFactory;
import com.linkedin.sales.factory.monitoring.CounterMetricsSensorFactory;
import com.linkedin.sales.factory.monitoring.GaugeMetricsSensorFactory;
import com.linkedin.sales.factory.services.acl.SalesRealtimeAuthorizationServiceFactory;
import com.linkedin.sales.factory.services.activities.SalesActivityTotalsJobServiceFactory;
import com.linkedin.sales.factory.services.alerts.SalesAlertsServiceFactory;
import com.linkedin.sales.factory.services.autofinder.AccountPlaysMetadataServiceFactory;
import com.linkedin.sales.factory.services.autofinder.AccountPlayServiceFactory;
import com.linkedin.sales.factory.services.buyerengagement.ProductCategoryInterestServiceFactory;
import com.linkedin.sales.factory.services.analytics.SalesAnalyticsExportJobServiceFactory;
import com.linkedin.sales.factory.services.authentication.SalesContractAuthorizationFilterFactory;
import com.linkedin.sales.factory.services.authentication.SalesOauthAuthenticationServiceFactory;
import com.linkedin.sales.factory.services.bookmark.SalesBookmarkServiceFactory;
import com.linkedin.sales.factory.services.buyerengagement.BuyerEngagementServiceFactory;
import com.linkedin.sales.factory.services.buyerengagement.BuyerIntentServiceFactory;
import com.linkedin.sales.factory.services.buyerengagement.SalesSellerIdentityServiceFactory;
import com.linkedin.sales.factory.services.buyerengagement.SellerIdentityProductHelperServiceFactory;
import com.linkedin.sales.factory.services.coach.SalesCoachConversationServiceFactory;
import com.linkedin.sales.factory.services.colleagues.SalesColleaguesServiceFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.common.SalesContractServiceFactory;
import com.linkedin.sales.factory.services.customfilterview.SalesFilterLayoutServiceFactory;
import com.linkedin.sales.factory.services.email.SalesNavigatorEmailServiceFactory;
import com.linkedin.sales.factory.services.enterprise.CrmUserOnboardingBulkActionServiceFactory;
import com.linkedin.sales.factory.services.enterprise.SalesInviteRegisterUrlServiceFactory;
import com.linkedin.sales.factory.services.entitymappings.ManualEmailToMemberMappingServiceFactory;
import com.linkedin.sales.factory.services.entityviews.SalesProfileViewsServiceFactory;
import com.linkedin.sales.factory.services.flagship.LeadPositionChangeServiceFactory;
import com.linkedin.sales.factory.services.flagship.SalesCommunicationPluginServiceFactory;
import com.linkedin.sales.factory.services.flagship.SalesPYMKRecommendationsServiceFactory;
import com.linkedin.sales.factory.services.integration.CrmDataValidationExportJobServiceFactory;
import com.linkedin.sales.factory.services.lbep.EnterpriseEmailPluginsServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesAccountsServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesLeadAccountAssociationServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesLeadAccountMetadataServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesLeadEditableContactInfoServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesLeadProfileUnlockHistoryInfoFactory;
import com.linkedin.sales.factory.services.leadaccount.SavedLeadServiceFactory;
import com.linkedin.sales.factory.services.list.SalesAccountToListMappingServiceFactory;
import com.linkedin.sales.factory.services.list.SalesDefaultListServiceFactory;
import com.linkedin.sales.factory.services.list.SalesListCsvImportServiceFactory;
import com.linkedin.sales.factory.services.list.SalesListEntityServiceFactory;
import com.linkedin.sales.factory.services.list.SalesListEntitySummaryServiceFactory;
import com.linkedin.sales.factory.services.list.SalesListServiceFactory;
import com.linkedin.sales.factory.services.list.SalesRelationshipMapChangeLogServiceFactory;
import com.linkedin.sales.factory.services.note.SalesNoteServiceFactory;
import com.linkedin.sales.factory.services.profile.ProfileVisibilityServiceFactory;
import com.linkedin.sales.factory.services.profile.SalesNavigatorProfileAssociationServiceFactory;
import com.linkedin.sales.factory.services.profile.SalesNavigatorSaveLeadServiceFactory;
import com.linkedin.sales.factory.services.profile.SalesNavigatorSeatDataExtensionServiceFactory;
import com.linkedin.sales.factory.services.recentviews.SalesRecentActivitiesServiceFactory;
import com.linkedin.sales.factory.services.recentviews.SalesRecentSearchesServiceFactory;
import com.linkedin.sales.factory.services.customfilterview.SalesPinnedFiltersServiceFactory;
import com.linkedin.sales.factory.services.settings.SalesMobileSettingsServiceFactory;
import com.linkedin.sales.factory.services.settings.SalesSeatSettingsServiceFactory;
import com.linkedin.sales.factory.services.sharing.SalesSharingServiceFactory;
import com.linkedin.sales.factory.services.subscriptionbenefits.SalesSubscriptionBenefitsServiceFactory;
import com.linkedin.sales.rest.filter.SalesContractAuthorizationFilter;
import com.linkedin.policy.enforcerv2.factory.restli.PolicyEnforcementFilterFactory;
import com.linkedin.policy.enforcerv2.restli.PolicyEnforcementFilter;
import com.linkedin.sales.service.CrmUserOnboardingBulkActionService;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.ProfileVisibilityService;
import com.linkedin.sales.service.SalesActivityTotalsJobService;
import com.linkedin.sales.service.SalesAnalyticsExportJobService;
import com.linkedin.sales.service.SalesColleaguesService;
import com.linkedin.sales.service.SalesContractService;
import com.linkedin.sales.service.SalesCryptoService;
import com.linkedin.sales.service.SalesFilterLayoutService;
import com.linkedin.sales.service.SalesInviteRegisterUrlService;
import com.linkedin.sales.service.SalesNavigatorProfileAssociationService;
import com.linkedin.sales.service.SalesNavigatorSaveLeadService;
import com.linkedin.sales.service.SalesNavigatorSeatDataExtensionService;
import com.linkedin.sales.service.SalesOauthAuthenticationService;
import com.linkedin.sales.service.SalesPinnedFiltersService;
import com.linkedin.sales.service.SalesRecentActivitiesService;
import com.linkedin.sales.service.SalesRecentSearchesService;
import com.linkedin.sales.service.SalesSubscriptionBenefitsService;
import com.linkedin.sales.service.alerts.SalesAlertsService;
import com.linkedin.sales.service.buyerengagement.ProductCategoryInterestService;
import com.linkedin.sales.service.buyerengagement.BuyerEngagementService;
import com.linkedin.sales.service.entityviews.SalesProfileViewsService;
import com.linkedin.sales.service.flagship.SalesCommunicationPluginService;
import com.linkedin.sales.service.integration.CrmDataValidationExportJobService;
import com.linkedin.sales.service.leadaccount.SalesAccountsService;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountMetadataService;
import com.linkedin.sales.service.leadaccount.SalesLeadEditableContactInfoService;
import com.linkedin.sales.service.leadaccount.SalesLeadProfileUnlockInfoService;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.service.list.SalesListCsvImportService;
import com.linkedin.sales.service.list.SalesListEntityService;
import com.linkedin.sales.service.list.SalesListEntitySummaryService;
import com.linkedin.sales.service.list.SalesListService;
import com.linkedin.sales.service.note.SalesNoteService;
import com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService;
import com.linkedin.sales.service.settings.SalesMobileSettingsService;
import com.linkedin.sales.service.settings.SalesSeatSettingsService;
import com.linkedin.sales.service.sharing.SalesSharingService;
import com.linkedin.tracker.producer.TrackingProducer;
import com.linkedin.util.factory.Generator;
import com.linkedin.util.factory.annotations.Import;
import java.util.Collections;


/**
 * Offspring boot listener to initialize beans injected into the restli resource
 * and register the sensor.
 * Boot listeners of restli server must extend RestliBootListener
 */
public class OffspringBootListener extends RestliBootListener {

  // List all imports here
  @Import(clazz = AutoRtfInjectorFactory.class)
  @Import(clazz = PolicyEnforcementFilterFactory.class)
  @Override
  protected RestliServletFactory.Overrides createOverrides(final Generator generator) {

    /* AutoRtfInjectorFactory needs to be called BEFORE any Offspring factories are called, to ensure all R2 and D2
    clients used by the service have autortf filters as part of their filter chain, if they have autortf enabled
    */
    generator.getBean(AutoRtfInjectorFactory.class);

    // if any injected beans are needed in the rest.li resource, they must be registered here
    final SettableBeanProvider provider = new SettableBeanProvider();

    RestLiValidationFilter restLiInputValidationFilter = new RestLiValidationFilter(Collections.singletonList(
        UrnConstants.URN_LOCATION));
    SalesContractAuthorizationFilter salesContractAuthorizationFilter = generator.getBean(SalesContractAuthorizationFilterFactory.class);
    PolicyEnforcementFilter policyEnforcementFilter = generator.getBean(PolicyEnforcementFilterFactory.class);

    // override with the bean provider
    RestliServletFactory.Overrides restliServletOverrides = new RestliServletFactory.Overrides();
    restliServletOverrides.beanProvider = provider;
    restliServletOverrides.customRestLiFilters =
        ImmutableList.of(
            restLiInputValidationFilter,
            salesContractAuthorizationFilter,
            policyEnforcementFilter);
    registerServices(generator, provider);
    registerSensors(generator);
    return restliServletOverrides;
  }

  /**
   * Method for instantiating and injecting all services that are used in resources.
   */
  @Import(clazz = GatewayCallerFinderFactory.class)
  @Import(clazz = SalesNavigatorEmailServiceFactory.class)
  @Import(clazz = SalesNavigatorProfileAssociationServiceFactory.class)
  @Import(clazz = SalesContractAuthorizationFilterFactory.class)
  @Import(clazz = SalesContractServiceFactory.class)
  @Import(clazz = SalesCryptoServiceFactory.class)
  @Import(clazz = SalesAnalyticsExportJobServiceFactory.class)
  @Import(clazz = SalesAnalyticsExportJobServiceV2Factory.class)
  @Import(clazz = SalesNavigatorSaveLeadServiceFactory.class)
  @Import(clazz = SalesActivityTotalsJobServiceFactory.class)
  @Import(clazz = ParSeqEngineFactory.class)
  @Import(clazz = SingletonTrackingProducerFactory.class)
  @Import(clazz = ProfileVisibilityServiceFactory.class)
  @Import(clazz = SalesOauthAuthenticationServiceFactory.class)
  @Import(clazz = SalesListEntityServiceFactory.class)
  @Import(clazz = CrmUserOnboardingBulkActionServiceFactory.class)
  @Import(clazz = SalesListEntitySummaryServiceFactory.class)
  @Import(clazz = SalesListServiceFactory.class)
  @Import(clazz = SalesRecentActivitiesServiceFactory.class)
  @Import(clazz = SalesRecentSearchesServiceFactory.class)
  @Import(clazz = SalesMobileSettingsServiceFactory.class)
  @Import(clazz = SalesSeatSettingsServiceFactory.class)
  @Import(clazz = SalesSharingServiceFactory.class)
  @Import(clazz = SalesInviteRegisterUrlServiceFactory.class)
  @Import(clazz = CrmDataValidationExportJobServiceFactory.class)
  @Import(clazz = SalesColleaguesServiceFactory.class)
  @Import(clazz = SalesLeadAccountAssociationServiceFactory.class)
  @Import(clazz = SalesLeadAccountMetadataServiceFactory.class)
  @Import(clazz = SalesAccountsServiceFactory.class)
  @Import(clazz = SalesLeadEditableContactInfoServiceFactory.class)
  @Import(clazz = SalesLeadProfileUnlockHistoryInfoFactory.class)
  @Import(clazz = SalesAlertsServiceFactory.class)
  @Import(clazz = ProductCategoryInterestServiceFactory.class)
  @Import(clazz = SalesNoteServiceFactory.class)
  @Import(clazz = ManualEmailToMemberMappingServiceFactory.class)
  @Import(clazz = SavedLeadServiceFactory.class)
  @Import(clazz = BuyerEngagementServiceFactory.class)
  @Import(clazz = SalesListCsvImportServiceFactory.class)
  @Import(clazz = SalesNavigatorSeatDataExtensionServiceFactory.class)
  @Import(clazz = SalesAccountToListMappingServiceFactory.class)
  @Import(clazz = SalesBookmarkServiceFactory.class)
  @Import(clazz = BuyerIntentServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = EnterpriseMappingClientFactory.class)
  @Import(clazz = LeadPositionChangeServiceFactory.class)
  @Import(clazz = SalesSubscriptionBenefitsServiceFactory.class)
  @Import(clazz = SalesProfileViewsServiceFactory.class)
  @Import(clazz = SalesFilterLayoutServiceFactory.class)
  @Import(clazz = SalesPinnedFiltersServiceFactory.class)
  @Import(clazz = SalesDefaultListServiceFactory.class)
  @Import(clazz = SalesRelationshipMapChangeLogServiceFactory.class)
  @Import(clazz = SalesCoachConversationServiceFactory.class)
  @Import(clazz = SalesPYMKRecommendationsServiceFactory.class)
  @Import(clazz = SalesRealtimeAuthorizationServiceFactory.class)
  @Import(clazz = AccountPlaysMetadataServiceFactory.class)
  @Import(clazz = SalesSellerIdentityServiceFactory.class)
  @Import(clazz = SellerIdentityProductHelperServiceFactory.class)
  @Import(clazz = EnterpriseEmailPluginsServiceFactory.class)
  @Import(clazz = AccountPlayServiceFactory.class)
  @Import(clazz = SalesCommunicationPluginServiceFactory.class)
  @Import(clazz = ContractSellerIdentityServiceFactory.class)
  @Import(clazz = LssSeatTransferActionsServiceFactory.class)
  public void registerServices(Generator generator, final SettableBeanProvider provider) {
    provider.registerBean(GatewayCallerFinderFactory.class.getName(),
        generator.getBean(GatewayCallerFinderFactory.class));
    provider.registerBean(SalesNavigatorEmailServiceFactory.class.getName(),
        generator.getBean(SalesNavigatorEmailServiceFactory.class));
    provider.registerBean(SalesNavigatorProfileAssociationService.class.getName(),
        generator.getBean(SalesNavigatorProfileAssociationServiceFactory.class));
    provider.registerBean(SalesContractService.class.getName(),
        generator.getBean(SalesContractServiceFactory.class));
    provider.registerBean(SalesCryptoService.class.getName(),
        generator.getBean(SalesCryptoServiceFactory.class));
    provider.registerBean(SalesAnalyticsExportJobService.class.getName(),
        generator.getBean(SalesAnalyticsExportJobServiceFactory.class));
    provider.registerBean(SalesAnalyticsExportJobServiceV2.class.getName(),
        generator.getBean(SalesAnalyticsExportJobServiceV2Factory.class));
    provider.registerBean(SalesActivityTotalsJobService.class.getName(),
        generator.getBean(SalesActivityTotalsJobServiceFactory.class));
    provider.registerBean(SalesNavigatorSaveLeadService.class.getName(),
        generator.getBean(SalesNavigatorSaveLeadServiceFactory.class));
    provider.registerBean(SalesNavigatorSeatDataExtensionService.class.getName(),
        generator.getBean(SalesNavigatorSeatDataExtensionServiceFactory.class));
    provider.registerBean(ProfileVisibilityService.class.getName(),
        generator.getBean(ProfileVisibilityServiceFactory.class));
    provider.registerBean(Engine.class.getName(),
        generator.getBean(ParSeqEngineFactory.class));
    provider.registerBean(TrackingProducer.class.getName(),
        generator.getBean(SingletonTrackingProducerFactory.class));
    provider.registerBean(SalesOauthAuthenticationService.class.getName(),
        generator.getBean(SalesOauthAuthenticationServiceFactory.class));
    provider.registerBean(SalesListEntityService.class.getName(),
        generator.getBean(SalesListEntityServiceFactory.class));
    provider.registerBean(CrmUserOnboardingBulkActionService.class.getName(),
        generator.getBean(CrmUserOnboardingBulkActionServiceFactory.class));
    provider.registerBean(SalesListEntitySummaryService.class.getName(),
        generator.getBean(SalesListEntitySummaryServiceFactory.class));
    provider.registerBean(SalesListService.class.getName(),
        generator.getBean(SalesListServiceFactory.class));
    provider.registerBean(SalesMobileSettingsService.class.getName(),
        generator.getBean(SalesMobileSettingsServiceFactory.class));
    provider.registerBean(SalesSeatSettingsService.class.getName(),
        generator.getBean(SalesSeatSettingsServiceFactory.class));
    provider.registerBean(SalesSharingService.class.getName(),
        generator.getBean(SalesSharingServiceFactory.class));
    provider.registerBean(SalesRecentActivitiesService.class.getName(),
        generator.getBean(SalesRecentActivitiesServiceFactory.class));
    provider.registerBean(SalesRecentSearchesService.class.getName(),
        generator.getBean(SalesRecentSearchesServiceFactory.class));
    provider.registerBean(SalesInviteRegisterUrlService.class.getName(),
        generator.getBean(SalesInviteRegisterUrlServiceFactory.class));
    provider.registerBean(CrmDataValidationExportJobService.class.getName(),
        generator.getBean(CrmDataValidationExportJobServiceFactory.class));
    provider.registerBean(SalesColleaguesService.class.getName(),
        generator.getBean(SalesColleaguesServiceFactory.class));
    provider.registerBean(SalesLeadAccountAssociationService.class.getName(),
        generator.getBean(SalesLeadAccountAssociationServiceFactory.class));
    provider.registerBean(SalesLeadProfileUnlockInfoService.class.getName(),
        generator.getBean(SalesLeadProfileUnlockHistoryInfoFactory.class));
    provider.registerBean(SalesLeadEditableContactInfoService.class.getName(),
        generator.getBean(SalesLeadEditableContactInfoServiceFactory.class));
    provider.registerBean(SalesLeadAccountMetadataService.class.getName(),
        generator.getBean(SalesLeadAccountMetadataServiceFactory.class));
    provider.registerBean(SalesAccountsService.class.getName(),
        generator.getBean(SalesAccountsServiceFactory.class));
    provider.registerBean(SalesAlertsService.class.getName(),
        generator.getBean(SalesAlertsServiceFactory.class));
    provider.registerBean(ProductCategoryInterestService.class.getName(),
        generator.getBean(ProductCategoryInterestServiceFactory.class));
    provider.registerBean(SalesNoteService.class.getName(),
        generator.getBean(SalesNoteServiceFactory.class));
    provider.registerBean(ManualEmailToMemberMappingService.class.getName(),
        generator.getBean(ManualEmailToMemberMappingServiceFactory.class));
    provider.registerBean(SavedLeadService.class.getName(),
        generator.getBean(SavedLeadServiceFactory.class));
    provider.registerBean(BuyerEngagementService.class.getName(),
        generator.getBean(BuyerEngagementServiceFactory.class));
    provider.registerBean(SalesListCsvImportService.class.getName(),
        generator.getBean(SalesListCsvImportServiceFactory.class));
    provider.registerBean(SalesAccountToListMappingServiceFactory.class.getName(),
        generator.getBean(SalesAccountToListMappingServiceFactory.class));
    provider.registerBean(SalesBookmarkServiceFactory.class.getName(),
        generator.getBean(SalesBookmarkServiceFactory.class));
    provider.registerBean(BuyerIntentServiceFactory.class.getName(),
        generator.getBean(BuyerIntentServiceFactory.class));
    provider.registerBean(LixService.class.getName(),
        generator.getBean(LixServiceFactory.class));
    provider.registerBean(EnterprisePlatformClient.class.getName(),
        generator.getBean(EnterpriseMappingClientFactory.class));
    provider.registerBean(LeadPositionChangeServiceFactory.class.getName(),
        generator.getBean(LeadPositionChangeServiceFactory.class));
    provider.registerBean(SalesSubscriptionBenefitsService.class.getName(),
        generator.getBean(SalesSubscriptionBenefitsServiceFactory.class));
    provider.registerBean(SalesProfileViewsService.class.getName(),
        generator.getBean(SalesProfileViewsServiceFactory.class));
    provider.registerBean(SalesFilterLayoutService.class.getName(),
        generator.getBean(SalesFilterLayoutServiceFactory.class));
    provider.registerBean(SalesPinnedFiltersService.class.getName(),
        generator.getBean(SalesPinnedFiltersServiceFactory.class));
    provider.registerBean(SalesDefaultListServiceFactory.class.getName(),
        generator.getBean(SalesDefaultListServiceFactory.class));
    provider.registerBean(SalesRelationshipMapChangeLogServiceFactory.class.getName(),
        generator.getBean(SalesRelationshipMapChangeLogServiceFactory.class));
    provider.registerBean(SalesCoachConversationServiceFactory.class.getName(),
        generator.getBean(SalesCoachConversationServiceFactory.class));
    provider.registerBean(SalesPYMKRecommendationsServiceFactory.class.getName(),
        generator.getBean(SalesPYMKRecommendationsServiceFactory.class));
    provider.registerBean(SalesRealtimeAuthorizationServiceFactory.class.getName(),
        generator.getBean(SalesRealtimeAuthorizationServiceFactory.class));
    provider.registerBean(AccountPlaysMetadataServiceFactory.class.getName(),
        generator.getBean(AccountPlaysMetadataServiceFactory.class));
    provider.registerBean(SalesSellerIdentityServiceFactory.class.getName(),
        generator.getBean(SalesSellerIdentityServiceFactory.class));
    provider.registerBean(SellerIdentityProductHelperServiceFactory.class.getName(),
        generator.getBean(SellerIdentityProductHelperServiceFactory.class));
    provider.registerBean(EnterpriseEmailPluginsServiceFactory.class.getName(),
        generator.getBean(EnterpriseEmailPluginsServiceFactory.class));
    provider.registerBean(AccountPlayServiceFactory.class.getName(),
        generator.getBean(AccountPlayServiceFactory.class));
    provider.registerBean(SalesCommunicationPluginService.class.getName(),
        generator.getBean(SalesCommunicationPluginServiceFactory.class));
    provider.registerBean(LssSeatTransferActionsService.class.getName(),
        generator.getBean(LssSeatTransferActionsServiceFactory.class));
    provider.registerBean(ContractSellerIdentityService.class.getName(),
        generator.getBean(ContractSellerIdentityServiceFactory.class));
  }

  @Import(clazz = HealthCheckFactory.class)
  @Import(clazz = CounterMetricsSensorFactory.class)
  @Import(clazz = GaugeMetricsSensorFactory.class)
  private void registerSensors(Generator generator) {
    SensorRegistry sensorRegistry = generator.getBean(HealthCheckFactory.class);

    sensorRegistry.registerSensor(generator.getBean(CounterMetricsSensorFactory.class));
    sensorRegistry.registerSensor(generator.getBean(GaugeMetricsSensorFactory.class));
  }
}
