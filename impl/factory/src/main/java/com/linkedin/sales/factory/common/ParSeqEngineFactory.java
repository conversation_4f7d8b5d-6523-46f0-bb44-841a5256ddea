package com.linkedin.sales.factory.common;

import com.linkedin.container.executor.factory.ExecutorServiceFactory;
import com.linkedin.container.executor.factory.ScheduledExecutorServiceFactory;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.factory.EngineFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link Engine}.
 *
 * <AUTHOR>
 */
public class ParSeqEngineFactory extends SimpleSingletonFactory<Engine> {
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEngine");
  private static final String EXECUTOR_PREFIX = "sales-executor";
  private static final String SCHEDULED_EXECUTOR_PREFIX = "sales-scheduled-executor";
  private static final String ENGINE_PREFIX = "sales-engine";

  @Import(clazz = ScheduledExecutorServiceFactory.class, prefix = SCHEDULED_EXECUTOR_PREFIX)
  @Import(clazz = ExecutorServiceFactory.class, prefix = EXECUTOR_PREFIX)
  @Import(clazz = EngineFactory.class, prefix = ENGINE_PREFIX)
  @Override
  protected Engine createInstance(ConfigView view) {
    EngineFactory.Overrides overrides = new EngineFactory.Overrides();
    return overrideAndGetBean(EngineFactory.class, SCOPE.child(ENGINE_PREFIX), overrides);
  }
}
