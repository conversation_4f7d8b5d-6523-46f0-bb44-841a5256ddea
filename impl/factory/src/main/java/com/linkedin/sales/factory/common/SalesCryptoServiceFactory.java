package com.linkedin.sales.factory.common;

import com.linkedin.sales.service.SalesCryptoService;
import com.linkedin.security.crypto.CryptoException;
import com.linkedin.security.crypto.CryptoSuite;
import com.linkedin.security.crypto.secretmgmt.ConfigMapSecretStoreLoader;
import com.linkedin.security.crypto.secretmgmt.SecretStore;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigMap;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesCryptoServiceFactory extends SimpleSingletonFactory<SalesCryptoService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesCryptoService");

  @Config
  private static class Cfg {
    @Required
    ConfigMap secrets;
  }

  @Override
  protected SalesCryptoService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);

    try {
      SecretStore secretStore = new ConfigMapSecretStoreLoader(cfg.secrets).load();
      CryptoSuite cryptoSuite = CryptoSuite.getInstance(secretStore);
      return new SalesCryptoService(cryptoSuite);
    } catch (CryptoException e) {
      throw new RuntimeException(e);
    }
  }
}
