package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.espresso.client.factory.EspressoClientFactory;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Created by jiawang at 11/6/2018
 * This is an abstract base factory class to help create {@link EspressoClient} in a specific cluster in production.
 */
public abstract class SalesEspressoBaseClientFactory extends SimpleSingletonFactory<EspressoClient> {
  private static final String ESPRESSO_PREFIX = "espresso";

  @Import(clazz = EspressoClientFactory.class, prefix = ESPRESSO_PREFIX)
  @Override
  protected EspressoClient createInstance(ConfigView view) {
    return getBean(EspressoClientFactory.class, view.getScope().child(ESPRESSO_PREFIX));
  }
}
