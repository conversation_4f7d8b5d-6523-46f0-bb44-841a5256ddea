package com.linkedin.sales.factory.common;

import com.linkedin.util.factory.Scope;


/**
 * This is to help create {@link com.linkedin.espresso.client.EspressoClient} in cluster ESPRESSO_MT-LD-3 in production.
 * Note that for dev and EI, they should share the same cluster
 *
 * created 11/26/2018
 * <AUTHOR>
 */
public class SalesEspressoLd3ClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesEspressoLd3");
}
