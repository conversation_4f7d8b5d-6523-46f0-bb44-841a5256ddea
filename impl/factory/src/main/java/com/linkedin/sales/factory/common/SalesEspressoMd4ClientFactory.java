package com.linkedin.sales.factory.common;

import com.linkedin.util.factory.Scope;


/**
 * Created by jiawang at 8/17/2018
 * This is to help create {@link com.linkedin.espresso.client.EspressoClient} in cluster ESPRESSO_MT-MD-4 in production.
 * Note that for dev and EI, they should share the same cluster
 */
public class SalesEspressoMd4ClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesEspressoMd4");
}
