package com.linkedin.sales.factory.common;

import com.linkedin.util.factory.Scope;


/**
 * Created by jiawang at 8/18/2018
 * This is to help create {@link com.linkedin.espresso.client.EspressoClient} in cluster ESPRESSO_MT-SD-1 in production.
 * Note that for dev and EI, they should share the same cluster
 */
public class SalesEspressoSd1ClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesEspressoSd1");
}
