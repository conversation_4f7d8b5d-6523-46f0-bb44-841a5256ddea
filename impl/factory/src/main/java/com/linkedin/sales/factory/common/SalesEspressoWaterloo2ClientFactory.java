package com.linkedin.sales.factory.common;

import com.linkedin.util.factory.Scope;

/**
 * This is to help create {@link com.linkedin.espresso.client.EspressoClient} in cluster ESPRESSO_WATERLOO2 in production.
 * Note that for dev and EI, they should share the same cluster
 *
 * created 06/07/2022
 * <AUTHOR>
 */
public class SalesEspressoWaterloo2ClientFactory extends SalesEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesEspressoWaterloo2");
}