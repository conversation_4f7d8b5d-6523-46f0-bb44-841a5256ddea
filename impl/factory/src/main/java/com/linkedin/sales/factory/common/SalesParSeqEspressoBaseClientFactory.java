package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClientFactory;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Created by jiawang at 11/6/2018
 * This is an abstract base factory class to help create {@link ParSeqEspressoClient} by using the related
 * espresso client in the specific cluster.
 */
public abstract class SalesParSeqEspressoBaseClientFactory extends SimpleSingletonFactory<ParSeqEspressoClient> {
  private static final String PARSEQ_ESPRESSO_CLIENT_PREFIX = "parseqEspressoClient";

  abstract EspressoClient getEspressoClient();

  @Import(clazz = ParSeqEspressoClientFactory.class, prefix = PARSEQ_ESPRESSO_CLIENT_PREFIX)
  @ConfigOverride(clazz = ParSeqEspressoClientFactory.class, prefix = PARSEQ_ESPRESSO_CLIENT_PREFIX)
  @Override
  protected ParSeqEspressoClient createInstance(ConfigView view) {
    ParSeqEspressoClientFactory.Cfg overrides = new ParSeqEspressoClientFactory.Cfg();
    overrides.espressoClient = getEspressoClient();

    return configOverrideAndGetBean(ParSeqEspressoClientFactory.class, view.getScope().child(PARSEQ_ESPRESSO_CLIENT_PREFIX), overrides);
  }
}
