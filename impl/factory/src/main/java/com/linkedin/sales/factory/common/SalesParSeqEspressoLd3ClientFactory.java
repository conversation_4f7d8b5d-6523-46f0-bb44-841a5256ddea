package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link SalesEspressoLd3ClientFactory}
 *
 * created 11/26/2018
 * <AUTHOR>
 */
public class SalesParSeqEspressoLd3ClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEspressoLd3Client");

  @Import(clazz = SalesEspressoLd3ClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(SalesEspressoLd3ClientFactory.class);
  }
}
