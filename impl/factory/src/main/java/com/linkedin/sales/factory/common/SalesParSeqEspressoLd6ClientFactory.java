package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Created by jiawang at 8/17/2018
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using {@link SalesEspressoLd6ClientFactory}
 */
public class SalesParSeqEspressoLd6ClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEspressoLd6Client");

  @Import(clazz = SalesEspressoLd6ClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(SalesEspressoLd6ClientFactory.class);
  }
}
