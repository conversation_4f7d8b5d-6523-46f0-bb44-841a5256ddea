package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;

public class SalesParSeqEspressoMt3ClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEspressoMt3Client");

  @Import(clazz = SalesEspressoMt3ClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(SalesEspressoMt3ClientFactory.class);
  }
}
