package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * Created by jiawang at 8/17/2018
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using {@link SalesEspressoSd1ClientFactory}
 */
public class SalesParSeqEspressoSd1ClientFactory extends SalesParSeqEspressoBaseClientFactory {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEspressoSd1Client");

  @Import(clazz = SalesEspressoSd1ClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(SalesEspressoSd1ClientFactory.class);
  }
}
