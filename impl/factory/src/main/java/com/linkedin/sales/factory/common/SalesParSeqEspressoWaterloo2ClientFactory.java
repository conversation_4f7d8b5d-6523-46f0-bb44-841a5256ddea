package com.linkedin.sales.factory.common;

import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.Import;


/**
 * This is to help create {@link com.linkedin.espresso.client.ParSeqEspressoClient} using
 * {@link SalesEspressoWaterloo2ClientFactory}
 *
 * created 06/07/2022
 * <AUTHOR>
 */
public class SalesParSeqEspressoWaterloo2ClientFactory extends SalesParSeqEspressoBaseClientFactory {
  private static final Scope SCOPE = Scope.ROOT.child("salesParSeqEspressoWaterloo2Client");

  @Import(clazz = SalesEspressoWaterloo2ClientFactory.class)
  @Override
  EspressoClient getEspressoClient() {
    return getBean(SalesEspressoWaterloo2ClientFactory.class);
  }
}
