package com.linkedin.sales.factory.common;

import com.linkedin.tracker.producer.TrackingProducer;
import com.linkedin.tracker.producer.TrackingProducerFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory to create instances of {@link SingletonTrackingProducerFactory}
 */
public class SingletonTrackingProducerFactory extends SimpleSingletonFactory<TrackingProducer> {

  private static final Scope SCOPE = Scope.ROOT.child("singletonTrackingProducer");

  @Override
  @Import(clazz = TrackingProducerFactory.class, prefix = "trackingProducer")
  protected TrackingProducer createInstance(ConfigView view) {

    return getBean(TrackingProducerFactory.class, view.getScope().child("trackingProducer"));
  }
}