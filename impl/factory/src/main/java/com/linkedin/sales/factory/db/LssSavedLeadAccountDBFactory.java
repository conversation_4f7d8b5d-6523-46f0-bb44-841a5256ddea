package com.linkedin.sales.factory.db;

import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.factory.common.LssSavedLeadAccountParSeqEspressoClientFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * factory for LssSavedLeadAccountDB
 */
public class LssSavedLeadAccountDBFactory  extends SimpleSingletonFactory<LssSavedLeadAccountDB> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lssSavedLeadAccountDB");

  @Import(clazz = LssSavedLeadAccountParSeqEspressoClientFactory.class)
  @Override
  protected LssSavedLeadAccountDB createInstance(ConfigView view) {
    return new LssSavedLeadAccountDB(getBean(LssSavedLeadAccountParSeqEspressoClientFactory.class));
  }
}