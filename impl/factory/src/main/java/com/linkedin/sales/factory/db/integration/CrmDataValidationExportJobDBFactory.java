package com.linkedin.sales.factory.db.integration;

import com.linkedin.sales.ds.db.CrmDataValidationExportJobDB;
import com.linkedin.sales.factory.common.SalesParSeqEspressoSd1ClientFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class CrmDataValidationExportJobDBFactory extends SimpleSingletonFactory<CrmDataValidationExportJobDB> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmDataValidationExportJobDB");

  @Import(clazz = SalesParSeqEspressoSd1ClientFactory.class)
  @Override
  protected CrmDataValidationExportJobDB createInstance(ConfigView view) {
    return new CrmDataValidationExportJobDB(getBean(SalesParSeqEspressoSd1ClientFactory.class));
  }
}
