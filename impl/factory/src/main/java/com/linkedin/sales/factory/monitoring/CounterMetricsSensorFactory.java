package com.linkedin.sales.factory.monitoring;

import com.linkedin.healthcheck.SensorRegistryFactory;
import com.linkedin.sales.monitoring.CounterMetricsSensor;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Created by jiawang on 11/5/2018
 * Factory class for {@link CounterMetricsSensor}
 */
public class CounterMetricsSensorFactory extends SimpleSingletonFactory<CounterMetricsSensor> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("counterMetricsSensor");

  @Import(clazz = SensorRegistryFactory.class)
  @Override
  protected CounterMetricsSensor createInstance(ConfigView view) {
    return new CounterMetricsSensor();
  }
}
