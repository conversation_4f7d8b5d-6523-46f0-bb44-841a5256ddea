package com.linkedin.sales.factory.monitoring;

import com.linkedin.healthcheck.SensorRegistryFactory;
import com.linkedin.sales.monitoring.GaugeMetricsSensor;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Created by jiawang on 11/5/2018
 * Factory class for {@link GaugeMetricsSensor}
 */
public class GaugeMetricsSensorFactory extends SimpleSingletonFactory<GaugeMetricsSensor> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("gaugeMetricsSensor");

  @Import(clazz = SensorRegistryFactory.class)
  @Override
  protected GaugeMetricsSensor createInstance(ConfigView view) {
    return new GaugeMetricsSensor();
  }
}
