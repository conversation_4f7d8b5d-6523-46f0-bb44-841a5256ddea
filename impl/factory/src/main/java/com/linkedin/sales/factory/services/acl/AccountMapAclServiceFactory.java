package com.linkedin.sales.factory.services.acl;

import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.service.acl.AccountMapAclService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * factory for `AccountMapAclService` class
 */
public class AccountMapAclServiceFactory extends SimpleSingletonFactory<AccountMapAclService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("accountMapAclService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)

  @Override
  protected AccountMapAclService createInstance(ConfigView view) {
    LssListDB lssListDB = new LssListDB(getBean(LssListParSeqEspressoClientFactory.class));
    LssSharingDB lssSharingDB = new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class));
    return new AccountMapAclService(lssSharingDB, lssListDB, getBean(SalesSeatClientFactory.class));
  }
}
