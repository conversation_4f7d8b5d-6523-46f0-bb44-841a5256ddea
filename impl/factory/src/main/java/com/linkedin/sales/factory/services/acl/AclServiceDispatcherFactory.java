package com.linkedin.sales.factory.services.acl;

import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.acl.ListAclService;
import com.linkedin.sales.service.acl.AccountMapAclService;
import com.linkedin.sales.service.acl.LsiMetricsReportAclService;
import com.linkedin.sales.service.acl.NoteAclService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * factory for `AclServiceDispatcher` class
 * <AUTHOR>
 */
public class AclServiceDispatcherFactory extends SimpleSingletonFactory<AclServiceDispatcher> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("aclServiceDispatcher");

  @Import(clazz = ListAclServiceFactory.class)
  @Import(clazz = AccountMapAclServiceFactory.class)
  @Import(clazz = NoteAclServiceFactory.class)
  @Import(clazz = LsiMetricsReportAclServiceFactory.class)

  @Override
  protected AclServiceDispatcher createInstance(ConfigView view) {
    ListAclService listAclService = getBean(ListAclServiceFactory.class);
    AccountMapAclService accountMapAclService = getBean(AccountMapAclServiceFactory.class);
    NoteAclService noteAclService = getBean(NoteAclServiceFactory.class);
    LsiMetricsReportAclService lsiMetricsReportAclService = getBean(LsiMetricsReportAclServiceFactory.class);
    return new AclServiceDispatcher(listAclService, accountMapAclService, noteAclService, lsiMetricsReportAclService);
  }
}
