package com.linkedin.sales.factory.services.acl;

import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.client.salesinsights.SalesInsightsMetricsReportClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.service.acl.LsiMetricsReportAclService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * factory for `LsiMetricsReportAclService` class
 */
public class LsiMetricsReportAclServiceFactory extends SimpleSingletonFactory<LsiMetricsReportAclService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("LsiMetricsReportAclService");

  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Import(clazz = SalesInsightsMetricsReportClientFactory.class)

  @Override
  protected LsiMetricsReportAclService createInstance(ConfigView view) {
    LssSharingDB lssSharingDB = new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class));
    return new LsiMetricsReportAclService(lssSharingDB,
        getBean(SalesSeatClientFactory.class), getBean(SalesInsightsMetricsReportClientFactory.class));
  }
}
