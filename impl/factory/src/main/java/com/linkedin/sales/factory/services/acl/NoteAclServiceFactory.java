package com.linkedin.sales.factory.services.acl;

import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.service.acl.NoteAclService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * factory for `NoteAclService` class
 */
public class NoteAclServiceFactory extends SimpleSingletonFactory<NoteAclService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("NoteAclService");

  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)

  @Override
  protected NoteAclService createInstance(ConfigView view) {
    LssSharingDB lssSharingDB = new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class));
    return new NoteAclService(lssSharingDB, getBean(SalesSeatClientFactory.class));
  }
}
