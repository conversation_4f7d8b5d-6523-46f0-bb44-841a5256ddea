package com.linkedin.sales.factory.services.acl;

import com.linkedin.sales.service.SalesRealtimeAuthorizationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.sales.factory.client.common.SalesIdentityClientFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory to create SalesRealtimeAuthorizationService.
 */
public class SalesRealtimeAuthorizationServiceFactory extends
                                                      SimpleSingletonFactory<SalesRealtimeAuthorizationService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesRealtimeAuthorizationService");
  @Import(clazz = AclServiceDispatcherFactory.class)
  @Import(clazz = SalesIdentityClientFactory.class)
  @Override
  protected SalesRealtimeAuthorizationService createInstance(ConfigView view) {
    return new SalesRealtimeAuthorizationService(getBean(AclServiceDispatcherFactory.class),
        getBean(SalesIdentityClientFactory.class));
  }
}
