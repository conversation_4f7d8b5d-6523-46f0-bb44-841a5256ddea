package com.linkedin.sales.factory.services.activities;

import com.linkedin.sales.factory.client.common.PinotUtilsFactory;
import com.linkedin.sales.factory.monitoring.CounterMetricsSensorFactory;
import com.linkedin.sales.service.SalesActivityTotalsJobService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesActivityTotalsJobServiceFactory extends SimpleSingletonFactory<SalesActivityTotalsJobService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesActivityTotalsService");

  @Import(clazz = PinotUtilsFactory.class)
  @Import(clazz = CounterMetricsSensorFactory.class)
  protected SalesActivityTotalsJobService createInstance(ConfigView view) {
    return new SalesActivityTotalsJobService(
        getBean(PinotUtilsFactory.class),
        getBean(CounterMetricsSensorFactory.class)
    );
  }
}