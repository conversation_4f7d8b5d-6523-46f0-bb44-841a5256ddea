package com.linkedin.sales.factory.services.alerts;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssAlertDB;
import com.linkedin.sales.factory.common.LssAlertParSeqEspressoClientFactory;
import com.linkedin.sales.service.alerts.SalesAlertsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory class for {@link SalesAlertsService}
 */
public class SalesAlertsServiceFactory extends SimpleSingletonFactory<SalesAlertsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesAlertsService");

  @Import(clazz = LssAlertParSeqEspressoClientFactory.class)
  @Override
  protected SalesAlertsService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssAlertParSeqEspressoClientFactory.class);
    LssAlertDB lssAlertDB = new LssAlertDB(parseqEspressoClient);
    return new SalesAlertsService(lssAlertDB);
  }
}
