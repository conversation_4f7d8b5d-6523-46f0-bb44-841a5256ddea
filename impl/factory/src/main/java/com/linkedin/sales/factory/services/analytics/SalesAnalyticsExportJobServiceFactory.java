package com.linkedin.sales.factory.services.analytics;

import com.linkedin.sales.factory.client.common.PinotUtilsFactory;
import com.linkedin.sales.service.SalesAnalyticsExportJobService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesAnalyticsExportJobServiceFactory extends SimpleSingletonFactory<SalesAnalyticsExportJobService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesAnalyticsExportJobService");

  @Import(clazz = PinotUtilsFactory.class)
  @Override
  protected SalesAnalyticsExportJobService createInstance(ConfigView view) {
    return new SalesAnalyticsExportJobService(getBean(PinotUtilsFactory.class));
  }
}
