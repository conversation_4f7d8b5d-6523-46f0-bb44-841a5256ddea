package com.linkedin.sales.factory.services.analytics;

import com.linkedin.sales.factory.client.analytics.SalesAnalyticsExportJobsApiClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.SalesAnalyticsExportJobServiceV2;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesAnalyticsExportJobServiceV2Factory extends SimpleSingletonFactory<SalesAnalyticsExportJobServiceV2> {
  private static final Scope SCOPE = Scope.ROOT.child("salesAnalyticsExportJobServiceV2");

  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = SalesAnalyticsExportJobsApiClientFactory.class)
  @Import(clazz = SalesAnalyticsExportJobServiceFactory.class)
  @Override
  protected SalesAnalyticsExportJobServiceV2 createInstance(ConfigView view) {
    return new SalesAnalyticsExportJobServiceV2(getBean(LixServiceFactory.class),
        getBean(SalesAnalyticsExportJobsApiClientFactory.class),
        getBean(SalesAnalyticsExportJobServiceFactory.class));
  }
}
