package com.linkedin.sales.factory.services.authentication;

import com.linkedin.sales.rest.filter.AllowListedEndPointsForAuthorizationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;
import java.util.Set;


public class AllowListedEndpointForAuthorizationServiceFactory extends SimpleSingletonFactory<AllowListedEndPointsForAuthorizationService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesAllowListedEndPointsForAuthorizationService");

  @Config
  private static class Cfg {
    @Required
    Set<String> authorizationAllowListedEndpoints;
  }

  @Override
  protected AllowListedEndPointsForAuthorizationService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);
    return new AllowListedEndPointsForAuthorizationService(cfg.authorizationAllowListedEndpoints);
  }
}