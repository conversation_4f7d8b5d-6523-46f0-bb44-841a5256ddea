package com.linkedin.sales.factory.services.authentication;

import com.linkedin.restligateway.util.GatewayCallerFinderFactory;
import com.linkedin.sales.factory.common.ParSeqEngineFactory;
import com.linkedin.sales.factory.services.common.SalesContractServiceFactory;
import com.linkedin.sales.rest.filter.SalesContractAuthorizationFilter;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesContractAuthorizationFilterFactory extends SimpleSingletonFactory<SalesContractAuthorizationFilter> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesContractAuthorizationFilter");

  @Import(clazz = SalesContractServiceFactory.class)
  @Import(clazz = AllowListedEndpointForAuthorizationServiceFactory.class)
  @Import(clazz = ParSeqEngineFactory.class)
  @Import(clazz = GatewayCallerFinderFactory.class)
  @Override
  protected SalesContractAuthorizationFilter createInstance(ConfigView view) {
    return new SalesContractAuthorizationFilter(getBean(ParSeqEngineFactory.class),
        getBean(SalesContractServiceFactory.class),
        getBean(AllowListedEndpointForAuthorizationServiceFactory.class),
        getBean(GatewayCallerFinderFactory.class));
  }
}
