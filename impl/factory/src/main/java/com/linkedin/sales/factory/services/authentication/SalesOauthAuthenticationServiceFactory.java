package com.linkedin.sales.factory.services.authentication;

import com.linkedin.container.ic.handlers.factory.MemberIdentityHandlerFactory;
import com.linkedin.sales.factory.client.externalization.SalesOauthAuthenticationClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.SalesOauthAuthenticationService;
import com.linkedin.util.factory.EnvInfoFinderFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesOauthAuthenticationServiceFactory extends SimpleSingletonFactory<SalesOauthAuthenticationService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesOauthAuthenticationService");

  @Import(clazz = SalesOauthAuthenticationClientFactory.class)
  @Import(clazz = MemberIdentityHandlerFactory.class)
  @Import(clazz = EnvInfoFinderFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesOauthAuthenticationService createInstance(ConfigView view) {
    return new SalesOauthAuthenticationService(
        getBean(SalesOauthAuthenticationClientFactory.class),
        getBean(MemberIdentityHandlerFactory.class),
        getBean(EnvInfoFinderFactory.class),
        getBean(LixServiceFactory.class)
    );
  }
}
