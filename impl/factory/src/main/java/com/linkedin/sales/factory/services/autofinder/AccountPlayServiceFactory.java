package com.linkedin.sales.factory.services.autofinder;

import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.factory.common.LssAutoFinderParSeqEspressoClientFactory;
import com.linkedin.sales.service.autofinder.AccountPlayService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class AccountPlayServiceFactory extends SimpleSingletonFactory<AccountPlayService> {
  private static final Scope SCOPE = Scope.ROOT.child("accountPlayService");
  @Import(clazz = LssAutoFinderParSeqEspressoClientFactory.class)
  @Override
  protected AccountPlayService createInstance(ConfigView configView) {
    LssAutoFinderDB lssAutoFinderDB = new LssAutoFinderDB(getBean(LssAutoFinderParSeqEspressoClientFactory.class));
    return new AccountPlayService(lssAutoFinderDB);
  }
}
