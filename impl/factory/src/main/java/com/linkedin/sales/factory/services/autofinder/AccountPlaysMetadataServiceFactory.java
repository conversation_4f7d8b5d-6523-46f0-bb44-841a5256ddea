package com.linkedin.sales.factory.services.autofinder;

import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.factory.common.LssAutoFinderParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.autofinder.AccountPlaysMetadataService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class AccountPlaysMetadataServiceFactory extends SimpleSingletonFactory<AccountPlaysMetadataService> {
  private static final Scope SCOPE = Scope.ROOT.child("accountPlaysMetadataService");
  @Import(clazz = LssAutoFinderParSeqEspressoClientFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected AccountPlaysMetadataService createInstance(ConfigView configView) {
    LssAutoFinderDB lssAutoFinderDB = new LssAutoFinderDB(getBean(LssAutoFinderParSeqEspressoClientFactory.class));
    return new AccountPlaysMetadataService(lssAutoFinderDB,
            getBean(LixServiceFactory.class));
  }
}
