package com.linkedin.sales.factory.services.autoprospecting;

import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.factory.common.LssAutoProspectingParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.buyerengagement.SalesSellerIdentityServiceFactory;
import com.linkedin.sales.service.autoprospecting.CampaignService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class CampaignServiceFactory extends SimpleSingletonFactory<CampaignService> {
  private static final Scope SCOPE = Scope.ROOT.child("campaignService");

  @Import(clazz = LssAutoProspectingParSeqEspressoClientFactory.class)
  @Import(clazz = SalesSellerIdentityServiceFactory.class)
  @Override
  protected CampaignService createInstance(ConfigView configView) {
    LssAutoProspectingDB lssAutoProspectingDB =
        new LssAutoProspectingDB(getBean(LssAutoProspectingParSeqEspressoClientFactory.class));
    return new CampaignService(lssAutoProspectingDB, getBean(SalesSellerIdentityServiceFactory.class));
  }
}
