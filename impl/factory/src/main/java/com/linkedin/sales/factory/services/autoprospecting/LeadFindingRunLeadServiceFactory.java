package com.linkedin.sales.factory.services.autoprospecting;

import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.factory.common.LssAutoProspectingParSeqEspressoClientFactory;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunLeadService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory for creating instances of {@link LeadFindingRunLeadService}.
 * This factory is used to create a singleton instance of the service.
 */
public class LeadFindingRunLeadServiceFactory extends SimpleSingletonFactory<LeadFindingRunLeadService> {
  private static final Scope SCOPE = Scope.ROOT.child("LeadFindingRunLeadService");

  @Import(clazz = LssAutoProspectingParSeqEspressoClientFactory.class)
  @Override
  protected LeadFindingRunLeadService createInstance(ConfigView configView) {
    LssAutoProspectingDB lssAutoProspectingDB =
        new LssAutoProspectingDB(getBean(LssAutoProspectingParSeqEspressoClientFactory.class));
    return new LeadFindingRunLeadService(lssAutoProspectingDB);
  }
}