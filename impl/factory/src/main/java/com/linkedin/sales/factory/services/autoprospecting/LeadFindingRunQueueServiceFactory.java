package com.linkedin.sales.factory.services.autoprospecting;

import com.linkedin.sales.factory.common.LssAutoProspectingParSeqEspressoClientFactory;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunQueueService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class LeadFindingRunQueueServiceFactory extends SimpleSingletonFactory<LeadFindingRunQueueService> {
  private static final Scope SCOPE = Scope.ROOT.child("LeadFindingRunQueueService");

  @Import(clazz = LssAutoProspectingParSeqEspressoClientFactory.class)
  @Override
  protected LeadFindingRunQueueService createInstance(ConfigView configView) {
    return new LeadFindingRunQueueService();
  }
}
