package com.linkedin.sales.factory.services.autoprospecting;

import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.factory.common.LssAutoProspectingParSeqEspressoClientFactory;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class LeadFindingRunServiceFactory extends SimpleSingletonFactory<LeadFindingRunService> {
  private static final Scope SCOPE = Scope.ROOT.child("LeadFindingRunService");

  @Import(clazz = LssAutoProspectingParSeqEspressoClientFactory.class)
  @Override
  protected LeadFindingRunService createInstance(ConfigView configView) {
    LssAutoProspectingDB lssAutoProspectingDB =
        new LssAutoProspectingDB(getBean(LssAutoProspectingParSeqEspressoClientFactory.class));
    return new LeadFindingRunService(lssAutoProspectingDB);
  }
}
