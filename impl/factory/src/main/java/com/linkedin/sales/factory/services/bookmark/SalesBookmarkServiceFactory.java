package com.linkedin.sales.factory.services.bookmark;

import com.linkedin.sales.ds.db.LssBookmarkDB;
import com.linkedin.sales.factory.common.LssBookmarkParSeqEspressoClientFactory;
import com.linkedin.sales.service.bookmark.SalesBookmarkService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class SalesBookmarkServiceFactory extends SimpleSingletonFactory<SalesBookmarkService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesBookmarkService");

  @Import(clazz = LssBookmarkParSeqEspressoClientFactory.class)

  @Override
  protected SalesBookmarkService createInstance(ConfigView view) {
    return new SalesBookmarkService(
        new LssBookmarkDB(getBean(LssBookmarkParSeqEspressoClientFactory.class))
    );
  }
}
