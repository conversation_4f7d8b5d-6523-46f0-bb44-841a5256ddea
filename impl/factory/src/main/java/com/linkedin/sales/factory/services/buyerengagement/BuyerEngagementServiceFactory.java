package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.sales.factory.client.common.PinotUtilsFactory;
import com.linkedin.sales.service.buyerengagement.BuyerEngagementService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory for creating {@link BuyerEngagementService}
 */
public class BuyerEngagementServiceFactory extends SimpleSingletonFactory<BuyerEngagementService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("buyerEngagementSegmentService");

  /**
   * Pinot only return 10 rows by default
   * This config is to specify how many rows are returned in SQL
   */
  @Config
  private static class Cfg {
    int rowCount = 100;
  }

  @Import(clazz = PinotUtilsFactory.class)
  @Override
  protected BuyerEngagementService createInstance(ConfigView view) {
    final Cfg cfg = view.fill(Cfg.class);
    return new BuyerEngagementService(getBean(PinotUtilsFactory.class), cfg.rowCount);
  }
}