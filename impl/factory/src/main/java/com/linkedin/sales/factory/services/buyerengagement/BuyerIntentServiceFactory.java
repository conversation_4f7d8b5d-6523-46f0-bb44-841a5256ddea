package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.client.factory.VeniceStoreClientFactory;
import com.linkedin.client.factory.VeniceStoreClientFactoryFactory;
import com.linkedin.lssbuyer.schemas.venice.BuyerIntentTrendVeniceValue;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.restligateway.util.GatewayCallerFinderFactory;
import com.linkedin.sales.service.buyerengagement.BuyerIntentService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory for creating {@link BuyerIntentService}
 */
public class BuyerIntentServiceFactory extends SimpleSingletonFactory<BuyerIntentService> {
  private static final Scope SCOPE = Scope.ROOT.child("buyerIntentService");
  protected static final String VENICE_PREFIX = "venice";

  @Config
  private static class Cfg {
    @Required
    private String _buyerIntentTrendStoreName;
    private String _buyerIntentTrendV2StoreName;
  }

  @Import(clazz = VeniceStoreClientFactoryFactory.class, prefix = VENICE_PREFIX)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = GatewayCallerFinderFactory.class)
  @Override
  protected BuyerIntentService createInstance(ConfigView view) {
    Cfg buyerIntentTrendVeniceCfg = view.fill(Cfg.class);

    VeniceStoreClientFactory veniceFactory = getBean(VeniceStoreClientFactoryFactory.class, view.getScope().child(VENICE_PREFIX));
    return new BuyerIntentService(
        veniceFactory.getAndStartAvroSpecificStoreClient(buyerIntentTrendVeniceCfg._buyerIntentTrendStoreName, BuyerIntentTrendVeniceValue.class),
        veniceFactory.getAndStartAvroSpecificStoreClient(buyerIntentTrendVeniceCfg._buyerIntentTrendV2StoreName, BuyerIntentTrendVeniceValue.class),
        getBean(LixServiceFactory.class),
        getBean(GatewayCallerFinderFactory.class)
    );
  }
}
