package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.factory.common.LssBuyerParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.tracking.TrackingServiceFactory;
import com.linkedin.sales.service.buyerengagement.ContractSellerIdentityService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class ContractSellerIdentityServiceFactory extends SimpleSingletonFactory<ContractSellerIdentityService> {
  private static final Scope SCOPE = Scope.ROOT.child("contractSellerIdentityService");

  @Import(clazz = LssBuyerParSeqEspressoClientFactory.class)
  @Import(clazz = TrackingServiceFactory.class)
  @Override
  protected ContractSellerIdentityService createInstance(ConfigView view) {
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssBuyerParSeqEspressoClientFactory.class);
    LssBuyerDB lssBuyerDB = new LssBuyerDB(parSeqEspressoClient);
    return new ContractSellerIdentityService(lssBuyerDB, getBean(TrackingServiceFactory.class));
  }
}
