package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.factory.common.LssBuyerParSeqEspressoClientFactory;
import com.linkedin.sales.service.buyerengagement.ProductCategoryInterestService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory class for {@link ProductCategoryInterestService}
 */
public class ProductCategoryInterestServiceFactory extends SimpleSingletonFactory<ProductCategoryInterestService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("productCategoryInterestService");

  @Import(clazz = LssBuyerParSeqEspressoClientFactory.class)
  @Override
  protected ProductCategoryInterestService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssBuyerParSeqEspressoClientFactory.class);
    LssBuyerDB lssBuyerDB = new LssBuyerDB(parseqEspressoClient);
    return new ProductCategoryInterestService(lssBuyerDB);
  }
}
