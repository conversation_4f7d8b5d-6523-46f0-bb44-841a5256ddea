package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.factory.client.common.SalesContractSettingsClientFactory;
import com.linkedin.sales.factory.common.LssBuyerParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.buyerengagement.SalesSellerIdentityService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesSellerIdentityService}
 */
public class SalesSellerIdentityServiceFactory extends SimpleSingletonFactory<SalesSellerIdentityService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesSellerIdentityService");

  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = LssBuyerParSeqEspressoClientFactory.class)
  @Import(clazz = SalesContractSettingsClientFactory.class)

  @Override
  protected SalesSellerIdentityService createInstance(ConfigView view) {
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssBuyerParSeqEspressoClientFactory.class);
    LssBuyerDB lssBuyerDB = new LssBuyerDB(parSeqEspressoClient);
    return new SalesSellerIdentityService(lssBuyerDB, getBean(SalesContractSettingsClientFactory.class), getBean(LixServiceFactory.class));
  }
}
