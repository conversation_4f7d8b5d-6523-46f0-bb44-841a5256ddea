package com.linkedin.sales.factory.services.buyerengagement;

import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.tracking.TrackingServiceFactory;
import com.linkedin.sales.service.buyerengagement.SellerIdentityProductHelperService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SellerIdentityProductHelperService}
 */
public class SellerIdentityProductHelperServiceFactory extends SimpleSingletonFactory<SellerIdentityProductHelperService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("sellerIdentityProductHelperService");

  @Import(clazz = ContractSellerIdentityServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = SalesSellerIdentityServiceFactory.class)
  @Import(clazz = TrackingServiceFactory.class)
  @Override
  protected SellerIdentityProductHelperService createInstance(ConfigView view) {
    return new SellerIdentityProductHelperService(getBean(SalesSellerIdentityServiceFactory.class),
        getBean(TrackingServiceFactory.class),
        getBean(LixServiceFactory.class),
        getBean(ContractSellerIdentityServiceFactory.class));
  }
}
