package com.linkedin.sales.factory.services.coach;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssCoachDB;
import com.linkedin.sales.factory.common.LssCoachParSeqEspressoClientFactory;
import com.linkedin.sales.service.coach.SalesCoachConversationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for SalesCoachConversationService
 */
public class SalesCoachConversationServiceFactory extends SimpleSingletonFactory<SalesCoachConversationService> {

  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesCoachConversationService");

  @Import(clazz = LssCoachParSeqEspressoClientFactory.class)
  @Override
  protected SalesCoachConversationService createInstance(ConfigView view) {
    ParSeqEspressoClient lssCoachParseqEspressoClient = getBean(LssCoachParSeqEspressoClientFactory.class);
    LssCoachDB lssCoachDB = new LssCoachDB(lssCoachParseqEspressoClient);
    return new SalesCoachConversationService(lssCoachDB);
  }
}
