package com.linkedin.sales.factory.services.colleagues;

import com.linkedin.sales.ds.db.LssColleaguesDB;
import com.linkedin.sales.factory.common.LssColleaguesParSeqEspressoClientFactory;
import com.linkedin.sales.service.SalesColleaguesService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesColleaguesServiceFactory extends SimpleSingletonFactory<SalesColleaguesService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesColleaguesService");

  @Import(clazz = LssColleaguesParSeqEspressoClientFactory.class)
  @Override
  protected SalesColleaguesService createInstance(ConfigView view) {
    LssColleaguesDB lssColleaguesDB = new LssColleaguesDB(getBean(LssColleaguesParSeqEspressoClientFactory.class));
    return new SalesColleaguesService(lssColleaguesDB);
  }
}
