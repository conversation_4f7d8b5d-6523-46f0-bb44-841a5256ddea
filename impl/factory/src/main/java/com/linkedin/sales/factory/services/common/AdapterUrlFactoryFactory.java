package com.linkedin.sales.factory.services.common;

import com.linkedin.lix.mp.client.LixClient;
import com.linkedin.lix.mp.client.factory.LixClientFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import com.linkedin.util.servlet.i18n.LocaleManager;
import com.linkedin.util.servlet.i18n.LocaleManagerOffspringFactory;
import org.xeril.wafwk.gui.url.AdapterUrlFactoryImpl;
import org.xeril.wafwk.gui.url.UrlFactory;
import org.xeril.wafwk.gui.url.UrlFactoryFactory;
import org.xeril.wafwk.gui.url.UrlFactoryImpl;


/**
 * Factory to create instances of {@link AdapterUrlFactoryFactory}
 */
public class AdapterUrlFactoryFactory extends SimpleSingletonFactory<AdapterUrlFactoryImpl> {

  private static final Scope SCOPE = Scope.ROOT.child("adapterUrlFactory");

  @Import(clazz = UrlFactoryFactory.class)
  @Import(clazz = LixClientFactory.class)
  @Import(clazz = LocaleManagerOffspringFactory.class)
  @Override
  protected AdapterUrlFactoryImpl createInstance(ConfigView view) {
    UrlFactory urlFactory = getBean(UrlFactoryFactory.class);

    LocaleManager localeManager = getBean(LocaleManagerOffspringFactory.class);

    LixClient lixClient = getBean(LixClientFactory.class);

    return new AdapterUrlFactoryImpl((UrlFactoryImpl) urlFactory, localeManager, null, lixClient);
  }
}
