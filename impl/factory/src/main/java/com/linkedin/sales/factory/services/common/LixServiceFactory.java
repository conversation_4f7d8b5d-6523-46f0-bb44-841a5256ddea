package com.linkedin.sales.factory.services.common;

import com.linkedin.lix.mp.client.factory.LixClientFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.service.LixService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class LixServiceFactory extends SimpleSingletonFactory<LixService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("lixService");

  @Import(clazz = LixClientFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Override
  protected LixService createInstance(ConfigView view) {
    return new LixService(
        getBean(LixClientFactory.class),
        getBean(SalesSeatClientFactory.class)
    );
  }
}
