package com.linkedin.sales.factory.services.common;

import com.linkedin.i18n.resource.DynamicResourceBundleManager;
import com.linkedin.i18n.resource.DynamicResourceBundleManagerFactory;
import com.linkedin.sales.service.LocalizationHelper;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class LocalizationHelperFactory extends SimpleSingletonFactory<LocalizationHelper> {
  private static final Scope SCOPE = Scope.ROOT.child("localizationHelper");

  @Override
  @Import(clazz = DynamicResourceBundleManagerFactory.class)
  protected LocalizationHelper createInstance(ConfigView configView) {
    final DynamicResourceBundleManager dynamicResourceBundleManager = getBean(DynamicResourceBundleManagerFactory.class);
    return new LocalizationHelper(dynamicResourceBundleManager);
  }
}