package com.linkedin.sales.factory.services.common;

import com.linkedin.sales.factory.client.common.SalesContractClientFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.service.SalesContractService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesContractServiceFactory extends SimpleSingletonFactory<SalesContractService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesContractService");

  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = SalesContractClientFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Override
  protected SalesContractService createInstance(ConfigView view) {
    return new SalesContractService(
        getBean(SalesContractClientFactory.class),
        getBean(SalesSeatClientFactory.class),
        getBean(LixServiceFactory.class));
  }
}
