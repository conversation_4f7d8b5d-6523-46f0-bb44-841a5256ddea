package com.linkedin.sales.factory.services.customfilterview;

import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.factory.common.SalesParSeqEspressoLd3ClientFactory;
import com.linkedin.sales.factory.common.SalesParSeqEspressoMt3ClientFactory;
import com.linkedin.sales.service.SalesFilterLayoutService;
import com.linkedin.util.factory.EnvInfoFinderFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory initializing {@link SalesFilterLayoutService}
 */
public class SalesFilterLayoutServiceFactory extends SimpleSingletonFactory<SalesFilterLayoutService> {

  private static final Scope SCOPE = Scope.ROOT.child("salesFilterLayoutService");

  @Import(clazz = EnvInfoFinderFactory.class)
  @Import(clazz = SalesParSeqEspressoMt3ClientFactory.class)
  @Import(clazz = SalesParSeqEspressoLd3ClientFactory.class)

  @Override
  protected SalesFilterLayoutService createInstance(ConfigView view) {
    LssCustomFilterViewDB lssCustomFilterViewDB;
    String envInfo = getBean(EnvInfoFinderFactory.class).getEnvInfo().getFabricGroup();
    if (envInfo.toLowerCase().contains("ei")) {
      lssCustomFilterViewDB = new LssCustomFilterViewDB(getBean(SalesParSeqEspressoMt3ClientFactory.class));
    } else {
      lssCustomFilterViewDB = new LssCustomFilterViewDB(getBean(SalesParSeqEspressoLd3ClientFactory.class));
    }
    return new SalesFilterLayoutService(lssCustomFilterViewDB);
  }
}