package com.linkedin.sales.factory.services.customfilterview;

import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.factory.common.SalesParSeqEspressoMt3ClientFactory;
import com.linkedin.sales.factory.common.SalesParSeqEspressoWaterloo2ClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.SalesPinnedFiltersService;
import com.linkedin.util.factory.EnvInfoFinderFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory initializing {@link SalesPinnedFiltersService}
 */
public class SalesPinnedFiltersServiceFactory extends SimpleSingletonFactory<SalesPinnedFiltersService> {

  private static final Scope SCOPE = Scope.ROOT.child("salesPinnedFiltersService");

  @Import(clazz = EnvInfoFinderFactory.class)
  @Import(clazz = SalesParSeqEspressoMt3ClientFactory.class)
  @Import(clazz = SalesParSeqEspressoWaterloo2ClientFactory.class)
  @Import(clazz = LixServiceFactory.class)

  @Override
  protected SalesPinnedFiltersService createInstance(ConfigView view) {
    LssCustomFilterViewDB lssCustomFilterViewDB;
    String envInfo = getBean(EnvInfoFinderFactory.class).getEnvInfo().getFabricGroup();
    if (envInfo.toLowerCase().contains("ei")) {
      lssCustomFilterViewDB = new LssCustomFilterViewDB(getBean(SalesParSeqEspressoMt3ClientFactory.class));
    } else {
      lssCustomFilterViewDB = new LssCustomFilterViewDB(getBean(SalesParSeqEspressoWaterloo2ClientFactory.class));
    }
    return new SalesPinnedFiltersService(lssCustomFilterViewDB, getBean(LixServiceFactory.class));
  }
}
