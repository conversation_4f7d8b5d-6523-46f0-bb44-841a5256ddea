package com.linkedin.sales.factory.services.email;

import com.linkedin.sales.factory.client.common.SalesAccountsV2ClientFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.client.email.EmailClientFactory;
import com.linkedin.sales.factory.client.enterprise.EnterpriseMappingClientFactory;
import com.linkedin.sales.factory.client.enterprise.EnterpriseProfileActivationLinksFactory;
import com.linkedin.sales.factory.services.common.AdapterUrlFactoryFactory;
import com.linkedin.sales.factory.services.common.SalesContractServiceFactory;
import com.linkedin.sales.factory.services.enterprise.SalesInviteRegisterUrlServiceFactory;
import com.linkedin.sales.service.SalesNavigatorEmailService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesNavigatorEmailServiceFactory extends SimpleSingletonFactory<SalesNavigatorEmailService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesNavigatorEmailService");

  @Import(clazz = EmailClientFactory.class)
  @Import(clazz = SalesContractServiceFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Import(clazz = SalesInviteRegisterUrlServiceFactory.class)
  @Import(clazz = EnterpriseMappingClientFactory.class)
  @Import(clazz = EnterpriseProfileActivationLinksFactory.class)
  @Import(clazz = AdapterUrlFactoryFactory.class)
  @Import(clazz = SalesAccountsV2ClientFactory.class)
  @Override
  protected SalesNavigatorEmailService createInstance(ConfigView view) {
    return new SalesNavigatorEmailService(
        getBean(EmailClientFactory.class),
        getBean(SalesContractServiceFactory.class),
        getBean(SalesSeatClientFactory.class),
        getBean(SalesInviteRegisterUrlServiceFactory.class),
        getBean(EnterpriseMappingClientFactory.class),
        getBean(EnterpriseProfileActivationLinksFactory.class),
        getBean(AdapterUrlFactoryFactory.class),
        getBean(SalesAccountsV2ClientFactory.class)
    );
  }
}
