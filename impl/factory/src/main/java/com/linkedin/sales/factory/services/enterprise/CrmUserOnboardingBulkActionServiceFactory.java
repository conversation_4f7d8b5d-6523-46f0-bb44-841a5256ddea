package com.linkedin.sales.factory.services.enterprise;

import com.linkedin.parseq.factory.DefaultEngineFactory;
import com.linkedin.sales.factory.client.email.EmailAddressClientFactory;
import com.linkedin.sales.factory.client.enterprise.EnterpriseMappingClientFactory;
import com.linkedin.sales.factory.client.integration.CrmUserClientFactory;
import com.linkedin.sales.service.CrmUserOnboardingBulkActionService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class CrmUserOnboardingBulkActionServiceFactory extends SimpleSingletonFactory<CrmUserOnboardingBulkActionService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmUserEnterpriseLicenseAssignmentService");

  @Import(clazz = DefaultEngineFactory.class)
  @Import(clazz = CrmUserClientFactory.class)
  @Import(clazz = EnterpriseMappingClientFactory.class)
  @Import(clazz = EmailAddressClientFactory.class)
  @Override
  protected CrmUserOnboardingBulkActionService createInstance(ConfigView view) {
    return new CrmUserOnboardingBulkActionService(
        getBean(DefaultEngineFactory.class),
        getBean(CrmUserClientFactory.class),
        getBean(EnterpriseMappingClientFactory.class),
        getBean(EmailAddressClientFactory.class)
    );
  }
}
