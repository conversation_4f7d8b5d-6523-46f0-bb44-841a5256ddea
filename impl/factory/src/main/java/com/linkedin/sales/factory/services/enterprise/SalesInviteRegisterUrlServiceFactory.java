package com.linkedin.sales.factory.services.enterprise;

import com.linkedin.sales.factory.common.SalesCryptoServiceFactory;
import com.linkedin.sales.service.SalesInviteRegisterUrlService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesInviteRegisterUrlServiceFactory extends SimpleSingletonFactory<SalesInviteRegisterUrlService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesInviteRegisterUrlService");

  @Config
  private static class Cfg {
    @Required
    String inviteRegisterUrlPrefix;
  }


  @Import(clazz = SalesCryptoServiceFactory.class)
  @Override
  protected SalesInviteRegisterUrlService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);
    return new SalesInviteRegisterUrlService(
        cfg.inviteRegisterUrlPrefix,
        getBean(SalesCryptoServiceFactory.class)
    );
  }
}
