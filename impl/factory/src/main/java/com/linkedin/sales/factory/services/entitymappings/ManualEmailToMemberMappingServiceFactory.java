package com.linkedin.sales.factory.services.entitymappings;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssEntityMappingDB;
import com.linkedin.sales.service.entitymappings.ManualEmailToMemberMappingService;
import com.linkedin.sales.factory.common.LssEntityMappingParSeqEspressoClientFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class ManualEmailToMemberMappingServiceFactory
    extends SimpleSingletonFactory<ManualEmailToMemberMappingService> {

  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("manualEmailToMemberMappingService");

  @Import(clazz = LssEntityMappingParSeqEspressoClientFactory.class)
  @Override
  protected ManualEmailToMemberMappingService createInstance(ConfigView view) {
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssEntityMappingParSeqEspressoClientFactory.class);
    LssEntityMappingDB lssEntityMappingDB = new LssEntityMappingDB(parSeqEspressoClient);
    return new ManualEmailToMemberMappingService(lssEntityMappingDB);
  }
}
