package com.linkedin.sales.factory.services.entityviews;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssEntityViewDB;
import com.linkedin.sales.factory.common.LssEntityViewParSeqEspressoClientFactory;
import com.linkedin.sales.service.entityviews.SalesProfileViewsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesProfileViewsService}
 */
public class SalesProfileViewsServiceFactory extends SimpleSingletonFactory<SalesProfileViewsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesProfileViewsService");

  @Import(clazz = LssEntityViewParSeqEspressoClientFactory.class)

  @Override
  protected SalesProfileViewsService createInstance(ConfigView view) {
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssEntityViewParSeqEspressoClientFactory.class);
    LssEntityViewDB lssEntityViewDB = new LssEntityViewDB(parSeqEspressoClient);
    return new SalesProfileViewsService(lssEntityViewDB);
  }
}
