package com.linkedin.sales.factory.services.flagship;

import com.linkedin.sales.service.flagship.LeadPositionChangeService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.cfg.ConfigView;

public class LeadPositionChangeServiceFactory extends SimpleSingletonFactory<LeadPositionChangeService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("leadPositionChangeService");

  @Override
  protected LeadPositionChangeService createInstance(ConfigView view) {
    return new LeadPositionChangeService();
  }
}
