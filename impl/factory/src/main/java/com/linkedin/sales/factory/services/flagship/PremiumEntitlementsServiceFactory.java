package com.linkedin.sales.factory.services.flagship;

import com.linkedin.sales.factory.client.flagship.PremiumEntitlementsClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.flagship.PremiumEntitlementsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class PremiumEntitlementsServiceFactory extends SimpleSingletonFactory<PremiumEntitlementsService> {
  private static final Scope SCOPE = Scope.ROOT.child("premiumEntitlementsService");

  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = PremiumEntitlementsClientFactory.class)
  @Override
  protected PremiumEntitlementsService createInstance(ConfigView view) {
    return new PremiumEntitlementsService(getBean(LixServiceFactory.class),
        getBean(PremiumEntitlementsClientFactory.class));
  }
}
