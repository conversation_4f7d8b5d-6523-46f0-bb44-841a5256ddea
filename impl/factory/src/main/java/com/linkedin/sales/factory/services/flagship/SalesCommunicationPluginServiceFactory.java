package com.linkedin.sales.factory.services.flagship;

import com.google.common.collect.ImmutableMap;
import com.linkedin.sales.service.flagship.enums.SalesCommunicationCampaignName;
import com.linkedin.sales.service.flagship.SalesCommunicationPluginService;
import com.linkedin.sales.service.flagship.SalesCommunicationRenderer;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import java.util.Map;


public class SalesCommunicationPluginServiceFactory
    extends SimpleSingletonFactory<SalesCommunicationPluginService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesCommunicationPluginService");

  @Override
  protected SalesCommunicationPluginService createInstance(ConfigView view) {
    return new SalesCommunicationPluginService(buildRendererMap());
  }

  @Import(clazz = SalesLeadSharedUpdateRendererFactory.class)
  private Map<SalesCommunicationCampaignName, SalesCommunicationRenderer> buildRendererMap() {
    return ImmutableMap.<SalesCommunicationCampaignName, SalesCommunicationRenderer>builder()
        .put(SalesCommunicationCampaignName.FS_LEAD_SHARED_UPDATE, getBean(SalesLeadSharedUpdateRendererFactory.class))
        .build();
  }
}