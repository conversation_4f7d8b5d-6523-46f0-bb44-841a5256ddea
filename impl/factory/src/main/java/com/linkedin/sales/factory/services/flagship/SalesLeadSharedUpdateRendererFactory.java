package com.linkedin.sales.factory.services.flagship;

import com.linkedin.comms.helpers.factory.CommsLocalizationHandlerFactory;
import com.linkedin.sales.service.flagship.SalesLeadSharedUpdateRenderer;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public final class SalesLeadSharedUpdateRendererFactory extends SimpleSingletonFactory<SalesLeadSharedUpdateRenderer> {

  public static final Scope SCOPE = Scope.ROOT.child("salesLeadSharedUpdateRenderer");

  @Import(clazz = CommsLocalizationHandlerFactory.class)
  @Override
  protected SalesLeadSharedUpdateRenderer createInstance(ConfigView view) {
    return new SalesLeadSharedUpdateRenderer(getBean(CommsLocalizationHandlerFactory.class));
  }
}