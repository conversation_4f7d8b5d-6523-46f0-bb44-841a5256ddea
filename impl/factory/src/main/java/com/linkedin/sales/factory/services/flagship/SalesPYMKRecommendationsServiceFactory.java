package com.linkedin.sales.factory.services.flagship;

import com.linkedin.container.ic.factory.IcFinderFactory;
import com.linkedin.healthcheck.EnumMetricsSensorFactory;
import com.linkedin.healthcheck.SensorRegistryFactory;
import com.linkedin.lss.clients.SimpleSettingsClientFactory;
import com.linkedin.sales.factory.client.common.MemberRestrictionClientFactory;
import com.linkedin.sales.factory.client.pymk.SalesPymkVeniceClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.monitoring.flagship.SalesPymkRecommendationsMetrics;
import com.linkedin.sales.service.flagship.SalesPYMKRecommendationsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesPYMKRecommendationsServiceFactory extends SimpleSingletonFactory<SalesPYMKRecommendationsService> {
  private static final Scope SCOPE = Scope.ROOT.child("pymkRecommendationsService");
  public static final String METRICS_SCOPE = "metrics";

  @ConfigOverride(clazz = EnumMetricsSensorFactory.class, prefix = METRICS_SCOPE)
  @Import(clazz = EnumMetricsSensorFactory.class, prefix = METRICS_SCOPE)
  @Import(clazz = IcFinderFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = MemberRestrictionClientFactory.class)
  @Import(clazz = PremiumEntitlementsServiceFactory.class)
  @Import(clazz = SalesPymkVeniceClientFactory.class)
  @Import(clazz = SensorRegistryFactory.class)
  @Import(clazz = SimpleSettingsClientFactory.class)
  @Override
  protected SalesPYMKRecommendationsService createInstance(ConfigView view) {
    final EnumMetricsSensorFactory.Cfg sensorCfg = new EnumMetricsSensorFactory.Cfg();
    final SalesPymkRecommendationsMetrics metrics = new SalesPymkRecommendationsMetrics();

    sensorCfg.enumMetrics = metrics;
    getBean(SensorRegistryFactory.class)
        .registerSensor(configOverrideAndGetBean(EnumMetricsSensorFactory.class, SCOPE.child(METRICS_SCOPE), sensorCfg));

    return new SalesPYMKRecommendationsService(
        getBean(LixServiceFactory.class),
        getBean(IcFinderFactory.class),
        getBean(MemberRestrictionClientFactory.class),
        getBean(PremiumEntitlementsServiceFactory.class),
        metrics,
        getBean(SalesPymkVeniceClientFactory.class),
        getBean(SimpleSettingsClientFactory.class));
  }
}
