package com.linkedin.sales.factory.services.flagship;

import com.linkedin.sales.factory.client.flagship.PremiumUpsellFlowsClientFactory;
import com.linkedin.sales.service.flagship.SalesUpsellFlowConfigService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesUpsellFlowConfigServiceFactory extends SimpleSingletonFactory<SalesUpsellFlowConfigService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesUpsellFlowConfigService");
  @Import(clazz = PremiumUpsellFlowsClientFactory.class)
  @Override
  protected SalesUpsellFlowConfigService createInstance(ConfigView view) {
    return new SalesUpsellFlowConfigService(getBean(PremiumUpsellFlowsClientFactory.class));
  }
}
