package com.linkedin.sales.factory.services.integration;

import com.linkedin.sales.factory.client.integration.CrmPairingClientFactory;
import com.linkedin.sales.factory.client.integration.CrmSettingClientFactory;
import com.linkedin.sales.factory.db.integration.CrmDataValidationExportJobDBFactory;
import com.linkedin.sales.factory.monitoring.CounterMetricsSensorFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.integration.CrmDataValidationExportJobService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import java.util.Collections;
import java.util.Set;


/**
 * Factory for {@link CrmDataValidationExportJobService}
 * <AUTHOR>
 */
public class CrmDataValidationExportJobServiceFactory extends SimpleSingletonFactory<CrmDataValidationExportJobService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("crmDataValidationExportJobService");

  @Config
  private static class Cfg {

    // This should only be set to false for Epsilon framework.
    boolean enabled = true;

    Set<String> trustedCrmInstanceIds = Collections.emptySet();
  }

  @Import(clazz = CrmDataValidationExportJobDBFactory.class)
  @Import(clazz = CounterMetricsSensorFactory.class)
  @Import(clazz = CrmPairingClientFactory.class)
  @Import(clazz = CrmSettingClientFactory.class)
  @Import(clazz = SalesExternalizationServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected CrmDataValidationExportJobService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);

    return new CrmDataValidationExportJobService(
        getBean(CrmPairingClientFactory.class),
        getBean(CrmDataValidationExportJobDBFactory.class),
        getBean(CrmSettingClientFactory.class),
        getBean(SalesExternalizationServiceFactory.class),
        getBean(LixServiceFactory.class),
        getBean(CounterMetricsSensorFactory.class),
        cfg.trustedCrmInstanceIds);
  }
}
