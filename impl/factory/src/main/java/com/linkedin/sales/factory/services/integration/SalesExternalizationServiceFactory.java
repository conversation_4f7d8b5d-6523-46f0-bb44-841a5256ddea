package com.linkedin.sales.factory.services.integration;

import com.linkedin.ambry.client.AmbryClientFactory;
import com.linkedin.restligateway.util.GatewayCallerFinderFactory;
import com.linkedin.sales.factory.client.externalization.ProvisionedApplicationClientFactory;
import com.linkedin.sales.service.integration.SalesExternalizationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory for {@link SalesExternalizationService}
 * <AUTHOR>
 */
public class SalesExternalizationServiceFactory extends SimpleSingletonFactory<SalesExternalizationService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesExternalizationService");
  private static final String PREFIX_AMBRY_CLIENT = "ambryClient";

  @Import(clazz = GatewayCallerFinderFactory.class)
  @Import(clazz = ProvisionedApplicationClientFactory.class)
  @Import(clazz = AmbryClientFactory.class, prefix = PREFIX_AMBRY_CLIENT)
  @Override
  protected SalesExternalizationService createInstance(ConfigView view) {
    return new SalesExternalizationService(getBean(GatewayCallerFinderFactory.class),
        getBean(ProvisionedApplicationClientFactory.class),
        getBean(AmbryClientFactory.class, view.getScope().child(PREFIX_AMBRY_CLIENT)));
  }
}
