package com.linkedin.sales.factory.services.lbep;

import com.linkedin.healthcheck.EnumMetricsSensorFactory;
import com.linkedin.healthcheck.SensorRegistryFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.email.SalesNavigatorEmailServiceFactory;
import com.linkedin.sales.service.lbep.EnterpriseEmailPluginsService;
import com.linkedin.sales.service.lbep.EnterpriseEmailPluginsServiceImpl;
import com.linkedin.sales.service.lbep.EnterpriseEmailPluginsServiceMetrics;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class EnterpriseEmailPluginsServiceFactory extends SimpleSingletonFactory<EnterpriseEmailPluginsService> {
  private static final Scope SCOPE = Scope.ROOT.child("enterpriseEmailPluginsService");
  private static final String METRICS = "metrics";

  @Import(clazz = EnumMetricsSensorFactory.class, prefix = METRICS)
  @ConfigOverride(clazz = EnumMetricsSensorFactory.class, prefix = METRICS)
  @Import(clazz = SalesNavigatorEmailServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected EnterpriseEmailPluginsService createInstance(ConfigView view) {
    EnterpriseEmailPluginsServiceMetrics metrics = new EnterpriseEmailPluginsServiceMetrics();
    final EnumMetricsSensorFactory.Cfg sensorCfg = new EnumMetricsSensorFactory.Cfg();
    sensorCfg.enumMetrics = metrics;
    getBean(SensorRegistryFactory.class).registerSensor(
        configOverrideAndGetBean(EnumMetricsSensorFactory.class, SCOPE.child(METRICS), sensorCfg));

    return new EnterpriseEmailPluginsServiceImpl(
        getBean(SalesNavigatorEmailServiceFactory.class),
        metrics,
        getBean(LixServiceFactory.class));
  }
}
