package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.factory.common.LssSavedLeadAccountParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.leadaccount.SalesAccountsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesAccountsService}
 */
public class SalesAccountsServiceFactory extends SimpleSingletonFactory<SalesAccountsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("saleAccountsService");

  @Config
  private static final class Cfg {
    @Required
    Integer maxSavedAccountLimitAllTiers;
  }

  @Import(clazz = LssSavedLeadAccountParSeqEspressoClientFactory.class)
  @Import(clazz = LixServiceFactory.class)

  @Override
  protected SalesAccountsService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssSavedLeadAccountParSeqEspressoClientFactory.class);
    LssSavedLeadAccountDB lssSavedLeadAccountDB = new LssSavedLeadAccountDB(parSeqEspressoClient);
    return new SalesAccountsService(lssSavedLeadAccountDB, getBean(LixServiceFactory.class),
        cfg.maxSavedAccountLimitAllTiers);
  }
}
