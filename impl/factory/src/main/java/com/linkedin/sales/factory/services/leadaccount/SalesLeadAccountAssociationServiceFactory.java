package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.factory.common.LssSavedLeadAccountParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesLeadAccountAssociationServiceFactory extends SimpleSingletonFactory<SalesLeadAccountAssociationService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("leadAccountAssociationService");

  @Import(clazz = LssSavedLeadAccountParSeqEspressoClientFactory.class)
  @Import(clazz = LixServiceFactory.class)

  @Override
  protected SalesLeadAccountAssociationService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssSavedLeadAccountParSeqEspressoClientFactory.class);
    LssSavedLeadAccountDB lssSavedLeadAccountDB = new LssSavedLeadAccountDB(parseqEspressoClient);
    return new SalesLeadAccountAssociationService(lssSavedLeadAccountDB, getBean(LixServiceFactory.class));
  }
}
