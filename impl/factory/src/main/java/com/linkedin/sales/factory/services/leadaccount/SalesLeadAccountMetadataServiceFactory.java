package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.factory.common.LssSavedLeadAccountParSeqEspressoClientFactory;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountMetadataService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesLeadAccountMetadataService}
 */
public class SalesLeadAccountMetadataServiceFactory extends SimpleSingletonFactory<SalesLeadAccountMetadataService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesLeadAccountMetadataService");

  @Import(clazz = LssSavedLeadAccountParSeqEspressoClientFactory.class)

  @Override
  protected SalesLeadAccountMetadataService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssSavedLeadAccountParSeqEspressoClientFactory.class);
    LssSavedLeadAccountDB lssSavedLeadAccountDB = new LssSavedLeadAccountDB(parseqEspressoClient);
    return new SalesLeadAccountMetadataService(lssSavedLeadAccountDB);
  }
}
