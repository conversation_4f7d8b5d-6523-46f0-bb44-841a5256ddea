package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.factory.common.LssLeadExtendedInfoParSeqEspressoClientFactory;
import com.linkedin.sales.service.leadaccount.SalesLeadEditableContactInfoService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesLeadEditableContactInfoService}
 */
public class SalesLeadEditableContactInfoServiceFactory extends SimpleSingletonFactory<SalesLeadEditableContactInfoService> {
    @SuppressWarnings("unused")
    private static final Scope SCOPE = Scope.ROOT.child("salesLeadEditableContactInfoService");

    @Import(clazz = LssLeadExtendedInfoParSeqEspressoClientFactory.class)

    @Override
    protected SalesLeadEditableContactInfoService createInstance(ConfigView view) {
        ParSeqEspressoClient parSeqEspressoClient = getBean(LssLeadExtendedInfoParSeqEspressoClientFactory.class);
        LssLeadExtendedInfoDB lssLeadExtendedInfoDB = new LssLeadExtendedInfoDB(parSeqEspressoClient);
        return new SalesLeadEditableContactInfoService(lssLeadExtendedInfoDB);
    }
}
