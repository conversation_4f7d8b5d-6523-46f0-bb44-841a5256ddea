package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.factory.common.LssLeadExtendedInfoParSeqEspressoClientFactory;
import com.linkedin.sales.service.leadaccount.SalesLeadProfileUnlockInfoService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesLeadProfileUnlockInfoService}
 */
public class SalesLeadProfileUnlockHistoryInfoFactory
    extends SimpleSingletonFactory<SalesLeadProfileUnlockInfoService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesLeadProfileUnlockHistoryInfoService");

  @Import(clazz = LssLeadExtendedInfoParSeqEspressoClientFactory.class)

  @Override
  protected SalesLeadProfileUnlockInfoService createInstance(ConfigView view) {
    ParSeqEspressoClient parSeqEspressoClient = getBean(LssLeadExtendedInfoParSeqEspressoClientFactory.class);
    LssLeadExtendedInfoDB lssLeadExtendedInfoDB = new LssLeadExtendedInfoDB(parSeqEspressoClient);
    return new SalesLeadProfileUnlockInfoService(lssLeadExtendedInfoDB);
  }
}
