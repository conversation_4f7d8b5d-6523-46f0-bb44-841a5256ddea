package com.linkedin.sales.factory.services.leadaccount;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.factory.common.LssSavedLeadAccountParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


public class SavedLeadServiceFactory extends SimpleSingletonFactory<SavedLeadService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("savedLeadService");

  @Config
  private static final class Cfg {
    @Required
    Integer maxSavedLeadLimitAllTiers;
  }

  @Import(clazz = LssSavedLeadAccountParSeqEspressoClientFactory.class)
  @Import(clazz = LixServiceFactory.class)

  @Override
  protected SavedLeadService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);
    ParSeqEspressoClient parseqEspressoClient = getBean(LssSavedLeadAccountParSeqEspressoClientFactory.class);
    LssSavedLeadAccountDB lssSavedLeadAccountDB = new LssSavedLeadAccountDB(parseqEspressoClient);
    return new SavedLeadService(lssSavedLeadAccountDB,
        getBean(LixServiceFactory.class), cfg.maxSavedLeadLimitAllTiers);
  }
}
