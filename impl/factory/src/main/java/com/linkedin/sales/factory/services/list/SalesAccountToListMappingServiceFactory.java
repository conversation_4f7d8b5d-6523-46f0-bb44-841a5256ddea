package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.service.list.SalesAccountToListMappingService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class to create {@link SalesAccountToListMappingService}
 */
public class SalesAccountToListMappingServiceFactory extends SimpleSingletonFactory<SalesAccountToListMappingService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesAccountToListMappingService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)

  @Override
  protected SalesAccountToListMappingService createInstance(ConfigView view) {
    return new SalesAccountToListMappingService(
        new LssListDB(getBean(LssListParSeqEspressoClientFactory.class)));
  }
}
