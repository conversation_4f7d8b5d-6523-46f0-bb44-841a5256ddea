package com.linkedin.sales.factory.services.list;

import com.linkedin.omni.utils.ambry.factories.AmbryDocumentServiceFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.list.SalesAdminCsvImportService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;
import java.util.concurrent.TimeUnit;


public class SalesAdminCsvImportServiceFactory extends SimpleSingletonFactory<SalesAdminCsvImportService> {

  private static final Scope SCOPE = Scope.ROOT.child("salesAdminCsvImportService");

  private static final String PREFIX_CSV_IMPORT = "csvImport";

  @Import(clazz = AmbryDocumentServiceFactory.class, prefix = PREFIX_CSV_IMPORT)
  @Import(clazz = SalesListCsvImportServiceFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @ConfigOverride(clazz = AmbryDocumentServiceFactory.class, prefix = PREFIX_CSV_IMPORT)
  @Override
  protected SalesAdminCsvImportService createInstance(ConfigView configView) {
    AmbryDocumentServiceFactory.Cfg csvImportAmbryOverrides = new AmbryDocumentServiceFactory.Cfg();
    csvImportAmbryOverrides.ambryAccountName = "sales-workflow";
    csvImportAmbryOverrides.ambryContainerName = "csv-data-sources";
    csvImportAmbryOverrides.mimeType = "text/csv";
    csvImportAmbryOverrides._ttlInSeconds = TimeUnit.DAYS.toSeconds(27);
    return new SalesAdminCsvImportService(
        configOverrideAndGetBean(AmbryDocumentServiceFactory.class, SCOPE.child(PREFIX_CSV_IMPORT), csvImportAmbryOverrides),
        getBean(SalesListCsvImportServiceFactory.class),
        getBean(LixServiceFactory.class),
        getBean(SalesSeatClientFactory.class));
  }
}
