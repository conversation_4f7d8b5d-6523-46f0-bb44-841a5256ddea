package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.service.list.SalesDefaultListService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class to create {@link com.linkedin.sales.service.list.SalesDefaultListService}
 */
public class SalesDefaultListServiceFactory extends SimpleSingletonFactory<SalesDefaultListService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesDefaultListService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)

  @Override
  protected SalesDefaultListService createInstance(ConfigView view) {
    return new SalesDefaultListService(new LssListDB(getBean(LssListParSeqEspressoClientFactory.class)));
  }
}
