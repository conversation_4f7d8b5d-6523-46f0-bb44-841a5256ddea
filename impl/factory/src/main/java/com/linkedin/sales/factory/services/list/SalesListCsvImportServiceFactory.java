package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.factory.client.salesinsights.CsvImportTaskClientFactory;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.service.list.SalesListCsvImportService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory class to create {@link SalesListCsvImportService}
 */
public class SalesListCsvImportServiceFactory extends SimpleSingletonFactory<SalesListCsvImportService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesListCsvImportService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = CsvImportTaskClientFactory.class)
  @Import(clazz = SalesListIdServiceFactory.class)
  @Import(clazz = SalesListServiceFactory.class)

  @Override
  protected SalesListCsvImportService createInstance(ConfigView view) {
    return new SalesListCsvImportService(
        new LssListDB(getBean(LssListParSeqEspressoClientFactory.class)),
        getBean(CsvImportTaskClientFactory.class),
        getBean(SalesListIdServiceFactory.class),
        getBean(SalesListServiceFactory.class));
  }
}
