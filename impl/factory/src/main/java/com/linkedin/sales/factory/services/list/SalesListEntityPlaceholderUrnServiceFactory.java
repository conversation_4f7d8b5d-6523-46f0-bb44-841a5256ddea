package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.factory.client.common.TorrentClientFactory;
import com.linkedin.sales.service.list.SalesListEntityPlaceholderUrnService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory to generate {@link SalesListEntityPlaceholderUrnService}
 */
public class SalesListEntityPlaceholderUrnServiceFactory
    extends SimpleSingletonFactory<SalesListEntityPlaceholderUrnService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesListEntityPlaceholderUrnService");

  @Import(clazz = TorrentClientFactory.class)
  @Override
  protected SalesListEntityPlaceholderUrnService createInstance(ConfigView view) {
    return new SalesListEntityPlaceholderUrnService(getBean(TorrentClientFactory.class));
  }
}
