package com.linkedin.sales.factory.services.list;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.acl.AclServiceDispatcherFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.list.SalesListEntityService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Created by hacao at 6/13/2018
 * Factory class to help create class {@link SalesListEntityService}
 */
public class SalesListEntityServiceFactory extends SimpleSingletonFactory<SalesListEntityService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesListEntityService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = AclServiceDispatcherFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = SalesListEntityPlaceholderUrnServiceFactory.class)

  @Override
  protected SalesListEntityService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssListParSeqEspressoClientFactory.class);
    LssListDB lssListDB = new LssListDB(parseqEspressoClient);
    return new SalesListEntityService(lssListDB, getBean(AclServiceDispatcherFactory.class),
        getBean(LixServiceFactory.class), getBean(SalesListEntityPlaceholderUrnServiceFactory.class));
  }
}
