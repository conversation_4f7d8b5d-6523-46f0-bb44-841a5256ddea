package com.linkedin.sales.factory.services.list;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.service.list.SalesListEntitySummaryService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Created by hacao at 6/25/18
 * Factory class for {@link SalesListEntitySummaryService}
 */
public class SalesListEntitySummaryServiceFactory extends SimpleSingletonFactory<SalesListEntitySummaryService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesListEntitySummaryService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = SalesListServiceFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)

  @Override
  protected SalesListEntitySummaryService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssListParSeqEspressoClientFactory.class);
    LssListDB lssListDB = new LssListDB(parseqEspressoClient);
    return new SalesListEntitySummaryService(lssListDB, getBean(SalesListServiceFactory.class),
        getBean(SalesSeatClientFactory.class));
  }
}