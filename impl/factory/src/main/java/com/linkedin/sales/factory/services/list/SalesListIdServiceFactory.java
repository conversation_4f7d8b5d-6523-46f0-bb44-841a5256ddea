package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.factory.client.common.TorrentClientFactory;
import com.linkedin.sales.service.list.SalesListIdService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory for {@link SalesListIdService}
 */
public class SalesListIdServiceFactory extends SimpleSingletonFactory<SalesListIdService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesListIdService");

  @Import(clazz = TorrentClientFactory.class)
  @Override
  protected SalesListIdService createInstance(ConfigView view) {
    return new SalesListIdService(
        getBean(TorrentClientFactory.class));
  }
}
