package com.linkedin.sales.factory.services.list;

import com.linkedin.lss.salesleadaccount.clients.TrackingClientFactory;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.client.integration.CrmPairingClientFactory;
import com.linkedin.sales.factory.client.integration.CrmSettingClientFactory;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.acl.AclServiceDispatcherFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.common.LocalizationHelperFactory;
import com.linkedin.sales.service.list.SalesListIdService;
import com.linkedin.sales.service.list.SalesListService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesListServiceFactory extends SimpleSingletonFactory<SalesListService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesListService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = SalesListIdServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = AclServiceDispatcherFactory.class)
  @Import(clazz = CrmPairingClientFactory.class)
  @Import(clazz = LocalizationHelperFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Import(clazz = TrackingClientFactory.class)
  @Import(clazz = SalesAccountToListMappingServiceFactory.class)
  @Import(clazz = CrmSettingClientFactory.class)
  @Override
  protected SalesListService createInstance(ConfigView view) {
    LssListDB lssListDB = new LssListDB(getBean(LssListParSeqEspressoClientFactory.class));
    SalesListIdService salesListIdService = getBean(SalesListIdServiceFactory.class);
    LssSharingDB lssSharingDB = new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class));
    return new SalesListService(lssListDB, salesListIdService, lssSharingDB, getBean(LixServiceFactory.class),
        getBean(AclServiceDispatcherFactory.class), getBean(CrmPairingClientFactory.class),
        getBean(LocalizationHelperFactory.class), getBean(SalesSeatClientFactory.class),
        getBean(TrackingClientFactory.class), getBean(SalesAccountToListMappingServiceFactory.class),
        getBean(CrmSettingClientFactory.class));
  }
}
