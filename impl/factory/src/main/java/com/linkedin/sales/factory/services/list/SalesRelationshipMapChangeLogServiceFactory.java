package com.linkedin.sales.factory.services.list;

import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.client.realtime.RealtimeDispatcherClientFactory;
import com.linkedin.sales.factory.common.LssListParSeqEspressoClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.acl.AclServiceDispatcherFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.list.SalesRelationshipMapChangeLogService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesRelationshipMapChangeLogServiceFactory
    extends SimpleSingletonFactory<SalesRelationshipMapChangeLogService> {

  private static final Scope SCOPE = Scope.ROOT.child("salesRelationshipMapChangeLogService");

  @Import(clazz = LssListParSeqEspressoClientFactory.class)
  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = AclServiceDispatcherFactory.class)
  @Import(clazz = RealtimeDispatcherClientFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesRelationshipMapChangeLogService createInstance(ConfigView view) {
    return new SalesRelationshipMapChangeLogService(new LssListDB(getBean(LssListParSeqEspressoClientFactory.class)),
        new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class)),
        getBean(AclServiceDispatcherFactory.class), getBean(RealtimeDispatcherClientFactory.class), getBean(
        LixServiceFactory.class)
        );
  }
}
