package com.linkedin.sales.factory.services.note;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssNoteDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.common.LssNoteParSeqEspressoClientFactory;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.service.note.SalesNoteService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class SalesNoteServiceFactory extends SimpleSingletonFactory<SalesNoteService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesNoteService");

  @Import(clazz = LssNoteParSeqEspressoClientFactory.class)
  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Override
  protected SalesNoteService createInstance(ConfigView view) {
    ParSeqEspressoClient lssNoteParseqEspressoClient = getBean(LssNoteParSeqEspressoClientFactory.class);
    ParSeqEspressoClient lssSharingParseqEspressoClient = getBean(LssSharingParSeqEspressoClientFactory.class);
    LssNoteDB lssNoteDB = new LssNoteDB(lssNoteParseqEspressoClient);
    LssSharingDB lssSharingDB = new LssSharingDB(lssSharingParseqEspressoClient);
    return new SalesNoteService(lssNoteDB, lssSharingDB);
  }
}
