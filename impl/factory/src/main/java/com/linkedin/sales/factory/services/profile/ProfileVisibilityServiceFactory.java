package com.linkedin.sales.factory.services.profile;

import com.linkedin.sales.factory.client.common.GraphDistancesClientFactory;
import com.linkedin.sales.factory.client.profile.MemberBadgesClientFactory;
import com.linkedin.sales.service.ProfileVisibilityService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class ProfileVisibilityServiceFactory extends SimpleSingletonFactory<ProfileVisibilityService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesProfileVisibilityService");

  @Import(clazz = GraphDistancesClientFactory.class)
  @Import(clazz = MemberBadgesClientFactory.class)
  @Override
  protected ProfileVisibilityService createInstance(ConfigView view) {
    return new ProfileVisibilityService(
        getBean(GraphDistancesClientFactory.class),
        getBean(MemberBadgesClientFactory.class)
    );
  }
}
