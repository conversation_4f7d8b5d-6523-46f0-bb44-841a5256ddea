package com.linkedin.sales.factory.services.profile;

import com.linkedin.noniterableprofileid.ProfileIdGeneratorFactory;
import com.linkedin.sales.factory.client.common.VectorClientFactory;
import com.linkedin.sales.factory.client.crm.CrmMappingClientFactory;
import com.linkedin.sales.factory.client.integration.CrmPairingClientFactory;
import com.linkedin.sales.factory.client.profile.ProfileClientFactory;
import com.linkedin.sales.factory.services.common.AdapterUrlFactoryFactory;
import com.linkedin.sales.factory.services.integration.SalesExternalizationServiceFactory;
import com.linkedin.sales.service.SalesNavigatorProfileAssociationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesNavigatorProfileAssociationServiceFactory extends SimpleSingletonFactory<SalesNavigatorProfileAssociationService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesNavigatorProfileAssociationService");

  @Import(clazz = CrmPairingClientFactory.class)
  @Import(clazz = ProfileClientFactory.class)
  @Import(clazz = AdapterUrlFactoryFactory.class)
  @Import(clazz = ProfileIdGeneratorFactory.class)
  @Import(clazz = VectorClientFactory.class)
  @Import(clazz = CrmMappingClientFactory.class)
  @Import(clazz = SalesExternalizationServiceFactory.class)
  @Override
  protected SalesNavigatorProfileAssociationService createInstance(ConfigView view) {
    return new SalesNavigatorProfileAssociationService(
        getBean(CrmPairingClientFactory.class),
        getBean(ProfileClientFactory.class),
        getBean(AdapterUrlFactoryFactory.class),
        getBean(ProfileIdGeneratorFactory.class),
        getBean(VectorClientFactory.class),
        getBean(CrmMappingClientFactory.class),
        getBean(SalesExternalizationServiceFactory.class)
    );
  }
}
