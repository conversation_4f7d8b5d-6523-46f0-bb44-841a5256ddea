package com.linkedin.sales.factory.services.profile;

import com.linkedin.sales.factory.client.common.SalesContractSettingsClientFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.client.profile.ProfileClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesAccountsServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesLeadAccountAssociationServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SavedLeadServiceFactory;
import com.linkedin.sales.factory.services.tracking.TrackingServiceFactory;
import com.linkedin.sales.service.SalesNavigatorSaveLeadService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesNavigatorSaveLeadServiceFactory extends SimpleSingletonFactory<SalesNavigatorSaveLeadService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesNavigatorSaveLeadService");

  @Import(clazz = SalesContractSettingsClientFactory.class)
  @Import(clazz = ProfileClientFactory.class)
  @Import(clazz = SavedLeadServiceFactory.class)
  @Import(clazz = SalesAccountsServiceFactory.class)
  @Import(clazz = SalesLeadAccountAssociationServiceFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Import(clazz = TrackingServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesNavigatorSaveLeadService createInstance(ConfigView view) {
    return new SalesNavigatorSaveLeadService(
        getBean(SalesSeatClientFactory.class),
        getBean(SalesContractSettingsClientFactory.class),
        getBean(ProfileClientFactory.class),
        getBean(SavedLeadServiceFactory.class),
        getBean(SalesAccountsServiceFactory.class),
        getBean(SalesLeadAccountAssociationServiceFactory.class),
        getBean(TrackingServiceFactory.class),
        getBean(LixServiceFactory.class)
    );
  }
}
