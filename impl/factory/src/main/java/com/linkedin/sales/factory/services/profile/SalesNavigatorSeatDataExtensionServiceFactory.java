package com.linkedin.sales.factory.services.profile;

import com.linkedin.sales.factory.client.crm.CrmUserMappingClientFactory;
import com.linkedin.sales.factory.client.integration.CrmPairingClientFactory;
import com.linkedin.sales.service.SalesNavigatorSeatDataExtensionService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory for {@link SalesNavigatorSeatDataExtensionService}.
 */
public class SalesNavigatorSeatDataExtensionServiceFactory extends SimpleSingletonFactory<SalesNavigatorSeatDataExtensionService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesNavigatorSeatDataExtensionService");

  @Import(clazz = CrmUserMappingClientFactory.class)
  @Import(clazz = CrmPairingClientFactory.class)
  @Override
  protected SalesNavigatorSeatDataExtensionService createInstance(ConfigView view) {
    return new SalesNavigatorSeatDataExtensionService(
        getBean(CrmUserMappingClientFactory.class),
        getBean(CrmPairingClientFactory.class)
    );
  }
}
