package com.linkedin.sales.factory.services.recentviews;

import com.linkedin.sales.ds.db.LssRecentViewsDB;
import com.linkedin.sales.factory.common.SalesParSeqEspressoLd3ClientFactory;
import com.linkedin.sales.factory.common.SalesParSeqEspressoMt3ClientFactory;
import com.linkedin.sales.service.SalesRecentActivitiesService;
import com.linkedin.util.factory.EnvInfoFinderFactory;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesRecentActivitiesServiceFactory extends SimpleSingletonFactory<SalesRecentActivitiesService> {

  private static final Scope SCOPE = Scope.ROOT.child("salesRecentViewsService");

  @Import(clazz = EnvInfoFinderFactory.class)
  @Import(clazz = SalesParSeqEspressoMt3ClientFactory.class)
  @Import(clazz = SalesParSeqEspressoLd3ClientFactory.class)

  @Override
  protected SalesRecentActivitiesService createInstance(ConfigView view) {
    LssRecentViewsDB lssRecentViewsDB;
    String envInfo = getBean(EnvInfoFinderFactory.class).getEnvInfo().getFabricGroup();
    if (envInfo.toLowerCase().contains("ei")) {
      lssRecentViewsDB = new LssRecentViewsDB(getBean(SalesParSeqEspressoMt3ClientFactory.class));
    } else {
      lssRecentViewsDB = new LssRecentViewsDB(getBean(SalesParSeqEspressoLd3ClientFactory.class));
    }
    return new SalesRecentActivitiesService(lssRecentViewsDB);
  }
}
