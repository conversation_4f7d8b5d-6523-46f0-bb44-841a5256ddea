package com.linkedin.sales.factory.services.seattransfer;

import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferRequestsClientFactory;
import com.linkedin.sales.factory.services.seattransfer.salescustomlists.SalesCustomListsTransferServiceFactory;
import com.linkedin.sales.factory.services.seattransfer.salesentitynotes.SalesEntityNotesTransferServiceFactory;
import com.linkedin.sales.factory.services.seattransfer.salesleadaccountassociations.SalesLeadAccountAssociationsTransferServiceFactory;
import com.linkedin.sales.factory.services.seattransfer.salesaccounts.SalesAccountsTransferServiceFactory;
import com.linkedin.sales.factory.services.seattransfer.salesleads.SalesLeadsTransferServiceFactory;
import com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class LssSeatTransferActionsServiceFactory extends SimpleSingletonFactory<LssSeatTransferActionsService> {
  private static final Scope SCOPE = Scope.ROOT.child("lssSeatTransferActionsService");

  @Import(clazz = SalesSeatTransferRequestsClientFactory.class)
  @Import(clazz = SalesLeadsTransferServiceFactory.class)
  @Import(clazz = SalesCustomListsTransferServiceFactory.class)
  @Import(clazz = SalesEntityNotesTransferServiceFactory.class)
  @Import(clazz = SalesLeadAccountAssociationsTransferServiceFactory.class)
  @Import(clazz = SalesAccountsTransferServiceFactory.class)
  @Override
  protected LssSeatTransferActionsService createInstance(ConfigView view) {
    return new LssSeatTransferActionsService(
        getBean(SalesSeatTransferRequestsClientFactory.class),
        getBean(SalesLeadsTransferServiceFactory.class),
        getBean(SalesEntityNotesTransferServiceFactory.class),
        getBean(SalesLeadAccountAssociationsTransferServiceFactory.class),
        getBean(SalesAccountsTransferServiceFactory.class),
        getBean(SalesCustomListsTransferServiceFactory.class)
    );
  }
}
