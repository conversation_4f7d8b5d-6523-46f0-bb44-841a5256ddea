package com.linkedin.sales.factory.services.seattransfer.salesaccounts;

import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonServiceFactory;
import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferCopyAssociationsClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SalesAccountsServiceFactory;
import com.linkedin.sales.service.seattransfer.salesaccounts.SalesAccountsTransferService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesAccountsTransferServiceFactory extends SimpleSingletonFactory<SalesAccountsTransferService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesAccountsTransferService");

  @Import(clazz = SalesLeadAccountCommonServiceFactory.class)
  @Import(clazz = SalesSeatTransferCopyAssociationsClientFactory.class)
  @Import(clazz = SalesAccountsServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesAccountsTransferService createInstance(ConfigView view) {
    SalesAccountsTransferServiceFactory.Cfg cfg = view.fill(SalesAccountsTransferServiceFactory.Cfg.class);
    return new SalesAccountsTransferService(
        getBean(SalesLeadAccountCommonServiceFactory.class),
        getBean(SalesSeatTransferCopyAssociationsClientFactory.class),
        cfg.maxSavedAccountLimitAllTiers,
        getBean(LixServiceFactory.class)
      );
  }

  @Config
  private static final class Cfg {
    @Required
    Integer maxSavedAccountLimitAllTiers;
  }
}
