package com.linkedin.sales.factory.services.seattransfer.salescustomlists;

import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferCopyAssociationsClientFactory;
import com.linkedin.sales.factory.services.list.SalesListEntityServiceFactory;
import com.linkedin.sales.factory.services.list.SalesListServiceFactory;
import com.linkedin.sales.service.seattransfer.salescustomlists.SalesCustomListsTransferService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesCustomListsTransferServiceFactory extends SimpleSingletonFactory<SalesCustomListsTransferService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesCustomListsTransferService");

  @Import(clazz = SalesSeatTransferCopyAssociationsClientFactory.class)
  @Import(clazz = SalesListServiceFactory.class)
  @Import(clazz = SalesListEntityServiceFactory.class)
  @Override
  protected SalesCustomListsTransferService createInstance(ConfigView view) {
    return new SalesCustomListsTransferService(
        getBean(SalesSeatTransferCopyAssociationsClientFactory.class),
        getBean(SalesListServiceFactory.class),
        getBean(SalesListEntityServiceFactory.class)
    );
  }
}
