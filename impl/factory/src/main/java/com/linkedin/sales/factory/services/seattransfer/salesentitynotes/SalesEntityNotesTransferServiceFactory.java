package com.linkedin.sales.factory.services.seattransfer.salesentitynotes;

import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferCopyAssociationsClientFactory;
import com.linkedin.sales.factory.services.note.SalesNoteServiceFactory;
import com.linkedin.sales.service.seattransfer.salesentitynotes.SalesEntityNotesTransferService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesEntityNotesTransferServiceFactory extends SimpleSingletonFactory<SalesEntityNotesTransferService> {
  private static final Scope SCOPE = Scope.ROOT.child("entityNotesTransferService");

  @Import(clazz = SalesNoteServiceFactory.class)
  @Import(clazz = SalesSeatTransferCopyAssociationsClientFactory.class)
  @Override
  protected SalesEntityNotesTransferService createInstance(ConfigView view) {
    Cfg cfg = view.fill(Cfg.class);
    return new SalesEntityNotesTransferService(
        getBean(SalesNoteServiceFactory.class),
        getBean(SalesSeatTransferCopyAssociationsClientFactory.class),
        cfg.noteBatchSize
    );
  }

  @Config
  private static final class Cfg {
    Integer noteBatchSize = 200;
  }
}
