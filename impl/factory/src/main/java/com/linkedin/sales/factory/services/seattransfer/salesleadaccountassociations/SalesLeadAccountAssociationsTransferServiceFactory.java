package com.linkedin.sales.factory.services.seattransfer.salesleadaccountassociations;

import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonServiceFactory;
import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferCopyAssociationsClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.service.seattransfer.salesleadaccountassociations.SalesLeadAccountAssociationsTransferService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesLeadAccountAssociationsTransferServiceFactory
    extends SimpleSingletonFactory<SalesLeadAccountAssociationsTransferService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesLeadAccountAssociationsTransferService");

  @Import(clazz = SalesLeadAccountCommonServiceFactory.class)
  @Import(clazz = SalesSeatTransferCopyAssociationsClientFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesLeadAccountAssociationsTransferService createInstance(ConfigView view) {
    return new SalesLeadAccountAssociationsTransferService(
        getBean(SalesLeadAccountCommonServiceFactory.class),
        getBean(SalesSeatTransferCopyAssociationsClientFactory.class),
        getBean(LixServiceFactory.class)
    );
  }
}
