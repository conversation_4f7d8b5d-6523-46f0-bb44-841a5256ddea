package com.linkedin.sales.factory.services.seattransfer.salesleads;

import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonServiceFactory;
import com.linkedin.sales.factory.client.seattransfer.SalesSeatTransferCopyAssociationsClientFactory;
import com.linkedin.sales.factory.services.common.LixServiceFactory;
import com.linkedin.sales.factory.services.leadaccount.SavedLeadServiceFactory;
import com.linkedin.sales.service.seattransfer.salesleads.SalesLeadsTransferService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class SalesLeadsTransferServiceFactory extends SimpleSingletonFactory<SalesLeadsTransferService> {
  private static final Scope SCOPE = Scope.ROOT.child("salesLeadsTransferService");

  @Import(clazz = SalesSeatTransferCopyAssociationsClientFactory.class)
  @Import(clazz = SalesLeadAccountCommonServiceFactory.class)
  @Import(clazz = SavedLeadServiceFactory.class)
  @Import(clazz = LixServiceFactory.class)
  @Override
  protected SalesLeadsTransferService createInstance(ConfigView view) {
    return new SalesLeadsTransferService(
        getBean(SalesSeatTransferCopyAssociationsClientFactory.class),
        getBean(SalesLeadAccountCommonServiceFactory.class),
        getBean(SavedLeadServiceFactory.class),
        getBean(LixServiceFactory.class)
    );
  }
}
