package com.linkedin.sales.factory.services.settings;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.factory.common.LssSeatSettingParSeqEspressoClientFactory;
import com.linkedin.sales.service.settings.SalesMobileSettingsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesMobileSettingsService}
 */
public class SalesMobileSettingsServiceFactory extends SimpleSingletonFactory<SalesMobileSettingsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesMobileSettingsService");

  @Import(clazz = LssSeatSettingParSeqEspressoClientFactory.class)
  @Override
  protected SalesMobileSettingsService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssSeatSettingParSeqEspressoClientFactory.class);
    LssSeatSettingDB lssSeatSettingDB = new LssSeatSettingDB(parseqEspressoClient);
    return new SalesMobileSettingsService(lssSeatSettingDB);
  }
}
