package com.linkedin.sales.factory.services.settings;

import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.factory.common.LssSeatSettingParSeqEspressoClientFactory;
import com.linkedin.sales.service.settings.SalesSeatSettingsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


/**
 * Factory class for {@link SalesSeatSettingsService}
 */
public class SalesSeatSettingsServiceFactory extends SimpleSingletonFactory<SalesSeatSettingsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesSeatSettingsService");

  @Import(clazz = LssSeatSettingParSeqEspressoClientFactory.class)
  @Override
  protected SalesSeatSettingsService createInstance(ConfigView view) {
    ParSeqEspressoClient parseqEspressoClient = getBean(LssSeatSettingParSeqEspressoClientFactory.class);
    LssSeatSettingDB lssSeatSettingDB = new LssSeatSettingDB(parseqEspressoClient);
    return new SalesSeatSettingsService(lssSeatSettingDB);
  }
}
