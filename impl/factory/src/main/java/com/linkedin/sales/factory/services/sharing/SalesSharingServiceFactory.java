package com.linkedin.sales.factory.services.sharing;

import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.factory.common.LssSharingParSeqEspressoClientFactory;
import com.linkedin.sales.factory.services.acl.AclServiceDispatcherFactory;
import com.linkedin.sales.service.sharing.SalesSharingService;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

public class SalesSharingServiceFactory extends SimpleSingletonFactory<SalesSharingService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesSharingService");

  // TODO: Make sure ld number is aligned with prod once availble.
  @Import(clazz = LssSharingParSeqEspressoClientFactory.class)
  @Import(clazz = AclServiceDispatcherFactory.class)

  @Override
  protected SalesSharingService createInstance(ConfigView view) {
    LssSharingDB lssSharingDB = new LssSharingDB(getBean(LssSharingParSeqEspressoClientFactory.class));
    AclServiceDispatcher aclServiceDispatcher = getBean(AclServiceDispatcherFactory.class);
    return new SalesSharingService(lssSharingDB, aclServiceDispatcher);
  }
}
