package com.linkedin.sales.factory.services.subscriptionbenefits;

import com.linkedin.sales.factory.client.common.SalesCreditGrantsClientFactory;
import com.linkedin.sales.factory.client.common.SalesSeatClientFactory;
import com.linkedin.sales.factory.db.LssSavedLeadAccountDBFactory;
import com.linkedin.sales.factory.services.common.SalesContractServiceFactory;
import com.linkedin.sales.service.SalesSubscriptionBenefitsService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;

/**
 * Factory for creating {@link SalesSubscriptionBenefitsService}
 */
public class SalesSubscriptionBenefitsServiceFactory extends SimpleSingletonFactory<SalesSubscriptionBenefitsService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesSubscriptionBenefitsService");

  @Override
  @Import(clazz = LssSavedLeadAccountDBFactory.class)
  @Import(clazz = SalesSeatClientFactory.class)
  @Import(clazz = SalesCreditGrantsClientFactory.class)
  @Import(clazz = SalesContractServiceFactory.class)
  protected SalesSubscriptionBenefitsService createInstance(ConfigView view) {
    return new SalesSubscriptionBenefitsService(
        getBean(SalesSeatClientFactory.class),
        getBean(LssSavedLeadAccountDBFactory.class),
        getBean(SalesCreditGrantsClientFactory.class),
        getBean(SalesContractServiceFactory.class));
  }
}
