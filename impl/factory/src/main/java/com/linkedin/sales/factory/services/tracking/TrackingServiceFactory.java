package com.linkedin.sales.factory.services.tracking;

import com.linkedin.sales.factory.common.SingletonTrackingProducerFactory;
import com.linkedin.sales.service.TrackingService;
import com.linkedin.tracker.producer.TrackingProducer;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Config;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.annotations.Required;
import com.linkedin.util.factory.cfg.ConfigView;

public class TrackingServiceFactory extends SimpleSingletonFactory<TrackingService> {
  @SuppressWarnings("unused")
  private static final Scope SCOPE = Scope.ROOT.child("salesTrackingService");

  @Import(clazz = SingletonTrackingProducerFactory.class)

  @Config
  private static class Cfg {
    @Required
    String machineName;
    @Required
    String envName;
    @Required
    String appName;
  }

  @Override
  protected TrackingService createInstance(ConfigView view) {
    TrackingServiceFactory.Cfg cfg = view.fill(Cfg.class);
    TrackingProducer trackingProducer = getBean(SingletonTrackingProducerFactory.class);
    return new TrackingService(
        trackingProducer,
        cfg.machineName,
        cfg.envName,
        cfg.appName);
  }
}
