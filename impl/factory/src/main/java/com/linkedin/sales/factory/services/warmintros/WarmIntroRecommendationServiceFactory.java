package com.linkedin.sales.factory.services.warmintros;

import com.linkedin.sales.ds.db.LssWarmIntrosDB;
import com.linkedin.sales.factory.common.LssWarmIntrosParSeqEspressoClientFactory;
import com.linkedin.sales.service.warmintros.WarmIntroRecommendationService;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.SimpleSingletonFactory;
import com.linkedin.util.factory.annotations.Import;
import com.linkedin.util.factory.cfg.ConfigView;


public class WarmIntroRecommendationServiceFactory extends SimpleSingletonFactory<WarmIntroRecommendationService> {
  private static final Scope SCOPE = Scope.ROOT.child("warmIntroRecommendationService");

  @Import(clazz = LssWarmIntrosParSeqEspressoClientFactory.class)
  @Override
  protected WarmIntroRecommendationService createInstance(ConfigView configView) {
    LssWarmIntrosDB lssWarmIntrosDB =
        new LssWarmIntrosDB(getBean(LssWarmIntrosParSeqEspressoClientFactory.class));
    return new WarmIntroRecommendationService(lssWarmIntrosDB);
  }
}
