import com.github.spotbugs.snom.SpotBugsTask


ext.apiMpName = 'lss-mt-api'
ext.apiMpApiDir = 'lss-mt-api'
ext.apiProject = project(':rest-api')

apply plugin: 'li-pegasus2'

pegasus.main.idlOptions.addIdlItem(['com.linkedin.sales', 'com.linkedin.enterprise'])
project.ext.setProperty('rest.model.compatibility', 'ignore')

// The following transitive dependencies bring a wrong implementation of javax.ws.rs.core. implementation, which is used by azkaban.client.AzkabanClient,
// resulting in "java.lang.NoSuchMethodError: 'void javax.ws.rs.core.MultivaluedMap.addAll"
configurations.all { configuration ->
  exclude group: 'com.sun.jersey.contribs', module: 'jersey-guice'
  exclude group: 'com.sun.jersey', module: 'jersey-client'
  exclude group: 'com.sun.jersey', module: 'jersey-core'
  exclude group: 'com.sun.jersey', module: 'jersey-json'
  exclude group: 'com.sun.jersey', module: 'jersey-server'
}

dependencies {

  api project(':impl:ds')
  api project(":impl:service$scalaSuffix")
  api spec.external.'spotbugs-annotations'
  api spec.product.container.'pegasus-d2-client-factory'
  api spec.product.'eis-backend-api'.'rest-api'
  api spec.product.'eis-backend-api'.'rest-api-dataTemplate'
  api spec.product.'handles-midtier-api'.'handles-midtier-api-restClient'
  api spec.product.'handles-midtier-api'.'handles-midtier-api-dataTemplate'
  api spec.product.talent.'talent-common-pegasus'
  api spec.product.'restli-gateway-util'.'restli-gateway-util'
  api spec.product.'ep-apps-connector'.'dataextension-plugin'
  api spec.product.'ep-apps-connector'.'dataextension-plugin-dataTemplate'
  api spec.product.'lss-reporting'.'lss-reporting-grpc-api-protoImplementation'

  // For decorating and formatting notifications for Flagship (go/commsrenderingplugins):
  dataModel spec.product.'notifications-service'.'api-dataTemplate'
  api spec.product.'notifications-service'.api
  dataModel spec.product.'comms-rendering-mt'.'pluginsApiDataTemplate'
  api spec.product.'comms-rendering-mt'.'plugins-api'
  api spec.product.'comms-rendering-mt'.'comms-rendering-mt-helpers'
  api spec.product.'comms-rendering-mt'.'comms-rendering-mt-helpers-factory'

  api spec.product.'resource-identity'.'resource-identity-urn-generator'

  // For seat transfer
  dataModel spec.product.'ownership-transfer-lib'.'models-dataTemplate'

  // add test compile
  testImplementation project(':test:test-fwk')
  testImplementation spec.external.'mockito-inline'
  testImplementation spec.external.testng
}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.jvmArgs = ['-Xms8g', '-Xmx8g']
}
tasks.withType(SpotBugsTask).configureEach {
  maxHeapSize = '4g'
}
// BEGIN: grpc migration script changes
apply plugin: 'li-grpc-bridged-service'
dependencies {
  protoDataModel spec.product.'lss-mt-api'.'lss-mt-api-protoDataModel'
  implementation spec.product.'lss-mt-api'.'lss-mt-api-restClient'
  implementation spec.product.'util'.'util-factory'
  implementation spec.product.'grpc-infra'.'si-grpc-impl'
}
// END: grpc migration script changes

