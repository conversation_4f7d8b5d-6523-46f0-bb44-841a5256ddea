package com.linkedin.sales.rest.activities;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.KeyValueResource;
import com.linkedin.restli.server.resources.ResourceContextHolder;
import com.linkedin.sales.service.SalesActivityTotalsJobService;
import com.linkedin.salesactivities.SalesActivityTotal;
import javax.inject.Inject;


/**
 * This resource is used to get the list of activities and their completion counts for a seat holder.
 * This resource only supports finder with a dummy id right now.
 * <AUTHOR>
 */

@RestLiCollection(name = "salesActivityTotals", namespace = "com.linkedin.sales", keyName = "id")
public class SalesActivityTotalsResource extends ResourceContextHolder
    implements KeyValueResource<Long, SalesActivityTotal> {
  private final SalesActivityTotalsJobService _salesActivityTotalsJobService;

  @Inject
  public SalesActivityTotalsResource(SalesActivityTotalsJobService salesActivityTotalsJobService) {
    _salesActivityTotalsJobService = salesActivityTotalsJobService;
  }

  /**
   * find all the activities and their completion counts for a certain seat holder
   * @param seat seat urn of the user who completed the activities
   * @param contract contract urn of the user. Caller is responsible to make sure SeatUrn belongs to the given contract.
   * @return the collection of all the activities and their completion counts
   */
  @Finder("seatAndContract")
  public Task<BasicCollectionResult<SalesActivityTotal>> findBySeatAndContract(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) final SeatUrn seat,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) final ContractUrn contract) {

    return _salesActivityTotalsJobService.getSalesActivityTotals(seat, contract);
  }

  /**
   * find all the activities and their completion counts for a certain member. This will only be called from a service
   * outside of Sales Navigator.
   * @param member member urn of the user who completed the activities
   * @return the collection of all the activities and their completion counts
   */
  @Finder("member")
  public Task<BasicCollectionResult<SalesActivityTotal>> findByMember(
      @QueryParam(value = "member", typeref = com.linkedin.common.MemberUrn.class) final MemberUrn member) {
    return _salesActivityTotalsJobService.getSalesActivityTotals(member);
  }

  /**
   * find all the activities and their completion counts for a contract.
   * This will be consumed by voyager-api-premium to evaluate Free Trial Extension(FTE) promos.
   * @param contract contract urn
   * @return the collection of all the activities and their completion counts
   */
  @Finder("contract")
  public Task<BasicCollectionResult<SalesActivityTotal>> findByContract(
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) final ContractUrn contract) {
    return _salesActivityTotalsJobService.getSalesActivityTotals(contract);
  }
}