package com.linkedin.sales.rest.alerts;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.alerts.SalesAlertsService;
import com.linkedin.salesalerts.Alert;
import com.linkedin.salesalerts.AlertEntity;
import com.linkedin.salesalerts.AlertKey;
import com.linkedin.salesalerts.AlertOrdering;
import com.linkedin.salesalerts.AlertType;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * The resource to delete/find recipient agnostic alerts in Sales Navigator, e.g., alerts on account or lead pages
 * that will be the same to all users no matter who the viewer is.
 */
@RestLiCollection(name = "salesAlerts", keyName = "alertKey", namespace = "com.linkedin.sales")
@ReadOnly({"alertId"})
@CreateOnly({"entity", "alertType", "content", "createdAt"})
public class SalesAlertsResource extends ComplexKeyResourceTaskTemplate<AlertKey, EmptyRecord, Alert> {

  private final SalesAlertsService _salesAlertsService;
  @Inject
  public SalesAlertsResource(SalesAlertsService salesAlertsService) {
    _salesAlertsService = salesAlertsService;
  }

  /**
   * delete an alert, will be used by sales-api when the related contents cannot be decorated successfully, e.g., article is removed
   * @param key key including the entity urn and alert Id to uniquely identify the alert to delete
   * @return the update response to tell if the delete succeeds
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(ComplexResourceKey<AlertKey, EmptyRecord> key) {
    return _salesAlertsService.deleteAlert(key.getKey());
  }

  /**
   * find all or some types of the alerts on this entity, sorted by given order
   * @param alertEntity the entity that the alerts belong to, a union of memberUrn, organizationUrn or salesListUrn
   * @param alertTypes the set of alert types to get, by default, an empty array will be put and all types will be fetched
   * @param alertOrdering the sorting criteria of the retrieved alerts, by default, sort by creation time in descending order
   * @param pagingContext the paging context
   * @return the collection of alerts under this entity
   */
  @Finder("criteria")
  public Task<BasicCollectionResult<Alert>> findByCriteria(
      @QueryParam(value = "alertEntity") AlertEntity alertEntity,
      @QueryParam(value = "alertTypes") @Optional("[]") AlertType[] alertTypes,
      @QueryParam(value = "alertOrdering") @Optional("CREATION_TIME") AlertOrdering alertOrdering,
      @PagingContextParam PagingContext pagingContext) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : DEFAULT_COUNT;
    return _salesAlertsService.findByCriteria(alertEntity, alertTypes, alertOrdering, start, count);
  }
}