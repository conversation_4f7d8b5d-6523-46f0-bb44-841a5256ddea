package com.linkedin.sales.rest.analytics;

import com.linkedin.common.ClosedTimeRange;
import com.linkedin.common.Time;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.lighthouse.client.queue.QueueType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTemplate;
import com.linkedin.sales.service.SalesAnalyticsExportJobServiceV2;
import com.linkedin.salesanalytics.SalesAnalyticsExportJob;
import com.linkedin.salesanalytics.SalesAnalyticsStatus;
import java.util.concurrent.ExecutionException;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.SalesAnalyticsConstants.*;

/**
 * This resource handles Sales Navigator Application Platform analytics data export job
 * The resource defines Actions for exporting different types of Sales Navigator usage data,
 * and provides methods to get latest status of data export job and cancel any data export job
 */
@RestLiCollection(name = "salesAnalyticsExportJobs", namespace = "com.linkedin.salesanalytics")
public class SalesAnalyticsExportJobResource extends CollectionResourceTemplate<Long, SalesAnalyticsExportJob> {
  private SalesAnalyticsExportJobServiceV2 _salesAnalyticsExportJobService;

  @Inject
  public SalesAnalyticsExportJobResource(SalesAnalyticsExportJobServiceV2 salesAnalyticsExportJobService) {
    _salesAnalyticsExportJobService = salesAnalyticsExportJobService;
  }
  /**
   * Export Sales Analytics Activity data for a time range
   * Criteria for valid range:
   * 1. Range between startAt and endAt cannot exceed 90 days
   * 2. Range between startAt and endAt cannot be less than one hour
   * 3. User cannot request data that is more than 2 years old
   * 4. start and end date have to be past date and at least one day back from the midnight of the day when the request was made
   * 5. startAt cannot be larger than or equal to endAt
   * 6. start and end date cannot be earlier than the date when the contract was first created
   * @param startAt Start date of exported data
   * @param endAt End date of exported data
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return A new SalesAnalyticsExportJob with jobId and status as "ENQUEUED"
   */
  @Action(name = "exportActivityData")
  public Task<SalesAnalyticsExportJob> exportActivityData(
      @ActionParam(value = "startAt", typeref = Time.class) Long startAt,
      @ActionParam(value = "endAt", typeref = Time.class) Long endAt,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
      ) {
    Task<Long> jobId = _salesAnalyticsExportJobService.createSalesAnalyticsExportJob(
        QueueType.SalesAnalyticsActivity(),
        startAt,
        endAt,
        contractUrn,
        memberUrn
    );
    return jobId.map(id -> new SalesAnalyticsExportJob()
        .setStatus(SalesAnalyticsStatus.ENQUEUED)
        .setId(id));
  }

  /**
   * Export Sales Analytics Activity Outcome data for a time range
   * Criteria for valid range:
   * 1. Range between startAt and endAt cannot exceed 90 days
   * 2. Range between startAt and endAt cannot be less than one hour
   * 3. User cannot request data that is more than 2 years old
   * 4. start and end date have to be past date and at least one day back from the midnight of the day when the request was made
   * 5. startAt cannot be larger than or equal to endAt
   * 6. start and end date cannot be earlier than the date when the contract was first created
   * @param startAt Start date of exported data
   * @param endAt End date of exported data
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return A new SalesAnalyticsExportJob with jobId and status as "ENQUEUED"
   */
  @Action(name = "exportActivityOutcomeData")
  public Task<SalesAnalyticsExportJob> exportActivityOutcomeData(
      @ActionParam(value = "startAt", typeref = Time.class) Long startAt,
      @ActionParam(value = "endAt", typeref = Time.class) Long endAt,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
  ) {
    Task<Long> jobId = _salesAnalyticsExportJobService.createSalesAnalyticsExportJob(
        QueueType.SalesAnalyticsActivityOutcome(),
        startAt,
        endAt,
        contractUrn,
        memberUrn
    );
    return jobId.map(id -> new SalesAnalyticsExportJob()
        .setStatus(SalesAnalyticsStatus.ENQUEUED)
        .setId(id));
  }

  /**
   * Export Sales Analytics Seat data for a time range
   * Criteria for valid range:
   * 1. Range between startAt and endAt cannot exceed 90 days
   * 2. Range between startAt and endAt cannot be less than one hour
   * 3. User cannot request data that is more than 2 years old
   * 4. start and end date have to be past date and at least one day back from the midnight of the day when the request was made
   * 5. startAt cannot be larger than or equal to endAt
   * 6. start and end date cannot be earlier than the date when the contract was first created
   * @param startAt Start date of exported data
   * @param endAt End date of exported data
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return A new SalesAnalyticsExportJob with jobId and status as "ENQUEUED"
   */
  @Action(name = "exportSeatData")
  public Task<SalesAnalyticsExportJob> exportSeatData(
      @ActionParam(value = "startAt", typeref = Time.class) Long startAt,
      @ActionParam(value = "endAt", typeref = Time.class) Long endAt,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
  ) {
    Task<Long> jobId = _salesAnalyticsExportJobService.createSalesAnalyticsExportJob(
        QueueType.SalesAnalyticsSeat(),
        startAt,
        endAt,
        contractUrn,
        memberUrn
    );
    return jobId.map(id -> new SalesAnalyticsExportJob()
        .setStatus(SalesAnalyticsStatus.ENQUEUED)
        .setId(id));
  }

  /**
   * Export Sales Analytics Tag data for a time range
   * Criteria for valid range:
   * 1. Range between startAt and endAt cannot exceed 90 days
   * 2. Range between startAt and endAt cannot be less than one hour
   * 3. User cannot request data that is more than 2 years old
   * 4. start and end date have to be past date and at least one day back from the midnight of the day when the request was made
   * 5. startAt cannot be larger than or equal to endAt
   * 6. start and end date cannot be earlier than the date when the contract was first created
   * @param startAt Start date of exported data
   * @param endAt End date of exported data
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return A new SalesAnalyticsExportJob with jobId and status as "ENQUEUED"
   */
  @Action(name = "exportTagData")
  public Task<SalesAnalyticsExportJob> exportTagData(
      @ActionParam(value = "startAt", typeref = Time.class) Long startAt,
      @ActionParam(value = "endAt", typeref = Time.class) Long endAt,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
  ) {
    Task<Long> jobId = _salesAnalyticsExportJobService.createSalesAnalyticsExportJob(
        QueueType.SalesAnalyticsTag(),
        startAt,
        endAt,
        contractUrn,
        memberUrn
    );
    return jobId.map(id -> new SalesAnalyticsExportJob()
        .setStatus(SalesAnalyticsStatus.ENQUEUED)
        .setId(id));
  }

  /**
   * Export Sales Analytics Member Identity Mapping data for a time range
   * Criteria for valid range:
   * 1. Range between startAt and endAt cannot exceed 90 days
   * 2. Range between startAt and endAt cannot be less than one hour
   * 3. User cannot request data that is more than 2 years old
   * 4. start and end date have to be past date and at least one day back from the midnight of the day when the request was made
   * 5. startAt cannot be larger than or equal to endAt
   * 6. start and end date cannot be earlier than the date when the contract was first created
   * @param startAt Start date of exported data
   * @param endAt End date of exported data
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return A new SalesAnalyticsExportJob with jobId and status as "ENQUEUED"
   */
  @Action(name = "exportMemberIdentityMappingData")
  public Task<SalesAnalyticsExportJob> exportMemberIdentityMappingData(
      @ActionParam(value = "startAt", typeref = Time.class) Long startAt,
      @ActionParam(value = "endAt", typeref = Time.class) Long endAt,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
  ) {
    Task<Long> jobId = _salesAnalyticsExportJobService.createSalesAnalyticsExportJob(
        QueueType.SalesAnalyticsMemberIdentityMapping(),
        startAt,
        endAt,
        contractUrn,
        memberUrn
    );
    return jobId.map(id -> new SalesAnalyticsExportJob()
        .setStatus(SalesAnalyticsStatus.ENQUEUED)
        .setId(id));
  }

  /**
   * Fetches the SalesAnalyticsExportJob object corresponding to the job id with the latest status
   * @param jobId id representing a job
   * @param contractUrn Contract URN of the user
   * @param memberUrn Member URN of the user
   * @return a sales analytics export job
   */
  @RestMethod.Get
  public Task<SalesAnalyticsExportJob> get(
      Long jobId,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @QueryParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn memberUrn
  ) throws ExecutionException, InterruptedException {
    return _salesAnalyticsExportJobService.getSalesAnalyticsExportJob(memberUrn, contractUrn, jobId);
  }

  /**
   * Retrieve activity data availability for a contract. Before users make request to export Sales Navigator activity data,
   * they want to query this data availability endpoint first to see in what time range the data is available for them to export
   * @param contractUrn Contract URN of the user
   * @return A SalesAnalyticsDataAvailability object with data availability info showing in what time range the data is available to export
   */
  @Action(name = "retrieveActivityDataAvailability")
  public Task<ClosedTimeRange> retrieveActivityDataAvailability(
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn
  ) {
    return _salesAnalyticsExportJobService.retrieveDataAvailability(ACTIVITY_TABLE, contractUrn);
  }

  /**
   * Retrieve activity outcome data availability for a contract. Before users make request to export Sales Navigator activity outcome data,
   * they want to query this data availability endpoint first to see in what time range the data is available for them to export
   * @param contractUrn Contract URN of the user
   * @return A SalesAnalyticsDataAvailability object with data availability info showing in what time range the data is available to export
   */
  @Action(name = "retrieveActivityOutcomeDataAvailability")
  public Task<ClosedTimeRange> retrieveActivityOutcomeDataAvailability(
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn
  ) {
    return _salesAnalyticsExportJobService.retrieveDataAvailability(ACTIVITY_OUTCOME_TABLE, contractUrn);
  }

  /**
   * Retrieve seat data availability for a contract. Before users make request to export Sales Navigator seat data,
   * they want to query this data availability endpoint first to see in what time range the data is available for them to export
   * @param contractUrn Contract URN of the user
   * @return A SalesAnalyticsDataAvailability object with data availability info showing in what time range the data is available to export
   */
  @Action(name = "retrieveSeatDataAvailability")
  public Task<ClosedTimeRange> retrieveSeatDataAvailability(
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn
  ) {
    return _salesAnalyticsExportJobService.retrieveDataAvailability(SEAT_TABLE, contractUrn);
  }
}
