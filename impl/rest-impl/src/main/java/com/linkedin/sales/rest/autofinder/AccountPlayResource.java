package com.linkedin.sales.rest.autofinder;

import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.autofinder.AccountPlayService;
import com.linkedin.salesautofinder.AccountPlay;
import com.linkedin.salesautofinder.AccountPlayKey;
import java.util.HashMap;
import java.util.Map;
import javax.inject.Inject;


/**
 * Resource that is responsible for management of AccountPlay. An AccountPlay is a specific search
 * that is run on behalf of a seat on an account.
 */
@RestLiCollection(name = "accountPlays", keyName = "key", namespace = "com.linkedin.sales")
@ReadOnly({"id"})
@CreateOnly({"playType", "personaLocalId"})
public class AccountPlayResource extends ComplexKeyResourceTaskTemplate<AccountPlayKey, EmptyRecord, AccountPlay> {
  @Inject
  private AccountPlayService _accountPlayService;

  /**
   * Batch create AccountPlay
   * @param batchCreateRequest
   * @return
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay>> batchCreate(
      BatchCreateRequest<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay> batchCreateRequest) {
    return _accountPlayService.batchCreate(batchCreateRequest).map(BatchCreateResult::new);
  }

  /**
   * Find AccountPlays by account
   * @param seatUrn - seat urn
   * @param organizationUrn - organization urn
   * @param pagingContext
   * @return
   */
  @Finder("findByAccount")
  public Task<BasicCollectionResult<AccountPlay>> findByAccount(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      @QueryParam(value = "organization", typeref = com.linkedin.common.OrganizationUrn.class) OrganizationUrn organizationUrn,
      @PagingContextParam PagingContext pagingContext) {
    return _accountPlayService.findByAccount(seatUrn, organizationUrn, pagingContext.getStart(), pagingContext.getCount());
  }

  /**
   * Batch update AccountPlay
   * @param batchPatchRequest
   * @return
   */
  @RestMethod.BatchPartialUpdate
  public Task<BatchUpdateResult<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay>> batchUpdate(
      BatchPatchRequest<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay> batchPatchRequest) {
    return _accountPlayService.batchPartialUpdate(batchPatchRequest).map(result -> {
      Map<ComplexResourceKey<AccountPlayKey, EmptyRecord>, UpdateResponse> successMap = new HashMap<>();
      Map<ComplexResourceKey<AccountPlayKey, EmptyRecord>, RestLiServiceException> errorMap = new HashMap<>();

      batchPatchRequest.getData().keySet().forEach(key -> {
        AccountPlayKey accountPlayKey = key.getKey();
        UpdateResponse updateResponse = result.getOrDefault(accountPlayKey,
            new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        if (updateResponse.getStatus().equals(HttpStatus.S_200_OK)) {
          successMap.put(key, updateResponse);
        } else {
          errorMap.put(key, new RestLiServiceException(updateResponse.getStatus()));
        }
      });

      return new BatchUpdateResult<>(successMap, errorMap);
    });
  }
}
