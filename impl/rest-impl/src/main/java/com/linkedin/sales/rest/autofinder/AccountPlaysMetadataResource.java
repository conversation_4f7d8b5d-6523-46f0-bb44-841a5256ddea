package com.linkedin.sales.rest.autofinder;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.autofinder.AccountPlaysMetadataService;
import com.linkedin.salesautofinder.AccountPlaysMetadata;
import com.linkedin.salesautofinder.AccountPlaysMetadataKey;
import javax.inject.Inject;

/**
 * Resource that is responsible for management of AccountPlaysMetadata
 */
@RestLiCollection(name = "accountPlaysMetadata", keyName = "key", namespace = "com.linkedin.sales")
public class AccountPlaysMetadataResource
    extends ComplexKeyResourceTaskTemplate<AccountPlaysMetadataKey, EmptyRecord, AccountPlaysMetadata> {

  @Inject
  private AccountPlaysMetadataService _accountPlaysMetadataService;

  /**
   * Get AccountPlaysMetadata by key
   * @param key resource key
   * @return AccountPlaysMetadata
   */
  @RestMethod.Get
  public Task<AccountPlaysMetadata> get(ComplexResourceKey<AccountPlaysMetadataKey, EmptyRecord> key) {
    return _accountPlaysMetadataService.get(key.getKey());
  }

  /**
   * Create a new AccountPlaysMetadata
   * @param entity the entity to create
   * @return
   */
  @RestMethod.Create
  public Task<CreateResponse> create(AccountPlaysMetadata entity) {
    return _accountPlaysMetadataService.create(entity);
  }

  /**
   * Update an existing AccountPlaysMetadata
   * @param key resource key
   * @param patch the updated entity
   * @param isUndoDismiss determines the update action is for dismiss or undo dismiss
   * @param memberUrn memberUrn of the lead to be dismissed or undismissed
   * @return
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> partialUpdate(
      ComplexResourceKey<AccountPlaysMetadataKey, EmptyRecord> key,
      PatchRequest<AccountPlaysMetadata> patch,
      @com.linkedin.restli.server.annotations.Optional("false") @QueryParam("isUndoDismiss")
          Boolean isUndoDismiss,
      @com.linkedin.restli.server.annotations.Optional
          @QueryParam(value = "memberUrn", typeref = com.linkedin.common.MemberUrn.class)
          com.linkedin.common.urn.MemberUrn memberUrn) {

    return _accountPlaysMetadataService.partialUpdate(key.getKey(), patch, isUndoDismiss, memberUrn);
  }
}
