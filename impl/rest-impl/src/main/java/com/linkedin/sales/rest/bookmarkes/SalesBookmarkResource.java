package com.linkedin.sales.rest.bookmarkes;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchFinderResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.NoMetadata;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.BatchFinder;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.bookmark.SalesBookmarkService;
import com.linkedin.salesbookmark.Bookmark;
import com.linkedin.salesbookmark.BookmarkContent;
import com.linkedin.salesbookmark.BookmarkType;
import java.util.Collections;
import javax.inject.Inject;


/**
 * Resource that is responsible on all user bookmark calls
 * <AUTHOR>
 */
@RestLiCollection(name = "salesBookmarks", keyName = "id", namespace = "com.linkedin.sales")
@ReadOnly({"createdAt", "id"})
@CreateOnly({"content", "contract", "expiredAt", "owner", "type"})
public class SalesBookmarkResource extends CollectionResourceTaskTemplate<Long, Bookmark> {
  @Inject
  private SalesBookmarkService _salesBookmarkService;

  /**
   * create a new bookmark
   * @param bookmark the bookmark that wishes to create
   * @return create response to indicate if the creation succeeds
   */
  @RestMethod.Create
  public Task<CreateResponse> create(Bookmark bookmark) {
    return _salesBookmarkService.createBookmark(bookmark);
  }

  /**
   * delete an existing bookmark
   * @param id the bookmark id the seat needs to delete
   * @param seat the seatUrn of requester, must match the owner of the bookmark
   * @return update response to tell if the deletion succeeds
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(Long id,
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat) {
    return _salesBookmarkService.deleteBookmark(id, seat).map(UpdateResponse::new);
  }

  /**
   * finder that can help get all bookmarks that match the provided contents and type, result is ordered by the
   * argument contents positions. Expired bookmarks will be excluded from result and deleted.
   * @param seat the seatUrn of requester
   * @param type specific BookmarkType
   * @param contents the contentUrns of bookmarks
   * @return a collection of bookmarks that seat has access that matches the given type and one content
   */
  @BatchFinder(value = "contents", batchParam = "contents")
  public Task<BatchFinderResult<BookmarkContent, Bookmark, NoMetadata>> batchFindByContents(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "type") BookmarkType type,
      @QueryParam(value = "contents") BookmarkContent[] contents) {
    return _salesBookmarkService.batchFindByContents(seat, type, contents)
        .map(result -> new BatchFinderResult<>(result, Collections.emptyMap()));
  }

  /**
   * finder that can help get all bookmarks that are owned by a given seat and type. Expired bookmarks will be
   * excluded from result and deleted.
   * @param pagingContext the paging context
   * @param seat the seatUrn of requester
   * @param type specific BookmarkType
   * @return a collection of bookmarks that seat has access that matches given condition
   */
  @Finder("seatAndType")
  public Task<BasicCollectionResult<Bookmark>> findBySeatAndType(
      @PagingContextParam PagingContext pagingContext,
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "type") BookmarkType type) {
    return _salesBookmarkService.findBySeatAndType(seat, type, pagingContext.getStart(), pagingContext.getCount());
  }
}
