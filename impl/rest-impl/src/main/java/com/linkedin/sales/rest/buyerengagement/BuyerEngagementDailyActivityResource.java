package com.linkedin.sales.rest.buyerengagement;

import com.linkedin.buyerengagement.BuyerEngagementDailyActivity;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.CollectionResourceTemplate;
import com.linkedin.sales.service.buyerengagement.BuyerEngagementService;
import javax.inject.Inject;


/**
 * {@link BuyerEngagementDailyActivity} is account based engagement segment metrics for any buyer activities related to an account.
 * BuyerSegment contains facets like member's function, seniority, and geo, etc.
 * This resource helps to find buyer's segment details from buyer activities like page/pixel view visits for a given time window.
 * Current use case is to decorate side panel on Sales Navigator when users click buyer alerts notification card.
 */
@RestLiCollection(name = "buyerEngagementDailyActivities", namespace = "com.linkedin.sales")
public class BuyerEngagementDailyActivityResource
    extends CollectionResourceTemplate<String, BuyerEngagementDailyActivity> {

  private final BuyerEngagementService _buyerEngagementService;

  @Inject
  public BuyerEngagementDailyActivityResource(BuyerEngagementService buyerEngagementService) {
    _buyerEngagementService = buyerEngagementService;
  }

  /**
   * Find all unique segment details for members from one organization who initiate the buyer engagement activities towards the target organization.
   *
   * @param buyerOrganization the organization of the members who initiate the buyer engagement activities towards the seller organization
   * @param sellerOrganization the organization of Sales Navigator seat holder, which the buyer engagement activity were acted on.
   * @param startDate the left bound corresponding to the time window query
   * @param endDate the right bound corresponding to the time window query
   * @return a list of segments detail(geo, function, seniority, etc) that buyer engagement activities were initiated
   */
  @Finder("buyerOrganization")
  public Task<BasicCollectionResult<BuyerEngagementDailyActivity>> findByBuyerOrganization(
      @QueryParam(value = "buyerOrganization", typeref = com.linkedin.common.OrganizationUrn.class) final OrganizationUrn buyerOrganization,
      @QueryParam(value = "sellerOrganization", typeref = com.linkedin.common.OrganizationUrn.class) final OrganizationUrn sellerOrganization,
      @QueryParam(value = "startDate", typeref = com.linkedin.common.Time.class) final long startDate,
      @QueryParam(value = "endDate", typeref = com.linkedin.common.Time.class) final long endDate,
      @PagingContextParam PagingContext pagingContext) {

    return _buyerEngagementService.findByBuyerOrganization(buyerOrganization, sellerOrganization, startDate, endDate,
        pagingContext);
  }
}
