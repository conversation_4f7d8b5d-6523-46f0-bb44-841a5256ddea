package com.linkedin.sales.rest.buyerengagement;

import com.linkedin.buyerengagement.BuyerIntentTrend;
import com.linkedin.buyerengagement.BuyerIntentTrendKey;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.buyerengagement.BuyerIntentService;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * Provides Buyer Intent Score data between a buyer and a seller company. So how likely a specific buyer org
 * will purchase from a specific seller org. Provides the latest raw score, percentage score change over 7 days,
 * and percentage score change over 30 days.
 */
@RestLiCollection(name = "buyerIntentTrend", namespace = "com.linkedin.sales", keyName = "key")
public class BuyerIntentTrendResource
    extends ComplexKeyResourceTaskTemplate<BuyerIntentTrendKey, EmptyRecord, BuyerIntentTrend> {

  private final BuyerIntentService _buyerIntentService;

  @Inject
  public BuyerIntentTrendResource(BuyerIntentService buyerIntentService) {
    _buyerIntentService = buyerIntentService;
  }

  /**
   * Fetches the Buyer Intent Score and trend data using the given buyer and seller organization urns.
   * As an optimization we are limiting which buyer/seller org pairs get scored.
   * Refer <a href="http://go/buyerintentc2c">here</a> to see what the latest pair generation strategy is.
   * @param key BuyerIntentTrendKey
   * @param sellerContract Identifies the contract of the seller (Sales Navigator user) who will be viewing the BuyerIntentTrend.
   *                       The field is optional for backward compatibility. It is added to ramp a contract based lix.
   *                       Passing this value does not influence the BuyerIntentTrend result of buyer-seller company pair.
   * @return BuyerIntentTrend
   */
  @RestMethod.Get
  public Task<BuyerIntentTrend> get(ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> key,
      @QueryParam(value = "sellerContract", typeref = com.linkedin.common.ContractUrn.class) @Optional ContractUrn sellerContract) {
    return _buyerIntentService.getTrend(key, sellerContract);
  }

  /**
   * Batch fetches Buyer Intent data using the given keys. See the get documentation for more information.
   * @param keys Set of BuyerIntentTrendKeys for which the BuyerIntentTrend needs to be fetched.
   * @param sellerContract Identifies the contract of the seller (Sales Navigator user) who will be viewing the BuyerIntentTrend.
   *                       The field is optional for backward compatibility. It is added to ramp a contract based lix.
   *                       Passing this value does not influence the BuyerIntentTrend result of buyer-seller company pair.
   * @return BuyerIntentTrend for the input keys.
   */
  @RestMethod.BatchGet
  public Task<Map<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>, BuyerIntentTrend>> batchGet(
      Set<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>> keys,
      @QueryParam(value = "sellerContract", typeref = com.linkedin.common.ContractUrn.class) @Optional ContractUrn sellerContract) {
    return _buyerIntentService.batchGetTrends(keys, sellerContract);
  }
}