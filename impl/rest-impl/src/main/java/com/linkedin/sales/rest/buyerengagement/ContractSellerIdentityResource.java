package com.linkedin.sales.rest.buyerengagement;

import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.buyerengagement.ContractSellerIdentityService;
import java.util.UUID;
import javax.inject.Inject;
import pegasus.com.linkedin.buyerengagement.ContractSellerIdentity;


/**
 * This is the resource file for ContractSellerIdentity
 */
@RestLiCollection(name = "contractSellerIdentities", namespace = "com.linkedin.sales")
public class ContractSellerIdentityResource
    extends CollectionResourceTaskTemplate<Long, ContractSellerIdentity> {

  private final ContractSellerIdentityService _contractSellerIdentityService;

  @Inject
  public ContractSellerIdentityResource(ContractSellerIdentityService contractSellerIdentityService) {
    _contractSellerIdentityService = contractSellerIdentityService;
  }

  /**
   * Get a ContractSellerIdentity record
   *
   * @param contractId the contract id
   * @return a ContractSellerIdentity record, or HttpStatus.S_404_NOT_FOUND
   */
  @RestMethod.Get
  public Task<ContractSellerIdentity> get(Long contractId) {
    return _contractSellerIdentityService.getContractSellerIdentity(contractId);
  }

  /**
   * Add a product for a Contract Seller Identity record
   * @param contractUrn the contract urn
   * @param product the product to be added
   * @param seatUrn seat identifier who is performing the action.
   * @param sessionId the session id
   * @return ActionResult with the product id (Generated UUID)
   */
  @Action(name = "addProduct")
  public Task<ActionResult<String>> addProduct(
      @ActionParam(value = "contractUrn", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "product") SellerIdentityProduct product,
      @ActionParam(value = "seatUrn", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      @ActionParam(value = "sessionId") @Optional String sessionId) {
    return _contractSellerIdentityService.addProduct(contractUrn, seatUrn, product, sessionId == null ? UUID.randomUUID().toString() : sessionId);
  }

  /**
   * Update a product for a Contract Seller Identity record
   * @param contractUrn the contract urn
   * @param product the product to be updated
   * @param seatUrn seat identifier who is performing the action.
   * @return ActionResult with status code only
   */
  @Action(name = "updateProduct")
  public Task<ActionResult<Void>> updateProduct(
      @ActionParam(value = "contractUrn", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "product") SellerIdentityProduct product,
      @ActionParam(value = "seatUrn", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn) {
    return _contractSellerIdentityService.updateProduct(contractUrn, seatUrn, product);
  }

  /**
   * Remove a product for a Contract Seller Identity record
   * @param contractUrn the contract urn
   * @param productId the product id to be deleted
   * @return ActionResult with status code only
   */
  @Action(name = "removeProduct")
  public Task<ActionResult<Void>> removeProduct(
      @ActionParam(value = "contractUrn", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contractUrn,
      @ActionParam(value = "productId") String productId) {
    return _contractSellerIdentityService.removeProduct(contractUrn, productId);
  }
}