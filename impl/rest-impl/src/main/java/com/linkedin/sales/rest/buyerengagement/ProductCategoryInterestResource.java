package com.linkedin.sales.rest.buyerengagement;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.buyerengagement.ProductCategoryInterest;
import com.linkedin.buyerengagement.ProductCategoryInterestKey;
import com.linkedin.sales.service.buyerengagement.ProductCategoryInterestService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * This is the resource file for Product Category Interest.
 * It supports basic operations like create, update or delete a product category interest record, as well as batch operations.
 * Users could also get all the categories or find interest records by category.
 */
@RestLiCollection(name = "productCategoryInterests", keyName = "key", namespace = "com.linkedin.sales")
@ReadOnly({"interestId"})
public class ProductCategoryInterestResource
    extends ComplexKeyResourceTaskTemplate<ProductCategoryInterestKey, EmptyRecord, ProductCategoryInterest> {

  private final ProductCategoryInterestService _productCategoryInterestService;
  @Inject
  public ProductCategoryInterestResource(ProductCategoryInterestService productCategoryInterestService) {
    _productCategoryInterestService = productCategoryInterestService;
  }

  /**
   * Create a batch of product category interest records for a user.
   * @param interestBatchCreateRequest the list of interest records to be created
   * @return batch create result indicating if the creation for each interest record succeeds
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest>> batchCreate(
      BatchCreateRequest<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest> interestBatchCreateRequest) {
    return _productCategoryInterestService.batchCreateInterests(interestBatchCreateRequest.getInput())
        .map(BatchCreateResult::new);
  }

  /**
   * Partial update a batch of product category interest records for a user.
   * @param interestBatchPatchRequest the patches of the list of interest records to be updated
   * @return batch update result indicating if the partial update for each interest record succeeds
   */
  @RestMethod.BatchPartialUpdate
  public Task<BatchUpdateResult<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest>> batchUpdate(
      BatchPatchRequest<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest> interestBatchPatchRequest) {
    return _productCategoryInterestService.batchPartialUpdateInterests(interestBatchPatchRequest.getData())
        .map(resultMap -> {
          // build two maps, for success and error cases
          Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, UpdateResponse> successMap = new HashMap<>();
          Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, RestLiServiceException> errorMap = new HashMap<>();
          interestBatchPatchRequest.getData().keySet().forEach(resourceKey -> {
            // find from response map by key
            ProductCategoryInterestKey interestKey = resourceKey.getKey();
            CompoundKey compoundKey = _productCategoryInterestService.toCompoundKey(interestKey);
            UpdateResponse response = resultMap.get(compoundKey);
            if (response == null) {
              // if target record not found, put 500
              errorMap.put(resourceKey, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
            } else if (resultMap.get(compoundKey).getStatus() != HttpStatus.S_200_OK) {
              // expect 200 only. if not, put other error status
              errorMap.put(resourceKey, new RestLiServiceException(response.getStatus()));
            } else {
              // return 200, success.
              successMap.put(resourceKey, response);
            }
          });
          return new BatchUpdateResult<>(successMap, errorMap);
        });
  }

  /**
   * Delete a batch of product category interest records for a user
   * @param batchDeleteRequest the list of interest keys to be deleted
   * @return batch update result indicating if the deletion for each interest record succeeds
   */
  @RestMethod.BatchDelete
  public Task<BatchUpdateResult<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest>> batchDelete(
      BatchDeleteRequest<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, ProductCategoryInterest> batchDeleteRequest) {
    List<ProductCategoryInterestKey> keyList = batchDeleteRequest.getKeys().stream().map(ComplexResourceKey::getKey)
        .collect(Collectors.toList());
    return _productCategoryInterestService.batchDeleteInterests(keyList)
        .map(resultMap -> {
          // build two maps, for success and error cases
          Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, UpdateResponse> successMap = new HashMap<>();
          Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, RestLiServiceException> errorMap = new HashMap<>();
          batchDeleteRequest.getKeys().forEach(resourceKey -> {
            // find from response map by key
            CompoundKey compoundKey = _productCategoryInterestService.toCompoundKey(resourceKey.getKey());
            UpdateResponse response = resultMap.get(compoundKey);
            if (response == null) {
              // even target not found, should return 404. for null case there is other error.
              errorMap.put(resourceKey, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
            } else if (resultMap.get(compoundKey).getStatus() != HttpStatus.S_204_NO_CONTENT) {
              // other error cases. most common is 404.
              errorMap.put(resourceKey, new RestLiServiceException(response.getStatus()));
            } else {
              // return 204, success.
              successMap.put(resourceKey, response);
            }
          });
          return new BatchUpdateResult<>(successMap, errorMap);
        });
  }

  /**
   * Find all product category interest records per category of a user.
   * @param seat seat urn of the user
   * @param categoryNames the names of target categories. If this parameter is omitted, return records for all categories.
   * @param pagingContext the paging context
   * @return a collection of product category interest records
   */
  @Finder("seat")
  public Task<BasicCollectionResult<ProductCategoryInterest>> findBySeat(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "categoryNames") @Optional("[]") String[] categoryNames,
      @PagingContextParam PagingContext pagingContext
  ) {
    // if page context is omitted, use the default.
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : DEFAULT_COUNT;
    return _productCategoryInterestService.findInterests(seat, categoryNames, start, count)
        .map(BasicCollectionResult::new);
  }
}
