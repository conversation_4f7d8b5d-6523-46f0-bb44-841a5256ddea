package com.linkedin.sales.rest.buyerengagement;

import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.buyerengagement.SeatSellerIdentityKey;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.PathKeys;
import com.linkedin.restli.server.ResourceLevel;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.PathKeysParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.buyerengagement.SalesSellerIdentityService;
import com.linkedin.sales.service.buyerengagement.SellerIdentityProductHelperService;
import java.util.UUID;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * This is the resource file for Seat Seller Identity.
 * It supports get() and update() method, as well as a finder by contract method.
 */
@RestLiCollection(name = "salesSellerIdentities", keyName = SalesSellerIdentityResource.KEY_NAME, namespace = "com.linkedin.sales")
public class SalesSellerIdentityResource
    extends ComplexKeyResourceTaskTemplate<SeatSellerIdentityKey, EmptyRecord, SeatSellerIdentity> {

  public static final String KEY_NAME = "key";
  private final SalesSellerIdentityService _salesSellerIdentityService;
  private final SellerIdentityProductHelperService _sellerIdentityProductHelperService;

  @Inject
  public SalesSellerIdentityResource(SalesSellerIdentityService salesSellerIdentityService,
      SellerIdentityProductHelperService sellerIdentityProductHelperService) {
    _salesSellerIdentityService = salesSellerIdentityService;
    _sellerIdentityProductHelperService = sellerIdentityProductHelperService;
  }
  /**
   * Get a seat's Seller Identity record
   * @param key resource key, containing contract urn and seat urn
   * @return a SeatSellerIdentity record, or HttpStatus.S_404_NOT_FOUND
   */
  @Override
  @RestMethod.Get
  public Task<SeatSellerIdentity> get(ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> key) {
    return _salesSellerIdentityService.getSellerIdentity(key.getKey().getContract(), key.getKey().getSeat());
  }

  /**
   * Create or update a Seller Identity record
   * @param key resource key, containing contract urn and seat urn
   * @param identity a full Seat Seller Identity record
   * @return update response indicating whether the record was created or updated successfully,
   *           HttpStatus.S_201_CREATED for created; HttpStatus.S_200_OK for updated.
   */
  @Override
  @RestMethod.Update
  public Task<UpdateResponse> update(ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> key, SeatSellerIdentity identity) {
    return _salesSellerIdentityService.updateSellerIdentity(key.getKey().getContract(), key.getKey().getSeat(), identity);
  }

  /**
   * Create or partial update a Seller Identity record
   * @param key resource key, containing contract urn and seat urn
   * @param patch value to update the SeatSellerIdentity record
   * @return update response indicating whether the record was created or updated successfully,
   *           HttpStatus.S_201_CREATED for created; HttpStatus.S_200_OK for updated.
   */
  @Override
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> key,
      PatchRequest<SeatSellerIdentity> patch) {
    return _salesSellerIdentityService.updateSellerIdentity(key.getKey().getContract(), key.getKey().getSeat(), patch);
  }

  /**
   * Find all seats' Seller Identity records under the same contract
   * @param contract the urn of the contract
   * @param pagingContext the paging context
   * @return a collection of Seller Identity records
   */
  @Finder("contract")
  public Task<BasicCollectionResult<SeatSellerIdentity>> findByContract(
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @PagingContextParam PagingContext pagingContext
  ) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : DEFAULT_COUNT;
    return _salesSellerIdentityService.findSellerIdentities(contract, start, count)
        .map(BasicCollectionResult::new);
  }

  /**
   * Update the default product of a Seller Identity record
   * @param pathKeys resource key, containing contract urn and seat urn
   * @param productId the product id to be updated as default product
   * @return ActionResult with status code only
   */
  @Action(name = "updateDefaultProduct", resourceLevel = ResourceLevel.ENTITY)
  public Task<ActionResult<Void>> updateDefaultProduct(
      @PathKeysParam PathKeys pathKeys,
      @ActionParam(value = "productId") String productId) {
    ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> seatSellerIdentityKey = pathKeys.get(SalesSellerIdentityResource.KEY_NAME);
    return _sellerIdentityProductHelperService.updateDefaultProduct(seatSellerIdentityKey.getKey().getContract(),
        seatSellerIdentityKey.getKey().getSeat(), productId);
  }

  /**
   * Add a product for a Seller Identity record
   * @param pathKeys resource key, containing contract urn and seat urn
   * @param product the product to be added
   * @param sessionId the session id (it's optional for backward compatability and it won't be null for new calls)
   * @return ActionResult with the product id (Generated UUID)
   */
  @Action(name = "addProduct", resourceLevel = ResourceLevel.ENTITY)
  public Task<ActionResult<String>> addProduct(@PathKeysParam PathKeys pathKeys,
      @ActionParam(value = "product") SellerIdentityProduct product, @ActionParam(value = "sessionId") @Optional String sessionId) {
    ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> seatSellerIdentityKey = pathKeys.get(SalesSellerIdentityResource.KEY_NAME);
    return _sellerIdentityProductHelperService.addProduct(seatSellerIdentityKey.getKey().getContract(),
        seatSellerIdentityKey.getKey().getSeat(), product, sessionId == null ? UUID.randomUUID().toString() : sessionId);
  }

  /**
   * Update a product for a Seller Identity record
   * @param pathKeys resource key, containing contract urn and seat urn
   * @param product the product to be updated
   * @return ActionResult with status code only
   */
  @Action(name = "updateProduct", resourceLevel = ResourceLevel.ENTITY)
  public Task<ActionResult<Void>> updateProduct(
      @PathKeysParam PathKeys pathKeys,
      @ActionParam(value = "product") SellerIdentityProduct product) {
    ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> seatSellerIdentityKey = pathKeys.get(SalesSellerIdentityResource.KEY_NAME);
    return _sellerIdentityProductHelperService.updateProduct(seatSellerIdentityKey.getKey().getContract(),
        seatSellerIdentityKey.getKey().getSeat(), product);
  }

  /**
   * Remove a product from a Seller Identity record
   * @param pathKeys resource key, containing contract urn and seat urn
   * @param productId the product id to be deleted
   * @return ActionResult with status code only
   */
  @Action(name = "removeProduct", resourceLevel = ResourceLevel.ENTITY)
  public Task<ActionResult<Void>> removeProduct(
      @PathKeysParam PathKeys pathKeys,
      @ActionParam(value = "productId") String productId) {
    ComplexResourceKey<SeatSellerIdentityKey, EmptyRecord> seatSellerIdentityKey = pathKeys.get(SalesSellerIdentityResource.KEY_NAME);
    return _sellerIdentityProductHelperService.removeProduct(seatSellerIdentityKey.getKey().getContract(),
        seatSellerIdentityKey.getKey().getSeat(), productId);
  }
}
