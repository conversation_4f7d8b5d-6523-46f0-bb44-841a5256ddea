package com.linkedin.sales.rest.coach;

import com.google.common.collect.Lists;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.sales.service.coach.SalesCoachConversationService;
import com.linkedin.salescoach.ChatHistoryMetadata;
import com.linkedin.salescoach.ChatMessage;
import com.linkedin.salescoach.SalesAIChatHistoryKey;
import com.linkedin.salescoach.SalesAIChatHistory;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import javax.inject.Inject;


/**
 * This is the resource class for maintaining the conversation history between a member with the sales coach.
 */
@RestLiCollection(name = "salesCoachConversation", keyName = "key", namespace = "com.linkedin.sales")
public class SalesCoachConversationResource
    extends ComplexKeyResourceTaskTemplate<SalesAIChatHistoryKey, EmptyRecord, SalesAIChatHistory> {


  private final SalesCoachConversationService _salesCoachConversationService;

  @Inject
  public SalesCoachConversationResource(SalesCoachConversationService salesCoachConversationService) {
    _salesCoachConversationService = salesCoachConversationService;
  }

  /**
   * Fetch the conversation history given a {@link SalesAIChatHistoryKey} which holds the seatUrn and sessionId of the conversation.
   *
   * @param key The {@link SalesAIChatHistoryKey} key representing memberId's Sales Navigator seat and the sessionId of a chat history.
   * @return The conversation history {@link SalesAIChatHistory} given a {@link SalesAIChatHistoryKey}.
   */
  @RestMethod.Get
  public Task<SalesAIChatHistory> get(final ComplexResourceKey<SalesAIChatHistoryKey, EmptyRecord> key) {
    SalesAIChatHistoryKey salesAIChatHistoryKey = key.getKey();
    SeatUrn seatUrn = salesAIChatHistoryKey.getSeatUrn();
    String sessionId = salesAIChatHistoryKey.getSessionId();
    return _salesCoachConversationService.get(seatUrn, sessionId);
  }

  /**
   * Add a new chat messages to the conversation history. Messages content are immutable and cannot be updated.
   * We add new messages to the end of the chat history.
   *
   * We expect either chatMessages or chatHistoryMetadata (or both) to be present. Otherwise, no action will be taken.
   *
   * @param seatUrn The {@link SeatUrn} representing memberId's Sales Navigator seat.
   * @param sessionId The unique sessionId that represents the given conversation history.
   * @param chatMessages List of {@link ChatMessage} to be added to the conversation history.
   * @param chatHistoryMetadata The {@link ChatHistoryMetadata} to be added to the conversation history.
   * @return True if the chat messages are successfully added to the conversation history.
   */
  @Action(name = "addSalesAIChatMessages")
  public Task<Boolean> addSalesAIChatMessages(
      @ActionParam(value = "seatUrn", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      @ActionParam(value = "sessionId") String sessionId,
      @ActionParam(value = "chatMessages") @Optional ChatMessage[] chatMessages,
      @ActionParam(value = "chatHistoryMetadata") @Optional ChatHistoryMetadata chatHistoryMetadata) {

    return _salesCoachConversationService.addSalesAIChatMessages(seatUrn, sessionId, chatMessages, chatHistoryMetadata);
  }

  /**
   * Fetch the last K messages written as part of the conversation history wrapped in a singleton list of {@link SalesAIChatHistory}.
   * K represents the number of messages authored by both MEMBER and SYSTEM.
   *
   * @param seatUrn The {@link SeatUrn} representing memberId's Sales Navigator seat.
   * @param sessionId The unique sessionId that represents the given conversation history.
   * @param k The number of messages to be fetched.
   * @return The list of last K messages stored per given sessionId and seatUrn.
   */
  @Finder("lastKMessages")
  public Task<BasicCollectionResult<SalesAIChatHistory>> lastKMessages(
      @QueryParam(value = "seatUrn", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      @QueryParam(value = "sessionId") String sessionId,
      @QueryParam(value = "k") int k) {

    return _salesCoachConversationService.getLastKMessages(seatUrn, sessionId, k)
        .map(salesAIChatHistory -> new BasicCollectionResult<>(Lists.newArrayList(salesAIChatHistory)));
  }

}

