package com.linkedin.sales.rest.colleagues;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesColleaguesService;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salescolleagues.ColleagueRelationship;
import com.linkedin.salescolleagues.ColleagueRelationshipKey;
import com.linkedin.salescolleagues.RelationshipType;
import javax.inject.Inject;


/**
 * The resource to add/remove/find colleague relationships
 * <AUTHOR>
 */
@RestLiCollection(
    name = "salesColleagueRelationships",
    keyName = "key",
    namespace = "com.linkedin.salescolleagues"
)
public class SalesColleagueRelationshipResource
    extends ComplexKeyResourceTaskTemplate<ColleagueRelationshipKey, EmptyRecord, ColleagueRelationship> {

  @Inject
  SalesColleaguesService _salesColleaguesService;

  /**
   * Adds a manager relationship: fromMember reports to toMember.
   * In the database, this adds a REPORTS_TO entry in the ColleagueRelationship table with the state ADDED.
   * If the passed fromMember already has a manager relationship, will return FALSE and no changes will be made.
   *
   * @param fromMember The member for which to add the manager.
   * @param contract The contract to add the relationship to.
   * @param toMember The manager.
   * @param creator The seat that created this relationship.
   * @return True if success, false if failure.
   */
  @Action(name = "addManagerRelationship")
  public Task<Boolean> addManagerRelationship(
      @ActionParam(value = "fromMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn fromMember,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @ActionParam(value = "toMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn toMember,
      @ActionParam(value = "creator", typeref = com.linkedin.common.SeatUrn.class) SeatUrn creator
  ) {
    return _salesColleaguesService.addManagerRelationship(fromMember.getMemberIdEntity(),
        contract.getContractIdEntity(), toMember.getMemberIdEntity(), creator);
  }

  /**
   * Explicitly remove this manager relationship with net effect that this "fromMember reports to toMember" is explicitly marked as removed for this contract.
   * In the database, this adds a REPORTS_TO entry in the ColleagueRelationship table with the state REMOVED.
   * If there is already a previous entry of this type with the same fromMember and a different toMember, this will fail.
   *
   * @param fromMember The member for which to remove the manager.
   * @param contract The contract to remove the relationship from.
   * @param toMember The manager.
   * @param creator The seat that removed this relationship.
   * @return True if success, false if failure.
   */
  @Action(name = "removeManagerRelationship")
  public Task<Boolean> removeManagerRelationship(
      @ActionParam(value = "fromMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn fromMember,
      @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @ActionParam(value = "toMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn toMember,
      @ActionParam(value = "creator", typeref = com.linkedin.common.SeatUrn.class) SeatUrn creator
  ) {
    return _salesColleaguesService.removeManagerRelationship(fromMember.getMemberIdEntity(),
        contract.getContractIdEntity(), toMember.getMemberIdEntity(), creator);
  }

  /**
   * Finds the latest colleague relationships for the given member.
   *
   * @param fromMember The member for which to find the relationships. Only looks at the fromMember field in the database.
   * @param contract The contract for which to find the relationships.
   * @param relationshipType The relationship type to fetch. Fetches all when unspecified.
   * @return A collection of ColleagueRelationships that are the most recently added relationships.
   */
  @Finder("latestByFromMember")
  public Task<BasicCollectionResult<ColleagueRelationship>> findLatestByFromMember(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "fromMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn fromMember,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @QueryParam("relationshipType") @Optional RelationshipType relationshipType) {

    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : 0;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : 1;

    return _salesColleaguesService.findByFromMember(fromMember.getMemberIdEntity(), contract.getContractIdEntity(),
        relationshipType, start, count).map(BasicCollectionResult::new);
  }

  /**
   * Finds the history of colleague relationships for the given member.
   *
   * @param fromMember The member for which to find historical relationships. Only looks at the fromMember field in the database.
   * @param contract The contract for which to find historical relationships.
   * @param relationshipType The relationship type to fetch. Fetches all when unspecified.
   * @return A collection of ColleagueRelationships that represent all the historical relationships.
   */
  @Finder("historyByFromMember")
  public Task<BasicCollectionResult<ColleagueRelationship>> findHistoryByFromMember(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "fromMember", typeref = com.linkedin.common.MemberUrn.class) MemberUrn fromMember,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @QueryParam("relationshipType") @Optional RelationshipType relationshipType) {

    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : ServiceConstants.DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : ServiceConstants.DEFAULT_COUNT;

    return _salesColleaguesService.findHistoryByFromMember(fromMember.getMemberIdEntity(), contract.getContractIdEntity(),
        relationshipType, start, count).map(BasicCollectionResult::new);
  }

}
