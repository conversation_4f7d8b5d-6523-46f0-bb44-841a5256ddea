package com.linkedin.sales.rest.common;

import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.SalesAccessToken;
import com.linkedin.sales.service.SalesOauthAuthenticationService;
import java.util.Collections;
import java.util.List;
import javax.inject.Inject;


/**
 * Allow Sales Navigator CRM widget integration partners to exchange standard Linkedin oauth access tokens with a
 * Sales Access Token. The Sales Access Token is functionally the same as an access token but it has limited scope,
 * a short TTL and is unable to be refreshed.
 */
@RestLiCollection(name = "salesAccessTokens", keyName = "memberId", namespace = "com.linkedin.sales")
public class SalesAccessTokenResource extends CollectionResourceTaskTemplate<Long, SalesAccessToken> {

  @Inject
  private SalesOauthAuthenticationService _salesOauthAuthenticationService;

  /**
   * All tokens belonging to given member and application
   *
   * @param viewer The member to which tokens are bound
   * @param developerApplication The application to which tokens are bound
   */
  @Finder("viewerAndDeveloperApp")
  public Task<List<SalesAccessToken>> salesAccessTokenByViewerAndDeveloperApp(
      @QueryParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewer,
      @QueryParam(value = "application", typeref = com.linkedin.common.DeveloperApplicationUrn.class) DeveloperApplicationUrn developerApplication) {
    return _salesOauthAuthenticationService.getSalesAccessToken(developerApplication.getIdAsLong(), viewer.getMemberIdEntity())
        .map(salesAccessToken -> Collections.singletonList(salesAccessToken));
  }
}
