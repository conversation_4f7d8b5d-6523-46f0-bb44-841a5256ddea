package com.linkedin.sales.rest.common;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.Contract;
import com.linkedin.sales.service.SalesContractService;
import java.util.List;
import javax.inject.Inject;


/**
 * This resource is a wrapper on cap contracts as we do not want to externalize anything on cap-backend.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesContracts", keyName = "salesContractId", namespace = "com.linkedin.sales")
public class SalesContractResource extends CollectionResourceTaskTemplate<Long, Contract> {

  private final SalesContractService _salesContractService;

  @Inject
  public SalesContractResource(SalesContractService salesContractService) {
    _salesContractService = salesContractService;
  }

  /**
   * Find all contracts where user has an active seat.
   *
   * @param viewer The Viewing Member
   * @return Contracts containing id, name and description.
   */
  @Finder("contractsByMember")
  public Task<List<Contract>> contractsByMember(
      @QueryParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewer) {

    return _salesContractService.findContractsByMember(viewer);
  }

  /**
   * Find all contractIds that are mapped to the input companyId from the Handraise pilot list.
   * The contracts have been hand verified to be mapped to that company and thus we are not doing
   * any source-of-truth lookup here.
   * The handraise project is to send eligible tier 1 users prompts to join their company's tier 3 contracts.
   *
   * @param companyId id of the company
   * @return a list of Contracts mapped to a handraise pilot company
   */
  @Finder("contractsByHandraisePilotCompany")
  public Task<List<Contract>> contractsByHandraisePilotCompany(@QueryParam("companyId") Long companyId) {
    return _salesContractService.getContractsByHandraisePilotCompany(companyId);
  }
}