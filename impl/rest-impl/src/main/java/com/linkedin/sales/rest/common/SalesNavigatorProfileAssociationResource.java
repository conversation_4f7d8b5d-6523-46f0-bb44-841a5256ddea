package com.linkedin.sales.rest.common;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.KeyValueResource;
import com.linkedin.sales.service.SalesNavigatorProfileAssociationService;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociation;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociationKey;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;


/**
 * This resource maps 3rd party, vendor-specific ID's to LinkedIn MemberUrns
 * The intended use of this resource is to return these MemberUrns to the rest.li gateway,
 * where they will be converted to PersonUrns and decorated with public profile information
 */
@RestLiCollection(name = "salesNavigatorProfileAssociations", keyName = "id", namespace = "com.linkedin.salesgateway")
public class SalesNavigatorProfileAssociationResource
    implements KeyValueResource<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> {

  private final SalesNavigatorProfileAssociationService _salesNavigatorProfileAssociationService;

  @Inject
  public SalesNavigatorProfileAssociationResource(
      SalesNavigatorProfileAssociationService salesNavigatorProfileAssociationService) {
    _salesNavigatorProfileAssociationService = salesNavigatorProfileAssociationService;
  }

  /**
   * Fetches the corresponding MemberUrn when provided with a SalesNavigatorProfileAssociationKey
   *
   * @param id contains (CRM that is making the call, UUID of the particular crm installation, UUID of a record belonging to that instance)
   * @return MemberUrn if there is one in the CSA database
   */
  @RestMethod.Get
  public Task<SalesNavigatorProfileAssociation> get(
      ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> id) {
    return _salesNavigatorProfileAssociationService.getSalesNavProfileAssociation(id.getKey())
        .map(salesNavigatorProfileAssociation -> salesNavigatorProfileAssociation.orElseThrow(
            () -> new RestLiServiceException(HttpStatus.S_404_NOT_FOUND)));
  }

  /**
   * Batch fetches the corresponding MemberUrn when provided with multiple SalesNavigatorProfileAssociationKeys
   *
   * @param ids a set of keys that contain (CRM that is making the call, UUID of a particular crm installation, UUID of a record belonging to that instance)
   * @return One or more MemberUrn's if there are matches in the CSA database
   */
  @RestMethod.BatchGet
  public Task<BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>> batchGet(
      Set<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>> ids) {
    if (ids.isEmpty()) {
      return Task.value(new BatchResult<>(Collections.emptyMap(), Collections.emptyMap()));
    }
    Set<SalesNavigatorProfileAssociationKey> unpackedKeys =
        ids.stream().map(ComplexResourceKey::getKey).collect(Collectors.toSet());
    return _salesNavigatorProfileAssociationService.batchGetSalesNavProfileAssociations(unpackedKeys);
  }
}
