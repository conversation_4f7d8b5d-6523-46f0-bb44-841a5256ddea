package com.linkedin.sales.rest.common;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.sales.service.ProfileVisibilityService;
import com.linkedin.sales.service.SalesNavigatorSaveLeadService;
import com.linkedin.sales.utils.MemberUtils;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * This resource is used in Office 365 Outlook People Card.
 * Office 365 Outlook user can view Linkedin member information in people card.
 * If this user is Sales Navigator seat, user (viewer) can save/unsave that member (viewee) as lead.
 * Outlook will call MS Graph API, which calls Linkedin Restli gateway and calls this resource.
 * Design doc: http://go/sn+o365
 */
@RestLiActions(name = "salesNavigatorSaveLead", namespace = "com.linkedin.salesgateway")
public class SalesNavigatorSaveLeadResource {
  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorSaveLeadResource.class);

  @Inject
  private SalesNavigatorSaveLeadService _salesNavigatorSaveLeadService;

  @Inject
  private ProfileVisibilityService _profileVisibilityService;

  /**
   * Office 365 Outlook user can save Linkedin member in People Card as lead.
   *
   * @param viewer member Urn of the viewer who is Office 365 Outlook user and also a Sales Navigator seat.
   * @param viewee member Urn of the viewee who is linkedin member.
   * @param accountName the account name associated with this lead. This field is optional.
   * @return success or not of save lead action
   */
  @Action(name = "saveVieweeAsLead")
  public Task<Boolean> saveVieweeAsLead(
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewer,
      @ActionParam(value = "viewee", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewee,
      @ActionParam(value = "accountName") @Optional String accountName
  ) {
    MemberUtils.ifNonMemberThenThrow400(viewer, viewee);
    return _profileVisibilityService.isProfileVisible(viewer, viewee).flatMap(
        isProfileVisible -> {
          if (isProfileVisible) {
            return _salesNavigatorSaveLeadService.saveLead(viewer, viewee, accountName);
          }
          LOG.info("saveVieweeAsLead viewer {} doesn't have visibility on viewee {}", viewer, viewee);
          return Task.value(FALSE);
        }
    );
  }



  /**
   * Office 365 Outlook user can unsave Linkedin member in People Card as lead.
   *
   * @param viewer member Urn of the viewer who is Office 365 Outlook user and also a Sales Navigator seat.
   * @param viewee member Urn of the viewee who is linkedin member.
   * @return If unsave action success, this method will return true. Otherwise when viewee is not a lead to unsave, this method will return false.
   */
  @Action(name = "unsaveVieweeAsLead")
  public Task<Boolean> unsaveVieweeAsLead(
      @ActionParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewer,
      @ActionParam(value = "viewee", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewee
  ) {
    MemberUtils.ifNonMemberThenThrow400(viewer, viewee);
    return _profileVisibilityService.isProfileVisible(viewer, viewee).flatMap(
        isProfileVisible -> {
          if (isProfileVisible) {
            return _salesNavigatorSaveLeadService.unsaveLead(viewer, viewee);
          }
          LOG.info("unsaveVieweeAsLead viewer {} doesn't have visibility on viewee {}", viewer, viewee);
          return Task.value(FALSE);
        }
    );
  }
}
