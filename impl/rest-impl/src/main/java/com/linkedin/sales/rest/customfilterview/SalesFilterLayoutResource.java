package com.linkedin.sales.rest.customfilterview;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesFilterLayoutService;
import com.linkedin.salescustomfilterview.FilterLayout;
import com.linkedin.salescustomfilterview.FilterLayoutKey;
import com.linkedin.salescustomfilterview.SearchViewType;
import javax.inject.Inject;


/**
 * This resource handles Sales Navigator FilterLayout operations. This stores the user's preferences on the layout of
 * filters in Sales Navigator search. A filter layout consists of a key denoting the filterGroup, and an ordered
 * array of the filters within said filter group.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesFilterLayout", keyName = "key", namespace = "com.linkedin.sales")
public class SalesFilterLayoutResource extends ComplexKeyResourceTaskTemplate<FilterLayoutKey, EmptyRecord, FilterLayout> {
  private final SalesFilterLayoutService _salesFilterLayoutService;

  @Inject
  public SalesFilterLayoutResource(SalesFilterLayoutService salesFilterLayoutService) {
    _salesFilterLayoutService = salesFilterLayoutService;
  }

  /**
   * Update a filter layout.
   * @param key {@link FilterLayoutKey} which contains the seatUrn and searchViewType
   * @param filterLayout {@link FilterLayout}
   * @return {@link UpdateResponse}
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(final ComplexResourceKey<FilterLayoutKey, EmptyRecord> key, FilterLayout filterLayout) {
    SeatUrn seatUrn = key.getKey().getSeat();
    ContractUrn contractUrn = key.getKey().getContract();
    SearchViewType searchViewType = key.getKey().getSearchViewType();
    return _salesFilterLayoutService.upsertFilterLayout(seatUrn, contractUrn, searchViewType, filterLayout)
        .map(isUpdated -> (isUpdated) ? new UpdateResponse(HttpStatus.S_204_NO_CONTENT)
            : new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
  }

  /**
   * Gets filter layout for a given {@link FilterLayoutKey}
   * @param key {@link FilterLayoutKey} which contains the seatUrn and searchViewType
   * @return {@link FilterLayout}
   */
  @RestMethod.Get
  public Task<FilterLayout> get(final ComplexResourceKey<FilterLayoutKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    SearchViewType searchViewType = key.getKey().getSearchViewType();
    return _salesFilterLayoutService.getFilterLayout(seatUrn, searchViewType);
  }

  /**
   * Deletes existing records for the given key to reset to the default
   *
   * @param key {@link FilterLayoutKey} which contains the seatUrn and searchViewType
   * @return {@link UpdateResponse}
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<FilterLayoutKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    SearchViewType searchViewType = key.getKey().getSearchViewType();
    return _salesFilterLayoutService.deleteFilterLayout(seatUrn, searchViewType).map(UpdateResponse::new);
  }
}
