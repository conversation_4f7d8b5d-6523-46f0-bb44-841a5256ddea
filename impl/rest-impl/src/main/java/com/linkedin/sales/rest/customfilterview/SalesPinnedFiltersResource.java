package com.linkedin.sales.rest.customfilterview;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesPinnedFiltersService;
import com.linkedin.salescustomfilterview.PinnedFilters;
import com.linkedin.salescustomfilterview.PinnedFiltersKey;
import com.linkedin.salescustomfilterview.SearchType;
import javax.inject.Inject;


/**
 * This resource handles Sales Navigator get and updates to PinnedFilters.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesPinnedFilters", keyName = "key", namespace = "com.linkedin.sales")
public class SalesPinnedFiltersResource extends ComplexKeyResourceTaskTemplate<PinnedFiltersKey, EmptyRecord, PinnedFilters> {
  private final SalesPinnedFiltersService _salesPinnedFiltersService;

  @Inject
  public SalesPinnedFiltersResource(SalesPinnedFiltersService salesPinnedFiltersService) {
    _salesPinnedFiltersService = salesPinnedFiltersService;
  }
  /**
   * Performs a partial update on a pinnedFilter. Use {@link PatchRequest} $set to pin a filter,
   * and $unset to unpin a given filter.
   *
   * @param key {@link PinnedFiltersKey} which contains the seatUrn and searchType
   * @param patch patch of {@link PinnedFilters}
   * @return {@link UpdateResponse}
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(final ComplexResourceKey<PinnedFiltersKey, EmptyRecord> key,
      PatchRequest<PinnedFilters> patch) {
    SeatUrn seatUrn = key.getKey().getSeat();
    ContractUrn contractUrn = key.getKey().getContract();
    SearchType searchType = key.getKey().getSearchType();
    return _salesPinnedFiltersService.updateEspressoPinnedFilters(seatUrn, searchType, patch, contractUrn)
        .map(isUpdated -> (isUpdated) ? new UpdateResponse(HttpStatus.S_204_NO_CONTENT)
            : new UpdateResponse(HttpStatus.S_404_NOT_FOUND));
  }

  /**
   * Gets pinned filters for a given {@link PinnedFiltersKey}
   * @param key {@link PinnedFiltersKey} which contains the seatUrn and searchType
   * @return {@link PinnedFilters}
   */
  @RestMethod.Get
  public Task<PinnedFilters> get(final ComplexResourceKey<PinnedFiltersKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    ContractUrn contractUrn = key.getKey().getContract();
    SearchType searchType = key.getKey().getSearchType();
    return _salesPinnedFiltersService.getEspressoPinnedFilters(seatUrn, contractUrn, searchType);
  }

  /**
   * Deletes existing records for the given key to reset to the default
   *
   * @param key {@link PinnedFiltersKey} which contains the seatUrn and searchType
   * @return {@link UpdateResponse}
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<PinnedFiltersKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    SearchType searchType = key.getKey().getSearchType();
    return _salesPinnedFiltersService.deleteEspressoPinnedFilters(seatUrn, searchType).map(UpdateResponse::new);
  }
}
