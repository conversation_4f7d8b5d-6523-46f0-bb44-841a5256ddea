package com.linkedin.sales.rest.enterprise;

import com.linkedin.common.CrmUserUrnArray;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.bulk.BulkActionMessage;
import com.linkedin.enterprise.bulk.BulkActionPluginException;
import com.linkedin.enterprise.bulk.BulkActionPluginResource;
import com.linkedin.enterprise.bulk.BulkCrmUserSyncContext;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.sales.service.CrmUserOnboardingBulkActionService;
import java.util.Optional;
import javax.inject.Inject;


/**
 * This Rest.li action resource defines the plugin actions for the bulk CRM User onboarding plugin.
 * The enterprise platform bulk action framework requires two plugin action as of now: validate and process.
 * The actions in this Rest.li resource will be called by bulk action framework while processing.
 *
 * This plugin provides the following functionalities:
 * 1. Get CrmUser data (email, first name, last name, etc.)
 * 2. Create Enterprise Profiles based on CrmUser data.
 *    2.a. [Optional]: Assign license.
 * 3. Call other LSS code to create LSS seat.
 * 4. Link CrmUser with LSS seat.
 *
 * Bulk action client documentation: go/epbulk_onboarding
 */
@RestLiActions(name = "crmUserOnboardingBulkActionPlugin", namespace = "com.linkedin.sales")
public class CrmUserOnboardingBulkActionPluginResource implements BulkActionPluginResource<CrmUserUrnArray, BulkCrmUserSyncContext> {

  @Inject
  private CrmUserOnboardingBulkActionService _crmUserOnboardingBulkActionService;

  /**
   * EP CRM User onboarding bulk action - Processor
   * This is the plugin action to process a sub-batch of bulk CRM User onboarding.
   * It can also optionally assign licenses to the newly created profiles, given the proper context.
   * @param inputKeys  the sub-batch of CRM User URNs to use to create Profiles
   * @param context  the context for CRM User onboarding, which may contain license assignment context
   * @param viewer  the viewer of the bulk action. The type is generic Urn.
   * It can be used as a pass through for different viewers. For processBatch action, this will be used to record who the creator is.
   * @return  the batch result entity of this sub-batch for the bulk framework to aggregate results
   * @throws BulkActionPluginException
   */
  @Action(name = "processBatch")
  @Override
  public BulkActionBatchResult processBatch(@ActionParam("inputKeys") CrmUserUrnArray inputKeys,
      @ActionParam("context") BulkCrmUserSyncContext context,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.Urn.class) Urn viewer) throws BulkActionPluginException {
    return _crmUserOnboardingBulkActionService.processBatch(inputKeys, context, viewer);
  }

  /**
   * EP CRM User onboarding bulk action - Validator
   * The plugin action to do pre-validation for bulk CRM User onboarding. Pre-validation is done separately from processBatch
   * to ensure that before we dispatch the bulk request into multiple batches to be processed either sequentially or in parallel,
   * all their common requirements are validated first, instead of having overlapping validations on common requirements.
   * Since we don't need to do any validation on the, CRM User URNs themselves, this validation will primarily be
   * on the license assignment context, if it exists. If validation fails, an exception will be thrown,
   * otherwise a 200 OK will be returned.
   * @param context  the CRM User onboarding context of this bulk action task.
   * @param viewer  the viewer of the bulk action. The type is generic Urn.
   * It can be used as a pass through for different viewers to be validated. e.g., When license assignment is specified,
   * this will be used to validate whether the viewer has permission to the budget group.
   * @throws BulkActionPluginException
   */
  @Action(name = "validate")
  @Override
  public void validate(@ActionParam("context") BulkCrmUserSyncContext context,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.Urn.class) Urn viewer)
      throws BulkActionPluginException {
    Optional<BulkActionMessage> bulkActionMessage = _crmUserOnboardingBulkActionService.validate(context, viewer);
    if (bulkActionMessage.isPresent()) {
      throw new BulkActionPluginException(bulkActionMessage.get());
    }
  }

}
