package com.linkedin.sales.rest.enterprise;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.license.LicenseAssignment;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.PathKeys;
import com.linkedin.restli.server.ResourceLevel;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.PathKeysParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.EnterpriseSalesSeat;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.service.SalesNavigatorEmailService;
import com.linkedin.sales.service.utils.SeatRoleUtils;
import java.util.List;
import javax.inject.Inject;


/**
 * Hosting all miscellaneous APIs related to Enterprise AccountCenter integrations
 *
 */
@RestLiAssociation(name = "enterpriseSalesSeats",
    keyName = EnterpriseSalesSeatResource.KEY_NAME,
    assocKeys = {
        @Key(name = EnterpriseProfileUrn.ENTITY_TYPE,
            type = com.linkedin.common.urn.EnterpriseProfileUrn.class,
            typeref = com.linkedin.common.EnterpriseProfileUrn.class),
        @Key(name = EnterpriseApplicationInstanceUrn.ENTITY_TYPE,
            type = com.linkedin.common.urn.EnterpriseApplicationInstanceUrn.class,
            typeref = com.linkedin.common.EnterpriseApplicationInstanceUrn.class)
    },
    namespace = "com.linkedin.sales")
public class EnterpriseSalesSeatResource extends AssociationResourceTaskTemplate<EnterpriseSalesSeat> {
  public static final String KEY_NAME = "id";
  private static final EnterpriseApplicationUrn SALES_NAVIGATOR = EnterpriseApplication.SALES_NAVIGATOR.getUrn();

  @Inject
  private EnterprisePlatformClient _enterprisePlatformClient;
  @Inject
  private SalesNavigatorEmailService _salesNavigatorEmailService;

  private static final EnterpriseApplicationUsageUrn VIEWER =
      new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
          "migratedAppInstanceMappingToEP");

  /**
   * Generating Enterprise Platform activation Url by enterpriseApplicationInstanceUrn and enterpriseProfileUrn
   *
   * @param pathKeys payload required for generating activation Url
   *
   * @return a url with payload encrypted using AES
   */
  @Action(name = "generateActivationUrl", resourceLevel = ResourceLevel.ENTITY)
  public Task<String> generateActivationUrl(@PathKeysParam PathKeys pathKeys) {
    EnterpriseProfileUrn enterpriseProfileUrn = pathKeys.get(EnterpriseProfileUrn.ENTITY_TYPE);
    EnterpriseApplicationInstanceUrn enterpriseApplicationInstanceUrn =
        pathKeys.get(EnterpriseApplicationInstanceUrn.ENTITY_TYPE);

    Task<ContractUrn> contractUrnTask = _enterprisePlatformClient
        .getAppInstanceWithMappedContract(enterpriseApplicationInstanceUrn, VIEWER)
        .map(appInstanceOptional -> {
          if (appInstanceOptional.isPresent() && appInstanceOptional.get().hasContractId()) {
            return new ContractUrn(appInstanceOptional.get().getContractId());
          }
          throw new IllegalStateException("cannot generate activation url because no contract "
              + "mapped to this enterpriseAppInstanceUrn: " + enterpriseApplicationInstanceUrn);
        });

    Task<List<LicenseAssignment>> licenseAssignmentsTask =
        _enterprisePlatformClient.findLicenseAssignments(enterpriseApplicationInstanceUrn, enterpriseProfileUrn);

    return Task.par(licenseAssignmentsTask, contractUrnTask).flatMap((licenseAssignments, contractUrn) -> {
      boolean isTLE = SeatRoleUtils.hasTLELicense(licenseAssignments);
      return _salesNavigatorEmailService.generateInvitationEmailUrl(enterpriseProfileUrn,
          enterpriseApplicationInstanceUrn, SALES_NAVIGATOR, contractUrn.getContractIdEntity(), isTLE);
    });
  }
}
