package com.linkedin.sales.rest.enterprise;

import com.linkedin.common.EnterpriseProfileUrnArray;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.api.common.EnterpriseApplicationNoticeResource;
import com.linkedin.enterprise.notice.BatchSendNoticeResults;
import com.linkedin.enterprise.notice.SendNoticeResult;
import com.linkedin.parseq.Engine;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.sales.service.SalesNavigatorEmailService;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/*
 * for Sales Navigator to send enterprise application notice to seat owners or invitees, implements {@link
 * EnterpriseApplicationNoticeResource}
 */
@RestLiActions(name = "salesNavigatorEnterpriseApplicationNotices", namespace = "com.linkedin.sales")
public class SalesNavigatorEnterpriseApplicationNoticeResource implements EnterpriseApplicationNoticeResource {

  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorEnterpriseApplicationNoticeResource.class);
  private final SalesNavigatorEmailService _salesNavigatorEmailService;
  private final Engine _parseqEngine;

  @Inject
  public SalesNavigatorEnterpriseApplicationNoticeResource(SalesNavigatorEmailService salesNavigatorEmailService, Engine parseqEngine) {
    this._salesNavigatorEmailService = salesNavigatorEmailService;
    this._parseqEngine = parseqEngine;
  }

  /**
   * Send out a batch of activation notices to the given list of enterprise profile recipients.
   * In Sales Navigator, the current implementation is sending emails
   *
   * @deprecated
   * @param recipients the list of enterprise profile recipients to send the notice to.
   * @param applicationInstance the application instance used for clients to build the email within this context.
   * @param actor the actor who triggered the batch send activation notice action. Used as a pass-through for clients to verify.
   * @return a {@link BatchSendNoticeResults} entity which is essentially a list of each individual send email results.
   */
  @Action(name = "batchSendActivationNotice")
  @Override
  @Deprecated
  public BatchSendNoticeResults batchSendActivationNotice(
      @ActionParam(value = "recipients") EnterpriseProfileUrnArray recipients,
      @ActionParam(value = "applicationInstance", typeref = com.linkedin.common.EnterpriseApplicationInstanceUrn.class)
          EnterpriseApplicationInstanceUrn applicationInstance,
      @ActionParam(value = "actor", typeref = com.linkedin.common.Urn.class) Urn actor) {
    throw new UnsupportedOperationException("/salesNavigatorEnterpriseApplicationNotices is deprecated and should not be used");
  }

  /**
   * Send out a single activation notice to the given enterprise profile recipient.
   *In Sales Navigator, the current implementation is sending email
   *
   * @deprecated
   * @param recipient the enterprise profile recipients to send the notice to.
   * @param applicationInstance the application instance used for clients to build the email within this context.
   * @param actor the actor who triggered the batch send notice action. Used as a pass-through for clients to verify.
   * @return a {@link SendNoticeResult} entity which contains the result of this send action.
   */
  @Action(name = "sendActivationNotice")
  @Override
  @Deprecated
  public SendNoticeResult sendActivationNotice(
      @ActionParam(value = "recipient", typeref = com.linkedin.common.EnterpriseProfileUrn.class)EnterpriseProfileUrn recipient,
      @ActionParam(value = "applicationInstance", typeref = com.linkedin.common.EnterpriseApplicationInstanceUrn.class)
          EnterpriseApplicationInstanceUrn applicationInstance,
      @ActionParam(value = "actor", typeref = com.linkedin.common.Urn.class)Urn actor) {
    throw new UnsupportedOperationException("/salesNavigatorEnterpriseApplicationNotices is deprecated and should not be used");
  }
}
