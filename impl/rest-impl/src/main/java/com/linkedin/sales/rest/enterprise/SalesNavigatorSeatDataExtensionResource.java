package com.linkedin.sales.rest.enterprise;

import com.linkedin.common.urn.EnterpriseSeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.appsconnector.dataextension.EnterpriseSeatDataExtensionPluginResource;
import com.linkedin.enterprise.appsconnector.dataextension.SalesNavigatorSeatDataExtension;
import com.linkedin.parseq.Engine;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.sales.service.SalesNavigatorSeatDataExtensionService;
import com.linkedin.sales.service.utils.ParseqUtil;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 *  Enterprise data extension plugin that provides SalesNav-specific data associated with enterprise seat.
 */
@RestLiCollection(
    name = "salesNavigatorSeatDataExtensions",
    keyName = "id",
    keyTyperefClass = com.linkedin.common.EnterpriseSeatUrn.class,
    namespace = "com.linkedin.sales")
public class SalesNavigatorSeatDataExtensionResource
    implements EnterpriseSeatDataExtensionPluginResource<SalesNavigatorSeatDataExtension> {

  private final SalesNavigatorSeatDataExtensionService _salesNavigatorSeatDataExtensionService;
  private final Engine _parseqEngine;

  @Inject
  public SalesNavigatorSeatDataExtensionResource(
      SalesNavigatorSeatDataExtensionService salesNavigatorSeatDataExtensionService, Engine parseqEngine) {
    _salesNavigatorSeatDataExtensionService = salesNavigatorSeatDataExtensionService;
    _parseqEngine = parseqEngine;
  }

  /**
   * Get data extensions by {@link EnterpriseSeatUrn}s.
   *
   * @param seatUrns enterprise seat urns to get data extensions for.
   * @param viewer A viewer that performs this operation. Examples include enterprise profile urn, cs user urn,
   *               enterprise application urn, etc.
   */
  @Override
  @RestMethod.BatchGet
  public Map<EnterpriseSeatUrn, SalesNavigatorSeatDataExtension> batchGet(Set<EnterpriseSeatUrn> seatUrns,
      @QueryParam(value = VIEWER_PARAM, typeref = com.linkedin.common.Urn.class) Urn viewer) {
    return ParseqUtil.awaitByDefaultTimeout(
        _salesNavigatorSeatDataExtensionService.getDataExtensions(seatUrns), _parseqEngine);
  }

  /**
   * Get data extension by {@link EnterpriseSeatUrn}.
   *
   * @param seatUrn enterprise seat urn this data extension belongs to.
   * @param viewer A viewer that performs this operation. Examples include enterprise profile urn, cs user urn,
   *    *               enterprise application urn, etc.
   */
  @Override
  @RestMethod.Get
  public SalesNavigatorSeatDataExtension get(EnterpriseSeatUrn seatUrn,
      @QueryParam(value = VIEWER_PARAM, typeref = com.linkedin.common.Urn.class) Urn viewer) {
    return ParseqUtil.awaitByDefaultTimeout(
        _salesNavigatorSeatDataExtensionService.getDataExtension(seatUrn), _parseqEngine);
  }
}
