package com.linkedin.sales.rest.entitymappings;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.entitymappings.ManualEmailToMemberMappingService;
import com.linkedin.salesentitymappings.EmailMatchingKey;
import com.linkedin.salesentitymappings.MatchedMemberEntity;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * This resource handles manual email to member mapping operations
 */
@RestLiCollection(name = "manualEmailToMemberMapping", namespace = "com.linkedin.sales", keyName = "key")
@ReadOnly({"lastModified", "created", "deleted"})
public class ManualEmailToMemberMappingResource
    extends ComplexKeyResourceTaskTemplate<EmailMatchingKey, EmptyRecord, MatchedMemberEntity> {

  private final ManualEmailToMemberMappingService _manualEmailToMemberMappingService;

  @Inject
  public ManualEmailToMemberMappingResource(ManualEmailToMemberMappingService manualEmailToMemberMappingService) {
    _manualEmailToMemberMappingService = manualEmailToMemberMappingService;
  }

  /**
   * Get matched LinkedIn member entities by the email matching keys
   * @param keys the keys for which the matched member entities are to be fetched
   * @return a map of email matching keys to matched member entities
   */
  @RestMethod.BatchGet
  public Task<Map<ComplexResourceKey<EmailMatchingKey, EmptyRecord>, MatchedMemberEntity>> batchGet(
      final Set<ComplexResourceKey<EmailMatchingKey, EmptyRecord>> keys) {
    return _manualEmailToMemberMappingService.batchGet(keys);
  }

  /**
   * Upsert a matched member entity for a given email matching key
   * @param key the email matching key for which the matched member entity is to be upserted
   * @param matchedMemberEntity the matched member entity to be upserted
   * @return an update response indicating if the upsert operation succeeded
   */
  @RestMethod.Update
  public Task<UpdateResponse> upsert(final ComplexResourceKey<EmailMatchingKey, EmptyRecord> key,
      final MatchedMemberEntity matchedMemberEntity) {
    return _manualEmailToMemberMappingService.upsert(key, matchedMemberEntity);
  }

  /**
   * Delete a matched member entity for a given email
   * @param key the email matching key for which the matched member entity is to be deleted
   * @return an update response indicating if the delete operation succeeded
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<EmailMatchingKey, EmptyRecord> key) {
    return _manualEmailToMemberMappingService.delete(key);
  }

}
