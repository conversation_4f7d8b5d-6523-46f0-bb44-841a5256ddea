package com.linkedin.sales.rest.entityviews;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.entityviews.SalesProfileViewsService;
import com.linkedin.salesentityview.SalesProfileView;
import javax.inject.Inject;


/**
 * The resource to manage profile view records in Sales Navigator
 * The SalesProfileView entity represents the viewing record of the profile of LinkedIn member by a seat holder in Sales Navigator.
 * Visiting profile pages or opening profile panels in Sales Navigator are counted as profile viewing actions.
 * Other actions could be counted as profile view in the future.
 * The memberUrn in association keys represent the memberUrn of the LinkedIn member whose profile is viewed
 * The seatUrn in association keys represent the seatUrn of the sales user who views the profile
 *
 * <AUTHOR>
 */
@RestLiAssociation(name = "salesProfileViews", assocKeys = {
    @Key(name = "viewerSeat", type = com.linkedin.common.urn.SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
    @Key(name = "viewedMember", type = com.linkedin.common.urn.MemberUrn.class, typeref = com.linkedin.common.MemberUrn.class)},
    namespace = "com.linkedin.sales")
public class SalesProfileViewsResource extends AssociationResourceTaskTemplate<SalesProfileView> {

  private final SalesProfileViewsService _salesProfileViewsService;

  @Inject
  public SalesProfileViewsResource(SalesProfileViewsService salesProfileViewsService) {
    _salesProfileViewsService = salesProfileViewsService;
  }

  /**
   * Upsert sales profile view record for a seat holder
   *
   * @param key Compound key
   * @param salesProfileView Sales profile view record to be created/ updated
   * @return update response to tell if the recent view record is added successfully
   * return status 200 for creating new record or updating existing record successfully,
   * error code: 400 for bad request, 500 -> DB failure
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(CompoundKey key, final SalesProfileView salesProfileView) {
    return _salesProfileViewsService.upsertSalesProfileView(key, salesProfileView);
  }

  /**
   * Get a sales profile view record
   * @param key the compound key of sales profile view to get
   * @return the sales profile view record that the input compound key maps to
   * error code: 500 -> DB failure, 400 -> Bad Request, 404 -> Entity Not Found
   */
  @RestMethod.Get
  public Task<SalesProfileView> get(CompoundKey key) {
    return _salesProfileViewsService.getSalesProfileView(key);
  }
}
