package com.linkedin.sales.rest.filter;

import com.google.common.collect.ImmutableSet;
import com.linkedin.restli.common.ResourceMethod;
import com.linkedin.restli.server.filter.FilterRequestContext;
import java.util.Set;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class AllowListedEndPointsForAuthorizationService {

  private final Set<String> _authorizationAllowListedEndpoints;
  private static final Logger log =
      LoggerFactory.getLogger(AllowListedEndPointsForAuthorizationService.class.getName());

  public AllowListedEndPointsForAuthorizationService(Set<String> authorizationAllowListedEndpoints) {
    _authorizationAllowListedEndpoints = ImmutableSet.copyOf(authorizationAllowListedEndpoints);
  }

  /**
   * This API is used to find the resource endpoints which have been allowlisted.
   * Only the APIs which are allowlisted will be doing an authorization check rest will skip it.
   * The externalized LSS_MT APIs are allowlisted, defined in config files.
   *
   * @param requestContext
   * @return true when a endpoint is allowlisted , false otherwise.
   */
  public boolean isAllowListedForAuthorization(FilterRequestContext requestContext) {
    try {
      Validate.notNull(requestContext, "Request Context cannot be null while checking for allowlisted endpoints");
      Validate.notNull(requestContext.getFilterResourceModel(),
          "Resource Model cannot be null while checking for allowlisted endpoints");
      ResourceMethod resourceMethod = requestContext.getMethodType();
      Validate.notNull(resourceMethod, "Method Type cannot be null while checking for allowlisted endpoints");
      String resourceName = requestContext.getFilterResourceModel().getResourceName();
      Validate.notNull(resourceName, "Resource Name cannot be null while checking for allowlisted endpoints");

      String completeMethodName;
      switch (resourceMethod) {
        case FINDER:
          completeMethodName = resourceName + ":" + resourceMethod + ":" + requestContext.getFinderName();
          break;
        case ACTION:
          completeMethodName = resourceName + ":" + resourceMethod + ":" + requestContext.getActionName();
          break;
        default:
          log.debug("Found resource name : {} resource method type {} while checking if endpoint is allowlisted.",
              resourceName, resourceMethod);
          completeMethodName = resourceName + ":" + resourceMethod;
          break;
      }
      return (_authorizationAllowListedEndpoints.contains(completeMethodName));
    } catch (IllegalArgumentException ex) {
      String errorMsg =
          "An error occurred while checking if the endpoint from the request is allowlisted for authorization check.";
      log.error(errorMsg, ex);
      throw ex;
    }
  }

}