package com.linkedin.sales.rest.filter;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.data.DataMap;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpMethod;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.internal.server.response.RestLiResponseEnvelope;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.filter.Filter;
import com.linkedin.restli.server.filter.FilterRequestContext;
import com.linkedin.restli.server.filter.FilterResponseContext;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.restligateway.util.GatewayCallerIdentity;
import com.linkedin.sales.service.SalesContractService;
import java.net.URISyntaxException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesContractAuthorizationFilter implements Filter {
  private static final Logger LOG = LoggerFactory.getLogger(SalesContractAuthorizationFilter.class.getSimpleName());
  private static final String CONTRACT_PARAM = "contract";

  private final SalesContractService _salesContractService;
  private final AllowListedEndPointsForAuthorizationService _allowListedEndPointsForAuthorizationService;
  private final Engine _parseqEngine;
  private final GatewayCallerFinder _gatewayCallerFinder;

  public SalesContractAuthorizationFilter(Engine parseqEngine, SalesContractService salesContractService,
      AllowListedEndPointsForAuthorizationService allowListedEndPointsForAuthorizationService,
      GatewayCallerFinder gatewayCallerFinder) {
    _salesContractService = salesContractService;
    _allowListedEndPointsForAuthorizationService = allowListedEndPointsForAuthorizationService;
    _parseqEngine = parseqEngine;
    _gatewayCallerFinder = gatewayCallerFinder;
  }

  @Override
  public CompletableFuture<Void> onRequest(FilterRequestContext requestContext) {
    CompletableFuture<Void> future = new CompletableFuture<>();
    _parseqEngine.run(onRequestGatewayAuthorization(requestContext).andThen(x -> future.complete(null))
        .onFailure(future::completeExceptionally));

    return future;
  }

  @Override
  public CompletableFuture<Void> onError(Throwable th, final FilterRequestContext requestContext,
      final FilterResponseContext responseContext) {
    overrideExceptionIfRequired(responseContext);
    CompletableFuture<Void> future = new CompletableFuture<>();
    future.completeExceptionally(responseContext.getResponseData().getResponseEnvelope().getException());
    return future;
  }

  /**
   * This method will be applied to external API response.
   * It will intercept each error response, it will
   * (1) modify the status code if necessary to follow gateway rules: http://go/restliGatewayStatusCode
   * (2) hide internal error message and replace it will general error message
   * (3) override error response format settings to hide call stack trace.
   * (4) log the original error
   */
  private void overrideExceptionIfRequired(final FilterResponseContext responseContext) {
    if (isExternalCall()) {
      RestLiResponseEnvelope responseEnvelope = responseContext.getResponseData().getResponseEnvelope();
      if (responseEnvelope.isErrorResponse()) {
        RestLiServiceException responseException = responseEnvelope.getException();
        if (responseException.getStatus().getCode() > 499) {
          LOG.error("Original Exception before overriding:", responseException);
        }

        if (SalesExternalError.exists(responseException.getStatus())) {
          SalesExternalError salesExternalError = SalesExternalError.enumOf(responseEnvelope.getStatus());
          responseEnvelope.setExceptionInternal(
              new RestLiServiceException(salesExternalError.getStatus(), salesExternalError.getMessage())
                  .setServiceErrorCode(responseException.getServiceErrorCode()));
        } else {
          responseEnvelope.setExceptionInternal(
              new RestLiServiceException(responseEnvelope.getStatus(), responseException.getMessage())
                  .setServiceErrorCode(responseException.getServiceErrorCode()));
        }
      }
    }
  }

  /**
   * This method perform authorization if request is external & rest resource are configured to be authorized.
   * @param requestContext request context
   */
  private Task<Void> onRequestGatewayAuthorization(FilterRequestContext requestContext) {
    // no authorization required if internal call
    if (!isExternalCall()) {
      return Task.value(null);
    }

    //check if call needs to be authorized
    if (!_allowListedEndPointsForAuthorizationService.isAllowListedForAuthorization(requestContext)) {
      // no authorization required
      return Task.value(null);
    }

    //No auth is required for salesContracts
    if ("salesContracts".equals(requestContext.getFilterResourceModel().getResourceName())
        && "contractsByMember".equals(requestContext.getFinderName())) {
      //No auth required, for contract_chooser.
      return Task.value(null);
    }

    final ContractUrn contract = getContractParam(requestContext);
    MemberUrn memberUrn = getMemberUrnFromGateway();

    return _salesContractService.checkMemberAllowedToUseSNAP(contract, memberUrn);
  }

  private static boolean isContractParamPresent(FilterRequestContext requestContext) {
    if (requestContext.getMethodType().getHttpMethod() == HttpMethod.POST) {
      DataMap dataMap = requestContext.getRequestData().getEntity().data();
      return dataMap.containsKey(CONTRACT_PARAM);
    } else {
      return requestContext.getQueryParameters() != null && requestContext.getQueryParameters()
          .containsKey(CONTRACT_PARAM);
    }
  }

  private static ContractUrn getContractParam(FilterRequestContext requestContext) throws RestLiServiceException {
    Validate.notNull(requestContext, "Request context cannot be null while trying to get the contract parameter.");
    String contractParam;
    if (isContractParamPresent(requestContext)) {
      if (requestContext.getMethodType().getHttpMethod() == HttpMethod.POST) {
        contractParam = requestContext.getRequestData().getEntity().data().getString(CONTRACT_PARAM);
      } else {
        contractParam = requestContext.getQueryParameters().get(CONTRACT_PARAM).toString();
      }
    } else {
      String errorMsg = "contract parameter not found in request.";
      throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, errorMsg);
    }
    ContractUrn contractUrn;
    try {
      contractUrn = ContractUrn.deserialize(contractParam);
    } catch (URISyntaxException e) {
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
          "could not create urn from contract param " + contractParam);
    }
    return contractUrn;
  }

  /**
   * This API returns boolean to indicate if the call is via restli gateway.
   * @return true when the call is external, false otherwise
   */
  private boolean isExternalCall() {
    return Optional.ofNullable(_gatewayCallerFinder.getCaller())
        .map(GatewayCallerIdentity::getApplicationId)
        .isPresent();
  }

  private MemberUrn getMemberUrnFromGateway() {
    return Optional.ofNullable(_gatewayCallerFinder.getCaller())
        .map(gatewayCaller -> new MemberUrn(gatewayCaller.getMemberId()))
        .orElse(null);
  }
}
