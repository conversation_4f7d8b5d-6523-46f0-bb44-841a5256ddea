package com.linkedin.sales.rest.filter;

import com.linkedin.restli.common.HttpStatus;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * An enum class contains a list of error response and predefined error message used specially externally
 * For more detail, please refer to http://go/restliGatewayStatusCode
 */
enum SalesExternalError {

  FORBIDDEN(HttpStatus.S_403_FORBIDDEN, "Forbidden."),

  TIMEOUT(HttpStatus.S_408_REQUEST_TIMEOUT, "Request Timeout."),

  PRECONDITION_FAILED(HttpStatus.S_412_PRECONDITION_FAILED, "HTTP precondition header failed."),

  INTERNAL_ERROR(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Internal Error."),

  NOT_IMPLEMENTED(HttpStatus.S_501_NOT_IMPLEMENTED, "Not Implemented.");

  private final HttpStatus _status;
  private final String _message;

  SalesExternalError(HttpStatus status, String message) {
    _status = status;
    _message = message;
  }

  public HttpStatus getStatus() {
    return _status;
  }

  public String getMessage() {
    return _message;
  }

  // mapping between request response code and its external SalesExternalError
  private static final Map<Integer, SalesExternalError> INTERNAL_EXCEPTION_MAP =
      Stream.of(SalesExternalError.values())
          .collect(Collectors.toMap(exEnum -> exEnum.getStatus().getCode(), Function.identity()));

  /**
   * Whether there exist a predefined error enum for the status code
   */
  public static boolean exists(int status) {
    return INTERNAL_EXCEPTION_MAP.containsKey(status);
  }

  /**
   * Whether there exist a predefined error enum for the status
   */
  public static boolean exists(HttpStatus status) {
    return exists(status.getCode());
  }

  /**
   * Return the corresponding SalesExternalError for the status
   * Return null if there is not such mapping
   */
  public static SalesExternalError enumOf(HttpStatus status) {
    return enumOf(status.getCode());
  }

  /**
   * Return the corresponding SalesExternalError for the status
   * Return null if there is not such mapping
   */
  public static SalesExternalError enumOf(int status) {
    return INTERNAL_EXCEPTION_MAP.get(status);
  }
}

