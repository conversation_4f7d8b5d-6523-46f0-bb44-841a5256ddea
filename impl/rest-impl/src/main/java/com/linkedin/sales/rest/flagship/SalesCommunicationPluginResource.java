package com.linkedin.sales.rest.flagship;

import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.NotificationCard;
import com.linkedin.comms.plugins.api.CommunicationRenderingPluginTemplate;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.sales.service.flagship.SalesCommunicationPluginService;
import javax.inject.Inject;


/**
 * Sales Navigator has the Sales Notification which is shown in Flagship. This resource implements two actions
 * required by the Flagship comms team to decorate and format those notifications.
 * See <a href="http://go/commsrenderingplugins">go/commsrenderingplugins</a> for more details.
 */
@RestLiActions(name = "salesCommunicationPlugin", namespace = "com.linkedin.sales")
public class SalesCommunicationPluginResource implements CommunicationRenderingPluginTemplate {
  private final SalesCommunicationPluginService _communicationPluginService;

  @Inject
  public SalesCommunicationPluginResource(
      SalesCommunicationPluginService communicationsPluginService) {
    _communicationPluginService = communicationsPluginService;
  }
  /**
   * Formats given Sales Notifications to NotificationCard array by applying our domain logic.
   *
   * @param notifications notifications that needs to be formatted.
   * @param communicationContext the context in which the notification is to be formatted.
   * @return An array of {@link NotificationCard} task.
   */
  @Action(name = "formatNotifications")
  @Override
  public Task<NotificationCard[]> formatNotifications(
      @ActionParam(value = "notifications") NotificationsV2[] notifications,
      @ActionParam(value = "communicationContext") CommunicationContext communicationContext) {
    return _communicationPluginService.formatNotifications(notifications, communicationContext);
  }

  /**
   * Generates the decorator for the given Sales Notification communication and context.
   *
   * Comms platform will use the generated decorator to decorate the Sales Notification communication by
   * resolving the specified referenced data.
   *
   * Campaign names are defined in the comms gateway:
   *   https://comms-gateway.prod.linkedin.com/communications-gateway/v2/manage/prod/types
   * A campaign could be InApp notification, Email, Push or SMS, the respective category can be found in
   * CommunicationContext.
   *
   * @param campaignName Campaign name of the communication for which the decorator is to be generated.
   * @param communicationContext the context in which the decorator is to be generated.
   * @return A {@link CommunicationDecorator} task.
   */
  @Action(name = "generateDecorator")
  @Override
  public Task<CommunicationDecorator> generateDecorator(@ActionParam(value = "campaignName") String campaignName,
      @ActionParam(value = "communicationContext") CommunicationContext communicationContext) {
    return _communicationPluginService.generateDecorator(campaignName, communicationContext);
  }
}
