package com.linkedin.sales.rest.flagship;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.resources.CollectionResourceTemplate;
import com.linkedin.sales.service.flagship.SalesPYMKRecommendationsService;
import com.linkedin.salespymk.GroupReason;
import com.linkedin.salespymk.PymkRecommendationsGroup;
import javax.inject.Inject;


/**
 * This endpoint returns a list of recommended Decision Makers the member may be interested in. The data will be shown
 * in a PYMK cohort on my network page on flagship. This is a freemium feature for members who have not yet
 * signed up for Sales Navigator.
 *
 * See <a href="http://go/freemium-pymk-rfc">go/freemium-pymk-rfc</a> for more details.
 */
@RestLiCollection(
    name = "salesPeopleYouMayKnowRecommendations",
    namespace = "com.linkedin.sales",
    keyName = "memberUrn",
    keyTyperefClass = com.linkedin.common.MemberUrn.class)
public class SalesPeopleYouMayKnowRecommendationsResource extends CollectionResourceTemplate<MemberUrn, PymkRecommendationsGroup> {

  private final SalesPYMKRecommendationsService _salesPYMKRecommendationsService;

  @Inject
  public SalesPeopleYouMayKnowRecommendationsResource(SalesPYMKRecommendationsService salesPYMKRecommendationsService) {
    _salesPYMKRecommendationsService = salesPYMKRecommendationsService;
  }

  /**
   * Returns a list of all the Decision Makers recommendation for the viewing member can use to onboard a new cohort.
   *
   * @param memberUrn the viewing member that the recommendations are for.
   * @param groupReason the reason for the recommendations.
   * @return a list of Decision Makers.
   */
  @Finder("recommend")
  public Task<BasicCollectionResult<PymkRecommendationsGroup>> recommend(
      @QueryParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) final MemberUrn memberUrn,
      @QueryParam(value = "groupReason") final GroupReason groupReason) {
    return _salesPYMKRecommendationsService.findRecommendations(memberUrn, groupReason);
  }
}
