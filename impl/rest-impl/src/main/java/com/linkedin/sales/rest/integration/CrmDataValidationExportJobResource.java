package com.linkedin.sales.rest.integration;

import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.CrmDataValidationExportJob;
import com.linkedin.sales.model.CrmDataValidationServiceErrorCode;
import com.linkedin.sales.service.integration.CrmDataValidationExportJobService;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import javax.inject.Inject;


/**
 * Created by jiawang on 8/3/2018
 * Restli interface to request and get the Crm Data Validation Export, which is used to help improve the quality
 * of users' data in their CRM systems, but does not rely on direct data sharing. For example, the job could flag
 * a contact and lead’s current company information in CRM if it's out of date.
 */
@RestLiCollection(name = "crmDataValidationExportJobs", namespace = "com.linkedin.sales", keyName = "jobId")
@ReadOnly({"jobId", "status", "downloadUrls", "expireAt", "nextExportStartAt"})
@CreateOnly({"exportStartAt", "exportEndAt"})
public class CrmDataValidationExportJobResource extends CollectionResourceTaskTemplate<Long, CrmDataValidationExportJob> {

  @Inject
  private CrmDataValidationExportJobService _crmDataValidationExportJobService;

  /**
   * Request CRM validation data export.
   * @param exportJob export job requested from the third-party user,
   *                  which should only contain 'exportStartAt' and 'exportEndAt' field.
   * @param crmInstanceUrnString the CRM instance Urn for the exported data, which is an identifier for the requester.
   *                             It could be "urn:li:crmInstance:(DYNAMICS,uuid)" or "urn:li:crmInstance:(SFDC,id)"
   * @return the job identifier id.
   */
  @RestMethod.Create
  public Task<CreateResponse> create(CrmDataValidationExportJob exportJob,
      @QueryParam(value = "crmInstanceId") String crmInstanceUrnString) {
    CrmInstanceUrn crmInstanceUrn = deserializeCrmInstanceUrnString(crmInstanceUrnString);
    return _crmDataValidationExportJobService.createJob(exportJob, crmInstanceUrn).map(CreateResponse::new);
  }

  /**
   * Get the crm data validation export based on the job id.
   * @param jobId the job identifier id.
   * @param crmInstanceUrnString the CRM instance Urn for the exported data, which is an identifier for the requester.
   * @return {@link CrmDataValidationExportJob} if processing or completed.
   *         500 Error Code if there's any internal error.
   *         504 Error Code if the request is timed out.
   */
  @RestMethod.Get
  public Task<CrmDataValidationExportJob> get(Long jobId, @QueryParam(value = "crmInstanceId") String crmInstanceUrnString) {
    CrmInstanceUrn crmInstanceUrn = deserializeCrmInstanceUrnString(crmInstanceUrnString);
    return _crmDataValidationExportJobService.getJob(jobId, crmInstanceUrn);
  }

  @NonNull
  private CrmInstanceUrn deserializeCrmInstanceUrnString(@NonNull String crmInstanceUrnString) {
    try {
      return CrmInstanceUrn.deserialize(crmInstanceUrnString);
    } catch (URISyntaxException e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Invalid Crm Instance " + crmInstanceUrnString).setServiceErrorCode(
          CrmDataValidationServiceErrorCode.INVALID_CRM_INSTANCE.getCode());
    }
  }
}
