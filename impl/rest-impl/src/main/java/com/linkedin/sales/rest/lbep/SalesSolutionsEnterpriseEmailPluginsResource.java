package com.linkedin.sales.rest.lbep;

import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.platform.api.EnterpriseOnboardingEmailCustomDataPluginResource;
import com.linkedin.platform.api.EnterpriseOnboardingEmailCustomFilterPluginResource;
import com.linkedin.platform.api.EnterpriseOnboardingEmailException;
import com.linkedin.platform.email.EnterpriseOnboardingEmailCustomData;
import com.linkedin.platform.email.EnterpriseOnboardingEmailTriggerContext;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.restli.server.resources.ResourceContextHolder;
import com.linkedin.sales.service.lbep.EnterpriseEmailPluginsService;
import javax.inject.Inject;


/**
 * Resource to host LSS specific logic for emails sent from EP, called by EP as plugin
 */
@RestLiActions(name = "salesSolutionsEnterpriseEmailPlugins",
    namespace = "com.linkedin.sales")
public class SalesSolutionsEnterpriseEmailPluginsResource extends ResourceContextHolder
    implements EnterpriseOnboardingEmailCustomFilterPluginResource, EnterpriseOnboardingEmailCustomDataPluginResource {

  private final EnterpriseEmailPluginsService _enterpriseEmailPluginsService;

  @Inject
  public SalesSolutionsEnterpriseEmailPluginsResource(EnterpriseEmailPluginsService enterpriseEmailPluginsService) {
    _enterpriseEmailPluginsService = enterpriseEmailPluginsService;
  }

  /**
   * Fetch onboarding email custom render data from applications by trigger context and viewer.
   *
   * @param context The context includes all data associated with this email trigger,e.g. profileUrn, contractUrn etc.
   * @param viewer  The viewer of the action
   * @return Task of EnterpriseOnboardingEmailCustomData
   */
  @Action(name = "fetchOnboardingEmailData")
  @Override
  public Task<ActionResult<EnterpriseOnboardingEmailCustomData>> fetchOnboardingEmailData(
      @ActionParam(value = "context") EnterpriseOnboardingEmailTriggerContext context,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.Urn.class) Urn viewer)
      throws EnterpriseOnboardingEmailException {
    return _enterpriseEmailPluginsService.fetchOnboardingEmailData(context, viewer).map(ActionResult::new);
  }

  /**
   * Should send onboarding email based on the provided context.
   * This will only get triggered from upstream if lix ep.should.process.onboarding.email is enabled
   *
   * @param context The context includes all information in the triggering flow
   * @param viewer  The viewer of the action
   * @return True means should send onboarding email, otherwise filter out this email request.
   */
  @Action(name = "shouldSendOnboardingEmail")
  @Override
  public Task<ActionResult<Boolean>> shouldSendOnboardingEmail(
      @ActionParam(value = "context") EnterpriseOnboardingEmailTriggerContext context,
      @ActionParam(value = "viewer", typeref = com.linkedin.common.Urn.class) Urn viewer)
      throws EnterpriseOnboardingEmailException {
    return _enterpriseEmailPluginsService.shouldSendOnboardingEmail(context, viewer).map(ActionResult::new);
  }
}
