package com.linkedin.sales.rest.leadaccount;

import com.linkedin.common.MemberUrnArray;
import com.linkedin.common.OrganizationUrnArray;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService;
import com.linkedin.sales.service.utils.RestliUtils;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import java.util.ArrayList;
import com.linkedin.espresso.common.util.Pair;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.inject.Inject;


/**
 * The resource to help create/delete/find the association/associations between LSS saved lead and LSS saved account(LeadAccountAssociation)
 */
@RestLiAssociation(name = "salesLeadAccountAssociations", assocKeys = {
        @Key(name = "creator", type = com.linkedin.common.urn.SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
        @Key(name = "lead", type = com.linkedin.common.urn.MemberUrn.class, typeref = com.linkedin.common.MemberUrn.class),
        @Key(name = "account", type = com.linkedin.common.urn.OrganizationUrn.class, typeref = com.linkedin.common.OrganizationUrn.class)
    },
    namespace = "com.linkedin.sales")

@CreateOnly({"creator", "lead", "account"})
public class LeadAccountAssociationResource extends AssociationResourceTaskTemplate<LeadAccountAssociation> {

  private final SalesLeadAccountAssociationService _salesLeadAccountAssociationService;

  @Inject
  public LeadAccountAssociationResource(SalesLeadAccountAssociationService salesLeadAccountAssociationService) {
    _salesLeadAccountAssociationService = salesLeadAccountAssociationService;
  }

  /**
   * Create an accountLeadAssociation.
   * @param leadAccountAssociation the accountLeadAssociation object to be created
   * @return create response to tell if the entity is created successfully
   * 201 -> created successfully, 200 -> association already exists, 500 -> DB failure, 400 -> bad Request
   */
  @RestMethod.Create
  public Task<CreateResponse> create(LeadAccountAssociation leadAccountAssociation) {
    return _salesLeadAccountAssociationService.createLeadAccountAssociation(leadAccountAssociation)
        .map(Pair::getSecond).recover(throwable -> {
      CompoundKey compoundKey = _salesLeadAccountAssociationService.getCompoundKey(leadAccountAssociation);
      return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
          "Failed to create the leadAccountAssociation " + compoundKey));
    });
  }

  /**
   * Batch create a collection of new leadAccountAssociation
   * @param leadAccountAssociations a collection of leadAccountAssociation to be created
   * @return batch create response to indicate if the creation for each leadAccountAssociation succeeds
   * 201 -> created successfully or association already exists, 500 -> DB failure, 400 -> bad Request
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<CompoundKey, LeadAccountAssociation>> batchCreate(
      BatchCreateRequest<CompoundKey, LeadAccountAssociation> leadAccountAssociations) {
    return _salesLeadAccountAssociationService.batchCreateLeadAccountAssociations(leadAccountAssociations.getInput())
        .map(resultMap -> {
          List<CreateResponse> createResponseList = leadAccountAssociations.getInput().stream().map(leadAccountAssociation -> {
            CompoundKey compoundKey = _salesLeadAccountAssociationService.getCompoundKey(leadAccountAssociation);
            if (!resultMap.containsKey(compoundKey)) {
              RestLiServiceException exception =
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to create the entity");
              resultMap.put(compoundKey, new CreateResponse(exception));
            }
            return RestliUtils
                .createCreateResponseWithDatamapInError(compoundKey, resultMap.get(compoundKey), LeadAccountAssociationResource.class);
          }).collect(Collectors.toList());
          return new BatchCreateResult<>(createResponseList);
        });
  }

  /**
   * Delete existing leadAccountAssociation
   * @param accountLeadAssociationKey the accountLeadAssociationKey that need to be deleted
   * @return Unpdate response to tell if the deletion succeeds.
   * 500 -> DB failure, 403 -> no permission to delete association. 400 -> Bad request
   * 204 -> Successful delete operation. If this association is not available before deletion,still 204 will return.
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(CompoundKey accountLeadAssociationKey) {
    return _salesLeadAccountAssociationService.deleteLeadAccountAssociation(accountLeadAssociationKey)
        .map(updateResponse -> {
      switch (updateResponse.getStatus()) {
        case S_500_INTERNAL_SERVER_ERROR:
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
              "delete the leadAccountAssociation get internal error" + accountLeadAssociationKey);
        case S_400_BAD_REQUEST:
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST);
        default:
          return updateResponse;
      }
    });
  }

  /**
   * Batch delete existing leadAccountAssociation
   * @param batchDeleteRequest
   * @return batch update result to tell if the association are deleted.
   * 204 -> Successful delete operation. If this association is not available before deletion,still 204 will return.
   * 500 -> DB failure, 403 -> no permission to delete the association. 400 -> Bad request
   */
  @RestMethod.BatchDelete
  public Task<BatchUpdateResult<CompoundKey, LeadAccountAssociation>> batchDelete(
      final BatchDeleteRequest<CompoundKey, LeadAccountAssociation> batchDeleteRequest) {
    return _salesLeadAccountAssociationService.batchDeleteLeadAccountAssociations(batchDeleteRequest.getKeys())
        .map(responseMap -> {
          Map<CompoundKey, UpdateResponse> resultMap = new HashMap<>();
          Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
          batchDeleteRequest.getKeys().forEach(key -> {
            UpdateResponse updateResponse = responseMap.get(key);
            if (updateResponse == null) {
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
            } else {
              switch (updateResponse.getStatus()) {
                case S_500_INTERNAL_SERVER_ERROR:
                  errorMap.put(key,  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                      "delete the leadAccountAssociation get internal error" + key));
                  break;
                case S_400_BAD_REQUEST:
                  errorMap.put(key, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
                  break;
                default:
                  resultMap.put(key, updateResponse);
                  break;
              }
            }
          });
          return new BatchUpdateResult<>(resultMap, errorMap);
        });

  }

  /**
   * Return matching LeadAccountAssociation, currently we only support one lead be associated with one account
   * @param seatUrn The seat holder who is the owner of leadAccountAssociation
   * @param leads a collection of lead's memberUrn
   * @param pagingContext Pagination Param, not used for now as currently we only support one lead be associated with one account
   * @return A collection of leadAccountAssociation for each given lead
   *
   */
  @Finder("seatAndLeads")
  public Task<BasicCollectionResult<LeadAccountAssociation>> findBySeatAndLeads(
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      final @QueryParam(value = "leads") MemberUrnArray leads,
      final @PagingContextParam PagingContext pagingContext) {
    return _salesLeadAccountAssociationService.getLeadAccountAssociationForLeads(new ArrayList<>(leads), seatUrn)
        .map(results -> new BasicCollectionResult<>(results, results.size()));
  }
  /**
   * Return matching LeadAccountAssociation, currently we support one account be associated by multiple leads
   * @param seatUrn the seat holder who is the owner of leadAccountAssociation
   * @param accounts a collection of account's organizationUrn
   * @param pagingContext Pagination Param
   * @return A collection of leadAccountAssociation with the given account
   *
   */
  @Finder("seatAndAccounts")
  public Task<BasicCollectionResult<LeadAccountAssociation>> findBySeatAndAccounts(
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      final @QueryParam(value = "accounts") OrganizationUrnArray accounts,
      final @PagingContextParam PagingContext pagingContext) {
    return _salesLeadAccountAssociationService
        .getLeadAccountAssociationForAccounts(new ArrayList<>(accounts), seatUrn, pagingContext.getStart(), pagingContext.getCount())
        .map(paginatedResults -> new BasicCollectionResult<>(paginatedResults.getResult(), paginatedResults.getTotal()));
  }

  /**
   * Return LeadAccountAssociations owned by the given seat with given pagination
   * @param seatUrn the seat holder who is the owner of leadAccountAssociation
   * @param pagingContext Pagination Param
   * @return A collection of leadAccountAssociations owned by the given seat
   *
   */
  @Finder("seat")
  public Task<List<LeadAccountAssociation>> findBySeat(
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      final @PagingContextParam PagingContext pagingContext) {
    return _salesLeadAccountAssociationService.getLeadAccountAssociations(seatUrn, pagingContext.getStart(), pagingContext.getCount());
  }
}
