package com.linkedin.sales.rest.leadaccount;

import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountMetadataService;
import com.linkedin.salesleadaccount.LeadAccountMetadata;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * * The resource to help get the metadata of leadAccountAssociation.
 * * The metadata could be how many leads be associated with the account. It could be expanded into other fields in the future.
 */

@RestLiCollection(name = "salesLeadAccountMetadata",
    keyName = "urn",
    keyTyperefClass = com.linkedin.common.OrganizationUrn.class,
    namespace = "com.linkedin.sales")

public class SalesLeadAccountMetadataSource extends CollectionResourceTaskTemplate<OrganizationUrn, LeadAccountMetadata> {

  private final SalesLeadAccountMetadataService _salesLeadAccountMetadataService;
  @Inject
  public SalesLeadAccountMetadataSource(SalesLeadAccountMetadataService salesLeadAccountMetadataService) {
    _salesLeadAccountMetadataService = salesLeadAccountMetadataService;
  }

  /**
   * Batch get the metadata for leads in certain account.
   * @param urns the set of account's organizationUrn
   * @param requester the seat Urn of the user in Sales Navigator
   * @return a map that tells the how many leads be associated for each account.
   */
  @RestMethod.BatchGet
  public Task<Map<OrganizationUrn, LeadAccountMetadata>> batchGet(Set<OrganizationUrn> urns,
      @QueryParam(value = "requester", typeref = com.linkedin.common.SeatUrn.class) SeatUrn requester) {
    return _salesLeadAccountMetadataService.batchGetAssociatedLeadCounts(urns, requester);
  }

}
