package com.linkedin.sales.rest.leadaccount;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SalesLeadEditableContactInfoService;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfo;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfoKey;
import javax.inject.Inject;


/**
 * A Lead is a Linkedin member that represents a potential or existing customer of a Sales Navigator user.
 * Lead editable contact info is manually entered by LSS seat, and other seats in the same contract could view and update it.
 * The memberUrn of SalesLeadEditableContactInfoKey represent the memberUrn of the sales lead and the contractUrn of
 * SalesLeadEditableContactInfoKey represent the contract of the sales user who create this editable contact info
 * This class is resource to upsert/get LSS's leads editable contact info.
 */
@RestLiCollection(name = "salesLeadEditableContactInfo",  namespace = "com.linkedin.sales", keyName = "id")
public class SalesLeadEditableContactInfoResource extends
        ComplexKeyResourceTaskTemplate<SalesLeadEditableContactInfoKey, EmptyRecord, SalesLeadEditableContactInfo> {

  private final SalesLeadEditableContactInfoService _salesLeadEditableContactInfoService;

  @Inject
  public SalesLeadEditableContactInfoResource(SalesLeadEditableContactInfoService salesLeadEditableContactInfoService) {
    _salesLeadEditableContactInfoService = salesLeadEditableContactInfoService;
  }

  /**
   * Upsert the sales lead editable contact info
   * @param key SalesLeadEditableContactInfoKey for the update which includes the memberUrn of the sales lead and contractUrn of the actor
   * @param salesLeadEditableContactInfo  the create or update content
   * @return the update response to tell if the update succeeds
   * return status 201 for creating new record successfully,
   * 200 for updating exiting record successfully, 400 for bad request, 500 for any other exception.
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(ComplexResourceKey<SalesLeadEditableContactInfoKey, EmptyRecord> key,
                                     final SalesLeadEditableContactInfo salesLeadEditableContactInfo) {
    return _salesLeadEditableContactInfoService.upsertLeadEditableInfo(key.getKey(), salesLeadEditableContactInfo);
  }

  /**
   * Get a sales lead editable contact info
   * @param key the compound key of sales lead editable contact info to get
   * @return the sales lead editable contact info that the input compound key maps to
   * error code: 500 -> DB failure, 400 -> Bad Request, 404 -> Entity Not Found
   */
  @RestMethod.Get
  public Task<SalesLeadEditableContactInfo> get(final ComplexResourceKey<SalesLeadEditableContactInfoKey, EmptyRecord> key) {
    return _salesLeadEditableContactInfoService.getLeadEditableContactInfo(key.getKey());
  }
}
