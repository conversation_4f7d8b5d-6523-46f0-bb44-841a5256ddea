package com.linkedin.sales.rest.leadaccount;

import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SalesLeadProfileUnlockInfoService;
import com.linkedin.salesleadaccount.SalesLeadProfileUnlockInfo;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * A Lead is a Linkedin member that represents a potential or existing customer of a Sales Navigator user.
 * Sales seat could unlock(view) certain lead's profile which out-of-network by consuming one profile unlock credit.
 * One sales seat unlock a certain lead profile, the lead profile will be visible for all seats under the same contract.
 * This class is the resource to create/get sales leads profile unlock info for a contract
 */

@RestLiAssociation(name = "salesLeadProfileUnlockInfo", keyName = "id",
        assocKeys = {
    @Key(name = "member", type = com.linkedin.common.urn.MemberUrn.class, typeref = com.linkedin.common.MemberUrn.class),
    @Key(name = "contract", type = com.linkedin.common.urn.ContractUrn.class,
        typeref = com.linkedin.common.ContractUrn.class)}, namespace = "com.linkedin.sales")
@CreateOnly({"contract", "member"})
public class SalesLeadProfileUnlockInfoResource extends AssociationResourceTaskTemplate<SalesLeadProfileUnlockInfo> {

  private final SalesLeadProfileUnlockInfoService _salesLeadsProfileUnlockInfoService;

  @Inject
  public SalesLeadProfileUnlockInfoResource(SalesLeadProfileUnlockInfoService salesLeadsProfileUnlockInfoService) {
    _salesLeadsProfileUnlockInfoService = salesLeadsProfileUnlockInfoService;
  }

  /**
   * Create sales lead profile unlock info for a contract
   * @param salesLeadProfileUnlockInfo sales lead unlock profile info to be created
   * @return a create response that indicates if the sales lead profile unlock info is created successfully.
   * The returning http status could be 201 for success, 200 if the entity to be created already exists,
   * 500 for any other exception.
   */
  @RestMethod.Create
  public Task<CreateResponse> create(final SalesLeadProfileUnlockInfo salesLeadProfileUnlockInfo) {
    return _salesLeadsProfileUnlockInfoService.createLeadProfileUnlockInfo(salesLeadProfileUnlockInfo)
        .map(Pair::getSecond);
  }


  /**
   * Batch get sales leads profile unlock info
   * @param keys the compound keys of sales lead profile unlock info to batch get, each of them contains memberUrn of sales lead
   *             and ContractUrn of the Sales Navigator user who create this unlock info
   * @return the sales lead profile unlock info that the input compound key maps to
   * error code: 500 -> DB failure, 400 -> Bad Request
   * The returning map doesn't contain the key for which no sales lead profile unlock info is found.
   */
  @RestMethod.BatchGet
  public Task<Map<CompoundKey, SalesLeadProfileUnlockInfo>> batchGet(final Set<CompoundKey> keys) {
    return _salesLeadsProfileUnlockInfoService.batchGetLeadProfileUnlockInfo(keys);
  }
}

