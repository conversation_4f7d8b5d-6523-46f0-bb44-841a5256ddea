package com.linkedin.sales.rest.leadaccount;

import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SalesAccountsService;
import com.linkedin.sales.service.utils.RestliUtils;
import com.linkedin.salesleadaccount.SalesAccount;
import com.linkedin.salesleadaccount.SalesAccountFilter;
import com.linkedin.salesleadaccount.SalesAccountSortCriteria;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;

/**
 * This class manages LSS's saved accounts behavior.
 * An account is a Linkedin organization that represents a potential or existing customer of a Sales Navigator user.
 * An account becomes saved when the organization is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync.
 *
 * <AUTHOR>
 */
@RestLiAssociation(name = "salesSavedAccounts",
    assocKeys = {
        @Key(name = "owner", type = com.linkedin.common.urn.SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
        @Key(name = "organization", type = com.linkedin.common.urn.OrganizationUrn.class, typeref = com.linkedin.common.OrganizationUrn.class)
    },
    namespace = "com.linkedin.sales")
@CreateOnly({"owner", "organization"})
public class SalesSavedAccountsResource extends AssociationResourceTaskTemplate<SalesAccount> {
    private final SalesAccountsService _salesAccountsService;
    @Inject
    public SalesSavedAccountsResource(SalesAccountsService salesAccountsService) {
        this._salesAccountsService = salesAccountsService;
    }

    /**
     * Find sales accounts owned by the given seat
     * @param pagingContext The paging context
     * @param owner The seatUrn of the sales account owner
     * @param filter The filtering condition to apply when fetching the accounts, for example, STAR_ONLY for
     *               fetching only starred accounts and UN_STAR_ONLY for unstarred accounts.  By default, no filtering
     *               is applied.
     * @param sortCriteria How the result should be sorted, for example, by account created time or star modification time.
     *                     By default, no sorting criteria is applied, i.e. the result is not sorted.
     * @param sortOrder How the result should be sorted, either in descending or ascending order. By default,
     *                  the result is ordered in descending order.  This parameter is effective only if the sortCriteria
     *                  parameter is specified.
     * @return a collection of sales accounts that are owned by the seat. The total count in response is always the total
     *         sales account count w/o any filtering applied.
     */
    @Finder("owner")
    public Task<BasicCollectionResult<SalesAccount>> findByOwner(@PagingContextParam final PagingContext pagingContext,
        @QueryParam(value = "owner", typeref = com.linkedin.common.SeatUrn.class) SeatUrn owner,
        @QueryParam(value = "filter") @Optional SalesAccountFilter filter,
        @QueryParam(value = "sortCriteria") @Optional SalesAccountSortCriteria sortCriteria,
        @QueryParam(value = "sortOrder") @Optional("DESCENDING") SortOrder sortOrder) {
        int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : DEFAULT_START;
        int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : DEFAULT_COUNT;
        return _salesAccountsService.getSalesAccountsForGivenOwner(owner, start, count, filter, sortCriteria, sortOrder)
            .map(paginatedList -> new BasicCollectionResult<>(paginatedList.getResult(), paginatedList.getTotal()));
    }

    /**
     * Create a sales account.
     * @param salesAccount sales account to be created
     * @return a create response that indicates if the sales account is created successfully.
     * return 201 if created successfully, return 200 if the entity to be created already exists,
     * errorCode: 412 -> saved account limit exceeded
     *            500 -> DB failure
     */
    @RestMethod.Create
    public Task<CreateResponse> create(SalesAccount salesAccount) {
        return _salesAccountsService.createSavedAccount(salesAccount).map(Pair::getSecond);
    }

    /**
     * Batch create multiple sales accounts.
     * @param batchCreateRequest batch create request containing sales accounts to be created
     * @return a batch create result that indicates if each sales account is created successfully.
     * return 201 if created successfully, return 200 if the entity to be created already exists
     * errorCode: 500 -> DB failure, 400 -> owner is not identical in one call
     */
    @RestMethod.BatchCreate
    public Task<BatchCreateResult<CompoundKey, SalesAccount>> batchCreate(
        BatchCreateRequest<CompoundKey, SalesAccount> batchCreateRequest) {
        return _salesAccountsService.batchCreateSavedAccounts(batchCreateRequest.getInput()).map(resultMap -> {
            List<CreateResponse> createResponseList = batchCreateRequest.getInput()
                .stream()
                .map(salesAccount -> getCreateResponseFromResult(salesAccount, resultMap))
                .collect(Collectors.toList());
            return new BatchCreateResult<>(createResponseList);
        });
    }

    /**
     * Delete a sales account.
     * @param key the compound key of sales account to be deleted
     * @return a update response that indicates if the sales account is deleted successfully.
     * return 204 if the record is deleted successfully, return 200 if the record doesn't exist
     * errorCode: 500 -> DB failure, 400 -> Bad Request
     */
    @RestMethod.Delete
    public Task<UpdateResponse> delete(final CompoundKey key) {
        return _salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(key)).map(responseMap -> {
            if (!responseMap.containsKey(key)) {
                return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
            } else {
                return responseMap.get(key);
            }
        });
    }

    /**
     * Delete multiple sales accounts
     * @param batchDeleteRequest batch delete request containing the keys of sales accounts to be deleted
     * @return a batch update result which indicates if each sales account is deleted successfully.
     * return 204 if the record is deleted successfully, return 200 if the record doesn't exist
     * errorCode: 500 -> DB failure, 400 -> Bad Request
     */
    @RestMethod.BatchDelete
    public Task<BatchUpdateResult<CompoundKey, SalesAccount>> batchDelete(
        final BatchDeleteRequest<CompoundKey, SalesAccount> batchDeleteRequest) {
        return _salesAccountsService.batchDeleteSavedAccounts(batchDeleteRequest.getKeys()).map(responseMap -> {
            Map<CompoundKey, UpdateResponse> resultMap = new HashMap<>();
            Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
            batchDeleteRequest.getKeys().forEach(key -> {
                if (!responseMap.containsKey(key)) {
                    errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                        "Failed to delete savedAccount due to internal error with key" + key));
                } else {
                    switch (responseMap.get(key).getStatus()) {
                        case S_204_NO_CONTENT:
                        case S_200_OK:
                            resultMap.put(key, responseMap.get(key));
                            break;
                        case S_400_BAD_REQUEST:
                            errorMap.put(key, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
                            break;
                        default:
                            errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                                "Failed to delete savedAccount due to internal error with key" + key));
                            break;
                    }
                }
            });
            return new BatchUpdateResult<>(resultMap, errorMap);
        });
    }

    /**
     * Get a sales account
     * @param key the compound key of sales account to get
     * @return the sales account that the input compound key maps to
     * error code: 500 -> DB failure, 400 -> Bad Request, 404 -> Entity Not Found
     */
    @RestMethod.Get
    public Task<SalesAccount> get(CompoundKey key) {
        return _salesAccountsService.getSalesAccountForGivenCompoundKey(key);
    }

    /**
     * Batch get the sales accounts
     * @param keys the compound keys of sales accounts to get
     * @return the map with compound key to sales lead mapping
     * The returning map doesn't contain the key for which no sales account is found.
     */
    @RestMethod.BatchGet
    public Task<Map<CompoundKey, SalesAccount>> batchGet(Set<CompoundKey> keys) {
        return _salesAccountsService.batchGetSalesAccounts(keys);
    }

    /**
     * Batch partial update of sales accounts
     * @param batchPatchRequest map of (compoundKey, salesAccount) pairs
     * @return the batch partial update result with compound key to sales account mapping.
     * error code: 400 if the patch request was incorrect.
     *             404 if the entity is not found.
     *             500 if there was an internal error
     */
    @RestMethod.BatchPartialUpdate
    public Task<BatchUpdateResult<CompoundKey, SalesAccount>> batchPartialUpdate(
        final BatchPatchRequest<CompoundKey, SalesAccount> batchPatchRequest) {
        return _salesAccountsService.batchPartialUpdateSalesAccounts(batchPatchRequest.getData()).flatMap(resultMap -> {
            Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
            Map<CompoundKey, UpdateResponse> successMap = new HashMap<>();

            batchPatchRequest.getData().keySet().forEach(compoundKey -> {
                UpdateResponse updateResponse = resultMap.get(compoundKey);
                HttpStatus status = (updateResponse == null) ? HttpStatus.S_404_NOT_FOUND : updateResponse.getStatus();
                if (status != HttpStatus.S_200_OK) {
                    errorMap.put(compoundKey, new RestLiServiceException(status));
                } else {
                    successMap.put(compoundKey, updateResponse);
                }
            });

            return Task.value(new BatchUpdateResult<>(successMap, errorMap));
        });
    }

    /**
     * Helper function to get Create Response based on SavedAccount batchCreate result map
     * @param salesAccount sales account to be created
     * @param resultMap mapping of CompoundKey to CreateResponse returned by SavedAccount batchCreate service method
     * @return a create response
     */
    private CreateResponse getCreateResponseFromResult(@NonNull SalesAccount salesAccount,
        @NonNull Map<CompoundKey, CreateResponse> resultMap) {
        CompoundKey compoundKey = _salesAccountsService.getCompoundKey(salesAccount);
        if (!resultMap.containsKey(compoundKey)) {
            RestLiServiceException exception = new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "Failed to create the savedAccount" + compoundKey);
            resultMap.put(compoundKey, new CreateResponse(exception));
        }
        return RestliUtils.createCreateResponseWithDatamapInError(compoundKey, resultMap.get(compoundKey),
            SalesSavedAccountsResource.class);
    }
}
