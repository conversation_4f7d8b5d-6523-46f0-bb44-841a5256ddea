package com.linkedin.sales.rest.leadaccount;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.service.utils.RestliUtils;
import com.linkedin.salesleadaccount.SalesLead;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * This class manages LSS's saved leads behavior.
 * A lead is a Linkedin member that represents a potential or existing customer of a Sales Navigator user.
 * A lead becomes saved when the member is saved by a Sales Navigator user through UI or auto-saved by other data pipeline like CRM sync.
 *
 * <AUTHOR>
 */
@RestLiAssociation(name = "salesSavedLeads", assocKeys = {
    @Key(name = "owner", type = com.linkedin.common.urn.SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
    @Key(name = "member", type = com.linkedin.common.urn.MemberUrn.class, typeref = com.linkedin.common.MemberUrn.class)}, namespace = "com.linkedin.sales")
@CreateOnly({"owner", "member"})
public class SalesSavedLeadsResource extends AssociationResourceTaskTemplate<SalesLead> {

  private final SavedLeadService _savedLeadService;

  @Inject
  public SalesSavedLeadsResource(SavedLeadService savedLeadService) {
    _savedLeadService = savedLeadService;
  }

  /**
   * Find sales leads owned by the given seat
   * @param pagingContext the paging context
   * @param owner the seatUrn of the sales lead owner
   * @return a collection of sales leads that are owned by the seat
   */
  @Finder("owner")
  public Task<BasicCollectionResult<SalesLead>> findByOwner(@PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "owner", typeref = com.linkedin.common.SeatUrn.class) SeatUrn owner) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : DEFAULT_COUNT;
    return _savedLeadService.getSalesLeadsForGivenOwner(owner, start, count)
        .map(paginatedList -> new BasicCollectionResult<>(paginatedList.getResult(), paginatedList.getTotal()));
  }

  /**
   * Create a sales lead.
   * @param salesLead sales lead to be created
   * @return a create response that indicates if the sales lead is created successfully.
   * The returning http status could be 201 for success, 200 if the entity to be created already exists and 500 for any
   * other exception. It throws 412 exception for exceeding lead limit.
   */
  @RestMethod.Create
  public Task<CreateResponse> create(SalesLead salesLead) {
    return _savedLeadService.createSavedLead(salesLead);
  }

  /**
   * Batch create multiple sales leads.
   * @param batchCreateRequest batch create request containing sales leads to be created
   * @return a batch create result that indicates if each sales lead is created successfully.
   * The returning http status for each compound key could be 201 for success, 200 if the entity to be created already
   * exists and 500 for any other exception. It throws 400 exception for non identity owner, 412 exception for exceeding
   * lead limit.
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<CompoundKey, SalesLead>> batchCreate(
      BatchCreateRequest<CompoundKey, SalesLead> batchCreateRequest) {
    List<SalesLead> salesLeadsToCreate = batchCreateRequest.getInput();
    return _savedLeadService.batchCreateSavedLeads(new HashSet<>(salesLeadsToCreate)).map(resultMap -> {
      List<CreateResponse> createResponseList = salesLeadsToCreate.stream().map(salesLead -> {
        CompoundKey compoundKey = _savedLeadService.getSavedLeadCompoundKey(salesLead);
        if (!resultMap.containsKey(compoundKey)) {
          RestLiServiceException exception = new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
              String.format("Failed to create saved lead %s", salesLead));
          resultMap.put(compoundKey, new CreateResponse(exception));
        }
        return RestliUtils.createCreateResponseWithDatamapInError(compoundKey, resultMap.get(compoundKey),
            SalesSavedLeadsResource.class);
      }).collect(Collectors.toList());
      return new BatchCreateResult<>(createResponseList);
    });
  }

  /**
   * Delete a sales lead.
   * @param key the compound key of sales lead to be deleted
   * @return a update response that indicates if the sales lead is deleted successfully.
   * The returning http status could be 204 for success, 200 if the entity to be deleted doesn't exist, 400 for bad
   * request and 500 for any other exception.
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final CompoundKey key) {
    return _savedLeadService.deleteSavedLead(key);
  }

  /**
   * Delete multiple sales leads
   * @param batchDeleteRequest batch delete request containing the keys of sales leads to be deleted
   * @return a batch update result which indicates if each sales lead is deleted successfully.
   * The returning http status for each compound key could be 204 for success, 200 if the entity to be deleted doesn't
   * exist, 400 for bad request and 500 for any other exception.
   */
  @RestMethod.BatchDelete
  public Task<BatchUpdateResult<CompoundKey, SalesLead>> batchDelete(
      final BatchDeleteRequest<CompoundKey, SalesLead> batchDeleteRequest) {
    return _savedLeadService.batchDeleteSavedLeads(batchDeleteRequest.getKeys()).map(responseMap -> {
      Map<CompoundKey, UpdateResponse> resultMap = new HashMap<>();
      Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
      batchDeleteRequest.getKeys().forEach(key -> {
        UpdateResponse response = responseMap.get(key);
        if (response == null) {
          errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        } else {
          switch (response.getStatus()) {
            case S_204_NO_CONTENT:
            case S_200_OK:
              resultMap.put(key, response);
              break;
            case S_400_BAD_REQUEST:
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
              break;
            default:
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
              break;
          }
        }
      });
      return new BatchUpdateResult<>(resultMap, errorMap);
    });
  }

  /**
   * Get a sales lead
   * @param key the compound key of sales lead to get
   * @return the sales lead that the input compound key maps to
   * error code: 500 -> DB failure, 400 -> Bad Request, 404 -> Entity Not Found
   */
  @RestMethod.Get
  public Task<SalesLead> get(CompoundKey key) {
    return _savedLeadService.getSalesLead(key);
  }

  /**
   * Batch get sales leads
   * @param keys the compound keys of sales lead to get
   * @return the map with compound key to sales lead mapping
   * The returning map doesn't contain the key for which no sales lead is found.
   */
  @RestMethod.BatchGet
  public Task<Map<CompoundKey, SalesLead>> batchGet(Set<CompoundKey> keys) {
    return _savedLeadService.batchGetSalesLeads(keys);
  }
}
