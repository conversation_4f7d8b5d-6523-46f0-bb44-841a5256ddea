package com.linkedin.sales.rest.list;

import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.service.list.SalesAccountToListMappingService;
import com.linkedin.saleslist.AccountToListMapping;
import javax.inject.Inject;

import static com.linkedin.restli.common.HttpStatus.*;


/**
 * The resource to help create/find the mapping between an account and a Sales Navigator custom list created to
 * represent the associated account map.
 */
@RestLiAssociation(name = "salesAccountToListMappings", assocKeys = {
    @Key(name = "owner", type = SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
    @Key(name = "account", type = OrganizationUrn.class, typeref = com.linkedin.common.OrganizationUrn.class),
    @Key(name = "list", type = SalesListUrn.class, typeref = com.linkedin.common.SalesListUrn.class)},
    namespace = "com.linkedin.sales", keyName = "salesAccountToListMappingsKey")

@CreateOnly({"owner", "account", "list", "contract"})
public class SalesAccountToListMappingResource extends AssociationResourceTaskTemplate<AccountToListMapping> {

  private final SalesAccountToListMappingService _salesAccountToListMappingService;

  @Inject
  public SalesAccountToListMappingResource(SalesAccountToListMappingService salesAccountToListMappingService) {
    _salesAccountToListMappingService = salesAccountToListMappingService;
  }

  /**
   * Associate a list to the account map for the given seat.
   * @param accountToListMapping the accountToListMapping object to be created
   * @return create response to tell if the entity is created successfully
   * 201 -> created successfully, 200 -> mapping already exists, 500 -> DB failure, 400 -> bad Request
   */
  @RestMethod.Create
  public Task<CreateResponse> create(AccountToListMapping accountToListMapping) {
    return _salesAccountToListMappingService.createAccountToListMapping(accountToListMapping)
        .recover(throwable -> new CreateResponse(new RestLiServiceException(S_500_INTERNAL_SERVER_ERROR,
            "Failed to create the accountToListMapping " + accountToListMapping)));
  }

  /**
   * Delete the association between a list and the account map for the given seat
   *
   * @param accountToListMappingKey the accountToListMappingKey that need to be deleted
   * @return Unpdate response to tell if the deletion succeeds.
   * 500 -> DB failure, 400 -> Bad request
   * 204 -> Successful delete operation. If this mapping is not available before deletion, 204 will still be returned.
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(CompoundKey accountToListMappingKey) {
    return _salesAccountToListMappingService.deleteAccountToListMapping(accountToListMappingKey)
        .map(updateResponse -> {
      switch (updateResponse.getStatus()) {
        case S_500_INTERNAL_SERVER_ERROR:
          throw new RestLiServiceException(S_500_INTERNAL_SERVER_ERROR,
              "delete the accountToListMapping get internal error" + accountToListMappingKey);
        case S_400_BAD_REQUEST:
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST);
        default:
          return updateResponse;
      }
    });
  }

  /**
   * Return AccountToListMappings given the owner seat urn and organization urn
   *
   * @param owner the seat urn of the owner of the list created to represent an account map for the specified
   *                account
   * @param account organization target of the account map
   * @param pagingContext Pagination Param
   * @return A collection of accountToListMappings with the given seat urn and organization urn
   *
   */
  @Finder("seatAndAccount")
  public Task<BasicCollectionResult<AccountToListMapping>> findBySeatAndAccount(
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn owner,
      final @QueryParam(value = "account", typeref = com.linkedin.common.OrganizationUrn.class) OrganizationUrn account,
      final @PagingContextParam PagingContext pagingContext) {
    return _salesAccountToListMappingService
        .getAccountToListMappingForAccount(owner, account, pagingContext.getStart(), pagingContext.getCount())
        .map(paginatedResults -> new BasicCollectionResult<>(paginatedResults.getResult(), paginatedResults.getTotal()));
  }
}
