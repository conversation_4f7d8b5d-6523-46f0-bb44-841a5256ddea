package com.linkedin.sales.rest.list;

import com.google.common.base.Preconditions;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.list.SalesDefaultListService;
import com.linkedin.saleslist.DefaultList;
import com.linkedin.saleslist.DefaultListKey;
import javax.inject.Inject;


/**
 * The resource to help manage a seat's default list for a given list type.
 */
@RestLiCollection(name = "salesDefaultLists", namespace = "com.linkedin.sales", keyName = "key")
public class SalesDefaultListResource extends ComplexKeyResourceTaskTemplate<DefaultListKey, EmptyRecord, DefaultList> {

  private final SalesDefaultListService _salesDefaultListService;

  @Inject
  public SalesDefaultListResource(SalesDefaultListService salesDefaultListService) {
    _salesDefaultListService = salesDefaultListService;
  }

  /**
   * Get the default list.
   * @param key key of the default list
   * @return the default list
   * error code:
   * 404 -> Entity Not Found
   * 500 -> DB failure
   */
  @RestMethod.Get
  public Task<DefaultList> get(ComplexResourceKey<DefaultListKey, EmptyRecord> key) {
    return _salesDefaultListService.getDefaultList(key.getKey());
  }

  /**
   * Upsert a default list.
   * @param key key of the default list
   * @param defaultList the default list to be upserted
   * @return update response
   * status code:
   * 200 -> an existing default list updated
   * 201 -> a new default list created
   * 500 -> DB failure
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(ComplexResourceKey<DefaultListKey, EmptyRecord> key,
      final DefaultList defaultList) {
    DefaultListKey defaultListKey = key.getKey();
    Preconditions.checkArgument(defaultListKey.getSeat().equals(defaultList.getSeat())
            && defaultListKey.getListType() == defaultList.getListType(),
        String.format("The seat or list type in the key: %s does not match those in the default list: %s", key,
            defaultList));

    return _salesDefaultListService.upsertDefaultList(defaultListKey, defaultList);
  }

  /**
   * Delete a default list.
   *
   * @param key key of the default list
   * @return Update response
   * status code:
   * 204 -> Successful delete operation. If the default list is not available before deletion,
   *        204 will still be returned.
   * 500 -> DB failure
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(ComplexResourceKey<DefaultListKey, EmptyRecord> key) {
    return _salesDefaultListService.deleteDefaultList(key.getKey());
  }
}
