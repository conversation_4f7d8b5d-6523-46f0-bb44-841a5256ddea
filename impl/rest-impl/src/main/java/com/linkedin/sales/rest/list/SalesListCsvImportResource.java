package com.linkedin.sales.rest.list;

import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.list.SalesListCsvImportService;
import com.linkedin.saleslist.ListCsvImport;
import com.linkedin.saleslist.ListCsvImportStartRequest;
import com.linkedin.saleslist.ListCsvImportState;
import java.util.Set;
import javax.inject.Inject;


/**
 * A resource to manage List CSV Imports. A List CSV Import is a workflow that imports
 * a CSV file, matches its contents with known LinkedIn companies, create Sales List entities for those
 * companies, and than finally create a Sales List containing those entities.
 *
 * An instance of ListCsvImport contains the state of the overall import workflow, information about the
 * List being created and about the CSV matching process.
 *
 * A single action is provided to both create and instance of ListCsvImport and start the import workflow.
 */
@RestLiCollection(name = "salesListCsvImports",
    keyName = "id",
    namespace = "com.linkedin.sales")
public class SalesListCsvImportResource extends CollectionResourceTaskTemplate<Long, ListCsvImport> {

  private final SalesListCsvImportService _salesListCsvImportService;

  @Inject
  public SalesListCsvImportResource(SalesListCsvImportService salesListCsvImportService) {
    _salesListCsvImportService = salesListCsvImportService;
  }

  /**
   * Create and start a List CSV Import workflow which is comprised of:
   * 1) Creating a Sales Insights CSV Import Task for the given CSV file
   * 2) Starting the Sales Insights CSV Import task
   * 3) Generating an ID for the Sales List that will be created once the import workflow completes in the case of
   *    importing to a new list, i.e. list field not specified in the request
   * 4) Creating a List CSV Import instance to track the CSV import workflow
   * @param startRequest details about the CSV file to import and the List to create
   * @return an instance of ListCsvImport for tracking the status of the import workflow
   */
  @Action(name = "createAndStart")
  public Task<ListCsvImport> createAndStart(@ActionParam(value = "createAndStartRequest") ListCsvImportStartRequest startRequest) {
    return _salesListCsvImportService.createAndStart(startRequest);
  }

  /**
   * Get List CSV Import using its ID
   * @param id the ID of the List CSV Import
   * @return The List CSV Import
   */
  @RestMethod.Get
  @Override
  public Task<ListCsvImport> get(Long id) {
    return _salesListCsvImportService.get(id);
  }

  /**
   * Batch Get a set of List CSV Import records
   * @param ids the IDs of the List CSV Import records to get
   * @return a map of List CSV Import ID to List CSV Import
   */
  @RestMethod.BatchGet
  public Task<BatchResult<Long, ListCsvImport>> getBatchResult(Set<Long> ids) {
    return _salesListCsvImportService.batchGet(ids);
  }

  /**
   * Patch a List CSV Import using its ID. This is meant to be used to update the List CSV Import status
   * once the import workflow completes.
   * @param id the ID of the List CSV Import
   * @param patch the List CSV Import patch
   * @return an update response
   */
  @RestMethod.PartialUpdate
  @Override
  public Task<UpdateResponse> update(Long id, PatchRequest<ListCsvImport> patch) {
    return _salesListCsvImportService.partialUpdate(id, patch)
      .map(isSuccessful -> {
        if (isSuccessful) {
          return new UpdateResponse(HttpStatus.S_204_NO_CONTENT);
        } else {
          throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND);
        }
      });
  }

  /**
   * Delete a List CSV Import using its ID. Delete is only once the import is no longer IN_PROGRESS.
   * @param id the ID of the List CSV Import
   * @return an update response
   */
  @Override
  public Task<UpdateResponse> delete(Long id) {
    return _salesListCsvImportService.delete(id)
        .map(isSuccessful -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
  }

  /**
   * Find List CSV Import entities by the Seat that created the List CSV Import and optionally specify which
   * states the imports must be in
   * @param pagingContext the paging context
   * @param creator the SeatUrn of the creator
   * @param states optionally indicate which states to find imports in. If not provided, the default is all states.
   * @return a list of List CSV Import instances
   */
  @Finder("creator")
  public Task<BasicCollectionResult<ListCsvImport>> findByCreator(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "creator", typeref = com.linkedin.common.SeatUrn.class) SeatUrn creator,
      @QueryParam(value = "states") @Optional("[]")  ListCsvImportState[] states) {
    return _salesListCsvImportService.findByCreator(pagingContext, creator, states);
  }

  /**
   * Find List CSV Import entities by the CSV Import Task that was created to process the import.
   * Exactly one record should be returned.
   * @param csvImportTask the URN of the CSV Import Task
   * @return a List CSV Import instance
   */
  @Finder("csvImportTask")
  public Task<BasicCollectionResult<ListCsvImport>> findByCsvImportTask(
      @QueryParam(value = "csvImportTask", typeref = com.linkedin.common.CsvImportTaskUrn.class) CsvImportTaskUrn csvImportTask) {
    return _salesListCsvImportService.findByCsvImportTask(csvImportTask);
  }

  /**
   * Find List CSV Import entities by the List that will be, or was created, as part of the CSV import workflow
   * Exactly one record should be returned.
   * @param list the URN of the Sales List
   * @return a List CSV Import instance
   */
  @Finder("list")
  public Task<BasicCollectionResult<ListCsvImport>> findByList(
      @QueryParam(value = "list", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn list) {
    return _salesListCsvImportService.findByList(list);
  }
}
