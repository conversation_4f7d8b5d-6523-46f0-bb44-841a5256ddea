package com.linkedin.sales.rest.list;

import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.sales.service.list.SalesListEntityService;
import com.linkedin.sales.service.utils.RestliUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.saleslist.ListEntity;
import com.linkedin.saleslist.ListEntityOrdering;
import com.linkedin.talent.decorator.PathSpecSet;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.inject.Inject;
import org.testng.collections.Maps;


/**
 * The resource to help create/delete/find entities in the list
 * <AUTHOR>
 */
@RestLiAssociation(name = "salesListEntities",
    assocKeys = {
        @Key(name = "list", type = com.linkedin.common.urn.SalesListUrn.class, typeref = com.linkedin.common.SalesListUrn.class),
        @Key(name = "entity", type = com.linkedin.common.urn.Urn.class, typeref = com.linkedin.common.Urn.class)
    },
    namespace = "com.linkedin.sales")
@ReadOnly({"priorityInfo/created", "priorityInfo/lastModified", "leadManager/created", "leadManager/lastModified",
    "leadOwner/created", "leadOwner/lastModified", "leadRole/created", "leadRole/lastModified",
    "leadRelationshipStrength/created", "leadRelationshipStrength/lastModified", "leadText/created",
    "leadText/lastModified"
})
@CreateOnly({"creator", "createdAt", "list", "entity"})
public class SalesListEntityResource extends AssociationResourceTaskTemplate<ListEntity> {

  @Inject
  SalesListEntityService _salesListEntityService;

  /**
   * create an entity in a list. Please make sure the createdTime is in milliseconds.
   * @param listEntity the list entity object
   * @param contract contract of the user. It will be used for the ownership check.
   * @return create response to tell if the entity is created successfully.
   * errorCode: 500 -> DB failure, 409 -> create same entity twice. 403 -> no permission to create the entity
   * 412 -> hit the upper limit of the list, 400 -> creator is not identical in one call or list is not found
   */
  @RestMethod.Create
  public Task<CreateResponse> create(ListEntity listEntity,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {
    return _salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), contract, false)
        .map(resultMap  -> {
          CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity.getList())
              .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity.getEntity());
          if (!resultMap.containsKey(compoundKey)) {
            RestLiServiceException exception =
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to create the entity");
            resultMap.put(compoundKey, new CreateResponse(exception));
          }
          return createCreateResponseWithDatamapInError(compoundKey, resultMap.get(compoundKey));
        });
  }

  /**
   * batch create entities in lists
   * @param batchCreateRequest all the list entity create request
   * @param contract contract of the user. It will be used for the ownership check.
   * @param isLowPriorityOperation determines the priority for espresso quota. If set to true, the request would be deprioritized and can result in 429.
   *                               Should be used for nearline traffic when request can be retried.
   * @return batch create result to tell if the entity is created successfully
   * errorCode: 500 -> DB failure, 409 -> create same entity twice. 403 -> no permission to create the entity
   * 412 -> hit the upper limit of the list, 400 -> creator is not identical in one call or list is not found
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<CompoundKey, ListEntity>>
  batchCreate(BatchCreateRequest<CompoundKey, ListEntity> batchCreateRequest,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      final @QueryParam(value = "lowPriorityOperation") @Optional("false") boolean isLowPriorityOperation
      ) {

    return _salesListEntityService.batchCreateListEntities(batchCreateRequest.getInput(), contract, isLowPriorityOperation)
        .map(resultMap -> {
          List<CreateResponse> createResponseList = batchCreateRequest.getInput().stream().map(listEntity -> {
            CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity.getList())
                .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity.getEntity());
            if (!resultMap.containsKey(compoundKey)) {
              RestLiServiceException exception =
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "fail to create the entity");
              resultMap.put(compoundKey, new CreateResponse(exception));
            }
            return createCreateResponseWithDatamapInError(compoundKey, resultMap.get(compoundKey));
          }).collect(Collectors.toList());
          return new BatchCreateResult<>(createResponseList);
        });
  }

  /**
   * delete an entity from the list
   * @param compoundKey the list entity key to indicate which entity to delete
   * @param seat seat of the user. It will be used for the ownership check.
   * @param contract contract of the user. It will be used for the ownership check.
   * @return update response to tell if the entity is deleted successfully. If this entity is not available before deletion, still 204 will return.
   * errorCode: 500 -> DB failure, 403 -> no permission to create the entity. 400 -> the list id does not exist
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final CompoundKey compoundKey,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {
    return _salesListEntityService.deleteListEntity(compoundKey, seat)
        .map(updateResponse -> {
          switch (updateResponse.getStatus()) {
            case S_403_FORBIDDEN:
              throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
                  "no permission to delete the list entity " + compoundKey);
            case S_400_BAD_REQUEST:
              throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "the list does not exist " + compoundKey);
            default:
              return updateResponse;
          }
        });
  }

  /**
   * batch delete entities from the list
   * @param listEntities the list entities that need to be deleted
   * @param seat seat of the user. It will be used for the ownership check.
   * @param contract contract of the user. It will be used for the ownership check.
   * @param isLowPriorityOperation determines the priority for espresso quota. If set to true, the request would be deprioritized and can result in 429.
   *                               Should be used for nearline traffic when request can be retried.
   * @return batch update result to tell if the entities are deleted. If this entity is not available before deletion, still 204 will return.
   * errorCode: 500 -> DB failure, 403 -> no permission to delete the entity. 400 -> the list id does not exist
   */
  @RestMethod.BatchDelete
  public Task<BatchUpdateResult<CompoundKey, ListEntity>>
  batchDelete(final BatchDeleteRequest<CompoundKey, ListEntity> listEntities,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      final @QueryParam(value = "lowPriorityOperation") @Optional("false") boolean isLowPriorityOperation) {

    return _salesListEntityService.batchDeleteListEntities(listEntities.getKeys(), seat, isLowPriorityOperation).map(responseMap -> {
      Map<CompoundKey, UpdateResponse> resultMap = new HashMap<>();
      Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
      listEntities.getKeys().forEach(key -> {
        UpdateResponse response = responseMap.get(key);
        if (response == null) {
          errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        } else {
          switch (response.getStatus()) {
            case S_204_NO_CONTENT:
              resultMap.put(key, response);
              break;
            case S_403_FORBIDDEN:
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, "no permission to delete the list entity"));
              break;
            case S_400_BAD_REQUEST:
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "the list does not exist"));
              break;
            default:
              errorMap.put(key, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
              break;
          }
        }
      });
      return new BatchUpdateResult<>(resultMap, errorMap);
    });
  }

  /**
   * Find entities inside the list and give back the result with some certain sorting.
   * @param list listUrn that contains the entity
   * @param seat seat Urn for the viewer
   * @param contract contract Urn for the viewer
   * @param sortCriteria the sort criteria for the entity in the list. Default is the last updated time. Currently it only support last updated time only.
   * @param sortOrder the sort order of the entity, could be DESCENDING or ASCENDING. Default is DESCENDING.
   * @return a collection of list entities that belongs to the list
   */
  @Finder("list")
  public Task<BasicCollectionResult<ListEntity>> findByList(
      @PagingContextParam final PagingContext pagingContext,
      final @QueryParam(value = "list", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn list,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      final @QueryParam(value = "sortCriteria") @Optional("LAST_ACTIVITY") ListEntityOrdering sortCriteria,
      final @QueryParam(value = "sortOrder") @Optional("DESCENDING") SortOrder sortOrder) {

    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : ServiceConstants.DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : ServiceConstants.DEFAULT_COUNT;

    // support sorting by LAST_ACTIVITY only in lss-mt, other sorts can be supported after decoration.
    return _salesListEntityService.getListEntities(seat, list.getIdAsLong(), start, count, sortOrder)
        .map(integerListPair -> new BasicCollectionResult<>(integerListPair.getSecond(), integerListPair.getFirst()));
  }

  /**
   * Find entities inside the list with the given sort criteria and sort order. This finder is similar to the
   * list finder except that it serves a member viewer instead of a seat viewer.
   * @param list listUrn that contains the entity
   * @param viewer member Urn for the viewer
   * @return a collection of list entities that belongs to the list
   */
  @Finder("listForMember")
  public Task<BasicCollectionResult<ListEntity>> findByListForMember(
      @PagingContextParam final PagingContext pagingContext,
      final @QueryParam(value = "list", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn list,
      final @QueryParam(value = "viewer", typeref = com.linkedin.common.MemberUrn.class) MemberUrn viewer) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : ServiceConstants.DEFAULT_START;
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : ServiceConstants.DEFAULT_COUNT;

    return _salesListEntityService.getListEntities(viewer, list.getIdAsLong(), start, count, SortOrder.DESCENDING)
        .map(integerListPair -> new BasicCollectionResult<>(integerListPair.getSecond(), integerListPair.getFirst()));
  }

  /**
   * Returns the entities that belong to any of the provided lists and are accessible to the viewer.
   * @param lists array of listUrns that contains the entity
   * @param seat seat Urn for the viewer
   * @param contract contract Urn for the viewer
   * @return a collection of list entities, or an empty result if there are no entities in the lists or the viewer has
   * no permission to read any of the list entities.
   */
  @Finder("lists")
  public Task<BasicCollectionResult<ListEntity>> findByLists(@PagingContextParam final PagingContext pagingContext,
      final @QueryParam(value = "lists", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn[] lists,
      final @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "viewerContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {
    Set<Long> distinctListIds = Stream.of(lists).map(Urn::getIdAsLong).collect(Collectors.toSet());
    PathSpecSet projections = PathSpecSet.of(getContext().getProjectionMask());
    if (projections != null && projections.getPathSpecs().size() == 1
        && projections.getPathSpecs().contains(com.linkedin.saleslist.ListEntity.fields().entity())) {
      return _salesListEntityService.getListEntityUrnsByListIds(seat.getSeatIdEntity(), distinctListIds,
          pagingContext.getStart(), pagingContext.getCount()).map(listOfUrns -> listOfUrns.stream().map(urn -> {
        ListEntity listEntity = new ListEntity();
        listEntity.setEntity(urn);
        return listEntity;
      }).collect(Collectors.toList())).map(BasicCollectionResult::new);
    } else {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Request must contain only entity in projections which is required for this finder");
    }
  }

  /**
   * Batch get list entities
   * @param ids keys of the entities
   * @param seat seat to be used for permission check, in particular, it is used to check whether the seat
   *             has read access to the lists that contain the entities specified in the compound keys.
   * @return the batch result with compound key to list entity mapping
   * error code: 400 if the request was incorrect.
   *             403 if the viewer has no permission to read the entity.
   *             404 if the entity is not found.
   *             500 if there was an internal error.
   */
  @RestMethod.BatchGet
  public Task<BatchResult<CompoundKey, ListEntity>> batchGet(Set<CompoundKey> ids,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat) {

    return _salesListEntityService.batchGetListEntities(seat.getSeatIdEntity(), ids);
  }

  /**
   * Batch partial update of list entities
   * @param seat seat to be used for permission check, in particular, it is used to check whether the seat
   *             has write access to the lists that contain the entities specified in the compound keys.
   * @param contract the contract that the seat belongs to.
   * @return the batch result with compound key to list entity mapping
   * error code: 400 if the patch request was incorrect.
   *             403 if the viewer is not authorized to update the list entity.
   *             404 if the entity is not found.
   *             500 if there was an internal error
   */
  @RestMethod.BatchPartialUpdate
  public Task<BatchUpdateResult<CompoundKey, ListEntity>> batchPartialUpdate(
      final BatchPatchRequest<CompoundKey, ListEntity> batchPatchRequest,
      final @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "viewerContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    return _salesListEntityService.batchUpdateListEntities(seat, contract, batchPatchRequest.getData())
        .flatMap(resultMap -> {
          Map<CompoundKey, RestLiServiceException> errorMap = Maps.newHashMap();
          Map<CompoundKey, UpdateResponse> successMap = Maps.newHashMap();

          batchPatchRequest.getData().keySet().forEach(compoundKey -> {
            UpdateResponse updateResponse = resultMap.get(compoundKey);
            if (updateResponse == null || resultMap.get(compoundKey).getStatus() != HttpStatus.S_200_OK) {
              errorMap.put(compoundKey, updateResponse == null ? new RestLiServiceException(HttpStatus.S_404_NOT_FOUND)
                  : new RestLiServiceException(updateResponse.getStatus()));
            } else {
              successMap.put(compoundKey, updateResponse);
            }
          });

          return Task.value(new BatchUpdateResult<>(successMap, errorMap));
        });
  }

  private CreateResponse createCreateResponseWithDatamapInError(CompoundKey compoundKey, CreateResponse createResponse) {
    if (createResponse.hasError()) {
      RestLiServiceException exception = createResponse.getError();
      exception.setErrorDetails(compoundKey.toDataMap(RestliUtils.parseCompoundKeyAnnotation(SalesListEntityResource.class)));
      return new CreateResponse(exception);
    } else {
      return createResponse;
    }
  }

  /**
   * Create a placeholder entity in the list. Placeholder entity is a special case where an entity urn is created by the system.
   * The placeholder card's will be added to default positionInLevel of 0.
   * @param salesList list for which the placeholder entity needs to be created. Currently only ACCOUNT_MAP list type is supported.
   * @param seat seat urn for the creator.
   * @param contract contract Urn for the creator.
   * @param createdAt the time (ms since epoch) the entity was created and added to list.
   * @return Action result containing the urn for newly created placeholder entity.
   * errorCode: 500 -> DB failure or entity urn creation failure, 403 -> no permission to create the entity,
   * 400 -> list is not found
   */
  @Action(name = "createPlaceholderEntity", returnTyperef = com.linkedin.sales.SalesListEntityPlaceholderUrn.class)
  public Task<ActionResult<SalesListEntityPlaceholderUrn>> createPlaceholderEntity(
      final @ActionParam(value = "list", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn salesList,
      final @ActionParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @ActionParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      final @ActionParam(value = "createdAt", typeref = com.linkedin.common.Time.class) long createdAt) {
    return _salesListEntityService.createPlaceholderEntity(salesList, contract, seat, createdAt);
  }
}
