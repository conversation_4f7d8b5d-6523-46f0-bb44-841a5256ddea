package com.linkedin.sales.rest.list;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.list.SalesListEntitySummaryService;
import com.linkedin.saleslist.ListEntitySummary;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * The resource to help get the summary of the list for a certain entity (lead/account).
 * The summary could be how many lists the entity belongs to. It could be expanded into other fields in the future.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesListEntitySummaries",
    keyName = "id",
    keyTyperefClass = com.linkedin.common.Urn.class,
    namespace = "com.linkedin.sales")
public class SalesListEntitySummaryResource extends CollectionResourceTaskTemplate<Urn, ListEntitySummary> {

  @Inject
  SalesListEntitySummaryService _salesListEntitySummaryService;
  /**
   * Batch get the summary for entities. The summary for each entity changes with different contract and seat.
   * @param ids the set of entities (lead/account)
   * @param contract the contract Urn of the user in Sales Navigator
   * @param seat the seat Urn of the user in Sales Navigator
   * @return a map that tells the summary for each entity.
   */
  @RestMethod.BatchGet
  public Task<Map<Urn, ListEntitySummary>> batchGet(Set<Urn> ids,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat) {
    return _salesListEntitySummaryService.batchGetEntityCounts(ids, contract, seat);
  }
}
