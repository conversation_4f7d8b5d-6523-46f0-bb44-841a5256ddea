package com.linkedin.sales.rest.list;

import com.google.common.collect.Sets;
import com.linkedin.common.Locale;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.service.list.SalesListService;
import com.linkedin.sales.service.utils.LocaleUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.saleslist.List;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListOwnership;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import com.linkedin.talent.decorator.PathSpecSet;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;


/**
 * The resource to create/update/delete/find the lists in Sales Navigator. Creator will be assigned once it is created and cannot be changed.
 * EntityCount is derived and will be not be updated.
 * Finders to find list can be based on seats or a certain entity.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesLists",
    keyName = "id",
    namespace = "com.linkedin.sales")
@ReadOnly({"id", "entityCount", "lastModifiedBy", "shared"})
@CreateOnly({"creator", "listType"})
public class SalesListResource extends CollectionResourceTaskTemplate<Long, List> {

  @Inject
  SalesListService _salesListService;

  /**
   * Get the list information based on the list id
   * @param id the list id
   * @param viewer the viewer of the list, either a SeatUrn or a MemberUrn
   * @param locale the locale used to return the localized values for system generated lists. Default to en_US.
   * @return the list that has this id
   */
  @RestMethod.Get
  public Task<List> get(Long id, @QueryParam(value = "viewer", typeref = com.linkedin.common.Urn.class) Urn viewer,
      @QueryParam(value = "locale") @Optional Locale locale) {
    return _salesListService.getList(id, viewer, LocaleUtils.getJavaLocaleOrDefault(locale));
  }

  /**
   * Batch get the list information based on a set of list ids
   * @param ids the list ids
   * @param viewer the sales identification of the user
   * @param locale the locale used to return the localized values for system generated lists. Default to en_US.
   * @return the batch result with list id to list mapping
   */
  @RestMethod.BatchGet
  public Task<BatchResult<Long, List>> get(Set<Long> ids, @QueryParam(value = "viewer", typeref = com.linkedin.common.SeatUrn.class) SeatUrn viewer,
      @QueryParam(value = "locale") @Optional Locale locale) {
    return _salesListService.batchGetLists(ids, viewer.getSeatIdEntity(), LocaleUtils.getJavaLocaleOrDefault(locale)).map(listMap -> {
      Map<Long, RestLiServiceException> errorMap = ids.stream()
          .filter(id -> !listMap.containsKey(id))
          .collect(Collectors.toMap(id -> id, id -> new RestLiServiceException(HttpStatus.S_404_NOT_FOUND)));
      return new BatchResult<>(listMap, errorMap);
    });
  }

  /**
   * create a new list
   * @param list the list the seat needs to create
   * @param contract contract of the user. It will be used for the ownership check.
   * @return create response to indicate if the creation succeeds
   */
  @RestMethod.Create
  public Task<CreateResponse> create(List list,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    return _salesListService.createList(list, contract.getContractIdEntity())
        .map(listId -> new CreateResponse(listId, HttpStatus.S_201_CREATED));
  }

  /**
   * batch create a collection of new lists
   * @param lists the lists to be created
   * @param contract contract of the user. It will be used for the ownership check.
   * @return batch create response to indicate if the creation for each list succeeds
   */
  @RestMethod.BatchCreate
  public Task<BatchCreateResult<Long, List>> batchCreate(BatchCreateRequest<Long, List> lists,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    return _salesListService.batchCreateLists(lists.getInput(), contract.getContractIdEntity());
  }

  /**
   * Delete the list. If list size > 100, the list deletion is online task,
   * but listEntity deletion task will be handled via a nearline flow.
   *
   * @param id the list id the seat needs to delete
   * @param seat seat of the user. It will be used for the ownership check.
   * @param contract contract of the user. It will be used for the ownership check.
   * @return update response to tell if the deletion succeeds
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(Long id,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    return _salesListService.deleteList(id, seat, contract)
        .map(isSuccessful -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
  }

  /**
   * update the list information. The list name, last modified time, last viewed time can be updated.
   * @param id the id of the list
   * @param patch the list information
   * @param seat seat of the user. It will be used for the ownership check.
   * @param contract contract of the user. It will be used for the ownership check.
   * @return update response to tell if the update succeeds
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(Long id, PatchRequest<List> patch,
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    List newList = new List();
    try {
      PatchApplier.applyPatch(newList, patch);
    } catch (DataProcessingException e) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    return _salesListService.updateList(id, newList, seat.getSeatIdEntity()).map(isSuccessful -> {
      if (isSuccessful) {
        return new UpdateResponse(HttpStatus.S_204_NO_CONTENT);
      } else { // cannot find the list to udpate
        throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND);
      }
    });
  }

  /**
   * Upsert a list.
   * Under most circumstances create and partialUpdate should be used instead.
   * This Upsert option exists to support creation of a List using an ID supplied by the caller, which is
   * used by the List CSV Import workflow.
   * If an update is performed, then only list name, last modified time, last viewed time can be updated.
   * @param key the key of the list
   * @param entity the list
   * @param contract the contract the list will be associated with if its created
   * @return update response indicating whether the upsert succeeded or not
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(Long key, List entity,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {

    return _salesListService.upsertList(key, entity, contract.getIdAsLong());
  }

  /**
   * finder that can help get all the lists that are visible to a certain seat.
   * Use projections to reduce BE espresso calls.
   * @param pagingContext  the paging context
   * @param seat the seatUrn for having the access to the lists
   * @param listType list type, could be LEAD, ACCOUNT, or ACCOUNT_MAP
   * @param ownership list ownership to filter the results on. Return all the lists the seat has access to if ownership is absent.
   * @param sortCriteria the ordering criteria that the list ranking is based on
   * @param sortOrder the sort order, could be ASCENDING OR DESCENDING
   * @param listSources A collection of listSource, could be any enum of {@link com.linkedin.saleslist.ListSource}.
   *                    If it's an empty collection we will return list across all listSources.
   * @param locale the locale used to return the localized values for system generated lists. Default to en_US.
   * @return a collection of lists that the seat has access to
   */
  @Finder("seat")
  public Task<BasicCollectionResult<List>> findBySeat(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "listType") ListType listType,
      @QueryParam(value = "ownership") @Optional ListOwnership ownership,
      @QueryParam(value = "sortCriteria") @Optional("LAST_MODIFIED") ListOrdering sortCriteria,
      @QueryParam(value = "sortOrder") @Optional("DESCENDING") SortOrder sortOrder,
      @QueryParam(value = "listSources") @Optional("[]") ListSource[] listSources,
      @QueryParam(value = "locale") @Optional Locale locale) {

    // NOTE: if the user does not provide a count param, we set it to GET_COUNT_LIMIT (1000) to support getting all lists. This is
    // useful for edit list modal where we need to display all lists a seat has.  Previously, the default count was set to -1.
    // However, this would cause a warning from espresso.  See EXC-191684.
    int count = pagingContext.hasCount() ? pagingContext.getCount() : ServiceConstants.GET_COUNT_LIMIT;
    PathSpecSet projections = PathSpecSet.of(getContext().getProjectionMask());
    //TODO: Falling back to ListSource.MANUAL if absent.
    // This is to keep backward compatibility during saved lead/account migration. If user do not provide listSource,
    // we will return list across all listSource(CRM_SYNC, LINKEDIN_SALES_INSIGHTS, MANUAL, SYSTEM)
    Set<ListSource> listSourceSet = (listSources == null || listSources.length == 0)
        ? new HashSet<>(Collections.singletonList(ListSource.MANUAL))
        : Sets.newHashSet(listSources);
    return _salesListService.getListsForSeat(seat.getSeatIdEntity(), listType, sortCriteria, sortOrder, ownership, listSourceSet,
        LocaleUtils.getJavaLocaleOrDefault(locale), pagingContext.getStart(), count, projections)
        .map(resultPair -> new BasicCollectionResult<>(resultPair.getSecond(), resultPair.getFirst()));
  }

  /**
   * finder that can help get all the lists that the entity belongs to for a certain contract/seat
   * @param entity entity Urns. Could be member or organization.
   * @param contract the contractUrn for the user
   * @param seat the seatUrn for the user
   * @param ownership list ownership to filter the results on. Return all the lists the seat has access to if ownership is absent.
   * @param sortCriteria the ordering criteria that the list ranking is based on
   * @param sortOrder the sort order, could be ASCENDING OR DESCENDING
   * @param listSources A collection of listSource, could be any of CRM_SYNC, LINKEDIN_SALES_INSIGHTS, MANUAL, SYSTEM, TAGS_MIGRATION.
   *                   If it's an empty collection we will return list across all listSources.
   * @param locale the locale used to return the localized values for system generated lists. Default to en_US.
   * @param listTypes types of lists to be included in the result.  Valid values are LEAD, ACCOUNT, or ACCOUNT_MAP. Defaults
   *                  to [LEAD, ACCOUNT].
   * @return a collection of lists that the entityDetail belongs to
   */
  @Finder("entity")
  public Task<BasicCollectionResult<List>> findByEntity(
      @QueryParam(value = "entity", typeref = com.linkedin.common.Urn.class) Urn entity,
      @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract,
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) @Optional SeatUrn seat,
      @QueryParam(value = "ownership") @Optional ListOwnership ownership,
      @QueryParam(value = "sortCriteria") @Optional("LAST_MODIFIED") ListOrdering sortCriteria,
      @QueryParam(value = "sortOrder") @Optional("DESCENDING") SortOrder sortOrder,
      @QueryParam(value = "listSources") @Optional("[]") ListSource[] listSources,
      @QueryParam(value = "locale") @Optional Locale locale,
      @QueryParam(value = "listTypes") @Optional("[\"LEAD\", \"ACCOUNT\"]") ListType[] listTypes
  ) {
    // TODO: to return lists that the seat have certain permissions, we may need to add an additional roles QueryParam(array of roles)
    if (seat == null) { // do not support seat == null, but we can not remove "Optional" since it is not backward compatible
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }

    //TODO: Falling back to ListSource.MANUAL if absent.
    // This is to keep backward compatibility during saved lead/account migration. If user do not provide listSource,
    // we will return list across all listSource(CRM_SYNC, LINKEDIN_SALES_INSIGHTS, MANUAL, SYSTEM)
    Set<ListSource> listSourceSet = (listSources == null || listSources.length == 0)
        ? new HashSet<>(Collections.singletonList(ListSource.MANUAL))
        : Sets.newHashSet(listSources);
    return _salesListService.getListsForEntity(entity, contract.getContractIdEntity(), seat.getSeatIdEntity(), ownership,
        sortCriteria, sortOrder, listSourceSet, LocaleUtils.getJavaLocaleOrDefault(locale), Sets.newHashSet(listTypes)).map(BasicCollectionResult::new);
  }

  /**
   * Finds all account maps accessible by the seat for the given organization.
   * <p>By default, account maps will be ordered based on last viewed time descending. Newly shared maps which have not
   * been viewed by the seat will be ordered first.
   * @param seat the seat (owner or collaborator) to find account maps for
   * @param organization the organization to find account maps for
   */
  @Finder("organizationForAccountMap")
  public Task<BasicCollectionResult<List>> findByOrganizationForAccountMap(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "organization", typeref = com.linkedin.common.OrganizationUrn.class) OrganizationUrn organization
  ) {
    return _salesListService.findAccountMapsByOrg(seat, organization);
  }
}
