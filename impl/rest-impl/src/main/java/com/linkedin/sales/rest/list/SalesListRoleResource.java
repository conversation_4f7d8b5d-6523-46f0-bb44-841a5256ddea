package com.linkedin.sales.rest.list;

import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.AssociationResourceTaskTemplate;
import com.linkedin.saleslist.SeatListRoleMapping;


/**
 * The resource to help manage/get the role of the list
 * Created by hacao on 5/25/18
 */
@RestLiAssociation(name = "salesListRoles",
    assocKeys = {
        @Key(name = "seat", type = com.linkedin.common.urn.SeatUrn.class, typeref = com.linkedin.common.SeatUrn.class),
        @Key(name = "list", type = com.linkedin.common.urn.SalesListUrn.class, typeref = com.linkedin.common.SalesListUrn.class)
    },
    namespace = "com.linkedin.sales")
public class SalesListRoleResource extends AssociationResourceTaskTemplate<SeatListRoleMapping> {

  /**
   * Create a seat list role mapping
   * @param seatListRoleMapping the seat list role mapping that needs to be created
   * @return if the creation operation has succeeded
   */
  @RestMethod.Create
  public Task<CreateResponse> create(SeatListRoleMapping seatListRoleMapping) {
    return Task.failure(new RestLiServiceException(HttpStatus.S_501_NOT_IMPLEMENTED));
  }

  /**
   * delete a seat list role mapping
   * @param compoundKey the seat list role mapping that needs to be deleted
   * @return if the deletion operation has succeeded
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(CompoundKey compoundKey) {
    return Task.failure(new RestLiServiceException(HttpStatus.S_501_NOT_IMPLEMENTED));
  }

  /**
   * given a list id find all the seat list role mapping under this list
   * @param pagingContext the paging information
   * @param list the list urn
   * @return a collection of seat list role mapping
   */
  @Finder("list")
  public Task<BasicCollectionResult<SeatListRoleMapping>> findByList(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "list", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn list
  ) {
    return Task.failure(new RestLiServiceException(HttpStatus.S_501_NOT_IMPLEMENTED));
  }

  /**
   * given a seat and a list of list ids, find the seat list role mapping between this seat and the list
   * @param seat the seat Urn of the seat holder
   * @param lists all the list Urns that need to get the roles
   * @return a collection of the seat list role mapping
   */
  @Finder("seat")
  public Task<BasicCollectionResult<SeatListRoleMapping>> findBySeat(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "lists", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn[] lists
  ) {
    return Task.failure(new RestLiServiceException(HttpStatus.S_501_NOT_IMPLEMENTED));
  }

}
