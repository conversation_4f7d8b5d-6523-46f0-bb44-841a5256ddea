package com.linkedin.sales.rest.list;

import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.realtimedispatcher.Authorization;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.SalesRealtimeAuthorizationService;
import java.util.Set;
import javax.inject.Inject;


/**
 * Resource to authorize access to realtime topic SalesRelationshipMapChangeLogsTopic.
 *
 * The resource will be called by realtime frontend when a client needs to subscribe to realtime topic.
 * The resource is created based on realtime authorization resource template in go/realtime
 * The realtime frontend will call GET or BATCH_GET of this resource with the urn of the subscriber to determine if the
 * client can subscribe to the topic.
 */
@RestLiCollection(
    name = "salesRealtimeRelationshipMapChangeLogAuthorization",
    keyName = "id",
    keyTyperefClass = com.linkedin.common.Urn.class,
    namespace = "com.linkedin.sales"
)
public class SalesRealtimeRelationshipMapChangeLogAuthorizationResource extends CollectionResourceTaskTemplate<Urn, Authorization> {
  private final SalesRealtimeAuthorizationService _salesRealtimeAuthorizationService;

  @Inject
  public SalesRealtimeRelationshipMapChangeLogAuthorizationResource(
      SalesRealtimeAuthorizationService salesRealtimeAuthorizationService) {
    _salesRealtimeAuthorizationService = salesRealtimeAuthorizationService;
  }

  /**
   * Get authorization for a subscriber on a given topic.
   * @param topic Topic to authorize. Only accepts SalesRelationshipMapChangeLogsTopic
   * @param subscriber Subscriber to authorize. Current supported: salesIdentity
   * @return Authorization response containing Allowed or Denied.
   */
  @RestMethod.Get
  public Task<Authorization> get(final Urn topic,
      @QueryParam(value = "subscriber", typeref = com.linkedin.common.Urn.class) final Urn subscriber) {
    return _salesRealtimeAuthorizationService.authorizeForRelationshipMapChangeLogTopic(topic,
        subscriber);
  }

  /**
   * Get authorization for a subscriber on a given topics.
   * @param topics Topics to authorize. Only accepts SalesRelationshipMapChangeLogsTopic
   * @param subscriber Subscriber to authorize. Current supported: salesIdentity
   * @return Authorization response containing Allowed or Denied for each topic provided.
   */
  @RestMethod.BatchGet
  public Task<BatchResult<Urn, Authorization>> batchGet(final Set<Urn> topics,
      @QueryParam(value = "subscriber", typeref = com.linkedin.common.Urn.class) final Urn subscriber) {
    return _salesRealtimeAuthorizationService.authorizeForRelationshipMapChangeLogTopics(topics, subscriber);
  }
}
