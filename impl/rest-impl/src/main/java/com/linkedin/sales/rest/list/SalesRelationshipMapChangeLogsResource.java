package com.linkedin.sales.rest.list;

import com.linkedin.common.NullKey;
import com.linkedin.common.TimeRange;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.list.SalesRelationshipMapChangeLogService;
import com.linkedin.saleslist.RelationshipMapChangeLog;
import javax.inject.Inject;


/**
 * The resource to create and find change logs for relationship maps.
 */
@RestLiCollection(name = "salesRelationshipMapChangeLogs", keyName = "key", namespace = "com.linkedin.sales")
public class SalesRelationshipMapChangeLogsResource
    extends CollectionResourceTaskTemplate<NullKey, RelationshipMapChangeLog> {

  @Inject
  SalesRelationshipMapChangeLogService _salesRelationshipMapChangeLogService;

  /**
   * Create change log for a relationship map.
   * Change logs have a TTL and are append only.
   * @param contract contract for the seat creating the change log. Is required for GDPR.
   */
  @RestMethod.Create
  public Task<CreateResponse> create(RelationshipMapChangeLog changeLog,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn contract) {
    return _salesRelationshipMapChangeLogService.createChangeLog(changeLog, contract).map(CreateResponse::new);
  }

  /**
   * Find all relationshipMap changeLogs associated with given relationshipMap and available to be viewed by the viewer seat.
   * A acl check determines the start time from which user had access to the map or map became shared. We only retrieve logs after the acl start time.
   * Acl start time takes preference over provided start time.
   * If no start time is provided, we use the acl start time.
   * If end time is provided, it is used, else we set it to current time.
   * @param relationshipMapListUrn salesList urn for relationship map to find changeLogs for it.
   * @param seat seat of the viewer. Will be used to check acl access on relationship map and get sharing policy.
   * @param timeRange start and end time range to look for changeLogs created in the time range.
   */
  @Finder("relationshipMap")
  public Task<BasicCollectionResult<RelationshipMapChangeLog>> findByRelationshipMap(
      @PagingContextParam final PagingContext pagingContext,
      @QueryParam(value = "relationshipMap", typeref = com.linkedin.common.SalesListUrn.class) SalesListUrn relationshipMapListUrn,
      @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seat,
      @QueryParam(value = "timeRange") @Optional TimeRange timeRange) {
    return _salesRelationshipMapChangeLogService.findByRelationshipMap(relationshipMapListUrn, seat,
        pagingContext.getStart(), pagingContext.getCount(), timeRange);
  }
}
