package com.linkedin.sales.rest.note;

import com.linkedin.common.urn.Urn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.service.note.SalesNoteService;
import com.linkedin.salesnote.Note;
import com.linkedin.salesnote.NoteKey;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;


/**
 * The resource to create/delete/find notes in Sales Navigator.
 */
@RestLiCollection(name = "salesNotes", keyName = "noteKey", namespace = "com.linkedin.sales")
@ReadOnly({"noteId", "created", "lastModified"})
@CreateOnly({"entity"})
public class SalesNoteResource extends ComplexKeyResourceTaskTemplate<NoteKey, EmptyRecord, Note> {

  private final SalesNoteService _salesNoteService;
  @Inject
  public SalesNoteResource(SalesNoteService salesNoteService) {
    _salesNoteService = salesNoteService;
  }
  /**
   * create a note
   * @param note including content and information about the note
   * @return create response to tell if the create succeeds
   */
  @RestMethod.Create
  public Task<CreateResponse> create(Note note) {
    return _salesNoteService.createNote(note);
  }

  /**
   * upsert a note with timestamps
   * @param key including the entity urn and seat urn and note id
   * @param note including content and information about the note
   * @return update response to tell if the upsert succeeds
   */
  @RestMethod.Update
  public Task<UpdateResponse> upsert(final ComplexResourceKey<NoteKey, EmptyRecord> key, Note note) {
    return _salesNoteService.createNote(note).map(createResponse ->
        new UpdateResponse(createResponse.getStatus()));
  }

  /**
   * Delete a note
   * @param key key including the entity urn and note Id to delete the note
   * @param viewerSeat seat information to determine the permission to delete
   * @param viewerContract contract information to determine the permission to delete
   * @return the update response to tell if the delete succeeds
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<NoteKey, EmptyRecord> key,
      final @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn viewerSeat,
      final @QueryParam(value = "viewerContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn viewerContract) {
    return _salesNoteService.deleteNote(key.getKey(), viewerSeat, viewerContract);
  }

  /**
   * partial update the note
   * @param key key including the entity urn and note Id to delete the note
   * @param patch patch of the update content
   * @param viewerSeat seat information to determine the permission to update
   * @param viewerContract contract information to determine the permission to update
   * @return the update response to tell if the update succeeds
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(final ComplexResourceKey<NoteKey, EmptyRecord> key, PatchRequest<Note> patch,
      final @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn viewerSeat,
      final @QueryParam(value = "viewerContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn viewerContract) {
    Note note = new Note();
    try {
      PatchApplier.applyPatch(note, patch);
    } catch (DataProcessingException e) {
      return Task.failure("Error applying patch to note during update",
          new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, e));
    }
    note.setNoteId(key.getKey().getNoteId());
    note.setSeat(key.getKey().getSeat());
    note.setEntity(key.getKey().getEntity());

    return _salesNoteService.updateNote(note, viewerSeat, viewerContract)
        .map(isSuccessful -> {
          if (isSuccessful) {
            return new UpdateResponse(HttpStatus.S_204_NO_CONTENT);
          } else { // cannot find the note to update
            throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND);
          }
        });
  }
  /**
   * find all the notes for this entity
   * @param entity the entity urn that the notes are under
   * @param viewerSeat seat information to determine the permission to read
   * @param viewerContract contract information to determine the permission to read
   * @return the collection of notes under this entity
   */
  @Finder("entity")
  public Task<BasicCollectionResult<Note>> findByEntity(
      @QueryParam(value = "entity", typeref = com.linkedin.common.Urn.class) Urn entity,
      @QueryParam(value = "viewerSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn viewerSeat,
      @QueryParam(value = "viewerContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn viewerContract,
      @PagingContextParam final PagingContext pagingContext) {
    int start = pagingContext.getStart();
    int count = pagingContext.getCount();
    return _salesNoteService.findBySeatAndEntity(viewerSeat, entity, viewerContract, start, count);
  }

  /**
   * find all the notes created by the given seat
   * @param creator seat urn of the notes creator
   * @return the collection of notes created by this seat
   */
  @Finder("seat")
  public Task<BasicCollectionResult<Note>> findBySeat(
      @QueryParam(value = "creator", typeref = com.linkedin.common.SeatUrn.class) SeatUrn creator,
      @PagingContextParam final PagingContext pagingContext) {
    int start = pagingContext.getStart();
    int count = pagingContext.getCount();
    return _salesNoteService.findBySeat(creator, start, count);
  }

  /**
   * Get the Note based on the noteKey
   * @param noteKey unique ID that represents the note
   * @return the note that has this id
   */
  @RestMethod.Get
  public Task<Note> get(ComplexResourceKey<NoteKey, EmptyRecord> noteKey) {
    return _salesNoteService.getNote(noteKey.getKey());
  }

  /**
   * Batch get notes based on noteKeys
   * @param noteKeys unique ID's that represent the note
   * @return map of notekeys and notes
   */
  @RestMethod.BatchGet
  public Task<Map<ComplexResourceKey<NoteKey, EmptyRecord>, Note>> batchGet(
      Set<ComplexResourceKey<NoteKey, EmptyRecord>> noteKeys,
      final @PagingContextParam PagingContext pagingContext) {
    List<NoteKey> noteKeysList = noteKeys.stream().map(ComplexResourceKey::getKey).collect(Collectors.toList());
    return _salesNoteService.batchGet(noteKeysList).map(pairs -> pairs.stream().collect(Collectors.toMap(
        pair -> new ComplexResourceKey<>(pair.getFirst(), new EmptyRecord()),
        pair -> pair.getSecond())));
  }
}
