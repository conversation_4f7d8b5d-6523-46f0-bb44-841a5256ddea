package com.linkedin.sales.rest.recentviews;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesRecentActivitiesService;
import com.linkedin.salesrecentactivities.RecentActivity;
import com.linkedin.salesrecentactivities.RecentActivityKey;
import com.linkedin.salesrecentactivities.RecentActivityType;
import java.util.List;
import javax.inject.Inject;


/**
 * The resource for RecentActivities record in Sales Navigator.
 *
 * <AUTHOR>
 */
@RestLiCollection(name = "salesRecentActivities",
    keyName = "id",
    namespace = "com.linkedin.sales")
public class SalesRecentActivitiesResource extends ComplexKeyResourceTaskTemplate<RecentActivityKey, EmptyRecord, RecentActivity> {

  private final SalesRecentActivitiesService _salesRecentActivitiesService;
  @Inject
  public SalesRecentActivitiesResource(SalesRecentActivitiesService salesRecentActivitiesService) {
    _salesRecentActivitiesService = salesRecentActivitiesService;
  }

  /**
   * Upsert RecentActivity record for a seat holder based on entity type.
   * This creates a record of an array of RecentActivity for a seat and entity type
   * for the first time and appends to that array from next time onwards.
   *
   * @param key Recent Activity key for the update which includes the seat, contract and activity type of the viewer
   * @param recentActivity The single recent activity data
   * @return create response to tell if the recent view record is added successfully
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(ComplexResourceKey<RecentActivityKey, EmptyRecord> key, RecentActivity recentActivity) {

    SeatUrn seatUrn = key.getKey().getSeat();
    ContractUrn contractUrn = key.getKey().getContract();
    RecentActivityType recentActivityType = key.getKey().getRecentActivityType();
    return _salesRecentActivitiesService.upsertRecentViews(seatUrn,
        recentActivityType, contractUrn, recentActivity)
        .map(listId -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT))
        .recover(throwable -> {
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to add the recent view",
              throwable);
        });
  }

  /**
   * Finder that helps get list of all recent activity by seat Urn and entity type
   *
   * @param seatUrn Seat Urn of the user
   * @param recentActivityType type (enum) of the recent activity
   * @return The list of recent activity data
   */
  @Finder("seat")
  public Task<List<com.linkedin.salesrecentactivities.RecentActivity>> findRecentActivitiesBySeat(
      @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      @QueryParam(value = "entityType") RecentActivityType recentActivityType
  ) {
    return _salesRecentActivitiesService.getEspressoRecentViews(seatUrn, recentActivityType);
  }

  /**
   * Delete a RecentActivity given a RecentActivityKey
   *
   * @param key Recent activity key for the delete which includes the seat, contract and activity type of the viewer
   * @return {@link UpdateResponse} to tell if the recent activity record is deleted successfully
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(ComplexResourceKey<RecentActivityKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    RecentActivityType recentActivityType = key.getKey().getRecentActivityType();
    return _salesRecentActivitiesService.deleteEspressoRecentViews(seatUrn, recentActivityType)
        .map(deleteRsp -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
  }
}
