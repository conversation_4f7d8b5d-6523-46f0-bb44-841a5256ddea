package com.linkedin.sales.rest.recentviews;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesRecentSearchesService;
import com.linkedin.salesrecentactivities.RecentActivityType;
import com.linkedin.salesrecentactivities.RecentSearchKey;
import com.linkedin.salesrecentactivities.RecentSearch;
import java.util.List;
import javax.inject.Inject;


/**
 * The resource for RecentSearch record in Sales Navigator.
 * This resource helps to create/update/get by recentSearchKey and find RecentSearch records by seat
 * <AUTHOR>
 */
@RestLiCollection(name = "salesRecentSearches", keyName = "recentSearchKey", namespace = "com.linkedin.sales")
@ReadOnly({"id"})
@CreateOnly({"seat", "recentActivityType"})
public class SalesRecentSearchesResource extends ComplexKeyResourceTaskTemplate<RecentSearchKey, EmptyRecord, RecentSearch> {
  private final SalesRecentSearchesService _salesRecentSearchesService;

  private static final int DEFAULT_RECORD_COUNT = 5;
  private static final int DEFAULT_RECORD_START = 0;

  @Inject
  public SalesRecentSearchesResource(SalesRecentSearchesService salesRecentSearchesService) {
    _salesRecentSearchesService = salesRecentSearchesService;
  }

  /**
   * Create RecentSearch record for a seat holder.
   *
   * @param recentSearch the single recent search object that needs to be created
   * @return create response to tell if the recent search record is added successfully along with id for the record
   */
  @RestMethod.Create
  public Task<CreateResponse> create(RecentSearch recentSearch
      ) {
    return _salesRecentSearchesService.createRecentSearches(recentSearch)
        .map(recentSearchId -> {
          RecentSearchKey recentSearchKey = new RecentSearchKey()
              .setRecentActivityType(recentSearch.getRecentActivityType())
              .setId(recentSearchId)
              .setSeat(recentSearch.getSeat());
          return new CreateResponse(new ComplexResourceKey<>(recentSearchKey, new EmptyRecord()), HttpStatus.S_201_CREATED);
        });
  }

  /**
   * Update RecentSearch record for a seat holder based on RecentSearchKey
   *
   * @param recentSearchKey key including the seat urn, recent activity type and recent search id to update the record
   * @param recentSearch the single recent search object that needs to be updated for the given id
   * @return update response to tell if the recent search record is updated successfully
   */
  @RestMethod.Update
  public Task<UpdateResponse> update(final ComplexResourceKey<RecentSearchKey, EmptyRecord> recentSearchKey,
      RecentSearch recentSearch
      ) {
    return _salesRecentSearchesService.updateRecentSearches(recentSearchKey.getKey(), recentSearch)
        .map(recentSearchId -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
  }

  /**
   * Finder that helps get list of all recent searches by seat Urn and search type
   *
   * @param seatUrn sales identification urn of the user. For eg. urn:li:seat:123
   * @param recentActivityType Optional type (enum) of the recent activity. Eg. PEOPLE_SEARCH
   * @param pagingContext pagination parameter
   * @return the list of recent search object
   */
  @Finder("seat")
  public Task<List<RecentSearch>> findRecentSearchesBySeat(
      final @QueryParam(value = "seat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn seatUrn,
      final @QueryParam(value = "recentActivityType") @Optional RecentActivityType recentActivityType,
      @PagingContextParam final PagingContext pagingContext
  ) {
    int count = pagingContext.hasCount() ? pagingContext.getCount() : DEFAULT_RECORD_COUNT;
    int start = pagingContext.hasStart() ? pagingContext.getStart() : DEFAULT_RECORD_START;
    return _salesRecentSearchesService.getEspressoRecentSearchesBySeat(seatUrn, recentActivityType, start, count);
  }

  /**
   * Get the recent search record based on the RecentSearchKey
   *
   * @param recentSearchKey key including the seat urn, recent activity type and recent search id to lookup the record
   * @return the recent search object
   */
  @RestMethod.Get
  public Task<RecentSearch> get(final ComplexResourceKey<RecentSearchKey, EmptyRecord> recentSearchKey) {
    return _salesRecentSearchesService.getEspressoRecentSearch(recentSearchKey.getKey(), recentSearchKey.getKey().getRecentActivityType());
  }

  /**
   * Delete a RecentSearch given a RecentSearchKey
   *
   * @param key Recent search key for the delete which includes the seat, contract and activity type of the viewer
   * @return {@link UpdateResponse} to tell if the recent search record is deleted successfully
   */
  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<RecentSearchKey, EmptyRecord> key) {
    SeatUrn seatUrn = key.getKey().getSeat();
    RecentActivityType recentActivityType = key.getKey().getRecentActivityType();
    return _salesRecentSearchesService.deleteEspressoRecentSearches(seatUrn, recentActivityType)
        .map(deleteRsp -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
  }
}
