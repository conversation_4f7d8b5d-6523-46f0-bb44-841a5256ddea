package com.linkedin.sales.rest.seattransfer;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.RestLiActions;
import com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import javax.inject.Inject;


@RestLiActions(name = "salesEntityNotesSeatTransferActions", namespace = "com.linkedin.sales")
public class SalesEntityNotesSeatTransferActionsResource {
  @Inject
  private LssSeatTransferActionsService _lssSeatTransferActionsService;
  /**
   * Action to execute a seat transfer request on sales entity notes.
   * This action should only be invoked for synchronous processing of entities.
   *
   * Responses:
   * 200 -> When executes successfully
   * 400 -> Upon encountering invalid/unsupported Seat Transfer Operation, unsupported EntityType in Seat Transfer Request
   * Throws 500 -> upon encountering any unknown error/exception
   *
   * @param salesSeatTransferRequestUrn seat transfer request urn.
   * @param entityType entityType of SALES_ENTITY_NOTES to be processed for seat transfer.
   * @param actor The actor performing the seat transfer action.
   *
   * @return {@link ActionResult} with a string message, static "Success" message for 200 response
   * else error reason message for 400 bad request or any expected error.
   */
  @Action(name = "executeSeatTransferRequest")
  public Task<ActionResult<String>> executeSeatTransferRequest(
      @ActionParam(value = "salesSeatTransferRequestUrn", typeref = com.linkedin.sales.SalesSeatTransferRequestUrn.class)
      final SalesSeatTransferRequestUrn salesSeatTransferRequestUrn,
      @ActionParam(value = "entityType") final OwnershipTransferEntityType entityType,
      @ActionParam(value = "actor", typeref = com.linkedin.common.SeatUrn.class) final SeatUrn actor) throws Exception {
    return _lssSeatTransferActionsService.executeSeatTransferRequest(salesSeatTransferRequestUrn,
            entityType, actor)
        .map(voidResponse -> new ActionResult<>("Success", HttpStatus.S_200_OK))
        .recoverWith(error -> {
          if (error instanceof UnsupportedOperationException) {
            return Task.value(new ActionResult<>(error.toString(), HttpStatus.S_400_BAD_REQUEST));
          }
          return Task.failure(error);
        });
  }
}
