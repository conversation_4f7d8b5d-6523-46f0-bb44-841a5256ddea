package com.linkedin.sales.rest.settings;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.settings.SalesMobileSettingsService;
import com.linkedin.salesseatpreference.SalesMobileSettings;
import javax.inject.Inject;

/**
 * Resource to create/update/get mobile settings for a seat
 */
@RestLiCollection(name = "salesMobileSettings", namespace = "com.linkedin.sales", keyName = "id",
    keyTyperefClass = com.linkedin.common.SeatUrn.class)
public class SalesMobileSettingsResource extends CollectionResourceTaskTemplate<SeatUrn, SalesMobileSettings> {

  @Inject
  SalesMobileSettingsService _salesMobileSettingsService;

  /**
   * Create or update mobile settings for a seat
   * @param seat sales identification urn of the user. For eg. urn:li:seat:123
   * @param patch patch of salesMobileSettings that has to be created/updated for the seat
   * @return response update response to tell if the settings was updated successfully
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(final SeatUrn seat,
      final PatchRequest<SalesMobileSettings> patch) {
    return _salesMobileSettingsService.updateMobileSetting(seat, patch);
  }

  /**
   * Get settings for a seat
   * @param seat sales identification urn of the user. For eg. urn:li:seat:123
   * @return SalesMobileSettings
   */
  @RestMethod.Get
  public Task<SalesMobileSettings> get(final SeatUrn seat) {
    return _salesMobileSettingsService.getMobileSetting(seat);
  }
}