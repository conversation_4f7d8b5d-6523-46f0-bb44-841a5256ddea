package com.linkedin.sales.rest.settings;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.CollectionResourceTaskTemplate;
import com.linkedin.sales.service.settings.SalesSeatSettingsService;
import com.linkedin.salesseatpreference.SalesSeatSetting;
import java.util.Map;
import java.util.Set;
import javax.inject.Inject;


/**
 * Resource to create/update/get settings for a seat
 */
@RestLiCollection(name = "salesSeatSettings", namespace = "com.linkedin.sales", keyName = "seat",
    keyTyperefClass = com.linkedin.common.SeatUrn.class)
public class SalesSeatSettingsResource extends CollectionResourceTaskTemplate<SeatUrn, SalesSeatSetting> {

  @Inject
  SalesSeatSettingsService _salesSeatSettingsService;
  /**
   * Create or update settings for a seat
   * @param seat sales identification urn of the user. For eg. urn:li:seat:123
   * @param patch patch of salesSeatSetting that has to be created/updated for the seat
   * @return response update response to tell if the settings was updated successfully
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> update(final SeatUrn seat,
      final PatchRequest<SalesSeatSetting> patch) {
    return _salesSeatSettingsService.updateSeatSetting(seat, patch);
  }

  /**
   * Get settings for a seat
   * @param seat sales identification urn of the user. For eg. urn:li:seat:123
   * @return SalesSeatSetting
   */
  @RestMethod.Get
  public Task<SalesSeatSetting> get(final SeatUrn seat) {
    return _salesSeatSettingsService.getSeatSetting(seat);
  }

  /**
   * Batch fetches SeatSettings with a set of Seat Urn's
   * @param seatUrns Set of Seat Urn's. Seat Urn is the key for the espresso table.
   * @return Mapping of Seat Urn's to SalesSeatSettings
   */
  @RestMethod.BatchGet
  public Task<Map<SeatUrn, SalesSeatSetting>> batchGet(Set<SeatUrn> seatUrns) {
    return _salesSeatSettingsService.batchGet(seatUrns);
  }
}
