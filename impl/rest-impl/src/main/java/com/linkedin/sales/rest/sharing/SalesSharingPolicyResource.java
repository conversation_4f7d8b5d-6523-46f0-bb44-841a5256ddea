package com.linkedin.sales.rest.sharing;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.common.validation.CreateOnly;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchDeleteRequest;
import com.linkedin.restli.server.BatchUpdateRequest;
import com.linkedin.restli.server.BatchUpdateResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.Action;
import com.linkedin.restli.server.annotations.ActionParam;
import com.linkedin.restli.server.annotations.Finder;
import com.linkedin.restli.server.annotations.Optional;
import com.linkedin.restli.server.annotations.PagingContextParam;
import com.linkedin.restli.server.annotations.QueryParam;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.sharing.SalesSharingService;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.salessharing.SharingRole;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.inject.Inject;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Resource that is responsible on all administration on sharing access.
 * <AUTHOR>
 */
@RestLiCollection(name = "salesSharingPolicies", keyName = "key", namespace = "com.linkedin.sales")
@CreateOnly({"creator", "createdAt"})
public class SalesSharingPolicyResource extends ComplexKeyResourceTaskTemplate<PolicyKey, EmptyRecord, Policy> {

  @Inject
  SalesSharingService _salesSharingService;

  /**
   * Batch get policies
   *
   * @param ids set of policy keys in ComplexResourceKey type
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI). It used for permission check.
   * @return task of map from policy key to found policy object
   */
  @RestMethod.BatchGet
  public Task<Map<ComplexResourceKey<PolicyKey, EmptyRecord>, Policy>> batchGet(
      Set<ComplexResourceKey<PolicyKey, EmptyRecord>> ids,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester) {
    Set<PolicyKey> policyKeys = ids.stream().map(ComplexResourceKey::getKey).collect(Collectors.toSet());
    return _salesSharingService.batchGet(policyKeys)
        .map(policyMap -> policyMap.entrySet()
            .stream()
            .collect(Collectors.toMap(entry -> new ComplexResourceKey<>(entry.getKey(), new EmptyRecord()),
                Map.Entry::getValue)));
  }

  /**
   * Batch update existing policies
   *
   * Adding new policies should use this resource too by upserts because there's no generated key in a policy.
   *
   * @param batchUpdateRequest batchUpdateRequest
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI). Throw error if request doesn't have access to update any of the record.
   * @param contract the contract Urn. Caller is responsible to make sure requester SeatUrn belongs to the given contract.
   * @return task of BatchUpdateResponse
   */
  @RestMethod.BatchUpdate
  public Task<BatchUpdateResult<ComplexResourceKey<PolicyKey, EmptyRecord>, Policy>> batchUpdate(
      BatchUpdateRequest<ComplexResourceKey<PolicyKey, EmptyRecord>, Policy> batchUpdateRequest,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester,
      final @QueryParam(value = "contract", typeref = com.linkedin.common.ContractUrn.class) @Optional ContractUrn contract) {
    return _salesSharingService.batchPartialUpdatePolicies(new ArrayList<>(batchUpdateRequest.getData().values()), requester, contract)
        .map(resultMap -> new BatchUpdateResult<>(batchUpdateRequest.getData().values().stream().map(policy -> {
          PolicyKey policyKey = new PolicyKey().setSubject(policy.getSubject())
              .setPolicyType(policy.getPolicyType())
              .setResource(policy.getResource());
          CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, policy.getSubject())
              .append(POLICY_TYPE_COMPOUND_KEY, policy.getPolicyType())
              .append(RESOURCE_COMPOUND_KEY, policy.getResource());
          ComplexResourceKey<PolicyKey, EmptyRecord> complexResourceKey =
              new ComplexResourceKey<>(policyKey, new EmptyRecord());
          UpdateResponse response = resultMap.get(compoundKey);
          return response == null ? null : new AbstractMap.SimpleEntry<>(complexResourceKey, response);
        }).filter(Objects::nonNull).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))));
  }

  /**
   * Partial update a sharing policy.
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI). Throw error if request doesn't have access to update any of the record.
   */
  @RestMethod.PartialUpdate
  public Task<UpdateResponse> partialUpdate(
      ComplexResourceKey<PolicyKey, EmptyRecord> key,
      final PatchRequest<Policy> patch,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester) {
    return _salesSharingService.partialUpdatePolicy(key.getKey(), patch, requester);
  }

  /**
   * Batch delete existing policies.
   *
   * Can be used for data wipe out and user action.
   * @param batchDeleteRequest
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI). Throw error if request doesn't have access to update any of the record.
   * @return task of BatchUpdateResponse
   */
  @RestMethod.BatchDelete
  public Task<BatchUpdateResult<ComplexResourceKey<PolicyKey, EmptyRecord>, Policy>> batchDelete(
      final BatchDeleteRequest<ComplexResourceKey<PolicyKey, EmptyRecord>, Policy> batchDeleteRequest,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester) {
    List<PolicyKey> keys =
        batchDeleteRequest.getKeys().stream().map(ComplexResourceKey::getKey).collect(Collectors.toList());
    return _salesSharingService.batchDeletePolicies(keys, requester)
        .map(resultMap -> new BatchUpdateResult<>(keys.stream().map(policyKey -> {
          CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, policyKey.getSubject())
              .append(POLICY_TYPE_COMPOUND_KEY, policyKey.getPolicyType())
              .append(RESOURCE_COMPOUND_KEY, policyKey.getResource());
          ComplexResourceKey<PolicyKey, EmptyRecord> complexResourceKey =
              new ComplexResourceKey<>(policyKey, new EmptyRecord());
          UpdateResponse response = resultMap.get(compoundKey);
          return response == null ? null : new AbstractMap.SimpleEntry<>(complexResourceKey, response);
        }).filter(Objects::nonNull).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))));
  }

  /**
   * Return matching policies on given resource.
   *
   * Throw error if requester doesn't have at least view access.
   * @param pagingContext pagination parameter
   * @param resource the provided resource urn. Possible types are ListUrn, NoteUrn, SharedSearchUrn,
   * {@link com.linkedin.common.urn.SalesInsightsMetricsReportUrn}, etc
   * @param policyType the type of policy
   * @param roles array of role values that found policies can have
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI). A requester must have at least read access to the resource.
   * @return
   */
  @Finder("resource")
  public Task<BasicCollectionResult<Policy>> findPoliciesByResource(
      final @PagingContextParam PagingContext pagingContext,
      final @QueryParam(value = "resource", typeref = com.linkedin.common.Urn.class) Urn resource,
      final @QueryParam(value = "policyType") PolicyType policyType,
      final @QueryParam(value = "roles") SharingRole[] roles,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : ServiceConstants.DEFAULT_START;
    //If user do not provide count, default to get all
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : SHARING_POLICY_GET_ALL_COUNT;
    // if roles is empty, we pass null to service layer to fetch all roles. Note that the service layer do not allow
    // roles to be empty(to fetch all, use null roles).
    Set<SharingRole> rolesSet = roles.length == 0 ? null : Arrays.stream(roles).collect(Collectors.toSet());
    return _salesSharingService.findPoliciesByResource(resource, policyType, rolesSet, requester, start, count)
        .map(paginatedPolicies -> new BasicCollectionResult<>(paginatedPolicies.getResult(), paginatedPolicies.getTotal()));
  }

  /**
   * Return matching policies on any given resource context, policyType, roles, seatUrn and contractUrn
   *
   * @param pagingContext pagination parameter
   * @param resourceContext an entity urn, the resource the policy is related to. Acceptable types are MemberUrn or
   *                        OrganizationUrn. E.g. if a note is written on the profile of a member and shared then the
   *                        resource context is the memberUrn of that member
   * @param policyType the type of policy based on the entity being shared
   * @param roles array of role values that found policies can have. If empty, no default value will be applied and the
   *              result will be empty.
   * @param requesterSeat seatUrn is the seat associated with the requester who is the user requesting for policies.
   *                     RequesterSeat is required to find all the policies that are shared explicitly
   *                      shared to them.
   * @param requesterContract the contract Urn. Caller is responsible to make sure requester SeatUrn belongs to the
   *                         given contract. ContractUrn is required to find all the policies that are shared across the
   *                         contract.
   * @return
   */
  @Finder("resourceContext")
  public Task<BasicCollectionResult<Policy>> findPoliciesByResourceContext(
      final @PagingContextParam PagingContext pagingContext,
      final @QueryParam(value = "resourceContext", typeref = com.linkedin.common.Urn.class) Urn resourceContext,
      final @QueryParam(value = "policyType") PolicyType policyType,
      final @QueryParam(value = "roles") SharingRole[] roles,
      final @QueryParam(value = "requesterSeat", typeref = com.linkedin.common.SeatUrn.class) SeatUrn requesterSeat,
      final @QueryParam(value = "requesterContract", typeref = com.linkedin.common.ContractUrn.class) ContractUrn
          requesterContract) {
    Set<SharingRole> rolesSet = roles.length == 0 ? null : Arrays.stream(roles).collect(Collectors.toSet());
    return _salesSharingService.findPoliciesByResourceContext(resourceContext, policyType, rolesSet, requesterSeat,
        requesterContract).map(paginatedPolicies -> new BasicCollectionResult<>(
            paginatedPolicies.getResult(), paginatedPolicies.getTotal()));
  }

  /**
   * Return matching policies that are associated to given requester.
   * @param pagingContext pagination parameter
   * @param policyType the type of policy
   * @param roles array of role values that found policies can have
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI)
   * @return
   */
  @Finder("subject")
  public Task<BasicCollectionResult<Policy>> findPoliciesBySubject(
      final @PagingContextParam PagingContext pagingContext,
      final @QueryParam(value = "policyType")PolicyType policyType,
      final @QueryParam(value = "roles") SharingRole[] roles,
      final @QueryParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester) {
    int start = (pagingContext != null && pagingContext.hasStart()) ? pagingContext.getStart() : ServiceConstants.DEFAULT_START;
    //If user do not provide count, default to get all
    int count = (pagingContext != null && pagingContext.hasCount()) ? pagingContext.getCount() : SHARING_POLICY_GET_ALL_COUNT;
    // if roles is empty, we pass null to service layer to fetch all roles. Note that the service layer do not allow
    // roles to be empty(to fetch all, use null roles).
    Set<SharingRole> rolesSet = roles.length == 0 ? null : Arrays.stream(roles).collect(Collectors.toSet());
    return _salesSharingService.findPoliciesBySubject(requester, policyType, rolesSet, start, count)
        .map(paginatedPolicies -> new BasicCollectionResult<>(paginatedPolicies.getResult(), paginatedPolicies.getTotal()));
  }

  /**
   * Return the access decision on a single policy.
   * @param requester logged in user's {@link com.linkedin.common.urn.SeatUrn} (LSS) or
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI)
   * @param policyType policyType
   * @param resource the provided resource urn that all returned policies should match
   * @param accessAction accessAction READ, WRITE, or ADMIN etc. actions on a given resource
   * @return access decision
   */
  @Action(name = "checkAccessDecision")
  public Task<AccessDecision> checkAccessDecision(
      @ActionParam(value = "requester", typeref = com.linkedin.common.Urn.class) Urn requester,
      @ActionParam(value = "policyType") PolicyType policyType,
      @ActionParam(value = "resource", typeref = com.linkedin.common.Urn.class) Urn resource,
      @ActionParam(value = "accessAction") AccessAction accessAction) {
    return _salesSharingService.checkAccessDecision(requester, policyType, resource, accessAction);
  }
}
