package com.linkedin.sales.rest.subscriptionbenefits;

import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.validation.ReadOnly;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.annotations.RestLiCollection;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.restli.server.resources.ComplexKeyResourceTaskTemplate;
import com.linkedin.sales.service.SalesSubscriptionBenefitsService;
import com.linkedin.salesbenefits.SalesSubscriptionBenefits;
import com.linkedin.salesbenefits.SalesSubscriptionBenefitsKey;
import com.linkedin.talent.decorator.PathSpecSet;
import javax.inject.Inject;


/**
 * This resource is to get the subscription benefits that member will lose on cancelling the sales navigator subscription
 * This resource will be used by voyager-api to generate the sticky cancellation modal
 * <AUTHOR>
 */
@RestLiCollection(name = "salesSubscriptionBenefits", namespace = "com.linkedin.sales", keyName = "salesSubscriptionBenefitsKey")
@ReadOnly({"savedLeadsCount", "savedAccountsCount", "unusedInmailCredits", "messageThreadsCount"})
public class SalesSubscriptionBenefitsResource
    extends ComplexKeyResourceTaskTemplate<SalesSubscriptionBenefitsKey, EmptyRecord, SalesSubscriptionBenefits> {
  private final SalesSubscriptionBenefitsService _salesSubscriptionBenefitsService;

  @Inject
  public SalesSubscriptionBenefitsResource(SalesSubscriptionBenefitsService salesSubscriptionBenefitsService) {
    _salesSubscriptionBenefitsService = salesSubscriptionBenefitsService;
  }

  /**
   * Get the sales subscription benefits for a member and contract.
   *
   * @param key complex key with member and contract
   * @return Sales subscription benefits based on the projection
   */
  @RestMethod.Get
  public Task<SalesSubscriptionBenefits> get(ComplexResourceKey<SalesSubscriptionBenefitsKey, EmptyRecord> key) {
    SalesSubscriptionBenefitsKey salesSubscriptionBenefitsKey = key.getKey();
    if (getContext().getProjectionMask() == null) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Projection is null for salesSubscriptionBenefitsKey: " + salesSubscriptionBenefitsKey);
    }
    return _salesSubscriptionBenefitsService.getSubscriptionBenefits(salesSubscriptionBenefitsKey.getMember(),
        salesSubscriptionBenefitsKey.getContract(), PathSpecSet.of(getContext().getProjectionMask()));
  }
}
