package com.linkedin.sales.utils;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import java.util.Arrays;


public final class MemberUtils {

  private MemberUtils() {
  }

  public static void ifNonMemberThenThrow400(MemberUrn... memberUrns) {
    if (Arrays.stream(memberUrns).anyMatch(memberUrn -> memberUrn == null || memberUrn.getMemberIdEntity() <= 0)) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST);
    }
  }
}
