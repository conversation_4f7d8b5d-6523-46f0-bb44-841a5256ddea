package proto.com.linkedin.sales.autoprospecting;

import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.autoprospecting.CampaignService;
import io.grpc.stub.StreamObserver;
import javax.inject.Inject;
import proto.com.linkedin.salesautoprospecting.CampaignApiGrpc;
import proto.com.linkedin.salesautoprospecting.CreateCampaignRequest;
import proto.com.linkedin.salesautoprospecting.CreateCampaignResponse;
import proto.com.linkedin.salesautoprospecting.FindCampaignBySeatAndContractRequest;
import proto.com.linkedin.salesautoprospecting.FindCampaignBySeatAndContractResponse;
import proto.com.linkedin.salesautoprospecting.GetCampaignRequest;
import proto.com.linkedin.salesautoprospecting.GetCampaignResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateCampaignRequest;
import proto.com.linkedin.salesautoprospecting.PartialUpdateCampaignResponse;


/**
 * gRPC API for Auto Prospecting Campaign CRUD operations.
 */
public class CampaignApi extends CampaignApiGrpc.CampaignApiImplBase {
  private final CampaignService _campaignService;
  private final Engine _engine;

  @Inject
  public CampaignApi(CampaignService campaignService, Engine engine) {
    _campaignService = campaignService;
    _engine = engine;
  }

  @Override
  public void create(CreateCampaignRequest request, StreamObserver<CreateCampaignResponse> responseObserver) {
    Task<CreateCampaignResponse> createCampaignTask = _campaignService.createCampaign(request.getValue())
        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
        .andThen(createCampaignResponse -> {
          responseObserver.onNext(createCampaignResponse);
          responseObserver.onCompleted();
        });
    _engine.run(createCampaignTask);
  }

  @Override
  public void partialUpdate(PartialUpdateCampaignRequest request,
      StreamObserver<PartialUpdateCampaignResponse> responseObserver) {
    Task<PartialUpdateCampaignResponse> partialUpdateCampaignTask =
        _campaignService.partialUpdateCampaign(request.getKey(), request.getValue())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(partialUpdateCampaignResponse -> {
              responseObserver.onNext(partialUpdateCampaignResponse);
              responseObserver.onCompleted();
            });
    _engine.run(partialUpdateCampaignTask);
  }

  @Override
  public void get(GetCampaignRequest request, StreamObserver<GetCampaignResponse> responseObserver) {
    Task<GetCampaignResponse> getCampaignTask = _campaignService.getCampaign(request.getKey())
        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
        .andThen(getCampaignResponse -> {
          responseObserver.onNext(getCampaignResponse);
          responseObserver.onCompleted();
        });
    _engine.run(getCampaignTask);
  }

  @Override
  public void findBySeatAndContract(FindCampaignBySeatAndContractRequest request,
      StreamObserver<FindCampaignBySeatAndContractResponse> responseObserver) {
    Task<FindCampaignBySeatAndContractResponse> findCampaignTask =
        _campaignService.findCampaignsBySeatAndContract(request.getSeatUrn(), request.getContractUrn())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(findCampaignResponse -> {
              responseObserver.onNext(findCampaignResponse);
              responseObserver.onCompleted();
            });
    _engine.run(findCampaignTask);
  }
}