package proto.com.linkedin.sales.autoprospecting;

import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunService;
import io.grpc.stub.StreamObserver;
import javax.inject.Inject;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunRequest;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.FindByQueryLeadFindingRunRequest;
import proto.com.linkedin.salesautoprospecting.FindByQueryLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunRequest;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunRequest;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunApiGrpc;


/**
 * gRPC API for lead finding run CRUD operations.
 */
public class LeadFindingRunApi extends LeadFindingRunApiGrpc.LeadFindingRunApiImplBase {

  private final LeadFindingRunService _leadFindingRunService;
  private final Engine _engine;

  @Inject
  public LeadFindingRunApi(
      LeadFindingRunService leadFindingRunService,
      Engine engine) {
    _leadFindingRunService = leadFindingRunService;
    _engine = engine;
  }

  @Override
  public void get(GetLeadFindingRunRequest request,
      StreamObserver<GetLeadFindingRunResponse> responseObserver) {
    Task<GetLeadFindingRunResponse> getTask =
        _leadFindingRunService.get(request.getKey())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(getLeadFindingRunResponse -> {
              responseObserver.onNext(getLeadFindingRunResponse);
              responseObserver.onCompleted();
            });
    _engine.run(getTask);
  }

  @Override
  public void create(CreateLeadFindingRunRequest request,
      StreamObserver<CreateLeadFindingRunResponse> responseObserver) {
    Task<CreateLeadFindingRunResponse> createTask =
        _leadFindingRunService.create(request.getValue())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(createLeadFindingRunResponse -> {
              responseObserver.onNext(createLeadFindingRunResponse);
              responseObserver.onCompleted();
            });
    _engine.run(createTask);
  }

  @Override
  public void partialUpdate(PartialUpdateLeadFindingRunRequest request,
      StreamObserver<PartialUpdateLeadFindingRunResponse> responseObserver) {
    Task<PartialUpdateLeadFindingRunResponse> partialUpdateTask =
        _leadFindingRunService.partialUpdate(request.getKey(), request.getValue())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(partialUpdateLeadFindingRunResponse -> {
              responseObserver.onNext(partialUpdateLeadFindingRunResponse);
              responseObserver.onCompleted();
            });
    _engine.run(partialUpdateTask);
  }

  @Override
  public void findByQuery(FindByQueryLeadFindingRunRequest request,
      StreamObserver<FindByQueryLeadFindingRunResponse> responseObserver) {
    Task<FindByQueryLeadFindingRunResponse> findByQueryTask =
        _leadFindingRunService.findByQuery(request.getSeatUrn(),
                request.hasCampaignId() ? request.getCampaignId() : null,
                request.hasStatus() ? request.getStatus() : null,
                request.hasType() ? request.getType() : null,
                request.hasPaging() ? request.getPaging() : null)
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(findByQueryLeadFindingRunResponse -> {
              responseObserver.onNext(findByQueryLeadFindingRunResponse);
              responseObserver.onCompleted();
            });
    _engine.run(findByQueryTask);
  }
}
