package proto.com.linkedin.sales.autoprospecting;

import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunLeadService;
import io.grpc.stub.StreamObserver;

import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.salesautoprospecting.BatchCreateLeadFindingRunLeadRequest;
import proto.com.linkedin.salesautoprospecting.BatchCreateLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunLeadRequest;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.FindByParamsLeadFindingRunLeadRequest;
import proto.com.linkedin.salesautoprospecting.FindByParamsLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunLeadRequest;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadsApiGrpc;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunLeadRequest;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunLeadResponse;


/**
 * gRPC API for LeadFindingRunLead CRUD operations.
 */
public class LeadFindingRunLeadsApi extends LeadFindingRunLeadsApiGrpc.LeadFindingRunLeadsApiImplBase {
    private static final Logger LOG = LoggerFactory.getLogger(LeadFindingRunLeadsApi.class);

    private final LeadFindingRunLeadService _leadFindingRunLeadService;
    private final Engine _engine;

    @Inject
    public LeadFindingRunLeadsApi(LeadFindingRunLeadService leadFindingRunLeadService, Engine engine) {
        _leadFindingRunLeadService = leadFindingRunLeadService;
        _engine = engine;
    }

    @Override
    public void create(CreateLeadFindingRunLeadRequest request,
        StreamObserver<CreateLeadFindingRunLeadResponse> responseObserver) {
        Task<CreateLeadFindingRunLeadResponse> createLeadFindingRunLeadTask =
            _leadFindingRunLeadService.create(request.getValue())
                .onFailure(throwable -> {
                    LOG.error("Failed to create LeadFindingRunLead", throwable);
                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
                })
                .andThen(response -> {
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });

        _engine.run(createLeadFindingRunLeadTask);
    }

    @Override
    public void get(GetLeadFindingRunLeadRequest request,
        StreamObserver<GetLeadFindingRunLeadResponse> responseObserver) {
        Task<GetLeadFindingRunLeadResponse> getTask =
            _leadFindingRunLeadService.getLeadFindingRunLead(request.getKey())
                .onFailure(throwable -> {
                    LOG.error("Failed to get LeadFindingRunLead for key: {}", request.getKey(), throwable);
                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
                })
                .andThen(response -> {
                    LOG.info("Successfully retrieved LeadFindingRunLead for key: {}", request.getKey());
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });

        _engine.run(getTask);
    }

    @Override
    public void findByParams(FindByParamsLeadFindingRunLeadRequest request,
        StreamObserver<FindByParamsLeadFindingRunLeadResponse> responseObserver) {
        Task<FindByParamsLeadFindingRunLeadResponse> findTask =
            _leadFindingRunLeadService.findByParams(request.getRunId(), request.hasPaging() ? request.getPaging() : null)
                .onFailure(throwable -> {
                    LOG.error("Failed to find LeadFindingRunLead for runId: {}", request.getRunId(), throwable);
                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
                })
                .andThen(response -> {
                    LOG.info("Successfully found LeadFindingRunLead for runId: {}", request.getRunId());
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });

        _engine.run(findTask);
    }

    @Override
    public void partialUpdate(PartialUpdateLeadFindingRunLeadRequest request,
        StreamObserver<PartialUpdateLeadFindingRunLeadResponse> responseObserver) {
        Task<PartialUpdateLeadFindingRunLeadResponse> updateTask =
            _leadFindingRunLeadService.partialUpdate(request.getKey(), request.getValue())
                .onFailure(throwable -> {
                    LOG.error("Failed to partially update LeadFindingRunLead with key: {}, value: {}",
                        request.getKey(), request.getValue(), throwable);
                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
                })
                .andThen(response -> {
                    LOG.info("Successfully updated LeadFindingRunLead with key: {}", request.getKey());
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });

        _engine.run(updateTask);
    }

    @Override
    public void batchCreate(BatchCreateLeadFindingRunLeadRequest request,
        StreamObserver<BatchCreateLeadFindingRunLeadResponse> responseObserver) {

        if (request.getValuesList().isEmpty()) {
            LOG.warn("Batch create request contains an empty values list");
            responseObserver.onError(GrpcUtils.createStatusRuntimeException(new IllegalArgumentException("Values list cannot be empty")));
            return;
        }

        Task<BatchCreateLeadFindingRunLeadResponse> batchCreateTask =
            _leadFindingRunLeadService.batchCreateLeadFindingRunLead(request.getValuesList())
                .onFailure(throwable -> {
                    LOG.error("Failed to batch create LeadFindingRunLeads", throwable);
                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
                })
                .andThen(response -> {
                    LOG.info("Successfully batch created LeadFindingRunLeads");
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                });

        _engine.run(batchCreateTask);
    }
}