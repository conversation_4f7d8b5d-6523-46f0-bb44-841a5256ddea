package proto.com.linkedin.sales.autoprospecting;

import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.autoprospecting.LeadFindingRunQueueService;
import io.grpc.stub.StreamObserver;
import javax.inject.Inject;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.DeleteLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.DeleteLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.FindByCampaignLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.FindByCampaignLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.FindByStatusLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.FindByStatusLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunQueueApiGrpc;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunQueueResponse;


/**
 * gRPC API for lead finding run queue CRUD operations.
 */
public class LeadFindingRunQueueApi extends LeadFindingRunQueueApiGrpc.LeadFindingRunQueueApiImplBase {

  private final LeadFindingRunQueueService _leadFindingRunQueueService;
  private final Engine _engine;

  @Inject
  public LeadFindingRunQueueApi(LeadFindingRunQueueService leadFindingRunQueueService, Engine engine) {
    _leadFindingRunQueueService = leadFindingRunQueueService;
    _engine = engine;
  }

  @Override
  public void get(GetLeadFindingRunQueueRequest request,
      StreamObserver<GetLeadFindingRunQueueResponse> responseObserver) {
    Task<GetLeadFindingRunQueueResponse> getTask = _leadFindingRunQueueService.get(request.getKey())
        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
        .andThen(response -> {
          responseObserver.onNext(response);
          responseObserver.onCompleted();
        });
    _engine.run(getTask);
  }

  @Override
  public void create(CreateLeadFindingRunQueueRequest request,
      StreamObserver<CreateLeadFindingRunQueueResponse> responseObserver) {
    Task<CreateLeadFindingRunQueueResponse> createTask = _leadFindingRunQueueService.create(request.getValue())
        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
        .andThen(response -> {
          responseObserver.onNext(response);
          responseObserver.onCompleted();
        });
    _engine.run(createTask);
  }

  @Override
  public void partialUpdate(PartialUpdateLeadFindingRunQueueRequest request,
      StreamObserver<PartialUpdateLeadFindingRunQueueResponse> responseObserver) {
    Task<PartialUpdateLeadFindingRunQueueResponse> partialUpdateTask =
        _leadFindingRunQueueService.partialUpdate(request.getKey(), request.getValue())
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(response -> {
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });
    _engine.run(partialUpdateTask);
  }

  @Override
  public void delete(DeleteLeadFindingRunQueueRequest request,
      StreamObserver<DeleteLeadFindingRunQueueResponse> responseObserver) {
    Task<DeleteLeadFindingRunQueueResponse> createTask = _leadFindingRunQueueService.delete(request)
        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
        .andThen(response -> {
          responseObserver.onNext(response);
          responseObserver.onCompleted();
        });
    _engine.run(createTask);
  }

  @Override
  public void findByStatus(FindByStatusLeadFindingRunQueueRequest request,
      StreamObserver<FindByStatusLeadFindingRunQueueResponse> responseObserver) {
    Task<FindByStatusLeadFindingRunQueueResponse> findByQueryTask =
        _leadFindingRunQueueService.findByStatus(request.getStatus(), request.hasPaging() ? request.getPaging() : null)
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(response -> {
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });
    _engine.run(findByQueryTask);
  }

  @Override
  public void findByCampaign(FindByCampaignLeadFindingRunQueueRequest request,
      StreamObserver<FindByCampaignLeadFindingRunQueueResponse> responseObserver) {
    Task<FindByCampaignLeadFindingRunQueueResponse> findByQueryTask =
        _leadFindingRunQueueService.findByCampaign(request.getCampaignId(),
                request.hasStatus() ? request.getStatus() : null, request.hasPaging() ? request.getPaging() : null)
            .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
            .andThen(response -> {
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });
    _engine.run(findByQueryTask);
  }
}
