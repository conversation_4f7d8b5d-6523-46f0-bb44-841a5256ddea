package proto.com.linkedin.sales.autoprospecting;

import com.linkedin.grpc.common.ExceptionUtils;
import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.service.autoprospecting.SearchCriteriaService;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import proto.com.linkedin.salesautoprospecting.CreateSearchCriteriaRequest;
import proto.com.linkedin.salesautoprospecting.CreateSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.GetSearchCriteriaRequest;
import proto.com.linkedin.salesautoprospecting.GetSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateSearchCriteriaRequest;
import proto.com.linkedin.salesautoprospecting.PartialUpdateSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.SearchCriteriaApiGrpc;

import javax.inject.Inject;

/**
 * gRPC API for Auto Prospecting SearchCriteria CRUD operations.
 */
public class SearchCriteriaApi extends SearchCriteriaApiGrpc.SearchCriteriaApiImplBase {
    private final SearchCriteriaService _searchCriteriaService;
    private final Engine _engine;

    @Inject
    public SearchCriteriaApi(SearchCriteriaService searchCriteriaService, Engine engine) {
        _searchCriteriaService = searchCriteriaService;
        _engine = engine;
    }

    @Override
    public void create(CreateSearchCriteriaRequest request,
                                       StreamObserver<CreateSearchCriteriaResponse> responseObserver) {
        Task<CreateSearchCriteriaResponse> createSearchCriteriaTask =
                _searchCriteriaService.createSearchCriteria(request.getSearchCriteriaKey(), request.getValue())
                        .onFailure(throwable -> {
                            if (throwable instanceof RestLiServiceException
                                && ((RestLiServiceException) throwable).getStatus() == HttpStatus.S_412_PRECONDITION_FAILED) {
                                    responseObserver.onError(GrpcUtils.createStatusRuntimeException(
                                        Status.Code.FAILED_PRECONDITION,
                                        "Search criteria already exists for given key: " + request.getSearchCriteriaKey()));
                            }
                            responseObserver.onError(ExceptionUtils.convertToStatusRuntimeException(throwable));
                        })
                        .andThen(createSearchCriteriaResponse -> {
                            responseObserver.onNext(createSearchCriteriaResponse);
                            responseObserver.onCompleted();
                        });
        _engine.run(createSearchCriteriaTask);
    }

    @Override
    public void partialUpdate(PartialUpdateSearchCriteriaRequest request,
                              StreamObserver<PartialUpdateSearchCriteriaResponse> responseObserver) {
        Task<PartialUpdateSearchCriteriaResponse> partialUpdateSearchCriteriaTask =
                _searchCriteriaService.partialUpdateSearchCriteria(request.getKey(), request.getValue())
                        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
                        .andThen(partialUpdateSearchCriteriaResponse -> {
                            responseObserver.onNext(partialUpdateSearchCriteriaResponse);
                            responseObserver.onCompleted();
                        });
        _engine.run(partialUpdateSearchCriteriaTask);
    }

    @Override
    public void get(GetSearchCriteriaRequest request, StreamObserver<GetSearchCriteriaResponse> responseObserver) {
        Task<GetSearchCriteriaResponse> getSearchCriteriaTask =
                _searchCriteriaService.getSearchCriteria(request.getKey())
                        .onFailure(throwable -> responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable)))
                        .andThen(getSearchCriteriaResponse -> {
                            responseObserver.onNext(getSearchCriteriaResponse);
                            responseObserver.onCompleted();
                        });
        _engine.run(getSearchCriteriaTask);
    }
}
