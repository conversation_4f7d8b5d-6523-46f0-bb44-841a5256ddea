package proto.com.linkedin.sales.flagship;

import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.flagship.SalesUpsellFlowConfigService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import javax.inject.Inject;
import proto.com.linkedin.sales.SalesUpsellFlowConfigApiGrpc;
import proto.com.linkedin.sales.SalesUpsellFlowConfigFindBySlotTypeRequest;
import proto.com.linkedin.sales.SalesUpsellFlowConfigFindBySlotTypeResponse;
import proto.com.linkedin.salesupsell.UpsellFlowConfig;
import si.ResponsePagingContext;


// Endpoint to fetch upsell metadata for a member and a slot type
public class SalesUpsellFlowConfigApi extends SalesUpsellFlowConfigApiGrpc.SalesUpsellFlowConfigApiImplBase {
  private final SalesUpsellFlowConfigService _service;
  private final Engine _engine;

  @Inject
  public SalesUpsellFlowConfigApi(SalesUpsellFlowConfigService service, Engine engine) {
    _service = service;
    _engine = engine;
  }

  /**
   * Find upsell for a member (can be SN user or not). If user is not eligible for an upsell, empty payload is returned.
   */
  @Override
  public void findBySlotType(SalesUpsellFlowConfigFindBySlotTypeRequest request,
      StreamObserver<SalesUpsellFlowConfigFindBySlotTypeResponse> responseObserver) {
    Task<List<UpsellFlowConfig>> findBySlotTask =
        _service.getUpsellConfig(request.getViewer(), request.getSlotType(), request.getFlowContext())
            .andThen(upsellFlowConfigs -> {
              SalesUpsellFlowConfigFindBySlotTypeResponse response =
                  SalesUpsellFlowConfigFindBySlotTypeResponse.newBuilder()
                  .setPaging(ResponsePagingContext.newBuilder().setCount(1).build())
                  .setValues(0, upsellFlowConfigs.get(0))
                  .build();
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });
    _engine.run(findBySlotTask);
  }
}
