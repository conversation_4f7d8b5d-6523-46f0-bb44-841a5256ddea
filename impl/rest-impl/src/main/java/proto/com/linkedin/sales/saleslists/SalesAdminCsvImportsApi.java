package proto.com.linkedin.sales.saleslists;

import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.list.SalesAdminCsvImportService;
import io.grpc.stub.StreamObserver;
import javax.inject.Inject;
import proto.com.linkedin.saleslist.AdminCsvImport;
import proto.com.linkedin.saleslist.AdminCsvImportRequest;
import proto.com.linkedin.saleslist.AdminCsvImportResponse;
import proto.com.linkedin.saleslist.SalesAdminCsvImportsApiGrpc;

/**
 * Endpoint for managing all csv import tasks triggered by admin
 */
public class SalesAdminCsvImportsApi extends SalesAdminCsvImportsApiGrpc.SalesAdminCsvImportsApiImplBase {

  private final SalesAdminCsvImportService _service;
  private final Engine _engine;
  @Inject
  public SalesAdminCsvImportsApi(SalesAdminCsvImportService service, Engine engine) {
    _service = service;
    _engine = engine;
  }

  @Override
  public void createAndStartImport(AdminCsvImportRequest request,
      StreamObserver<AdminCsvImportResponse> responseObserver) {
    Task<AdminCsvImportResponse> createAndStartImports = _service.createAndStartUploadByAdmin(request.getStartRequest())
        .map(csvImportTasks -> AdminCsvImportResponse.newBuilder()
            .setAdminCsvImport(AdminCsvImport.newBuilder().addAllListCsvImport(csvImportTasks)).build())
        .andThen(response -> {
          responseObserver.onNext(response);
          responseObserver.onCompleted();
        })
        .onFailure(responseObserver::onError);
  _engine.run(createAndStartImports);
  }
}
