package proto.com.linkedin.sales.warmintros;

import com.linkedin.lss.GrpcUtils;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.warmintros.WarmIntroRecommendationService;
import io.grpc.stub.StreamObserver;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.saleswarmintros.BatchCreateWarmIntroRecommendationRequest;
import proto.com.linkedin.saleswarmintros.BatchCreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.BatchGetWarmIntroRecommendationRequest;
import proto.com.linkedin.saleswarmintros.BatchGetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.CreateWarmIntroRecommendationRequest;
import proto.com.linkedin.saleswarmintros.CreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.GetWarmIntroRecommendationRequest;
import proto.com.linkedin.saleswarmintros.GetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendationApiGrpc;


/**
 * gRPC API for WarmIntroRecommendation CRUD operations.
 */
public class WarmIntroRecommendationApi extends WarmIntroRecommendationApiGrpc.WarmIntroRecommendationApiImplBase {
  private static final Logger LOG = LoggerFactory.getLogger(WarmIntroRecommendationApi.class);

  private final WarmIntroRecommendationService _warmIntroRecommendationService;
  private final Engine _engine;

  @Inject
  public WarmIntroRecommendationApi(
      WarmIntroRecommendationService warmIntroRecommendationService,
      Engine engine) {
    _warmIntroRecommendationService = warmIntroRecommendationService;
    _engine = engine;
  }

  @Override
  public void create(CreateWarmIntroRecommendationRequest request,
      StreamObserver<CreateWarmIntroRecommendationResponse> responseObserver) {
    Task<CreateWarmIntroRecommendationResponse> createWarmIntroRecommendationTask =
        _warmIntroRecommendationService.create(request.getValue())
            .onFailure(throwable -> {
              LOG.error("Failed to create WarmIntroRecommendation", throwable);
              responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
            })
            .andThen(response -> {
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });

    _engine.run(createWarmIntroRecommendationTask);
  }

  @Override
  public void get(GetWarmIntroRecommendationRequest request,
      StreamObserver<GetWarmIntroRecommendationResponse> responseObserver) {
    Task<GetWarmIntroRecommendationResponse> getTask =
        _warmIntroRecommendationService.getWarmIntroRecommendation(request.getKey())
            .onFailure(throwable -> {
              LOG.error("Failed to get WarmIntroRecommendation for key: {}", request.getKey(), throwable);
              responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
            })
            .andThen(response -> {
              LOG.info("Successfully retrieved WarmIntroRecommendation for key: {}", request.getKey());
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });

    _engine.run(getTask);
  }

  @Override
  public void batchCreate(BatchCreateWarmIntroRecommendationRequest request,
      StreamObserver<BatchCreateWarmIntroRecommendationResponse> responseObserver) {
    if (request.getValuesList().isEmpty()) {
      LOG.warn("Batch create request contains an empty values list");
      responseObserver.onError(GrpcUtils.createStatusRuntimeException(new IllegalArgumentException("Values list cannot be empty")));
      return;
    }

    Task<BatchCreateWarmIntroRecommendationResponse> batchCreateTask =
        _warmIntroRecommendationService.batchCreateWarmIntroRecommendation(request.getValuesList())
            .onFailure(throwable -> {
              LOG.error("Failed to batch create WarmIntroRecommendations", throwable);
              responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
            })
            .andThen(response -> {
              LOG.info("Successfully batch created WarmIntroRecommendations");
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });

    _engine.run(batchCreateTask);
  }

  @Override
  public void batchGet(BatchGetWarmIntroRecommendationRequest request,
      StreamObserver<BatchGetWarmIntroRecommendationResponse> responseObserver) {
    Task<BatchGetWarmIntroRecommendationResponse> getTask =
        _warmIntroRecommendationService.batchGetWarmIntroRecommendation(request.getKeysList())
            .onFailure(throwable -> {
              LOG.error("Failed to batch get WarmIntroRecommendation for keys: {}", request.getKeysList(), throwable);
              responseObserver.onError(GrpcUtils.createStatusRuntimeException(throwable));
            })
            .andThen(response -> {
              LOG.info("Successfully retrieved WarmIntroRecommendation for keys: {}", request.getKeysList());
              responseObserver.onNext(response);
              responseObserver.onCompleted();
            });

    _engine.run(getTask);
  }
}
