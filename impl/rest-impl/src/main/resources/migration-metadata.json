[{"customMethodMapping": {}, "resourceName": "salesRealtimeSharedResourceViewerAuthorization", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesRealtimeSharedResourceViewerAuthorizationService"}, {"customMethodMapping": {}, "resourceName": "salesPinnedFilters", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesPinnedFiltersService"}, {"customMethodMapping": {}, "resourceName": "crmDataValidationExportJobs", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.CrmDataValidationExportJobsService"}, {"customMethodMapping": {"seat": "findBySeat", "entity": "findByEntity"}, "resourceName": "salesNotes", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesNotesService"}, {"customMethodMapping": {}, "resourceName": "accountPlaysMetadata", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.AccountPlaysMetadataService"}, {"customMethodMapping": {"buyerOrganization": "findByBuyerOrganization"}, "resourceName": "buyerEngagementDailyActivities", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.BuyerEngagementDailyActivitiesService"}, {"customMethodMapping": {"batchSendActivationNotice": "actionBatchSendActivationNotice", "sendActivationNotice": "actionSendActivationNotice"}, "resourceName": "salesNavigatorEnterpriseApplicationNotices", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesNavigatorEnterpriseApplicationNoticesService"}, {"customMethodMapping": {"lists": "findByLists", "createPlaceholderEntity": "actionCreatePlaceholderEntity", "listForMember": "findByListForMember", "list": "findByList"}, "resourceName": "salesListEntities", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesListEntitiesService"}, {"customMethodMapping": {"seat": "findBySeat"}, "resourceName": "salesRecentActivities", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesRecentActivitiesService"}, {"customMethodMapping": {"removeProduct": "actionRemoveProduct", "addProduct": "actionAddProduct", "updateProduct": "actionUpdateProduct"}, "resourceName": "contractSellerIdentities", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.ContractSellerIdentitiesService"}, {"customMethodMapping": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "resourceName": "salesSavedAccounts", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSavedAccountsService"}, {"customMethodMapping": {}, "resourceName": "salesListEntitySummaries", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesListEntitySummariesService"}, {"customMethodMapping": {"lastKMessages": "findByLastKMessages", "addSalesAIChatMessages": "actionAddSalesAIChatMessages"}, "resourceName": "salesCoachConversation", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesCoachConversationService"}, {"customMethodMapping": {"executeSeatTransferRequest": "actionExecuteSeatTransferRequest"}, "resourceName": "salesLeadsSeatTransferActions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadsSeatTransferActionsService"}, {"customMethodMapping": {"executeSeatTransferRequest": "actionExecuteSeatTransferRequest"}, "resourceName": "salesLeadAccountAssociationsSeatTransferActions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadAccountAssociationsSeatTransferActionsService"}, {"customMethodMapping": {"seat": "findBySeat", "list": "findByList"}, "resourceName": "salesListRoles", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesListRolesService"}, {"customMethodMapping": {"owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "resourceName": "salesSavedLeads", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSavedLeadsService"}, {"customMethodMapping": {}, "resourceName": "salesSubscriptionBenefits", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSubscriptionBenefitsService"}, {"customMethodMapping": {"generateActivationUrl": "actionGenerateActivationUrl"}, "resourceName": "enterpriseSalesSeats", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.EnterpriseSalesSeatsService"}, {"customMethodMapping": {"seatAndContract": "findBySeatAndContract", "contract": "findByContract", "member": "findByMember"}, "resourceName": "salesActivityTotals", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesActivityTotalsService"}, {"customMethodMapping": {"formatNotifications": "actionFormatNotifications", "generateDecorator": "actionGenerateDecorator"}, "resourceName": "salesCommunicationPlugin", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesCommunicationPluginService"}, {"customMethodMapping": {}, "resourceName": "salesLeadAccountMetadata", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadAccountMetadataService"}, {"customMethodMapping": {"saveVieweeAsLead": "actionSaveVieweeAsLead", "unsaveVieweeAsLead": "actionUnsaveVieweeAsLead"}, "resourceName": "salesNavigatorSaveLead", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.salesgateway.SalesNavigatorSaveLeadService"}, {"customMethodMapping": {}, "resourceName": "manualEmailToMemberMapping", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.ManualEmailToMemberMappingService"}, {"customMethodMapping": {"seat": "findBySeat"}, "resourceName": "productCategoryInterests", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.ProductCategoryInterestsService"}, {"customMethodMapping": {"removeProduct": "actionRemoveProduct", "addProduct": "actionAddProduct", "updateProduct": "actionUpdateProduct", "contract": "findByContract", "updateDefaultProduct": "actionUpdateDefaultProduct"}, "resourceName": "salesSellerIdentities", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSellerIdentitiesService"}, {"customMethodMapping": {}, "resourceName": "salesNavigatorProfileAssociations", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.salesgateway.SalesNavigatorProfileAssociationsService"}, {"customMethodMapping": {}, "resourceName": "salesMobileSettings", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesMobileSettingsService"}, {"customMethodMapping": {"contractsByHandraisePilotCompany": "findByContractsByHandraisePilotCompany", "contractsByMember": "findByContractsByMember"}, "resourceName": "salesContracts", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesContractsService"}, {"customMethodMapping": {"historyByFromMember": "findByHistoryByFromMember", "latestByFromMember": "findByLatestByFromMember", "addManagerRelationship": "actionAddManagerRelationship", "removeManagerRelationship": "actionRemoveManagerRelationship"}, "resourceName": "salesColleagueRelationships", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.salescolleagues.SalesColleagueRelationshipsService"}, {"customMethodMapping": {"relationshipMap": "findByRelationshipMap"}, "resourceName": "salesRelationshipMapChangeLogs", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesRelationshipMapChangeLogsService"}, {"customMethodMapping": {}, "resourceName": "salesProfileViews", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesProfileViewsService"}, {"customMethodMapping": {"seatAndAccount": "findBySeatAndAccount"}, "resourceName": "salesAccountToListMappings", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesAccountToListMappingsService"}, {"customMethodMapping": {"executeSeatTransferRequest": "actionExecuteSeatTransferRequest"}, "resourceName": "salesAccountsSeatTransferActions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesAccountsSeatTransferActionsService"}, {"customMethodMapping": {"csvImportTask": "findByCsvImportTask", "creator": "findByCreator", "list": "findByList", "createAndStart": "actionCreateAndStart"}, "resourceName": "salesListCsvImports", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesListCsvImportsService"}, {"customMethodMapping": {}, "resourceName": "salesRealtimeRelationshipMapChangeLogAuthorization", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesRealtimeRelationshipMapChangeLogAuthorizationService"}, {"customMethodMapping": {}, "resourceName": "salesSeatSettings", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSeatSettingsService"}, {"customMethodMapping": {"fetchOnboardingEmailData": "actionFetchOnboardingEmailData", "shouldSendOnboardingEmail": "actionShouldSendOnboardingEmail"}, "resourceName": "salesSolutionsEnterpriseEmailPlugins", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSolutionsEnterpriseEmailPluginsService"}, {"customMethodMapping": {"resource": "findByResource", "subject": "findBySubject", "checkAccessDecision": "actionCheckAccessDecision", "resourceContext": "findByResourceContext"}, "resourceName": "salesSharingPolicies", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesSharingPoliciesService"}, {"customMethodMapping": {}, "resourceName": "salesNavigatorSeatDataExtensions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesNavigatorSeatDataExtensionsService"}, {"customMethodMapping": {"processBatch": "actionProcessBatch", "validate": "actionValidate"}, "resourceName": "crmUserOnboardingBulkActionPlugin", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.CrmUserOnboardingBulkActionPluginService"}, {"customMethodMapping": {"executeSeatTransferRequest": "actionExecuteSeatTransferRequest"}, "resourceName": "salesCustomListsSeatTransferActions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesCustomListsSeatTransferActionsService"}, {"customMethodMapping": {"organizationForAccountMap": "findByOrganizationForAccountMap", "seat": "findBySeat", "entity": "findByEntity"}, "resourceName": "salesLists", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesListsService"}, {"customMethodMapping": {}, "resourceName": "salesDefaultLists", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesDefaultListsService"}, {"customMethodMapping": {"executeSeatTransferRequest": "actionExecuteSeatTransferRequest"}, "resourceName": "salesEntityNotesSeatTransferActions", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesEntityNotesSeatTransferActionsService"}, {"customMethodMapping": {}, "resourceName": "salesLeadEditableContactInfo", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadEditableContactInfoService"}, {"customMethodMapping": {"findByAccount": "findByFindByAccount"}, "resourceName": "accountPlays", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.AccountPlaysService"}, {"customMethodMapping": {"viewerAndDeveloperApp": "findByViewerAndDeveloperApp"}, "resourceName": "salesAccessTokens", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesAccessTokensService"}, {"customMethodMapping": {}, "resourceName": "buyerIntentTrend", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.BuyerIntentTrendService"}, {"customMethodMapping": {"recommend": "findByRecommend"}, "resourceName": "salesPeopleYouMayKnowRecommendations", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesPeopleYouMayKnowRecommendationsService"}, {"customMethodMapping": {"criteria": "findByCriteria"}, "resourceName": "salesAlerts", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesAlertsService"}, {"customMethodMapping": {}, "resourceName": "salesLeadProfileUnlockInfo", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadProfileUnlockInfoService"}, {"customMethodMapping": {"seatAndType": "findBySeatAndType", "contents": "batchFindByContents"}, "resourceName": "salesBookmarks", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesBookmarksService"}, {"customMethodMapping": {"retrieveActivityDataAvailability": "actionRetrieveActivityDataAvailability", "exportMemberIdentityMappingData": "actionExportMemberIdentityMappingData", "exportActivityData": "actionExportActivityData", "retrieveActivityOutcomeDataAvailability": "actionRetrieveActivityOutcomeDataAvailability", "exportSeatData": "actionExportSeatData", "retrieveSeatDataAvailability": "actionRetrieveSeatDataAvailability", "exportActivityOutcomeData": "actionExportActivityOutcomeData", "exportTagData": "actionExportTagData"}, "resourceName": "salesAnalyticsExportJobs", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.salesanalytics.SalesAnalyticsExportJobsService"}, {"customMethodMapping": {"formatNotifications": "actionFormatNotifications", "generateDecorator": "actionGenerateDecorator"}, "resourceName": "salesLeadPositionChangeCommunicationPlugin", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadPositionChangeCommunicationPluginService"}, {"customMethodMapping": {"seat": "findBySeat", "seatAndAccounts": "findBySeatAndAccounts", "seatAndLeads": "findBySeatAndLeads"}, "resourceName": "salesLeadAccountAssociations", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesLeadAccountAssociationsService"}, {"customMethodMapping": {}, "resourceName": "salesFilterLayout", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesFilterLayoutService"}, {"customMethodMapping": {"seat": "findBySeat"}, "resourceName": "salesRecentSearches", "migrationStatus": "BRIDGE_MODE", "serviceName": "proto.com.linkedin.sales.SalesRecentSearchesService"}]