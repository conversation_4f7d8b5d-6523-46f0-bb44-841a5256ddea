package com.linkedin.sales.utils;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.restli.server.RestLiServiceException;
import org.testng.annotations.Test;


public class MemberUtilsTest {

  private static long testIfNonMemberBadParametersCount = -1;
  @Test(invocationCount = 7, expectedExceptions = RestLiServiceException.class)
  public void testIfNonMemberBadParameters() throws RestLiServiceException {
    testIfNonMemberBadParametersCount++;

    MemberUrn memberUrn1;
    MemberUrn memberUrn2;
    switch ((int) testIfNonMemberBadParametersCount)
    {
      case 0:
        memberUrn1 = new MemberUrn(0l);
        memberUrn2 = new MemberUrn(1l);
        break;
      case 1:
        memberUrn1 = new MemberUrn(-1l);
        memberUrn2 = new MemberUrn(1l);
        break;
      case 2:
        memberUrn1 = null;
        memberUrn2 = new MemberUrn(1l);
        break;
      case 3:
        memberUrn1 = new MemberUrn(1l);
        memberUrn2 = new MemberUrn(0l);
        break;
      case 4:
        memberUrn1 = new MemberUrn(1l);
        memberUrn2 = new MemberUrn(-1l);
        break;
      case 5:
        memberUrn1 = new MemberUrn(1l);
        memberUrn2 = null;
        break;
      case 6:
        memberUrn1 = null;
        memberUrn2 = null;
        break;
      default:
        throw new RuntimeException("Didn't define a test case for case # " + testIfNonMemberBadParametersCount);

    }
    MemberUtils.ifNonMemberThenThrow400(memberUrn1, memberUrn2);
  }

  @Test()
  public void testIfNonMemberValidParam() {
    MemberUtils.ifNonMemberThenThrow400(new MemberUrn(1l), new MemberUrn(Long.MAX_VALUE));
  }
}
