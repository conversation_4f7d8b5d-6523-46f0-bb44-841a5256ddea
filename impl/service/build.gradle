import com.github.spotbugs.snom.SpotBugsTask


ext.apiMpName = 'lss-mt-api'
ext.apiMpApiDir = 'lss-mt-api'
ext.apiProject = project(':rest-api')

apply plugin: 'li-java'

dependencies {
  api project(':impl:ds')
  api project(':impl:client')

  // jersey_client is needed for Azkaban client
  api spec.external.'jersey-client'
  api spec.external.'jersey-media-jaxb'
  api spec.external.'jersey-media-multipart'

  api spec.external.'spotbugs-annotations'

  api spec.product.azcli.azcli
  api spec.external.'parseq-test-api'
  api spec.product.talent.decorator
  api spec.product.'avro-schemas'.'avro-schemas-tracking'
  api spec.product.'crm-api'.'crm-common'
  api spec.product.'crm-api'.'crm-backend-api-restClient'
  api spec.product.'espresso-pub'.'espresso-client-impl'
  api spec.product.'restli-gateway-util'.'restli-gateway-util'
  api spec.product.'security-crypto'.'security-crypto'
  api spec.product.'security-crypto'.'security-crypto-factory'
  api spec.product.urls.adapters
  api spec.product.urls.'url-private-aliases'
  api (spec.product.'lighthouse-bps'."lighthouse-client-impl$scalaSuffix")
  api spec.product.'ambry-client'.'ambry-client-factory'
  api spec.product.'login-server-api'.'oauth2-client-restClient'
  api spec.product.'global-keygen'.'global-keygen-api-restClient'
  api spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api'
  api spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api-dataTemplate'
  api spec.product.frameworks.'fwk-gui-impl'
  api spec.product.util.'util-json'
  api spec.product.'lix-client-api'.'lix-client-api'
  api spec.product.'lix-client'.'lix-client-factory'
  api spec.product.'linkedin-kafka-clients'.'tracker-processor-factory'
  api spec.product.'lss-buyer-data-schemas'.'lss-buyer-data-model-avro17'
  api spec.product.'lss-common'.'crm-clients'
  api spec.product.'lss-common'.'lss-salesleadaccount-common'
  api spec.product.'lss-common'.'lss-workflow-common'
  api spec.product.'lss-common'.'lss-search-common'
  api spec.product.'lss-mt-api'.'lss-mt-api-restClient'
  api spec.product.'gaap-tasks-client-java'.'polling-client'
  api spec.product.'i18n-core'.'i18n-core-impl'
  api spec.product.ibrik.'ibrik-client'
  api spec.product.'ep-apps-connector'.'dataextension-plugin'
  api spec.product.'ep-apps-connector'.'dataextension-plugin-dataTemplate'
  api spec.product.'venice-thin-client'.'venice-thin-client'
  api spec.product.'omni-utils'.'omni-utils-common_2.11'
  api spec.product.'lsi-common'.'lsi-factories_2.12'
  api spec.product.'omni-utils'.'omni-utils-ambry_2.11'
  api spec.product.'omni-utils'.'omni-utils-ambry-factories_2.11'
  api spec.product.'comms-rendering-mt'.'plugins-api'
  api spec.product.'comms-rendering-mt'.'comms-rendering-mt-helpers'
  api spec.product.'comms-rendering-mt'.'comms-rendering-mt-helpers-factory'
  api spec.product.'application-infrastructure-common'.'application-testing'
  api spec.product.'application-infrastructure-common'.'application-utilities'
  api spec.product.'lss-reporting'.'lss-reporting-grpc-api-protoImplementation'
  api spec.product.'lss-reporting'.'lss-reporting-grpc-api-proto-data-model'

  testImplementation project(':test:test-fwk')
  testImplementation spec.external.'assertj-core'
  testImplementation spec.external.'mockito-core'
  testImplementation spec.external.'mockito-junit-jupiter'
  testImplementation spec.external.'parseq-test-api'
  testImplementation spec.external.'mockito-inline'
  testImplementation spec.external.'powermock-api-mockito2'
  testImplementation spec.external.'powermock-module-testng'
  testImplementation spec.external.'restli-client-testutils'
  testImplementation spec.external.testng
  testImplementation spec.product.'comms-rendering-mt'.'comms-rendering-mt-test-helpers'

  tasks.withType(JavaCompile).configureEach {
    options.fork = true
    options.forkOptions.memoryMaximumSize = '8g'
  }

  tasks.withType(SpotBugsTask).configureEach {
    maxHeapSize = '4g'
  }
}

sourceSets {
  main {
    resources {
      srcDirs "${rootDir}/i18n/lang/impl/service/src/main/resources"
    }
  }
}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.memoryMaximumSize = '8g'
}
