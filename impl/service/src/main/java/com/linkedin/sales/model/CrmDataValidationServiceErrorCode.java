package com.linkedin.sales.model;

/**
 * Created by jiawang on 8/21/2018
 * Service error code for Lss Crm data validation export
 */
public enum CrmDataValidationServiceErrorCode {

  /**
   * 400 Error
   */
  INVALID_CRM_INSTANCE(40001),
  INVALID_EXPORT_DATE(40002),
  NOT_SYNCED_TO_SN(40003),
  FEATURE_NOT_ENABLED(40004),
  REQUEST_NOT_TRUSTED(40005),

  /**
   * 500 Error
   */
  AZKABAN_JOB_CREATION_FAILURE(50001),
  GOBBLIN_ERROR(50002),
  MISSING_AMBRY_BLOB_ID(50003),
  APPLICATION_URN_CREATION_FAILURE(50004),
  AMBRY_URL_CREATION_FAILURE(50005),
  AZKABAN_JOB_EXECUTION_FAILURE(50006),
  NO_RECORDS_FOUND_FOR_CRM_INSTANCE(50007),
  BULK_EXPORT_MISSING_AMBRY_BLOB_ID(50008);

  private final int _code;

  CrmDataValidationServiceErrorCode(int code) {
    _code = code;
  }

  public int getCode() {
    return _code;
  }
}
