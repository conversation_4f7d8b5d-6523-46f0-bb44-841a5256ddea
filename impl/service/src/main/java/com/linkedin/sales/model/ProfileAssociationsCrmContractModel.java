package com.linkedin.sales.model;

import com.linkedin.common.urn.ContractUrn;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This model add a boolean flag to indicate if the contract is picked from multiple contracts matched from preference search.
 * Only used by SalesNavigatorProfileAssociationsService
 */
public class ProfileAssociationsCrmContractModel {
  private static final Logger LOG = LoggerFactory.getLogger(ProfileAssociationsCrmContractModel.class);

  private final ContractUrn _contactUrn;
  private final boolean _isPickedFromMultipleResults;

  public ProfileAssociationsCrmContractModel(@NonNull ContractUrn contactUrn, @NonNull boolean isPickedFromMultipleResults) {
    _contactUrn = contactUrn;
    _isPickedFromMultipleResults = isPickedFromMultipleResults;
  }

  @NonNull
  public ContractUrn getContactUrn() {
    return _contactUrn;
  }

  @NonNull
  public boolean isPickedFromMultipleResults() {
    return _isPickedFromMultipleResults;
  }
}
