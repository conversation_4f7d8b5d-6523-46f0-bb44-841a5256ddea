package com.linkedin.sales.model;

/**
 * Created by jfontas on 6/5/2019
 * Service error codes for Profile Associations
 */
public enum ProfileAssociationsServiceErrorCode {

  /**
   * 400 Error
   */
  CRM_SYNC_NOT_ENABLED(40001, "CRM Sync not enabled for this CRM instance."),
  CRM_SYNC_STATUS_NOT_FOUND(40002, "Cannot determine CRM Sync status for this CRM instance."),
  REQUEST_NOT_TRUSTED(40003, "Requested data does not belong to the app instance.");

  private final int _code;
  private final String _message;

  ProfileAssociationsServiceErrorCode(int code, String message) {
    _code = code;
    _message = message;
  }

  public int getCode() {
    return _code;
  }
  public String getMessage() {
    return _message;
  }

}
