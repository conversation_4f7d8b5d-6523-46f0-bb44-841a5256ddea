package com.linkedin.sales.model.pinot;

/**
 * represents the selected pinot columns in the first part of a SELECT PQL query,
 * it could be a direct pinot column or an aggregation on a pinot column
 * example:
 * - SELECT count(pinotColumnNameA)
 * - SELECT col(pinotColumnNameB)
 * etc...
 */
public class Column {

  public Column(String expr) {
    this.expr = expr;
  }

  private String expr;

  /**
   * converts a facet into a pinot column
   *
   * @param val the converted facet
   * @return the pinot column
   */
  public static Column col(String val) {
    return new Column(val);
  }

  public Column next(Column that) {
    return new Column(expr + ", " + that.expr);
  }

  /**
   * PQL min(val) aggregation
   */
  public static Column min(String val) {
    return new Column("min(" + val + ")");
  }

  /**
   * PQL max(val) aggregation
   */
  public static Column max(String val) {
    return new Column("max(" + val + ")");
  }

  /**
   * PQL sum(val) aggregation
   */
  public static Column sum(String val) {
    return new Column("sum(" + val + ")");
  }

  /**
   * PQL count(val) statement
   */
  public static Column count(String val) {
    return new Column("count(" + val + ")");
  }

  /**
   * PQL ORDERING DESC
   */
  public Column desc() {
    return new Column(expr + " desc ");
  }

  /**
   * PQL ORDERING ASC
   */
  public Column asc() {
    return new Column(expr + " asc ");
  }

  /**
   * string representation of this column aggregation
   */
  public String getExpr() {
    return expr;
  }
}
