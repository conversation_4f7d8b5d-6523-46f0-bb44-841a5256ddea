package com.linkedin.sales.model.pinot;

/**
 * represents a condition in a PQL WHERE clause.
 * Example: (A == 2), (A == 2 AND B > 4), (A == 2 OR (B == 4 AND C == 6))
 */
public class Condition {

  public Condition() {
  }

  public Condition(String expr) {
    this.expr = expr;
  }

  private String expr;

  /**
   * combine a condition with another with algebraic AND
   *
   * @param otherCondition the condition to combine with
   * @return a generic representation of the combination of two condition with AND
   */
  public Condition and(Condition otherCondition) {
    return new Condition(expr + " and " + otherCondition.expr);
  }

  /**
   * combine a condition with another with algebraic OR
   *
   * @param otherCondition the condition to combine with
   * @return a generic representation of the combination of two condition with OR
   */
  public Condition or(Condition otherCondition) {
    return new Condition(expr + " or " + otherCondition.expr);
  }

  /**
   * @param col the pinot table column name
   * @param value the facet value to compare
   * @return a generic representation of a PQL condition `col = value`
   */
  public static Condition equality(String col, Object value) {
    return new Condition(col + " = " + value);
  }

  /**
   * @param col the pinot table column name
   * @param value the facet value to compare
   * @return a generic representation of a PQL condition `col < value`
   */
  public static Condition less(String col, Object value) {
    return new Condition(col + "<" + value);
  }

  /**
   * @param col the pinot table column name
   * @param value the facet value to compare
   * @return a generic representation of a PQL condition `col <= value`
   */
  public static Condition lessOrEqual(String col, Object value) {
    return new Condition(col + "<=" + value);
  }

  /**
   * @param col the pinot table column name
   * @param value the facet value to compare
   * @return a generic representation of a PQL condition `col > value`
   */
  public static Condition greater(String col, Object value) {
    return new Condition(col + ">" + value);
  }

  /**
   * @param col the pinot table column name
   * @param value the facet value to compare
   * @return a generic representation of a PQL condition `col >= value`
   */
  public static Condition greaterOrEqual(String col, Object value) {
    return new Condition(col + ">=" + value);
  }

  /**
   * @param col the pinot table column name
   * @param left the left facet value to compare
   * @param right the right facet value to compare
   * @return a generic representation of a PQL condition `col between left and right`
   */
  public static Condition betweenAnd(String col, Object left, Object right) {
    return new Condition(col + " between " + left + " and " + right);
  }

  /**
   * string representation of this condition
   */
  public String getExpr() {
    return expr;
  }
}
