package com.linkedin.sales.model.pinot;

/**
 * Builder pattern to instantiate and populate a PQL query
 */
public class PinotQueryBuilder {

  private static final char SPACE = ' ';

  private String table;
  private Condition condition;
  private Column select;
  private Column groupBy;
  private Column orderBy;
  private Integer limit;

  /**
   * PQL SELECT statement
   */
  public PinotQueryBuilder select(Column select) {
    this.select = select;
    return this;
  }

  /**
   * PQL FROM statement
   */
  public PinotQueryBuilder from(String table) {
    this.table = table;
    return this;
  }

  /**
   * PQL WHERE statement
   */
  public PinotQueryBuilder where(Condition condition) {
    this.condition = condition;
    return this;
  }

  /**
   * PQL GROUPBY statement
   */
  public PinotQueryBuilder groupBy(Column groupBy) {
    this.groupBy = groupBy;
    return this;
  }

  /**
   * PQL ORDERBY statement
   */
  public PinotQueryBuilder orderBy(Column orderBy) {
    this.orderBy = orderBy;
    return this;
  }

  /**
   * PQL LIMIT statement
   */
  public PinotQueryBuilder limit(int count) {
    this.limit = count;
    return this;
  }

  /**
   * build a PQL query from {@link PinotQueryBuilder} attributes
   */
  public String build() {
    StringBuilder sb = new StringBuilder();
    if (select != null) {
      sb.append("select ").append(select.getExpr()).append(SPACE);
    }
    if (table != null) {
      sb.append("from ").append(table).append(SPACE);
    }
    if (condition != null) {
      sb.append("where ").append(condition.getExpr()).append(SPACE);
    }
    if (groupBy != null) {
      sb.append("group by ").append(groupBy.getExpr()).append(SPACE);
    }
    if (orderBy != null) {
      sb.append("order by ").append(orderBy.getExpr()).append(SPACE);
    }
    if (limit != null) {
      sb.append("limit ").append(limit).append(SPACE);
    }
    return sb.toString();
  }
}
