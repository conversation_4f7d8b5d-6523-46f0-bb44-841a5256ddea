package com.linkedin.sales.monitoring;

import com.linkedin.healthcheck.pub.AttributeMetricType;
import com.linkedin.healthcheck.pub.sensor.MetricsSensor;
import com.linkedin.util.stats.CounterMetrics;
import com.linkedin.util.stats.CounterMetricsImpl;
import edu.umd.cs.findbugs.annotations.NonNull;
/**
 * Created by jiawang on 11/5/2018
 * InGraph custom metrics sensor for Counter
 */
public class CounterMetricsSensor extends MetricsSensor {
  private static final String CRM_DATA_VALIDATION_CREATE = "CrmDataValidationCreate";
  private static final String CRM_DATA_VALIDATION_BULK_EXPORT_CREATE = "CrmDataValidationBulkExportCreate";
  private static final String CRM_DATA_VALIDATION_GET = "CrmDataValidationGet";
  private static final String CRM_DATA_VALIDATION_BULK_EXPORT_GET = "CrmDataValidationBulkExportGet";
  private static final String CRM_DATA_VALIDATION_AZKABAN_EXECUTION_SUCCESS = "CrmDataValidationAzkabanExecSuccess";
  private static final String CRM_DATA_VALIDATION_AZKABAN_EXECUTION_FAILURE = "CrmDataValidationAzkabanExecFailure";
  private static final String CRM_DATA_VALIDATION_NO_RECENT_AMBRY_BLOB_FOUND = "CrmDataValidationNoRecentAmbryBlobFound";

  private static final String QPS_EXCEEDED_FOR_SALES_ACTIVITY_PINOT_TABLE = "QpsExceededForSalesActivityPinotTable";

  private final CounterMetrics _counterMetrics;

  public CounterMetricsSensor() {
    this(new CounterMetricsImpl(CounterMetricsSensor.class.getName()));
  }

  private CounterMetricsSensor(CounterMetricsImpl counterMetrics) {
    super(counterMetrics, AttributeMetricType.COUNTER, 100);
    _counterMetrics = counterMetrics;
  }

  private void incrementCounter(@NonNull String id) {
    _counterMetrics.increment(id);
  }

  /**
   * Increments the metrics counter for Data Validation Create call
   */
  public void incrementCrmDataValidationCreateCallCounter() {
    incrementCounter(CRM_DATA_VALIDATION_CREATE);
  }

  /**
   * Increments the metrics counter for Data Validation Create call when bulk export is enabled
   */
  public void incrementCrmDataValidationBulkExportCreateCallCounter() {
    incrementCounter(CRM_DATA_VALIDATION_BULK_EXPORT_CREATE);
  }

  /**
   * Increments the metrics counter if QPS limit exceeded for call to salesActivityPinotStatistics
   */
  public void incrementQPSExceededForPinotTableCallCounter() {
    incrementCounter(QPS_EXCEEDED_FOR_SALES_ACTIVITY_PINOT_TABLE);
  }


  /**
   * Increments the metrics counter for Data Validation Get call
   */
  public void incrementCrmDataValidationGetCallCounter() {
    incrementCounter(CRM_DATA_VALIDATION_GET);
  }

  /**
   * Increments the metrics counter for Data Validation Get call when Bulk Export is enabled
   */
  public void incrementCrmDataValidationBulkExportGetCallCounter() {
    incrementCounter(CRM_DATA_VALIDATION_BULK_EXPORT_GET);
  }

  /**
   * Increments the metrics counter for Data Validation Azkaban client execution call when successful
   */
  public void incrementCrmDataValidationAzkabanExecutionSuccessCounter() {
    incrementCounter(CRM_DATA_VALIDATION_AZKABAN_EXECUTION_SUCCESS);
  }

  /**
   * Increments the metrics counter for Data Validation Azkaban client execution call when failed
   */
  public void incrementCrmDataValidationAzkabanExecutionFailureCounter() {
    incrementCounter(CRM_DATA_VALIDATION_AZKABAN_EXECUTION_FAILURE);
  }

  /**
   * Increments the metrics counter for Data Validation Azkaban when no recent Ambry Blob found
   */
  public void incrementCrmDataValidationNoRecentAmbryBlobFoundCounter() {
    incrementCounter(CRM_DATA_VALIDATION_NO_RECENT_AMBRY_BLOB_FOUND);
  }
}
