package com.linkedin.sales.monitoring;

import com.linkedin.healthcheck.pub.AttributeMetricType;
import com.linkedin.healthcheck.pub.sensor.MetricsSensor;
import com.linkedin.util.stats.GaugeMetrics;
import com.linkedin.util.stats.GaugeMetricsImpl;
import edu.umd.cs.findbugs.annotations.NonNull;


/**
 * Created by jiawang on 11/5/2018
 * InGraph custom metrics sensor for Gauge
 */
public class GaugeMetricsSensor extends MetricsSensor {
  private static final String CRM_DATA_VALIDATION_JOB_RUNNING_TIME_FROM_GET = "CrmDataValidationJobRunningTimeFromGet";
  private static final String CRM_DEALS_CONTACT_REQUIRED_CUSTOM_FIELDS = "CrmDealsContactRequiredFields";
  private static final String CRM_DEALS_CONTACT_MAILING_COUNTRY_CODE = "CrmDealsContactMailingCountryCode";
  private static final String CRM_DEALS_CONTACT_NON_WRITEABLE_STANDARD_FIELDS = "CrmDealsContactNonWritableStandardFields";

  private final GaugeMetrics _gaugeMetrics;

  public GaugeMetricsSensor() {
    this(new GaugeMetricsImpl(GaugeMetricsSensor.class.getName()));
  }

  private GaugeMetricsSensor(GaugeMetricsImpl gaugeMetrics) {
    super(gaugeMetrics, AttributeMetricType.GAUGE, 100);
    _gaugeMetrics = gaugeMetrics;
  }

  private void setGauge(@NonNull String id, long value) {
    _gaugeMetrics.set(id, value);
  }

  /**
   * Sets the job running time for Data Validation Get call
   * Note that this calculates how long the job has been running since the created time, which is used to trigger alerts
   * if the job is still in progress after a large time threshold.
   * @param value time duration in minutes
   */
  public void setCrmDataValidationJobRunningTime(long value) {
    setGauge(CRM_DATA_VALIDATION_JOB_RUNNING_TIME_FROM_GET, value);
  }

  public void trackCrmDealsContactRequiredFields(String crmTenantId, String field) {
    setGauge(CRM_DEALS_CONTACT_REQUIRED_CUSTOM_FIELDS + "-" + field + "-" + crmTenantId, 1);
  }

  public void trackCrmDealsContactCountryCode(String crmTenantId) {
    setGauge(CRM_DEALS_CONTACT_MAILING_COUNTRY_CODE + "-" + crmTenantId, 1);
  }

  public void trackCrmDealsContactNonWritableStandardFields(String crmTenantId, String field) {
    setGauge(CRM_DEALS_CONTACT_NON_WRITEABLE_STANDARD_FIELDS + "-" + field + "-" + crmTenantId, 1);
  }
}
