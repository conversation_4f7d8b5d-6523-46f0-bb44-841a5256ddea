package com.linkedin.sales.monitoring.flagship;

import com.linkedin.util.AbstractEnumMetrics;
import com.linkedin.util.EnumMetrics;
import java.util.EnumSet;


public class SalesPymkRecommendationsMetrics
    extends AbstractEnumMetrics<SalesPymkRecommendationsMetrics.Counter, SalesPymkRecommendationsMetrics.Gauge> {
  public SalesPymkRecommendationsMetrics() {
    super(Counter.class, Gauge.class,
        //Supported percentile metrics
        EnumSet.of(EnumMetrics.Stat.AVG, EnumMetrics.Stat.PCT_95, EnumMetrics.Stat.PCT_90, EnumMetrics.Stat.PCT_50,
            EnumMetrics.Stat.PCT_99));
  }

  public enum Counter {
    // Count of total number of requests
    TOTAL_REQUESTS,

    // Count of total errors, since we return empty if there's an error
    TOTAL_ERRORS,

    // Count of requests where we had no recommendations for the user
    NO_RESULTS,

    // Count of requests where we return results to the user
    SUCCESSFUL_RESULT,

    // Count of requests where the user was a premium member
    HAS_PREMIUM,

    // Total count of requests where we try to fetch a recommendation from venice
    FETCH_RECOMMENDATIONS,

    // Total count of requests where the overall feature gating lix was not enabled
    FEATURE_GATING_LIX_DISABLED,

    // Total count of requests for mocked data
    MOCK_DATA,

    // Count of requests missing IC client platform information
    MISSING_IC_CLIENT_PLATFORM
  }

  public enum Gauge {
  }
}
