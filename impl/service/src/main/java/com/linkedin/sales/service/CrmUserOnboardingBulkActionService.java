package com.linkedin.sales.service;

import com.linkedin.common.CrmUserUrnArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmUserUrn;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.CrmUser;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResult;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResultArray;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyStatus;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.bulk.BulkActionI18nMessage;
import com.linkedin.enterprise.bulk.BulkActionMessage;
import com.linkedin.enterprise.bulk.BulkActionPluginException;
import com.linkedin.enterprise.bulk.BulkCrmUserSyncContext;
import com.linkedin.enterprise.bulk.BulkLicenseAssignmentContext;
import com.linkedin.enterprise.identity.ProfileKey;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CreateIdStatus;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.EmailAddressClient;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.client.integration.CrmUserClient;
import com.linkedin.sales.service.utils.ParseqUtil;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;


/**
 * This service implements the functionality for CrmUserOnboardingBulkActionResource.
 * There are two main public calls: validate and processBatch.
 * "validate" makes sure the context is valid.
 * "processBatch" is the main processor for this bulk action. It does the things described in the Resource.
 */
public class CrmUserOnboardingBulkActionService {
  private static final Logger LOG = LoggerFactory.getLogger(CrmUserOnboardingBulkActionService.class);
  private static final int DEFAULT_PARSEQ_SECONDS = 5;

  private final Engine _engine;
  private final CrmUserClient _crmUserClient;
  private final EnterprisePlatformClient _enterprisePlatformClient;
  private final EmailAddressClient _emailAddressClient;

  private static final EnterpriseApplicationUsageUrn VIEWER =
      new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
          "migratedAppInstanceMappingToEP");

  public CrmUserOnboardingBulkActionService(Engine engine, CrmUserClient crmUserClient,
      EnterprisePlatformClient enterprisePlatformClient, EmailAddressClient emailAddressClient) {
    _engine = engine;
    _crmUserClient = crmUserClient;
    _enterprisePlatformClient = enterprisePlatformClient;
    _emailAddressClient = emailAddressClient;
  }

  /**
   * Validate that context contains applicationInstance, and if license assignment context exists,
   * validate about its budget group and license availability. This is done by a service call to eis-backend.
   */
  public Optional<BulkActionMessage> validate(BulkCrmUserSyncContext context, Urn viewer)
      throws BulkActionPluginException {
    if (!context.hasCrmSyncApplicationInstance()) {
      return Optional.of(BulkActionMessage.PLUGIN_PROFILE_CREATION_ERROR);
    }

    if (context.hasLicenseAssignmentContext()) {
      Task<Void> validateLicenseRequestTask = _enterprisePlatformClient.validateLicenseAssignmentBulkActionContext(
          context.getLicenseAssignmentContext(), viewer);
      try {
        // Validation failure will result in BulkActionPluginException
        ParseqUtil.await(validateLicenseRequestTask, _engine);
      } catch (Exception e) {
        return Optional.of(BulkActionMessage.PLUGIN_ASSIGN_LICENSE_ERROR);
      }
    }
    return Optional.empty();
  }

  /**
   * This is the main action to process the bulk action as described in the Resource.
   * 1. create CrmUserOnboardingContext for each CrmUserUrn in CrmUserUrnArray
   * 2. append CrmUser, set Error when no CrmUser
   * 3. append EmailAddressUrn, setError when no emailAddressUrn
   * 4. append profile, setError when no profileKey
   * 5. assign licenses, setError when unable to
   *
   * @param inputKeys  A list of CrmUserUrn to be processed.
   * @param context  The context for onboarding, which may optionally contain license assignment context.
   * @param viewer  Urn of viewer initiated this bulk action.
   * @return  Result which contains a map of the original CrmUserUrn, and the processing status.
   */
  public BulkActionBatchResult processBatch(CrmUserUrnArray inputKeys,
      BulkCrmUserSyncContext context, Urn viewer) {
    EnterpriseApplicationInstanceUrn applicationInstanceUrn = context.getCrmSyncApplicationInstance();

    // Create or find EnterpriseProfiles
    List<CrmUserOnboardingContext> crmUserContexts = initCrmUserOnboardingContexts(applicationInstanceUrn, inputKeys);

    appendCrmUsers(crmUserContexts, applicationInstanceUrn, inputKeys);

    appendEmailAddressUrns(crmUserContexts);

    // NOTE: Existing MemberUrns will be assigned to EnterpriseProfile during creation.
    // So, we do not need a step to find existing member Urn.
    // Find existing enterprise profiles based on EmailAddressUrns.
    // This step does not eliminate any CrmUserOnboardingContext from the current list.
    // Therefore, no error to track afterwards.
    appendExistingProfileKeys(crmUserContexts, viewer);
    createAndAppendProfileKeys(crmUserContexts, viewer);

    crmUserContexts.forEach(cuoCtxt -> {
      if (!cuoCtxt.isGoodToAssignLicense()) {
        cuoCtxt.setError(CrmUserOnboardingError.CANNOT_CREATE_ENTERPRISE_PROFILE);
      }
    });

    if (context.hasLicenseAssignmentContext()) {
      assignLicenseToCrmUsers(crmUserContexts,
          context.getLicenseAssignmentContext(),
          viewer);
    }

    return new BulkActionBatchResult().setInputKeyResults(
        new BulkActionBatchInputKeyResultArray(convertToInputKeyResult(crmUserContexts)));
  }

  private List<CrmUserOnboardingContext> initCrmUserOnboardingContexts(
      EnterpriseApplicationInstanceUrn applicationInstanceUrn, Collection<CrmUserUrn> crmUserUrns) {
    return crmUserUrns.stream()
        .map(crmUserUrn -> new CrmUserOnboardingContext(applicationInstanceUrn, crmUserUrn))
        .collect(Collectors.toList());
  }


  private void appendCrmUsers(Collection<CrmUserOnboardingContext> crmUserOnboardingContexts,
      EnterpriseApplicationInstanceUrn enterpriseApplicationInstanceUrn,
      CrmUserUrnArray crmUserUrnArray) {

    Map<CrmUserUrn, CrmUser> crmUserMap = findCrmUsers(enterpriseApplicationInstanceUrn, crmUserUrnArray);
    crmUserOnboardingContexts.forEach(cuoCtxt -> {
      CrmUser cUser = crmUserMap.get(cuoCtxt.getUrn());
      cuoCtxt.setCrmUser(cUser);
      cuoCtxt.setError(verifyCrmUser(cuoCtxt, cUser));
    });
  }

  private CrmUserOnboardingError verifyCrmUser(CrmUserOnboardingContext context, CrmUser cUser) {
    if (cUser == null) {
      LOG.warn("bad crmUser for appending, crmUserUrn:{}, applicationInstance:{}",
          context.getUrn(), context.getApplicationInstanceUrn());
      return CrmUserOnboardingError.CANNOT_FIND_CRM_USER;
    } else if (cUser.getEmail() == null) {
      LOG.warn("bad crmUser for appending, crmUser:{}, applicationInstance:{}",
          cUser, context.getApplicationInstanceUrn());
      return CrmUserOnboardingError.BAD_CRM_USER;
    }
    return null;
  }

  private Map<CrmUserUrn, CrmUser> findCrmUsers(EnterpriseApplicationInstanceUrn enterpriseApplicationInstanceUrn,
      CrmUserUrnArray crmUserUrnArray) {
    if (crmUserUrnArray.isEmpty()) {
      return Collections.emptyMap();
    }

    Task<ContractUrn> getMappedContractTask = _enterprisePlatformClient
        .getAppInstanceWithMappedContract(enterpriseApplicationInstanceUrn, VIEWER)
        .map(appInstanceOptional -> {
          if (appInstanceOptional.isPresent() && appInstanceOptional.get().hasContractId()) {
            return new ContractUrn(appInstanceOptional.get().getContractId());
          }
          throw new IllegalStateException("failed to find crm users because no contract "
              + "mapped to this enterpriseAppInstanceUrn: " + enterpriseApplicationInstanceUrn);
        });

    Task<List<CrmUser>> task = getMappedContractTask.flatMap(maybeContractUrn -> {
      if (maybeContractUrn != null) {
        return _crmUserClient.findByQuery(maybeContractUrn, StringUtils.EMPTY, crmUserUrnArray, false, 0,
            crmUserUrnArray.size());
      } else {
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            "No contract urn found for the given app instance");
      }
    });

    try {
      return ParseqUtil.await(task, _engine).stream()
          .filter(Objects::nonNull)
          .collect(Collectors.toMap(
              CrmUser::getUrn,
              Function.identity()
          ));
    } catch (Exception e) {
      LOG.error("unable to initCrmUserOnboardingContexts, enterpriseApplicationInstanceUrn: {}, crmUserUrns:{}",
          enterpriseApplicationInstanceUrn, crmUserUrnArray, e);
      return Collections.emptyMap();
    }
  }


  private void appendEmailAddressUrns(List<CrmUserOnboardingContext> crmUserContexts) {
    if (CollectionUtils.isEmpty(crmUserContexts)) {
      return;
    }
    List<String> emailStrs = crmUserContexts.stream()
        .filter(CrmUserOnboardingContext::isGoodToAppendEmailAddressUrn)
        .map(CrmUserOnboardingContext::getEmail)
        .collect(Collectors.toList());

    Map<String, EmailAddressClient.CreateOrGetResult> emailAddressUrnGetOrCreateResults =
        ParseqUtil.await(_emailAddressClient.batchCreateOrGetEmailAddressUrns(emailStrs), _engine,
            DEFAULT_PARSEQ_SECONDS, TimeUnit.SECONDS);

    crmUserContexts.stream()
        .filter(CrmUserOnboardingContext::isGoodToAppendEmailAddressUrn)
        .forEach(cuoCtxt -> {
      EmailAddressClient.CreateOrGetResult getOrCreateResult = emailAddressUrnGetOrCreateResults.get(cuoCtxt.getEmail());
      EmailAddressUrn emailAddressUrnIfAny = getOrCreateResult.getEmailAddressUrnIfAny();
      if (emailAddressUrnIfAny == null) {
        cuoCtxt.setError(CrmUserOnboardingError.CANNOT_FIND_OR_CREATE_EMAIL_ADDRESS_URN);
        CreateIdStatus<Long> status = getOrCreateResult.getStatus();
        LOG.error("unable to createOrGet emailAddressUrn for crmUser, crmUserOnboardingContext:{}, createIdStatus:{}",
            cuoCtxt, status);
      } else {
        cuoCtxt.setEmailAddressUrn(emailAddressUrnIfAny);
      }
    });
  }


  private void appendExistingProfileKeys(Collection<CrmUserOnboardingContext> crmUserContexts, Urn viewer) {
    // Summarize the valid EmailAddressUrn and the corresponding CrmUserOnboardingContext.
    // This is used for cross-checking with Profiles list returned from EP which may contain ambiguity.
    List<Task<CrmUserOnboardingContext>> tasks = crmUserContexts.stream()
        .filter(CrmUserOnboardingContext::isGoodToAppendProfileKey)
        .map(crmUserContext -> {
          final EmailAddressUrn emailAddressUrn = crmUserContext.getEmailAddressUrn();
          return _enterprisePlatformClient.findEnterpriseProfileByEmail(
              crmUserContext.getApplicationInstanceUrn(),
              crmUserContext.getEmail(),
              viewer)
              .map(foundProfiles -> {
                foundProfiles.stream()
                    .filter(profile -> emailAddressUrn.equals(profile.getPrimaryEmailAddress()))
                    .findFirst()
                    .ifPresent(profile -> crmUserContext.setProfileKey(profile.getKey()));
                return crmUserContext;
              });
        })
        .collect(Collectors.toList());

    ParseqUtil.par(tasks, _engine);
  }

  private void createAndAppendProfileKeys(List<CrmUserOnboardingContext> crmUserContexts, Urn viewer) {
    List<Task<Pair<CrmUserUrn, ProfileKey>>> createProfilesTasks = crmUserContexts.stream()
        .filter(CrmUserOnboardingContext::isGoodToAppendProfileKey)
        .map(crmUserContext -> _enterprisePlatformClient.createEnterpriseProfileFromCrmUser(
            crmUserContext.getUrn(),
            crmUserContext.getFirstName(),
            crmUserContext.getLastName(),
            crmUserContext.getEmailAddressUrn(),
            crmUserContext.getApplicationInstanceUrn(),
            viewer))
        .collect(Collectors.toList());
    try {
      Map<CrmUserUrn, ProfileKey> crmUserUrnToProfileKeyMap = ParseqUtil.par(createProfilesTasks, _engine).stream()
          .collect(Collectors.toMap(
              Pair::getFirst,
              Pair::getSecond));

      crmUserContexts.forEach(crmUserContext -> {
        if (crmUserContext.isGoodToAppendProfileKey()) {
          ProfileKey profileKey = crmUserUrnToProfileKeyMap.get(crmUserContext.getUrn());
          if (profileKey == null) {
            crmUserContext.setError(CrmUserOnboardingError.CANNOT_CREATE_ENTERPRISE_PROFILE);
          } else {
            crmUserContext.setProfileKey(profileKey);
          }
        }
      });
    } catch (Exception e) {
      LOG.error("Error creating profiles", e);
    }
  }

  private void assignLicenseToCrmUsers(List<CrmUserOnboardingContext> crmUserContexts,
      BulkLicenseAssignmentContext context,
      Urn viewer) {
    if (crmUserContexts.isEmpty()) {
      return;
    }
    Map<EnterpriseProfileUrn, CrmUserOnboardingContext> enterpriseProfileUrnsMap = crmUserContexts.stream()
        .filter(CrmUserOnboardingContext::isGoodToAssignLicense)
        .collect(Collectors.toMap(CrmUserOnboardingContext::getEnterpriseProfileUrn, Function.identity()));

    BulkActionBatchResult bulkActionBatchResult = ParseqUtil.await(
        _enterprisePlatformClient.assignLicenses(enterpriseProfileUrnsMap.keySet(), context, viewer), _engine);

    bulkActionBatchResult.getInputKeyResults().forEach(result -> {
      EnterpriseProfileUrn enterpriseProfileUrn;
      try {
        enterpriseProfileUrn = EnterpriseProfileUrn.createFromUrn(result.getInputKey());
      } catch (URISyntaxException e) {
        throw new Error(e); //impossible
      }
      CrmUserOnboardingContext crmUserOnboardingContext = enterpriseProfileUrnsMap.get(enterpriseProfileUrn);
      if (BulkActionBatchInputKeyStatus.FAILED == result.getStatus()) {
        crmUserOnboardingContext.setBulkActionI18nMessage(result.getMessage());
      } else {
        LOG.info("successfully assigned License to enterpriseProfile:{}, applicationInstance:{}, crmUser:{}",
            enterpriseProfileUrn,
            crmUserOnboardingContext.getApplicationInstanceUrn(),
            crmUserOnboardingContext.getUrn());
      }
    });
  }

  private List<BulkActionBatchInputKeyResult> convertToInputKeyResult(
      Collection<CrmUserOnboardingContext> allCrmUserContexts) {
    return allCrmUserContexts.stream()
        .map(crmUserContext -> new BulkActionBatchInputKeyResult()
                .setInputKey(crmUserContext.getUrn())
                .setMessage(crmUserContext.getBulkActionI18nMessage(), SetMode.IGNORE_NULL)
                .setStatus(crmUserContext.hasError()
                    ? BulkActionBatchInputKeyStatus.FAILED : BulkActionBatchInputKeyStatus.SUCCEEDED))
        .collect(Collectors.toList());
  }

  private enum CrmUserOnboardingError {
    //TODO add new error message at ep-bulk-api BulkActionMessage
    BAD_CRM_USER(BulkActionMessage.PLUGIN_CRM_USER_ONBOARDING_CRM_USER_NOT_FOUND),
    CANNOT_FIND_CRM_USER(BulkActionMessage.PLUGIN_CRM_USER_ONBOARDING_CRM_USER_NOT_FOUND),
    CANNOT_FIND_OR_CREATE_EMAIL_ADDRESS_URN(BulkActionMessage.PLUGIN_CRM_USER_ONBOARDING_PROFILE_CREATION_ERROR),
    CANNOT_CREATE_ENTERPRISE_PROFILE(BulkActionMessage.PLUGIN_CRM_USER_ONBOARDING_PROFILE_CREATION_ERROR),
    CANNOT_ASSIGN_LICENSE(BulkActionMessage.PLUGIN_ASSIGN_LICENSE_ERROR);

    private final BulkActionI18nMessage _bulkActionI18nMessage;

    CrmUserOnboardingError(BulkActionMessage messageKey) {
      _bulkActionI18nMessage = new BulkActionI18nMessage().setKey(messageKey.toString());
    }

    BulkActionI18nMessage getBulkActionI18nMessage() {
      return _bulkActionI18nMessage;
    }
  }

  /**
   * the data structure to contain all appended attributes
   */
  private static class CrmUserOnboardingContext {

    // inputs
    private final EnterpriseApplicationInstanceUrn _applicationInstanceUrn;
    private final CrmUserUrn _crmUserUrn;

    // appended fields
    private CrmUser _crmUser;
    private EmailAddressUrn _emailAddressUrn;
    private ProfileKey _profileKey;

    private CrmUserOnboardingError _crmUserOnboardingError;
    private BulkActionI18nMessage _errorMessage;

    CrmUserOnboardingContext(EnterpriseApplicationInstanceUrn applicationInstanceUrn, CrmUserUrn crmUserUrn) {
      _applicationInstanceUrn = applicationInstanceUrn;
      _crmUserUrn = crmUserUrn;
    }

    EnterpriseApplicationInstanceUrn getApplicationInstanceUrn() {
      return _applicationInstanceUrn;
    }

    EnterpriseAccountUrn getEnterpriseAccountUrn() {
      return _applicationInstanceUrn.getAccountEntity();
    }

    CrmUserUrn getUrn() {
      return _crmUserUrn;
    }

    @Nullable
    CrmUser getCrmUser() {
      return _crmUser;
    }

    void setCrmUser(CrmUser cUser) {
      _crmUser = cUser;
    }

    //NPE if _crmUser not exist
    String getEmail() {
      return _crmUser.getEmail();
    }

    //NPE if _crmUser not exist
    String getFirstName() {
      return _crmUser.getFirstName();
    }

    //NPE if _crmUser not exist
    String getLastName() {
      return _crmUser.getLastName();
    }

    boolean isGoodToAppendEmailAddressUrn() {
      return hasNoError() && _crmUser != null;
    }

    EmailAddressUrn getEmailAddressUrn() {
      return _emailAddressUrn;
    }

    void setEmailAddressUrn(EmailAddressUrn emailAddressUrn) {
      _emailAddressUrn = emailAddressUrn;
    }

    boolean isGoodToAppendProfileKey() {
      return isGoodToAppendEmailAddressUrn() && _emailAddressUrn != null && _profileKey == null;
    }

    void setProfileKey(ProfileKey profileKey) {
      _profileKey = profileKey;
    }

    ProfileKey getProfileKey() {
      return _profileKey;
    }

    //NPE if _profileKey not exist
    EnterpriseProfileUrn getEnterpriseProfileUrn() {
      return new EnterpriseProfileUrn(getEnterpriseAccountUrn(), _profileKey.getProfileId());
    }

    boolean isGoodToAssignLicense() {
      return isGoodToAppendEmailAddressUrn() && _emailAddressUrn != null && _profileKey != null;
    }

    void setBulkActionI18nMessage(BulkActionI18nMessage message) {
      _errorMessage = message;
    }

    @Nullable
    BulkActionI18nMessage getBulkActionI18nMessage() {
      if (_errorMessage != null) {
        return _errorMessage;
      }
      return _crmUserOnboardingError == null ? null : _crmUserOnboardingError.getBulkActionI18nMessage();
    }

    void setError(CrmUserOnboardingError error) {
      _crmUserOnboardingError = error;
    }

    boolean hasNoError() {
      return !hasError();
    }

    boolean hasError() {
      return _crmUserOnboardingError != null || _errorMessage != null;
    }


  }
}
