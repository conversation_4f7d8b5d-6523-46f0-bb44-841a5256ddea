package com.linkedin.sales.service;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.lix.dsl.v2.api.impl.LixUserContextImpl;
import com.linkedin.lix.executor.EvaluationContext;
import com.linkedin.lix.executor.IEvaluationContext;
import com.linkedin.lix.mp.client.LixConstants;
import com.linkedin.lix.mp.client.LixExtendedClient;
import com.linkedin.lix.mp.client.LixResult;
import com.linkedin.lix.mp.client.builder.MultiEntityLixEvaluationRequest;
import com.linkedin.lix.mp.client.builder.MultiEntityLixEvaluationRequestBuilder;
import com.linkedin.omni.utils.common.parseq.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.utils.LixUtils;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class LixService {
  private static final Logger LOG = LoggerFactory.getLogger(LixService.class);
  private static final PathSpec[] SALES_SEAT_PATH_SPECS = new PathSpec[]{
      SalesSeat.fields().member(),
      SalesSeat.fields().contract()
  };

  public static final String MP_NAME_PLACEHOLDER = "mpName";
  public static final String MP_NAME_LSS_MT = "lss-mt";

  private final LixExtendedClient _lixExtendedClient;
  private final SalesSeatClient _salesSeatClient;

  public LixService(LixExtendedClient lixExtendedClient, SalesSeatClient salesSeatClient) {
    _lixExtendedClient = lixExtendedClient;
    _salesSeatClient = salesSeatClient;
  }

  /**
   * Perform member-based lix check for a seat
   */
  public Task<Boolean> isMemberBasedLixEnabledForSeat(@NonNull SeatUrn seatUrn, @NonNull String lixKey) {
    return getMemberBasedLixValueForSeat(seatUrn, lixKey).map(
        treatment -> !StringUtils.equalsIgnoreCase(LixConstants.FALLTHROUGH_TREATMENT, treatment));
  }

  /**
   * fetch member-based lix value for a seat.
   */
  public Task<String> getMemberBasedLixValueForSeat(@NonNull SeatUrn seatUrn, @NonNull String lixKey) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "getMemberBasedLixValueForSeat");
    return _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), null, viewer, SALES_SEAT_PATH_SPECS)
        .map(seat -> Optional.ofNullable(seat.getMember()))
        .flatMap(optMember -> optMember.map(member -> getLixTreatment(member, lixKey, null))
            .orElseGet(() -> Task.value(LixConstants.FALLTHROUGH_TREATMENT)))
        .recover(e -> {
          // a failure likely means we could not look up seat, which means recovering here will be of limited use; still,
          // since this is a lix method, we should recover.
          LOG.warn("Cannot perform {} LIX lookup for Seat urn {}, returning control for lix", lixKey, seatUrn, e);
          return LixConstants.FALLTHROUGH_TREATMENT;
        });
  }

  /**
   * fetch member-based and contract-based EEP lix value for a seat.
   */
  public Task<Boolean> isEEPLixEnabledForSeat(@NonNull SeatUrn seatUrn, @NonNull String lixKey) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "isEEPLixEnabledForSeat");
    return _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), null, viewer, SALES_SEAT_PATH_SPECS)
        .flatMap(seat -> {
          List<Urn> urnList = new ArrayList<>();
          urnList.add(seatUrn);
          if (seat.hasMember()) {
           urnList.add(seat.getMember());
          }
          if (seat.hasContract()) {
            urnList.add(seat.getContract());
          }
          return isEEPLixEnabled(lixKey, urnList);
        });
  }

  /**
   * batch get member-based and contract-based EEP lix values for a seat.
   */
  public Task<Map<String, Boolean>> batchGetIsEEPLixEnabledForSeat(@NonNull SeatUrn seatUrn, Set<String> lixKeys) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(), "isEEPLixEnabledForSeat");
    return _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), null, viewer, SALES_SEAT_PATH_SPECS).flatMap(seat -> {
      List<Urn> urnList = new ArrayList<>();
      urnList.add(seatUrn);
      if (seat.hasMember()) {
        urnList.add(seat.getMember());
      }
      if (seat.hasContract()) {
        urnList.add(seat.getContract());
      }
      return ParseqUtils.batchTask(lixKeys, lixKey -> isEEPLixEnabled(lixKey, urnList));
    });
  }

  /**
   * Check if the lix has enabled for the given contract
   * @param contractUrn {@link ContractUrn} to check
   * @param lixKey lix key to check
   * @return A task of boolean flag to indicate if the lix is enabled for the contract
   */
  public Task<Boolean> isContractBasedLixEnabled(@NonNull ContractUrn contractUrn, @NonNull String lixKey) {
    EvaluationContext context = new EvaluationContext();
    context.put(LixUtils.CONTRACT_ID_NUM_PROPERTY, contractUrn.getIdAsInt());
    return isLixEnabled(contractUrn, lixKey, context);
  }

  /**
   * Check if the EEP (Enterprise Experimentation Platform) lix is enabled for the given contract.
   * @param contractUrn {@link ContractUrn} to check
   * @param lixKey lix key to check
   * @return A task of boolean flag to indicate if the lix is enabled for the contract
   */
  public Task<Boolean> isContractBasedEEPLixEnabled(@NonNull ContractUrn contractUrn, @NonNull String lixKey) {
    return isEEPLixEnabled(lixKey, ImmutableList.of(contractUrn));
  }

  /**
   * Check if the EEP (Enterprise Experimentation Platform) lix is enabled for the given contract and analysis urn.
   * @param contractUrn {@link ContractUrn} to check
   * @param lixKey lix key to check
   * @param analysisUrn {@link Urn} to check for analysis
   * @return A task of boolean flag to indicate if the lix is enabled for the contract
   */
  public Task<Boolean> isContractBasedEEPLixEnabled(@NonNull ContractUrn contractUrn, @NonNull String lixKey, @NonNull Urn analysisUrn) {
    return isEEPLixEnabled(lixKey, ImmutableList.of(contractUrn, analysisUrn));
  }

  /**
   * Check if the lix has enabled for the given contract or an admin seat
   * @param contractUrn {@link ContractUrn} to check
   * @param seatUrn {@link SeatUrn} to check
   * @param lixKey lix key to check
   * @return A task of boolean flag to indicate if the lix is enabled for the contract or admin
   */
  public Task<Boolean> isContractBasedLixWithAdminEnabled(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn, @NonNull String lixKey) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "isContractBasedLixWithAdminEnabled");
    return _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), contractUrn, viewer, SalesSeat.fields().roles())
        .flatMap(seat -> {
          EvaluationContext context = new EvaluationContext();
          context.put(LixUtils.CONTRACT_ID_NUM_PROPERTY, contractUrn.getIdAsInt());
          context.put(LixUtils.IS_ADMIN_BOOLEAN_PROPERTY, isAdmin(seat.getRoles()));
          return isLixEnabled(contractUrn, lixKey, context);
        });

  }

  private static boolean isAdmin(com.linkedin.sales.admin.SeatRoleArray seatRoles) {
    return seatRoles.contains(com.linkedin.sales.admin.SeatRole.LSS_ADMIN_SEAT);
  }

  /**
   * Check if the lix has enabled for the given crm instance
   * @param crmInstanceUrn {@link CrmInstanceUrn} to check
   * @param lixKey lix key to check
   * @return A task of boolean flag to indicate if the lix is enabled for the crm instance
   */
  public Task<Boolean> isCrmInstanceBasedLixEnabled(@NonNull CrmInstanceUrn crmInstanceUrn, @NonNull String lixKey) {
    EvaluationContext context = new EvaluationContext();
    context.put(LixUtils.CRM_INSTANCE_ID_STRING_PROPERTY, CrmUrnUtils.getInstanceId(crmInstanceUrn));
    return isLixEnabled(crmInstanceUrn, lixKey, context);
  }

  /**
   * Check if the lix has enabled for the given EP App Instance
   * @param appInstanceUrn {@link EnterpriseApplicationInstanceUrn} to check
   * @param lixKey lix key to check
   * @return A task of boolean flag to indicate if the lix is enabled for the EP App Instance
   */
  public Task<Boolean> isEpAppInstanceBasedLixEnabled(@NonNull EnterpriseApplicationInstanceUrn appInstanceUrn,
      @NonNull String lixKey) {
    EvaluationContext context = new EvaluationContext();
    context.put(LixUtils.ENTERPRISE_APPLICATION_INSTANCE, appInstanceUrn.getApplicationInstanceIdEntity());
    return isLixEnabled(appInstanceUrn, lixKey, context);
  }

  public Task<Integer> getEntityBatchCreateConcurrencyLevel(SeatUrn seat, String lixKey) {
    return getLixTreatment(seat, lixKey, null)
        .map(lixTreatment -> {
          int concurrencyLevel = LixUtils.DEFAULT_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL;
          if (!LixConstants.FALLTHROUGH_TREATMENT.equals(lixTreatment)) {
            try {
              // Valid lix treatment is in the format of "concurrency_level_<value>", so getting the value from 18th character.
              concurrencyLevel = Integer.parseInt(lixTreatment.substring(18));
            } catch (Exception e) {
              LOG.warn("Invalid lix value for {}", LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL, e);
            }
          }
          return concurrencyLevel;
        });
  }

  public Task<String> getLixTreatment(@NonNull Urn urn, @NonNull String lixKey, @Nullable IEvaluationContext context) {
    return Task.callable(() -> _lixExtendedClient.getTreatment(urn, lixKey, context)).recover(t -> {
      LOG.warn("Unable to check lix value for {} with urn {}, fallback to control", lixKey, urn, t);
      return LixConstants.FALLTHROUGH_TREATMENT;
    });
  }

  private Task<String> getEEPLixTreatment(@NonNull MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest) {
    return _lixExtendedClient.getTreatmentAsync(multiEntityLixEvaluationRequest)
        .map(LixResult::getTreatment)
        .recover(t -> {
          LOG.warn("Unable to check lix value for {} fallback to control", multiEntityLixEvaluationRequest, t);
          return LixConstants.FALLTHROUGH_TREATMENT;
        });
  }

  public Task<Boolean> isLixEnabled(@NonNull Urn urn, @NonNull String lixKey, @Nullable IEvaluationContext context) {
    return getLixTreatment(urn, lixKey, context).map(treatment -> !StringUtils.equalsIgnoreCase(LixConstants.FALLTHROUGH_TREATMENT, treatment));
  }

  public Task<Boolean> isEEPLixEnabled(@NonNull String lixKey, @NonNull List<Urn> urns) {
    MultiEntityLixEvaluationRequestBuilder multiEntityLixEvaluationRequestBuilder = new MultiEntityLixEvaluationRequestBuilder()
        .setTestkey(lixKey)
        .setLixUserContext(new LixUserContextImpl().put(MP_NAME_PLACEHOLDER, MP_NAME_LSS_MT));

    urns.forEach(urn -> multiEntityLixEvaluationRequestBuilder.addNamedUrn(urn.getEntityType(), urn));

    return getEEPLixTreatment(multiEntityLixEvaluationRequestBuilder.build())
        .map(treatment -> !StringUtils.equalsIgnoreCase(LixConstants.FALLTHROUGH_TREATMENT, treatment));
  }
}