package com.linkedin.sales.service;

import com.google.common.collect.ImmutableMap;
import com.linkedin.i18n.resource.DynamicResourceBundleManager;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.Optional;
import java.util.ResourceBundle;
import javax.annotation.Nonnull;


/**
 * LocalizationHelper is to provide i18n support for all use cases in lss-mt.
 */
public class LocalizationHelper {
  private static final Locale DEFAULT_LOCALE = Locale.US;
  private static final String RESOURCE_BUNDLE = "com.linkedin.sales.lss-mt";
  public static final String I18N_USER_KEY = "user";
  public static final String I18N_GIVEN_NAME_KEY = "givenName";
  public static final String I18N_FAMILY_NAME_KEY = "familyName";
  public static final String I18N_COMPANY_KEY = "company";
  public static final String I18N_COMMENT_KEY = "comment";

  private static final String KEY_CRM_LIST_LEAD_LIST_NAME = "com.linkedin.sales.crmlist.lead_list_name";
  private static final String KEY_CRM_LIST_LEAD_LIST_PERSON_ACCOUNT_NAME  = "com.linkedin.sales.crmlist.lead_list_person_account_name";
  private static final String KEY_CRM_LIST_ACCOUNT_LIST_NAME = "com.linkedin.sales.crmlist.accont_list_name";
  private static final String KEY_CRM_LIST_LEAD_LIST_DESCRIPTION = "com.linkedin.sales.crmlist.lead_list_description";
  private static final String KEY_CRM_LIST_LEAD_LIST_PERSON_ACCOUNT_DESCRIPTION = "com.linkedin.sales.crmlist.lead_list_person_account_description";
  private static final String KEY_CRM_LIST_ACCOUNT_LIST_DESCRIPTION_SEAT_SETTING = "com.linkedin.sales.crmlist.account_list_description_seat_setting";
  private static final String KEY_BLUEBIRD_LIST_NAME = "com.linkedin.sales.bluebirdlist.list_name";
  private static final String KEY_BLUEBIRD_LIST_DESCRIPTION = "com.linkedin.sales.bluebirdlist.list_description";
  private static final String KEY_BUYER_INTEREST_LIST_NAME = "com.linkedin.sales.buyerinterestlist.list_name";
  private static final String KEY_BUYER_INTEREST_LIST_DESCRIPTION = "com.linkedin.sales.buyerinterestlist.list_description";
  private static final String KEY_AT_RISK_LIST_NAME = "com.linkedin.sales.atriskopportunitylist.list_name";
  private static final String KEY_AT_RISK_LIST_DESCRIPTION = "com.linkedin.sales.atriskopportunitylist.list_description";
  private static final String KEY_RECOMMENDATION_LIST_LEAD_LIST_NAME = "com.linkedin.sales.recommendationlist.lead.list_name";
  private static final String KEY_RECOMMENDATION_LIST_LEAD_LIST_DESCRIPTION = "com.linkedin.sales.recommendationlist.lead.list_description";
  private static final String KEY_NEW_EXECS_IN_SAVED_ACCOUNTS_LIST_NAME = "com.linkedin.sales.newexecsinsavedaccounts.list_name";
  private static final String KEY_NEW_EXECS_IN_SAVED_ACCOUNTS_LIST_DESCRIPTION = "com.linkedin.sales.newexecsinsavedaccounts.list_description";
  private static final String KEY_LEADS_TO_FOLLOW_UP_LIST_NAME = "com.linkedin.sales.leadstofollowup.list_name";
  private static final String KEY_LEADS_TO_FOLLOW_UP_LIST_DESCRIPTION = "com.linkedin.sales.leadstofollowup.list_description";
  private static final String KEY_AUTO_PROSPECTOR_REC_LIST_NAME = "com.linkedin.sales.autoprospectorrec.list_name";
  private static final String KEY_AUTO_PROSPECTOR_REC_LIST_DESCRIPTION = "com.linkedin.sales.autoprospectorrec.list_description";
  private static final String KEY_MY_CURRENT_ACCOUNTS_LIST_NAME = "com.linkedin.sales.mycurrentaccountslist.list_name";
  private static final String KEY_MY_CURRENT_ACCOUNTS_LIST_DESCRIPTION = "com.linkedin.sales.mycurrentaccountslist.list_description";
  public static final String KEY_FS_HEAD_SHARED_UPDATE_HEADERIMAGE_A11Y_TEXT_V2 =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headerimage_a11y_text_v2";
  public static final String KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_V2_A11Y_TEXT =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_v2_a11y_text";
  public static final String KEY_FS_LEAD_SHARED_UPDATE_CARD_ACTION_A11Y_TEXT =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_card_action_a11y_text";
  public static final String KEY_FS_LEAD_SHARED_UPDATE_BRANDING_INSIGHT_TEXT =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_branding_insight_text";
  public static final String KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_FOR_SAVED_LEAD =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_for_saved_lead";
  public static final String KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_FOR_POTENTIAL_LEAD =
      "com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_for_potential_lead";

  private final DynamicResourceBundleManager _dynamicResourceBundleManager;

  public LocalizationHelper(DynamicResourceBundleManager dynamicResourceBundleManager) {
    _dynamicResourceBundleManager = dynamicResourceBundleManager;
  }

  /**
   * Helper method for fetching the first name and optional last name of a given profile.
   *
   * @param firstName localized first name
   * @param lastName localized last name
   * @return map containing first name and last name
   */
  @Nonnull
  public static Map<String, String> getNameMap(
      @Nonnull String firstName,
      @Nullable String lastName
  ) {
    if (lastName == null) {
      return new ImmutableMap.Builder<String, String>()
          .put(I18N_GIVEN_NAME_KEY, firstName)
          .build();
    }
    return new ImmutableMap.Builder<String, String>()
        .put(I18N_GIVEN_NAME_KEY, firstName)
        .put(I18N_FAMILY_NAME_KEY, lastName)
        .build();
  }

  /**
   * Get the localized list name. Only applies to autogen lists and BOOK_OF_BUSINESS list.
   */
  @Nullable
  public String getLocalizedListName(@NonNull ListSource listSource, @NonNull ListType listType, @Nullable Locale locale) {
    ResourceBundle resourceBundle = getResourceBundle(RESOURCE_BUNDLE, Optional.ofNullable(locale).orElse(DEFAULT_LOCALE));

    switch (listSource) {
      case CRM_BLUEBIRD:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_BLUEBIRD_LIST_NAME)
            : null;
      case CRM_SYNC:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_CRM_LIST_LEAD_LIST_NAME)
            : resourceBundle.getString(KEY_CRM_LIST_ACCOUNT_LIST_NAME);
      case CRM_PERSON_ACCOUNT:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_CRM_LIST_LEAD_LIST_PERSON_ACCOUNT_NAME)
            : null;
      case BUYER_INTEREST:
        return listType == ListType.ACCOUNT
            ? resourceBundle.getString(KEY_BUYER_INTEREST_LIST_NAME)
            : null;
      case CRM_AT_RISK_OPPORTUNITY:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_AT_RISK_LIST_NAME)
            : null;
      case RECOMMENDATION:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_RECOMMENDATION_LIST_LEAD_LIST_NAME)
            : null;
      case LEADS_TO_FOLLOW_UP:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_LEADS_TO_FOLLOW_UP_LIST_NAME)
            : null;
      case NEW_EXECS_IN_SAVED_ACCOUNTS:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_NEW_EXECS_IN_SAVED_ACCOUNTS_LIST_NAME)
            : null;
      case BOOK_OF_BUSINESS:
        return listType == ListType.ACCOUNT
            ? resourceBundle.getString(KEY_MY_CURRENT_ACCOUNTS_LIST_NAME)
            : null;
      case AUTO_PROSPECTOR_REC:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_AUTO_PROSPECTOR_REC_LIST_NAME)
            : null;
      default: // Unsupported list source
        return null;
    }
  }

  /**
   * Get the localized list description. Only applies to autogen lists and BOOK_OF_BUSINESS list.
   */
  @Nullable
  public String getLocalizedListDescription(@NonNull ListSource listSource, @NonNull ListType listType, @Nullable Locale locale) {
    ResourceBundle resourceBundle = getResourceBundle(RESOURCE_BUNDLE, Optional.ofNullable(locale).orElse(DEFAULT_LOCALE));

    switch (listSource) {
      case CRM_BLUEBIRD:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_BLUEBIRD_LIST_DESCRIPTION)
            : null;
      case CRM_SYNC:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_CRM_LIST_LEAD_LIST_DESCRIPTION)
            : resourceBundle.getString(KEY_CRM_LIST_ACCOUNT_LIST_DESCRIPTION_SEAT_SETTING);
      case CRM_PERSON_ACCOUNT:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_CRM_LIST_LEAD_LIST_PERSON_ACCOUNT_DESCRIPTION)
            : null;
      case BUYER_INTEREST:
        return listType == ListType.ACCOUNT
            ? resourceBundle.getString(KEY_BUYER_INTEREST_LIST_DESCRIPTION)
            : null;
      case CRM_AT_RISK_OPPORTUNITY:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_AT_RISK_LIST_DESCRIPTION)
            : null;
      case RECOMMENDATION:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_RECOMMENDATION_LIST_LEAD_LIST_DESCRIPTION)
            : null;
      case LEADS_TO_FOLLOW_UP:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_LEADS_TO_FOLLOW_UP_LIST_DESCRIPTION)
            : null;
      case NEW_EXECS_IN_SAVED_ACCOUNTS:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_NEW_EXECS_IN_SAVED_ACCOUNTS_LIST_DESCRIPTION)
            : null;
      case BOOK_OF_BUSINESS:
        return listType == ListType.ACCOUNT
            ? resourceBundle.getString(KEY_MY_CURRENT_ACCOUNTS_LIST_DESCRIPTION)
            : null;
      case AUTO_PROSPECTOR_REC:
        return listType == ListType.LEAD
            ? resourceBundle.getString(KEY_AUTO_PROSPECTOR_REC_LIST_DESCRIPTION)
            : null;
      default: // Unsupported list source
        return null;
    }
  }

  @NonNull
  private ResourceBundle getResourceBundle(@NonNull String bundleName, @NonNull Locale locale) {
    ResourceBundle resourceBundle;
    try {
      resourceBundle = _dynamicResourceBundleManager.getBundle(bundleName, locale);
    } catch (MissingResourceException ex) {
      // if the translations are not available for whatever reason, fall back to the US default
      resourceBundle =
          _dynamicResourceBundleManager.getBundle(bundleName, DEFAULT_LOCALE);
    }
    return resourceBundle;
  }
}
