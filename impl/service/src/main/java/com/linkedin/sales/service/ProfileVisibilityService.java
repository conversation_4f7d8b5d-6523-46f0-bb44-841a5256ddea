package com.linkedin.sales.service;

import com.linkedin.badge.internal.BadgeType;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.messaging.GraphDistancesClient;
import com.linkedin.sales.client.messaging.MemberBadgesClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


public class ProfileVisibilityService {
  private static final Logger LOG = LoggerFactory.getLogger(ProfileVisibilityService.class);

  private final GraphDistancesClient _graphDistancesClient;
  private final MemberBadgesClient _memberBadgesClient;

  public ProfileVisibilityService(GraphDistancesClient graphDistancesClient, MemberBadgesClient memberBadgesClient) {
    _graphDistancesClient = graphDistancesClient;
    _memberBadgesClient = memberBadgesClient;
  }

  public Task<Boolean> isProfileVisible(MemberUrn viewer, MemberUrn viewee) {
    return Task.par(isProfileInNetwork(viewer, viewee), isOpenLink(viewee))
        .map(
            (isProfileInNetwork, isOpenLink) -> {
              LOG.info("isProfileVisible viewer: {}, viewee: {}, isProfileInNetwork: {}, isOpenLink: {}",
                  viewer, viewee, isProfileInNetwork, isOpenLink); //TODO lower log level
              return isOpenLink || isProfileInNetwork;
        });

  }

  public Task<Boolean> isProfileInNetwork(MemberUrn viewer, MemberUrn viewee) {
    return _graphDistancesClient.getGraphDistance(viewer, viewee)
        .map(
            distance -> {
              LOG.info("isProfileInNetwork viewer: {}, viewee: {}, distance: {}", viewer, viewee, distance); //TODO lower log level
              return distance >= 0 && distance <= 3; // see DISTANCE_ENUM_INTEGER_MAP in GraphDistancesClient
            })
        .recoverWith(
            e -> {
              LOG.error("Couldn't get distance from viewer: {} to viewee: {}", viewer, viewee, e);
              return Task.value(FALSE);
            });
  }

  public Task<Boolean> isOpenLink(MemberUrn viewee) {
    return _memberBadgesClient.getMemberBadges(viewee.getMemberIdEntity())
        .map(memberBadges -> memberBadges.hasBadges() ? memberBadges.getBadges().contains(BadgeType.OPENLINK) : false)
        .recoverWith(
            e -> {
              LOG.error("Couldn't get badges for viewee: {}", viewee, e);
              return Task.value(FALSE);
            });
  }

}
