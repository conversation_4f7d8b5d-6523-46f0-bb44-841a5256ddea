package com.linkedin.sales.service;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.linkedin.analytics.QueryResponse;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.RestLiResponseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.sales.monitoring.CounterMetricsSensor;
import com.linkedin.sales.service.utils.PinotUtils;
import com.linkedin.salesactivities.SalesActivityTotal;
import com.linkedin.salesactivities.SalesActivityType;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.EnumUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xeril.util.Utils;


/**
 * Provide methods needed by SalesActivityTotalsResource
 */
public class SalesActivityTotalsJobService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesActivityTotalsJobService.class);
  private static final String SALES_ACTIVITY_PINOT_QUERY_RESOURCE = "salesActivityPinotQuery";

  private static final String YEAR_MONTH_DATE_FORMAT = "yyyyMM";
  private static final DateTimeFormatter YEAR_MONTH_DATE_FORMATTER =
      DateTimeFormatter.ofPattern(YEAR_MONTH_DATE_FORMAT, Locale.ENGLISH);

  private static final String DAY_QUERY_STRING_SQL = "SELECT SUM(count), activityType FROM salesActivityDayTotals WHERE %s "
      + "AND yearMonth >= %s GROUP BY activityType LIMIT 100";
  private static final String MONTH_QUERY_STRING_SQL = "SELECT SUM(count), activityType FROM salesActivityMonthTotals WHERE %s "
      + "AND yearMonth < %s GROUP BY activityType LIMIT 100";

  private static final String CONTRACT_ID_KEY = "contractId";
  private static final String SEAT_ID_KEY = "seatId";
  private static final String MEMBER_ID_KEY = "memberId";

  private final PinotUtils _pinotUtils;
  private final CounterMetricsSensor _counterMetricsSensor;

  public SalesActivityTotalsJobService(PinotUtils pinotUtils, CounterMetricsSensor counterMetricsSensor) {
    _pinotUtils = pinotUtils;
    _counterMetricsSensor = counterMetricsSensor;
  }

  /**
   * Retrieve a list of activity type and counts for a given seat holder
   *
   * @param seatUrn Seat URN of the user
   * @param contractUrn Contract URN of the user
   * @return A list of user's activity total counts
   */
  public Task<BasicCollectionResult<SalesActivityTotal>> getSalesActivityTotals(@NonNull SeatUrn seatUrn,
      @NonNull ContractUrn contractUrn) {

    final Long contractId = contractUrn.getContractIdEntity();
    final Long seatId = seatUrn.getSeatIdEntity();

    Map<String, Long> queryParamMap = ImmutableMap.of(SEAT_ID_KEY, seatId, CONTRACT_ID_KEY, contractId);

    // Query the current and previous months' activities from day tables and < previous months' activities from the
    // month table. The motivation for this is because the month activities get populated on the first of the
    // month and would not contain the previous months' activities in the month table.
    String formattedDayQuery = buildPinotQueryString(queryParamMap, DAY_QUERY_STRING_SQL);
    String formattedMonthQuery = buildPinotQueryString(queryParamMap, MONTH_QUERY_STRING_SQL);

    Task<Map<SalesActivityType, Long>> salesActivityDayTotals =
        getActivityCountMap(formattedDayQuery, "salesActivityDayTotals", null, seatId, contractId);
    Task<Map<SalesActivityType, Long>> salesActivityMonthTotals =
        getActivityCountMap(formattedMonthQuery, "salesActivityMonthTotals", null, seatId, contractId);
    return constructSalesActivityTotals(salesActivityDayTotals, salesActivityMonthTotals);
  }

  /**
   * Retrieve a list of activity type and counts for a given member
   *
   * @param memberUrn Member URN of the user
   * @return A list of user's activity total counts
   */
  public Task<BasicCollectionResult<SalesActivityTotal>> getSalesActivityTotals(@NonNull MemberUrn memberUrn) {
    final Long memberId = memberUrn.getMemberIdEntity();
    Map<String, Long> queryParamMap = Collections.singletonMap(MEMBER_ID_KEY, memberId);

    // Query the current and previous months' activities from day tables and < previous months' activities from the
    // month table. The motivation for this is because the month activities get populated on the first of the
    // month and would not contain the previous months' activities in the month table.
    String formattedDayQuery = buildPinotQueryString(queryParamMap, DAY_QUERY_STRING_SQL);
    String formattedMonthQuery = buildPinotQueryString(queryParamMap, MONTH_QUERY_STRING_SQL);

    Task<Map<SalesActivityType, Long>> salesActivityDayTotals =
        getActivityCountMap(formattedDayQuery, "salesActivityDayTotals", memberId, null, null);
    Task<Map<SalesActivityType, Long>> salesActivityMonthTotals =
        getActivityCountMap(formattedMonthQuery, "salesActivityMonthTotals", memberId, null, null);

    return constructSalesActivityTotals(salesActivityDayTotals, salesActivityMonthTotals);
  }

  /**
   * Retrieve a list of activity type and counts for a given contract
   *
   * @param contractUrn Contract URN
   * @return A list of user's activity total counts
   */
  public Task<BasicCollectionResult<SalesActivityTotal>> getSalesActivityTotals(@NonNull ContractUrn contractUrn) {
    final Long contractId = contractUrn.getContractIdEntity();
    Map<String, Long> queryParamMap = Collections.singletonMap(CONTRACT_ID_KEY, contractId);

    // Query the current and previous months' activities from day tables and < previous months' activities from the
    // month table. The motivation for this is because the month activities get populated on the first of the
    // month and would not contain the previous months' activities in the month table.
    String formattedDayQuery = buildPinotQueryString(queryParamMap, DAY_QUERY_STRING_SQL);
    String formattedMonthQuery = buildPinotQueryString(queryParamMap, MONTH_QUERY_STRING_SQL);

    Task<Map<SalesActivityType, Long>> salesActivityDayTotals =
        getActivityCountMap(formattedDayQuery, "salesActivityDayTotals", null, null, contractId);
    Task<Map<SalesActivityType, Long>> salesActivityMonthTotals =
        getActivityCountMap(formattedMonthQuery, "salesActivityMonthTotals", null, null, contractId);

    return constructSalesActivityTotals(salesActivityDayTotals, salesActivityMonthTotals);
  }

  /**
   * Construct the sales activity totals list from the data queried from pinot tables
   *
   * @param salesActivityDayTotals activity types and counts from salesActivityDayTotals table
   * @param salesActivityMonthTotals activity types and counts from salesActivityMonthTotals table
   * @return A list of user's activity total counts
   */
  private Task<BasicCollectionResult<SalesActivityTotal>> constructSalesActivityTotals(
      Task<Map<SalesActivityType, Long>> salesActivityDayTotals,
      Task<Map<SalesActivityType, Long>> salesActivityMonthTotals) {
    return Task.par(salesActivityDayTotals, salesActivityMonthTotals).map((dayTotals, monthTotals) -> {
      List<SalesActivityTotal> result = Arrays.stream(SalesActivityType.values())
          .filter(salesActivityType -> salesActivityType != SalesActivityType.$UNKNOWN)
          .map(salesActivityType -> {
            SalesActivityTotal salesActivityTotal = new SalesActivityTotal();
            salesActivityTotal.setActivityType(salesActivityType);
            //we sum daily and monthly counts because day table only stores user's activity counts for last 60 days
            //month table stores aggregate monthly counts up to last 3 years. The Pinot queries ensure no double-counting
            salesActivityTotal.setCount(
                dayTotals.getOrDefault(salesActivityType, 0L) + monthTotals.getOrDefault(salesActivityType, 0L));
            return salesActivityTotal;
          })
          .collect(Collectors.toList());
      return new BasicCollectionResult<>(result);
    });
  }

  /**
   * Execute the given Pinot query to fetch the activity type and counts for the given user
   *
   * @param salesActivityQuery Pinot query to fetch activity data
   * @param tableTitle Name of the table being queried
   * @param memberId users memberId
   * @param seatId users seatId
   * @param contractId users contractId
   * @return A map of user's activity types to counts
   */
  private Task<Map<SalesActivityType, Long>> getActivityCountMap(@NonNull String salesActivityQuery, @NonNull String tableTitle,
      @Nullable Long memberId, @Nullable Long seatId, @Nullable Long contractId) {
    return _pinotUtils.runPinotQuery(SALES_ACTIVITY_PINOT_QUERY_RESOURCE, salesActivityQuery)
        .map(this::retrieveCompletionStatusFromQueryResponse)
        .onFailure(e -> {
          if (e instanceof RestLiResponseException
              && ((RestLiResponseException) e).getStatus() == HttpStatus.S_429_TOO_MANY_REQUESTS.getCode()) {
            LOG.warn("Failed to fetch {} because QPS quota exceeded for pinot table", tableTitle, e);
            // log the failure in InGraph
            _counterMetricsSensor.incrementQPSExceededForPinotTableCallCounter();
          } else {
            LOG.error("Pinot query to {} failed for memberId {}, seatId {}, and contractId {} "
                + "because {}", tableTitle, memberId, seatId, contractId, e);
          }
        });
  }

  /**
   * Retrieve a map of activity type to activity completion counts by unwrapping query response
   *
   * @param queryResponse QueryResponse for salesActivityTotal
   * @return The processed map of activity type to count
   */
  @VisibleForTesting
  Map<SalesActivityType, Long> retrieveCompletionStatusFromQueryResponse(@NonNull QueryResponse queryResponse) {
    Preconditions.checkNotNull(queryResponse, "Argument queryResponse cannot be null");
    if (Utils.isEmptyCollection(queryResponse.getRows(GetMode.NULL))) {
      return Collections.emptyMap();
    }

    return queryResponse.getRows().stream()
        .filter(row -> row != null && row.getValue().size() == 2)
        // there will be SNAP activities in Pinot table as well, we need to filter those
        .filter(row -> EnumUtils.isValidEnum(SalesActivityType.class, row.getValue().get(1).getString()))
        .collect(Collectors.toMap(row -> SalesActivityType.valueOf(row.getValue().get(1).getString()),
            row -> row.getValue().get(0).getDouble().longValue()));
  }

  /**
   * Builds the Pinot query string for either salesActivityDayTotals or salesActivityMonthTotals table
   *
   * @param queryParamMap A map of the query arguments and values
   * @param formattedQueryString A string used to make the Pinot query
   * @return The query string
   */
  private String buildPinotQueryString(@NonNull Map<String, Long> queryParamMap, @NonNull String formattedQueryString) {
    if (queryParamMap.isEmpty()) {
      throw new RuntimeException("Query parameter map contains no parameters");
    }

    Period oneMonth = Period.ofMonths(1);
    LocalDateTime lastMonthLocalDateTime = LocalDateTime.now().minus(oneMonth);
    String lastMonthYearMonth = YEAR_MONTH_DATE_FORMATTER.format(lastMonthLocalDateTime);

    StringBuilder sBuilder = new StringBuilder();
    for (Map.Entry<String, Long> queryParamEntry : queryParamMap.entrySet()) {
      appendQueryParameters(sBuilder, queryParamEntry);
    }

    return String.format(formattedQueryString, sBuilder, lastMonthYearMonth);
  }

  /**
   * Appends query parameters to the query string
   *
   * @param sBuilder A StringBuilder object used to build the query string
   * @param queryParamEntry A K,V pair where the key is the column name and the V is the value to query
   */
  private void appendQueryParameters(StringBuilder sBuilder, Map.Entry<String, Long> queryParamEntry) {
    if (sBuilder.length() > 0) {
      sBuilder.append(" AND ");
    }
    sBuilder.append(queryParamEntry.getKey());
    sBuilder.append(" = ");
    sBuilder.append(queryParamEntry.getValue());
  }
}
