package com.linkedin.sales.service;

import com.linkedin.common.ClosedTimeRange;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.utils.PinotUtils;
import com.linkedin.sales.service.utils.SalesAnalyticsPinotUtils;


/**
 * Provide methods needed by SalesAnalyticsExportJobResource
 */
public class SalesAnalyticsExportJobService {

  private final SalesAnalyticsPinotUtils _salesAnalyticsPinotUtils;

  public SalesAnalyticsExportJobService(PinotUtils pinotUtils) {
    _salesAnalyticsPinotUtils = new SalesAnalyticsPinotUtils(pinotUtils);
  }

  /**
   * Retrieve data availability for a contract from a endpoint
   * @param table The name of the table serving data for the endpoint
   * @param contractUrn The contract urn of the user requesting data
   * @return An instance of SalesAnalyticsDataAvailability with data availability info
   */
  public Task<ClosedTimeRange> retrieveDataAvailability(String table, ContractUrn contractUrn) {
    Task<Long> earliestDataAvailability = _salesAnalyticsPinotUtils.retrieveEarliestDataAvailability(table, contractUrn);
    Task<Long> latestDataAvailability = _salesAnalyticsPinotUtils.retrieveLatestDataAvailability(table, contractUrn);
    return Task.par(earliestDataAvailability, latestDataAvailability).map((earliest, latest) ->
        new ClosedTimeRange().setStart(earliest).setEnd(latest));
  }
}
