package com.linkedin.sales.service;

import com.linkedin.common.ClosedTimeRange;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.lighthouse.client.queue.QueueType;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.analytics.SalesAnalyticsExportJobsApiClient;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.SalesAnalyticsConstants;
import com.linkedin.salesanalytics.SalesAnalyticsExportJob;
import com.linkedin.salesanalytics.SalesAnalyticsStatus;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJobStatus;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJobType;
import scala.Enumeration;


/**
 * Service class for SalesAnalyticsExportJobResource
 */
// Suppressing warning for grpc generated proto classes
@SuppressFBWarnings("SSCU_SUSPICIOUS_SHADED_CLASS_USE")
public class SalesAnalyticsExportJobServiceV2 {
  private final LixService _lixService;
  private final SalesAnalyticsExportJobsApiClient _salesAnalyticsExportJobsApiClient;
  private final SalesAnalyticsExportJobService _salesAnalyticsExportJobService;

  private static final Logger LOG = LoggerFactory.getLogger(SalesAnalyticsExportJobServiceV2.class);

  public SalesAnalyticsExportJobServiceV2(LixService lixService,
      SalesAnalyticsExportJobsApiClient salesAnalyticsExportJobsApiClient,
      SalesAnalyticsExportJobService salesAnalyticsExportJobService) {
    _lixService = lixService;
    _salesAnalyticsExportJobsApiClient = salesAnalyticsExportJobsApiClient;
    _salesAnalyticsExportJobService = salesAnalyticsExportJobService;
  }

  /**
   * Create SalesAnalyticsExportJob and return the jobId
   * @param dataType The type of data user is requesting, e.g., ACTIVITY, ACTIVITY_OUTCOME, etc
   * @param startAt The start date of the time range of the exported data
   * @param endAt The end data of the time range of the exported data
   * @param contractUrn The contract urn of the user requesting data
   * @param memberUrn The member urn of the user requesting data
   * @return Job id of the export job
   */
  public Task<Long> createSalesAnalyticsExportJob(Enumeration.Value dataType, Long startAt, Long endAt,
      ContractUrn contractUrn, MemberUrn memberUrn) {
    try {
      return _salesAnalyticsExportJobsApiClient.submitJobRequest(convertQueueTypeToJobType(dataType), contractUrn, memberUrn, startAt, endAt).map(
          salesAnalyticsExportJob -> salesAnalyticsExportJob.getId());
    } catch (Exception e) {
      LOG.error(
          "Encountered exception in createSalesAnalyticsExportJob for dataType - {}, contractUrn - {}. memberUrn - {}, startAt - {}, endAt - {} :: ",
          dataType, contractUrn, memberUrn, startAt, endAt, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * Get an instance of SalesAnalyticsExportJob based on the jobId with latest status
   * @param memberUrn The member urn of the user requesting data
   * @param contractUrn The contract urn of the user requesting data
   * @param jobId Job id of the export job
   * @return SalesAnalyticsExportJob containing the latest status
   */
  public Task<SalesAnalyticsExportJob> getSalesAnalyticsExportJob(MemberUrn memberUrn, ContractUrn contractUrn, Long jobId) {
    try {
      return _salesAnalyticsExportJobsApiClient.retrieveJobDetails(jobId, contractUrn, memberUrn).map(
          salesAnalyticsExportJob -> convert(salesAnalyticsExportJob));
    } catch (Exception e) {
      LOG.error(
          "Encountered exception in getSalesAnalyticsExportJob for jobId - {}, memberUrn - {}, contractUrn - {} :: ",
          jobId, memberUrn, contractUrn, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * Retrieve data availability for a contract
   * @param table The name of the table serving data for the endpoint
   * @param contractUrn The contract urn of the user requesting data
   * @return SalesAnalyticsDataAvailability with data availability info
   */
  public Task<ClosedTimeRange> retrieveDataAvailability(String table, ContractUrn contractUrn) {
    return _lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_USAGE_REPORTING_MIGRATE_DATA_AVAILABILITY_TO_LSS_REPORTING).flatMap(isLixEnabled -> {
      if (!isLixEnabled) {
        return _salesAnalyticsExportJobService.retrieveDataAvailability(table, contractUrn);
      }
      try {
        return _salesAnalyticsExportJobsApiClient.retrieveDataAvailability(convertToJobType(table), contractUrn).map(
            dataAvailability -> convert(dataAvailability.getTimeRange()));
      } catch (Exception e) {
        LOG.error(
            "Encountered exception in retrieveDataAvailability for table - {}, contractUrn - {} :: ",
            table, contractUrn, e);
        throw e;
      }
    });
  }

  private SalesAnalyticsExportJobType convertQueueTypeToJobType(Enumeration.Value dataType) {
    if (dataType.equals(QueueType.SalesAnalyticsActivity())) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_DATA;
    } else if (dataType.equals(QueueType.SalesAnalyticsSeat())) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_SEAT_DATA;
    } else if (dataType.equals(QueueType.SalesAnalyticsTag())) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_TAG_DATA;
    } else if (dataType.equals(QueueType.SalesAnalyticsActivityOutcome())) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_OUTCOME_DATA;
    } else if (dataType.equals(QueueType.SalesAnalyticsMemberIdentityMapping())) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_MEMBER_IDENTITY_MAPPING_DATA;
    }
    return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_UNKNOWN;
  }

  private SalesAnalyticsExportJob convert(proto.com.linkedin.sales.report.SalesAnalyticsExportJob clientExportJob) {
    SalesAnalyticsExportJob salesAnalyticsExportJob = new SalesAnalyticsExportJob();
    salesAnalyticsExportJob.setId(clientExportJob.getId());
    salesAnalyticsExportJob.setDownloadUrl(new Url(clientExportJob.getCsvUrl()));
    salesAnalyticsExportJob.setProgress(clientExportJob.getProgress());
    salesAnalyticsExportJob.setRowCount(clientExportJob.getRowCount());
    salesAnalyticsExportJob.setStatus(convertStatus(clientExportJob.getStatus()));
    salesAnalyticsExportJob.setExpireAt(clientExportJob.getExpiresAt());
    return salesAnalyticsExportJob;
  }

  private SalesAnalyticsStatus convertStatus(SalesAnalyticsExportJobStatus clientStatus) {
    if (clientStatus.equals(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_SUBMITTED)) {
      return SalesAnalyticsStatus.ENQUEUED;
    } else if (clientStatus.equals(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_JOB_COMPLETED)) {
      return SalesAnalyticsStatus.COMPLETED;
    } else if (clientStatus.equals(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_FAILED_DUE_TO_EXCEEDED_FILE_SIZE_ERROR)) {
      return SalesAnalyticsStatus.FAILED_DUE_TO_EXCEEDED_FILE_SIZE_ERROR;
    } else if (clientStatus.equals(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_FAILED_DUE_TO_INTERNAL_ERROR)) {
      return SalesAnalyticsStatus.FAILED_DUE_TO_INTERNAL_ERROR;
    } else if (clientStatus.equals(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_PROCESSING)) {
      return SalesAnalyticsStatus.PROCESSING;
    }
    return SalesAnalyticsStatus.FAILED_DUE_TO_INTERNAL_ERROR;
  }

  private SalesAnalyticsExportJobType convertToJobType(String tableName) {
    if (tableName.equals(SalesAnalyticsConstants.ACTIVITY_TABLE)) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_DATA;
    } else if (tableName.equals(SalesAnalyticsConstants.ACTIVITY_OUTCOME_TABLE)) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_OUTCOME_DATA;
    } else if (tableName.equals(SalesAnalyticsConstants.SEAT_TABLE)) {
      return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_SEAT_DATA;
    }
    return SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_UNKNOWN;
  }

  private ClosedTimeRange convert(proto.com.linkedin.common.ClosedTimeRange clientTimeRange) {
    ClosedTimeRange closedTimeRange = new ClosedTimeRange();
    closedTimeRange.setStart(clientTimeRange.getStart());
    closedTimeRange.setEnd(clientTimeRange.getEnd());
    return closedTimeRange;
  }
}
