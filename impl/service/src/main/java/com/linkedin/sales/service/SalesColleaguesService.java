package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssColleaguesDB;
import com.linkedin.salescolleagues.ColleagueRelationship;
import com.linkedin.salescolleagues.RelationshipType;
import com.linkedin.salescolleagues.State;
import java.net.URISyntaxException;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * Service class for colleagues (reports-to)
 * <AUTHOR>
 */
public class SalesColleaguesService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesColleaguesService.class);

  private final LssColleaguesDB _lssColleaguesDB;

  public SalesColleaguesService(LssColleaguesDB lssColleaguesDB) {
    _lssColleaguesDB = lssColleaguesDB;
  }

  /**
   * This method adds a colleagueRelationship entry with state ADDED and relationshipType REPORTS_TO.
   * This will return false if an entry already exists for this fromMemberId.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract to add the relationship to.
   * @param toMemberId The member on the other side of the relationship.
   * @param creatorSeatUrn The creator of this relationship.
   * @return True if succeeded, false if failed.
   */
  public Task<Boolean> addManagerRelationship(Long fromMemberId, Long contractId, Long toMemberId,
      SeatUrn creatorSeatUrn) {
    com.linkedin.sales.espresso.ColleagueRelationship colleagueRelationship =
        new com.linkedin.sales.espresso.ColleagueRelationship();

    colleagueRelationship.createdBySeatUrn = creatorSeatUrn.toString();
    colleagueRelationship.state = com.linkedin.sales.espresso.State.ADDED;
    colleagueRelationship.updatedTime = System.currentTimeMillis();

    return _lssColleaguesDB.loadToMemberIdForLatestAdded(fromMemberId, contractId, RelationshipType.REPORTS_TO.name())
        .flatMap(returnedToMemberId -> {
          if (returnedToMemberId != null) {
            return Task.value(FALSE);
          }
          return _lssColleaguesDB.addColleagueRelationshipEntry(fromMemberId, contractId, toMemberId,
              RelationshipType.REPORTS_TO.name(), colleagueRelationship).map(result -> TRUE);
        });
  }

  /**
   * This method adds a colleagueRelationship entry with state REMOVED and relationshipType REPORTS_TO.
   * This will return false if a different ADDED entry already exists for this fromMemberId.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract to add the relationship to.
   * @param toMemberId The member on the other side of the relationship.
   * @param creatorSeatUrn The creator of this relationship.
   * @return True if succeeded, false if failed.
   */
  public Task<Boolean> removeManagerRelationship(Long fromMemberId, Long contractId, Long toMemberId,
      SeatUrn creatorSeatUrn) {
    com.linkedin.sales.espresso.ColleagueRelationship colleagueRelationship =
        new com.linkedin.sales.espresso.ColleagueRelationship();

    colleagueRelationship.createdBySeatUrn = creatorSeatUrn.toString();
    colleagueRelationship.state = com.linkedin.sales.espresso.State.REMOVED;
    colleagueRelationship.updatedTime = System.currentTimeMillis();

    return _lssColleaguesDB.loadToMemberIdForLatestAdded(fromMemberId, contractId, RelationshipType.REPORTS_TO.name())
        .flatMap(returnedToMemberId -> {
          // PWN-16841: disallowing removal of non-existent manager entries
          // However, in the future when we add flagship integration, we need to change this logic
          if (returnedToMemberId == null || !returnedToMemberId.equals(toMemberId)) {
            return Task.value(FALSE);
          }
          return _lssColleaguesDB.addColleagueRelationshipEntry(fromMemberId, contractId, toMemberId,
              RelationshipType.REPORTS_TO.name(), colleagueRelationship).map(result -> TRUE);
        });
  }

  /**
   * This method finds current ColleagueRelationships that match the given {fromMemberId, contractId, [relationshipType]} tuple.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract the relationship belongs to.
   * @param relationshipType The type of relationship between two members, in the format [fromMember] [relationshipType] [toMember]
   * @param start The paging start
   * @param count The paging count
   * @return List of ColleagueRelationships.
   */
  public Task<List<ColleagueRelationship>> findByFromMember(Long fromMemberId, Long contractId,
      RelationshipType relationshipType, int start, int count) {
    return _lssColleaguesDB.getColleagueRelationships(fromMemberId, contractId,
        relationshipType != null ? relationshipType.name() : null, start, count)
        .map(dbResults -> dbResults.stream().map(dbResult -> {

          String returnedRelationshipType = dbResult.getFirst().getFirst();
          Long toMemberId = dbResult.getFirst().getSecond();
          com.linkedin.sales.espresso.ColleagueRelationship colleagueRelationship = dbResult.getSecond();
          String creatorSeatUrnString = colleagueRelationship.createdBySeatUrn.toString();
          Long updatedTime = colleagueRelationship.updatedTime;
          com.linkedin.sales.espresso.State state = colleagueRelationship.state;

          return constructColleagueRelationship(fromMemberId, contractId, returnedRelationshipType, toMemberId,
              creatorSeatUrnString, updatedTime, state);
        }).collect(Collectors.toList()));
  }

  /**
   * This method finds historical ColleagueRelationships that match the given {fromMemberId, contractId, [relationshipType]} tuple.
   * @param fromMemberId The member from which the relationship originates, usually the viewee.
   * @param contractId The contract the relationship belongs to.
   * @param relationshipType The type of relationship between two members, in the format [fromMember] [relationshipType] [toMember]
   * @param start The paging start
   * @param count The paging count
   * @return List of ColleagueRelationships representing the history.
   */
  public Task<List<ColleagueRelationship>> findHistoryByFromMember(Long fromMemberId, Long contractId,
      RelationshipType relationshipType, int start, int count) {
    return _lssColleaguesDB.getColleagueRelationshipHistory(fromMemberId, contractId,
        relationshipType != null ? relationshipType.name() : null, start, count)
        .map(dbResults -> dbResults.stream().map(dbResult -> {

          String returnedRelationshipType = dbResult.getFirst().getFirst();
          Long toMemberId = dbResult.getFirst().getSecond();
          com.linkedin.sales.espresso.ColleagueRelationshipHistory historyEntry = dbResult.getSecond();
          String creatorSeatUrnString = historyEntry.createdBySeatUrn.toString();
          Long updatedTime = historyEntry.updatedTime;
          com.linkedin.sales.espresso.State state = historyEntry.state;

          return constructColleagueRelationship(fromMemberId, contractId, returnedRelationshipType, toMemberId,
              creatorSeatUrnString, updatedTime, state);
        }).collect(Collectors.toList()));
  }

  private ColleagueRelationship constructColleagueRelationship(Long fromMemberId, Long contractId,
      String relationshipType, Long toMemberId, String creatorSeatUrnString, Long updatedTime,
      com.linkedin.sales.espresso.State state) {
    ColleagueRelationship colleagueRelationship = new ColleagueRelationship();
    try {
      SeatUrn creatorSeatUrn = SeatUrn.deserialize(creatorSeatUrnString);
      colleagueRelationship.setCreator(creatorSeatUrn);
    } catch (URISyntaxException e) {
      String message = "Failed to deserialize creator seat urn";
      LOG.error(message);
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, message, e);
    }
    switch (state) {
      case ADDED:
        colleagueRelationship.setState(State.ADDED);
        break;
      case REMOVED:
        colleagueRelationship.setState(State.REMOVED);
        break;
      default:
        String message = "Unexpected state when constructing ColleagueRelationship";
        LOG.error(message);
        throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, message);
    }
    colleagueRelationship.setLastUpdatedAt(updatedTime);
    colleagueRelationship.setFromMember(new MemberUrn(fromMemberId));
    colleagueRelationship.setContract(new ContractUrn(contractId));
    colleagueRelationship.setRelationshipType(RelationshipType.valueOf(relationshipType));
    colleagueRelationship.setToMember(new MemberUrn(toMemberId));
    return colleagueRelationship;
  }
}
