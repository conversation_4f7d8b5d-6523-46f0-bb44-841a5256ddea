package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.EntityResponse;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.admin.ContractStatus;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesContractClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.utils.HandraiseConstant;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


public class SalesContractService {
  private static final PathSpec[] SALES_SEAT_ACTIVE_ROLE_PATH_SPEC =
      new PathSpec[]{SalesSeat.fields().contract(), SalesSeat.fields().id(), SalesSeat.fields().status(),
          SalesSeat.fields().roles(), SalesSeat.fields().entitlements()};

  private static final PathSpec[] SALES_CONTRACT_PATH_SPEC =
      new PathSpec[]{SalesContract.fields().id(), SalesContract.fields().name(), SalesContract.fields().description(),
          SalesContract.fields().locked(), SalesContract.fields().status(),
          SalesContract.fields().seatRoleAllocations()};

  private final SalesContractClient _salesContractClient;
  private final SalesSeatClient _salesSeatClient;
  private final LixService _lixService;

  public SalesContractService(SalesContractClient salesContractClient, SalesSeatClient salesSeatClient,
      LixService lixService) {
    _salesContractClient = salesContractClient;
    _salesSeatClient = salesSeatClient;
    _lixService = lixService;
  }

  /**
   * get Contract by Id
   * @param contractId contract id
   * @param pathSpecs fields need to fill
   * @return the contract mapped to the given id
   */
  public Task<SalesContract> getSalesContractById(Long contractId, PathSpec... pathSpecs) {
    return _salesContractClient.getContract(contractId, pathSpecs);
  }

  /**
   * Get all contracts in which a member has seats
   * @return List of contracts and whether the member has SNAP access in the contracts
   */
  public Task<List<Pair<SalesContract, Boolean>>> findSalesContractsByMember(@NonNull MemberUrn memberUrn,
      @Nullable PagingContext pagingContext) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(), "findSalesContractsByMember");
    return _salesSeatClient.findByMember(memberUrn, null, viewer, pagingContext, SALES_SEAT_ACTIVE_ROLE_PATH_SPEC)
        .flatMap(seats -> {
          Set<Long> contractIds = seats.stream()
              .map(SalesSeat::getContract)
              .map(ContractUrn::getContractIdEntity)
              .collect(Collectors.toSet());
          return _salesContractClient.getContracts(contractIds, SALES_CONTRACT_PATH_SPEC).map(contractResponse -> {
            Map<Long, EntityResponse<SalesContract>> contractById = contractResponse.getResults();
            return seats.stream().map(seat -> {
              SalesContract contract = contractById.get(seat.getContract().getContractIdEntity()).getEntity();
              return new Pair<>(contract, hasUsageReportingExternalExportPermission(seat));
            }).collect(Collectors.toList());
          });
        });
  }

  /**
   * Get all contracts in which a member has seats
   */
  public Task<List<com.linkedin.sales.Contract>> findContractsByMember(MemberUrn memberUrn) {
    PagingContext pagingContext = new PagingContext(0, 50);
    return findSalesContractsByMember(memberUrn, pagingContext).flatMap(contracts -> {
      List<Task<com.linkedin.sales.Contract>> tasks = contracts.stream().map(contractSnapAccessPair -> {
        SalesContract contract = contractSnapAccessPair.getFirst();
        return isUsageReportingDisabled(new ContractUrn(contract.getId(GetMode.NULL))).map(isUsageReportDisabled -> {
          com.linkedin.sales.Contract contractData = new com.linkedin.sales.Contract();
          contractData.setContract(new ContractUrn(contract.getId(GetMode.NULL))).setName(contract.getName());
          contractData.setDescription(contract.getDescription(GetMode.NULL), SetMode.IGNORE_NULL);
          contractData.setHasReportingAccess(contractSnapAccessPair.getSecond());
          contractData.setOptedOutOfReporting(isUsageReportDisabled);
          return contractData;
        });
      }).collect(Collectors.toList());

      return Task.par(tasks).map(ArrayList::new);
    });
  }

  /**
   * Check if a user is allowed to use SNAP apis, exception thrown if user does not have all required permission to use SNAP.
   */
  public Task<Void> checkMemberAllowedToUseSNAP(ContractUrn contractUrn, MemberUrn memberUrn) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "checkMemberAllowedToUseSNAP");
    Task<List<SalesSeat>> seatsTask =
        _salesSeatClient.findByMember(memberUrn, Collections.singleton(contractUrn), viewer, null, SALES_SEAT_ACTIVE_ROLE_PATH_SPEC);
    return seatsTask.flatMap(seats -> {
      if (seats.isEmpty()) {
        throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "User does not have any assigned permission on Sales Navigator contract");
      }
      SalesSeat seat = seats.get(0);
      if (!hasUsageReportingExternalExportPermission(seat)) {
        throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "User does not have external reporting permission on Sales Navigator contract");
      }

      return _salesContractClient.getContract(contractUrn.getContractIdEntity(), SALES_CONTRACT_PATH_SPEC)
          .map(contract -> {
            if (contract.isLocked() || contract.getStatus(GetMode.NULL) == ContractStatus.SUSPENDED) {
              throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
                  "Cannot access Sales Navigator data because the contract is not active");
            }
            return null;
          });
    });
  }

  private boolean hasUsageReportingExternalExportPermission(SalesSeat seat) {
    return seat.getEntitlements().contains(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);
  }

  public Task<Boolean> isUsageReportingDisabled(ContractUrn contractUrn) {
    return _lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_DISABLE_USAGE_REPORT);
  }

  /**
   * Fetches contracts containing contractId associated with input companyId from Handraise pilot list
   * The Name and HasReportingAccess fields of the returned contracts have no meaning in this context.
   * They're added as they're required and will always return "" and false
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "COMPANY_TO_CONTRACT_MAP.getOrDefault values are not null")
  public Task<List<com.linkedin.sales.Contract>> getContractsByHandraisePilotCompany(Long companyId) {
    List<Long> contractIds = HandraiseConstant.COMPANY_TO_CONTRACT_MAP.getOrDefault(companyId, Collections.emptyList());
    return Task.value(contractIds
        .stream()
        .map(contractId -> new com.linkedin.sales.Contract()
            .setContract(new ContractUrn(contractId))
            .setName("")
            .setHasReportingAccess(false))
        .collect(Collectors.toList()));
  }
}