package com.linkedin.sales.service;

import com.linkedin.security.crypto.CryptoException;
import com.linkedin.security.crypto.CryptoSuite;
import com.linkedin.security.crypto.ISymmetricEncrypterDecrypter;


/**
 *
 */
public class SalesCryptoService {

  private static final String ALIAS_INVITE_ENCRYPTION_KEY = "invite.encryptionkey";

  protected final ISymmetricEncrypterDecrypter inviteCrypto;

  public SalesCryptoService(CryptoSuite cryptoSuite) {
    this(cryptoSuite.getSymmetricEncrypterDecrypter(ALIAS_INVITE_ENCRYPTION_KEY));
  }

  public SalesCryptoService(ISymmetricEncrypterDecrypter inviteCrypto) {
    this.inviteCrypto = inviteCrypto;
  }

  /**
   * Linkedin {@link ISymmetricEncrypterDecrypter} AES then Base64, for encryption
   *
   * @param data
   * @return
   */
  public String encryptSeatInviteToken(byte[] data) {
    try {
      return inviteCrypto.encryptToBase64(inviteCrypto.encrypt(data));
    } catch (CryptoException e) {
      throw new RuntimeException(e);
    }
  }




}
