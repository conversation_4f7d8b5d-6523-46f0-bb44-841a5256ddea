package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.salescustomfilterview.FilterEntityGroup;
import com.linkedin.salescustomfilterview.FilterLayout;
import com.linkedin.salescustomfilterview.FilterLayoutColumn;
import com.linkedin.salescustomfilterview.FilterLayoutConfig;
import com.linkedin.salescustomfilterview.FilterLayoutElement;
import com.linkedin.salescustomfilterview.FilterLayoutElementArray;
import com.linkedin.salescustomfilterview.SearchViewType;
import java.util.ArrayList;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service for get, upsert, and delete Sales Filter Layout
 */
public class SalesFilterLayoutService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesFilterLayoutService.class);

  private final LssCustomFilterViewDB _lssCustomFilterViewDB;

  public SalesFilterLayoutService(LssCustomFilterViewDB lssCustomFilterViewDB) {
    _lssCustomFilterViewDB = lssCustomFilterViewDB;
  }

  /**
   * Upserts a filterLayout to the DB for the given seat and searchViewType. If the record exists, override it. If the
   * record does not exist, create it with the specified values.
   *
   * @param seatUrn the {@link SeatUrn} of the actor
   * @param contractUrn the {@link ContractUrn} of the actor
   * @param searchViewType the search view type this layout corresponds to
   * @param filterLayout the specific layout to store
   * @return TRUE is successful, false otherwise
   */
  public Task<Boolean> upsertFilterLayout(@Nonnull SeatUrn seatUrn, @Nonnull ContractUrn contractUrn,
      @Nonnull SearchViewType searchViewType, @Nonnull FilterLayout filterLayout) {
    com.linkedin.sales.espresso.FilterLayout dbFilterLayout = new com.linkedin.sales.espresso.FilterLayout();
    dbFilterLayout.setContractUrn(contractUrn.toString());
    dbFilterLayout.setFilterOrder(filterLayout.getLayout().stream().collect(Collectors.toMap(
        element -> element.getGroup().toString(),
        element -> {
          com.linkedin.sales.espresso.FilterLayoutConfig filterLayoutConfig = new com.linkedin.sales.espresso.FilterLayoutConfig();
          if (element.getConfig().hasEntityColumn()) {
            switch (element.getConfig().getEntityColumn(GetMode.STRICT)) {
              case LEFT:
                filterLayoutConfig.setEntityColumn(0);
                break;
              case RIGHT:
                filterLayoutConfig.setEntityColumn(1);
                break;
              default:
                LOG.warn("Unrecognized entity column {} for searchFilterLayout. Seat: {}, SearchViewType {}",
                    element.getConfig().getEntityColumn(GetMode.STRICT), seatUrn, searchViewType);
                break;
            }
          }
          filterLayoutConfig.setEntityNames(new ArrayList<>(element.getConfig().getEntities()));
          return filterLayoutConfig;
        })));
    return _lssCustomFilterViewDB.upsertFilterLayout(seatUrn.toString(), searchViewType.toString(), dbFilterLayout);
  }

  /**
   * Gets a {@link FilterLayout} for a given seatUrn and searchViewType
   *
   * @param seatUrn the {@link SeatUrn} of the actor
   * @param searchViewType the search view type this layout corresponds to
   * @return {@link FilterLayout}
   */
  public Task<FilterLayout> getFilterLayout(@Nonnull SeatUrn seatUrn, @Nonnull SearchViewType searchViewType) {
    return _lssCustomFilterViewDB.getFilterLayout(seatUrn.toString(), searchViewType.toString()).map(dbFilterLayout -> {
      FilterLayout filterLayout = new FilterLayout();
      filterLayout.setLayout(dbFilterLayout.getFilterOrder().entrySet().stream().map(dbEntry -> {
        FilterLayoutElement filterLayoutElement = new FilterLayoutElement();
        filterLayoutElement.setGroup(FilterEntityGroup.valueOf(dbEntry.getKey().toString()));
        FilterLayoutConfig filterLayoutConfig = new FilterLayoutConfig();
        if (dbEntry.getValue().hasField("entityColumn")) {
          if (dbEntry.getValue().getEntityColumn() == 0) {
            filterLayoutConfig.setEntityColumn(FilterLayoutColumn.LEFT);
          } else if (dbEntry.getValue().getEntityColumn() == 1) {
            filterLayoutConfig.setEntityColumn(FilterLayoutColumn.RIGHT);
          } else {
            LOG.warn("Unrecognized entity column {} for searchFilterLayout. Seat: {}, SearchViewType {}",
                dbEntry.getValue().getEntityColumn(), seatUrn, searchViewType);
          }
        }
        filterLayoutConfig.setEntities(new StringArray(
            dbEntry.getValue().getEntityNames().stream().map(CharSequence::toString).collect(Collectors.toList())));
        filterLayoutElement.setConfig(filterLayoutConfig);
        return filterLayoutElement;
      }).collect(Collectors.toCollection(FilterLayoutElementArray::new)));
      return filterLayout;
    }).recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t));
  }

  /**
   * Deletes a filterLayout record given the seatUrn and searchViewType
   *
   * @param seatUrn the {@link SeatUrn} of the actor
   * @param searchViewType the search view type this layout corresponds to
   * @return TRUE if successful, FALSE otherwise.
   */
  public Task<HttpStatus> deleteFilterLayout(@Nonnull SeatUrn seatUrn, @Nonnull SearchViewType searchViewType) {
    return _lssCustomFilterViewDB.deleteFilterLayout(seatUrn.toString(), searchViewType.toString());
  }
}
