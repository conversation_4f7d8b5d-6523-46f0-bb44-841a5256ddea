package com.linkedin.sales.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import org.apache.http.client.utils.URIBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.nio.charset.StandardCharsets.*;


public class SalesInviteRegisterUrlService {


  private static final Logger logger = LoggerFactory.getLogger(SalesInviteRegisterUrlService.class);
  private final String _inviteRegisterUrlPrefix;
  private final SalesCryptoService _salesCryptoService;
  private final ObjectMapper _objMapper;


  public SalesInviteRegisterUrlService(String inviteRegisterUrlPrefix,
      SalesCryptoService salesCryptoService,
      ObjectMapper objMapper) {
    _inviteRegisterUrlPrefix = inviteRegisterUrlPrefix;
    _salesCryptoService = salesCryptoService;
    _objMapper = objMapper;
  }

  public SalesInviteRegisterUrlService(String inviteRegisterUrlPrefix, SalesCryptoService salesCryptoService) {
    this(inviteRegisterUrlPrefix, salesCryptoService, new ObjectMapper());
  }

  private static final String AUTH = "auth";

  /**
   * Generating Enterprise Platform registration Url link by  enterpriseApplicationInstanceUrn and enterpriseProfileUrn
   * @param enterpriseApplicationInstance payload required for generating registration Url
   * @param enterpriseProfile payload required for generating registration Url
   * @return a url with payload encrypted using AES
   */
  public String generateLighthouseFrontendRegistrationUrl(
      EnterpriseApplicationInstanceUrn enterpriseApplicationInstance,
      EnterpriseProfileUrn enterpriseProfile) {
    EnterprisePlatformRegistrationData data = new EnterprisePlatformRegistrationData(
        enterpriseApplicationInstance,
        enterpriseProfile
    );
    return toRegisterUrl(data);
  }

  private String toRegisterUrl(Object payload) {
    try {
      String generatedUrl = new URIBuilder(_inviteRegisterUrlPrefix)
          .addParameter(AUTH, _salesCryptoService.encryptSeatInviteToken(toJson(payload).getBytes(UTF_8)))
          .build().toURL().toString();
      logger.info("generatedUrl:{}, with payload:{}", generatedUrl, payload);
      return generatedUrl;
    } catch (Throwable e) {
      logger.error("failed to generate Url by payload:{}", payload, e);
      throw new RuntimeException(e);
    }
  }

  //for test
  String toJson(Object bean) {
    try {
      return _objMapper.writeValueAsString(bean);
    } catch (JsonProcessingException e) {
      //this should never happen
      throw new Error("Trying to write unknown object to json, object:" + bean, e);
    }
  }
}

final class EnterprisePlatformRegistrationData {
  private String enterpriseApplicationInstance;
  private String enterpriseProfile;

  EnterprisePlatformRegistrationData(
      EnterpriseApplicationInstanceUrn enterpriseApplicationInstance,
      EnterpriseProfileUrn enterpriseProfile) {
    this.setEnterpriseApplicationInstance(enterpriseApplicationInstance);
    this.setEnterpriseProfile(enterpriseProfile);
  }

  public String getEnterpriseApplicationInstance() {
    return enterpriseApplicationInstance;
  }

  public void setEnterpriseApplicationInstance(EnterpriseApplicationInstanceUrn enterpriseApplicationInstance) {
    this.enterpriseApplicationInstance = enterpriseApplicationInstance.toString();
  }

  public String getEnterpriseProfile() {
    return enterpriseProfile;
  }

  public void setEnterpriseProfile(EnterpriseProfileUrn enterpriseProfile) {
    this.enterpriseProfile = enterpriseProfile.toString();
  }

  @Override
  public String toString() {
    return "EnterprisePlatformRegistrationData{" + "enterpriseApplicationInstance=" + enterpriseApplicationInstance
        + ", enterpriseProfile=" + enterpriseProfile + '}';
  }
}