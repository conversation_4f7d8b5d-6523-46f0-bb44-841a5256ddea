package com.linkedin.sales.service;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.identity.ActivationLink;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesAccount;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.EmailClient;
import com.linkedin.sales.client.common.SalesAccountsV2Client;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.client.ep.EnterpriseProfileActivationLinksClient;
import com.linkedin.urls_private.url.aliases.SalesUrlAliases;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xeril.util.url.URL;
import org.xeril.wafwk.gui.url.AdapterUrlFactory;
import org.xeril.wafwk.gui.url.AppUrlOptions;
import org.xeril.wafwk.gui.url.UrlAliasParameterMap;
import org.xeril.wafwk.gui.url.UrlContext;


/**
 * originated from lighthouse-frontend::SeatsJsonService::sendWelcomeEmails()
 *\
 * we split Seat & SeatRequest handling from here.
 *
 */
public class SalesNavigatorEmailService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorEmailService.class);

  private static final EnterpriseApplicationUrn SALES_NAVIGATOR = EnterpriseApplication.SALES_NAVIGATOR.getUrn();
  private static final Duration CHECKPOINT_URL_TTL = Duration.ofDays(90);

  private final EmailClient _emailClient;
  private final SalesAccountsV2Client _salesAccountsV2Client;
  private final SalesContractService _salesContractService;
  private final SalesSeatClient _salesSeatClient;
  private final SalesInviteRegisterUrlService _salesInviteRegisterUrlService;
  private final EnterprisePlatformClient _enterprisePlatformClient;
  private final EnterpriseProfileActivationLinksClient _enterpriseProfileActivationLinksClient;
  private final AdapterUrlFactory _adapterUrlFactory;

  // need to explicitly define the path specs, the Admin Facade requires a projection
  private static final PathSpec[] SALES_SEAT_PATH_SPECS = new PathSpec[]{
      SalesSeat.fields().contract(),
      SalesSeat.fields().id(),
      SalesSeat.fields().member(),
      SalesSeat.fields().roles(),
      SalesSeat.fields().entitlements()
  };

  private static final PathSpec[] SALES_CONTRACT_PATH_SPECS = new PathSpec[] {
      SalesContract.fields().id(),
      SalesContract.fields().account(),
      SalesContract.fields().provisionType()
  };

  @VisibleForTesting
  public static final PathSpec[] ACCOUNT_V2_PATH_SPECS = new PathSpec[] {
      SalesAccount.fields().id(),
      SalesAccount.fields().name(),
      SalesAccount.fields().company()
  };

  private static final EnterpriseApplicationUsageUrn VIEWER =
      new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
          "migratedAppInstanceMappingToEP");

  public SalesNavigatorEmailService(
      EmailClient emailClient,
      SalesContractService salesContractService,
      SalesSeatClient salesSeatClient,
      SalesInviteRegisterUrlService salesInviteRegisterUrlService,
      EnterprisePlatformClient enterprisePlatformClient,
      EnterpriseProfileActivationLinksClient enterpriseProfileActivationLinksClient,
      AdapterUrlFactory adapterUrlFactory,
      SalesAccountsV2Client salesAccountsV2Client) {
    _emailClient = emailClient;
    _salesContractService = salesContractService;
    _salesSeatClient = salesSeatClient;
    _salesInviteRegisterUrlService = salesInviteRegisterUrlService;
    _enterprisePlatformClient = enterprisePlatformClient;
    _enterpriseProfileActivationLinksClient = enterpriseProfileActivationLinksClient;
    _adapterUrlFactory = adapterUrlFactory;
    _salesAccountsV2Client = salesAccountsV2Client;
  }

  /**
   * There are two registration Flows as of 4.15.2020:
   * 1) CAP: invitation email -> lighthouse-frontend (/sales/register) -> checkpoint -> lighthouse-frontend (/sales/register/confirm)
   * 2) EP: invitation email -> checkpoint -> lighthouse-web (/sales/activate) -> EP Member Bind Flow (multiadmin-web) -> lighthouse-web (/sales/provision)
   *
   * If you are ramped to EP, you will be in the Lix and the URL in your invitation will take you to the 2020 EP Flow.
   * This flow takes you to checkpoint which then redirects you to lighthouse-web after logging in.
   *
   * Otherwise, you will get directed to the 2015 lighthouse-frontend flow.
   * This flow will take you directly to lighthouse-frontend.
   *
   * Once all customers are ramped to EP, 100% of traffic will go thru the new EP Flow and we can deprecate the old flow.
   */
  public Task<String> generateInvitationEmailUrl(
      EnterpriseProfileUrn recipientProfileUrn,
      EnterpriseApplicationInstanceUrn recipientAppInstanceUrn,
      Urn senderUrn,
      Long contractId,
      boolean isTLE) {
    ContractUrn customerContractUrn = new ContractUrn(contractId);
    return isMigratedToAccountCenter(customerContractUrn)
        .flatMap(isRampedToAccountCenter -> {
      if (isRampedToAccountCenter) {
        Url redirect = buildLighthouseWebRedirectUrl(recipientAppInstanceUrn, recipientProfileUrn, isTLE);
        Task<ActivationLink> activationLinkTask = _enterpriseProfileActivationLinksClient.generateCheckpointEnterpriseLoginUrl(
            SALES_NAVIGATOR, recipientAppInstanceUrn, recipientProfileUrn, redirect, CHECKPOINT_URL_TTL.getSeconds(), senderUrn);
        return activationLinkTask.map(response -> response.getUrl().toString());
      } else {
        return Task.value(_salesInviteRegisterUrlService.generateLighthouseFrontendRegistrationUrl(recipientAppInstanceUrn, recipientProfileUrn));
      }
    });
  }

  Task<Boolean> isMigratedToAccountCenter(ContractUrn contractUrn) {
    Task<SalesContract> contractTask = _salesContractService.getSalesContractById(contractUrn.getContractIdEntity(),
        SalesContract.fields().migratedToAccountCenter());
    return contractTask.map(SalesContract::isMigratedToAccountCenter).recover(t -> Boolean.FALSE);
  }

  /**
   *
   * @param applicationInstanceUrn the recipient's appInstance
   * @param enterpriseProfileUrn the recipient's enterpriseProfile
   * @return a url that takes you to www.linkedin.com/sales/activate
   */
  @VisibleForTesting
  public Url buildLighthouseWebRedirectUrl(EnterpriseApplicationInstanceUrn applicationInstanceUrn, EnterpriseProfileUrn enterpriseProfileUrn,
      boolean isTLE) {
    UrlAliasParameterMap params = new UrlAliasParameterMap();
    params.put(SalesUrlAliases.SalesSeatActivate.Parameters.enterpriseProfile, enterpriseProfileUrn.toString());
    params.put(SalesUrlAliases.SalesSeatActivate.Parameters.applicationInstance, applicationInstanceUrn.toString());
    params.put(SalesUrlAliases.SalesSeatActivate.Parameters.isTLE, Boolean.toString(isTLE));
    URL activateSeatUrl = _adapterUrlFactory.makeAppUrl(SalesUrlAliases.SalesSeatActivate.instance,
        params, new UrlContext.UrlContextBuilder().build(), new AppUrlOptions());
    return new Url(activateSeatUrl.getURL());
  }
}

