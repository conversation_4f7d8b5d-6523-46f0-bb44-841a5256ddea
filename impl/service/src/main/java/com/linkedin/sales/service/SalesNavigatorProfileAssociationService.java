package com.linkedin.sales.service;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.crm.Channel;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.CrmSource;
import com.linkedin.crm.DerivedMapping;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.identity.Profile;
import com.linkedin.noniterableprofileid.ProfileIdGenerator;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.CrmMappingClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.ds.rest.ProfileClient;
import com.linkedin.sales.ds.rest.VectorClient;
import com.linkedin.sales.model.ProfileAssociationsCrmContractModel;
import com.linkedin.sales.model.ProfileAssociationsServiceErrorCode;
import com.linkedin.sales.service.integration.SalesExternalizationService;
import com.linkedin.sales.service.utils.VectorUtils;
import com.linkedin.salesgateway.Partner;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociation;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociationKey;
import com.linkedin.security.crypto.CryptoException;
import com.linkedin.urls_private.url.aliases.SalesUrlAliases;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xeril.util.url.URL;
import org.xeril.wafwk.gui.url.AdapterUrlFactory;
import org.xeril.wafwk.gui.url.AppUrlOptions;
import org.xeril.wafwk.gui.url.UrlAliasParameterMap;
import org.xeril.wafwk.gui.url.UrlContext;

import static com.linkedin.sales.model.ProfileAssociationsServiceErrorCode.*;

/**
 * This service maps 3rd party vendor-specific ID's to LinkedIn MemberID's by:
 *  a) mapping the information passed to us by LSS Partners (a globally unique URL) to a LSS contract by utilizing the LSS contract preferences table
 *  b) mapping (said contract, 3rd party vendor-specific ID) to a LinkedIn MID by utilizing cross system awareness
 */
public class SalesNavigatorProfileAssociationService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorProfileAssociationService.class);
  private static final int VECTOR_SERVICE_TIMEOUT = 2;
  private static final int DESIRED_PROFILE_PIC_WIDTH = 200;
  private static final int DESIRED_PROFILE_PIC_HEIGHT = 200;
  private static final UrlContext DEFAULT_URL_CONTEXT = new UrlContext.UrlContextBuilder().build();
  private static final AppUrlOptions DEFAULT_APP_URL_OPTIONS = new AppUrlOptions();
  private static final Map<Partner, CrmSource> PARTNER_CRM_SOURCE_MAP =
      ImmutableMap.of(Partner.MS, CrmSource.DYNAMICS, Partner.SFDC, CrmSource.SFDC);
  private static final PathSpec[] FIELDS =
      {Profile.fields().pictureInfo(), Profile.fields().profilePicture(), Profile.fields().id()};

  private final CrmPairingClient _crmPairingClient;
  private final ProfileClient _profileClient;
  private final ProfileIdGenerator _profileIdGenerator;
  private final AdapterUrlFactory _adapterUrlFactory;
  private final CrmMappingClient _crmMappingClient;
  private final VectorClient _vectorClient;
  private final SalesExternalizationService _salesExternalizationService;

  public SalesNavigatorProfileAssociationService(CrmPairingClient crmPairingClient, ProfileClient profileClient,
      AdapterUrlFactory adapterUrlFactory, ProfileIdGenerator profileIdGenerator, VectorClient vectorClient,
      CrmMappingClient crmMappingClient, SalesExternalizationService salesExternalizationService) {
    _crmPairingClient = crmPairingClient;
    _profileClient = profileClient;
    _adapterUrlFactory = adapterUrlFactory;
    _profileIdGenerator = profileIdGenerator;
    _vectorClient = vectorClient;
    _crmMappingClient = crmMappingClient;
    _salesExternalizationService = salesExternalizationService;
  }

  static {
    DEFAULT_APP_URL_OPTIONS.setForceAbsolute(true);
    DEFAULT_APP_URL_OPTIONS.setForceSecure(true);
  }

  /**
   * Step 1: Hit contract preferences to resolve the contract urn
   * Step 2: Hit the CSA database to get the member urn
   * @param key (Partner, vendor-specific ID of a partner's instance, vendor-specific ID of a lead or contact record inside that instance)
   * @return
   */
  public Task<Optional<SalesNavigatorProfileAssociation>> getSalesNavProfileAssociation(@NonNull SalesNavigatorProfileAssociationKey key) {
    return batchGetSalesNavProfileAssociations(Collections.singleton(key)).map(kvMapping -> {
      if (kvMapping.size() >= 1) {
        return Optional.of(kvMapping.entrySet().iterator().next().getValue());
      }
      return Optional.empty();
    });
  }

  /**
   * Step 1: Hit contract preferences or crmPairings to resolve the contract urn
   * Step 2: Hit the CSA database to get the member urn
   * @param keys List of (Partner, vendor-specific ID of a crm instance, vendor-specific ID of a lead or contact record inside that instance) pairs
   * @return
   */
  public Task<BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>>
  batchGetSalesNavProfileAssociations(@NonNull Set<SalesNavigatorProfileAssociationKey> keys) {
    SalesNavigatorProfileAssociationKey firstKey = keys.iterator().next();
    if (!keys.stream()
        .allMatch(key -> Objects.equals(key.getInstanceId(), firstKey.getInstanceId())
            && key.getPartner() == firstKey.getPartner())) {
      // Every key in the request should have the same crm instance id and partner type
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST);
    }

    Partner partner = firstKey.getPartner();
    String crmInstanceId = firstKey.getInstanceId();
    CrmInstanceUrn crmInstanceUrn = CrmUrnUtils.createCrmInstanceUrn(PARTNER_CRM_SOURCE_MAP.get(partner), crmInstanceId);

    return _salesExternalizationService.isRequestTrustedForApplication(crmInstanceUrn).flatMap(isTrusted -> {
      if (!isTrusted) {
        throw newServiceCodeError(HttpStatus.S_403_FORBIDDEN, REQUEST_NOT_TRUSTED, "");
      }
      return _crmPairingClient.findByCrmInstance(crmInstanceUrn, true)
          .map(crmPairingsResponse -> getCrmContractModelFromCrmPairing(crmPairingsResponse, firstKey))
          .flatMap(crmContractModel -> {
            Set<String> crmIdsToFind =
                keys.stream().map(SalesNavigatorProfileAssociationKey::getRecordId).collect(Collectors.toSet());

            return findMemberCrmAssociationsWithCsa(crmContractModel, crmInstanceUrn, crmIdsToFind).map(
                crmIdToMemberUrnMap -> formatBatchResultsFromCrmMappings(crmIdsToFind, crmIdToMemberUrnMap, partner,
                    crmInstanceId));
          })
          .flatMap(this::decorate);
    });
  }

  /**
   * Decorates (i.e. adds the necessary fields to the profile associations passed in)
   * Field 1/3: MemberUrn -> already present in the input
   * Field 2/3: Public Profile Photo URL (Vector) requires network I/O
   * Field 3/3: Sales Navigator Profile URL can be formed offline
   * @param profileAssociations
   * @return
   */
  @VisibleForTesting
  Task<BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>> decorate(
      @NonNull BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> profileAssociations) {
    if (profileAssociations.values().isEmpty()) {
      return Task.value(profileAssociations);
    }
    Set<Long> membersWeNeedProfPicUrlsFor = profileAssociations.values()
        .stream()
        .map(assoc -> assoc.getMember().getMemberIdEntity())
        .collect(Collectors.toSet());

    Task<Map<MemberUrn, Optional<Url>>> memberToProfilePicUrlTask = getVectorPublicProfileUrls(membersWeNeedProfPicUrlsFor);

    return memberToProfilePicUrlTask.map(profilePicUrls -> {
      addPublicProfilePhotoUrl(profileAssociations, profilePicUrls);
      addSalesNavProfileUrl(profileAssociations);
      return profileAssociations;
    });
  }

  /**
   * Adds 1/3 of the necessary fields to be decorated - public profile photo
   * @param profileAssociations the records that need to be decorated and returned to the end user
   * @param publicProfilePhotoUrls - mapping from a member to their public profile photo (if available)
   */
  @VisibleForTesting
  void addPublicProfilePhotoUrl(
      @NonNull BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> profileAssociations,
      @NonNull Map<MemberUrn, Optional<Url>> publicProfilePhotoUrls) {
    profileAssociations.forEach((key, association) -> {
      Optional<Url> url = publicProfilePhotoUrls.getOrDefault(association.getMember(), Optional.empty());
      url.ifPresent(association::setProfilePhoto);
    });
  }

  /**
   * Adds 1/3 of the necessary fields to be decorated - sales navigator profile url
   * @param profileAssociations the records that need to be decorated and returned to the end user
   */
  @VisibleForTesting
  void addSalesNavProfileUrl(
      @NonNull BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> profileAssociations) {
    profileAssociations.forEach((key, association) -> {
      Url salesNavProfileUrl = buildSalesNavProfileUrl(association.getMember());
      association.setProfile(salesNavProfileUrl);
    });
  }

  /**
   * 1. Retrieve the full Profiles for each MID
   * 2. Get the DigitalMediaAssetUrn from each Profile
   * 3. Hit Vector resource with those DigitalMediaAssetUrns to get Profile Pic URL's
   * 4. Map those URL's to MemberUrns
   * @param members
   * @return
   */
  @VisibleForTesting
  Task<Map<MemberUrn, Optional<Url>>> getVectorPublicProfileUrls(@NonNull Set<Long> members) {
    // hit profiles so that we can get the full Profile objects which contain DigitalMediaAssetUrns
    Task<List<Profile>> profilesTask = _profileClient.batchGet(members, -1L, FIELDS);

    // pass the digital media asset urns to vector
    return profilesTask
        .flatMap(profiles -> _vectorClient.batchGetLinkedinProfilePhotoUrl(profiles, DESIRED_PROFILE_PIC_HEIGHT, DESIRED_PROFILE_PIC_WIDTH)
            .withTimeout(VECTOR_SERVICE_TIMEOUT, TimeUnit.SECONDS)
            .recoverWith(throwable -> Task.value(Collections.emptyList()))
        .map(mediaArtifactFileIdentifiers -> VectorUtils.mapProfilesToVectorUrls(profiles, mediaArtifactFileIdentifiers)));
  }

  /**
   * Given a MemberUrn, return the URL to that member's Sales Nav Profile
   * @param memberUrn (urn:li:member:110610779)
   * @return salesNavProfileUrl (https://www.linkedin.com/sales/people/ACwAAAaXyVsBMJ1V1zhzEEC58zAX1EiMZbxjU_U,NAME_SEARCH,pLKQ)
   */
  @VisibleForTesting
  @NonNull
  Url buildSalesNavProfileUrl(@NonNull MemberUrn memberUrn) {
    UrlAliasParameterMap salesPeopleUrlParams = new UrlAliasParameterMap();
    salesPeopleUrlParams.put(SalesUrlAliases.SalesPeople.Parameters.profileId, encode(memberUrn));
    URL url = _adapterUrlFactory.makeAppUrl(SalesUrlAliases.SalesPeople.instance, salesPeopleUrlParams,
        DEFAULT_URL_CONTEXT, DEFAULT_APP_URL_OPTIONS);
    return new Url(url.getURL());
  }

  /**
   * Use the ProfileIdGenerator to encode the MID with the same configs
   * that sales-api and lighthouse-frontend use
   */
  @VisibleForTesting
  @NonNull
  String encode(@NonNull MemberUrn memberUrn) {
    try {
      return _profileIdGenerator.generateProfileId(memberUrn.getMemberIdEntity());
    } catch (CryptoException e) {
      LOG.error("Failed to encode a member id", e); // for security reasons, we cannot log the MID, it's considered PII
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
  }

  private Task<Map<String, MemberUrn>> findMemberCrmAssociationsWithCsa(@NonNull ProfileAssociationsCrmContractModel contractModel,
      @NonNull CrmInstanceUrn crmInstanceUrn, @NonNull Set<String> crmIds) {
    ContractUrn contractUrn = contractModel.getContactUrn();
    if (contractModel.isPickedFromMultipleResults()) {
      // If the contract is picked from multiple contracts, only get the best derived mapping, to avoid sharing data across contracts
      return findDerivedMappingByCrmEntityList(contractUrn, crmInstanceUrn, crmIds);
    } else {
      return _crmMappingClient.batchGetMappingsByCrmEntity(contractUrn, crmInstanceUrn, crmIds)
          .map(mappings -> mappings.entrySet()
              .stream()
              .filter(entry -> entry.getValue().getLinkedInEntity().isMember())
              .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getLinkedInEntity().getMember())));
    }
  }

  private Task<Map<String, MemberUrn>> findDerivedMappingByCrmEntityList(@NonNull ContractUrn contractUrn,
      @NonNull CrmInstanceUrn crmInstanceUrn, @NonNull Set<String> crmEntityIds) {
    List<Task<Optional<DerivedMapping>>> maybeMatchTasks = crmEntityIds.stream()
        .map(crmEntityId -> _crmMappingClient.findDerivedMappingByCrmEntityId(contractUrn, crmInstanceUrn, Channel.CRM_SYNC, crmEntityId)
            .map(derivedMappings -> derivedMappings.stream()
                .filter(mapping -> mapping.hasConfidence() && MemberUrn.ENTITY_TYPE.equals(
                    mapping.getLinkedInEntity().getEntityType()))
                .max(Comparator.comparing((DerivedMapping mapping) -> mapping.getConfidence())
                    .thenComparing((DerivedMapping mapping) -> mapping.getChangeTimeStamps().getLastModified()))))
        .collect(Collectors.toList());
    return Task.par(maybeMatchTasks)
        .map(maybeMatches -> maybeMatches.stream()
            .filter(Optional::isPresent)
            .collect(Collectors.toMap(maybeMatch -> maybeMatch.get().getCrmEntityId(), maybeMatch -> {
              try {
                return MemberUrn.createFromUrn(maybeMatch.get().getLinkedInEntity());
              } catch (URISyntaxException e) {
                throw new IllegalStateException("Unable to parse " + maybeMatch.get().getLinkedInEntity()); // Unlikely to happen
              }
            })));
  }

  @NonNull
  private BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> formatBatchResultsFromCrmMappings(
      @NonNull Set<String> crmIds, Map<String, MemberUrn> crmIdToMemberUrnMap, @NonNull Partner partner,
      @NonNull String crmInstanceId) {
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> hits = new HashMap<>();
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, RestLiServiceException> misses = new HashMap<>();

    for (String crmId: crmIds) {
      SalesNavigatorProfileAssociationKey simpleKey = new SalesNavigatorProfileAssociationKey()
          .setRecordId(crmId)
          .setInstanceId(crmInstanceId)
          .setPartner(partner);
      ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey = new ComplexResourceKey<>(simpleKey, new EmptyRecord());

      MemberUrn memberUrn = crmIdToMemberUrnMap.get(crmId);
      if (memberUrn != null) {
        SalesNavigatorProfileAssociation value = new SalesNavigatorProfileAssociation().setMember(memberUrn);
        hits.putIfAbsent(complexKey, value);
      } else {
        misses.putIfAbsent(complexKey, new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
      }
    }
    return new BatchResult<>(hits, misses);
  }

  /**
   * Helper method that gets contract URNs from a CRM Pairing response and returns a single contract URN.
   * @param crmPairingsResponse a response from CrmPairingClient.findByCrmInstance.
   * @param key the SalesNavigatorProfileAssociations key we are looking up contracts for
   * @return a single contract URN for the given instance ID supplied
   */
  @VisibleForTesting
  @NonNull
  ProfileAssociationsCrmContractModel getCrmContractModelFromCrmPairing(@NonNull CollectionResponse<CrmPairing> crmPairingsResponse,
      @NonNull SalesNavigatorProfileAssociationKey key) {
    if (crmPairingsResponse.getElements().isEmpty()) {
      throw newServiceCodeError(HttpStatus.S_400_BAD_REQUEST, CRM_SYNC_NOT_ENABLED, " CRM instance: " + key.getInstanceId());
    }

    if (crmPairingsResponse.getElements().size() > 1) {
      return new ProfileAssociationsCrmContractModel(crmPairingsResponse.getElements().get(0).getContract(), true);
    } else {
      return new ProfileAssociationsCrmContractModel(crmPairingsResponse.getElements().get(0).getContract(), false);
    }
  }

  @NonNull
  private static RestLiServiceException newServiceCodeError(@NonNull HttpStatus httpStatus,
    @Nullable ProfileAssociationsServiceErrorCode errorCode, String additionalMessage) {
    RestLiServiceException exception = new RestLiServiceException(httpStatus);
    if (errorCode != null) {
      exception = new RestLiServiceException(httpStatus, errorCode.getMessage() + additionalMessage);
      exception.setServiceErrorCode(errorCode.getCode());
    }
    return exception;
  }
}
