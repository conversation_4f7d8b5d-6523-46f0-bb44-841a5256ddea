package com.linkedin.sales.service;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.events.UnifiedActionType;
import com.linkedin.events.federator.UnifiedAction;
import com.linkedin.identity.Position;
import com.linkedin.identity.Profile;
import com.linkedin.lss.UrnUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.SalesSavedLeadsRequestBuilders;
import com.linkedin.sales.admin.ContractSettingType;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesContractSettingsClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.rest.ProfileClient;
import com.linkedin.sales.service.leadaccount.SalesAccountsService;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salesleadaccount.AccountDataSource;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import com.linkedin.salesleadaccount.LeadDataSource;
import com.linkedin.salesleadaccount.SalesAccount;
import com.linkedin.salesleadaccount.SalesLead;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


public class SalesNavigatorSaveLeadService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorSaveLeadService.class);
  @VisibleForTesting
  static final PathSpec[] SEAT_FIELDS = new PathSpec[]{
      SalesSeat.fields().contract(), SalesSeat.fields().id()
  };
  protected static final PathSpec[] ISB_PROFILE_FIELDS = new PathSpec[]{
      Profile.fields().positions().values().company(),
      Profile.fields().positions().values().localizedCompanyName()
  };
  protected final SalesSeatClient _salesSeatClient;
  private final SalesContractSettingsClient _salesContractSettingsClient;
  protected final ProfileClient _isbProfileClient;
  private final SavedLeadService _savedLeadService;
  private final SalesAccountsService _salesAccountsService;
  private final SalesLeadAccountAssociationService _salesLeadAccountAssociationService;
  private final TrackingService _trackingService;
  private final LixService _lixService;

  public SalesNavigatorSaveLeadService(
      SalesSeatClient salesSeatClient,
      SalesContractSettingsClient salesContractSettingsClient,
      ProfileClient isbProfileClient,
      SavedLeadService savedLeadService,
      SalesAccountsService salesAccountsService,
      SalesLeadAccountAssociationService salesLeadAccountAssociationService,
      TrackingService trackingService,
      LixService lixService
  ) {
    _salesSeatClient = salesSeatClient;
    _salesContractSettingsClient = salesContractSettingsClient;
    _isbProfileClient = isbProfileClient;
    _savedLeadService = savedLeadService;
    _salesAccountsService = salesAccountsService;
    _salesLeadAccountAssociationService = salesLeadAccountAssociationService;
    _trackingService = trackingService;
    _lixService = lixService;
  }

  public Task<Boolean> saveLead(MemberUrn viewer, MemberUrn viewee, String accountName) {
    return getSeatAndCheckContractEnabled(viewer).flatMap(
        seatOpt -> {
          if (!seatOpt.isPresent()) {
            return Task.value(FALSE);
          }
          SalesSeat seat = seatOpt.get();
          ContractUrn contractUrn = seat.getContract();
          SeatUrn seatUrn = new SeatUrn(seat.getId());
          LOG.info("save lead request: {}, {}, viewer: {}, viewee: {}, accountName: {}",
              contractUrn, seatUrn, viewer, viewee, accountName);
          return saveLead(contractUrn, seatUrn, viewer, viewee, accountName);
        }
    );
  }

  /**
   * Save the lead and if companyId is present, also save the account and the lead account association.
   */
  public Task<Boolean> saveLead(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn, @NonNull MemberUrn viewer,
      @NonNull MemberUrn viewee, @Nullable String accountName) {
    return getCompanyIdFromAccountName(viewer, viewee, accountName).map(companyIdOpt -> companyIdOpt.orElse(null))
        .flatMap(companyId -> {
          SalesLead salesLead = new SalesLead().setOwner(seatUrn)
              .setMember(viewee)
              .setContract(contractUrn)
              .setDataSource(LeadDataSource.USER_GENERATED);

          return _savedLeadService.createSavedLead(salesLead).flatMap(savedLeadResult -> {
            if (savedLeadResult.getStatus() != HttpStatus.S_200_OK
                && savedLeadResult.getStatus() != HttpStatus.S_201_CREATED) {
              return Task.value(FALSE);
            }
            if (savedLeadResult.getStatus() == HttpStatus.S_201_CREATED) {
              _trackingService.createAndSendOutSalesActionTrackingEvent(UnifiedAction.SAVE, viewee.toString(),
                  UnifiedActionType.SINGLE, Collections.emptyMap(), viewer, seatUrn, contractUrn);
            }
            if (companyId == null) {
              return Task.value(TRUE);
            }
            OrganizationUrn orgUrn = UrnUtils.createOrganizationUrn(companyId);
            return saveAccount(contractUrn, seatUrn, orgUrn, viewer).flatMap(
                saveAccountResult -> saveLeadAccountAssociation(contractUrn, seatUrn, viewee, orgUrn).map(
                    associationResult -> TRUE).recover(e -> {
                  LOG.error("Fail to save the lead account association for member {} and account {}", viewee, orgUrn,
                      e);
                  return TRUE;
                })).recover(e -> {
                  if (e instanceof RestLiServiceException && ((RestLiServiceException) e).getStatus() == HttpStatus.S_412_PRECONDITION_FAILED) {
                    LOG.warn("Failed to save account for seat {} because saved account limit exceeded", seatUrn, e);
                  } else {
                    LOG.error("Failed to save account for seat {}", seatUrn, e);
                  }
                  return TRUE;  // We return true here since the main operation of saving the lead still succeeded
            });
          }).recoverWith(e -> {
            if (e instanceof RestLiServiceException) {
              HttpStatus status = ((RestLiServiceException) e).getStatus();
              if (status == HttpStatus.S_412_PRECONDITION_FAILED) {
                LOG.warn("Fail to save the lead {} for seat {} as lead limit has been exceeded", viewee,
                    seatUrn);
                return Task.value(FALSE);
              }
            }
            return Task.failure(e);
          });
        });
  }

  /**
   * Save the account and if the account was not previously saved, re-save all the leads under this account.
   */
  private Task<Pair<CompoundKey, CreateResponse>> saveAccount(
      @NonNull ContractUrn contractUrn,
      @NonNull SeatUrn seatUrn,
      @NonNull OrganizationUrn orgUrn,
      @NonNull MemberUrn viewerUrn
  ) {
    SalesAccount salesAccount = new SalesAccount()
        .setOwner(seatUrn)
        .setContract(contractUrn)
        .setOrganization(orgUrn)
        .setDataSource(AccountDataSource.USER_GENERATED);
    return Task.par(_lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE),
            _salesAccountsService.createSavedAccount(salesAccount))
        .flatMap((isAssociationUpdateLixEnabled, saveAccountResponse) -> {
          HttpStatus saveAccountStatus = saveAccountResponse.getSecond().getStatus();
          if (saveAccountStatus == HttpStatus.S_201_CREATED) {
            _trackingService.createAndSendOutSalesActionTrackingEvent(UnifiedAction.SAVE, orgUrn.toString(),
                UnifiedActionType.SINGLE, Collections.emptyMap(), viewerUrn, seatUrn, contractUrn);
            //If the association lix enable, user will save the given account only
            if (isAssociationUpdateLixEnabled) {
              return Task.value(saveAccountResponse);
            }
            return saveLeadsUnderAccount(seatUrn, orgUrn).map(saveLeadsResponse -> saveAccountResponse);
          }
          return Task.value(saveAccountResponse);
        });
  }

  /**
   * Re-save leads under account based on the existing association records
   */
  private Task<Map<CompoundKey, CreateResponse>> saveLeadsUnderAccount(@NonNull SeatUrn seatUrn,
      @NonNull OrganizationUrn orgUrn) {
    return _salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(Collections.singletonList(orgUrn),
        seatUrn, 0, ServiceConstants.GET_ALL_COUNT).flatMap(associations -> {
      Set<SalesLead> salesLeads = associations.getResult()
          .stream()
          .map(association -> new SalesLead().setMember(association.getLead())
              .setOwner(seatUrn)
              .setContract(association.getContract())
              .setDataSource(LeadDataSource.USER_GENERATED))
          .collect(Collectors.toSet());
      return _savedLeadService.batchCreateSavedLeads(salesLeads).recover(e -> {
        LOG.error("Fail to resave leads under account {} for seat {}", orgUrn, seatUrn, e);
        return Collections.emptyMap();
      });
    });
  }

  private Task<Pair<CompoundKey, CreateResponse>> saveLeadAccountAssociation(
      @NonNull ContractUrn contractUrn,
      @NonNull SeatUrn seatUrn,
      @NonNull MemberUrn lead,
      @NonNull OrganizationUrn account
  ) {
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation()
        .setCreator(seatUrn)
        .setContract(contractUrn)
        .setLead(lead)
        .setAccount(account);
    return _salesLeadAccountAssociationService.createLeadAccountAssociation(leadAccountAssociation);
  }

  protected Task<Optional<Long>> getCompanyIdFromAccountName(MemberUrn viewer, MemberUrn viewee, String accountName) {
    if (accountName == null) {
      return Task.value(Optional.empty());
    }

    return _isbProfileClient
        .getProfile(viewer.getMemberIdEntity(), viewee.getMemberIdEntity(), ISB_PROFILE_FIELDS)
        .map(
            profile ->
                Optional.ofNullable(profile.getPositions())
                    .flatMap(positions -> findCompanyIdFromPositionMap(positions, accountName))
        )
        .recover(e -> Optional.empty());
  }

  protected Optional<Long> findCompanyIdFromPositionMap(Map<String, Position> positions, String accountName) {
    return positions.values().stream()
        .filter(position -> StringUtils.equalsIgnoreCase(accountName, position.getLocalizedCompanyName()))
        .findFirst()
        .filter(Position::hasCompany)
        .map(Position::getCompany)
        .map(Urn::getIdAsLong);
  }

  protected Task<Optional<SalesSeat>> getSeatAndCheckContractEnabled(MemberUrn viewer) {
    return getLastChosenContractSeat(viewer)
        .flatMap(seatOpt -> {
          if (seatOpt.isPresent()) {
            SalesSeat seat = seatOpt.get();
            ContractUrn contractUrn = seat.getContract();
            return isContractEnabled(contractUrn)
                .map(enabled -> enabled ? seatOpt : Optional.empty());
          } else {
            LOG.info("Viewer {} doesn't have seat", viewer);
            return Task.value(Optional.empty());
          }
        });
  }

  protected Task<Optional<SalesSeat>> getLastChosenContractSeat(MemberUrn viewer) {
    return _salesSeatClient.findByMemberLastUsed(viewer, SEAT_FIELDS).map(list -> list.stream().findFirst());
  }

  protected Task<Boolean> isContractEnabled(ContractUrn contractUrn) {
    return _salesContractSettingsClient.get(contractUrn.getContractIdEntity(), ContractSettingType.OFFICE_365_INTEGRATION_ENABLED)
        .map(enabled -> enabled.getValue().getBoolean())
        .recover(e -> {
          LOG.warn("Failed to get OFFICE_365_INTEGRATION_ENABLED setting for contract {}", contractUrn, e);
          return TRUE;
        });
  }

  public Task<Boolean> unsaveLead(MemberUrn viewer, MemberUrn viewee) {
    return getSeatAndCheckContractEnabled(viewer).flatMap(
        seatOpt -> {
          if (!seatOpt.isPresent()) {
            return Task.value(FALSE);
          }
          SalesSeat seat = seatOpt.get();
          ContractUrn contractUrn = seat.getContract();
          SeatUrn seatUrn = new SeatUrn(seat.getId());
          LOG.info("unsave lead request: {}, {}, viewer: {}, viewee: {}",
              contractUrn, seatUrn, viewer, viewee);
          return unsaveLead(contractUrn, seatUrn, viewee, viewer);
        }
    );
  }

  /**
   * Unsave the lead and if the lead was not previously unsaved, also remove any lead account associations.
   */
  public Task<Boolean> unsaveLead(ContractUrn contractUrn, SeatUrn seatUrn, MemberUrn viewee, MemberUrn viewer) {
    CompoundKey compoundKey = new SalesSavedLeadsRequestBuilders.Key().setMember(viewee).setOwner(seatUrn);
    return _savedLeadService.deleteSavedLead(compoundKey).flatMap(response -> {
      if (response.getStatus() == HttpStatus.S_200_OK) {
        // Lead already unsaved so nothing else to do
        return Task.value(TRUE);
      } else if (response.getStatus() == HttpStatus.S_204_NO_CONTENT) {
        _trackingService.createAndSendOutSalesActionTrackingEvent(UnifiedAction.UNSAVE, viewee.toString(),
            UnifiedActionType.SINGLE, Collections.emptyMap(), viewer, seatUrn, contractUrn);
        // The main operation of unsaving the lead succeeded so we mark the result as true even if the association
        // failed to be deleted
        return _salesLeadAccountAssociationService.deleteAssociationsForLead(viewee, seatUrn)
            .map(result -> TRUE)
            .recover(e -> {
              LOG.error("Fail to delete lead account association for lead {}", viewee, e);
              return TRUE;
            });
      }

      LOG.error("Failed to unsave lead {} for contract {} and seat {}: {}", viewee, contractUrn, seatUrn,
          response.getStatus());
      return Task.value(FALSE);
    });
  }
}
