package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.common.urn.EnterpriseSeatUrn;
import com.linkedin.crm.CrmUserMapping;
import com.linkedin.crm.CrmUserMappingKey;
import com.linkedin.enterprise.appsconnector.dataextension.CrmSyncStatus;
import com.linkedin.enterprise.appsconnector.dataextension.SalesNavigatorSeatDataExtension;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.CrmUserMappingClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service that creates enterprise seat data extensions from Sales Navigator data.
 */
public class SalesNavigatorSeatDataExtensionService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesNavigatorSeatDataExtensionService.class);
  private final CrmUserMappingClient _crmUserMappingClient;
  private final CrmPairingClient _crmPairingClient;

  public SalesNavigatorSeatDataExtensionService(CrmUserMappingClient crmUserMappingClient,
      CrmPairingClient crmPairingClient) {
    _crmUserMappingClient = crmUserMappingClient;
    _crmPairingClient = crmPairingClient;
  }

  /**
   * Get data extension by {@code seatUrn}.
   *
   * @param seatUrn enterprise seat urn this data extension belongs to.
   */
  public Task<SalesNavigatorSeatDataExtension> getDataExtension(EnterpriseSeatUrn seatUrn) {
    return getDataExtensions(Collections.singleton(seatUrn)).map(map -> map.get(seatUrn));
  }

  /**
   * Get data extensions by {@code seatUrns}.
   *
   * @param seatUrns enterprise seat urns to get data extensions for.
   */
  public Task<Map<EnterpriseSeatUrn, SalesNavigatorSeatDataExtension>> getDataExtensions(
      Set<EnterpriseSeatUrn> seatUrns) {
    return getCrmUserMappings(seatUrns).map(crmUserMappings -> seatUrns.stream().collect(Collectors.toMap(
        Function.identity(),
        seatUrn -> {
          CrmUserMapping crmUserMapping = crmUserMappings.get(seatUrn.getSeatIdEntity());
          return new SalesNavigatorSeatDataExtension().setCrmSyncStatus(
              (crmUserMapping == null) ? CrmSyncStatus.CRM_SYNC_NOT_MATCHED : CrmSyncStatus.CRM_SYNC_ON);
    })));
  }

  private Task<Map<Long, CrmUserMapping>> getCrmUserMappings(Set<EnterpriseSeatUrn> seatUrns) {
    Long contractId = seatUrns.iterator().next().getContractIdEntity();
    if (!seatUrns.stream().allMatch(seatUrn -> seatUrn.getContractIdEntity().equals(contractId))) {
      throw new RestLiServiceException(
          HttpStatus.S_400_BAD_REQUEST, String.format("All seats should be from the same contract %s", contractId));
    }
    ContractUrn contractUrn = UrnUtils.createContractUrn(contractId);
    return _crmPairingClient.findCrmPairingsByContract(contractUrn, true, null)
        .flatMap(crmPairings -> {
          if (crmPairings.isEmpty()) {
            LOG.error("Can't find CRM pairing by contract {} ", contractUrn);
            return Task.value(Collections.emptyMap());
          }
          CrmPairingUrn crmPairingUrn =
              new CrmPairingUrn(crmPairings.get(0).getContract(), crmPairings.get(0).getCrmPairingId());
          List<CrmUserMappingKey> crmUserMappingKeys = seatUrns.stream()
              .map(seatUrn -> new CrmUserMappingKey().setSeat(UrnUtils.createSeatUrn(seatUrn.getSeatIdEntity()))
                  .setCrmPairing(crmPairingUrn))
              .collect(Collectors.toList());
          Task<Map<CrmUserMappingKey, CrmUserMapping>> crmUserMappingsTask =
              _crmUserMappingClient.getCrmUserMappings(crmUserMappingKeys);
          return crmUserMappingsTask.map(crmUserMappings ->
              crmUserMappings.values().stream()
                  .collect(Collectors.toMap(value -> value.getSeat().getSeatIdEntity(), Function.identity())));
        });
  }
}