package com.linkedin.sales.service;

import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.container.ic.handlers.api.MemberIdentityHandler;
import com.linkedin.oauth2.internal.OAuth2AccessTokenData;
import com.linkedin.parseq.Task;
import com.linkedin.sales.SalesAccessToken;
import com.linkedin.sales.client.externalization.SalesOauthAuthenticationClient;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.security.identitytoken.api.ColoAware;
import com.linkedin.security.identitytoken.api.MemberIdentityToken;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScope;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScopeWithPermissionInfo;
import com.linkedin.util.envinfo.EnvInfo;
import com.linkedin.util.envinfo.EnvInfoFinder;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesOauthAuthenticationService {

  public static final String DEVELOPER_APPLICATION = "developerApplication";
  public static final String WIDGET_SCOPE = "r_sales_nav_display";
  private static final long SALES_ACCESS_TOKEN_TTL_STANDARD = 2700L; //45 minutes by default
  private static final long SALES_ACCESS_TOKEN_TTL_SHORT = 300L; //5 minutes, a shorter TTL for testing

  private static final Logger LOG = LoggerFactory.getLogger(SalesOauthAuthenticationService.class);

  private final SalesOauthAuthenticationClient _salesOauthAuthenticationClient;
  private final MemberIdentityHandler _memberIdentityHandler;
  private final EnvInfoFinder _envInfoFinder;
  private final LixService _lixService;

  public SalesOauthAuthenticationService(SalesOauthAuthenticationClient salesOauthAuthenticationClient,
      MemberIdentityHandler memberIdentityHandler, EnvInfoFinder envInfoFinder, LixService lixService) {
    _salesOauthAuthenticationClient = salesOauthAuthenticationClient;
    _memberIdentityHandler = memberIdentityHandler;
    _envInfoFinder = envInfoFinder;
    _lixService = lixService;
  }

  public Task<SalesAccessToken> getSalesAccessToken(long appId, long memberId) {
    Urn applicationUrn = createDeveloperApplicationUrnFrom(appId);
    return getSalesAccessTokenFromTPA(appId, memberId, applicationUrn);
  }

  public Task<SalesAccessToken> getSalesAccessTokenFromTPA(long appId, long memberId, Urn appUrn) {
    DeveloperApplicationUrn developerApplicationUrn = createDeveloperApplicationUrnFromUrn(appUrn);

    Task<ThirdPartyAuthorizedScopeWithPermissionInfo> authorizedScopeWithPermissionInfoTask =
        _salesOauthAuthenticationClient.generateThirdPartyAuthorizedScopeForApp(WIDGET_SCOPE, developerApplicationUrn);

    return authorizedScopeWithPermissionInfoTask.flatMap(authorizedScopeWithPermissionInfo -> {
      ThirdPartyAuthorizedScope authorizedScope = authorizedScopeWithPermissionInfo.getScope();
      MemberIdentityToken mit = _memberIdentityHandler.getMemberIdentity();
      EnvInfo accessTokenIssuedEnvInfo = getAccessTokenIssuingEnv(mit);

      Task<OAuth2AccessTokenData> oAuth2AccessTokenDataTask =
          _lixService.isLixEnabled(appUrn, LixUtils.LSS_SAT_TTL_SHORT, null).flatMap(shortTTLEnabled -> {
            long ttl = shortTTLEnabled ? SALES_ACCESS_TOKEN_TTL_SHORT : SALES_ACCESS_TOKEN_TTL_STANDARD;
            return _salesOauthAuthenticationClient.generateOauth2SalesAccessToken(appId, memberId,
                authorizedScope.getId().intValue(), ttl, mit, accessTokenIssuedEnvInfo);
          });

      return oAuth2AccessTokenDataTask.map(oAuth2AccessTokenData -> {
        SalesAccessToken salesAccessToken = new SalesAccessToken();
        salesAccessToken.setToken(oAuth2AccessTokenData.getAccessToken());
        salesAccessToken.setExpiryTime(calculateExpiryTimeInseconds(oAuth2AccessTokenData.getAccessTokenTTLInSec()));
        return salesAccessToken;
      });
    });
  }

  private Urn createDeveloperApplicationUrnFrom(long developerApplicationId) {
    return Urn.createFromTuple(DEVELOPER_APPLICATION, developerApplicationId);
  }

  private DeveloperApplicationUrn createDeveloperApplicationUrnFromUrn(Urn appUrn) {
    try {
      return DeveloperApplicationUrn.createFromUrn(appUrn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  private long calculateExpiryTimeInseconds(long accessTokenTtlInSeconds) {
    long currentTimeInSeconds = System.currentTimeMillis() / 1000;
    return currentTimeInSeconds + accessTokenTtlInSeconds - 60L;
  }

  /**
   * Fetches the Environment Info of the token's issuing colo
   * @param memberIdentityToken
   * @return
   */
  @Nullable
  private EnvInfo getAccessTokenIssuingEnv(@Nullable MemberIdentityToken memberIdentityToken) {
    EnvInfo envInfo = null;
    if (memberIdentityToken instanceof ColoAware) {
      ColoAware coloAware = ColoAware.class.cast(memberIdentityToken);
      int issuingColo = coloAware.getIssuingColo();
      try {
        envInfo = _envInfoFinder.getEnvInfoFromByteId((byte) issuingColo);
      } catch (IllegalArgumentException e) {
        LOG.info("Environment info not found for issuing colo {}", issuingColo);
      }
    }
    return envInfo;
  }
}
