package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.PinnedFilters;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.PinnedFiltersUtils;
import com.linkedin.sales.service.utils.SearchTypeUtils;
import com.linkedin.salescustomfilterview.SearchType;
import com.linkedin.util.Pair;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.apache.commons.collections4.ListUtils;


/**
 * Service for insert, update, get, and delete of Sales Pinned Filters
 * <AUTHOR>
 */
public class SalesPinnedFiltersService {
  private final LssCustomFilterViewDB _lssCustomFilterViewDB;
  private final LixService _lixService;

  public SalesPinnedFiltersService(LssCustomFilterViewDB lssCustomFilterViewDB, LixService lixService) {
    _lssCustomFilterViewDB = lssCustomFilterViewDB;
    _lixService = lixService;
  }

  /**
   * Update a pinnedFilter into a PinnedFilters record in PinnedFilters table. This performs a partial/soft update
   * on the record.
   *
   * If a record does not exist for a given seatUrn and searchType (or record is empty), the default set of filters will
   * be updated.
   * The patch document contains two keys, $set and $unset. For all filters in $set, if the filter does not exist in
   * the DB, add it to the list of pinned filters. For all filters in $unset, if the filter exists in the DB,
   * remove it from the pinned filters.
   * @param seatUrn seat urn of the user
   * @param searchType The type of search the pinned filter is from
   * @param patch The patch document holding what filters to pin/unpin
   * @param contractUrn contract urn of the user
   * @return Boolean denoting true if record was updated, false otherwise.
   */
  public Task<Boolean> updateEspressoPinnedFilters(@Nonnull SeatUrn seatUrn, @Nonnull SearchType searchType,
      @Nonnull PatchRequest<com.linkedin.salescustomfilterview.PinnedFilters> patch, @Nonnull ContractUrn contractUrn) {
    Task<Boolean> isSearchSalesIntelligenceFiltersMemberEnabled =
        _lixService.isMemberBasedLixEnabledForSeat(seatUrn, LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER);
    Task<Boolean> isSearchSalesIntelligenceFiltersContractEnabled =
        _lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT);
    return Task.par(isSearchSalesIntelligenceFiltersMemberEnabled, isSearchSalesIntelligenceFiltersContractEnabled)
        .flatMap((isSearchSalesIntelligenceEnabledMember, isSearchSalesIntelligenceEnabledContract) -> {
      boolean isSearchSalesIntelligenceEnabled = isSearchSalesIntelligenceEnabledContract || isSearchSalesIntelligenceEnabledMember;
      // Pinned filters modifications are currently not supported from mobile, this is a precautious verification
      if (SearchTypeUtils.isMobileSearchType(searchType)) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("Search type: %s is not supported in update operations", searchType)));
      }

      return lookupEspressoPinnedFilters(seatUrn, searchType).flatMap(optCurrentPinnedFilters -> {
        Pair<List<String>, List<String>> pinnedFilters = PinnedFiltersUtils.translatePatchDocument(patch);
        List<String> currentPinnedFilters;
        if (optCurrentPinnedFilters.isPresent() && !optCurrentPinnedFilters.get().isEmpty()) {
          currentPinnedFilters = optCurrentPinnedFilters.get();
        } else {
          currentPinnedFilters = PinnedFiltersUtils.getDefaultPinnedFilters(searchType, isSearchSalesIntelligenceEnabled);
        }
        // Check compatibility for both set and unset filters and update filters that exist in the DB based on compatibility.
        // This is because for filters we want to set, make sure they (or their older counterparts) dont already exist,
        // and for filters we want to unset, make sure we can remove them (and their older counterparts).
        // adjustedPinnedFilters is the adjusted set of the filters that already exist in the DB (currentPinnedFilters).
        Set<String> adjustedPinnedFilters = PinnedFiltersUtils.adjustFilterListForCompatibility(currentPinnedFilters,
            ListUtils.union(pinnedFilters.getFirst(), pinnedFilters.getSecond()));
        // Set Filters
        adjustedPinnedFilters.addAll(pinnedFilters.getFirst());
        // Unset Filters
        pinnedFilters.getSecond().forEach(adjustedPinnedFilters::remove);
        // We have to set a new arrayList here since we cannot convert List<String> directly to List<CharSequence>
        PinnedFilters pinnedFiltersToInsert = new PinnedFilters();
        pinnedFiltersToInsert.setContractUrn(contractUrn.toString());
        pinnedFiltersToInsert.setPinnedFilters(new ArrayList<>(adjustedPinnedFilters));
        return _lssCustomFilterViewDB.upsertPinnedFilters(seatUrn.toString(), searchType.name(), pinnedFiltersToInsert);
      });
    });
  }

  /**
   * Lookup pinnedFilters from an existing record as strings.
   *
   * @param seatUrn seat urn of the user
   * @param searchType The type of search the pinned filter is from
   * @return list of {@link String} containing all the pinned filters.
   */
  private Task<Optional<List<String>>> lookupEspressoPinnedFilters(@Nonnull SeatUrn seatUrn, @Nonnull SearchType searchType) {
    return _lssCustomFilterViewDB.getPinnedFilters(seatUrn.toString(), searchType.name()).map(espressoPinnedFilters ->
        Optional.of(espressoPinnedFilters.pinnedFilters.stream().map(CharSequence::toString).collect(Collectors.toList())))
        .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(Optional.empty()) : Task.failure(t));
  }

  /**
   * Get pinnedFilters from an existing record. Returns the default if no record is found
   * If salesIntelligenceFilters is enabled and contains spotlight filter,
   * call updateAndFetchPinnedFilters to remove spotlight filter from pinned filters
   * @param seatUrn seat urn of the user
   * @param contractUrn contract urn of the user
   * @param searchType The type of search the pinned filter is from
   * @return {@link com.linkedin.salescustomfilterview.PinnedFilters} containing all the pinned filters.
   */
  public Task<com.linkedin.salescustomfilterview.PinnedFilters> getEspressoPinnedFilters(@Nonnull SeatUrn seatUrn,
      @Nonnull ContractUrn contractUrn, @Nonnull SearchType searchType) {
    Task<Boolean> isSearchSalesIntelligenceFiltersMemberEnabled =
        _lixService.isMemberBasedLixEnabledForSeat(seatUrn, LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER);
    Task<Boolean> isSearchSalesIntelligenceFiltersContractEnabled =
        _lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT);
    return Task.par(isSearchSalesIntelligenceFiltersMemberEnabled, isSearchSalesIntelligenceFiltersContractEnabled)
        .flatMap((isSearchSalesIntelligenceEnabledMember, isSearchSalesIntelligenceEnabledContract) -> {
          boolean isSearchSalesIntelligenceEnabled = isSearchSalesIntelligenceEnabledContract || isSearchSalesIntelligenceEnabledMember;
          return getPinnedFilters(seatUrn, searchType, isSearchSalesIntelligenceEnabled)
              .flatMap(pinnedFilters -> (isSearchSalesIntelligenceEnabled && pinnedFilters.getFilters().contains("LEAD_HIGHLIGHTS"))
                  ? updateAndFetchPinnedFilters(seatUrn, contractUrn, searchType, isSearchSalesIntelligenceEnabled) : Task.value(pinnedFilters));
        });
  }

  /**
   * Remove spotlight filter from list of pinned filters then fetch the list of updated pinned filters of user after the removal
   * @param seatUrn seat urn of the user
   * @param contractUrn contract urn of the user
   * @param searchType The type of search the pinned filter is from
   * @param isSearchSalesIntelligenceEnabled whether sales intelligence filter lix is enabled or not
   * @return list of pinned filters after spotlight filter is unpinned
   */
  public Task<com.linkedin.salescustomfilterview.PinnedFilters> updateAndFetchPinnedFilters(@Nonnull SeatUrn seatUrn,
      @Nonnull ContractUrn contractUrn, @Nonnull SearchType searchType, boolean isSearchSalesIntelligenceEnabled) {
    // Create a patch to unset spotlight filter from list of pinned filters
    DataMap pinnedFiltersDataMap = new com.linkedin.salescustomfilterview.PinnedFilters()
        .setFilters(new StringArray("LEAD_HIGHLIGHTS")).data();
    DataMap patchDataMap = new DataMap();
    patchDataMap.put("$unset", pinnedFiltersDataMap);
    PatchRequest<com.linkedin.salescustomfilterview.PinnedFilters> patchRequest = PatchRequest.createFromPatchDocument(patchDataMap);

    // Update and then fetch list of pinned filters
    return updateEspressoPinnedFilters(seatUrn, searchType, patchRequest, contractUrn).flatMap(updated ->
        getPinnedFilters(seatUrn, searchType, isSearchSalesIntelligenceEnabled));
  }

  /**
   * Fetch espresso pinned filters from database and return sales pinned filters
   * @param seatUrn seat urn of the user
   * @param searchType The type of search the pinned filter is from
   * @param isSearchSalesIntelligenceEnabled whether sales intelligence filter lix is enabled or not
   * @return {@link com.linkedin.salescustomfilterview.PinnedFilters} containing all the pinned filters.
   */
  private Task<com.linkedin.salescustomfilterview.PinnedFilters> getPinnedFilters(@Nonnull SeatUrn seatUrn,
      @Nonnull SearchType searchType, boolean isSearchSalesIntelligenceEnabled) {
    return _lssCustomFilterViewDB.getPinnedFilters(seatUrn.toString(), searchType.name()).map(espressoPinnedFilters ->
        new com.linkedin.salescustomfilterview.PinnedFilters().setFilters(new StringArray(
            espressoPinnedFilters.pinnedFilters.stream().map(CharSequence::toString).collect(Collectors.toList()))))
        .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t)
            ? Task.value(new com.linkedin.salescustomfilterview.PinnedFilters().setFilters(
                new StringArray(PinnedFiltersUtils.getDefaultPinnedFilters(searchType, isSearchSalesIntelligenceEnabled))))
            : Task.failure(t));
  }

  /**
   * Delete pinnedFilters record for a given seat urn.
   * @param seatUrn seat urn of user
   * @param searchType searchType to clear searches on
   * @return http status codes denoting if deletion was successful.
   */
  public Task<HttpStatus> deleteEspressoPinnedFilters(@Nonnull SeatUrn seatUrn, @Nonnull SearchType searchType) {
    // Pinned filters modifications are currently not supported from mobile, this is a precautious verification
    if (SearchTypeUtils.isMobileSearchType(searchType)) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          String.format("Search type: %s is not supported in delete operations", searchType)));
    }
    return _lssCustomFilterViewDB.deletePinnedFilters(seatUrn.toString(), searchType.name()).recoverWith(t ->
      _lixService.isMemberBasedLixEnabledForSeat(seatUrn, LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS)
          .flatMap(isHttpStatusLixEnabled -> {
            if (ExceptionUtils.isEntityNotFoundException(t) && isHttpStatusLixEnabled) {
              return Task.value(HttpStatus.S_204_NO_CONTENT);
            }
            return Task.failure(t);
          }));
  }
}
