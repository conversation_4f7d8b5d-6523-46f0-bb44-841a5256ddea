package com.linkedin.sales.service;

import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.parseq.Task;
import com.linkedin.realtimedispatcher.Authorization;
import com.linkedin.realtimedispatcher.AuthorizationStatus;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.SalesIdentity;
import com.linkedin.sales.urn.SalesRelationshipMapChangeLogsTopicUrn;
import com.linkedin.sales.client.common.SalesIdentityClient;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.acl.SubResourceType;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.sales.urn.SalesSharedResourceViewersTopicUrn;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service to authorize access to realtime topics.
 */
public class SalesRealtimeAuthorizationService {
  private final AclServiceDispatcher _aclServiceDispatcher;
  private final SalesIdentityClient _salesIdentityClient;
  private static final Logger LOG = LoggerFactory.getLogger(SalesRealtimeAuthorizationService.class);
  private static final PathSpec[] SALES_IDENTITIES_PROJECTION = {SalesIdentity.fields().owner()};

  public SalesRealtimeAuthorizationService(AclServiceDispatcher aclServiceDispatcher,
      SalesIdentityClient salesIdentityClient) {
    _aclServiceDispatcher = aclServiceDispatcher;
    _salesIdentityClient = salesIdentityClient;
  }

  /**
   * Authorize user to have access to SalesSharedResourceViewersTopic.
   * @param topic Set of topics user wants to subscribe to. The topic urn is of type SalesSharedResourceViewersTopicUrn.
   *              Topic data contains salesList urn.
   * @param subscriber Urn of user. Currently SalesIdentity urn is only supported.
   * Throws 400 error if topic or subscriber is not valid. Any downstream error is also returned back.
   */
  public Task<Authorization> authorizeForSharedResourceViewerTopic(Urn topic, Urn subscriber) {
    Task<SeatUrn> seatUrnTask = getSeatForSubscriber(subscriber);
    SalesListUrn relationshipMapListUrn;
    try {
      relationshipMapListUrn = UrnUtils.createSalesListUrn(
          SalesSharedResourceViewersTopicUrn.createFromUrn(topic).getSharedResourceEntity());
    } catch (Exception e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format("Topic %s is not supported", topic));
    }
    return seatUrnTask.flatMap(seatUrn -> authorizeSeatForRelationshipMap(relationshipMapListUrn, seatUrn));
  }

  /**
   * Authorize user to have access to SalesSharedResourceViewersTopics.
   * @param topics Set of topics user wants to subscribe to. The topic urn is of type SalesSharedResourceViewersTopicUrn.
   *               Topic data contains salesList urn.
   * @param subscriber Urn of user. Currently SalesIdentity urn is only supported.
   * @return BatchResult containing the Authorization results and error map.
   * Throws 400 error if subscriber is not valid. Forwards downstream error in getting seat details for subscriber. Topic errors are added to error map.
   */
  public Task<BatchResult<Urn, Authorization>> authorizeForSharedResourceViewerTopics(Set<Urn> topics, Urn subscriber) {
    Task<SeatUrn> seatUrnTask = getSeatForSubscriber(subscriber);

    Map<Urn, RestLiServiceException> errorMap = new HashMap<>();
    Map<Urn, SalesListUrn> validTopicUrnMap = new HashMap<>();
    topics.forEach(topic -> {
      SalesSharedResourceViewersTopicUrn topicUrn;
      SalesListUrn salesListUrn;
      try {
        topicUrn = SalesSharedResourceViewersTopicUrn.createFromUrn(topic);
        salesListUrn = UrnUtils.createSalesListUrn(topicUrn.getSharedResourceEntity());
      } catch (Exception e) {
        errorMap.put(topic, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("Topic %s is not supported", topic)));
        return;
      }
      validTopicUrnMap.put(topicUrn, salesListUrn);
    });

    return seatUrnTask.flatMap(seatUrn -> authorizeSalesListTopicsHelper(seatUrn, validTopicUrnMap, errorMap))
        .map(resultMap -> new BatchResult<>(resultMap, errorMap));
  }

  /**
   * Authorize user to have access to SalesRelationshipMapChangeLogsTopic.
   * @param topic Topic user wants to subscribe to. The topic urn is of type SalesRelationshipMapChangeLogsTopicUrn.
   *              Topic data contains salesList urn.
   * @param subscriber Urn of user. Currently SalesIdentity urn is only supported.
   * Throws 400 error if topic or subscriber is not valid. Any downstream error is also returned back.
   */
  public Task<Authorization> authorizeForRelationshipMapChangeLogTopic(Urn topic, Urn subscriber) {
    Task<SeatUrn> seatUrnTask = getSeatForSubscriber(subscriber);
    SalesRelationshipMapChangeLogsTopicUrn topicUrn;
    try {
      topicUrn = SalesRelationshipMapChangeLogsTopicUrn.createFromUrn(topic);
    } catch (Exception e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format("Topic %s is not supported", topic));
    }
    SalesListUrn relationshipMapListUrn = UrnUtils.createSalesListUrn(topicUrn.getRelationshipMapEntity());
    return seatUrnTask.flatMap(seatUrn -> authorizeSeatForRelationshipMap(relationshipMapListUrn, seatUrn));
  }

  /**
   * Authorize user to have access to SalesRelationshipMapChangeLogsTopics.
   * @param topics Set of topics user wants to subscribe to. The topic urn is of type SalesRelationshipMapChangeLogsTopicUrn.
   *               Topic data contains salesList urn.
   * @param subscriber Urn of user. Currently SalesIdentity urn is only supported.
   * @return BatchResult containing the Authorization results and error map.
   * Throws 400 error if subscriber is not valid. Forwards downstream error in getting seat details for subscriber. Topic errors are added to error map.
   */
  public Task<BatchResult<Urn, Authorization>> authorizeForRelationshipMapChangeLogTopics(Set<Urn> topics,
      Urn subscriber) {
    Task<SeatUrn> seatUrnTask = getSeatForSubscriber(subscriber);

    Map<Urn, RestLiServiceException> errorMap = new HashMap<>();
    Map<Urn, SalesListUrn> validTopicUrnMap = new HashMap<>();
    topics.forEach(topic -> {
      SalesRelationshipMapChangeLogsTopicUrn topicUrn;
      SalesListUrn salesListUrn;
      try {
        topicUrn = SalesRelationshipMapChangeLogsTopicUrn.createFromUrn(topic);
        salesListUrn = UrnUtils.createSalesListUrn(topicUrn.getRelationshipMapEntity());
      } catch (Exception e) {
        errorMap.put(topic, new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("Topic %s is not supported", topic)));
        return;
      }
      validTopicUrnMap.put(topicUrn, salesListUrn);
    });

    return seatUrnTask.flatMap(seatUrn -> authorizeSalesListTopicsHelper(seatUrn, validTopicUrnMap, errorMap))
        .map(resultMap -> new BatchResult<>(resultMap, errorMap));
  }

  private Task<SeatUrn> getSeatForSubscriber(Urn subscriber) {
    if (UrnUtils.isSalesIdentityUrn(subscriber)) {
      return _salesIdentityClient.get(subscriber.getIdAsLong(), SALES_IDENTITIES_PROJECTION)
          .map(salesIdentity -> UrnUtils.createSeatUrn(salesIdentity.getOwner()));
    }
    return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
        String.format("Subscriber urn %s is expected to be a SalesIdentityUrn", subscriber)));
  }

  private Task<Map<Urn, Authorization>> authorizeSalesListTopicsHelper(SeatUrn seatUrn,
      Map<Urn, SalesListUrn> topicToListUrnMap, Map<Urn, RestLiServiceException> resultErrorMap) {
    List<Task<AbstractMap.SimpleEntry<Urn, Authorization>>> aclTasks = topicToListUrnMap.entrySet()
        .stream()
        .map(topicUrnToListUrn -> authorizeSeatForRelationshipMap(topicUrnToListUrn.getValue(), seatUrn).recover(e -> {
          resultErrorMap.put(topicUrnToListUrn.getKey(),
              new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, e));
          return null;
        }).map(authResult -> new AbstractMap.SimpleEntry<>(topicUrnToListUrn.getKey(), authResult)))
        .collect(Collectors.toList());
    return Task.par(aclTasks)
        .map(topicAuthResults -> topicAuthResults.stream()
            .filter(result -> Objects.nonNull(result.getValue()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  private Task<Authorization> authorizeSeatForRelationshipMap(SalesListUrn relationshipMapUrn, SeatUrn seatUrn) {
    return _aclServiceDispatcher.checkAccessDecision(seatUrn, PolicyType.ACCOUNT_MAP, relationshipMapUrn,
        AccessAction.READ, SubResourceType.LIST_ENTITY).map(accessDecision -> {
      if (accessDecision == AccessDecision.ALLOWED) {
        return new Authorization().setStatus(AuthorizationStatus.APPROVED);
      } else {
        return new Authorization().setStatus(AuthorizationStatus.DENIED);
      }
    });
  }
}
