package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.exceptions.InternalException;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssRecentViewsDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.RecentViewEntity;
import com.linkedin.salesrecentactivities.RecentActivity;
import com.linkedin.salesrecentactivities.RecentActivityType;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.util.stream.Collectors.*;

/**
 * Service for get and upsert of Sales RecentActivities
 * <AUTHOR>
 */
public class SalesRecentActivitiesService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesRecentActivitiesService.class);
  private static final int NUMBER_OF_RECORDS_ALLOWED = 5;
  private final LssRecentViewsDB _lssRecentViewsDB;

  public SalesRecentActivitiesService(LssRecentViewsDB lssRecentViewsDB) {
    _lssRecentViewsDB = lssRecentViewsDB;
  }

  /**
   * Create or Update RecentViews in LSS RecentViews DB
   * @param seatUrn seatUrn as part of key
   * @param recentActivityType type of the recent activity
   * @param contractUrn needed to create new record when no RecentViews record is present
   * @param recentActivity Sales RecentViewEntity item to be put in new record
   * @return True if RecentViews is created or modified, False if not
   */
  public Task<Boolean> upsertRecentViews(@NonNull SeatUrn seatUrn, @NonNull RecentActivityType recentActivityType,
      @NonNull ContractUrn contractUrn, @NonNull RecentActivity recentActivity) {
    com.linkedin.sales.espresso.RecentViewEntity espressoRecentViewEntity;
    try {
      espressoRecentViewEntity = constructEspressoRecentViewEntity(recentActivity);
    } catch (InternalException | IOException e) {
      String errorMsg = "Error converting input data to Espresso RecentViewEntity";
      LOG.error(errorMsg);
      return Task.failure(new InternalException(errorMsg));
    }
    return lookupEspressoRecentViews(seatUrn.toString(), recentActivityType.name()).flatMap(recentViewsResult -> {
      com.linkedin.sales.espresso.RecentViews recentViews = recentViewsResult;
      if (recentViews != null) {
        // update existing RecentViews record
        List<com.linkedin.sales.espresso.RecentViewEntity> entities = new ArrayList<>(recentViews.entities);
        List<String> entityList = entities.stream().map(entity -> entity.entity.toString()).collect(toList());
        // if the entity already exists update the timestamp
        int existingEntityIdx = entityList.indexOf(espressoRecentViewEntity.entity.toString());
        if (existingEntityIdx > -1) {
          entities.get(existingEntityIdx).lastViewedTime = espressoRecentViewEntity.lastViewedTime;
        } else if (entities.size() >= NUMBER_OF_RECORDS_ALLOWED) {
          // if the entity does not exist so the oldest entry needs to be replaced
          entities.sort(Comparator.comparing((RecentViewEntity entity) -> entity.lastViewedTime).reversed());
          entities.set(entities.size() - 1, espressoRecentViewEntity);
        } else {
          // add the new entity
          entities.add(espressoRecentViewEntity);
        }
        com.linkedin.sales.espresso.RecentViews newRecentViews = new com.linkedin.sales.espresso.RecentViews();
        newRecentViews.contractUrn = recentViews.contractUrn;
        newRecentViews.entities = entities;
        return _lssRecentViewsDB.updateRecentViews(seatUrn.toString(), recentActivityType.name(), newRecentViews);
      } else {
        // create new RecentViews record
        recentViews = new com.linkedin.sales.espresso.RecentViews();
        recentViews.contractUrn = contractUrn.toString();
        recentViews.entities = new ArrayList<>();
        recentViews.entities.add(espressoRecentViewEntity);
        return _lssRecentViewsDB.createRecentView(seatUrn.toString(), recentActivityType.name(), recentViews);
      }
    }).onFailure(t ->
        LOG.error("Failed to create or update RecentViews for seatUrn: {}, entityType: {}", seatUrn, recentActivityType, t));
  }

  /**
   * Lookup Espresso RecentViews
   * @param seat for the user
   * @param entityType of the recentviews data
   * @return Espresso recent views data
   */
  private Task<com.linkedin.sales.espresso.RecentViews> lookupEspressoRecentViews(@NonNull String seat,
      @NonNull String entityType) {
    return _lssRecentViewsDB.getRecentViews(seat, entityType).recoverWith(t -> {
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.value(null);
      } else {
        LOG.error("Failed to lookup recent view for seatUrn: {}, entityType: {}, {}", seat, entityType, t.getMessage());
        return Task.failure(t);
      }
    });
  }

  /**
   * Get RecentView for a seat holder of the given entity type
   * @param seat seatUrn as part of key
   * @param entityType entityType as part of key
   * @return RecentViews record or null if not present
   */

  public Task<List<RecentActivity>> getEspressoRecentViews(@NonNull SeatUrn seat,
      @NonNull RecentActivityType entityType) {
    return _lssRecentViewsDB.getRecentViews(seat.toString(), entityType.name())
        .map(espressoRecentViews -> constructSalesRecentActivityList(entityType.name(), espressoRecentViews))
        .recoverWith(t -> {
          LOG.warn("Failed to get recent activity for seatUrn: {}, entityType: {}, {}", seat, entityType, t.getMessage());
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.value(Collections.emptyList());
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Delete Views for a seat holder of the given entity type
   * @param seat seatUrn as part of key
   * @param entityType entityType as part of key
   * @return Boolean if deletion was successful.
   */
  public Task<Boolean> deleteEspressoRecentViews(@NonNull SeatUrn seat, @NonNull RecentActivityType entityType) {
    return _lssRecentViewsDB.deleteRecentViews(seat.toString(), entityType.name()).recoverWith(t -> {
          LOG.error("Failed to delete activity for seatUrn: {}, entityType: {}, {}", seat, entityType, t.getMessage());
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.value(Boolean.FALSE);
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Construct the Sales RecentViews from Espresso RecentViews
   * @param entityType type of viewed entity
   * @param espressoRecentViews Espresso RecentViews
   * @return Sales RecentViews
   */
  private List<RecentActivity> constructSalesRecentActivityList(@NonNull String entityType,
      @NonNull com.linkedin.sales.espresso.RecentViews espressoRecentViews) {
    return espressoRecentViews.entities.stream().map(entity -> {
      RecentActivity recentActivity = new RecentActivity();
      String stringEspressoEntity = entity.entity.toString();
      try {
        switch (RecentActivityType.valueOf(entityType)) {
          case PROFILE:
            MemberUrn memberUrn = MemberUrn.deserialize(stringEspressoEntity);
            recentActivity.setEntity(RecentActivity.Entity.createWithMemberUrn(memberUrn));
            break;
          case COMPANY:
            OrganizationUrn organizationUrn = OrganizationUrn.deserialize(stringEspressoEntity);
            recentActivity.setEntity(RecentActivity.Entity.createWithOrganizationUrn(organizationUrn));
            break;
          case LIST:
            SalesListUrn salesListUrn = SalesListUrn.deserialize(stringEspressoEntity);
            recentActivity.setEntity(RecentActivity.Entity.createWithSalesListUrn(salesListUrn));
            break;
          default:
            // Do nothing
        }
      } catch (URISyntaxException ex) {
        LOG.error("Failed to convert string {} to Urn", stringEspressoEntity);
      }
      recentActivity.setLastActionTime(entity.lastViewedTime);
      return recentActivity;
    }).filter(RecentActivity::hasEntity).collect(Collectors.toList());
  }

  /**
   * Construct espresso RecentViewEntity from Sales RecentViewEntity
   * @param recentActivity sales RecentActivity
   * @return returns the espresso RecentViewEntity
   */
  private com.linkedin.sales.espresso.RecentViewEntity constructEspressoRecentViewEntity(RecentActivity recentActivity)
      throws IOException, InternalException {
    com.linkedin.sales.espresso.RecentViewEntity espressoRecentViewEntity =
        new com.linkedin.sales.espresso.RecentViewEntity();
    RecentActivity.Entity entity = recentActivity.getEntity();
    if (entity.isMemberUrn()) {
      espressoRecentViewEntity.entity = entity.getMemberUrn().toString();
    } else if (entity.isOrganizationUrn()) {
      espressoRecentViewEntity.entity = entity.getOrganizationUrn().toString();
    } else if (entity.isSalesListUrn()) {
      espressoRecentViewEntity.entity = entity.getSalesListUrn().toString();
    } else {
      throw new InternalException("Entity format is incorrect");
    }
    espressoRecentViewEntity.lastViewedTime = recentActivity.getLastActionTime();
    return espressoRecentViewEntity;
  }
}
