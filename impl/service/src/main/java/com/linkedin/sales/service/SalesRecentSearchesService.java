package com.linkedin.sales.service;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.ByteString;
import com.linkedin.data.DataMap;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lssSearch.SearchQuery;
import com.linkedin.sales.service.utils.RestliUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.RestConstants;
import com.linkedin.restli.internal.common.DataMapConverter;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssRecentViewsDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.RecentSearchesV2;
import com.linkedin.salesrecentactivities.RecentActivityType;
import com.linkedin.salesrecentactivities.RecentSearch;
import com.linkedin.salesrecentactivities.RecentSearchKey;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.activation.MimeTypeParseException;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service for get, create and update of Sales Recent Searches
 * <AUTHOR>
 */
public class SalesRecentSearchesService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesRecentSearchesService.class);

  private final LssRecentViewsDB _lssRecentViewsDB;

  private static final String RECENT_ACTIVITY_TYPE_PEOPLE_SEARCH = "PEOPLE_SEARCH";
  private static final String RECENT_SEARCH_TYPE_PEOPLE_SEARCH = "PEOPLE_SEARCH";
  // Recent activity type doesn't have character constraint, however the search type in recent search DB has max
  // length of 18. It's hard to change this limit at DB level. We cannot directly use the type longer than 18,
  // for example, PEOPLE_SEARCH_QUERY_SHARE.
  // Here, we are doing a mapping, if the type exceeds the 18 character limit, we will map it to a short type name.
  private static final String RECENT_ACTIVITY_TYPE_PEOPLE_SEARCH_QUERY_SHARE = "PEOPLE_SEARCH_QUERY_SHARE";
  private static final String RECENT_SEARCH_TYPE_PEOPLE_SEARCH_QUERY_SHARE = "P_SEARCH_SHARE";

  private static final Map<String, String> RECENT_ACTIVITY_TYPE_TO_RECENT_SEARCH_TYPE = ImmutableMap.of(
      RECENT_ACTIVITY_TYPE_PEOPLE_SEARCH, RECENT_SEARCH_TYPE_PEOPLE_SEARCH,
      RECENT_ACTIVITY_TYPE_PEOPLE_SEARCH_QUERY_SHARE, RECENT_SEARCH_TYPE_PEOPLE_SEARCH_QUERY_SHARE
  );

  public SalesRecentSearchesService(LssRecentViewsDB lssRecentViewsDB) {
    _lssRecentViewsDB = lssRecentViewsDB;
  }

  /**
   * Create RecentSearches in LSS RecentSearches table
   * @param recentSearch Sales RecentSearch item to be put in new record
   * @return unique id representing the recent search record
   */
  public Task<Long> createRecentSearches(@NonNull RecentSearch recentSearch) {
    RecentSearchesV2 espressoRecentSearch = constructEspressoRecentSearch(recentSearch);
    RecentActivityType searchType = recentSearch.getRecentActivityType();
    SeatUrn seatUrn = recentSearch.getSeat();
    String type = RECENT_ACTIVITY_TYPE_TO_RECENT_SEARCH_TYPE.get(searchType.toString());
    return _lssRecentViewsDB.createRecentSearch(seatUrn, espressoRecentSearch,
        type).recoverWith(t -> {
      if (ExceptionUtils.isTooManyRequestsException(t)) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_429_TOO_MANY_REQUESTS, t.getMessage()));
      } else {
        return Task.failure(t);
      }
    });
  }

  /**
   * Update RecentSearches in LSS RecentSearches table
   * @param recentSearchKey key including the seat urn, contract urn and recent search id to lookup the record
   * @param recentSearch Sales RecentSearch item to be put in new record
   * @return unique id representing the recent search record
   */
  public Task<Long> updateRecentSearches(@NonNull RecentSearchKey recentSearchKey, @NonNull RecentSearch recentSearch) {
    if (!(recentSearch.getId().equals(recentSearchKey.getId())
    && recentSearch.getRecentActivityType() == recentSearchKey.getRecentActivityType())) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Mismatch in the values present in key and recent search payload"));
    }
    RecentSearchesV2 espressoRecentSearch = constructEspressoRecentSearch(recentSearch);
    RecentActivityType searchType = recentSearch.getRecentActivityType();
    String type = RECENT_ACTIVITY_TYPE_TO_RECENT_SEARCH_TYPE.get(searchType.toString());
    long recentSearchId = recentSearchKey.getId();
    SeatUrn seatUrn = recentSearchKey.getSeat();
    Task<RecentSearchesV2> findRecentSearchTask = _lssRecentViewsDB.getRecentSearch(recentSearchId, seatUrn, type);
    return findRecentSearchTask.flatMap(foundRecentSearch -> {
      if (ContractUrn.deserialize(foundRecentSearch.contractUrn.toString()).equals(recentSearch.getContract())) {
        return _lssRecentViewsDB.updateRecentSearch(recentSearchKey.getSeat(),
            espressoRecentSearch, type, recentSearchId)
            .onFailure(
                t -> LOG.error("Failed to update RecentSearches for id: {} seatUrn: {} searchType: {}",
                    recentSearchId, recentSearchKey.getSeat(), type, t));
      } else {
        return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "Incorrect contract urn sent in the request key"));
      }
    }).recoverWith(t -> {
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, t.getMessage()));
      } else if (ExceptionUtils.isTooManyRequestsException(t)) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_429_TOO_MANY_REQUESTS, t.getMessage()));
      } else {
        return Task.failure(t);
      }
    });
  }

  /**
   * Get top list of RecentSearch for a seat holder of the given entity type
   * @param seatUrn seat urn of the user
   * @param recentActivityType Optional type (enum) of the recent activity. Eg. People or Company Search
   * @param start start index of the results
   * @param count total number of results starting from start index
   * @return list of RecentSearch records
   */
  public Task<List<RecentSearch>> getEspressoRecentSearchesBySeat(@NonNull SeatUrn seatUrn,
      @Nullable RecentActivityType recentActivityType, int start, int count) {
    String type = recentActivityType == null ? null
        : RECENT_ACTIVITY_TYPE_TO_RECENT_SEARCH_TYPE.get(recentActivityType.toString());;
    return _lssRecentViewsDB.getRecentSearches(seatUrn, type, start, count)
        .map(espressoRecentSearches -> constructRecentSearchListFromEspressoRecentSearches(
            espressoRecentSearches, seatUrn.toString()))
        .recoverWith(t -> {
          LOG.error("Failed to get list of recent search from espresso searches for seatUrn: {}, searchType: {}, {}",
              seatUrn, recentActivityType.name(), t.getMessage());
          return Task.failure(t);
        });
  }

  /**
   * Get RecentSearch for a seat holder of the given entity type lookup by id
   * Updating lastSearchedTime to the latest time when the search record is looked up. This is taken care by the side-effect operation.
   * @param recentSearchKey key including the seat urn, contract urn and recent search id to lookup the record
   * @param recentActivityType type (enum) of the recent activity. Eg. People or Company Search
   * @return RecentSearch record
   */
  public Task<RecentSearch> getEspressoRecentSearch(@NonNull RecentSearchKey recentSearchKey,
      @NonNull RecentActivityType recentActivityType) {
    long recentSearchId = recentSearchKey.getId();
    SeatUrn seatUrn = recentSearchKey.getSeat();
    String type = RECENT_ACTIVITY_TYPE_TO_RECENT_SEARCH_TYPE.get(recentActivityType.toString());
    return _lssRecentViewsDB.getRecentSearch(recentSearchId, seatUrn, type)
        .map(espressoRecentSearch -> {
          String stringEspressoSearchQuery = espressoRecentSearch.searchQuery.toString();
          return constructRecentSearch(stringEspressoSearchQuery, recentSearchId,
              type, seatUrn.toString(),
              espressoRecentSearch.contractUrn.toString(), espressoRecentSearch.lastSearchedTime);
        })
        .withSideEffect(recentSearch -> {
          // Update the timestamp whenever we read a specific recent search
          recentSearch.setLastSearchedTime(System.currentTimeMillis());
          RecentSearchesV2 espressoRecentSearch = constructEspressoRecentSearch(recentSearch);
          return _lssRecentViewsDB.updateRecentSearch(recentSearchKey.getSeat(),
              espressoRecentSearch, type, recentSearchId)
              .onFailure(
                  t -> LOG.error("Failed to update timestamp of RecentSearches for id: {} seatUrn: {} searchType: {}",
                      recentSearchId, recentSearchKey.getSeat(), recentActivityType, t));
        })
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, t.getMessage()));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Delete Searches for a seat holder of the given entity type
   * @param seat seatUrn as part of key
   * @param entityType entityType as part of key
   * @return Boolean if deletion was successful.
   */
  public Task<Boolean> deleteEspressoRecentSearches(@NonNull SeatUrn seat, @NonNull RecentActivityType entityType) {
    return _lssRecentViewsDB.deleteRecentSearches(seat.toString(), entityType.name()).recoverWith(t -> {
      LOG.error("Failed to delete searches for seatUrn: {}, entityType: {}, {}", seat, entityType, t.getMessage());
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.value(Boolean.FALSE);
      } else {
        return Task.failure(t);
      }
    });
  }

  /**
   * Construct the Sales RecentSearch list from Espresso RecentSearches
   * @param espressoRecentSearches list of records containing id, search type and Espresso RecentSearches
   * @param seatUrn seat urn of the user in string format
   * @return RecentSearch list
   * @throws URISyntaxException when deserializing to Urn from string
   */
  private List<RecentSearch> constructRecentSearchListFromEspressoRecentSearches(
      @NonNull List<Pair<Pair<Long, String>, RecentSearchesV2>> espressoRecentSearches, @NonNull String seatUrn)
      throws URISyntaxException {
    List<RecentSearch> recentSearchesArray = new ArrayList<>(espressoRecentSearches.size());
    for (Pair<Pair<Long, String>, RecentSearchesV2> espressoRecentSearchPair : espressoRecentSearches) {
      String stringEspressoSearchQuery = espressoRecentSearchPair.getSecond().searchQuery.toString();
      Pair<Long, String> espressoKey = espressoRecentSearchPair.getFirst();
      RecentSearch recentSearch = constructRecentSearch(stringEspressoSearchQuery, espressoKey.getFirst(),
          espressoKey.getSecond(), seatUrn, espressoRecentSearchPair.getSecond().contractUrn.toString(),
          espressoRecentSearchPair.getSecond().lastSearchedTime);
      recentSearchesArray.add(recentSearch);
    }
    return recentSearchesArray;
  }

  /**
   * Construct the Sales RecentSearch Object
   * @param stringEspressoSearchQuery Espresso RecentSearches query in string format
   * @param recentSearchId unique id of the recent search record
   * @param searchType type of searched entity
   * @param seatUrn seat urn of the user in string format
   * @param contractUrn contract urn of the user in string format
   * @param lastSearchedTime time when the query was last searched
   * @return RecentSearch record
   * @throws URISyntaxException when deserializing to Urn from string
   */
  private RecentSearch constructRecentSearch(
      String stringEspressoSearchQuery, Long recentSearchId, @NonNull String searchType,
      @NonNull String seatUrn, @NonNull String contractUrn, long lastSearchedTime)
      throws URISyntaxException {
    DataMap dataMap = RestliUtils.deserializeToDataMap(stringEspressoSearchQuery);
    SearchQuery searchQuery = new SearchQuery(dataMap);
    RecentSearch recentSearch = new RecentSearch();
    recentSearch.setId(recentSearchId);
    recentSearch.setSeat(SeatUrn.deserialize(seatUrn));
    recentSearch.setContract(ContractUrn.deserialize(contractUrn));
    recentSearch.setLastSearchedTime(lastSearchedTime);
    switch (searchType) {
      case RECENT_SEARCH_TYPE_PEOPLE_SEARCH:
        recentSearch.setRecentActivityType(RecentActivityType.PEOPLE_SEARCH);
        recentSearch.setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery));
        break;
      case RECENT_SEARCH_TYPE_PEOPLE_SEARCH_QUERY_SHARE:
        recentSearch.setRecentActivityType(RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE);
        recentSearch.setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery));
        break;
      default:
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format("Search type %s is not supported", searchType));
    }
    return recentSearch;
  }

  /**
   * Construct espresso RecentSearch from Sales RecentViewEntity
   * @param recentSearch sales RecentSearch object
   * @return returns the espresso RecentSearches record
   */
  private RecentSearchesV2 constructEspressoRecentSearch(
      RecentSearch recentSearch) {
    RecentSearchesV2 espressoRecentSearch = new RecentSearchesV2();
    RecentSearch.SearchQuery searchQuery = recentSearch.getSearchQuery();
    boolean isSupportedPeopleSearchTypes = searchQuery.isPeopleSearchQuery()
        && (recentSearch.getRecentActivityType() == RecentActivityType.PEOPLE_SEARCH
        || recentSearch.getRecentActivityType() == RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE);
    if (isSupportedPeopleSearchTypes) {
      espressoRecentSearch.searchQuery = serializeSearchQueryToJson(searchQuery.getPeopleSearchQuery(), Long.MAX_VALUE);
    } else {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "search type is not supported");
    }
    espressoRecentSearch.lastSearchedTime = recentSearch.getLastSearchedTime();
    espressoRecentSearch.contractUrn = recentSearch.getContract().toString();
    return espressoRecentSearch;
  }

  /**
   * Serialize searchQuery object to a string
   * @param searchQuery search query object
   * @param maxSize Allowed size of the serialized search query string
   * @return returns the espresso RecentSearches
   */
  private static String serializeSearchQueryToJson(SearchQuery searchQuery, long maxSize) {
    try {
      ByteString
          byteString = DataMapConverter.dataMapToByteString(RestConstants.HEADER_VALUE_APPLICATION_JSON, searchQuery.data());
      // Usage of UTF-8 to make sure we serialize international characters correctly instead of using a default charset
      String json = byteString.asString(StandardCharsets.UTF_8);
      if (json != null && json.length() <= maxSize) {
        return json;
      }
    } catch (MimeTypeParseException | IOException e) {
      throw new RuntimeException(String.format("Could not convert searchQuery %s object to json", searchQuery), e);
    }
    String errMsg = String.format("serialized json is larger than %d", maxSize);
    LOG.error(errMsg);
    throw new RuntimeException(errMsg);
  }
}
