package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesCreditGrantsClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.salesbenefits.SalesSubscriptionBenefits;
import com.linkedin.talent.decorator.PathSpecSet;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collections;


/**
 * provides details of the benefits that members will lose on cancelling the subscription
 * <AUTHOR>
 */
public class SalesSubscriptionBenefitsService {
  static final long DEFAULT_UNUSED_INMAIL_CREDITS = 0;
  static final long DEFAULT_TOTAL_MESSAGE_THREADS = 0;
  static final long DEFAULT_SAVED_LEADS_COUNT = 0;
  static final long DEFAULT_SAVED_ACCOUNTS_COUNT = 0;
  static final PathSpec[] SALES_SEAT_ID_PATH_SPEC = new PathSpec[]{SalesSeat.fields().id()};
  static final PathSpec[] SALES_CONTRACT_PATH_SPEC =
      new PathSpec[]{SalesContract.fields().seatRoleAllocations()};

  private final SalesSeatClient _salesSeatClient;
  private final LssSavedLeadAccountDB _lssSavedLeadAccountDB;
  private final SalesCreditGrantsClient _salesCreditGrantsClient;
  private final SalesContractService _salesContractService;

  public SalesSubscriptionBenefitsService(SalesSeatClient salesSeatClient, LssSavedLeadAccountDB lssSavedLeadAccountDB,
      SalesCreditGrantsClient salesCreditGrantsClient, SalesContractService salesContractService) {
    _salesSeatClient = salesSeatClient;
    _lssSavedLeadAccountDB = lssSavedLeadAccountDB;
    _salesCreditGrantsClient = salesCreditGrantsClient;
    _salesContractService = salesContractService;
  }

  /**
   * Get details of sales subscription benefits that member will lose on cancelling the subscription.
   * The subscription benefits will be populated selectively based on the projection.
   * @param memberUrn member urn needed to query seatId
   * @param contractUrn needed to query by contract
   * @param projections needed to return data selectively
   * @return Subscription Benefits Schema with counts of each benefit as per projection
   */
  public Task<SalesSubscriptionBenefits> getSubscriptionBenefits(@NonNull MemberUrn memberUrn,
      @NonNull ContractUrn contractUrn, @NonNull PathSpecSet projections) {

    return getSeatUrn(memberUrn, contractUrn)
        .map(seatUrn -> {
      if (seatUrn == null) {
        throw new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "User does not have any assigned seat on Sales Navigator contract");
      }
      return seatUrn;
    }).flatMap(seatUrn -> {

      Task<Long> savedLeadsCountTask = Task.value(null);
      Task<Long> savedAccountsCountTask = Task.value(null);
      Task<Long> unUsedInMailCreditsTask = Task.value(null);
      Task<Long> messageThreadsCountTask = Task.value(null);

      if (projections.contains(SalesSubscriptionBenefits.fields().savedLeadsCount())) {
        savedLeadsCountTask = getSavedLeadsCount(contractUrn, seatUrn);
      }

      if (projections.contains(SalesSubscriptionBenefits.fields().savedAccountsCount())) {
        savedAccountsCountTask = getSavedAccountsCount(contractUrn, seatUrn);
      }

      if (projections.contains(SalesSubscriptionBenefits.fields().unusedInmailCredits())) {
        unUsedInMailCreditsTask = getUnusedInMailCreditsCount(contractUrn, seatUrn);
      }

      if (projections.contains(SalesSubscriptionBenefits.fields().messageThreadsCount())) {
        messageThreadsCountTask = getMessageThreadsCount();
      }

      return Task.par(savedLeadsCountTask, savedAccountsCountTask, unUsedInMailCreditsTask, messageThreadsCountTask)
          .map((savedLeadsCount, savedAccountsCount, unUsedInMailCredits, messageThreadsCount) ->
              new SalesSubscriptionBenefits()
                  .setSavedLeadsCount(savedLeadsCount, SetMode.IGNORE_NULL)
                  .setSavedAccountsCount(savedAccountsCount, SetMode.IGNORE_NULL)
                  .setUnusedInmailCredits(unUsedInMailCredits, SetMode.IGNORE_NULL)
                  .setMessageThreadsCount(messageThreadsCount, SetMode.IGNORE_NULL)
          );
    });
  }

  /**
   * Get the count of saved leads for contract and seat
   * todo: return the aggregated count in case of teams subscription
   * @param contractUrn contract urn
   * @param seatUrn seat urn
   * @return the count of saved leads
   */
  private Task<Long> getSavedLeadsCount(ContractUrn contractUrn, SeatUrn seatUrn) {
    return isProContract(contractUrn).flatMap(proContract ->
       proContract ? _lssSavedLeadAccountDB.getSavedLeadCountForSeat(seatUrn)
          .map(Long::valueOf) : Task.value(DEFAULT_SAVED_LEADS_COUNT)
    );
  }

  /**
   * Get the count of saved account for contract and seat
   * todo: return the aggregated count in case of teams subscription
   * @param contractUrn contract urn
   * @param seatUrn seat urn
   * @return the count of saved accounts
   */
  private Task<Long> getSavedAccountsCount(ContractUrn contractUrn, SeatUrn seatUrn) {
    return isProContract(contractUrn).flatMap(proContract ->
        proContract ? _lssSavedLeadAccountDB.getSavedAccountCountForSeat(seatUrn)
            .map(Long::valueOf) : Task.value(DEFAULT_SAVED_ACCOUNTS_COUNT)
    );
  }

  /**
   * Get count unused inMailCredits count from sales credit grants
   * todo: return the aggregated count in case of teams subscription
   * @param contractUrn contract urn
   * @param seatUrn seat urn
   * @return count of unused inMail credits
   */
  private Task<Long> getUnusedInMailCreditsCount(ContractUrn contractUrn, SeatUrn seatUrn) {
    return isProContract(contractUrn).flatMap(proContract ->
        proContract ? _salesCreditGrantsClient.getInmailCredits(contractUrn, seatUrn)
            .map(credits -> credits.stream()
                .map(credit -> (long) credit.getNumCreditsRemaining())
                .findFirst()
                .orElse(DEFAULT_UNUSED_INMAIL_CREDITS)) : Task.value(DEFAULT_UNUSED_INMAIL_CREDITS)
    );
  }

  /**
   * Get message threads count in Sales Navigator Inbox
   * todo: downstream call to messaging-mt to fetch number of message threads
   * @return count of message threads in Sales Navigator Inbox
   */
  private Task<Long> getMessageThreadsCount() {
    return Task.value(DEFAULT_TOTAL_MESSAGE_THREADS);
  }

  /**
   * Get seatUrn from member and contract
   * @param memberUrn member urn
   * @param contractUrn contract urn
   * @return seatUrn of the member
   */
  private Task<SeatUrn> getSeatUrn(MemberUrn memberUrn, ContractUrn contractUrn) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "getSalesSubscriptionBenefits");

    return _salesSeatClient.findByMember(memberUrn, Collections.singletonList(contractUrn), viewer, null,
        SALES_SEAT_ID_PATH_SPEC).map(seats -> {
      if (seats == null || seats.isEmpty()) {
        return null;
      } else {
        return new SeatUrn(seats.get(0).getId());
      }
    });
  }

  /**
   * Check if the contract is SN pro contract
   * @param contractUrn contract urn
   * @return boolean if the contract is pro
   */
  private Task<Boolean> isProContract(ContractUrn contractUrn) {
    return _salesContractService.getSalesContractById(contractUrn.getIdAsLong(), SALES_CONTRACT_PATH_SPEC)
        .map(contract -> contract
            .getSeatRoleAllocations()
            .stream()
            .anyMatch(allocation -> allocation.getRole() == com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER1
                && allocation.getAllocation() > 0));
  }
}
