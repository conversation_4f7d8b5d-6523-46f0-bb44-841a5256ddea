package com.linkedin.sales.service;

import avro.com.linkedin.events.common.lighthouse.products.ProductCollectFunnelStep;
import avro.com.linkedin.events.common.lighthouse.products.SaveProductRecord;
import avro.com.linkedin.events.lighthouse.coach.ProductCollectFunnelTrackingEvent;
import avro.com.linkedin.events.lighthouse.coach.innerProductCollectFunnelTrackingEvent.ProductCollectFunnelBodyWrapper;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.events.EventHeader;
import com.linkedin.events.MobileHeader;
import com.linkedin.events.UnifiedActionType;
import com.linkedin.events.UserRequestHeader;
import com.linkedin.events.common.Exception;
import com.linkedin.events.common.TrackingId;
import com.linkedin.events.federator.SalesActionEvent;
import com.linkedin.events.federator.SponsoredFlag;
import com.linkedin.events.federator.UnifiedAction;
import com.linkedin.events.federator.UnifiedActionEvent;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.tracker.TrackerUtils;
import com.linkedin.tracker.producer.TrackingProducer;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.UUID;

/**
 *  Service for sending tracking events and other tracking related functions
 */
public class TrackingService {
  private final String _machineName;
  private final String _envName;
  private final String _appName;

  private final TrackingProducer _trackingProducer;

  public TrackingService(TrackingProducer trackingProducer, String machineName, String envName, String appName) {
    _machineName = machineName;
    _envName = envName;
    _appName = appName;
    _trackingProducer = trackingProducer;
  }

  /**
   * Create and then send sales action event
   */
  public void createAndSendOutSalesActionTrackingEvent(@NonNull UnifiedAction action,
      @NonNull String target, @NonNull UnifiedActionType actionType,
      @NonNull Map<CharSequence, CharSequence> customProperties,
      @NonNull MemberUrn viewer, @NonNull SeatUrn viewerSeatUrn, @NonNull ContractUrn viewerContractUrn) {
    SalesActionEvent salesActionEvent =
        createSalesActionEvent(action, target, actionType, customProperties, viewer, viewerSeatUrn, viewerContractUrn);
    _trackingProducer.sendRecord(ServiceConstants.SALES_ACTION_EVENT_NAME, salesActionEvent);
  }

  /**
   * Create and then send product collect funnel tracking event
   */
  @SuppressFBWarnings({"SSCU_SUSPICIOUS_SHADED_CLASS_USE"})
  //Suppress SSCU_SUSPICIOUS_SHADED_CLASS_USE because spotbugs consider ProductCollectFunnelTrackingEvent from 3rd party
  //library by mistake
  public void createAndSendOutProductCollectFunnelSaveProductTrackingEvent(@NonNull SeatUrn actor, @NonNull String sessionId,
      @NonNull SellerIdentityProduct sellerIdentityProduct, boolean isError) {
    ProductCollectFunnelTrackingEvent productCollectFunnelTrackingEvent = new ProductCollectFunnelTrackingEvent();
    productCollectFunnelTrackingEvent.setHeader(generateEventHeader(actor));
    productCollectFunnelTrackingEvent.setRequestHeader(new UserRequestHeader());
    productCollectFunnelTrackingEvent.setTrackingId(convertUUIDToTrackingId(UUID.fromString(sessionId)));
    productCollectFunnelTrackingEvent.setFunnelStep(ProductCollectFunnelStep.SAVE_PRODUCT);
    productCollectFunnelTrackingEvent.setProductCollectFunnelBody(
        convertSellerIdentityProductToProductCollectFunnelBody(sellerIdentityProduct));
    if (isError) {
      Exception exception = new Exception();
      //Use generic error message to indicate a failure to save product
      exception.setMessage(ServiceConstants.PRODUCT_COLLECT_SAVE_ERROR_MESSAGE);
      productCollectFunnelTrackingEvent.setError(exception);
    }
    _trackingProducer.sendRecord(ServiceConstants.PRODUCT_COLLECT_FUNNEL_TRACKING_EVENT_NAME, productCollectFunnelTrackingEvent);
  }

  /**
   * Convert SellerIdentityProduct to ProductCollectFunnelBodyWrapper
   */
  @SuppressFBWarnings({"SSCU_SUSPICIOUS_SHADED_CLASS_USE"})
  //Suppress SSCU_SUSPICIOUS_SHADED_CLASS_USE because spotbugs consider SaveProductRecord from 3rd party
  //library by mistake
  private ProductCollectFunnelBodyWrapper convertSellerIdentityProductToProductCollectFunnelBody(
      @NonNull SellerIdentityProduct sellerIdentityProduct) {
    SaveProductRecord saveProductRecord = new SaveProductRecord();
    saveProductRecord.setName(sellerIdentityProduct.getProduct().toString());
    if (sellerIdentityProduct.hasProductUrl()) {
      saveProductRecord.setUrl(sellerIdentityProduct.getProductUrl().toString());
    }
    if (sellerIdentityProduct.hasDescription()) {
      saveProductRecord.setDescription(sellerIdentityProduct.getDescription());
    }
    if (sellerIdentityProduct.hasProductCategory()) {
      saveProductRecord.setCategory(sellerIdentityProduct.getProductCategory().toString());
    }
    ProductCollectFunnelBodyWrapper productCollectFunnelBodyWrapper = new ProductCollectFunnelBodyWrapper();
    productCollectFunnelBodyWrapper.setSaveProduct(saveProductRecord);
    return productCollectFunnelBodyWrapper;
  }

  /**
   * Generate general tracking event header
   * @param viewer viewer's memberUrn
   * @return EventHeader
   */
  @NonNull
  private EventHeader generateEventHeader(MemberUrn viewer) {
    EventHeader eventHeader = new EventHeader();
    Long viewerId = viewer.getMemberIdEntity();
    eventHeader.memberId = viewerId.intValue();
    eventHeader.viewerUrn = viewer.toString();
    eventHeader.time = System.currentTimeMillis();
    eventHeader.guid = TrackerUtils.createTrackingGuid();
    eventHeader.server = _machineName;
    eventHeader.service = _appName;
    eventHeader.environment = _envName;
    eventHeader.appName = _appName;
    return eventHeader;
  }

  /**
   * Generate general tracking event header
   * @param seatUrn seatUrn
   * @return EventHeader
   */
  @NonNull
  private EventHeader generateEventHeader(SeatUrn seatUrn) {
    EventHeader eventHeader = new EventHeader();
    eventHeader.setActorUrn(seatUrn.toString());
    eventHeader.setTime(System.currentTimeMillis());
    eventHeader.setGuid(TrackerUtils.createTrackingGuid());
    eventHeader.setServer(_machineName);
    eventHeader.setService(_appName);
    eventHeader.setEnvironment(_envName);
    eventHeader.setAppName(_appName);
    return eventHeader;
  }
  /**
   * Generate default unified action event for tracking 1.0 events
   */
  @NonNull
  private UnifiedActionEvent createUnifiedActionEvent(@Nullable UnifiedAction action, @Nullable String target,
      Map<CharSequence, CharSequence> customProperties, @NonNull MemberUrn viewer) {
    UnifiedActionEvent unifiedActionEvent = new UnifiedActionEvent();
    unifiedActionEvent.action = action;
    unifiedActionEvent.requestHeader = new UserRequestHeader();
    unifiedActionEvent.header = generateEventHeader(viewer);
    unifiedActionEvent.pageNumber = ServiceConstants.DEFAULT_NUMBER;
    unifiedActionEvent.position = ServiceConstants.TRACKING_EVENT_DEFAULT_POSITION;
    unifiedActionEvent.contextId = UUID.randomUUID().toString();
    unifiedActionEvent.sponsoredFlag = SponsoredFlag.ORGANIC;
    unifiedActionEvent.target = target;
    unifiedActionEvent.customProperties = customProperties;
    return unifiedActionEvent;
  }

  /**
   * Generate event that tracks action data
   */
  @NonNull
  public SalesActionEvent createSalesActionEvent(@NonNull UnifiedAction action,
      @NonNull String target, @NonNull UnifiedActionType actionType,
      @NonNull Map<CharSequence, CharSequence> customProperties,
      @NonNull MemberUrn viewer, @NonNull SeatUrn viewerSeatUrn, @NonNull ContractUrn viewerContractUrn) {
    SalesActionEvent salesActionEvent = new SalesActionEvent();
    salesActionEvent.header = generateEventHeader(viewer);
    salesActionEvent.requestHeader = new UserRequestHeader();
    salesActionEvent.mobileHeader = new MobileHeader();
    salesActionEvent.viewerUrn = Urn.createFromTuple(ServiceConstants.SALES,
        viewer.getMemberIdEntity(), viewerSeatUrn.getSeatIdEntity(), viewerContractUrn.getContractIdEntity()).toString();
    salesActionEvent.pageKey = ServiceConstants.NULL_PAGE_KEY;
    salesActionEvent.moduleKey = ServiceConstants.NULL_MODULE_KEY;
    salesActionEvent.action = createUnifiedActionEvent(action, target, customProperties, viewer);
    salesActionEvent.actionType = actionType;
    salesActionEvent.accountUrn = Urn.createFromTuple(ServiceConstants.SALES_ACCOUNT, -1L, -1L).toString();

    return salesActionEvent;
  }

  /**
   * copied from sales-api-frontend TrackingUtils
   * Convert UUID to TrackingId
   * @param uuid UUID
   * @return
   */
  private static TrackingId convertUUIDToTrackingId(UUID uuid) {
    ByteBuffer byteBuffer = ByteBuffer.wrap(new byte[16]);
    byteBuffer.putLong(uuid.getMostSignificantBits());
    byteBuffer.putLong(uuid.getLeastSignificantBits());
    return new TrackingId(byteBuffer.array());
  }
}
