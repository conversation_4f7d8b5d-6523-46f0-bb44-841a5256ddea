package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * ACL service class for salesApiAccountMaps. It extends the `BaseAclService` class and implements the `checkOwnership`
 * method and overrides getPermittedRoles.
 */
public class AccountMapAclService extends ListAclService {
  private static final Logger LOG = LoggerFactory.getLogger(AccountMapAclService.class);
  public AccountMapAclService(LssSharingDB lssSharingDB, LssListDB lssListDB, SalesSeatClient salesSeatClient) {
    super(lssSharingDB, lssListDB, salesSeatClient);
  }

  @Override
  protected Set<ShareRole> getPermittedRoles(@NonNull AccessAction accessAction, @NonNull SubResourceType subResourceType,
      @NonNull Urn requester) {
    if (accessAction == AccessAction.READ && subResourceType == SubResourceType.LIST_ENTITY && UrnUtils.isSeatUrn(requester)) {
      return ServiceConstants.WRITER_ROLES;
    }
    return ServiceConstants.ACCOUNT_MAP_SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(accessAction);
  }

  @Override
  Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction) {
    return checkAccessDecision(requester, policyType, resourceUrn, accessAction, SubResourceType.NONE);
  }

  Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction,
      @NonNull SubResourceType subResourceType) {
    // check if seat urn type otherwise return access denied
    if (!ServiceConstants.SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES.contains(requester.getEntityType())) {
      return Task.value(AccessDecision.DENIED);
    }
    Set<ShareRole> permittedRoles = getPermittedRoles(accessAction, subResourceType, requester);
      return _lssSharingDB.getPoliciesByResource(resourceUrn, policyType, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)
      .flatMap(paginatedPairsTry -> {
        if (paginatedPairsTry.getResult().isEmpty()) {
          //if no permitted roles found, allow it if the requester is the creator of the account map
          return checkOwnership(resourceUrn, requester).map(isCreator -> isCreator ? AccessDecision.ALLOWED : AccessDecision.DENIED);
        }
        // a subject can only have a single sharing policy (i.e. a single share role) for a given resource and resource type,
        // thus no need handle duplicated map keys here.
        Map<Urn, ShareRole> subjectUrns = paginatedPairsTry.getResult().stream().collect(
            Collectors.toMap(Pair::getFirst, Pair::getSecond));
        ShareRole requesterShareRole = subjectUrns.get(requester);
        if (requesterShareRole == null) {
          return Task.value(AccessDecision.DENIED);
        }
        return subjectUrns.containsKey(requester) ? Task.value(AccessDecision.ALLOWED) : Task.value(AccessDecision.DENIED);
      }).recover(t -> {
        LOG.warn("fail to check access decision for requester: {}, policyType: {}, resource: {}, accessAction: {}. "
                + "Fall back to DENIED.", requester, policyType, resourceUrn, accessAction, t);
        return AccessDecision.DENIED;
      });
  }
}
