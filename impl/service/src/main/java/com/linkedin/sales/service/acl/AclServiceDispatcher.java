package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Dispatcher class for ACL service. Used for dispatching permission check to corresponding ACL service based on
 * resource type
 * <AUTHOR>
 */
public class AclServiceDispatcher {
  private static final Logger LOG = LoggerFactory.getLogger(AclServiceDispatcher.class);

  private final ListAclService _listAclService;

  private final AccountMapAclService _accountMapAclService;

  private final NoteAclService _noteAclService;

  private final LsiMetricsReportAclService _lsiMetricsReportAclService;

  public AclServiceDispatcher(ListAclService listAclService, AccountMapAclService accountMapAclService,
      NoteAclService noteAclService, LsiMetricsReportAclService lsiMetricsReportAclService) {
    _listAclService = listAclService;
    _accountMapAclService = accountMapAclService;
    _noteAclService = noteAclService;
    _lsiMetricsReportAclService = lsiMetricsReportAclService;
  }

  /**
   * dispatch the permission check to corresponding ACL service based on resource type.
   * @param requester the requester. Service should check all subjects that the requester have.
   * @param policyType the policy(resource) type of given access.
   * @param resourceUrn urn of resource
   * @param accessAction type of action that the requester wishes to perform
   * @return AccessDecision of the request
   */
  public Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction) {
    if (policyType == PolicyType.LEAD_LIST || policyType == PolicyType.ACCOUNT_LIST) {
      return _listAclService.checkAccessDecision(requester, policyType, resourceUrn, accessAction);
    } else if (policyType == PolicyType.ACCOUNT_MAP) {
      return _accountMapAclService.checkAccessDecision(requester, policyType, resourceUrn, accessAction);
    } else if (policyType == PolicyType.NOTE) {
      return _noteAclService.checkAccessDecision(requester, policyType, resourceUrn, accessAction);
    } else if (policyType == PolicyType.LSI_METRICS_REPORT) {
      return _lsiMetricsReportAclService.checkAccessDecision(requester, policyType, resourceUrn, accessAction);
    } else {
      String errorMsg = String.format("unsupported policy type: %s", policyType);
      LOG.warn(errorMsg);
      return Task.failure(new RuntimeException(errorMsg));
    }
  }

  /**
   * Dispatch the permission check to corresponding ACL service based on resource type and subresource type.
   * Currently, this method only works for ACCOUNT_MAP policy type. Subresource type is ignored for other policy types.
   * @param requester the requester. Service should check all subjects that the requester have.
   * @param policyType the policy(resource) type of given access.
   * @param resourceUrn urn of resource
   * @param accessAction type of action that the requester wishes to perform
   * @param subResourceType subresource type. NONE means the access is checked on resource level and universally applies
   *                        to all sub resources, if any.
   * @return AccessDecision of the request
   */
  public Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction,
      @NonNull SubResourceType subResourceType) {
    if (policyType == PolicyType.ACCOUNT_MAP) {
      return _accountMapAclService.checkAccessDecision(requester, policyType, resourceUrn, accessAction, subResourceType);
    }
    if (subResourceType != SubResourceType.NONE) {
      LOG.warn(
          "subresource level access check is not supported for policyType: {}, falling back to resource level access check",
          policyType);
    }
    return checkAccessDecision(requester, policyType, resourceUrn, accessAction);
  }
}
