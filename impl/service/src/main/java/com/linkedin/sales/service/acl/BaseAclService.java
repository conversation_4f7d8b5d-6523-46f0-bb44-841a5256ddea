package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Base ACL service class. Extend this class and implement method `checkOwnership` to provide customized logic
 * for checking access decision.
 * <AUTHOR>
 */
abstract class BaseAclService {
  private static final Logger LOG = LoggerFactory.getLogger(BaseAclService.class);

  protected final LssSharingDB _lssSharingDB;
  protected final SalesSeatClient _salesSeatClient;

  protected static final PathSpec[] SALES_SEAT_PATH_SPECS = new PathSpec[]{
      SalesSeat.fields().contract(),
      SalesSeat.fields().roles()
  };

  BaseAclService(LssSharingDB lssSharingDB, SalesSeatClient salesSeatClient) {
    _lssSharingDB = lssSharingDB;
    _salesSeatClient = salesSeatClient;
  }

  /**
   * check access decision using sharing platform.
   * @param requester the requester. Service should check all subjects that the requester have.
   * @param policyType the policy(resource) type of given access.
   * @param resourceUrn urn of resource
   * @param accessAction type of action that the requester wishes to perform
   * @return AccessDecision of the request
   */
  Task<AccessDecision> checkSharingAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction) {

    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
            "checkSharingAccess");

    // check if seat urn type otherwise return access denied
    if (!UrnUtils.isSeatUrn(requester)) {
      return Task.value(AccessDecision.DENIED);
    }

    return _salesSeatClient.getSeat(UrnUtils.createSeatUrn(requester).getSeatIdEntity(), null, viewer, SALES_SEAT_PATH_SPECS).flatMap(seat -> {
      Set<ShareRole> permittedRoles = getPermittedRoles(accessAction);
      return _lssSharingDB.getPoliciesByResource(resourceUrn, policyType, permittedRoles, 0,
          ServiceConstants.SHARING_POLICY_GET_ALL_COUNT).map(paginatedPairs -> {

        Set<Urn> subjectUrns = paginatedPairs.getResult().stream().map(Pair::getFirst).collect(Collectors.toSet());
        // for now, since we can only share to seatUrn or contractUrn, we just need to check if subjectUrns contains
        // requester or the contract
        ContractUrn contractUrn = seat.getContract();
        return subjectUrns.contains(requester) || subjectUrns.contains(contractUrn) ? AccessDecision.ALLOWED
            : AccessDecision.DENIED;
      });
    }).recover(t -> {
      LOG.warn("fail to check access decision for seat: {}, policyType: {}, resource: {}, action: {}", requester,
          policyType, resourceUrn, accessAction, t);
      return AccessDecision.DENIED;
    });
  }

  /**
   * Get permitted roles for access action
   * @param accessAction type of action that the requester wishes to perform
   * @return permitted share roles
   */
  protected Set<ShareRole> getPermittedRoles(@NonNull AccessAction accessAction) {
    return ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(accessAction);
  }

  /**
   * Get permitted roles based on both access action and sub resource type.
   * @param accessAction type of action that the requester wishes to perform
   * @param subResourceType subresource type. This field is not respected in base implementation.
   * @param requester the requester
   * @return permitted share roles
   */
  protected Set<ShareRole> getPermittedRoles(@NonNull AccessAction accessAction, @NonNull SubResourceType subResourceType,
      @NonNull Urn requester) {
    return getPermittedRoles(accessAction);
  }

  /**
   * check access decision using both the information from sharing platform and resource ownership
   * @param requester the requester. Service should check all subjects that the requester have.
   * @param policyType the policy(resource) type of given access.
   * @param resourceUrn urn of resource
   * @param accessAction type of action that the requester wishes to perform
   * @return AccessDecision of the request
   */
  Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction) {
    return checkOwnership(resourceUrn, requester).flatMap(isOwner -> {
      if (isOwner) {
        return Task.value(AccessDecision.ALLOWED);
      }
      // gather information from sharing platform and resource platform
      return checkSharingAccessDecision(requester, policyType, resourceUrn, accessAction);
    }).recover(t -> {
      LOG.warn("fail to check access decision for seat: {}, policyType: {}, resource: {}, accessAction: {}", requester,
          policyType, resourceUrn, accessAction, t);
      return AccessDecision.DENIED;
    });
  }

  /**
   * check if the seat is the owner of the resource. Implement this method to provide customized logic for checking
   * access decision.
   * @param resourceUrn urn of resource on which you want to check permission
   * @param requesterUrn urn of requester on which you want to check permission
   * @return true if the requester is the owner of the resource
   */
  abstract Task<Boolean> checkOwnership(Urn resourceUrn, Urn requesterUrn);
}
