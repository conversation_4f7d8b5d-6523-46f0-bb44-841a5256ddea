package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.service.utils.UrnUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * ACL service class for salesList. It extends the `BaseAclService` class and implements the `checkOwnership`
 * method. Used for checking access permission for salesList.
 * <AUTHOR>
 */
public class ListAclService extends BaseAclService {
  private static final Logger LOG = LoggerFactory.getLogger(ListAclService.class);

  private final LssListDB _lssListDB;

  public ListAclService(LssSharingDB lssSharingDB, LssListDB lssListDB, SalesSeatClient salesSeatClient) {
    super(lssSharingDB, salesSeatClient);
    _lssListDB = lssListDB;
  }

  @Override
  Task<Boolean> checkOwnership(Urn resourceUrn, Urn requesterUrn) {
    if (!UrnUtils.isSeatUrn(requesterUrn)) {
      LOG.error("Entity Type not supported to check ownership");
      return Task.value(FALSE);
    }

    return _lssListDB.getList(resourceUrn.getIdAsLong()).map(foundList -> foundList.creatorSeatId == UrnUtils.createSeatUrn(requesterUrn).getSeatIdEntity())
        .recover(t -> {
          LOG.warn("fail to check list ownership for list {}, seat {}", resourceUrn, requesterUrn, t);
          return FALSE;
        });
  }
}
