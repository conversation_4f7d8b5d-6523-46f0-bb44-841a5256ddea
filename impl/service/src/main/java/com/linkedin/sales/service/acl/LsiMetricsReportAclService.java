package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.salesinsights.SalesInsightsMetricsReportClient;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * ACL service class for LsiMetricsReport. It extends the `BaseAclService` class and implements `checkSharingAccessDecision` and `checkOwnership`
 * method. Used for checking access permission for LSI metric reports.
 */
public class LsiMetricsReportAclService extends BaseAclService {
  private static final Logger LOG = LoggerFactory.getLogger(LsiMetricsReportAclService.class);

  private final SalesInsightsMetricsReportClient _salesInsightsMetricsReportClient;

  public LsiMetricsReportAclService(LssSharingDB lssSharingDB, SalesSeatClient salesSeatClient,
      SalesInsightsMetricsReportClient salesInsightsMetricsReportClient) {
    super(lssSharingDB, salesSeatClient);
    _salesInsightsMetricsReportClient = salesInsightsMetricsReportClient;
  }

  @Override
  Task<AccessDecision> checkSharingAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resourceUrn,
      @NonNull AccessAction accessAction) {
    EnterpriseProfileApplicationInstanceUrn profileApplicationInstanceUrn = UrnUtils.createEnterpriseProfileApplicationInstanceUrn(requester);
    EnterpriseApplicationInstanceUrn enterpriseApplicationInstanceUrn =
        UrnUtils.createEnterpriseApplicationInstanceUrn(profileApplicationInstanceUrn.getAccountIdEntity(),
            profileApplicationInstanceUrn.getApplicationInstanceIdEntity());
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(accessAction);
    try {
      return _lssSharingDB.getPoliciesByResource(resourceUrn, policyType, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT).flatMap(paginatedPairs -> {
          Set<Urn> subjectUrns = paginatedPairs.getResult().stream().map(Pair::getFirst).collect(Collectors.toSet());
          // check whether or not shared with individual or shared the enterprise application instance urn
          // sharing with enterprise application urn is similar to sharing with entire contract
          return subjectUrns.contains(requester) || subjectUrns.contains(enterpriseApplicationInstanceUrn) ? Task.value(
              AccessDecision.ALLOWED) : Task.value(AccessDecision.DENIED);
        });
    } catch (RuntimeException t) {
      LOG.warn("fail to check access decision for requester: {}, policyType: {}, resource: {}, action: {}", requester,
          policyType, resourceUrn, accessAction, t);
      return Task.value(AccessDecision.DENIED);
    }
  }

  @Override
  Task<Boolean> checkOwnership(Urn resourceUrn, Urn requesterUrn) {
    EnterpriseProfileApplicationInstanceUrn profileApplicationInstanceUrn =
        UrnUtils.createEnterpriseProfileApplicationInstanceUrn(requesterUrn);
    EnterpriseApplicationInstanceUrn applicationInstanceUrn =
        UrnUtils.createEnterpriseApplicationInstanceUrn(profileApplicationInstanceUrn.getAccountIdEntity(),
            profileApplicationInstanceUrn.getApplicationInstanceIdEntity());
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(profileApplicationInstanceUrn.getAccountIdEntity(),
        profileApplicationInstanceUrn.getProfileIdEntity());
    try {
      return _salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(resourceUrn))
          .map(report -> applicationInstanceUrn.equals(report.getEnterpriseApplicationInstance()) && profileUrn.equals(report.getEnterpriseProfile()));
    } catch (RuntimeException t) {
      LOG.warn("fail to check metrics report ownership for report {}, requester {}", resourceUrn, requesterUrn, t);
      return Task.value(FALSE);
    }
  }
}
