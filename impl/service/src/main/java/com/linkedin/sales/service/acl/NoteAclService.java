package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.service.utils.UrnUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * ACL service class for salesNote. It extends the `BaseAclService` class and implements the `checkOwnership`
 * method. Used for checking access permission for salesNote.
 */
public class NoteAclService extends BaseAclService {
  private static final Logger LOG = LoggerFactory.getLogger(NoteAclService.class);

  public NoteAclService(LssSharingDB lssSharingDB, SalesSeatClient salesSeatClient) {
    super(lssSharingDB, salesSeatClient);
  }

  @Override
  Task<Boolean> checkOwnership(Urn resourceUrn, Urn requesterUrn) {
    if (!SalesNoteUrn.ENTITY_TYPE.equals(resourceUrn.getEntityType())) {
      LOG.error("Entity Type not supported to check ownership");
      return Task.value(Boolean.FALSE);
    }

    if (!UrnUtils.isSeatUrn(requesterUrn)) {
      LOG.error("Entity Type not supported to check ownership");
      return Task.value(Boolean.FALSE);
    }

    SeatUrn noteSeatUrn = resourceUrn.getEntityKey().getAs(0, SeatUrn.class);
    return Task.value(UrnUtils.createSeatUrn(requesterUrn).equals(noteSeatUrn));
  }
}