package com.linkedin.sales.service.acl;

/**
 * Sub-level sharing resource type.
 */
public enum SubResourceType {
  /**
   * Sub sharing resource type is list entity. Applicable for {@link com.linkedin.salessharing.PolicyType#LEAD_LIST},
   * {@link com.linkedin.salessharing.PolicyType#ACCOUNT_LIST} and {@link com.linkedin.salessharing.PolicyType#ACCOUNT_MAP}.
   */
  LIST_ENTITY,

  /**
   * No sub sharing resource type. When used for checking access decision, it means the access is checked on resource level.
   */
  NONE
}
