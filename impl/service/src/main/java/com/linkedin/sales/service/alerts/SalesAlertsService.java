package com.linkedin.sales.service.alerts;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssAlertDB;
import com.linkedin.salesalerts.Alert;
import com.linkedin.salesalerts.AlertEntity;
import com.linkedin.salesalerts.AlertKey;
import com.linkedin.salesalerts.AlertOrdering;
import com.linkedin.salesalerts.AlertType;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service class for SalesAlertsResource
 */
public class SalesAlertsService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesAlertsService.class);
  private final LssAlertDB _lssAlertDB;

  public SalesAlertsService(LssAlertDB lssAlertDB) {
    _lssAlertDB = lssAlertDB;
  }

  /**
   * delete an alert
   * @param alertKey key including the entity urn and alert Id to uniquely identify the alert to delete
   * @return the update response to tell if the delete succeeds
   */
  public Task<UpdateResponse> deleteAlert(@NonNull AlertKey alertKey) {
    Urn entityUrn = convertAlertEntityToGeneralUrn(alertKey.getEntity());
    return _lssAlertDB.deleteAlert(entityUrn, alertKey.getAlertId())
        .map(httpStatus -> new UpdateResponse(httpStatus));
  }

  /**
   * find all or some types of the alerts on this entity, sorted by given order
   * @param alertEntity the entity that the alerts belong to, a union of memberUrn, organizationUrn or salesListUrn
   * @param alertTypes the set of alert types to get, by default, an empty array will be put and all types will be fetched
   * @param alertOrdering the sorting criteria of the retrieved alerts, by default, sort by creation time in descending order
   * @param start paging start
   * @param count paging count
   * @return the collection of alerts under this entity
   */
  public Task<BasicCollectionResult<Alert>> findByCriteria(@NonNull AlertEntity alertEntity,
      @NonNull AlertType[] alertTypes, @NonNull AlertOrdering alertOrdering, int start, int count) {
    Urn entityUrn = convertAlertEntityToGeneralUrn(alertEntity);
    Set<String> typeSet = Arrays.stream(alertTypes).map(AlertType::name).collect(Collectors.toSet());
    return _lssAlertDB.findByCriteria(entityUrn, typeSet, start, count).map(pairs -> {
      List<Alert> alerts = pairs.stream()
          .map(pair -> convertEspressoAlertToAlert(alertEntity, pair.getFirst(), pair.getSecond()))
          .filter(Objects::nonNull)
          .collect(Collectors.toList());
      return new BasicCollectionResult<>(alerts, alerts.size());
    });
  }

  @VisibleForTesting
  private static Urn convertAlertEntityToGeneralUrn(AlertEntity alertEntity) {
    if (alertEntity.isMember()) {
      return alertEntity.getMember();
    } else if (alertEntity.isOrganization()) {
      return alertEntity.getOrganization();
    } else if (alertEntity.isSalesList()) {
      return alertEntity.getSalesList();
    } else {
      throw new IllegalArgumentException("Unexpected urn type in " + alertEntity);
    }
  }

  @Nullable
  private Alert convertEspressoAlertToAlert(AlertEntity alertEntity, Long alertId,
      com.linkedin.sales.espresso.EntityAlert alertBody) {
    Urn alertContent;
    AlertType alertType = AlertType.valueOf(alertBody.alertType.toString());
    try {
      alertContent = new Urn(alertBody.contentUrn.toString());
    } catch (URISyntaxException e) {
      LOG.error("Fail to create the contentUrn for {}", alertBody, e);
      return null;
    }

    return new Alert().setEntity(alertEntity)
        .setAlertId(alertId)
        .setAlertType(alertType)
        .setContent(alertContent)
        .setCreatedAt(alertBody.createdTime);
  }
}
