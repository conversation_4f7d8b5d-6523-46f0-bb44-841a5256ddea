package com.linkedin.sales.service.autofinder;

import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.lssSearch.AutoLeadFinderPlayType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.service.utils.AutoFinderUtils;
import com.linkedin.salesautofinder.AccountPlay;
import com.linkedin.salesautofinder.AccountPlayKey;
import com.linkedin.util.Pair;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class AccountPlayService {
  private final LssAutoFinderDB _lssAutoFinderDB;
  private static final Logger LOG = LoggerFactory.getLogger(AccountPlayService.class);

  public AccountPlayService(LssAutoFinderDB lssAutoFinderDB) {
    _lssAutoFinderDB = lssAutoFinderDB;
  }

  /**
   * Create a new AccountPlay
   * @param batchCreateRequest
   * @return create response to indicate if the creations succeeded
   */
  public Task<List<CreateResponse>> batchCreate(
      BatchCreateRequest<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay> batchCreateRequest) {
    List<Task<CreateResponse>> createTasks =
        batchCreateRequest.getInput().stream().map(this::createAccountPlay).collect(Collectors.toList());
    return Task.par(createTasks)
        .recover(throwable -> batchCreateRequest.getInput().stream()
            .map(accountPlay -> new CreateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR))
            .collect(Collectors.toList()));
  }

  /**
   * Find AccountPlay by account
   * @param seatUrn
   * @param organizationUrn
   * @param start
   * @param count
   * @return
   */
  public Task<BasicCollectionResult<AccountPlay>> findByAccount(SeatUrn seatUrn, OrganizationUrn organizationUrn,
      int start, int count) {
    return _lssAutoFinderDB.getAccountPlays(seatUrn, organizationUrn, start, count)
        .map(accountPlaysById -> new BasicCollectionResult<>(accountPlaysById.stream()
            .map(idToAccountPlay -> convertToRestModel(seatUrn, organizationUrn, idToAccountPlay))
            .collect(Collectors.toList()), accountPlaysById.size()));
  }

  /**
   * Batch update AccountPlay
   * @param batchPatchRequest
   * @return
   */
  public Task<Map<AccountPlayKey, UpdateResponse>> batchPartialUpdate(
      BatchPatchRequest<ComplexResourceKey<AccountPlayKey, EmptyRecord>, AccountPlay> batchPatchRequest) {
    List<Task<Pair<AccountPlayKey, UpdateResponse>>> updateTasks = new ArrayList<>();
    batchPatchRequest.getData().forEach((key, patch) -> {
      try {
        AccountPlay updatedAccountPlay = new AccountPlay();
        PatchApplier.applyPatch(updatedAccountPlay, patch);
        updateTasks.add(updateAccountPlay(key.getKey(), updatedAccountPlay));
      } catch (DataProcessingException e) {
        updateTasks.add(Task.value(new Pair(key.getKey(), new UpdateResponse(HttpStatus.S_400_BAD_REQUEST))));
      }
    });

    return Task.par(updateTasks)
        .map(results -> results.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)))
        .recover(throwable ->
            batchPatchRequest.getData().keySet().stream().map(key ->
                    new Pair<>(key.getKey(), new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR)))
                .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)));
  }

  private com.linkedin.sales.espresso.AccountPlay convertToEspressoModel(AccountPlay accountPlay, boolean isInsert) {
    com.linkedin.sales.espresso.AccountPlay espressoAccountPlay = isInsert
        ? initializeAccountPlayForCreate() : new com.linkedin.sales.espresso.AccountPlay();

    if (accountPlay.hasPlayType()) {
      espressoAccountPlay.setPlayType(accountPlay.getPlayType().name());
    }
    if (accountPlay.hasPersonaLocalId()) {
      espressoAccountPlay.setPersonaLocalId(accountPlay.getPersonaLocalId());
    }
    if (accountPlay.hasCurrentLeads()) {
      espressoAccountPlay.setCurrentLeads(AutoFinderUtils.getMemberIds(accountPlay.getCurrentLeads()));
    }
    if (accountPlay.hasPastLeads()) {
      espressoAccountPlay.setPastLeads(AutoFinderUtils.getMemberIds(accountPlay.getPastLeads()));
    }
    return espressoAccountPlay;
  }

  private AccountPlay convertToRestModel(SeatUrn seatUrn, OrganizationUrn organizationUrn,
      Pair<Integer, com.linkedin.sales.espresso.AccountPlay> espressoAccountPlay) {
    AccountPlay restliAccountPlay = new AccountPlay();
    restliAccountPlay.setSeat(seatUrn);
    restliAccountPlay.setOrganization(organizationUrn);
    restliAccountPlay.setId(espressoAccountPlay.getFirst());
    restliAccountPlay.setPlayType(AutoLeadFinderPlayType.valueOf(espressoAccountPlay.getSecond().getPlayType().toString()));
    restliAccountPlay.setPersonaLocalId(espressoAccountPlay.getSecond().getPersonaLocalId(), SetMode.REMOVE_IF_NULL);
    restliAccountPlay.setCurrentLeads(AutoFinderUtils.getMemberUrnArray(espressoAccountPlay.getSecond().getCurrentLeads()));
    restliAccountPlay.setPastLeads(AutoFinderUtils.getMemberUrnArray(espressoAccountPlay.getSecond().getPastLeads()));
    return restliAccountPlay;
  }

  private Task<CreateResponse> createAccountPlay(AccountPlay accountPlay) {
    return _lssAutoFinderDB.createAccountPlay(accountPlay.getSeat(), accountPlay.getOrganization(),
            convertToEspressoModel(accountPlay, true))
        .map(CreateResponse::new)
        .recover(e -> {
          if (e instanceof RestLiServiceException) {
            return new CreateResponse((RestLiServiceException) e);
          } else {
            return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, e.getMessage()));
          }
        });
  }

  private Task<Pair<AccountPlayKey, UpdateResponse>> updateAccountPlay(AccountPlayKey key, AccountPlay accountPlay) {
    return _lssAutoFinderDB.partialUpdateAccountPlay(key.getSeat(), key.getOrganization(),
            key.getId(), convertToEspressoModel(accountPlay, false))
        .map(httpStatus -> new Pair<>(key, new UpdateResponse(httpStatus)))
        .recover(e -> {
          if (e instanceof RestLiServiceException) {
            String errMsg = String.format("Failed to update accountPlay %s", accountPlay);
            LOG.error(errMsg, e);
            return new Pair<>(key, new UpdateResponse(((RestLiServiceException) e).getStatus()));
          } else {
            String errMsg = String.format("Failed to update accountPlay %s", accountPlay);
            LOG.error(errMsg, e);
            return new Pair<>(key, new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
          }
        });
  }

  private com.linkedin.sales.espresso.AccountPlay initializeAccountPlayForCreate() {
    com.linkedin.sales.espresso.AccountPlay accountPlay = new com.linkedin.sales.espresso.AccountPlay();
    accountPlay.setCurrentLeads(Collections.emptyList());
    accountPlay.setPastLeads(Collections.emptyList());
    return accountPlay;
  }
}
