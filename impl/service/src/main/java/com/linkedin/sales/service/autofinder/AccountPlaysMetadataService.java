package com.linkedin.sales.service.autofinder;

import com.linkedin.common.MemberUrnArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.AccountPlaysMetadataV2;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.AutoFinderUtils;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.salesautofinder.AccountPlaysMetadata;
import com.linkedin.salesautofinder.AccountPlaysMetadataKey;
import com.linkedin.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Nullable;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * Service that is responsible for management of AccountPlaysMetadata
 */
public class AccountPlaysMetadataService {
  private static final Logger LOG = LoggerFactory.getLogger(AccountPlaysMetadataService.class);
  private final LssAutoFinderDB _lssAutoFinderDB;
  private final LixService _lixService;
  public AccountPlaysMetadataService(LssAutoFinderDB lssAutoFinderDB, LixService lixService) {
    _lssAutoFinderDB = lssAutoFinderDB;
    _lixService = lixService;
  }

  /**
   * Create a new AccountPlaysMetadata
   * @param accountPlaysMetadata
   * @return create response to indicate if the creation succeeds
   */
  public Task<CreateResponse> create(AccountPlaysMetadata accountPlaysMetadata) {
    Task<Boolean> isSwitchToAccountPlaysMetadataV2TableEnabledTask = _lixService.isEEPLixEnabledForSeat(accountPlaysMetadata.getSeat(),
        LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE);
    return isSwitchToAccountPlaysMetadataV2TableEnabledTask.flatMap(isSwitchToV2TableEnabled -> {
      if (Boolean.TRUE.equals(isSwitchToV2TableEnabled)) {
        // Currently only support dismiss one lead at a time
        if (accountPlaysMetadata.getDismissedLeads().size() != 1) {
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Can only dismiss one lead at once");
        }
        return _lssAutoFinderDB.createAccountPlaysMetadataV2(accountPlaysMetadata.getSeat(),
                    accountPlaysMetadata.getOrganization(), accountPlaysMetadata.getDismissedLeads().get(0))
                .map(CreateResponse::new);
      } else {
        return _lssAutoFinderDB.createAccountPlaysMetadata(accountPlaysMetadata.getSeat(),
                  accountPlaysMetadata.getOrganization(), convertToEspressoModel(accountPlaysMetadata, true))
              .map(CreateResponse::new);
      }
    });
  }

  /**
   * Get AccountPlaysMetadata by key
   * @param key resource key
   * @return AccountPlaysMetadata
   */
  public Task<AccountPlaysMetadata> get(AccountPlaysMetadataKey key) {
    Task<Boolean> isSwitchToAccountPlaysMetadataV2TableEnabledTask = _lixService.isEEPLixEnabledForSeat(key.getSeat(),
        LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE);
    return isSwitchToAccountPlaysMetadataV2TableEnabledTask.flatMap(isSwitchToV2TableEnabled -> {
      if (Boolean.TRUE.equals(isSwitchToV2TableEnabled)) {
        return getFromAccountPlaysMetadataV2WithDataMigration(key.getSeat(), key.getOrganization());
      } else {
        return _lssAutoFinderDB.getAccountPlaysMetadata(key.getSeat(), key.getOrganization())
                .map(accountPlaysMetadata ->
                        convertToRestModel(key.getSeat(), key.getOrganization(), accountPlaysMetadata))
                .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t));
      }
    });
  }

  /**
   * Partially update AccountPlaysMetadata by key
   * @param key resource key
   * @param patch patch request
   * @param isUndoDismiss true if the request is to undo dismiss a lead, false if the request is to dismiss a lead
   * @param memberUrn the member to be dismissed or undismissed, null when the request is only for updating lastRunAt
   * @return update response to indicate if the update succeeds
   */
  public Task<UpdateResponse> partialUpdate(AccountPlaysMetadataKey key, PatchRequest<AccountPlaysMetadata> patch,
                                            boolean isUndoDismiss, @Nullable MemberUrn memberUrn) {
    AccountPlaysMetadata updatedAccountPlaysMetaData = new AccountPlaysMetadata();
    try {
      PatchApplier.applyPatch(updatedAccountPlaysMetaData, patch);
    } catch (DataProcessingException e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Error applying patch during partialUpdate");
    }

    Task<Boolean> isSwitchToAccountPlaysMetadataV2TableEnabledTask = _lixService.isEEPLixEnabledForSeat(key.getSeat(),
        LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE);
    return isSwitchToAccountPlaysMetadataV2TableEnabledTask.flatMap(isSwitchToV2TableEnabled -> {
      if (Boolean.TRUE.equals(isSwitchToV2TableEnabled)) {
        if (isUndoDismiss) {
          return _lssAutoFinderDB.deleteAccountPlaysMetadataV2(key.getSeat(), key.getOrganization(), memberUrn)
                .map(UpdateResponse::new);
        } else {
          return _lssAutoFinderDB.createAccountPlaysMetadataV2(key.getSeat(), key.getOrganization(), memberUrn)
                .map(UpdateResponse::new);
        }
      } else {
        return _lssAutoFinderDB.partialUpdateAccountPlaysMetadata(key.getSeat(), key.getOrganization(),
                  convertToEspressoModel(updatedAccountPlaysMetaData, false))
              .map(UpdateResponse::new);
      }
    });
  }

  // Get from AccountPlaysMetadataV2 table and convert to AccountPlaysMetadata RestModel.
  // If empty, migrate all dismissed leads from AccountPlaysMetadata table.
  private Task<AccountPlaysMetadata> getFromAccountPlaysMetadataV2WithDataMigration(SeatUrn seatUrn, OrganizationUrn organizationUrn) {
    Task<Map<OrganizationUrn, List<Pair<MemberUrn, com.linkedin.sales.espresso.AccountPlaysMetadataV2>>>> getAccountPlaysMetadataV2sForSeatTask =
            _lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(seatUrn);
    return getAccountPlaysMetadataV2sForSeatTask.flatMap(organizationUrnMetadataV2Map -> {
      // getAccountPlaysMetadataV2ForSeat returns max 15 records so we only use it to check if dismissed leads were migrated
      // to AccountPlaysMetadataV2 table. Then use getAccountPlaysMetadataV2 to get all dismissed leads for the organization.
      if (!organizationUrnMetadataV2Map.isEmpty()) {
        return _lssAutoFinderDB.getAccountPlaysMetadataV2(seatUrn, organizationUrn).flatMap(memberAndMetadataV2Pairs -> {
          if (memberAndMetadataV2Pairs.isEmpty()) {
            LOG.info("No dismissed leads found for organization {} for seat {}", organizationUrn, seatUrn);
            return Task.value(null);
          } else {
            return Task.value(convertAccountPlaysMetadataV2sToAccountPlaysMetadataRestModel(seatUrn, organizationUrn, memberAndMetadataV2Pairs));
          }
        })
        .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t));
      } else {
        Task<Map<OrganizationUrn, com.linkedin.sales.espresso.AccountPlaysMetadata>> getAccountPlaysMetadataForSeatTask =
                _lssAutoFinderDB.getAccountPlaysMetadataForSeat(seatUrn);
        return getAccountPlaysMetadataForSeatTask.flatMap(organizationUrnAccountPlaysMetadataMap -> {
                  Map<OrganizationUrn, Set<MemberUrn>> organizationToDismissedMemberUrns = organizationUrnAccountPlaysMetadataMap.entrySet().stream()
                      // After migrate to AccountPlaysMetadataV2 table, will not support storing AccountPlaysMetadata only with lastRunAt
                      .filter(entry -> entry.getValue().getDismissedLeads() != null && !entry.getValue().getDismissedLeads().isEmpty())
                      .collect(Collectors.toMap(Map.Entry::getKey,
                              entry -> new HashSet<>(AutoFinderUtils.getMemberUrnArray(entry.getValue().getDismissedLeads()))
                      ));
                  if (organizationToDismissedMemberUrns.isEmpty()) {
                    return Task.value(null);
                  } else {
                    Task<Map<OrganizationUrn, Map<MemberUrn, HttpStatus>>> createAccountPlaysMetadataV2Task =
                        _lssAutoFinderDB.createAccountPlaysMetadataV2s(seatUrn, organizationToDismissedMemberUrns);
                    return createAccountPlaysMetadataV2Task.map(responseMap -> {
                      Map<OrganizationUrn, Set<MemberUrn>> failedDismissedLeads = extractDismissedLeadsFailedMigration(
                          organizationToDismissedMemberUrns, responseMap);
                      // Check if all dismissed leads are successfully migrated
                      if (failedDismissedLeads.isEmpty()) {
                        return convertToRestModel(seatUrn, organizationUrn, organizationUrnAccountPlaysMetadataMap
                                .getOrDefault(organizationUrn, null));
                      } else {
                        StringBuilder errorMessage = new StringBuilder();
                        errorMessage.append(String.format("Failed to migrated AccountPlaysMetadata for seat %s ", seatUrn));
                        failedDismissedLeads.forEach((key, value) ->
                            errorMessage.append(String.format("in organization: %s, members: %s;", key, value)));
                        throw new RestLiServiceException(HttpStatus.S_206_PARTIAL_CONTENT, errorMessage.toString());
                      }
                    });
                  }
                })
                .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t));
      }
    })
    .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t));
  }

  private com.linkedin.sales.espresso.AccountPlaysMetadata convertToEspressoModel(AccountPlaysMetadata accountPlaysMetadata,
      boolean isInsert) {
    com.linkedin.sales.espresso.AccountPlaysMetadata espressoAccountPlaysMetadata =
        new com.linkedin.sales.espresso.AccountPlaysMetadata();

    if (accountPlaysMetadata.hasLastRunAt()) {
      espressoAccountPlaysMetadata.setLastRunTimestamp(accountPlaysMetadata.getLastRunAt());
    }
    if (accountPlaysMetadata.hasDismissedLeads()) {
      espressoAccountPlaysMetadata.setDismissedLeads(AutoFinderUtils.getMemberIds(accountPlaysMetadata.getDismissedLeads()));
    } else if (isInsert) {
      espressoAccountPlaysMetadata.setDismissedLeads(Collections.emptyList());
    }
    return espressoAccountPlaysMetadata;
  }

  private AccountPlaysMetadata convertToRestModel(SeatUrn seatUrn, OrganizationUrn organizationUrn,
      com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata) {
    if (accountPlaysMetadata == null) {
      return null;
    }
    AccountPlaysMetadata accountPlaysMetadataRestli = new AccountPlaysMetadata();
    accountPlaysMetadataRestli.setSeat(seatUrn);
    accountPlaysMetadataRestli.setOrganization(organizationUrn);
    accountPlaysMetadataRestli.setLastRunAt(accountPlaysMetadata.getLastRunTimestamp());
    accountPlaysMetadataRestli.setDismissedLeads(AutoFinderUtils.getMemberUrnArray(accountPlaysMetadata.getDismissedLeads()));
    return accountPlaysMetadataRestli;
  }

  private AccountPlaysMetadata convertAccountPlaysMetadataV2sToAccountPlaysMetadataRestModel(SeatUrn seatUrn, OrganizationUrn organizationUrn,
          List<Pair<MemberUrn, AccountPlaysMetadataV2>> memberAndMetadataV2Pairs) {
    AccountPlaysMetadata accountPlaysMetadataRestli = new AccountPlaysMetadata();
    accountPlaysMetadataRestli.setSeat(seatUrn);
    accountPlaysMetadataRestli.setOrganization(organizationUrn);

    // Sort AccountPlaysMetadataV2s response entities by createdTime in ascending order
    Pair<MemberUrn, com.linkedin.sales.espresso.AccountPlaysMetadataV2> lastDismissed = memberAndMetadataV2Pairs
            .stream()
            .max(Comparator.comparingLong(pair -> pair.getSecond().getCreatedTime()))
            .orElse(null);
    if (lastDismissed == null || lastDismissed.getSecond() == null) {
      String errorMessage = String.format("No AccountPlaysMetadataV2 found for seat %s organization %s", seatUrn, organizationUrn);
      throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, errorMessage);
    }
    List<MemberUrn> dismissedLeads = memberAndMetadataV2Pairs.stream()
            .map(Pair::getFirst)
            .collect(Collectors.toList());

    // Use the largest createdTime among stored dismissed leads as the lastRunAt
    accountPlaysMetadataRestli.setLastRunAt(lastDismissed.getSecond().getCreatedTime());
    accountPlaysMetadataRestli.setDismissedLeads(new MemberUrnArray(dismissedLeads));
    return accountPlaysMetadataRestli;
  }

  private Map<OrganizationUrn, Set<MemberUrn>> extractDismissedLeadsFailedMigration(
          Map<OrganizationUrn, Set<MemberUrn>> organizationToDismissedMemberUrns,
          Map<OrganizationUrn, Map<MemberUrn, HttpStatus>> responseMap) {
    return organizationToDismissedMemberUrns.entrySet().stream()
            .filter(entry -> entry.getValue().stream()
                    .anyMatch(memberUrn ->
                            !responseMap.get(entry.getKey()).containsKey(memberUrn)
                                || responseMap.get(entry.getKey()).get(memberUrn) != HttpStatus.S_201_CREATED))
            .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue().stream()
                            .filter(memberUrn ->
                                    !responseMap.get(entry.getKey()).containsKey(memberUrn)
                                        || responseMap.get(entry.getKey()).get(memberUrn) != HttpStatus.S_201_CREATED)
                            .collect(Collectors.toSet())
            ));
  }
}
