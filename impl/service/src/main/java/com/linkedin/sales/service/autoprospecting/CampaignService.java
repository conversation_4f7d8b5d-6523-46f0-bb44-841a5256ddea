package com.linkedin.sales.service.autoprospecting;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.service.buyerengagement.SalesSellerIdentityService;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSetting;
import proto.com.linkedin.common.ContractUrnBridge;
import proto.com.linkedin.common.OrganizationUrnBridge;
import proto.com.linkedin.common.SalesListUrnBridge;
import proto.com.linkedin.common.SeatUrnBridge;
import proto.com.linkedin.salesautoprospecting.Campaign;
import proto.com.linkedin.salesautoprospecting.CampaignKey;
import proto.com.linkedin.salesautoprospecting.CreateCampaignResponse;
import proto.com.linkedin.salesautoprospecting.FindCampaignBySeatAndContractResponse;
import proto.com.linkedin.salesautoprospecting.GetCampaignResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateCampaignResponse;


/**
 * Service for interacting with Campaign Espresso table.
 */
public class CampaignService {
  private static final Logger LOG = LoggerFactory.getLogger(CampaignService.class);

  private final LssAutoProspectingDB _lssAutoProspectingDB;

  private final SalesSellerIdentityService _salesSellerIdentityService;

  public CampaignService(LssAutoProspectingDB lssAutoProspectingDB,
      SalesSellerIdentityService salesSellerIdentityService) {
    _lssAutoProspectingDB = lssAutoProspectingDB;
    _salesSellerIdentityService = salesSellerIdentityService;
  }

  /**
   * Create a new Campaign espresso record.
   * @param campaign campaign object to create
   * @return CreateCampaignResponse the response object
   */
  public Task<CreateCampaignResponse> createCampaign(@Nonnull Campaign campaign) {
    Preconditions.checkNotNull(campaign, "Campaign cannot be null");
    Preconditions.checkNotNull(campaign.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkNotNull(campaign.getProductId(), "productId cannot be null");

    return _lssAutoProspectingDB.createCampaign(SeatUrnBridge.INSTANCE.fromProto(campaign.getSeatUrn()),
            convertProtoToEspressoCampaign(campaign))
        .map(campaignId -> CreateCampaignResponse.newBuilder()
            .setKey(CampaignKey.newBuilder().setSeatUrn(campaign.getSeatUrn()).setCampaignId(campaignId).build())
            .setValue(campaign)
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to create Campaign for seatUrn {}", campaign.getSeatUrn(), throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Partial update Campaign espresso record by given seat and campaign id with updated values.
   * @param campaignKey campaign key including seat and campaign id
   * @param campaign campaign object with updated values
   * @return PartialUpdateCampaignResponse the response object
   */
  public Task<PartialUpdateCampaignResponse> partialUpdateCampaign(@Nonnull CampaignKey campaignKey,
      @Nonnull Campaign campaign) {
    Preconditions.checkNotNull(campaignKey, "CampaignKey cannot be null");
    Preconditions.checkNotNull(campaignKey.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkArgument(campaignKey.getCampaignId() > 0, "CampaignId must be positive");

    return _lssAutoProspectingDB.partialUpdateCampaign(SeatUrnBridge.INSTANCE.fromProto(campaignKey.getSeatUrn()),
            campaignKey.getCampaignId(), convertProtoToEspressoCampaign(campaign))
        .map(updatedCampaign -> PartialUpdateCampaignResponse.newBuilder()
            .setValue(convertEspressoToProtoCampaign(updatedCampaign, campaignKey))
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to update Campaign for seatUrn {}, campaignId {}", campaignKey.getSeatUrn(),
              campaignKey.getCampaignId(), throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Get Campaign espresso record by given seat and campaign id.
   * @param campaignKey campaign key including seat and campaign id
   * @return GetCampaignResponse the response object
   */
  public Task<GetCampaignResponse> getCampaign(@Nonnull CampaignKey campaignKey) {
    Preconditions.checkNotNull(campaignKey, "CampaignKey cannot be null");
    Preconditions.checkNotNull(campaignKey.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkArgument(campaignKey.getCampaignId() > 0, "CampaignId must be positive");

    return _lssAutoProspectingDB.getCampaign(SeatUrnBridge.INSTANCE.fromProto(campaignKey.getSeatUrn()),
            campaignKey.getCampaignId())
        .map(retrievedCampaign -> GetCampaignResponse.newBuilder()
            .setValue(convertEspressoToProtoCampaign(retrievedCampaign, campaignKey))
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to get Campaign for seatUrn {}, campaignId {}", campaignKey.getSeatUrn(),
              campaignKey.getCampaignId(), throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Find Campaign espresso records by given seat and contract.
   * @param seatUrn seat that owns the campaigns
   * @param contractUrn contract that the campaigns belong to
   * @return FindCampaignBySeatAndContractResponse the response object
   */
  public Task<FindCampaignBySeatAndContractResponse> findCampaignsBySeatAndContract(
      @Nonnull proto.com.linkedin.common.SeatUrn seatUrn, @Nonnull proto.com.linkedin.common.ContractUrn contractUrn) {
    Preconditions.checkNotNull(seatUrn, "SeatUrn cannot be null");
    Preconditions.checkNotNull(contractUrn, "ContractUrn cannot be null");

    return findCampaignsBySeat(seatUrn).flatMap(protoCampaigns -> {
      if (protoCampaigns.isEmpty()) {
        return maybeCloneApSettingsToCampaign(seatUrn, contractUrn).map(campaignOpt -> {
          FindCampaignBySeatAndContractResponse.Builder responseBuilder =
              FindCampaignBySeatAndContractResponse.newBuilder();
          campaignOpt.ifPresent(responseBuilder::addValues);
          return responseBuilder.build();
        });
      }

      return Task.value(FindCampaignBySeatAndContractResponse.newBuilder().addAllValues(protoCampaigns).build());
    }).recoverWith(throwable -> {
      LOG.error("Failed to find Campaigns for seatUrn {}, contractUrn {}", seatUrn, contractUrn, throwable);
      return Task.failure(throwable);
    });
  }

  /**
   * Fetch campaigns associated with a specific seat.
   * @param seatUrn seat that owns the campaigns
   * @return task with a list of proto campaign objects
   */
  private Task<List<Campaign>> findCampaignsBySeat(@Nonnull proto.com.linkedin.common.SeatUrn seatUrn) {
    Preconditions.checkNotNull(seatUrn, "SeatUrn cannot be null");

    return _lssAutoProspectingDB.findCampaignsBySeat(SeatUrnBridge.INSTANCE.fromProto(seatUrn))
        .map(campaigns -> campaigns.stream().map(idToCampaignPair -> {
          CampaignKey campaignKey =
              CampaignKey.newBuilder().setSeatUrn(seatUrn).setCampaignId(idToCampaignPair.getFirst()).build();
          return convertEspressoToProtoCampaign(idToCampaignPair.getSecond(), campaignKey);
        }).collect(Collectors.toList()));
  }

  /**
   * Clones AutoProspecting settings from SeatSellerIdentity to a new Campaign.
   * This method fetches seller identity AP settings and creates a new campaign with those settings.
   *
   * @param seatUrn the seat URN of the user
   * @param contractUrn the contract URN to use for the campaign
   * @return task with an optional campaign object
   */
  @VisibleForTesting
  Task<Optional<Campaign>> maybeCloneApSettingsToCampaign(@Nonnull proto.com.linkedin.common.SeatUrn seatUrn,
      @Nonnull proto.com.linkedin.common.ContractUrn contractUrn) {
    Preconditions.checkNotNull(seatUrn, "SeatUrn cannot be null");
    Preconditions.checkNotNull(contractUrn, "ContractUrn cannot be null");

    // Convert proto URNs to common URNs for SalesSellerIdentityService
    com.linkedin.common.urn.ContractUrn commonContractUrn = ContractUrnBridge.INSTANCE.fromProto(contractUrn);
    com.linkedin.common.urn.SeatUrn commonSeatUrn = SeatUrnBridge.INSTANCE.fromProto(seatUrn);

    Task<com.linkedin.buyerengagement.SeatSellerIdentity> sellerIdentityTask =
        _salesSellerIdentityService.getSellerIdentity(commonContractUrn, commonSeatUrn);

    return sellerIdentityTask.flatMap(sellerIdentity -> {
      if (Objects.isNull(sellerIdentity) || !sellerIdentity.hasAutoProspectingSettings()) {
        return Task.value(Optional.empty());
      }

      return Objects.requireNonNull(sellerIdentity.getAutoProspectingSettings())
          .stream()
          .findFirst()
          .map(autoProspectingSetting -> createCampaign(
              convertAutoProspectingSettingToCampaign(seatUrn, contractUrn, autoProspectingSetting)).map(
              createResponse -> Optional.of(Campaign.newBuilder(createResponse.getValue())
                  .setCampaignId(createResponse.getKey().getCampaignId())
                  .build())))
          .orElseGet(() -> Task.value(Optional.empty()));
    }).recoverWith(throwable -> {
      LOG.error("Failed to clone AP settings to campaigns for seatUrn {}, contractUrn {}", seatUrn, contractUrn,
          throwable);
      return Task.value(Optional.empty());
    });
  }

  /**
   * Converts an AutoProspectingSetting to a Campaign object.
   *
   * @param seatUrn the seat URN to use for the campaign
   * @param contractUrn the contract URN to use for the campaign
   * @param apSetting the AutoProspectingSetting to convert
   * @return a Campaign object with data from the AutoProspectingSetting
   */
  @VisibleForTesting
  Campaign convertAutoProspectingSettingToCampaign(@Nonnull proto.com.linkedin.common.SeatUrn seatUrn,
      @Nonnull proto.com.linkedin.common.ContractUrn contractUrn, @Nonnull AutoProspectingSetting apSetting) {

    // Create a new Campaign object
    Campaign.Builder campaignBuilder = Campaign.newBuilder().setSeatUrn(seatUrn).setContractUrn(contractUrn);

    if (apSetting.getProductId() != null) {
      campaignBuilder.setProductId(apSetting.getProductId());
    }

    if (apSetting.getOnboardingCompletedTime() != null) {
      campaignBuilder.setCreatedTime(apSetting.getOnboardingCompletedTime());
    }

    if (apSetting.getLastModifiedTime() != null) {
      campaignBuilder.setLastModifiedTime(apSetting.getLastModifiedTime());
    }

    if (apSetting.getAccountListUrns() != null) {
      campaignBuilder.addAllAccountListUrns(apSetting.getAccountListUrns()
          .stream()
          .map(listUrn -> proto.com.linkedin.common.SalesListUrn.newBuilder().setListId(listUrn.getIdAsLong()).build())
          .collect(Collectors.toList()));
    }

    if (apSetting.getAccountUrns() != null) {
      campaignBuilder.addAllAccountUrns(apSetting.getAccountUrns()
          .stream()
          .map(orgUrn -> proto.com.linkedin.common.OrganizationUrn.newBuilder()
              .setOrganizationId(orgUrn.getIdAsLong())
              .build())
          .collect(Collectors.toList()));
    }

    return campaignBuilder.build();
  }

  private com.linkedin.sales.espresso.Campaign convertProtoToEspressoCampaign(Campaign campaign) {
    com.linkedin.sales.espresso.Campaign espressoCampaign = new com.linkedin.sales.espresso.Campaign();

    if (campaign.hasContractUrn()) {
      espressoCampaign.setContractUrn(ContractUrnBridge.INSTANCE.fromProto(campaign.getContractUrn()).toString());
    }

    if (campaign.hasProductId()) {
      espressoCampaign.setProductId(campaign.getProductId());
    }

    // set created and last modified time if present
    if (campaign.getCreatedTime() > 0) {
      espressoCampaign.setCreatedTime(campaign.getCreatedTime());
    }

    if (campaign.getLastModifiedTime() > 0) {
      espressoCampaign.setLastModifiedTime(campaign.getLastModifiedTime());
    }

    // Convert account list URNs
    espressoCampaign.setAccountListUrns(campaign.getAccountListUrnsList()
        .stream()
        .map(accountListUrn -> SalesListUrnBridge.INSTANCE.fromProto(accountListUrn).toString())
        .collect(Collectors.toList()));

    // Convert account URNs
    espressoCampaign.setAccountUrns(campaign.getAccountUrnsList()
        .stream()
        .map(accountUrn -> OrganizationUrnBridge.INSTANCE.fromProto(accountUrn).toString())
        .collect(Collectors.toList()));

    return espressoCampaign;
  }

  private Campaign convertEspressoToProtoCampaign(com.linkedin.sales.espresso.Campaign espressoCampaign,
      CampaignKey campaignKey) {
    Campaign.Builder campaignBuilder = Campaign.newBuilder();

    // Set key fields if provided
    if (campaignKey != null) {
      campaignBuilder.setSeatUrn(campaignKey.getSeatUrn());
      campaignBuilder.setCampaignId(campaignKey.getCampaignId());
    }

    campaignBuilder.setContractUrn(UrnUtils.createContractUrn(espressoCampaign.getContractUrn().toString()));

    if (espressoCampaign.getProductId() != null) {
      campaignBuilder.setProductId(espressoCampaign.getProductId().toString());
    }

    for (CharSequence accountListUrnString : espressoCampaign.getAccountListUrns()) {
      campaignBuilder.addAccountListUrns(UrnUtils.createProtoSalesListUrn(accountListUrnString.toString()));
    }

    for (CharSequence accountUrnString : espressoCampaign.getAccountUrns()) {
      campaignBuilder.addAccountUrns(UrnUtils.createOrganizationUrn(accountUrnString.toString()));
    }

    campaignBuilder.setCreatedTime(espressoCampaign.getCreatedTime());
    campaignBuilder.setLastModifiedTime(espressoCampaign.getLastModifiedTime());

    return campaignBuilder.build();
  }
}