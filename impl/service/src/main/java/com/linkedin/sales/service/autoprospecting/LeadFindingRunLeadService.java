package com.linkedin.sales.service.autoprospecting;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.ContractUrnBridge;
import proto.com.linkedin.common.MemberUrnBridge;
import proto.com.linkedin.salesautoprospecting.BatchCreateLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.FindByParamsLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLead;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadStatus;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunLeadResponse;
import si.RequestPagingContext;
import si.ResponsePagingContext;


/**
 * Service for interacting with LeadFindingRunLead Espresso table.
 */
public class LeadFindingRunLeadService {
  private static final Logger LOG = LoggerFactory.getLogger(LeadFindingRunLeadService.class);

  private final LssAutoProspectingDB _lssAutoProspectingDB;

  private static final Map<String, LeadFindingRunLeadStatus> STRING_TO_LEAD_FINDING_RUN_LEAD_STATUS_MAP =
      new ImmutableMap.Builder<String, LeadFindingRunLeadStatus>()
          .put("ACCEPTED_MANUALLY", LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_ACCEPTED_MANUALLY)
          .put("REJECTED_MANUALLY", LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_REJECTED_MANUALLY)
          .put("RECOMMENDED", LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_RECOMMENDED)
          .put("UNKNOWN", LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_UNKNOWN)
          .build();

  private static final Map<LeadFindingRunLeadStatus, String> LEAD_FINDING_RUN_LEAD_STATUS_TO_STRING_MAP =
      STRING_TO_LEAD_FINDING_RUN_LEAD_STATUS_MAP.entrySet().stream()
          .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

  private static final int DEFAULT_PAGING_START = 0;
  private static final int DEFAULT_PAGING_COUNT = 25;

  public LeadFindingRunLeadService(LssAutoProspectingDB lssAutoProspectingDB) {
   _lssAutoProspectingDB = lssAutoProspectingDB;
  }

  /**
   * Creates multiple LeadFindingRunLead records in a batch operation.
   * @param leadFindingRunLeadList The list of LeadFindingRunLead objects to be created.
   * @return A Task that resolves to a BatchCreateLeadFindingRunLeadResponse containing
   *         the results of the batch operation.
   * @throws NullPointerException If the input list is null.
   */
  public Task<BatchCreateLeadFindingRunLeadResponse> batchCreateLeadFindingRunLead(@Nonnull List<LeadFindingRunLead> leadFindingRunLeadList) {
    Preconditions.checkNotNull(leadFindingRunLeadList, "BatchCreateLeadFindingRunLeadList cannot be null");

    if (leadFindingRunLeadList.isEmpty()) {
      LOG.warn("Received an empty list for batch creation. Returning an empty response.");
      return Task.value(BatchCreateLeadFindingRunLeadResponse.newBuilder()
          .setContext(si.ResponseContext.newBuilder().build())
          .addAllResponses(Collections.emptyList())
          .build());
    }

    List<Task<CreateLeadFindingRunLeadResponse>> tasks = leadFindingRunLeadList.stream()
        .map(this::create)
        .collect(Collectors.toList());

    return Task.par(tasks)
        .map(responses -> buildBatchResponse(responses))
        .recoverWith(throwable -> {
          LOG.error("Failed to batch create LeadFindingRunLead resources for {} items", leadFindingRunLeadList.size(), throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Builds a BatchCreateLeadFindingRunLeadResponse from a list of CreateLeadFindingRunLeadResponse objects.
   *
   * @param responses The list of individual create responses to be included in the batch response.
   * @return A BatchCreateLeadFindingRunLeadResponse containing all the provided responses.
   */
  private BatchCreateLeadFindingRunLeadResponse buildBatchResponse(List<CreateLeadFindingRunLeadResponse> responses) {
    BatchCreateLeadFindingRunLeadResponse.Builder responseBuilder = BatchCreateLeadFindingRunLeadResponse.newBuilder()
        .setContext(si.ResponseContext.newBuilder().build());

    List<LeadFindingRunLeadResponse> convertedResponses = responses.stream()
        .map(response -> LeadFindingRunLeadResponse.newBuilder()
            .setKey(response.getKey())
            .setValue(response.getValue())
            .build())
        .collect(Collectors.toList());

    responseBuilder.addAllResponses(convertedResponses);
    return responseBuilder.build();
  }

  /**
   * Create a new LeadFindingRunLead record.
   *
   * @param leadFindingRunLead The details of the lead finding run to be created.
   * @return A task that resolves to the response of the create operation.
   */
  public Task<CreateLeadFindingRunLeadResponse> create(@Nonnull LeadFindingRunLead leadFindingRunLead) {
    Preconditions.checkNotNull(leadFindingRunLead, "LeadFindingRunLead cannot be null");

    return _lssAutoProspectingDB.createLeadFindingRunLead(leadFindingRunLead.getRunId(),
            MemberUrnBridge.INSTANCE.fromProto(leadFindingRunLead.getMemberUrn()),
            convertProtoToEspressoLeadFindingRunLead(leadFindingRunLead))
        .map(httpStatus -> CreateLeadFindingRunLeadResponse.newBuilder()
            .setKey(LeadFindingRunLeadKey.newBuilder()
                .setRunId(leadFindingRunLead.getRunId())
                .setMemberUrn(leadFindingRunLead.getMemberUrn())
                .build())
            .setValue(leadFindingRunLead)
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to create LeadFindingRunLead {} details: {}", leadFindingRunLead, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Retrieves a LeadFindingRunLead record by its unique key.
   *
   * @param leadFindingRunLeadKey The unique key containing the run ID and member URN.
   * @return A task that resolves to the GetLeadFindingRunLeadResponse containing the requested record.
   */
  public Task<GetLeadFindingRunLeadResponse> getLeadFindingRunLead(@Nonnull LeadFindingRunLeadKey leadFindingRunLeadKey) {
    Preconditions.checkNotNull(leadFindingRunLeadKey, "LeadFindingRunLeadKey cannot be null");

    return _lssAutoProspectingDB.getLeadByLeadFindingRunIdAndMemberUrn(
        leadFindingRunLeadKey.getRunId(),
            MemberUrnBridge.INSTANCE.fromProto(leadFindingRunLeadKey.getMemberUrn())
        )
        .map(optionalLeadFindingRunLead -> {
          if (optionalLeadFindingRunLead.isPresent()) {
            return GetLeadFindingRunLeadResponse.newBuilder()
                .setValue(convertEspressoToProtoLeadFindingRunLead(optionalLeadFindingRunLead.get(),
                    leadFindingRunLeadKey.getRunId(),
                    MemberUrnBridge.INSTANCE.fromProto(leadFindingRunLeadKey.getMemberUrn())))
                .build();
          } else {
            return GetLeadFindingRunLeadResponse.newBuilder()
                .setValue(LeadFindingRunLead.newBuilder().build())
                .build();
          }
        })
        .recoverWith(throwable -> {
          LOG.error("Failed to get LeadFindingRunLead with key: {}", leadFindingRunLeadKey, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   Find LeadFindingRunLead records based on the specified params

   @param runId The ID of the lead finding run to search for.
   @return A task that resolves to the FindByParamsLeadFindingRunLeadResponse containing the matching records.
    **/
  public Task<FindByParamsLeadFindingRunLeadResponse> findByParams(@Nonnull Long runId, @Nullable RequestPagingContext pagingContextParam) {
    Preconditions.checkNotNull(runId, "Run ID cannot be null");
    int start = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_START : pagingContextParam.getStart();
    int count = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_COUNT : pagingContextParam.getCount();
    return _lssAutoProspectingDB.findLeadFindingRunLeadByParams(runId, start, count)
        .map(results -> {
          if (results == null || results.isEmpty()) {
            LOG.info("No results found for runId={}", runId);
            return FindByParamsLeadFindingRunLeadResponse.newBuilder().build();
          }

          FindByParamsLeadFindingRunLeadResponse.Builder responseBuilder = FindByParamsLeadFindingRunLeadResponse.newBuilder();
          responseBuilder.addAllValues(
              results.stream()
                  .map(result ->
                    convertEspressoToProtoLeadFindingRunLead(result.getSecond(), runId,
                        result.getFirst().getMemberUrn())
                  )
                  .collect(Collectors.toList())
          );
          return responseBuilder
              .setPaging(ResponsePagingContext.newBuilder().setStart(start).setCount(count).build())
              .build();
        })
        .recoverWith(throwable -> {
          LOG.error("Failed to find LeadFindingRunLead by params: runId={}", runId, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Partially updates a LeadFindingRunLead record.
   *
   * @param leadFindingRunLeadKey The unique key containing the run ID and member URN of the record to update.
   * @param leadFindingRunLead The updated fields for the LeadFindingRunLead record.
   * @return A task that resolves to the PartialUpdateLeadFindingRunLeadResponse containing the updated record.
   */
  public Task<PartialUpdateLeadFindingRunLeadResponse> partialUpdate(@Nonnull LeadFindingRunLeadKey leadFindingRunLeadKey,
      @Nonnull LeadFindingRunLead leadFindingRunLead) {

    Preconditions.checkNotNull(leadFindingRunLeadKey, "LeadFindingRunLeadKey cannot be null");
    Preconditions.checkNotNull(leadFindingRunLead, "LeadFindingRunLead cannot be null");

    Long runId = leadFindingRunLeadKey.getRunId();
    MemberUrn memberUrn = MemberUrnBridge.INSTANCE.fromProto(leadFindingRunLeadKey.getMemberUrn());
    return _lssAutoProspectingDB.partialUpdateLeadFindingRunLead(
            runId,
            memberUrn,
            convertProtoToEspressoLeadFindingRunLead(leadFindingRunLead)
        )
        .map(updatedLead -> PartialUpdateLeadFindingRunLeadResponse.newBuilder()
            .setContext(si.ResponseContext.newBuilder().build()) // Set the response context
            .setValue(convertEspressoToProtoLeadFindingRunLead(updatedLead, runId, memberUrn)) // Set the updated LeadFindingRunLead record
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to partially update LeadFindingRunLead with key: {}, updated fields: {}",
              leadFindingRunLeadKey, leadFindingRunLead, throwable);
          return Task.failure(throwable);
        });
  }

  private com.linkedin.sales.espresso.LeadFindingRunLead convertProtoToEspressoLeadFindingRunLead(
      LeadFindingRunLead protoLeadFindingRunLead) {
    com.linkedin.sales.espresso.LeadFindingRunLead espressoLeadFindingRunLead = new com.linkedin.sales.espresso.LeadFindingRunLead();

    if (protoLeadFindingRunLead.hasContractUrn()) {
      try {
        espressoLeadFindingRunLead.setContractUrn(
            ContractUrnBridge.INSTANCE.fromProto(protoLeadFindingRunLead.getContractUrn()).toString());
      } catch (Exception e) {
        LOG.error("Failed to convert contract URN: {}", protoLeadFindingRunLead.getContractUrn(), e);
      }
    }

    if (!protoLeadFindingRunLead.getRelevantPositionIdsList().isEmpty()) {
      espressoLeadFindingRunLead.setRelevantPositionIds(protoLeadFindingRunLead.getRelevantPositionIdsList());
    }
    if (!protoLeadFindingRunLead.getKeyStrengthsList().isEmpty()) {
      espressoLeadFindingRunLead.setKeyStrengths(protoLeadFindingRunLead.getKeyStrengthsList().stream()
          .map(CharSequence::toString)
          .collect(Collectors.toList()));
    }
    if (!protoLeadFindingRunLead.getRationale().isEmpty()) {
      espressoLeadFindingRunLead.setRationale(protoLeadFindingRunLead.getRationale());
    }
    if (!protoLeadFindingRunLead.getVariant().isEmpty()) {
      espressoLeadFindingRunLead.setVariant(protoLeadFindingRunLead.getVariant());
    }
    // TODO: Handle this with a field mask in the future to differentiate between
    //  an intentional score of 0 and an unset value.
    espressoLeadFindingRunLead.setScore(protoLeadFindingRunLead.getScore());

    if (protoLeadFindingRunLead.getCreatedTime() != 0) {
      espressoLeadFindingRunLead.setCreatedTime(protoLeadFindingRunLead.getCreatedTime());
    }
    if (protoLeadFindingRunLead.getModifiedTime() != 0) {
      espressoLeadFindingRunLead.setModifiedTime(protoLeadFindingRunLead.getModifiedTime());
    }
    if (protoLeadFindingRunLead.getStatus() != LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_UNKNOWN) {
      espressoLeadFindingRunLead.setStatus(
          LEAD_FINDING_RUN_LEAD_STATUS_TO_STRING_MAP.getOrDefault(
              protoLeadFindingRunLead.getStatus(), "UNKNOWN"));
    }

    if (protoLeadFindingRunLead.getConversationId() != null) {
      espressoLeadFindingRunLead.setConversationId(protoLeadFindingRunLead.getConversationId());
    }

    return espressoLeadFindingRunLead;
  }

  private LeadFindingRunLead convertEspressoToProtoLeadFindingRunLead(
      com.linkedin.sales.espresso.LeadFindingRunLead espressoLeadFindingRunLead,
      @Nonnull Long runId,
      @Nonnull MemberUrn memberUrn) {

    Preconditions.checkNotNull(espressoLeadFindingRunLead, "espressoLeadFindingRunLead cannot be null");

    LeadFindingRunLead.Builder protoBuilder = LeadFindingRunLead.newBuilder();

    protoBuilder.setContractUrn(UrnUtils.createContractUrn(espressoLeadFindingRunLead.getContractUrn().toString()));

    protoBuilder.setMemberUrn(MemberUrnBridge.INSTANCE.fromPegasus(memberUrn));
    protoBuilder.setRunId(runId);

    protoBuilder.addAllRelevantPositionIds(espressoLeadFindingRunLead.getRelevantPositionIds());

    protoBuilder.setRationale(espressoLeadFindingRunLead.getRationale().toString());
    protoBuilder.setScore(espressoLeadFindingRunLead.getScore());
    protoBuilder.setVariant(espressoLeadFindingRunLead.getVariant().toString());

    protoBuilder.setCreatedTime(espressoLeadFindingRunLead.getCreatedTime());
    protoBuilder.setModifiedTime(espressoLeadFindingRunLead.getModifiedTime());

    protoBuilder.setStatus(
        STRING_TO_LEAD_FINDING_RUN_LEAD_STATUS_MAP.get(espressoLeadFindingRunLead.getStatus().toString())
    );

    if (espressoLeadFindingRunLead.getConversationId() != null) {
      protoBuilder.setConversationId(espressoLeadFindingRunLead.getConversationId().toString());
    }

    protoBuilder.addAllKeyStrengths(
        espressoLeadFindingRunLead.getKeyStrengths().stream()
            .map(CharSequence::toString)
            .collect(Collectors.toList())
    );

    return protoBuilder.build();
  }
}