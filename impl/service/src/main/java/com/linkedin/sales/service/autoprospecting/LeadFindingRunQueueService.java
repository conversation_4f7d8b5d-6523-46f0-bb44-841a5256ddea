package com.linkedin.sales.service.autoprospecting;

import com.linkedin.parseq.Task;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.validation.constraints.Null;
import proto.com.linkedin.common.ContractUrn;
import proto.com.linkedin.common.MemberUrn;
import proto.com.linkedin.common.SeatUrn;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.DeleteLeadFindingRunQueueRequest;
import proto.com.linkedin.salesautoprospecting.DeleteLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.FindByCampaignLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.FindByStatusLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunQueueResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunQueue;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunQueueKey;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunQueueStatus;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunQueueType;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunQueueResponse;
import si.RequestPagingContext;
import si.ResponsePagingContext;


public class LeadFindingRunQueueService {
  private static final int DEFAULT_PAGING_START = 0;
  private static final int DEFAULT_PAGING_COUNT = 25;

  public LeadFindingRunQueueService() {
  }

  /**
   * Create a new leadFindingRunQueue record.
   * @param leadFindingRunQueue the leadFindingRunQueue object to be created
   * @return CreateLeadFindingRunResponse the response object
   */
  public Task<CreateLeadFindingRunQueueResponse> create(@Nonnull LeadFindingRunQueue leadFindingRunQueue) {
    // return mock response
    LeadFindingRunQueue mockedQueue = buildMockLeadFindingRunQueueData1();

    return Task.value(CreateLeadFindingRunQueueResponse.newBuilder()
        .setKey(LeadFindingRunQueueKey.newBuilder()
            .setQueueId(mockedQueue.getQueueId())
            .setSeatUrn(mockedQueue.getSeatUrn())
            .build())
        .setValue(mockedQueue)
        .build());
  }

  /**
   * Get leadFindingRunQueue by key
   * @param leadFindingRunQueueKey resource key
   * @return GetLeadFindingRunResponse the response object
   */
  public Task<GetLeadFindingRunQueueResponse> get(@Nonnull LeadFindingRunQueueKey leadFindingRunQueueKey) {
    // return mock response
    LeadFindingRunQueue mockedQueue = buildMockLeadFindingRunQueueData1();

    return Task.value(GetLeadFindingRunQueueResponse.newBuilder().setValue(mockedQueue).build());
  }

  /**
   * Partially update a LeadFindingRunQueue record using the given leadFindingRunKey and the updated leadFindingRun.
   * @param leadFindingRunQueueKey the key of the leadFindingRunQueue to be updated
   * @param leadFindingRunQueue the leadFindingRunQueue object which contains the updated fields
   * @return PartialUpdateLeadFindingRunResponse the response object which contains the updated leadFindingRunQueue
   */
  public Task<PartialUpdateLeadFindingRunQueueResponse> partialUpdate(
      @Nonnull LeadFindingRunQueueKey leadFindingRunQueueKey, @Nonnull LeadFindingRunQueue leadFindingRunQueue) {
    // return mock response

    return Task.value(PartialUpdateLeadFindingRunQueueResponse.newBuilder().setValue(leadFindingRunQueue).build());
  }

  /**
   * Delete a LeadFindingRunQueue record using the given leadFindingRunKey.
   */
  public Task<DeleteLeadFindingRunQueueResponse> delete(
      @Nonnull DeleteLeadFindingRunQueueRequest deleteLeadFindingRunQueueRequest) {
    // return mock response
    return Task.value(DeleteLeadFindingRunQueueResponse.newBuilder().build());
  }

  /**
   * Find LeadFindingRunQueue records by given status.
   * @param status the LeadFindingRunQueue status
   * @param pagingContextParam the paging context
   * @return FindByStatusLeadFindingRunQueueResponse the response object
   */
  public Task<FindByStatusLeadFindingRunQueueResponse> findByStatus(@Nonnull LeadFindingRunQueueStatus status,
      @Null RequestPagingContext pagingContextParam) {
    int start = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_START : pagingContextParam.getStart();
    int count = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_COUNT : pagingContextParam.getCount();

    List<LeadFindingRunQueue> queues = List.of(buildMockLeadFindingRunQueueData1(), buildMockLeadFindingRunQueueData2(),
        buildMockLeadFindingRunQueueData3());

    // return mock response
    return Task.value(FindByStatusLeadFindingRunQueueResponse.newBuilder()
        .addAllValues(queues)
        .setPaging(ResponsePagingContext.newBuilder().setStart(start).setCount(queues.size()).build())
        .build());
  }

  /**
   * Find LeadFindingRunQueue records by given campaign.
   * @param status the LeadFindingRunQueue status
   * @param pagingContextParam the paging context
   * @return FindByStatusLeadFindingRunQueueResponse the response object
   */
  public Task<FindByCampaignLeadFindingRunQueueResponse> findByCampaign(@Nonnull Long campaignId,
      @Nullable LeadFindingRunQueueStatus status, @Nullable RequestPagingContext pagingContextParam) {
    int start = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_START : pagingContextParam.getStart();
    int count = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_COUNT : pagingContextParam.getCount();

    List<LeadFindingRunQueue> queues = List.of(buildMockLeadFindingRunQueueData1(), buildMockLeadFindingRunQueueData2(),
        buildMockLeadFindingRunQueueData3());

    // return mock response
    return Task.value(FindByCampaignLeadFindingRunQueueResponse.newBuilder()
        .addAllValues(queues)
        .setPaging(ResponsePagingContext.newBuilder().setStart(start).setCount(queues.size()).build())
        .build());
  }

  private LeadFindingRunQueue buildMockLeadFindingRunQueueData1() {
    return LeadFindingRunQueue.newBuilder()
        .setQueueId(1234L)
        .setRunId(1111L)
        .setCampaignId(1000L)
        .setSeatUrn(SeatUrn.newBuilder().setSeatId(230138902L).build())
        .setContractUrn(ContractUrn.newBuilder().setContractId(18615004L).build())
        .setMemberUrn(MemberUrn.newBuilder().setMemberId(243022892L).build())
        .setStatus(LeadFindingRunQueueStatus.LeadFindingRunQueueStatus_PENDING)
        .setPriority(10)
        .setType(LeadFindingRunQueueType.LeadFindingRunQueueType_SCHEDULED)
        .setQueueTime(System.currentTimeMillis() - 1000)
        .build();
  }

  private LeadFindingRunQueue buildMockLeadFindingRunQueueData2() {
    return LeadFindingRunQueue.newBuilder()
        .setQueueId(2345L)
        .setRunId(2222L)
        .setCampaignId(2000L)
        .setSeatUrn(SeatUrn.newBuilder().setSeatId(230138902L).build())
        .setContractUrn(ContractUrn.newBuilder().setContractId(18615004L).build())
        .setMemberUrn(MemberUrn.newBuilder().setMemberId(243022892L).build())
        .setStatus(LeadFindingRunQueueStatus.LeadFindingRunQueueStatus_PENDING)
        .setPriority(6)
        .setType(LeadFindingRunQueueType.LeadFindingRunQueueType_SCHEDULED)
        .setQueueTime(System.currentTimeMillis())
        .build();
  }

  private LeadFindingRunQueue buildMockLeadFindingRunQueueData3() {
    return LeadFindingRunQueue.newBuilder()
        .setQueueId(3456L)
        .setRunId(2222L)
        .setCampaignId(3000L)
        .setSeatUrn(SeatUrn.newBuilder().setSeatId(443976407L).build())
        .setContractUrn(ContractUrn.newBuilder().setContractId(18615004L).build())
        .setMemberUrn(MemberUrn.newBuilder().setMemberId(87357359L).build())
        .setStatus(LeadFindingRunQueueStatus.LeadFindingRunQueueStatus_PROCESSED)
        .setPriority(8)
        .setType(LeadFindingRunQueueType.LeadFindingRunQueueType_SCHEDULED)
        .setQueueTime(System.currentTimeMillis() - 2000)
        .build();
  }
}
