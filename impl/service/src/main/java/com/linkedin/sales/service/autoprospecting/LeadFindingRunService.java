package com.linkedin.sales.service.autoprospecting;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.parseq.Task;
import com.linkedin.sales.espresso.TrackingId;
import com.linkedin.sales.service.utils.TrackingUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.validation.constraints.Null;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.ContractUrnBridge;
import proto.com.linkedin.common.SeatUrn;
import proto.com.linkedin.common.SeatUrnBridge;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.FindByQueryLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRun;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunKey;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunError;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunType;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunStatus;
import si.RequestPagingContext;
import si.ResponsePagingContext;

/**
 * Service for interacting with LeadFindingRun Espresso table.
 */
public class LeadFindingRunService {

  private static final Logger LOG = LoggerFactory.getLogger(LeadFindingRunService.class);

  private static final Map<String, LeadFindingRunStatus> STRING_TO_LEAD_FINDING_RUN_STATUS_MAP =
      new ImmutableMap.Builder<String, LeadFindingRunStatus>()
          .put("NOT_STARTED", LeadFindingRunStatus.LeadFindingRunStatus_NOT_STARTED)
          .put("IN_PROGRESS", LeadFindingRunStatus.LeadFindingRunStatus_IN_PROGRESS)
          .put("COMPLETED", LeadFindingRunStatus.LeadFindingRunStatus_COMPLETED)
          .put("FAILED", LeadFindingRunStatus.LeadFindingRunStatus_FAILED)
          .put("UNKNOWN", LeadFindingRunStatus.LeadFindingRunStatus_UNKNOWN)
          .build();
  private static final Map<LeadFindingRunStatus, String> LEAD_FINDING_RUN_STATUS_TO_STRING_MAP =
      STRING_TO_LEAD_FINDING_RUN_STATUS_MAP.entrySet().stream()
          .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

  private static final Map<String, LeadFindingRunType> STRING_TO_LEAD_FINDING_RUN_TYPE_MAP =
      new ImmutableMap.Builder<String, LeadFindingRunType>()
          .put("ON_DEMAND", LeadFindingRunType.LeadFindingRunType_ON_DEMAND)
          .put("SCHEDULED", LeadFindingRunType.LeadFindingRunType_SCHEDULED)
          .put("UNKNOWN", LeadFindingRunType.LeadFindingRunType_UNKNOWN)
          .build();
  private static final Map<LeadFindingRunType, String> LEAD_FINDING_RUN_TYPE_TO_STRING_MAP =
      STRING_TO_LEAD_FINDING_RUN_TYPE_MAP.entrySet().stream()
          .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
  private static final int DEFAULT_PAGING_START = 0;
  private static final int DEFAULT_PAGING_COUNT = 25;

  private final LssAutoProspectingDB _lssAutoProspectingDB;

  public LeadFindingRunService(LssAutoProspectingDB lssAutoProspectingDB) {
    _lssAutoProspectingDB = lssAutoProspectingDB;
  }

  /**
   * Create a new leadFindingRun espresso record.
   * @param leadFindingRun the leadFindingRun object to be created
   * @return CreateLeadFindingRunResponse the response object
   */
  public Task<CreateLeadFindingRunResponse> create(@Nonnull LeadFindingRun leadFindingRun) {
    Preconditions.checkNotNull(leadFindingRun, "leadFindingRun cannot be null");
    Preconditions.checkNotNull(leadFindingRun.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkArgument(leadFindingRun.getCampaignId() > 0, "campaignId cannot be null");

    return _lssAutoProspectingDB.createLeadFindingRun(SeatUrnBridge.INSTANCE.fromProto(leadFindingRun.getSeatUrn()),
            leadFindingRun.getCampaignId(), convertProtoToEspressoLeadFindingRun(leadFindingRun))
        .map(runId -> CreateLeadFindingRunResponse.newBuilder()
            .setKey(LeadFindingRunKey.newBuilder()
                .setSeatUrn(leadFindingRun.getSeatUrn())
                .setCampaignId(leadFindingRun.getCampaignId())
                .setRunId(runId)
                .build())
            .setValue(leadFindingRun)
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to create leadFindingRun {} for seatUrn: {}, campaignId: {}",
              leadFindingRun.getSeatUrn(), leadFindingRun.getCampaignId(), throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Get leadFindingRun by key
   * @param leadFindingRunKey resource key
   * @return GetLeadFindingRunResponse the response object
   */
  public Task<GetLeadFindingRunResponse> get(@Nonnull LeadFindingRunKey leadFindingRunKey) {
    Preconditions.checkNotNull(leadFindingRunKey, "leadFindingRunKey cannot be null");
    Preconditions.checkNotNull(leadFindingRunKey.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkArgument(leadFindingRunKey.getCampaignId() > 0, "campaignId cannot be null");
    Preconditions.checkArgument(leadFindingRunKey.getRunId() > 0, "runId cannot be null");

    SeatUrn seatUrn = leadFindingRunKey.getSeatUrn();
    Long campaignId = leadFindingRunKey.getCampaignId();
    Long runId = leadFindingRunKey.getRunId();
    return _lssAutoProspectingDB.getLeadFindingRun(SeatUrnBridge.INSTANCE.fromProto(seatUrn), campaignId, runId)
        .map(leadFindingRun -> GetLeadFindingRunResponse.newBuilder()
            .setValue(convertEspressoToProtoLeadFindingRun(leadFindingRun, seatUrn, campaignId, runId))
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to get Lead Finding Run for seatUrn {}, campaignId: {}, runId {}", seatUrn, campaignId, runId, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Partially update a LeadFindingRun espresso record using the given leadFindingRunKey and the updated leadFindingRun.
   * @param leadFindingRunKey the key of the leadFindingRun to be updated
   * @param leadFindingRun the leadFindingRun object which contains the updated fields
   * @return PartialUpdateLeadFindingRunResponse the response object which contains the updated leadFindingRun
   */
  public Task<PartialUpdateLeadFindingRunResponse> partialUpdate(
      @Nonnull LeadFindingRunKey leadFindingRunKey,
      @Nonnull LeadFindingRun leadFindingRun) {
    SeatUrn seatUrn = leadFindingRunKey.getSeatUrn();
    Long campaignId = leadFindingRunKey.getCampaignId();
    Long runId = leadFindingRunKey.getRunId();
    return _lssAutoProspectingDB.partialUpdateLeadFindingRun(SeatUrnBridge.INSTANCE.fromProto(seatUrn),
            campaignId, runId, convertProtoToEspressoLeadFindingRun(leadFindingRun))
        .map(espressoLeadFindingRun -> PartialUpdateLeadFindingRunResponse.newBuilder()
            .setValue(convertEspressoToProtoLeadFindingRun(espressoLeadFindingRun, seatUrn, campaignId, runId))
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to update LeadFindingRun for seatUrn: {}, campaignId: {}, runId: {}",
              seatUrn, campaignId, runId, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Find LeadFindingRun espresso records by the given seat, campaignId and status.
   * @param seatUrn the seat urn
   * @param campaignId the campaign id
   * @param status the LeadFindingRun status
   * @param pagingContextParam the paging context
   * @return FindByQueryLeadFindingRunResponse the response object
   */
  public Task<FindByQueryLeadFindingRunResponse> findByQuery(
      @Nonnull SeatUrn seatUrn, @Nullable Long campaignId, @Nullable LeadFindingRunStatus status,
      @Nullable LeadFindingRunType type,
      @Null RequestPagingContext pagingContextParam) {

    int start = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_START : pagingContextParam.getStart();
    int count = Objects.isNull(pagingContextParam) ? DEFAULT_PAGING_COUNT : pagingContextParam.getCount();
    return _lssAutoProspectingDB.findLeadFindingRunByQuery(SeatUrnBridge.INSTANCE.fromProto(seatUrn), campaignId,
            Objects.isNull(status) ? null : LEAD_FINDING_RUN_STATUS_TO_STRING_MAP.get(status),
            Objects.isNull(type) ? null : LEAD_FINDING_RUN_TYPE_TO_STRING_MAP.get(type),
            start, count)
        .map(leadFindingRuns -> FindByQueryLeadFindingRunResponse.newBuilder()
            .addAllValues(leadFindingRuns.stream()
                .map(results -> convertEspressoToProtoLeadFindingRun(results.getSecond(), seatUrn,
                    Objects.requireNonNull(results.getFirst().getCampaignId()),
                    Objects.requireNonNull(results.getFirst().getRunId())))
                .collect(Collectors.toList()))
            .setPaging(ResponsePagingContext.newBuilder().setStart(start).setCount(count).build())
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to find LeadFindingRun for seatUrn {}, campaignId {}, status {}",
              seatUrn, campaignId, status, throwable);
          return Task.failure(throwable);
        });
  }

  private com.linkedin.sales.espresso.LeadFindingRun convertProtoToEspressoLeadFindingRun(LeadFindingRun leadFindingRun) {
    com.linkedin.sales.espresso.LeadFindingRun espressoLeadFindingRun = new com.linkedin.sales.espresso.LeadFindingRun();
    if (leadFindingRun.hasContractUrn()) {
      espressoLeadFindingRun.setContractUrn(ContractUrnBridge.INSTANCE.fromProto(leadFindingRun.getContractUrn()).toString());
    }
    if (leadFindingRun.getStatus() != LeadFindingRunStatus.LeadFindingRunStatus_UNKNOWN) {
      espressoLeadFindingRun.setStatus(LEAD_FINDING_RUN_STATUS_TO_STRING_MAP.get(leadFindingRun.getStatus()));
    }
    if (leadFindingRun.getType() != LeadFindingRunType.LeadFindingRunType_UNKNOWN) {
      espressoLeadFindingRun.setType(LEAD_FINDING_RUN_TYPE_TO_STRING_MAP.get(leadFindingRun.getType()));
    }
    if (leadFindingRun.hasError()) {
      espressoLeadFindingRun.setError(convertProtoToEspressoError(leadFindingRun.getError()));
    }
    if (leadFindingRun.hasScheduledStartTime()) {
      espressoLeadFindingRun.setScheduledStartTime(leadFindingRun.getScheduledStartTime());
    }
    if (leadFindingRun.hasStartTime()) {
      espressoLeadFindingRun.setStartTime(leadFindingRun.getStartTime());
    }
    if (leadFindingRun.hasProjectedCompletionTime()) {
      espressoLeadFindingRun.setProjectedCompletionTime(leadFindingRun.getProjectedCompletionTime());
    }
    if (leadFindingRun.hasCompletionTime()) {
      espressoLeadFindingRun.setCompletionTime(leadFindingRun.getCompletionTime());
    }
    if (leadFindingRun.hasTrackingId()) {
      espressoLeadFindingRun.setTrackingId(new TrackingId(leadFindingRun.getTrackingId().getFixedValue().toByteArray()));
    }
    if (leadFindingRun.hasLeadLimit()) {
      espressoLeadFindingRun.setLeadLimit(leadFindingRun.getLeadLimit());
    }
    if (leadFindingRun.hasProspectedLeadsCount()) {
      espressoLeadFindingRun.setProspectedLeadsCount(leadFindingRun.getProspectedLeadsCount());
    }
     return espressoLeadFindingRun;
  }

  private com.linkedin.sales.espresso.LeadFindingRunError convertProtoToEspressoError(LeadFindingRunError error) {
    com.linkedin.sales.espresso.LeadFindingRunError leadFindingError = new com.linkedin.sales.espresso.LeadFindingRunError();
    leadFindingError.setCode(error.getCode());
    leadFindingError.setMessage(error.getMessage());
    return leadFindingError;
  }

  private LeadFindingRun convertEspressoToProtoLeadFindingRun(
      com.linkedin.sales.espresso.LeadFindingRun leadFindingRun, @Nonnull SeatUrn seatUrn, @Nonnull Long campaignId, @Nonnull Long runId) {
    LeadFindingRun.Builder builder = LeadFindingRun.newBuilder();

    builder.setSeatUrn(seatUrn);
    builder.setCampaignId(campaignId);
    builder.setRunId(runId);
    builder.setContractUrn(UrnUtils.createContractUrn(leadFindingRun.getContractUrn().toString()));
    builder.setStatus(STRING_TO_LEAD_FINDING_RUN_STATUS_MAP.get(leadFindingRun.getStatus().toString()));
    builder.setType(STRING_TO_LEAD_FINDING_RUN_TYPE_MAP.get(leadFindingRun.getType().toString()));
    if (Objects.nonNull(leadFindingRun.getError())) {
      builder.setError(convertEspressoToProtoError(leadFindingRun.getError()));
    }
    builder.setCreatedTime(leadFindingRun.getCreatedTime());
    if (Objects.nonNull(leadFindingRun.getScheduledStartTime())) {
      builder.setScheduledStartTime(leadFindingRun.getScheduledStartTime());
    }
    if (Objects.nonNull(leadFindingRun.getStartTime())) {
      builder.setStartTime(leadFindingRun.getStartTime());
    }
    if (Objects.nonNull(leadFindingRun.getProjectedCompletionTime())) {
      builder.setProjectedCompletionTime(leadFindingRun.getProjectedCompletionTime());
    }
    if (Objects.nonNull(leadFindingRun.getCompletionTime())) {
      builder.setCompletionTime(leadFindingRun.getCompletionTime());
    }
    builder.setLastModifiedTime(leadFindingRun.getLastModifiedTime());
    if (Objects.nonNull(leadFindingRun.getTrackingId())) {
      builder.setTrackingId(TrackingUtils.convertEspressoToProtoTrackingId(leadFindingRun.getTrackingId()));
    }
    if (Objects.nonNull(leadFindingRun.getLeadLimit())) {
      builder.setLeadLimit(leadFindingRun.getLeadLimit());
    }
    if (Objects.nonNull(leadFindingRun.getProspectedLeadsCount())) {
      builder.setProspectedLeadsCount(leadFindingRun.getProspectedLeadsCount());
    }

    return builder.build();
  }


  private LeadFindingRunError convertEspressoToProtoError(com.linkedin.sales.espresso.LeadFindingRunError error) {
    return LeadFindingRunError.newBuilder()
        .setCode(error.getCode().toString())
        .setMessage(error.getMessage().toString()).build();
  }
}
