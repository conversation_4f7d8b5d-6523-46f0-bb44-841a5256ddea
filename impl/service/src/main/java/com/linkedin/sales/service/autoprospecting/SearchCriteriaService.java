package com.linkedin.sales.service.autoprospecting;

import com.google.common.base.Preconditions;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import javax.annotation.Nonnull;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.ContractUrnBridge;
import proto.com.linkedin.common.SeatUrnBridge;
import proto.com.linkedin.salesautoprospecting.CreateSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.GetSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateSearchCriteriaResponse;
import proto.com.linkedin.salesautoprospecting.SearchCriteria;
import proto.com.linkedin.salesautoprospecting.SearchCriteriaKey;


/**
 * Service for interacting with SearchCriteria Espresso table.
 */
public class SearchCriteriaService {
  private static final String HASH_DELIMITER = ";";
  private static final Logger LOG = LoggerFactory.getLogger(SearchCriteriaService.class);

  private final LssAutoProspectingDB _lssAutoProspectingDB;

  public SearchCriteriaService(LssAutoProspectingDB lssAutoProspectingDB) {
    _lssAutoProspectingDB = lssAutoProspectingDB;
  }

  /**
   * Create a new SearchCriteria espresso record including raw criteria and query understanding output.
   * @param searchCriteriaKey search criteria key including seat entered the criteria, raw criteria and prompt variant
   * @param searchCriteria search criteria object including query understanding output
   * @return create response
   */
  public Task<CreateSearchCriteriaResponse> createSearchCriteria(@Nonnull SearchCriteriaKey searchCriteriaKey,
                                                                 @Nonnull SearchCriteria searchCriteria) {
    validateSearchCriteriaRequiredFields(searchCriteriaKey, searchCriteria);

    String hashedCriteria = generateHashValue(searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant());
    return _lssAutoProspectingDB.createSearchCriteria(SeatUrnBridge.INSTANCE.fromProto(searchCriteriaKey.getSeatUrn()), hashedCriteria,
                    convertProtoToEspressoSearchCriteria(searchCriteria, searchCriteriaKey))
            .map(httpStatus -> CreateSearchCriteriaResponse.newBuilder()
                    .setKey(searchCriteriaKey)
                    .setValue(searchCriteria)
                    .build())
            .recoverWith(throwable -> {
              LOG.error("Failed to create SearchCriteria {} for seatUrn {}, rawCriteria {}, promptVariant {}",
                      searchCriteriaKey.getSeatUrn(), searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant(), throwable);
              return Task.failure(throwable);
            });
  }

  /**
   * Partial update SearchCriteria espresso record by given seat, raw criteria and prompt variant with updated query understanding output.
   * @param searchCriteriaKey search criteria key including seat entered the criteria, raw criteria and prompt variant
   * @param searchCriteria search criteria object including updated query understanding output
   * @return update response
   */
  public Task<PartialUpdateSearchCriteriaResponse> partialUpdateSearchCriteria(@Nonnull SearchCriteriaKey searchCriteriaKey,
                                                                               @Nonnull SearchCriteria searchCriteria) {
    validateSearchCriteriaRequiredFields(searchCriteriaKey, searchCriteria);

    String hashedCriteria = generateHashValue(searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant());
    return _lssAutoProspectingDB.partialUpdateSearchCriteria(SeatUrnBridge.INSTANCE.fromProto(searchCriteriaKey.getSeatUrn()), hashedCriteria,
                    convertProtoToEspressoSearchCriteria(searchCriteria, searchCriteriaKey))
            .map(httpStatus -> PartialUpdateSearchCriteriaResponse.newBuilder()
                    .setValue(searchCriteria)
                    .build())
            .recoverWith(throwable -> {
              LOG.error("Failed to update SearchCriteria {} for seatUrn {}, rawCriteria {}, promptVariant {}",
                      searchCriteriaKey.getSeatUrn(), searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant(), throwable);
              return Task.failure(throwable);
            });
  }

  /**
   * Get SearchCriteria espresso record by given seat, raw criteria and prompt variant.
   * @param searchCriteriaKey search criteria key including seat entered the criteria, raw criteria and prompt variant
   * @return search criteria object including query understanding output
   */
  public Task<GetSearchCriteriaResponse> getSearchCriteria(@Nonnull SearchCriteriaKey searchCriteriaKey) {
    Preconditions.checkNotNull(searchCriteriaKey, "SearchCriteriaKey cannot be null");
    Preconditions.checkNotNull(searchCriteriaKey.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkNotNull(searchCriteriaKey.getRawCriteria(), "RawCriteria cannot be null");

    String hashedCriteria = generateHashValue(searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant());
    return _lssAutoProspectingDB.getSearchCriteria(SeatUrnBridge.INSTANCE.fromProto(searchCriteriaKey.getSeatUrn()), hashedCriteria)
            .map(optionalSearchCriteria -> {
              if (optionalSearchCriteria.isPresent()) {
                return GetSearchCriteriaResponse.newBuilder()
                        .setValue(convertEspressoToProtoSearchCriteria(optionalSearchCriteria.get()))
                        .build();
              } else {
                return GetSearchCriteriaResponse.newBuilder()
                        .setValue(SearchCriteria.newBuilder().build())
                        .build();
              }
            })
            .recoverWith(throwable -> {
              LOG.error("Failed to get SearchCriteria {} for seatUrn {}, rawCriteria {}, promptVariant {}",
                      searchCriteriaKey.getSeatUrn(), searchCriteriaKey.getRawCriteria(), searchCriteriaKey.getPromptVariant(), throwable);
              return Task.failure(throwable);
            });
  }

  private void validateSearchCriteriaRequiredFields(SearchCriteriaKey searchCriteriaKey, SearchCriteria searchCriteria) {
    Preconditions.checkNotNull(searchCriteriaKey, "SearchCriteriaKey cannot be null");
    Preconditions.checkNotNull(searchCriteriaKey.getSeatUrn(), "SeatUrn cannot be null");
    Preconditions.checkNotNull(searchCriteriaKey.getRawCriteria(), "RawCriteria cannot be null");

    Preconditions.checkNotNull(searchCriteria, "SearchCriteria cannot be null");
    Preconditions.checkNotNull(searchCriteria.getContractUrn(), "ContractUrn cannot be null");
  }

  private String generateHashValue(String rawCriteria, String promptVariant) {
    StringBuilder stringBuilder = new StringBuilder();
    stringBuilder.append(rawCriteria).append(HASH_DELIMITER).append(promptVariant);
    try {
      return Hex.encodeHexString(MessageDigest.getInstance("SHA-256").digest(stringBuilder.toString().getBytes(
              StandardCharsets.UTF_8)));
    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException("Failed to generate hash value", e);
    }
  }

  private com.linkedin.sales.espresso.SearchCriteriaV1 convertProtoToEspressoSearchCriteria(SearchCriteria searchCriteria,
                                                                                          SearchCriteriaKey searchCriteriaKey) {
    com.linkedin.sales.espresso.SearchCriteriaV1 espressoSearchCriteria = new com.linkedin.sales.espresso.SearchCriteriaV1();
    // Set raw criteria captured in searchCriteriaKey
    espressoSearchCriteria.setRawCriteria(searchCriteriaKey.getRawCriteria());
    espressoSearchCriteria.setFacetSelections(searchCriteria.getFacetSelections());
    espressoSearchCriteria.setEbrSearchQuery(searchCriteria.getEbrSearchQuery());
    espressoSearchCriteria.setContractUrn(ContractUrnBridge.INSTANCE.fromProto(searchCriteria.getContractUrn()).toString());
    return espressoSearchCriteria;
  }

  private SearchCriteria convertEspressoToProtoSearchCriteria(com.linkedin.sales.espresso.SearchCriteriaV1 espressoSearchCriteria)
          throws URISyntaxException {
    proto.com.linkedin.common.ContractUrn contractUrn = proto.com.linkedin.common.ContractUrn.newBuilder()
            .setContractId(ContractUrn.deserialize(espressoSearchCriteria.getContractUrn().toString()).getIdAsLong()).build();
    return SearchCriteria.newBuilder()
            .setFacetSelections(espressoSearchCriteria.getFacetSelections().toString())
            .setEbrSearchQuery(espressoSearchCriteria.getEbrSearchQuery().toString())
            .setContractUrn(contractUrn)
            .setCreatedTime(espressoSearchCriteria.getCreatedTime())
            .setLastModifiedTime(espressoSearchCriteria.getLastModifiedTime())
            .build();
  }
}
