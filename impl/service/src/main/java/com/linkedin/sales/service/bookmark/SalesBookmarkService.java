package com.linkedin.sales.service.bookmark;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.NotificationV2Urn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.NoMetadata;
import com.linkedin.sales.ds.db.LssBookmarkDB;
import com.linkedin.salesbookmark.Bookmark;
import com.linkedin.salesbookmark.BookmarkContent;
import com.linkedin.salesbookmark.BookmarkType;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkState;
import static com.linkedin.sales.service.utils.ServiceConstants.BOOKMARK_TYPE_BI_MAP;


/**
 * Sales Bookmark Service
 * <AUTHOR>
 */
public class SalesBookmarkService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesBookmarkService.class);

  // MV table has no knowledge about contract, so finders would return a dummy contract value that is not exposed to
  // customers.
  static final ContractUrn DUMMY_CONTRACT_URN = new ContractUrn(0L);

  private final LssBookmarkDB _lssBookmarkDB;

  public static final long BOOKMARK_CREATION_FAILURE_RETURN_VALUE = 0L;

  public SalesBookmarkService(LssBookmarkDB lssBookmarkDB) {
    _lssBookmarkDB = lssBookmarkDB;
  }

  /**
   * Create a new bookmark only if the entry of bookmark for given BookmarkEntry is not yet created.
   * It would be no-op if BookmarkContent already exists in DB.
   *
   * @param bookmark body
   * @return generated bookmark id
   */
  public Task<CreateResponse> createBookmark(Bookmark bookmark) {
    return batchFindByContents(bookmark.getOwner(), bookmark.getType(), new BookmarkContent[] {bookmark.getContent()})
        .flatMap(resultMap -> {
          checkState(resultMap.size() == 1, "Only expect findByContent to return map of size 1 inside creation call.");
          if (resultMap.get(bookmark.getContent()).getTotal() == 0) {
            com.linkedin.sales.espresso.Bookmark espressoBookmark = convertBookmarkToEspressoFormat(bookmark);
            return _lssBookmarkDB.createBookmark(espressoBookmark, bookmark.getExpiredAt(GetMode.NULL), null)
                .map(id -> new CreateResponse(id, HttpStatus.S_201_CREATED));
          }
          LOG.info("Cannot have repeated bookmark.");
          return Task.value(new CreateResponse(BOOKMARK_CREATION_FAILURE_RETURN_VALUE, HttpStatus.S_409_CONFLICT));
        });
  }

  /**
   * Delete a bookmark
   * @param bookmarkId bookmark id
   * @param seat the seatUrn of requester, must match the owner of bookmark.
   * @return the http status to tell if the deletion succeeds
   */
  public Task<HttpStatus> deleteBookmark(long bookmarkId, SeatUrn seat) {
    return _lssBookmarkDB.getBookmark(bookmarkId).flatMap(bookmark -> {
      if (bookmark == null) {
        LOG.error("Deletion request on bookmark {} failed: bookmark can not be found", bookmarkId);
        return Task.value(HttpStatus.S_404_NOT_FOUND);
      }
      if (seat == null || !bookmark.getSeatUrn().toString().equals(seat.toString())) {
        LOG.error("Deletion request on bookmark {} failed: requester {} can not be authorized", bookmarkId, seat);
        return Task.value(HttpStatus.S_403_FORBIDDEN);
      }
      return _lssBookmarkDB.deleteBookmark(bookmarkId);
    });
  }

  /**
   * Find the Bookmarks based on the seat and type
   * @param seatUrn seat to find the bookmark for
   * @param bookmarkType type of bookmark
   * @param start start
   * @param count count
   * @return BasicCollectionResult of Note
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "BOOKMARK_TYPE_BI_MAP.get value is not null")
  public Task<BasicCollectionResult<Bookmark>> findBySeatAndType(@NonNull SeatUrn seatUrn,
      @NonNull BookmarkType bookmarkType, int start, int count) {
    checkArgument(BOOKMARK_TYPE_BI_MAP.containsKey(bookmarkType),
        String.format("Unsupported BookmarkType [%s] on call findBySeatAndEntity", bookmarkType));

    com.linkedin.sales.espresso.BookmarkType espressoBookmarkType = BOOKMARK_TYPE_BI_MAP.get(bookmarkType);
    return _lssBookmarkDB.getBySeatAndType(seatUrn, espressoBookmarkType, start, count)
        .map(this::convertListOfEspressoBookmarkPairsToApiBookmarks)
        .recover(throwable -> {
      LOG.warn("Failed to get the bookmarks under the seat {} and type {}", seatUrn, bookmarkType, throwable);
      return new BasicCollectionResult<>(Collections.emptyList(), 0);
    });
  }

  /**
   * Batch find bookmarks by seat, type, and contents
   * @param seatUrn seat urn
   * @param bookmarkType bookmark type
   * @param contents bookmark contents
   * @return Map of BookmarkContent and Bookmark Collection pairs
   */
  public Task<Map<BookmarkContent, CollectionResult<Bookmark, NoMetadata>>> batchFindByContents(
      SeatUrn seatUrn, BookmarkType bookmarkType, BookmarkContent[] contents) {
    return Task.par(
    Arrays.stream(contents).map(content -> Pair.of(content, findByContent(seatUrn, bookmarkType, content)))
        .map(contentAndListOfBookmarkPair -> {
          return contentAndListOfBookmarkPair.getSecond()
              .map(bookmarkCollection -> Pair.of(contentAndListOfBookmarkPair.getFirst(), bookmarkCollection));
        }).collect(Collectors.toList()))
        .map(listOfPairs -> listOfPairs.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)));
  }

  /**
   * Helper function that converts a list of bookmarkId:espressoBookmark pairs into api layer Bookmark list
   */
  private BasicCollectionResult<Bookmark> convertListOfEspressoBookmarkPairsToApiBookmarks(
      Collection<Pair<Long, com.linkedin.sales.espresso.Bookmark>> pairs) {
    List<Bookmark> bookmarks = pairs.stream().map(pair -> {
      Bookmark bookmark = null;
      try {
        bookmark = convertEspressoBookmarkToApiFormat(pair.getFirst(), pair.getSecond());
      } catch (URISyntaxException e) {
        LOG.error("Unable to parse Bookmark record {}", pair);
      }
      return bookmark;
    }).filter(Objects::nonNull).collect(Collectors.toList());
    return new BasicCollectionResult<>(bookmarks, bookmarks.size());
  }

  /**
   * Find all bookmarks by seat, type, and a single content
   * @param seatUrn seat urn
   * @param bookmarkType bookmark type
   * @param content singular content
   * @return Collection of bookmarks
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "BOOKMARK_TYPE_BI_MAP.get value is not null")
  private Task<BasicCollectionResult<Bookmark>> findByContent(SeatUrn seatUrn, BookmarkType bookmarkType,
      BookmarkContent content) {
    checkArgument(BOOKMARK_TYPE_BI_MAP.containsKey(bookmarkType),
        String.format("Unsupported BookmarkType [%s]", bookmarkType));
    com.linkedin.sales.espresso.BookmarkType espressoBookmarkType = BOOKMARK_TYPE_BI_MAP.get(bookmarkType);
    String contentString = getContentString(content);
    return _lssBookmarkDB.getBySeatAndTypeAndContent(seatUrn, espressoBookmarkType, contentString)
        .map(this::convertListOfEspressoBookmarkPairsToApiBookmarks);
  }

  /**
   * Convert from api {@link Bookmark} to storage {@link com.linkedin.sales.espresso.Bookmark}
   */
  private com.linkedin.sales.espresso.Bookmark convertBookmarkToEspressoFormat(Bookmark apiBookmark) {
    checkArgument(apiBookmark.hasContent() && apiBookmark.getContent().hasBody(), "Incomplete bookmark content");
    checkArgument(apiBookmark.hasContract(), "bookmark missing contract");
    checkArgument(apiBookmark.hasType() && BOOKMARK_TYPE_BI_MAP.containsKey(apiBookmark.getType()),
        "Bookmark type missing or unsupported type [%s]", apiBookmark.getType());
    checkArgument(apiBookmark.hasOwner(), "bookmark missing owner");
    com.linkedin.sales.espresso.Bookmark espressoBookmark = new com.linkedin.sales.espresso.Bookmark();
    // TODO: Make the contentUrn assignment
    espressoBookmark.contentUrn = getContentString(apiBookmark.getContent());
    espressoBookmark.contractUrn = apiBookmark.getContract().toString();
    if (apiBookmark.hasCreatedAt()) {
      espressoBookmark.createdTime = apiBookmark.getCreatedAt();
    } else {
      espressoBookmark.createdTime = System.currentTimeMillis();
    }
    espressoBookmark.type = BOOKMARK_TYPE_BI_MAP.get(apiBookmark.getType());
    espressoBookmark.seatUrn = apiBookmark.getOwner().toString();
    return espressoBookmark;
  }

  /**
   * Convert espresso bookmark to api format
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "BOOKMARK_TYPE_BI_MAP.inverse().get values are not null")
  private Bookmark convertEspressoBookmarkToApiFormat(long id, com.linkedin.sales.espresso.Bookmark espressoBookmark)
      throws URISyntaxException {
    checkArgument(BOOKMARK_TYPE_BI_MAP.inverse().containsKey(espressoBookmark.type),
        String.format("Unsupported espressoBookmark type [%s]", espressoBookmark.type));
    // contract field is not available in materialized view table.
    ContractUrn contractUrn =
        espressoBookmark.contractUrn == null ? DUMMY_CONTRACT_URN : ContractUrn.deserialize(espressoBookmark.contractUrn.toString());
    SeatUrn seatUrn = SeatUrn.deserialize(espressoBookmark.seatUrn.toString());
    BookmarkType bookmarkType = BOOKMARK_TYPE_BI_MAP.inverse().get(espressoBookmark.type);

    return new Bookmark()
        .setContent(getBookmarkContentFromEspressoRecord(espressoBookmark))
        .setContract(contractUrn)
        .setCreatedAt(espressoBookmark.createdTime)
        .setId(id)
        .setOwner(seatUrn)
        .setType(bookmarkType);
  }

  /**
   * Get the raw string that is persistent in DB from BookmarkContent object
   */
  private String getContentString(BookmarkContent content) {
    BookmarkContent.Body body = content.getBody();
    if (body.isNotificationV2()) {
      return body.getNotificationV2().toString();
    }
    throw new IllegalArgumentException(String.format("Unsupported bookmark content %s", content));
  }

  private BookmarkContent getBookmarkContentFromEspressoRecord(com.linkedin.sales.espresso.Bookmark espressoBookmark)
      throws URISyntaxException {
    BookmarkContent.Body body = new BookmarkContent.Body();
    switch (espressoBookmark.type) {
      case ALERT:
        body.setNotificationV2(NotificationV2Urn.deserialize(espressoBookmark.contentUrn.toString()));
        break;
      default:
        throw new IllegalArgumentException(String.format("Cannot convert BookmarkContent on bookmark type %s", espressoBookmark.type));
    }
    return new BookmarkContent().setBody(body);
  }
}
