package com.linkedin.sales.service.buyerengagement;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.linkedin.analytics.QueryResponse;
import com.linkedin.analytics.Row;
import com.linkedin.buyerengagement.BuyerEngagementDailyActivity;
import com.linkedin.buyerengagement.BuyerSegment;
import com.linkedin.common.Date;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.sales.model.buyerengagement.MetricType;
import com.linkedin.sales.model.pinot.PinotQueryBuilder;
import com.linkedin.sales.service.utils.PinotUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.model.pinot.Column.*;
import static com.linkedin.sales.model.pinot.Condition.*;
import static com.linkedin.sales.service.utils.BuyerEngagementConstant.*;


/**
 * Service implementation to get buyer engagement data such as segments details, total activities
 */
public class BuyerEngagementService {

  private static final Logger LOG = LoggerFactory.getLogger(BuyerEngagementService.class);

  private final PinotUtils _pinotUtils;
  private final int _rowCount;

  public BuyerEngagementService(PinotUtils pinotUtils, int rowCount) {
    _pinotUtils = pinotUtils;
    _rowCount = rowCount;
  }

  /**
   * Find all unique segment details for members from one organization who initiate the buyer engagement activities towards the target organization.
   *
   * @param buyerOrganization the OrganizationUrn of the member who initiates the buyer engagement activities
   * @param sellerOrganization the OrganizationUrn where the buyer engagement activities were acted on
   * @param startDate the left bound corresponding to the time window query
   * @param endDate the right bound corresponding to the time window query
   * @return A parseq {@link Task} of execution to retrieve list of BuyerEngagementSegment
   */
  public Task<BasicCollectionResult<BuyerEngagementDailyActivity>> findByBuyerOrganization(
      final OrganizationUrn buyerOrganization, final OrganizationUrn sellerOrganization, final long startDate,
      final long endDate, final PagingContext pagingContext) {

    Preconditions.checkNotNull(buyerOrganization, "buyerOrganization cannot be null");
    Preconditions.checkNotNull(sellerOrganization, "sellerOrganization cannot be null");

    PinotQueryBuilder pinotQueryBuilder = new PinotQueryBuilder().select(
        col(ACTOR_SENIORITY_ID)
            .next(col(ACTOR_FUNCTION_ID))
            .next(col(ACTOR_GEO_ID))
            .next(col(DAYS_SINCE_EPOCH))
            .next(col(ENGAGEMENT_COUNT)))
        .from(BUYER_ENGAGEMENT_METRICS_TABLE)
        .where(equality(ACTOR_ORGANIZATION_ID, buyerOrganization.getIdAsLong())
            .and(equality(TARGET_ORGANIZATION_ID, sellerOrganization.getIdAsLong()))
            .and(equality(METRIC_TYPE, MetricType.SEGMENT.getMetricValue()))
            .and(betweenAnd(DAYS_SINCE_EPOCH, startDate, endDate)))
        .orderBy(col(DAYS_SINCE_EPOCH).desc()
            .next(col(ACTOR_SENIORITY_ID).desc()))
        .limit(_rowCount);

    String pinotQuery = pinotQueryBuilder.build();

    return _pinotUtils.runPinotQuery(BUYER_ENGAGEMENT_PINOT_CLUSTER, pinotQuery)
        .map(result -> processQueryResults(result, buyerOrganization, sellerOrganization, pagingContext))
        .recover(throwable -> {
          String msg = String.format("Failed to execute pinot query %s", pinotQuery);
          throw new RuntimeException(msg, throwable);
        });
  }

  /**
   * Retrieve a list of segments by unwrapping queryResponse
   *
   * @param queryResponse A response for buyer engagement segments,
   * @param buyerOrganization the OrganizationUrn of the member who initiates the buyer engagement activities
   * @param sellerOrganization the OrganizationUrn where the buyer engagement activities were acted on
   * @param pagingContext the context for pagination
   * @return The processed list of {@link BuyerEngagementDailyActivity}
   */
  private BasicCollectionResult<BuyerEngagementDailyActivity> processQueryResults(@Nonnull QueryResponse queryResponse,
      final OrganizationUrn buyerOrganization, final OrganizationUrn sellerOrganization,
      final PagingContext pagingContext) {
    Preconditions.checkNotNull(queryResponse, "Argument queryResponse cannot be null");

    if (queryResponse.getRows() == null || queryResponse.getRows().isEmpty()) {
      return new BasicCollectionResult<>(Lists.newArrayList());
    }

    List<BuyerEngagementDailyActivity> segmentList = queryResponse.getRows()
        .stream()
        .map(Row::getValue)
        .filter(rowVals -> !isInvalidRow(rowVals))
        .map(rowVals -> convertRowValueToBuyerEngagementDailyActivity(rowVals, buyerOrganization, sellerOrganization))
        .collect(Collectors.toList());

    return new BasicCollectionResult<>(
        validPagingContext(pagingContext, segmentList) ? segmentList.subList(pagingContext.getStart(),
            Math.min(pagingContext.getStart() + pagingContext.getCount(), segmentList.size())) : Lists.newArrayList());
  }

  /**
   *  convert pinot row to BuyerEngagementDailyActivity
   *
   * @param rowVals
   * @param buyerOrganization
   * @param sellerOrganization
   * @return
   */
  private BuyerEngagementDailyActivity convertRowValueToBuyerEngagementDailyActivity(final Row.ValueArray rowVals,
      final OrganizationUrn buyerOrganization, final OrganizationUrn sellerOrganization) {
    BuyerEngagementDailyActivity segmentActivity = new BuyerEngagementDailyActivity();
    BuyerSegment segment = new BuyerSegment();

    segment
        .setSeniority(UrnUtils.createSeniorityUrn(getSeniorityId(rowVals.get(0).getInt())))
        .setFunction(UrnUtils.createFunctionUrn(getFunctionId(rowVals.get(1).getInt())))
        .setGeo(UrnUtils.createGeoUrn(getGeoId(rowVals.get(2).getLong())));
    segmentActivity
        .setBuyerOrganization(buyerOrganization)
        .setSellerOrganization(sellerOrganization)
        .setSegment(segment)
        .setEngagedOn(getEngagedOn(rowVals.get(3).getInt()))
        .setActivityCount(rowVals.get(4).getInt());

    return segmentActivity;
  }

  /**
   * check if the pagingContext has the valid parameters
   * @param pagingContext
   * @return
   */
  private boolean validPagingContext(final PagingContext pagingContext,
      Collection<BuyerEngagementDailyActivity> segmentList) {
    return pagingContext != null && pagingContext.getStart() <= segmentList.size();
  }

  /**
   * Seniority id shouldn't be null
   * If it was null, we should fix pinot data
   * @param seniorityId
   * @return
   */
  private long getSeniorityId(Integer seniorityId) {
    return Optional.ofNullable(seniorityId).orElse(-1);
  }

  /**
   * Function Id shouldn't be null
   * If it was null, we should fix pinot data
   * @param functionId
   * @return
   */
  private long getFunctionId(Integer functionId) {
    return Optional.ofNullable(functionId).orElse(-1);
  }

  /**
   * Geo Id shouldn't be null
   * If it was null, we should fix pinot data
   * @param geoId
   * @return
   */
  private long getGeoId(Long geoId) {
    return Optional.ofNullable(geoId).orElse(-1L);
  }

  /**
   * daysSinceEpoch shouldn't be null
   * If it was null, we should fix pinot data
   * @param daysSinceEpoch
   * @return
   */
  private Date getEngagedOn(Integer daysSinceEpoch) {
    if (daysSinceEpoch != null) {
      DateTime dateTime = new DateTime(TimeUnit.DAYS.toMillis(daysSinceEpoch));
      return new Date().setDay(dateTime.getDayOfMonth()).setMonth(dateTime.getMonthOfYear()).setYear(dateTime.getYear());
    }
    return null;
  }


  /**
   * check if row data is invalid
   * there are three facets returned (seniority, function, geo)
   * A valid row at least has one facet of them
   *
   * @param values
   * @return
   */
  private boolean isInvalidRow(final Row.ValueArray values) {
    return values.get(0).getInt() == null
        && values.get(1).getInt() == null
        && values.get(2).getLong() == null;
  }
}
