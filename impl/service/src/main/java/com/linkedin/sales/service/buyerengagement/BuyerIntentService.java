package com.linkedin.sales.service.buyerengagement;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.buyerengagement.BuyerIntentLevel;
import com.linkedin.buyerengagement.BuyerIntentTrend;
import com.linkedin.buyerengagement.BuyerIntentTrendKey;
import com.linkedin.buyerengagement.FeatureCategoryArray;
import com.linkedin.buyerengagement.FeatureCategoryName;
import com.linkedin.buyerengagement.FeatureCategoryScoreLevel;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.lssbuyer.schemas.venice.BuyerIntentTrendVeniceKey;
import com.linkedin.lssbuyer.schemas.venice.BuyerIntentTrendVeniceValue;
import com.linkedin.lssbuyer.schemas.venice.FeatureCategory;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.venice.client.store.AvroSpecificStoreClient;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.AbstractMap;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import scala.tools.jline_embedded.internal.Log;


/**
 * Service for getting buyer intent scores, which represents how likely a specific buyer will purchases from a
 * specific seller
 */
public class BuyerIntentService {
  // Keep the keys in alphabetical order
  private static final Map<String, FeatureCategoryName> FEATURE_CATEGORY_NAME_MAP =
      new ImmutableMap.Builder<String, FeatureCategoryName>()
          .put("ADS_ENGAGEMENT", FeatureCategoryName.ADS_ENGAGEMENT)
          .put("COMPANY_PAGE_ACTIVITY", FeatureCategoryName.COMPANY_PAGE_ACTIVITY)
          .put("INMAIL_ACTIVITY", FeatureCategoryName.INMAIL_ACTIVITY)
          .put("CONNECTION_ACTIVITY", FeatureCategoryName.CONNECTION_ACTIVITY)
          .put("POST_INTERACTION_ACTIVITY", FeatureCategoryName.POST_INTERACTION_ACTIVITY)
          .put("PROFILE_PAGE_ACTIVITY", FeatureCategoryName.PROFILE_PAGE_ACTIVITY)
          .build();

  private final AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> _veniceClientForTestC2cScores;
  private final AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> _veniceClientForProdV2C2cScores;
  private final LixService _lixService;
  private final GatewayCallerFinder _gatewayCallerFinder;
  // set to true if we perform A/B test
  @VisibleForTesting static boolean abTestEnabled = false;

  public BuyerIntentService(AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> veniceClientForTestC2cScores,
      AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> veniceClientForProdV2C2cScores,
      LixService lixService, GatewayCallerFinder gatewayCallerFinder) {
    _veniceClientForTestC2cScores = veniceClientForTestC2cScores; //v1 store for test
    _veniceClientForProdV2C2cScores = veniceClientForProdV2C2cScores; //v2 store
    _lixService = lixService;
    _gatewayCallerFinder = gatewayCallerFinder;
  }

  /**
   * Fetches buyer intent trend data for a specific buyer/seller pair.
   */
  public Task<BuyerIntentTrend> getTrend(ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> key, @Nullable ContractUrn sellerContract) {
    return batchGetTrends(ImmutableSet.of(key), sellerContract).map(trendMap -> {
      if (trendMap.isEmpty()) {
        throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND);
      }
      return trendMap.get(key);
    });
  }

  /**
   * Fetches buyer intent trend data for a set of buyer/seller pairs
   */
  public Task<Map<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>, BuyerIntentTrend>> batchGetTrends(
      Set<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>> keys,
      @Nullable  ContractUrn sellerContract) {
    if (keys.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    Set<BuyerIntentTrendVeniceKey> trendKeys = toBuyerTrendVeniceKeys(keys);
    // Check lix value to determine which venice store to query for fetching buyer intent scores
    MemberUrn memberUrn = Optional.ofNullable(_gatewayCallerFinder.getCaller())
        .map(gatewayCaller -> new com.linkedin.common.urn.MemberUrn(gatewayCaller.getMemberId()))
        .orElse(null);

    // If performing the A/B test for the new DL model:
    // 1) set the `abTestEnabled` to true
    // 2) set the `LSS_BUYER_INTEREST_DL_MODEL_TEST` to the A/B test lix name
    Task<Boolean> dlModelABTestEnabledTask = Task.value(Boolean.FALSE);
    if (abTestEnabled && sellerContract != null) {
      dlModelABTestEnabledTask = memberUrn == null ? _lixService.isContractBasedEEPLixEnabled(sellerContract,
          LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST) : _lixService.isContractBasedEEPLixEnabled(sellerContract,
          LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST, memberUrn);
    }

    // If lix is enabled, fetch scores from the test store.
    // If lix is not enabled, or contract is not present in input then fetch it from existing lssBuyerIntentTrendV2 store
    return dlModelABTestEnabledTask.flatMap(isEnabled -> Task.fromCompletionStage(() ->
        isEnabled ? _veniceClientForTestC2cScores.batchGet(trendKeys) // fetch from the v1 test store
            : _veniceClientForProdV2C2cScores.batchGet(trendKeys)) // fetch from the current v2 prod store
        .map(trendMap -> trendMap.entrySet().stream()
        .map(trendEntry -> {
            BuyerIntentTrendKey trendKey = fromBuyerTrendVeniceKey(trendEntry.getKey());
            BuyerIntentTrend trend = fromBuyerIntentTrendVeniceValue(trendKey, trendEntry.getValue());
            return new AbstractMap.SimpleEntry<>(trendKey, trend);
          })
          .collect(Collectors.toMap(
              entry -> new ComplexResourceKey<>(entry.getKey(), new EmptyRecord()),
              Map.Entry::getValue)))
    );
  }

  /**
   * Deserializes the {@link BuyerIntentLevel} from int.
   * See <a href="http://go/buyerIntentLevel">the logic to serialize {@link BuyerIntentLevel} for storage in venice</a>
   */
  @VisibleForTesting
  BuyerIntentLevel intentLevelFromInt(int numericLevel) {
    switch (numericLevel) {
      case -1:
        return BuyerIntentLevel.BELOW_BASELINE;
      case 0:
        return BuyerIntentLevel.BASELINE;
      case 1:
        return BuyerIntentLevel.MEDIUM;
      case 2:
        return BuyerIntentLevel.HIGH;
      default:
        throw new RuntimeException(String.format("Unexpected buyer intent level: %d", numericLevel));
    }
  }

  /**
   * Deserializes the {@link FeatureCategoryScoreLevel} from int.
   */
  @VisibleForTesting
  FeatureCategoryScoreLevel featureCategoryLevelFromInt(int numericLevel) {
    switch (numericLevel) {
      case -1:
        return FeatureCategoryScoreLevel.LOW;
      case 0:
        return FeatureCategoryScoreLevel.MEDIUM;
      case 1:
        return FeatureCategoryScoreLevel.HIGH;
      case 2:
        return FeatureCategoryScoreLevel.VERY_HIGH;
      default:
        throw new RuntimeException(String.format("Unexpected buyer intent feature score category level: %d", numericLevel));
    }
  }

  /**
   * Converts {@link BuyerIntentTrendVeniceValue} to {@link BuyerIntentTrend} for use in lss-mt
   * @param trendKey the venice value doesn't provide all the necessary fields needed so trendKey is needed
   *                 to provide more data
   * @param veniceValue the value from venice
   */
  private BuyerIntentTrend fromBuyerIntentTrendVeniceValue(BuyerIntentTrendKey trendKey, BuyerIntentTrendVeniceValue veniceValue) {
    BuyerIntentLevel buyerIntentLevel = intentLevelFromInt(veniceValue.getLevel());
    FeatureCategoryArray featureCategories = fromBuyerTrendVeniceFeatureCategories(veniceValue.getFeatureCategories());
    return new BuyerIntentTrend()
        .setBuyer(trendKey.getBuyer())
        .setSeller(trendKey.getSeller())
        .setScore(veniceValue.getScore())
        .setAdvantageScore(veniceValue.getAdvantageScore(), SetMode.REMOVE_OPTIONAL_IF_NULL)
        .setLevel(buyerIntentLevel)
        .setLevelVersion(String.valueOf(veniceValue.getLevelVersion()))
        .setModelId(String.valueOf(veniceValue.getModelId()))
        .setPercentScoreChange7Day(veniceValue.getPercentScoreChange7Day(), SetMode.REMOVE_OPTIONAL_IF_NULL)
        .setPercentScoreChange30Day(veniceValue.getPercentScoreChange30Day(), SetMode.REMOVE_OPTIONAL_IF_NULL)
        .setCalculatedAt(veniceValue.getCalculatedAt())
        .setFeatureCategories(clearOutlierFeatureCategoryScore(buyerIntentLevel, featureCategories));
  }

  /**
   * Handle outlier buyerIntentLevel and featureCategories
   * Feature categories are not full representation of buyerIntentLevel. It is unavoidable to have weird combinations
   * that make people feel weird.
   *
   * For intent level = 2, suppress any feature category intent level <= 0
   * For intent level = 1, suppress any feature category intent level <= 0
   * For intent level = 0, suppress everything since we don't plan to show anything in UI
   * For intent level < 0, suppress any feature category intent level >= 0
   *
   */
  private FeatureCategoryArray clearOutlierFeatureCategoryScore(
      BuyerIntentLevel buyerIntentLevel, FeatureCategoryArray featureCategories) {
    switch (buyerIntentLevel) {
      case HIGH:
      case MEDIUM:
        return new FeatureCategoryArray(featureCategories.stream().filter(featureCategory ->
            featureCategory.getLevel() == FeatureCategoryScoreLevel.HIGH
                || featureCategory.getLevel() == FeatureCategoryScoreLevel.VERY_HIGH).collect(Collectors.toList()));
      case BASELINE:
        return new FeatureCategoryArray();
      case BELOW_BASELINE:
        // BELOW_BASELINE should have at least 1 LOW category
        return new FeatureCategoryArray(featureCategories.stream().filter(featureCategory ->
            featureCategory.getLevel() == FeatureCategoryScoreLevel.LOW).collect(Collectors.toList()));
      default:
        throw new IllegalStateException(String.format("Unsupported BuyerIntentLevel %s", buyerIntentLevel));
    }
  }

  private FeatureCategoryArray fromBuyerTrendVeniceFeatureCategories(
      Collection<FeatureCategory> veniceFeatureCategories) {
    return new FeatureCategoryArray(
        veniceFeatureCategories.stream()
            .filter(veniceFeatureCategory -> {
              // Gracefully handle unmatched category name by only throwing error.
              String name = veniceFeatureCategory.getName().toString();
              boolean nameFound = FEATURE_CATEGORY_NAME_MAP.containsKey(name);
              if (!nameFound) {
                Log.error("There is unrecognized buyer intent feature category name {}", name);
              }
              return nameFound;
            })
            .map(veniceFeatureCategory ->
                new com.linkedin.buyerengagement.FeatureCategory()
                    .setName(FEATURE_CATEGORY_NAME_MAP.get(veniceFeatureCategory.getName().toString()))
                    .setLevel(featureCategoryLevelFromInt(veniceFeatureCategory.getLevel())))
            .collect(Collectors.toList()));
  }

  /**
   * Converts {@link BuyerIntentTrendVeniceKey} to {@link BuyerIntentTrendKey} for use in lss-mt
   */
  private BuyerIntentTrendKey fromBuyerTrendVeniceKey(BuyerIntentTrendVeniceKey veniceKey) {
    return new BuyerIntentTrendKey()
        .setBuyer(UrnUtils.createOrganizationUrn(veniceKey.getBuyerId()))
        .setSeller(UrnUtils.createOrganizationUrn(veniceKey.getSellerId()));
  }

  /**
   * Converts {@link BuyerIntentTrendKey} keys passed into lss-mt to {@link BuyerIntentTrendVeniceKey} keys to query venice
   */
  private Set<BuyerIntentTrendVeniceKey> toBuyerTrendVeniceKeys(Set<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>> keys) {
    return keys.stream().map(key -> {
      BuyerIntentTrendKey trendKey = key.getKey();
      return BuyerIntentTrendVeniceKey.newBuilder()
          .setBuyerId(trendKey.getBuyer().getIdAsLong())
          .setSellerId(trendKey.getSeller().getIdAsLong())
          .build();
    }).collect(Collectors.toSet());
  }
}
