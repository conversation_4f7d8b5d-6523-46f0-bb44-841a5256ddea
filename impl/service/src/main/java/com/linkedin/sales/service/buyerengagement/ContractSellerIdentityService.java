package com.linkedin.sales.service.buyerengagement;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.espresso.Product;
import com.linkedin.sales.service.TrackingService;
import com.linkedin.sales.service.utils.UrnUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pegasus.com.linkedin.buyerengagement.ContractSellerIdentity;

// Service class for encapsulating business logic around Contract Seller Identity
@SuppressFBWarnings("SSCU_SUSPICIOUS_SHADED_CLASS_USE")
public class ContractSellerIdentityService {
  private static final Logger LOG = LoggerFactory.getLogger(ContractSellerIdentityService.class);
  private final LssBuyerDB _lssBuyerDB;
  private final TrackingService _trackingService;

  public ContractSellerIdentityService(LssBuyerDB lssBuyerDB, TrackingService trackingService) {
    _lssBuyerDB = lssBuyerDB;
    _trackingService = trackingService;
  }

  /**
   * Fetches contract seller identity for the given contract id
   *
   * @param contractId contract id for which seller identity needs to be fetched
   * @return a contract seller identity.
   */
  public Task<ContractSellerIdentity> getContractSellerIdentity(long contractId) {
    ContractUrn contractUrn = new ContractUrn(contractId);
    return _lssBuyerDB.getContractSellerIdentity(new ContractUrn(contractId))
        .map(maybeBackendContractSellerIdentity -> maybeBackendContractSellerIdentity
            .map(backendContractSellerIdentity -> convertToApiSellerIdentity(backendContractSellerIdentity, contractUrn))
            .orElse(null))
        .recover(throwable -> {
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
              "Failed to extract contract seller identity", throwable);
        });
  }

  /**
   * Add a new admin product in contract
   *
   * @param contractUrn Urn of the contract for which a new product is being added
   * @param seatUrn Actor who is performing the action.
   * @param product new product
   * @param sessionId sessionId used for tracking
   * @return an Action Result with id of newly created product.
   */
  public Task<ActionResult<String>> addProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull SellerIdentityProduct product, String sessionId) {
    if (product.hasId()) {
      LOG.error("Product should not have any id, product : {}", product);
      return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
    }
    long currTime = System.currentTimeMillis();
    product.setId(UUID.randomUUID().toString()).setCreatedBy(seatUrn)
        .setLastModifiedBy(seatUrn)
        .setCreatedTime(currTime)
        .setLastModifiedTime(currTime);
    return getContractSellerIdentity(contractUrn.getIdAsLong())
        .flatMap(contractSellerIdentity -> {
          if (contractSellerIdentity == null) {
            ContractSellerIdentity emptyContractSellerIdentity = new ContractSellerIdentity()
                .setContractUrn(contractUrn)
                .setSellerIdentityProducts(new SellerIdentityProductArray());
            return Task.value(emptyContractSellerIdentity);
          }
          return Task.value(contractSellerIdentity);
        }).flatMap(contractSellerIdentity -> {
          contractSellerIdentity.getSellerIdentityProducts().add(product);
          return updateContractSellerIdentity(contractUrn, product, convertToEspressoContractSellerIdentity(contractSellerIdentity))
              .map(actionResult -> {
                _trackingService.createAndSendOutProductCollectFunnelSaveProductTrackingEvent(
                    seatUrn, sessionId, product, actionResult.getStatus() == HttpStatus.S_500_INTERNAL_SERVER_ERROR
                );
                return actionResult;
              });
        });
  }

  /**
   * Updates an existing admin product in contract
   *
   * @param contractUrn Urn of the contract for which an existing product is being modified
   * @param seatUrn Actor who is performing the action.
   * @param sellerIdentityProduct updated product
   * @return an Action Result for update action
   */
  public Task<ActionResult<Void>> updateProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull SellerIdentityProduct sellerIdentityProduct) {
    if (!sellerIdentityProduct.hasId()) {
      LOG.error("Product must be existing one: {}", sellerIdentityProduct);
      return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
    }
    return getContractSellerIdentity(contractUrn.getContractIdEntity())
        .flatMap(contractSellerIdentity -> {
          if (contractSellerIdentity == null) {
            LOG.error("No seller identity found for the contract: {}", contractUrn);
            return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
          }
          String productId = sellerIdentityProduct.getId();
          sellerIdentityProduct.setLastModifiedBy(seatUrn);
          sellerIdentityProduct.setLastModifiedTime(System.currentTimeMillis());
          for (int i = 0; i < contractSellerIdentity.getSellerIdentityProducts().size(); i++) {
            if (productId.equals(contractSellerIdentity.getSellerIdentityProducts().get(i).getId())) {
              SellerIdentityProduct existingProduct = contractSellerIdentity.getSellerIdentityProducts().get(i);
              sellerIdentityProduct.setCreatedTime(existingProduct.getCreatedTime());
              sellerIdentityProduct.setCreatedBy(existingProduct.getCreatedBy());
              contractSellerIdentity.getSellerIdentityProducts().set(i, sellerIdentityProduct);
              return _lssBuyerDB.createOrUpdateContractSellerIdentity(contractUrn, convertToEspressoContractSellerIdentity(contractSellerIdentity))
                    .map(updateResponse -> new ActionResult<>(updateResponse));
            }
          }
          // If the product id is not found in the seller identity, return 412
          LOG.error("Failed to update because no product with id {} in seller identity record.", productId);
          return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
        });
  }

  private Task<ActionResult<String>> updateContractSellerIdentity(@NonNull ContractUrn contractUrn,
      SellerIdentityProduct product, com.linkedin.sales.espresso.ContractSellerIdentity contractSellerIdentity) {
    return _lssBuyerDB.createOrUpdateContractSellerIdentity(contractUrn, contractSellerIdentity)
        .map(httpStatus -> new ActionResult<>(product.getId(), httpStatus))
        .recover(throwable ->  {
          LOG.error(String.format("Failed to update contract seller identity for contract: %s", contractUrn), throwable);
          return new ActionResult<>(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
        });
  }

  @VisibleForTesting
  Product convertToEspressoProduct(SellerIdentityProduct sellerIdentityProduct) {
    Product product = new Product();
    product.setProductId(sellerIdentityProduct.getId());

    Optional.ofNullable(sellerIdentityProduct.getProductCategory())
        .ifPresent(productCategory -> {
          if (productCategory.isStandardizedProductCategory()) {
            product.setStandardizedProductCategoryUrn(productCategory.getStandardizedProductCategory().toString());
          } else {
            product.setProductCategoryName(productCategory.getProductCategoryName());
          }
        });

    if (sellerIdentityProduct.getProduct().isProductName()) {
      product.setProductName(sellerIdentityProduct.getProduct().getProductName());
    } else {
      product.setStandardizedProductUrn(sellerIdentityProduct.getProduct().getStandardizedProduct().toString());
    }

    Optional.ofNullable(sellerIdentityProduct.getProductUrl()).map(Url::toString).ifPresent(product::setProductUrl);
    Optional.ofNullable(sellerIdentityProduct.getDescription()).ifPresent(product::setProductDescription);
    product.setCreatedTime(sellerIdentityProduct.getCreatedTime());
    product.setLastModifiedTime(sellerIdentityProduct.getLastModifiedTime());
    product.setCreatedBy(sellerIdentityProduct.getCreatedBy().toString());
    product.setLastModifiedBy(sellerIdentityProduct.getLastModifiedBy().toString());
    return product;
  }

  private ContractSellerIdentity convertToApiSellerIdentity(com.linkedin.sales.espresso.ContractSellerIdentity backendContractSellerIdentity,
      ContractUrn contractUrn) {
    return new ContractSellerIdentity()
        .setContractUrn(contractUrn)
        .setSellerIdentityProducts(new SellerIdentityProductArray(convertToSellerIdentityProducts(backendContractSellerIdentity.getProducts())));
  }

  private List<SellerIdentityProduct> convertToSellerIdentityProducts(List<Product> products) {
    return products
        .stream()
        .map(this::convertFromEspressoProduct)
    .collect(Collectors.toList());
  }

  private com.linkedin.sales.espresso.ContractSellerIdentity convertToEspressoContractSellerIdentity(ContractSellerIdentity contractSellerIdentity) {
    return new com.linkedin.sales.espresso.ContractSellerIdentity(convertToEspressoSellerIdentityProducts(
        contractSellerIdentity.getSellerIdentityProducts()));
  }

  private List<Product> convertToEspressoSellerIdentityProducts(
      List<SellerIdentityProduct> sellerIdentityProducts) {
    return sellerIdentityProducts
        .stream()
        .map(this::convertToEspressoProduct)
        .collect(Collectors.toList());
  }

  private SellerIdentityProduct convertFromEspressoProduct(Product product) {
    SellerIdentityProduct sellerIdentityProduct = new SellerIdentityProduct()
        .setId(product.getProductId().toString());
    // Product
    if (product.getStandardizedProductUrn() != null) {
      sellerIdentityProduct.setProduct(SellerIdentityProduct.Product.createWithStandardizedProduct(
          UrnUtils.createStandardizedProductUrn(product.getStandardizedProductUrn())));
    } else {
      sellerIdentityProduct.setProduct(SellerIdentityProduct.Product.createWithProductName(product.getProductName().toString()));
    }

    // Product Category
    if (product.getStandardizedProductCategoryUrn() != null) {
      sellerIdentityProduct.setProductCategory(SellerIdentityProduct.ProductCategory.createWithStandardizedProductCategory(
          UrnUtils.createStandardizedProductCategoryUrn(product.getStandardizedProductCategoryUrn())));
    } else {
      sellerIdentityProduct.setProductCategory(SellerIdentityProduct.ProductCategory.createWithProductCategoryName(
          product.getProductCategoryName().toString()));
    }

    sellerIdentityProduct.setCreatedTime(product.getCreatedTime())
        .setCreatedBy(UrnUtils.createSeatUrn(product.getCreatedBy()))
        .setLastModifiedTime(product.getLastModifiedTime())
        .setLastModifiedBy(UrnUtils.createSeatUrn(product.getLastModifiedBy()));

    Optional.ofNullable(product.getProductDescription())
        .map(CharSequence::toString)
        .ifPresent(sellerIdentityProduct::setDescription);

    Optional.ofNullable(product.getCustomerFunctionUrns())
        .map(UrnUtils::formatFunctionUrns)
        .ifPresent(sellerIdentityProduct::setCustomerFunctions);

    Optional.ofNullable(product.getProductUrl())
        .map(CharSequence::toString)
        .map(Url::new)
        .ifPresent(sellerIdentityProduct::setProductUrl);
    return sellerIdentityProduct;
  }

  /**
   * Removes given the product from ContractSellerIdentity for the given contract Id
   *
   * @param contractUrn urn of the contract whose product should be removed
   * @param productId identifier of the product which needs to be removed
   * @return an Action result for deletion action
   */
  public Task<ActionResult<Void>> removeProduct(ContractUrn contractUrn, String productId) {
    return getContractSellerIdentity(contractUrn.getIdAsLong())
        .flatMap(contractSellerIdentity -> {
          if (contractSellerIdentity == null) {
            LOG.error("No Contract Seller Identity for that contract: {}", contractUrn);
            return Task.value(new ActionResult<>(HttpStatus.S_404_NOT_FOUND));
          }
          for (int i = 0; i < contractSellerIdentity.getSellerIdentityProducts().size(); i++) {
            if (productId.equals(contractSellerIdentity.getSellerIdentityProducts().get(i).getId())) {
              contractSellerIdentity.getSellerIdentityProducts().remove(i);
              return _lssBuyerDB.createOrUpdateContractSellerIdentity(contractUrn,
                      convertToEspressoContractSellerIdentity(contractSellerIdentity))
                  .map(ActionResult::new);
            }
          }
          // If the product id is not found in the seller identity, return 412
          return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
        });
  }
}