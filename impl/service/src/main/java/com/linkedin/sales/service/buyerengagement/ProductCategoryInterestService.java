package com.linkedin.sales.service.buyerengagement;

import com.linkedin.buyerengagement.ProductCategoryInterestKey;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.buyerengagement.ProductCategoryInterest;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Service class of Product Category Interest
 * created by guanwang 10/2022
 */
public class ProductCategoryInterestService {
  private static final Logger LOG = LoggerFactory.getLogger(ProductCategoryInterestService.class);
  private final LssBuyerDB _lssBuyerDB;

  public ProductCategoryInterestService(LssBuyerDB lssBuyerDB) {
    _lssBuyerDB = lssBuyerDB;
  }

  private static final String INVALID_PRODUCT_URN_STR = StandardizedProductUrn.createFromTuple(
      StandardizedProductUrn.ENTITY_TYPE, -1L).toString();
  private static final String INVALID_CATEGORY_URN_STR = StandardizedProductCategoryUrn.createFromTuple(
      StandardizedProductCategoryUrn.ENTITY_TYPE, -1L).toString();

  /**
   * Batch create Product Category Interest records.
   * @param interests list of interest records to be created.
   * @return a list of create responses indicating the result of each create operation.
   */
  public Task<List<CreateResponse>> batchCreateInterests(
      @NonNull List<ProductCategoryInterest> interests) {
    // if input list is empty, return empty immediately.
    if (interests.isEmpty()) {
      return Task.value(Collections.emptyList());
    }
    // call createInterest() for each request to get a list of tasks.
    List<Task<CreateResponse>> createTasks = interests.stream()
        .map(this::createInterest)
        .collect(Collectors.toList());
    // collect results. if par() failed, create error response for each create request.
    return Task.par(createTasks)
        .recover(throwable -> {
          LOG.error("Batch create interests failed.", throwable);
          return interests.stream()
              .map(interest -> new CreateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR))
              .collect(Collectors.toList());
        });
  }

  /**
   * Partial update a batch of Product Category Interest records.
   * @param patchMap the interest key to interest patch map
   * @return a map of interest key to update response indicating the result of each update operation.
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchPartialUpdateInterests(
      Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord>, PatchRequest<ProductCategoryInterest>> patchMap) {
    // if patch map is empty, return.
    if (patchMap.isEmpty()) {
      Task.value(Collections.emptyMap());
    }
    // call partial update for each patch request.
    List<Task<Pair<CompoundKey, UpdateResponse>>> taskList = new ArrayList<>();
    patchMap.forEach((resourceKey, patchRequest) -> {
      ProductCategoryInterestKey interestKey = resourceKey.getKey();
      taskList.add(partialUpdateProductCategoryInterest(interestKey, patchRequest));
    });
    // collect results. if par() failed, create error response for each update request.
    return Task.par(taskList)
        .map(responseList -> responseList.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)))
        .recover(throwable -> {
          LOG.error("Batch partial update interests failed.", throwable);
          return getUpdateResponseMap(patchMap.keySet().stream().map(ComplexResourceKey::getKey).collect(Collectors.toList()),
              HttpStatus.S_500_INTERNAL_SERVER_ERROR);
        });
  }

  /**
   * Batch delete Product Category Interest records.
   * @param deletedKeys the keys of the records to be deleted.
   * @return a map of interest key to update response indicating the result of each delete operation.
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeleteInterests(List<ProductCategoryInterestKey> deletedKeys) {
    // if key list is empty, directly return.
    if (deletedKeys.isEmpty()) {
      Task.value(Collections.emptyMap());
    }
    // call delete method for each key and get a task list.
    List<Task<Pair<CompoundKey, UpdateResponse>>> taskList = deletedKeys.stream().map(this::deleteInterest)
        .collect(Collectors.toList());
    // collect result and build the map
    return Task.par(taskList)
        .map(responseList -> responseList.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)))
        .recover(throwable -> {
          LOG.error("Batch delete interests failed.", throwable);
          return getUpdateResponseMap(deletedKeys, HttpStatus.S_500_INTERNAL_SERVER_ERROR);
        });
  }

  /**
   * Find Product Category Interest records within specified categories for a seat.
   * @param seatUrn the seat urn of the user
   * @param categoryNames an array of category names. If empty given, find all categories.
   * @param start the start index of page context.
   * @param count the count of page context
   * @return a list of interest records
   */
  public Task<List<ProductCategoryInterest>> findInterests(@NonNull SeatUrn seatUrn, @NonNull String[] categoryNames,
      int start, int count) {
    // call the corresponding method of DB to get Espresso interest records.
    return _lssBuyerDB.findProductCategoryInterests(seatUrn, categoryNames, start, count).map(resultList ->
        resultList.stream().map(result -> {
          // convert Espresso record to the record format defind in API
          Long interestId = result.getFirst();
          com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest =  result.getSecond();
          return convertEspressoInterestInterest(seatUrn, interestId, espressoInterest);
        })
            .collect(Collectors.toList()))
        .recover(throwable -> {
          LOG.error("Find Product Category Interests failed.", throwable);
          return Collections.emptyList();
        });
  }

  /**
   * Convert ProductCategoryInterestKey to CompoundKey
   * @param interestKey the key in schema defined in API
   * @return the compound key
   */
  public CompoundKey toCompoundKey(ProductCategoryInterestKey interestKey) {
    return new CompoundKey()
        .append(SEAT_COMPOUND_KEY, interestKey.getSeat())
        .append(INTEREST_ID_COMPOUND_KEY, interestKey.getInterestId());
  }

  /**
   * Create a map of compound key to update response
   * @param keys list of keys of the map
   * @param httpStatus the http status code used in the update response
   * @return a map of compound key to update response
   */
  public Map<CompoundKey, UpdateResponse> getUpdateResponseMap(java.util.Collection<ProductCategoryInterestKey> keys,
      HttpStatus httpStatus) {
    return keys.stream().map(key -> {
      UpdateResponse response = new UpdateResponse(httpStatus);
      return new AbstractMap.SimpleEntry<>(toCompoundKey(key), response);
    })
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }

  /**
   * Create a Product Category Interest record.
   * @param interest the interest record to be created
   * @return create response indicating the result of the creation operation
   */
  private Task<CreateResponse> createInterest(ProductCategoryInterest interest) {
    // the PCI record uses synthetic key. the creation should always succeed.
    return _lssBuyerDB.createProductCategoryInterest(interest.getSeat(), convertInterestToEspressoInterest(interest))
        .map(interestId -> new CreateResponse(interestId, HttpStatus.S_201_CREATED))
        .recover(throwable -> {
          String errorMsg = String.format("Failed to create interest, %s", interest);
          LOG.warn(errorMsg, throwable);
          return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMsg));
        });
  }

  /**
   * Get a single Product Category Interest record by seat and interest Id
   * @param seatUrn the seat urn of the user
   * @param interestId the auto-generated Id for each interest record.
   * @return the interest record, or null.
   */
  private Task<ProductCategoryInterest> getInterest(@NonNull SeatUrn seatUrn, @NonNull Long interestId) {
    return _lssBuyerDB.getProductCategoryInterest(seatUrn, interestId)
        .map(espressoInterest -> convertEspressoInterestInterest(seatUrn, interestId, espressoInterest))
        .recover(throwable -> {
          String errorMsg = String.format("Failed to get interest for seat:%s, interestId:%d", seatUrn, interestId);
          LOG.warn(errorMsg, throwable);
          return null;
        });
  }

  /**
   * Partial update a Product Category Interest record.
   * @param interestKey the key of the record to be updated
   * @param interestPatch the update patch of the record
   * @return the key and update response of this operation
   */
  private Task<Pair<CompoundKey, UpdateResponse>> partialUpdateProductCategoryInterest(ProductCategoryInterestKey interestKey,
      PatchRequest<ProductCategoryInterest> interestPatch) {
    // get the target interest record by key.
    CompoundKey compoundKey = toCompoundKey(interestKey);
    return getInterest(interestKey.getSeat(), interestKey.getInterestId())
        .flatMap(interest -> {
          // if target not found, return 404.
          if (interest == null) {
            return Task.value(new Pair<>(compoundKey, new UpdateResponse(HttpStatus.S_404_NOT_FOUND)));
          } else {
            // patch on the existing record
            try {
              PatchApplier.applyPatch(interest, interestPatch);
            } catch (DataProcessingException e) {
              return Task.value(new Pair<>(compoundKey, new UpdateResponse(HttpStatus.S_400_BAD_REQUEST)));
            }
          }
          // convert patched record to Espresso schema and call DB API to update
          return _lssBuyerDB.updateProductCategoryInterest(interestKey.getSeat(), interestKey.getInterestId(),
              convertInterestToEspressoInterest(interest))
              .map(httpStatus -> new Pair<>(compoundKey, new UpdateResponse(httpStatus)));
        })
        .recover(throwable -> {
          String errorMsg = String.format("Failed to update interest with key: %s", interestKey);
          LOG.warn(errorMsg, throwable);
          return new Pair<>(compoundKey, new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        });
  }

  /**
   * Delete a Product Category Interest record.
   * @param interestKey the key of record to be deleted
   * @return the key and update response of the deletion operation
   */
  private Task<Pair<CompoundKey, UpdateResponse>> deleteInterest(ProductCategoryInterestKey interestKey) {
    // delete by seat and interest Id. return either 204 or 404.
    CompoundKey compoundKey = toCompoundKey(interestKey);
    return _lssBuyerDB.deleteProductCategoryInterest(interestKey.getSeat(), interestKey.getInterestId())
        .map(httpStatus -> new Pair<>(compoundKey, new UpdateResponse(httpStatus)))
        .recover(throwable -> {
          String errorMsg = String.format("Failed to delete interest with key: %s", interestKey);
          LOG.warn(errorMsg, throwable);
          return new Pair<>(compoundKey, new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        });
  }

  /**
   * Convert API schema interest to Espresso schema interest
   * @param interest the API schema interest
   * @return Espresso schema interest
   */
  private com.linkedin.sales.espresso.ProductCategoryInterest convertInterestToEspressoInterest(ProductCategoryInterest interest) {
    com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest = new com.linkedin.sales.espresso.ProductCategoryInterest();
    espressoInterest.setProductName(interest.getProductName());
    if (interest.getStandardizedProduct() != null) {
      espressoInterest.setStandardizedProductUrn(interest.getStandardizedProduct().toString());
    } else {
      espressoInterest.setStandardizedProductUrn(INVALID_PRODUCT_URN_STR);
    }
    if (interest.getProductUrl() != null) {
      espressoInterest.setProductUrl(interest.getProductUrl().toString());
    } else {
      espressoInterest.setProductUrl(null);
    }
    espressoInterest.setCategoryName(interest.getCategoryName());
    if (interest.getStandardizedProductCategory() != null) {
      espressoInterest.setStandardizedProductCategoryUrn(interest.getStandardizedProductCategory().toString());
    } else {
      espressoInterest.setStandardizedProductCategoryUrn(INVALID_CATEGORY_URN_STR);
    }
    long currentTime = System.currentTimeMillis();
    espressoInterest.setCreatedTime(currentTime);
    espressoInterest.setLastModifiedTime(currentTime);
    return espressoInterest;
  }

  /**
   * Convert Espresso schema interest to API schema interest
   * @param seatUrn seat urn of the user
   * @param interestId auto-generated interest Id of the record
   * @param espressoInterest Espresso schema interest
   * @return API schema interest
   */
  private ProductCategoryInterest convertEspressoInterestInterest(SeatUrn seatUrn, Long interestId,
      com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest) {
    ProductCategoryInterest interest = new ProductCategoryInterest();
    interest.setSeat(seatUrn);
    interest.setInterestId(interestId);
    interest.setProductName(espressoInterest.getProductName().toString());
    String productUrnStr = espressoInterest.getStandardizedProductUrn().toString();
    if (!INVALID_PRODUCT_URN_STR.equals(productUrnStr)) {
      try {
        interest.setStandardizedProduct(StandardizedProductUrn.deserialize(productUrnStr));
      } catch (URISyntaxException e) {
        LOG.warn("Unable to deserialize StandardizedProductUrn", e);
      }
    }
    if (espressoInterest.getProductUrl() != null) {
      interest.setProductUrl(new Url(espressoInterest.getProductUrl().toString()));
    }
    interest.setCategoryName(espressoInterest.getCategoryName().toString());
    String categoryUrnStr = espressoInterest.getStandardizedProductCategoryUrn().toString();
    if (!INVALID_CATEGORY_URN_STR.equals(categoryUrnStr)) {
      try {
        interest.setStandardizedProductCategory(StandardizedProductCategoryUrn.deserialize(categoryUrnStr));
      } catch (URISyntaxException e) {
        LOG.warn("Unable to deserialize StandardizedProductCategoryUrn", e);
      }
    }
    return interest;
  }
}
