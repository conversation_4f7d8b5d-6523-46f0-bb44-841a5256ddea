package com.linkedin.sales.service.buyerengagement;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.buyerengagement.SellerIdentityServiceArray;
import com.linkedin.buyerengagement.SellerIdentityTargetType;
import com.linkedin.common.OrganizationUrnArray;
import com.linkedin.common.SalesListUrnArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.FunctionUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.admin.ContractSettingType;
import com.linkedin.sales.client.common.SalesContractSettingsClient;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.AutoProspectingSetting;
import com.linkedin.sales.espresso.SellerIdentityPosition;
import com.linkedin.sales.espresso.SellerIdentityProduct;
import com.linkedin.sales.espresso.SellerIdentityService;
import com.linkedin.sales.espresso.TargetType;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSettingArray;

@SuppressFBWarnings("SSCU_SUSPICIOUS_SHADED_CLASS_USE")
public class SalesSellerIdentityService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesSellerIdentityService.class);
  private final LssBuyerDB _lssBuyerDB;
  private final SalesContractSettingsClient _salesContractSettingsClient;
  private final LixService _lixService;

  public SalesSellerIdentityService(LssBuyerDB lssBuyerDB, SalesContractSettingsClient salesContractSettingsClient,
      LixService lixService) {
    _lssBuyerDB = lssBuyerDB;
    _salesContractSettingsClient = salesContractSettingsClient;
    _lixService = lixService;
  }

  /**
   * Create or Partial Update a Seat Seller Identity record.
   * @param contractUrn the first part of the key of the record to be created/updated
   * @param seatUrn the second part of the key of the record to be created/updated
   * @param patch the patch of the record (can be a full record for create)
   * @return update response of this operation
   */
  public Task<UpdateResponse> updateSellerIdentity(@Nonnull ContractUrn contractUrn, @Nonnull SeatUrn seatUrn, PatchRequest<SeatSellerIdentity> patch) {
    return getSellerIdentity(contractUrn, seatUrn).flatMap(identity -> {
      SeatSellerIdentity sellerIdentity = Objects.isNull(identity) ? new SeatSellerIdentity() : identity;
      try {
        PatchApplier.applyPatch(sellerIdentity, patch);
      } catch (DataProcessingException e) {
        String errMsg = String.format("Failed to process patch for update operation for contract: %s, seat: %s, patch: %s", contractUrn, seatUrn, patch);
        LOG.error(errMsg, e);
        return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
      }
      return _lssBuyerDB.updateSeatSellerIdentity(contractUrn, seatUrn, convertToEspressoSellerIdentity(sellerIdentity)).map(UpdateResponse::new);
    }).recover(throwable -> {
      String errMsg = String.format("Failed to get seller identity for partial update/create operation with contract: %s, seat: %s", contractUrn, seatUrn);
      LOG.error(errMsg, throwable);
      return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    });
  }

  /**
   * Create or Update a Seat Seller Identity record.
   * @param contractUrn the first part of the key of the record to be created/updated
   * @param seatUrn the second part of the key of the record to be created/updated
   * @param identity the full Seat Seller Identity record for create/update
   * @return update response of this operation
   */
  public Task<UpdateResponse> updateSellerIdentity(@Nonnull ContractUrn contractUrn, @Nonnull SeatUrn seatUrn, SeatSellerIdentity identity) {
      return _lssBuyerDB.updateSeatSellerIdentity(contractUrn, seatUrn, convertToEspressoSellerIdentity(identity)).map(UpdateResponse::new)
          .recover(throwable -> {
            String errMsg = String.format("Failed to update seller identity with contract: %s, seat: %s", contractUrn, seatUrn);
            LOG.error(errMsg, throwable);
            return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
      });
  }

  /**
   * Get a Seat Seller Identity record.
   * @param contractUrn the urn of the contract
   * @param seatUrn the urn of the seat
   * @return a SeatSellerIdentity espresso record in API schema format
   */
  public Task<SeatSellerIdentity> getSellerIdentity(@Nonnull ContractUrn contractUrn, @Nonnull SeatUrn seatUrn) {
    return Task.par(isSeatManagedProductsEnabled(contractUrn), _lssBuyerDB.getSellerIdentity(contractUrn, seatUrn))
        .map((shouldSetSeatManagedProducts, espressoIdentity) -> convertToApiSellerIdentity(contractUrn, seatUrn,
            espressoIdentity, shouldSetSeatManagedProducts))
        .recover(throwable -> {
          if (ExceptionUtils.isEntityNotFoundException(throwable)) {
            //return record or null for update
            return null;
          }
          String errMsg =
              String.format("Failed to get seller identity for contract: %s, seat: %s, %s", contractUrn, seatUrn,
                  throwable);
          LOG.error(errMsg);
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
        });
  }

  /**
   *
   * @param contractUrn the urn of the contract
   * @param start the start index of the page context
   * @param count the count of page context
   * @return a list of SeatSellerIdentity espresso records in API schema format
   */
  public Task<List<SeatSellerIdentity>> findSellerIdentities(@Nonnull ContractUrn contractUrn, int start, int count) {
    return Task.par(isSeatManagedProductsEnabled(contractUrn),
            _lssBuyerDB.findSellerIdentities(contractUrn, null, start, count))
        .map((shouldSetSeatManagedProducts, sellerIdentities) -> sellerIdentities.stream()
            .map(sellerIdentity -> convertToApiSellerIdentity(contractUrn, sellerIdentity.getFirst(),
                sellerIdentity.getSecond(), shouldSetSeatManagedProducts))
            .collect(Collectors.toList()))
        .recover(throwable -> {
          String errMsg = String.format("Find Seller Identities failed for contract: %s", contractUrn);
          LOG.error(errMsg, throwable);
          return Collections.emptyList();
        });
  }

  /**
   * Convert Espresso schema Seller Identity to API schema Seller Identity
   * @param contractUrn the urn of the contract
   * @param seatUrn the urn of the seat
   * @param espressoSellerIdentity the Seat Seller Identity espresso record
   * @param shouldSetSeatManagedProducts a boolean indicating if seat managed products should be set for seller identity
   * @return a Seat Seller Identity with API schema
   */
  @VisibleForTesting
  SeatSellerIdentity convertToApiSellerIdentity(@Nonnull ContractUrn contractUrn, @Nonnull SeatUrn seatUrn,
      com.linkedin.sales.espresso.SeatSellerIdentity espressoSellerIdentity, Boolean shouldSetSeatManagedProducts) {
    SeatSellerIdentity sellerIdentity = new SeatSellerIdentity();
    sellerIdentity.setContract(contractUrn);
    sellerIdentity.setSeat(seatUrn);
    sellerIdentity.setTargetType(SellerIdentityTargetType.valueOf(espressoSellerIdentity.getTargetType().toString()));
    if (espressoSellerIdentity.getCurrentPosition() != null) {
      sellerIdentity.setCurrentPosition(convertToApiCurrentPosition(espressoSellerIdentity.getCurrentPosition()), SetMode.IGNORE_NULL);
    }
    if (espressoSellerIdentity.getDefaultProductId() != null) {
      sellerIdentity.setDefaultProductId(espressoSellerIdentity.getDefaultProductId().toString());
    }
    sellerIdentity.setProducts(
        shouldSetSeatManagedProducts ? convertToApiProductArray(espressoSellerIdentity.getProducts(), seatUrn)
            : new SellerIdentityProductArray());
    sellerIdentity.setServices(convertToApiServiceArray(espressoSellerIdentity.getServices()));

    if (espressoSellerIdentity.getAutoProspectingSettings() != null) {
      sellerIdentity.setAutoProspectingSettings(convertToApiAutoProspectingSettingsArray(espressoSellerIdentity.getAutoProspectingSettings()));
    }
    return sellerIdentity;
  }

  /**
   * Helper Function to convert Espresso Seller Identity Position the API schema Current Position
   */
  private SeatSellerIdentity.CurrentPosition convertToApiCurrentPosition(@Nonnull SellerIdentityPosition espressoPosition) {
    SeatSellerIdentity.CurrentPosition currentPosition = new SeatSellerIdentity.CurrentPosition();
    if (espressoPosition.getPositionId() != null) {
      currentPosition.setPositionId(espressoPosition.getPositionId());
    } else {
      com.linkedin.buyerengagement.SellerIdentityPosition position = new com.linkedin.buyerengagement.SellerIdentityPosition();
      if (espressoPosition.getCompanyName() != null) {
        position.setCompanyName(espressoPosition.getCompanyName().toString());
      }
      if (espressoPosition.getTitle() != null) {
        position.setTitle(espressoPosition.getTitle().toString());
      }
      if (espressoPosition.getOrganizationUrn() != null) {
        try {
          OrganizationUrn orgUrn = OrganizationUrn.deserialize(espressoPosition.getOrganizationUrn().toString());
          position.setCompanyUrn(orgUrn);
        } catch (URISyntaxException e) {
          String errMsg = String.format("Unable to deserialize OrganizationUrn from String: %s", espressoPosition.getOrganizationUrn().toString());
          LOG.error(errMsg, e);
          throw new RuntimeException(errMsg);
        }
      }
      currentPosition.setPosition(position);
    }
    return currentPosition;
  }

  /**
   * Helper Function to convert Espresso schema Products to API schema SellerIdentityProductArray
   */
  private SellerIdentityProductArray convertToApiProductArray(@Nonnull List<SellerIdentityProduct> espressoProductList, SeatUrn seatUrn) {
    if (espressoProductList.isEmpty()) {
      return new SellerIdentityProductArray();
    }
    List<com.linkedin.buyerengagement.SellerIdentityProduct> productList = espressoProductList.stream()
        .map(espressoProduct -> {
          com.linkedin.buyerengagement.SellerIdentityProduct sellerProduct = new com.linkedin.buyerengagement.SellerIdentityProduct();
          if (espressoProduct.getProductId() != null) {
            sellerProduct.setId(espressoProduct.getProductId().toString());
          }
          sellerProduct.setCreatedTime(espressoProduct.getCreatedTime(), SetMode.IGNORE_NULL).setCreatedBy(seatUrn);
          sellerProduct.setLastModifiedTime(espressoProduct.getLastModifiedTime(), SetMode.IGNORE_NULL).setLastModifiedBy(seatUrn);

          com.linkedin.buyerengagement.SellerIdentityProduct.Product product = new com.linkedin.buyerengagement.SellerIdentityProduct.Product();
          sellerProduct.setProduct(product);
          if (espressoProduct.getProductName() != null) {
            product.setProductName(espressoProduct.getProductName().toString());
          } else {
            try {
              StandardizedProductUrn productUrn = StandardizedProductUrn.deserialize(espressoProduct.getStandardizedProductUrn().toString());
              product.setStandardizedProduct(productUrn);
            } catch (URISyntaxException e) {
              String errMsg = String.format("Unable to deserialize Product Urn from String: %s", espressoProduct.getStandardizedProductUrn().toString());
              LOG.error(errMsg, e);
              return null;
            }
          }

          if (espressoProduct.getProductUrl() != null) {
            sellerProduct.setProductUrl(new Url(espressoProduct.getProductUrl().toString()));
          }

          com.linkedin.buyerengagement.SellerIdentityProduct.ProductCategory productCategory =
              new com.linkedin.buyerengagement.SellerIdentityProduct.ProductCategory();

          if (espressoProduct.getProductCategoryName() != null) {
            productCategory.setProductCategoryName(espressoProduct.getProductCategoryName().toString());
            sellerProduct.setProductCategory(productCategory);
          } else if (espressoProduct.getStandardizedProductCategoryUrn() != null) {
            try {
              StandardizedProductCategoryUrn standardizedProductCategoryUrn =
                  StandardizedProductCategoryUrn.deserialize(espressoProduct.getStandardizedProductCategoryUrn().toString());
              productCategory.setStandardizedProductCategory(standardizedProductCategoryUrn);
              sellerProduct.setProductCategory(productCategory);
            } catch (URISyntaxException e) {
              String errMsg = String.format("Unable to deserialize Product Category Urn from String: %s",
                  espressoProduct.getStandardizedProductCategoryUrn().toString());
              LOG.error(errMsg, e);
            }
          }

          if (espressoProduct.getProductDescription() != null) {
            sellerProduct.setDescription(espressoProduct.getProductDescription().toString());
          }

          if (espressoProduct.getCustomerFunctionUrns() != null) {
            sellerProduct.setCustomerFunctions(UrnUtils.formatFunctionUrns(espressoProduct.getCustomerFunctionUrns()));
          }

          return sellerProduct;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    return new SellerIdentityProductArray(productList);
  }

  /**
   * Helper Function to convert Espresso schema Services to API schema SellerIdentityServiceArray
   */
  private SellerIdentityServiceArray convertToApiServiceArray(@Nonnull List<SellerIdentityService> espressoServiceList) {
    if (espressoServiceList.isEmpty()) {
      return new SellerIdentityServiceArray();
    }
    List<com.linkedin.buyerengagement.SellerIdentityService> serviceList = espressoServiceList.stream()
        .map(espressoService -> {
          com.linkedin.buyerengagement.SellerIdentityService sellerService = new com.linkedin.buyerengagement.SellerIdentityService();
          sellerService.setServiceName(espressoService.getServiceName().toString());
          if (espressoService.getServiceUrl() != null) {
            sellerService.setServiceUrl(new Url(espressoService.getServiceUrl().toString()));
          }
          return sellerService;
        }).collect(Collectors.toList());
    return new SellerIdentityServiceArray(serviceList);
  }

  /**
   * Convert espresso schema AutoProspectingSetting list to API schema AutoProspectingSettingArray
   * @param espressoAutoProspectingSettingsList
   * @return
   */
  private AutoProspectingSettingArray convertToApiAutoProspectingSettingsArray(@Nonnull List<AutoProspectingSetting> espressoAutoProspectingSettingsList) {
    if (espressoAutoProspectingSettingsList.isEmpty()) {
      return new AutoProspectingSettingArray();
    }
    List<pegasus.com.linkedin.buyerengagement.AutoProspectingSetting> autoProspectingSettingsList = espressoAutoProspectingSettingsList.stream()
        .map(espressoAutoProspectingSetting -> {
          pegasus.com.linkedin.buyerengagement.AutoProspectingSetting apiAutoProspectingSetting =
              new pegasus.com.linkedin.buyerengagement.AutoProspectingSetting();

          if (espressoAutoProspectingSetting.getProductId() != null) {
            apiAutoProspectingSetting.setProductId(espressoAutoProspectingSetting.getProductId().toString());
          }
          if (espressoAutoProspectingSetting.getPersonaId() != null) {
            apiAutoProspectingSetting.setPersonaId(espressoAutoProspectingSetting.getPersonaId());
          }
          if (espressoAutoProspectingSetting.getAccountUrns() != null) {
            apiAutoProspectingSetting.setAccountUrns(new OrganizationUrnArray(
                espressoAutoProspectingSetting.getAccountUrns().stream()
                    .map(orgUrnString -> {
                      try {
                        return OrganizationUrn.deserialize(orgUrnString.toString());
                      } catch (URISyntaxException e) {
                        throw new RuntimeException(e);
                      }
                    })
                    .collect(Collectors.toList())
            ));
          }
          if (espressoAutoProspectingSetting.getAccountListUrns() != null) {
            apiAutoProspectingSetting.setAccountListUrns(new SalesListUrnArray(
                espressoAutoProspectingSetting.getAccountListUrns().stream()
                    .map(orgUrnString -> {
                      try {
                        return SalesListUrn.deserialize(orgUrnString.toString());
                      } catch (URISyntaxException e) {
                        throw new RuntimeException(e);
                      }
                    })
                    .collect(Collectors.toList())
            ));
          }
          apiAutoProspectingSetting.setLastModifiedTime(espressoAutoProspectingSetting.getLastModifiedTime());
          Optional.ofNullable(espressoAutoProspectingSetting.getOnboardingCompletedTime())
              .ifPresent(apiAutoProspectingSetting::setOnboardingCompletedTime);
          return apiAutoProspectingSetting;
        }).collect(Collectors.toList());
    return new AutoProspectingSettingArray(autoProspectingSettingsList);
  }

  /**
   * Convert API schema Seller Identity to Espresso schema Seller Identity.
   * @param sellerIdentity API schema seller identity
   * @return a Espresso schema seller identity
   */
  private com.linkedin.sales.espresso.SeatSellerIdentity convertToEspressoSellerIdentity(@Nonnull SeatSellerIdentity sellerIdentity) {
    com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentity = new com.linkedin.sales.espresso.SeatSellerIdentity();
    espressoIdentity.setTargetType(TargetType.valueOf(sellerIdentity.getTargetType().toString()));
    if (sellerIdentity.getCurrentPosition() != null) {
      espressoIdentity.setCurrentPosition(convertToEspressoSellerIdentityPosition(sellerIdentity.getCurrentPosition()));
    }
    if (sellerIdentity.getDefaultProductId() != null) {
      espressoIdentity.setDefaultProductId(sellerIdentity.getDefaultProductId());
    }
    espressoIdentity.setProducts(convertToEspressoProductsList(sellerIdentity.getProducts()));
    espressoIdentity.setServices(convertToEspressoServicesList(sellerIdentity.getServices()));
    if (sellerIdentity.hasAutoProspectingSettings()) {
      espressoIdentity.setAutoProspectingSettings(convertToEspressoAutoProspectingSettingsList(sellerIdentity.getAutoProspectingSettings()));
    }
    return espressoIdentity;
  }

  /**
   * Helper function to convert API schema CurrentPosition to Espresso Seller Identity Position
   */
  private SellerIdentityPosition convertToEspressoSellerIdentityPosition(@Nonnull SeatSellerIdentity.CurrentPosition currentPosition) {
    SellerIdentityPosition espressoPosition = new SellerIdentityPosition();
    if (currentPosition.getPositionId() != null) {
      espressoPosition.setPositionId(currentPosition.getPositionId());
    } else {
      com.linkedin.buyerengagement.SellerIdentityPosition position = currentPosition.getPosition();
      if (position != null) {
        if (position.getCompanyUrn() != null) {
          espressoPosition.setOrganizationUrn(position.getCompanyUrn().toString());
        }
        if (position.getTitle() != null) {
          espressoPosition.setTitle(position.getTitle());
        }
        if (position.getCompanyName() != null) {
          espressoPosition.setCompanyName(position.getCompanyName());
        }
      }
    }
    return espressoPosition;
  }

  /**
   * Helper Function to convert API SellerIdentityProductArray to Espresso Products List
   */
  private List<SellerIdentityProduct> convertToEspressoProductsList(@Nonnull SellerIdentityProductArray productList) {
    return productList.stream().map(product -> {
      SellerIdentityProduct espressoProduct = new SellerIdentityProduct();
      if (product.getId() != null) {
        espressoProduct.setProductId(product.getId());
      }
      if (product.hasCreatedTime()) {
        espressoProduct.setCreatedTime(product.getCreatedTime());
      }
      if (product.hasLastModifiedTime()) {
        espressoProduct.setLastModifiedTime(product.getLastModifiedTime());
      }
      if (product.getProduct().isProductName()) {
        espressoProduct.setProductName(product.getProduct().getProductName());
      } else {
        espressoProduct.setStandardizedProductUrn(product.getProduct().getStandardizedProduct().toString());
      }
      if (product.getProductUrl() != null) {
        espressoProduct.setProductUrl(product.getProductUrl().toString());
      }
      if (product.getProductCategory() != null) {
        if (product.getProductCategory().isProductCategoryName()) {
          espressoProduct.setProductCategoryName(product.getProductCategory().getProductCategoryName());
        } else {
          espressoProduct.setStandardizedProductCategoryUrn(product.getProductCategory().getStandardizedProductCategory().toString());
        }
      }
      if (product.getDescription() != null) {
        espressoProduct.setProductDescription(product.getDescription());
      }
      if (!product.getCustomerFunctions().isEmpty()) {
        espressoProduct.setCustomerFunctionUrns(product.getCustomerFunctions().stream().map(FunctionUrn::toString).collect(Collectors.toList()));
      }
      return espressoProduct;
    }).collect(Collectors.toList());
  }

  /**
   * Helper Function to convert API SellerIdentityServiceArray to Espresso Services List
   */
  private List<SellerIdentityService> convertToEspressoServicesList(@Nonnull SellerIdentityServiceArray serviceList) {
    return serviceList.stream().map(service -> {
      SellerIdentityService espressoService = new SellerIdentityService();
      espressoService.setServiceName(service.getServiceName());
      if (service.getServiceUrl() != null) {
        espressoService.setServiceUrl(service.getServiceUrl().toString());
      }
      return espressoService;
    }).collect(Collectors.toList());
  }

  /**
   * Helper Function to convert API schema AutoProspectingSettingArray to Espresso schema AutoProspectingSetting List
   * @param autoProspectingSettingArray
   * @return
   */
  private List<AutoProspectingSetting> convertToEspressoAutoProspectingSettingsList(@Nonnull
      AutoProspectingSettingArray autoProspectingSettingArray) {
    return autoProspectingSettingArray.stream().map(autoProspectingSetting -> {
      AutoProspectingSetting espressoAutoProspectingSetting = new AutoProspectingSetting();
      espressoAutoProspectingSetting.setProductId(autoProspectingSetting.getProductId(GetMode.NULL));
      espressoAutoProspectingSetting.setPersonaId(autoProspectingSetting.getPersonaId(GetMode.NULL));
      espressoAutoProspectingSetting.setLastModifiedTime(autoProspectingSetting.getLastModifiedTime());
      Optional.ofNullable(autoProspectingSetting.getOnboardingCompletedTime(GetMode.NULL))
          .ifPresent(espressoAutoProspectingSetting::setOnboardingCompletedTime);

      if (autoProspectingSetting.hasAccountUrns()) {
        espressoAutoProspectingSetting.setAccountUrns(
            autoProspectingSetting.getAccountUrns().stream().map(urn -> urn.toString()).collect(Collectors.toList()));
      }

      if (autoProspectingSetting.hasAccountListUrns()) {
        espressoAutoProspectingSetting.setAccountListUrns(autoProspectingSetting.getAccountListUrns()
            .stream()
            .map(urn -> urn.toString())
            .collect(Collectors.toList()));
      }

      return espressoAutoProspectingSetting;
    }).collect(Collectors.toList());
  }
  /**
   * Helper function to check if seat managed products is enabled for a contract
   * @param contractUrn - contract urn
   * @return - Task of boolean indicating if seat managed products is enabled
   */
  Task<Boolean> isSeatManagedProductsEnabled(@Nonnull ContractUrn contractUrn) {
    return _lixService.isContractBasedLixEnabled(contractUrn, LixUtils.LSS_SEAT_MANAGED_PRODUCTS_ENABLED).flatMap(isSeatManagedProductsLixEnabled -> {
      if (isSeatManagedProductsLixEnabled) {
        return _salesContractSettingsClient.get(contractUrn.getContractIdEntity(), ContractSettingType.SEAT_MANAGE_PRODUCTS_ENABLED)
            .map(isSeatManagedProductsSettingEnabled -> isSeatManagedProductsSettingEnabled.getValue().getBoolean());
      } else {
        // Seat managed products were available earlier as well, with admin product collection it will be controlled by the setting
        // So we return true in this case
        return Task.value(Boolean.TRUE);
      }
    }).recover(e -> {
      LOG.error("Failed to check if seat managed products is enabled for contract {}", contractUrn, e);
      return Boolean.FALSE;
    });
  }
}
