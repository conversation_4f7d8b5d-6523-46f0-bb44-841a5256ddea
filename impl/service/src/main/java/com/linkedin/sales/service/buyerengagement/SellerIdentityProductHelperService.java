package com.linkedin.sales.service.buyerengagement;

import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.buyerengagement.SellerIdentityTargetType;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.TrackingService;
import com.linkedin.sales.service.utils.LixUtils;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@SuppressFBWarnings("SSCU_SUSPICIOUS_SHADED_CLASS_USE")
public class SellerIdentityProductHelperService {
  private static final Logger LOG = LoggerFactory.getLogger(SellerIdentityProductHelperService.class);

  private final SalesSellerIdentityService _salesSellerIdentityService;
  private final TrackingService _trackingService;
  private final LixService _lixService;
  private final ContractSellerIdentityService _contractSellerIdentityService;

  public SellerIdentityProductHelperService(SalesSellerIdentityService salesSellerIdentityService,
      TrackingService trackingService,
      LixService lixService,
      ContractSellerIdentityService contractSellerIdentityService) {
    _salesSellerIdentityService = salesSellerIdentityService;
    _trackingService = trackingService;
    _lixService = lixService;
    _contractSellerIdentityService = contractSellerIdentityService;
  }

  /**
   * Update the default product for a seller identity
   * @param contractUrn the contract urn of the logged-in seat.
   * @param seatUrn the seat urn of the logged-in seat.
   * @param productId the product id to set as default
   * @return ActionResult
   */
  public Task<ActionResult<Void>> updateDefaultProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull String productId) {
    Task<SeatSellerIdentity> getOrCreateSeatSellerIdentity =
        _salesSellerIdentityService.getSellerIdentity(contractUrn, seatUrn).map(seatSellerIdentity -> {
          if (seatSellerIdentity == null) {
            return new SeatSellerIdentity().setSeat(seatUrn)
                .setContract(contractUrn)
                .setTargetType(SellerIdentityTargetType.ACCOUNT)
                .setProducts(new SellerIdentityProductArray());
          }
          return seatSellerIdentity;
        });
    return Task.par(getOrCreateSeatSellerIdentity, getAdminProducts(contractUrn))
        .flatMap((sellerIdentity, adminProducts) -> {
          List<com.linkedin.buyerengagement.SellerIdentityProduct> combinedProducts = new ArrayList<>(adminProducts);
          combinedProducts.addAll(sellerIdentity.getProducts());
          for (SellerIdentityProduct product : combinedProducts) {
            if (productId.equals(product.getId())) {
              sellerIdentity.setDefaultProductId(productId);
              return _salesSellerIdentityService.updateSellerIdentity(contractUrn, seatUrn, sellerIdentity)
                  .map(updateResponse -> new ActionResult(updateResponse.getStatus()));
            }
          }
          // If the product id is not found in the seller identity, return error
          LOG.error("Failed to update default product because no product with id {} seller identity record.", productId);
          return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
    });
  }

  private Task<List<SellerIdentityProduct>> getAdminProducts(ContractUrn contractUrn) {
    return _lixService.isContractBasedEEPLixEnabled(contractUrn, LixUtils.LSS_ADMIN_PRODUCT_COLLECTION)
        .flatMap(isAdminProductsEnabled -> {
          if (isAdminProductsEnabled) {
            return _contractSellerIdentityService.getContractSellerIdentity(contractUrn.getIdAsLong())
                .map(contractSellerIdentity -> {
                  if (contractSellerIdentity == null) {
                    return Collections.emptyList();
                  }
                  return new ArrayList<>(contractSellerIdentity.getSellerIdentityProducts());
                });
          }
          return Task.value(Collections.emptyList());
        });
  }

  /**
   * Add a product to the seller identity
   * @param contractUrn the contract urn of the logged-in seat.
   * @param seatUrn the seat urn of the logged-in seat.
   * @param sellerIdentityProduct the product to add
   * @return ActionResult with the product id (Generated UUID)
   */
  public Task<ActionResult<String>> addProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull SellerIdentityProduct sellerIdentityProduct, @Nullable String seesionId) {
    return _salesSellerIdentityService.isSeatManagedProductsEnabled(contractUrn).flatMap(isEnabled -> {
      if (!isEnabled) {
        LOG.error("Failed to add product as seat managed products is not enabled for contract {} for seat{} ", contractUrn, seatUrn);
        return Task.value(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
      }

      Task<SeatSellerIdentity> getOrCreateSeatSellerIdentity =
          _salesSellerIdentityService.getSellerIdentity(contractUrn, seatUrn).flatMap(seatSellerIdentity -> {
            if (seatSellerIdentity == null) {
              // If the seller identity is not found, create a new one. It happens when a user tries to a product
              // before entering the seller identity onboarding flow. Onboarding flow will read SeatSellerIdentity
              // to resume the onboarding process but UX will be the same since the fields needed are not set or set to default.
              SeatSellerIdentity emptySellerIdentity = new SeatSellerIdentity().setSeat(seatUrn)
                  .setContract(contractUrn)
                  .setTargetType(SellerIdentityTargetType.ACCOUNT);
              //the default empty array is read-only
              emptySellerIdentity.setProducts(new SellerIdentityProductArray());
              return Task.value(emptySellerIdentity);
            }
            return Task.value(seatSellerIdentity);
          });
      return getOrCreateSeatSellerIdentity.flatMap(sellerIdentity -> {
        long currentTime = System.currentTimeMillis();
        sellerIdentityProduct.setId(UUID.randomUUID().toString());
        sellerIdentityProduct.setCreatedBy(seatUrn);
        sellerIdentityProduct.setCreatedTime(currentTime);
        sellerIdentityProduct.setLastModifiedBy(seatUrn);
        sellerIdentityProduct.setLastModifiedTime(currentTime);
        sellerIdentity.getProducts().add(sellerIdentityProduct);
        return _salesSellerIdentityService.updateSellerIdentity(contractUrn, seatUrn, sellerIdentity)
            .map(updateResponse -> {
              _trackingService.createAndSendOutProductCollectFunnelSaveProductTrackingEvent(seatUrn, seesionId,
                  sellerIdentityProduct, updateResponse.getStatus() == HttpStatus.S_500_INTERNAL_SERVER_ERROR);
              return new ActionResult<>(sellerIdentityProduct.getId(), updateResponse.getStatus());
            });
      });
    });
  }

  /**
   * Update a product in the seller identity
   * @param contractUrn the contract urn of the logged-in seat.
   * @param seatUrn the seat urn of the logged-in seat.
   * @param sellerIdentityProduct the product to update
   * @return ActionResult
   */
  public Task<ActionResult<Void>> updateProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull SellerIdentityProduct sellerIdentityProduct) {
    return _salesSellerIdentityService.isSeatManagedProductsEnabled(contractUrn).flatMap(isEnabled -> {
      if (!isEnabled) {
        LOG.error("Failed to update product as seat managed products is not enabled for contract {} for seat{} ", contractUrn, seatUrn);
        return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
      }

      if (sellerIdentityProduct.getId() == null) {
        LOG.error("Failed to update because no product id in sellerIdentityProduct");
        return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
      }
      return _salesSellerIdentityService.getSellerIdentity(contractUrn, seatUrn).flatMap(sellerIdentity -> {
        if (sellerIdentity == null) {
          LOG.error("Failed to update product because no seller identity with {}", seatUrn);
          return Task.value(new ActionResult(HttpStatus.S_404_NOT_FOUND));
        }
        String productId = sellerIdentityProduct.getId();
        for (int i = 0; i < sellerIdentity.getProducts().size(); i++) {
          if (productId.equals(sellerIdentity.getProducts().get(i).getId())) {
            SellerIdentityProduct existingProduct = sellerIdentity.getProducts().get(i);
            if (existingProduct.hasCreatedTime()) {
              sellerIdentityProduct.setCreatedTime(existingProduct.getCreatedTime());
            }
            sellerIdentityProduct.setCreatedBy(existingProduct.getCreatedBy());
            sellerIdentityProduct.setLastModifiedTime(System.currentTimeMillis());
            sellerIdentityProduct.setLastModifiedBy(seatUrn);
            sellerIdentity.getProducts().set(i, sellerIdentityProduct);
            return _salesSellerIdentityService.updateSellerIdentity(contractUrn, seatUrn, sellerIdentity)
                .map(updateResponse -> new ActionResult(updateResponse.getStatus()));
          }
        }
        // If the product id is not found in the seller identity, return 412
        LOG.error("Failed to update because no product with id {} in seller identity record.", productId);
        return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
      });
    });
  }

  /**
   * Remove a product from the seller identity.
   * @param contractUrn the contract urn of the logged-in seat.
   * @param seatUrn the seat urn of the logged-in seat.
   * @param productId the product id to remove
   * @return ActionResult
   */
  public Task<ActionResult<Void>> removeProduct(@NonNull ContractUrn contractUrn, @NonNull SeatUrn seatUrn,
      @NonNull String productId) {
    return _salesSellerIdentityService.isSeatManagedProductsEnabled(contractUrn).flatMap(isEnabled -> {
      if (!isEnabled) {
        LOG.error("Failed to remove product because seat managed products is not enabled for contract {}", contractUrn);
        return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
      }
      return _salesSellerIdentityService.getSellerIdentity(contractUrn, seatUrn).flatMap(sellerIdentity -> {
        if (sellerIdentity == null) {
          LOG.error("Failed to remove product because no seller identity with {}", seatUrn);
          return Task.value(new ActionResult(HttpStatus.S_404_NOT_FOUND));
        }
        for (int i = 0; i < sellerIdentity.getProducts().size(); i++) {
          if (productId.equals(sellerIdentity.getProducts().get(i).getId())) {
            sellerIdentity.getProducts().remove(i);
            return _salesSellerIdentityService.updateSellerIdentity(contractUrn, seatUrn, sellerIdentity)
                .map(updateResponse -> new ActionResult(updateResponse.getStatus()));
          }
        }
        // If the product id is not found in the seller identity, return 412
        return Task.value(new ActionResult(HttpStatus.S_412_PRECONDITION_FAILED));
      });
    });
  }

}
