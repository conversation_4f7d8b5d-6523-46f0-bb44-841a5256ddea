package com.linkedin.sales.service.coach;

import com.linkedin.avroutil1.compatibility.shaded.org.apache.commons.lang3.ArrayUtils;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssCoachDB;
import com.linkedin.sales.espresso.AuthorType;
import com.linkedin.sales.espresso.ChatMessage;
import com.linkedin.sales.espresso.CoachConversationHistory;
import com.linkedin.sales.espresso.Metadata;
import com.linkedin.sales.espresso.UseCaseType;
import com.linkedin.salescoach.ChatHistoryMetadata;
import com.linkedin.salescoach.ChatMessageArray;
import com.linkedin.salescoach.ChatMessageAuthorType;
import com.linkedin.salescoach.SalesAIChatHistory;
import com.linkedin.salescoach.SalesAIChatHistoryKey;
import com.linkedin.salescoach.SalesAIChatUseCase;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * Service class for SalesCoachConversationResource with /salesCoachConversation
 */
public class SalesCoachConversationService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesCoachConversationService.class);

  private final LssCoachDB _lssCoachDB;

  public SalesCoachConversationService(LssCoachDB lssCoachDB) {
    _lssCoachDB = lssCoachDB;
  }

  /**
   * Fetch the conversation history given a {@link SalesAIChatHistoryKey} which holds the seatUrn and sessionId of the conversation.
   *
   * @param seatUrn The {@link SeatUrn} representing memberId's Sales Navigator seat.
   * @param sessionId The sessionId of the conversation.
   * @return The conversation history {@link SalesAIChatHistory} given a {@link SalesAIChatHistoryKey}.
   */
  public Task<SalesAIChatHistory> get(@NonNull SeatUrn seatUrn, @NonNull String sessionId) {
    return _lssCoachDB.getCoachConversationHistory(seatUrn, sessionId)
        .map(coachConversationHistoryOptional -> {
          if (!coachConversationHistoryOptional.isPresent()) {
            String error = String.format("No chat history found for seat urn: %s and session id: %s, returning 404.",
                seatUrn, sessionId);
            LOG.error(error);
            throw new RestLiServiceException(HttpStatus.S_404_NOT_FOUND);
          }

          return coachConversationHistoryOptional
              .map(this::constructSalesAIChatHistory)
              .orElse(null);
        });
  }

  /**
   * Add a new chat messages to the conversation history. Messages content are immutable and cannot be updated.
   * We add new messages to the end of the chat history.
   * We expect either chatMessages or chatHistoryMetadata (or both) to be present. Otherwise, no action will be taken.
   *
   * @param seatUrn The {@link SeatUrn} representing memberId's Sales Navigator seat.
   * @param sessionId The unique sessionId that represents the given conversation history.
   * @param chatMessages List of {@link com.linkedin.salescoach.ChatMessage} to be added to the conversation history.
   * @param chatHistoryMetadata The {@link ChatHistoryMetadata} to be added to the conversation history.
   * @return True if the chat messages are successfully added to the conversation history.
   */
  public Task<Boolean> addSalesAIChatMessages(
      @NonNull SeatUrn seatUrn, @NonNull String sessionId,
      com.linkedin.salescoach.ChatMessage[] chatMessages, ChatHistoryMetadata chatHistoryMetadata) {

    if ((Objects.isNull(chatMessages) || chatMessages.length == 0) && chatHistoryMetadata == null) {
      LOG.info("No chat messages or chat history metadata provided, no action taken.");
      return Task.value(FALSE);
    }

    return _lssCoachDB.getCoachConversationHistory(seatUrn, sessionId)
        .map(coachConversationHistoryOptional ->
            coachConversationHistoryOptional
            .map(coachConversationHistory ->
                convertPegasusToEspressoCoachConversationHistoryForUpdation(chatMessages, chatHistoryMetadata, coachConversationHistory))
            .orElseGet(() -> convertPegasusToEspressoCoachConversationHistoryForCreation(chatMessages, chatHistoryMetadata)))
        .flatMap(espressoCoachConversationHistory -> _lssCoachDB.upsertCoachConversationHistory(seatUrn, sessionId, espressoCoachConversationHistory));
  }

  /**
   * Fetch the last K messages written as part of the conversation history wrapped in a singleton list of {@link SalesAIChatHistory}.
   * K represents the number of messages authored by both MEMBER and SYSTEM.
   *
   * @param seatUrn The {@link SeatUrn} representing memberId's Sales Navigator seat.
   * @param sessionId The unique sessionId that represents the given conversation history.
   * @param k The number of messages to be fetched.
   * @return The list of last K messages stored per given sessionId and seatUrn.
   */
  public Task<SalesAIChatHistory> getLastKMessages(
      @NonNull SeatUrn seatUrn, @NonNull String sessionId, int k) {

    return this.get(seatUrn, sessionId)
        .map(salesAIChatHistory -> {

          ChatMessageArray chatMessages = salesAIChatHistory
              .getChatContent()
              .stream()
              .sorted(Comparator.comparing(com.linkedin.salescoach.ChatMessage::getCreatedTime))
              .collect(Collectors.toCollection(ChatMessageArray::new));
          List<com.linkedin.salescoach.ChatMessage> lastKMessages = chatMessages.subList(chatMessages.size() - k, chatMessages.size());
          salesAIChatHistory.setChatContent(new ChatMessageArray(lastKMessages));
          return salesAIChatHistory;
        });
  }

  private CoachConversationHistory convertPegasusToEspressoCoachConversationHistoryForUpdation(
      com.linkedin.salescoach.ChatMessage[] chatMessages,
      ChatHistoryMetadata chatHistoryMetadata,
      CoachConversationHistory coachConversationHistory) {


    CoachConversationHistory newCoachConversationHistory = new CoachConversationHistory();

    if (coachConversationHistory.getChatContent() == null) {
      newCoachConversationHistory.setChatContent(new ArrayList<>());
    } else {
      newCoachConversationHistory.setChatContent(coachConversationHistory.getChatContent());
    }

    newCoachConversationHistory.setMetadata(coachConversationHistory.getMetadata());
    newCoachConversationHistory.setUpdatedTime(coachConversationHistory.getUpdatedTime());

    return convertPegasusToEspressoCoachConversationHistory(chatMessages, chatHistoryMetadata, newCoachConversationHistory);
  }

  private CoachConversationHistory convertPegasusToEspressoCoachConversationHistoryForCreation(
      com.linkedin.salescoach.ChatMessage[] chatMessages,
      ChatHistoryMetadata chatHistoryMetadata) {

    return convertPegasusToEspressoCoachConversationHistory(chatMessages, chatHistoryMetadata, new CoachConversationHistory());
  }

  private CoachConversationHistory convertPegasusToEspressoCoachConversationHistory(
      com.linkedin.salescoach.ChatMessage[] chatMessages,
      ChatHistoryMetadata chatHistoryMetadata,
      CoachConversationHistory coachConversationHistory) {

    //Make a copy of the original chat history
    CoachConversationHistory newCoachConversationHistory = new CoachConversationHistory();
    newCoachConversationHistory.setChatContent(coachConversationHistory.getChatContent());
    newCoachConversationHistory.setMetadata(coachConversationHistory.getMetadata());
    newCoachConversationHistory.setUpdatedTime(coachConversationHistory.getUpdatedTime());

    // Update coach chat history
    if (!ArrayUtils.isEmpty(chatMessages)) {
      List<ChatMessage> newChatMessages = constructEspressoChatContent(chatMessages);
      List<ChatMessage> existingChatContent = CollectionUtils.isEmpty(coachConversationHistory.getChatContent())
          ? new ArrayList<>() : coachConversationHistory.getChatContent();
      newCoachConversationHistory.setChatContent(mergeChatContent(existingChatContent, newChatMessages));
    }

    if (!Objects.isNull(chatHistoryMetadata)) {
      Metadata espressoMetadata = Objects.isNull(coachConversationHistory.metadata) ? new Metadata() : coachConversationHistory.metadata;
      newCoachConversationHistory.setMetadata(constructEspressoMetadata(chatHistoryMetadata, espressoMetadata));
    }

    newCoachConversationHistory.setUpdatedTime(System.currentTimeMillis());

    return newCoachConversationHistory;
  }

  private List<ChatMessage> mergeChatContent(List<ChatMessage> existingChatContent, List<ChatMessage> newChatMessages) {
    return Stream.of(existingChatContent, newChatMessages)
        .flatMap(Collection::stream)
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Metadata constructEspressoMetadata(
      ChatHistoryMetadata pegasusChatHistoryMetadata,
      Metadata espressoMetadata) {

    //Make a copy of the existing metadata
    Metadata newEspressoMetadata = new Metadata();
    newEspressoMetadata.setSummarizedContent(espressoMetadata.getSummarizedContent());
    newEspressoMetadata.setSummarizedIntent(espressoMetadata.getSummarizedIntent());
    newEspressoMetadata.setUseCase(espressoMetadata.getUseCase());


    //Update the metadata with the new values
    if (!Objects.isNull(pegasusChatHistoryMetadata.getSummarizedContent())) {
      newEspressoMetadata.setSummarizedContent(pegasusChatHistoryMetadata.getSummarizedContent());
    }

    if (!Objects.isNull(pegasusChatHistoryMetadata.getSummarizedIntent())) {
      newEspressoMetadata.setSummarizedIntent(pegasusChatHistoryMetadata.getSummarizedIntent());
    }

    newEspressoMetadata.setUseCase(UseCaseType.valueOf(pegasusChatHistoryMetadata.getUseCase().name()));

    return newEspressoMetadata;
  }

  private List<ChatMessage> constructEspressoChatContent(com.linkedin.salescoach.ChatMessage[] chatMessageList) {
    return Arrays.stream(chatMessageList)
        .map(pegasusMessage ->  {

            ChatMessage espressoMessage = new ChatMessage();
            espressoMessage.setMessage(pegasusMessage.getMessage());
            espressoMessage.setCreatedTime(pegasusMessage.getCreatedTime());
            espressoMessage.setAuthor(AuthorType.valueOf(pegasusMessage.getAuthor().name()));
            Optional.ofNullable(pegasusMessage.getChatFilterReason()).ifPresent(espressoMessage::setChatFilterReason);
            Optional.ofNullable(pegasusMessage.getFilteredMessage()).ifPresent(espressoMessage::setFilteredMessage);
            return espressoMessage;
        }).collect(Collectors.toList());
  }

  private SalesAIChatHistory constructSalesAIChatHistory(CoachConversationHistory coachConversationHistory) {
    SalesAIChatHistory salesAIChatHistory = new SalesAIChatHistory();
    salesAIChatHistory.setChatContent(constructPegasusChatContent(coachConversationHistory.getChatContent()));
    salesAIChatHistory.setMetaData(constructPegasusMetadata(coachConversationHistory.getMetadata()), SetMode.IGNORE_NULL);
    salesAIChatHistory.setUpdatedTime(coachConversationHistory.getUpdatedTime());
    return salesAIChatHistory;
  }

  private ChatMessageArray constructPegasusChatContent(List<ChatMessage> chatContent) {

    if (Objects.isNull(chatContent)) {
      return new ChatMessageArray();
    }

    return chatContent.stream()
        .map(espressoChatMessage -> {

          com.linkedin.salescoach.ChatMessage pegasusChatMessage = new com.linkedin.salescoach.ChatMessage();
          pegasusChatMessage.setMessage(espressoChatMessage.getMessage().toString());
          pegasusChatMessage.setCreatedTime(espressoChatMessage.getCreatedTime());
          pegasusChatMessage.setAuthor(ChatMessageAuthorType.valueOf(espressoChatMessage.getAuthor().toString()));
          Optional.ofNullable(espressoChatMessage.getChatFilterReason())
              .ifPresent(filterReason -> pegasusChatMessage.setChatFilterReason(filterReason.toString()));
          Optional.ofNullable(espressoChatMessage.getFilteredMessage())
              .ifPresent(filteredMessage -> pegasusChatMessage.setFilteredMessage(filteredMessage.toString()));
          return pegasusChatMessage;
        }).collect(Collectors.toCollection(ChatMessageArray::new));
  }

  private ChatHistoryMetadata constructPegasusMetadata(Metadata metadata) {

    if (Objects.isNull(metadata)) {
      return null;
    }

    ChatHistoryMetadata pegasusMetadata = new ChatHistoryMetadata();

    String summarizedContent = Objects.nonNull(metadata.getSummarizedContent()) ? metadata.getSummarizedContent().toString() : null;
    String summarizedIntent = Objects.nonNull(metadata.getSummarizedIntent()) ? metadata.getSummarizedIntent().toString() : null;

    pegasusMetadata.setSummarizedContent(summarizedContent, SetMode.IGNORE_NULL);
    pegasusMetadata.setSummarizedIntent(summarizedIntent, SetMode.IGNORE_NULL);
    pegasusMetadata.setUseCase(SalesAIChatUseCase.valueOf(metadata.getUseCase().toString()));

    return pegasusMetadata;
  }

}
