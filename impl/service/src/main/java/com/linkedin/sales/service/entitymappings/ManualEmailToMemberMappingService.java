package com.linkedin.sales.service.entitymappings;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Maps;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.annotations.RestMethod;
import com.linkedin.sales.ds.db.LssEntityMappingDB;
import com.linkedin.sales.espresso.ManualEmailToMemberMapping;
import com.linkedin.salesentitymappings.EmailMatchingKey;
import com.linkedin.salesentitymappings.MatchedMemberEntity;
import com.linkedin.security.crypto.SHA256Hasher;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service for manual email to member mapping
 */
public class ManualEmailToMemberMappingService {
  private static final Logger LOG = LoggerFactory.getLogger(ManualEmailToMemberMappingService.class);

  private final LssEntityMappingDB _lssEntityMappingDB;

  public ManualEmailToMemberMappingService(LssEntityMappingDB lssEntityMappingDB) {
    _lssEntityMappingDB = lssEntityMappingDB;
  }

  public Task<Map<ComplexResourceKey<EmailMatchingKey, EmptyRecord>, MatchedMemberEntity>> batchGet(
      final Set<ComplexResourceKey<EmailMatchingKey, EmptyRecord>> keys) {
    Map<String, String> hashToEmailMap = Maps.newHashMap();
    List<Pair<String, ContractUrn>> espressoKeys = keys.stream()
        .map(key -> {
          String email = key.getKey().getEmail();
          String hashedEmail = SHA256Hasher.hash(email);
          hashToEmailMap.put(hashedEmail, email);

          return Pair.of(hashedEmail, key.getKey().getContract());
        })
        .collect(Collectors.toList());

    return _lssEntityMappingDB.batchGetManualEmailMappings(espressoKeys)
        .map(manualEmailToMemberMappings -> manualEmailToMemberMappings.stream()
            .map(mapping -> {
              String hashedEmail = mapping.getFirst().getFirst();
              ContractUrn contractUrn = mapping.getFirst().getSecond();
              String originalEmail = hashToEmailMap.get(hashedEmail);
              EmailMatchingKey key = new EmailMatchingKey().setEmail(originalEmail).setContract(contractUrn);
              ComplexResourceKey<EmailMatchingKey, EmptyRecord> complexKey = new ComplexResourceKey<>(key, new EmptyRecord());

              MatchedMemberEntity matchedMemberEntity = createMatchedMemberEntity(mapping.getSecond());

              if (matchedMemberEntity == null) {
                LOG.warn("Failed to construct matched member entity from espresso record: {}", mapping.getSecond());
                return null;
              }

              return new Pair<>(complexKey, matchedMemberEntity);
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)))
        .onFailure(e -> LOG.warn("Failed to batch get manual mappings", e));
  }

  @RestMethod.Update
  public Task<UpdateResponse> upsert(@Nonnull final ComplexResourceKey<EmailMatchingKey, EmptyRecord> key,
      @Nonnull final MatchedMemberEntity matchedMemberEntity) {
    String email = key.getKey().getEmail();
    ContractUrn contractUrn = key.getKey().getContract();
    String hashedEmail = SHA256Hasher.hash(email);

    ManualEmailToMemberMapping manualEmailToMemberMapping = createManualEmailToMemberMapping(matchedMemberEntity);

    return _lssEntityMappingDB.upsertManualEmailToMemberMapping(hashedEmail, contractUrn, manualEmailToMemberMapping)
        .map(UpdateResponse::new)
        .onFailure(e -> LOG.warn("Failed to upsert the manual mapping", e));
  }

  @RestMethod.Delete
  public Task<UpdateResponse> delete(final ComplexResourceKey<EmailMatchingKey, EmptyRecord> key) {
    String email = key.getKey().getEmail();
    ContractUrn contractUrn = key.getKey().getContract();
    String hashedEmail = SHA256Hasher.hash(email);

    return _lssEntityMappingDB.deleteManualEmailMapping(hashedEmail, contractUrn).map(UpdateResponse::new);
  }

  @VisibleForTesting
  ManualEmailToMemberMapping createManualEmailToMemberMapping(MatchedMemberEntity matchedMemberEntity) {
    long currentTime = System.currentTimeMillis();
    ManualEmailToMemberMapping manualEmailToMemberMapping = new ManualEmailToMemberMapping();
    manualEmailToMemberMapping.memberUrn = matchedMemberEntity.getMember().toString();
    manualEmailToMemberMapping.seatUrn = matchedMemberEntity.getActorSeat().toString();
    manualEmailToMemberMapping.createdAt = currentTime;
    manualEmailToMemberMapping.lastModifiedAt = currentTime;

    if (matchedMemberEntity.getAssociatedOrganization() != null) {
      manualEmailToMemberMapping.associatedOrgUrn = matchedMemberEntity.getAssociatedOrganization().toString();
    }

    return manualEmailToMemberMapping;
  }

  @VisibleForTesting
  @Nullable
  MatchedMemberEntity createMatchedMemberEntity(ManualEmailToMemberMapping manualEmailToMemberMapping) {
    try {
      MemberUrn memberUrn = MemberUrn.deserialize(manualEmailToMemberMapping.memberUrn.toString());
      SeatUrn seatUrn = SeatUrn.deserialize(manualEmailToMemberMapping.seatUrn.toString());

      MatchedMemberEntity matchedMemberEntity = new MatchedMemberEntity();
      matchedMemberEntity.setMember(memberUrn);
      matchedMemberEntity.setActorSeat(seatUrn);
      matchedMemberEntity.setCreated(manualEmailToMemberMapping.createdAt);
      matchedMemberEntity.setLastModified(manualEmailToMemberMapping.lastModifiedAt);

      if (StringUtils.isNotBlank(manualEmailToMemberMapping.associatedOrgUrn)) {
        OrganizationUrn orgUrn = OrganizationUrn.deserialize(manualEmailToMemberMapping.associatedOrgUrn.toString());
        matchedMemberEntity.setAssociatedOrganization(orgUrn);
      }

      return matchedMemberEntity;
    } catch (URISyntaxException e) {
      LOG.error("fail to construct urns from espresso record: {}", manualEmailToMemberMapping, e);
      return null;
    }
  }
}
