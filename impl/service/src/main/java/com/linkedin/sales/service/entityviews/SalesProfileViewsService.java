package com.linkedin.sales.service.entityviews;

import com.google.common.base.Preconditions;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssEntityViewDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.ProfileView;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesentityview.SalesProfileView;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * service class for SalesProfileViews
 * <AUTHOR>
 */
public class SalesProfileViewsService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesProfileViewsService.class);
  private final LssEntityViewDB _lssEntityViewDB;

  public SalesProfileViewsService(LssEntityViewDB lssEntityViewDB) {
    _lssEntityViewDB = lssEntityViewDB;
  }

  /**
   * Upsert SalesProfileView
   * @param compoundKey CompoundKey
   * @param salesProfileView content that need to be updated or created
   * @return update response
   */
  public Task<UpdateResponse> upsertSalesProfileView(@NonNull CompoundKey compoundKey,
      @NonNull SalesProfileView salesProfileView) {
    Pair<SeatUrn, MemberUrn> keyPair = getEspressoKeyPartsFromCompoundKey(compoundKey);
    if (keyPair == null) {
      LOG.warn(
          "The viewed seat urn or the viewer member urn provided in the compoundKey {} is not valid to upsert salesProfileView {}",
          compoundKey, salesProfileView);
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatUrn viewerSeatUrn = keyPair.getFirst();
    MemberUrn viewedMemberUrn = keyPair.getSecond();

    Preconditions.checkArgument(viewerSeatUrn.equals(salesProfileView.getViewerSeat()) && viewedMemberUrn.equals(
        salesProfileView.getViewedMember()), String.format(
        "Not allowed to execute upsert the salesProfileView %s since contractUrn "
            + "and memberUrn within salesProfileView can't match to the compoundKey %s", salesProfileView,
        compoundKey));

    ProfileView espressoProfileView = convertToEspressoProfileView(salesProfileView);
    return _lssEntityViewDB.upsertProfileView(viewerSeatUrn, viewedMemberUrn, espressoProfileView)
        .map(UpdateResponse::new)
        .recover(t -> {
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
              "Failed to upsert salesProfileView by key: " + compoundKey, t);
        });
  }

  /**
   * Get sales profile view for given key
   * @param compoundKey compoundKey including seatUrn of the viewer and memberUrn of the viewee
   * @return SalesProfileView
   */
  public Task<SalesProfileView> getSalesProfileView(@NonNull CompoundKey compoundKey) {
    Pair<SeatUrn, MemberUrn> keyPair = getEspressoKeyPartsFromCompoundKey(compoundKey);
    if (keyPair == null) {
      LOG.warn(
          "The viewed seat urn or the viewer member urn provided in the compoundKey {} is not valid to get salesProfileView",
          compoundKey);
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatUrn viewerSeatUrn = keyPair.getFirst();
    MemberUrn viewedMemberUrn = keyPair.getSecond();

    return _lssEntityViewDB.getProfileView(viewerSeatUrn, viewedMemberUrn)
        .map(profileView -> convertToSalesProfileView(viewerSeatUrn, viewedMemberUrn, profileView))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
          } else {
            throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "Failed to upsert salesProfileView by key: " + compoundKey, t);
          }
        });
  }

  /**
   * Convert SalesProfileView object to Espresso ProfileView object
   */
  private ProfileView convertToEspressoProfileView(@NonNull SalesProfileView salesProfileView) {
    ProfileView espressoProfileView = new ProfileView();
    espressoProfileView.contractUrn = salesProfileView.getViewerContract().toString();
    espressoProfileView.lastViewedTime = salesProfileView.getLastViewedAt();
    return espressoProfileView;
  }

  /**
   * Convert Espresso ProfileView object to SalesProfileView object
   */
  private SalesProfileView convertToSalesProfileView(@NonNull SeatUrn seatUrn, @NonNull MemberUrn memberUrn,
      @NonNull ProfileView profileView) {
    SalesProfileView salesProfileView = new SalesProfileView();
    salesProfileView.setViewerSeat(seatUrn)
        .setViewedMember(memberUrn)
        .setViewerContract(UrnUtils.createContractUrn(profileView.getContractUrn()))
        .setLastViewedAt(profileView.getLastViewedTime());
    return salesProfileView;
  }

  /**
   * Helper function to check if input compoundKey is valid and get key parts
   * @param compoundKey compoundKey that contains viewer seat urn and the viewed member urn as strings
   * @return a pair of seatUrn and memberUrn. Return null if the compoundKey is invalid
   */
  private Pair<SeatUrn, MemberUrn> getEspressoKeyPartsFromCompoundKey(@NonNull CompoundKey compoundKey) {
    Object viewerSeatUrnObj = compoundKey.getPart(VIEWER_SEAT_COMPOUND_KEY);
    Object viewedMemberUrnObj = compoundKey.getPart(VIEWED_MEMBER_COMPOUND_KEY);
    SeatUrn viewerSeatUrn;
    MemberUrn viewedMemberUrn;
    try {
      viewerSeatUrn = SeatUrn.deserialize(viewerSeatUrnObj.toString());
      viewedMemberUrn = MemberUrn.deserialize(viewedMemberUrnObj.toString());
      return new Pair<>(viewerSeatUrn, viewedMemberUrn);
    } catch (URISyntaxException | NullPointerException e) {
      return null;
    }
  }
}
