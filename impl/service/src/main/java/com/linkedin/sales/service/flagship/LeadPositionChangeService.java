package com.linkedin.sales.service.flagship;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.PositionUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.DecoSpecification;
import com.linkedin.comms.NotificationCard;
import com.linkedin.deco.urn.projections.SpecParts;
import com.linkedin.deco.urn.projections.UrnSpec;
import com.linkedin.identity.Position;
import com.linkedin.identity.Profile;
import com.linkedin.notifications.NotificationTriggerSet;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.Task;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


public class LeadPositionChangeService {
  private static final String NOTIFICATION_TYPE = "SALES_LEAD_POSITION_CHANGE";

  /**
   * Normally we decorate our notifications by making Rest.li calls to other services.
   * For example, we want to find the companyName and title (job title) for the new
   * position based on the PositionUrn inside the notification and we would make a
   * call to the ISB d2://profiles/$memberId/positions/$positionId resource to get
   * those values.
   *
   * But since Flagship may be decorating and formatting a bunch of different notifications
   * that need the same data, we can make things more efficient through batching the
   * requests across notifications. So here we simply are returning a DecoSpecification
   * that says "Take the PositionUrn and fetch the companyName and (job) title. Also,
   * parse the MemberUrn out of the PositionUrn and use it to fetch the member's name and
   * profile photo." Then that data will be passed to our
   * {@link #formatNotifications(NotificationsV2[])} action so that we can use it to format
   * the notification.
   *
   * <a href="https://iwww.corp.linkedin.com/wiki/cf/display/ENGS/Onboarding+guide%3A+Communication+Rendering+as+Service+plugins">
   *   Walk-through document for notification rendering.</a>
   *
   * <a href="https://docs.google.com/document/d/1_7grJGd2uuXguUaLIQfhghs_hlunAYA761XgzXqSg5Q/edit#">
   *   Guide to Notification Decoration in Service Plugin</a>
   *
   * @param recipient is the recipient member for the SALES_LEAD_POSITION_CHANGE notification(s)
   *                  to be decorated by Flagship (voyager-api).
   * @return the decoration details, which simply wrap a {@link DecoSpecification} that references
   *         resolvers defined in voyager-api that will fetch the data we need to format any given
   *         notification(s).
   */
  public Task<CommunicationDecorator> generateDecorator(Urn recipient) {
    // The recipient can be used for lix tests so that the set of fields needed to decorate the notification
    // is different for different lix states; but if so, then set the result to have cacheable = false.
    // Currently we do not use it.

    // Use the "position" resolver from voyager-api's deco-resolvers.src to get the member's new
    // position's companyName and job title.
    SpecParts.NamedTypeSpec positionSpec = SpecParts.type(PositionUrn.ENTITY_TYPE,
        SpecParts.localSpec(Position.fields().companyName(), Position.fields().title()));

    // Use the "position:isbMini" resolver from voyager-api's comms-rendering.src to get the profile
    // details we need for the MemberUrn inside the PositionUrn.
    SpecParts.NamedTypeSpec isbMiniSpec = SpecParts.type(PositionUrn.ENTITY_TYPE, "isbMini",
        SpecParts.localSpec(Profile.fields().localizedFirstName()));

    UrnSpec urnSpec = SpecParts.urnSpec(positionSpec, isbMiniSpec);
    DecoSpecification decoSpecification = new DecoSpecification().setItemValue(urnSpec.toString());
    CommunicationDecorator result = new CommunicationDecorator()
        .setDetail(CommunicationDecorator.Detail.create(decoSpecification))
        .setCachable(true);
    return Task.value(result);
  }

  /**
   * Do not make Rest.li calls as part of this method. We need to keep latency low. Any data we
   * need needs to be requested by including it in the list of data to fetch that we return from
   * {@link #generateDecorator(Urn)}. It will then be fetched by voyager-api and included in the
   * notifications passed into this method.
   *
   * The Flagship notifications team is very sensitive to any latency and so they have decided
   * as part of their design that no formatting logic should make backend calls to other services
   * during formatting (except lix); formatting should only use the data that is included in the
   * notification which is the base notification data plus any decoration data we requested in
   * our generateDecorator code.
   *
   * @param notifications are the notifications to format.
   * @return the formatted notifications that can be returned to the client for rendering.
   */
  public NotificationCard[] formatNotifications(NotificationsV2[] notifications) {
    List<NotificationCard> formattedCards = Arrays.stream(notifications)
        .filter(this::isCorrectNotificationType)
        .map(this::formatNotification)
        .collect(Collectors.toList());
    return formattedCards.toArray(new NotificationCard[0]);
  }

  private boolean isCorrectNotificationType(NotificationsV2 notification) {
    NotificationTriggerSet notificationTriggerSet =
        notification.getNotificationTriggerSet().getNotificationTriggerSet();
    return NOTIFICATION_TYPE.equals(notificationTriggerSet.getNotificationType());
  }

  @VisibleForTesting
  protected NotificationCard formatNotification(NotificationsV2 notification) {
    return null;
  }
}
