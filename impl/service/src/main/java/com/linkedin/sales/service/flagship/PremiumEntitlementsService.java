package com.linkedin.sales.service.flagship;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.mnybe.shared.identity.Entitlement;
import com.linkedin.mnybe.shared.identity.EntityType;
import com.linkedin.mnybe.shared.identity.Party;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.flagship.PremiumEntitlementsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import java.util.Set;


/**
 * Service for fetching premium entitlements
 */
public class PremiumEntitlementsService {
  private final LixService _lixService;
  private final PremiumEntitlementsClient _premiumEntitlementsClient;

  // ID representing a member with only basic entitlements
  @VisibleForTesting
  static final Party BASIC_ENTITLEMENTS_PARTY = new Party(EntityType.Member, 0L);

  public PremiumEntitlementsService(LixService lixService, PremiumEntitlementsClient premiumEntitlementsClient) {
    _lixService = lixService;
    _premiumEntitlementsClient = premiumEntitlementsClient;
  }

  /**
   * Fetches the entitlements for a given member.
   * Respects the premium entitlements toggle lix, which will return only basic (non-premium) entitlements if enabled.
   * @param memberUrn Member to fetch entitlements for
   * @param entitlementsToGet List of entitlements to fetch
   * @return Entitlements for the member, being a subset of entitlementsToGet
   */
  public Task<Set<Entitlement>> getEntitlements(MemberUrn memberUrn, Set<Entitlement> entitlementsToGet) {
    return _lixService.getLixTreatment(memberUrn, LixUtils.PREMIUM_ENTITLEMENTS_TOGGLE_LIX, null)
        .flatMap(premiumToggleTreatment -> {
          // If the toggle is enabled, return only basic entitlements
          Party party = LixUtils.isEnabled(premiumToggleTreatment)
              ? BASIC_ENTITLEMENTS_PARTY
              : new Party(EntityType.Member, memberUrn.getIdAsLong());
          return _premiumEntitlementsClient.getEntitlements(party, entitlementsToGet);
        });
  }
}
