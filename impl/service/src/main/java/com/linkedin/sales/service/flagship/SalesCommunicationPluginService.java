package com.linkedin.sales.service.flagship;

import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.NotificationCard;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.service.flagship.enums.SalesCommunicationCampaignName;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesCommunicationPluginService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesCommunicationPluginService.class);
  private final Map<SalesCommunicationCampaignName, SalesCommunicationRenderer> _communicationRendererMap;

  public SalesCommunicationPluginService(
      Map<SalesCommunicationCampaignName, SalesCommunicationRenderer> communicationRendererMap) {
    _communicationRendererMap = communicationRendererMap;
  }

  /**
   * Formats given Sales Notifications to an array of NotificationCard by applying learning
   * domain logic.
   *
   * @param notifications notifications that needs to be formatted.
   * @param communicationContext the context in which the notification is to be formatted.
   * @return An array of notification card
   */
  public Task<NotificationCard[]> formatNotifications(NotificationsV2[] notifications,
      CommunicationContext communicationContext) {
    List<Task<NotificationCard>> formatNotificationTaskList = Arrays.stream(notifications)
        .map(notification -> formatNotification(communicationContext, notification))
        .collect(Collectors.toList());
    return Task.par(formatNotificationTaskList).map(this::toArray);
  }

  /**
   * Generates the decorator for the given Sales communication and context. Comms platform will
   * use the generated decorator to decorate the Lss Notification communication by resolving the specified
   * referenced data.
   *
   * @param campaignName Campaign name of the communication for which the decorator is to be
   *     generated. Campaign names are defined in the comms gateway:
   *     https://comms-gateway.prod.linkedin.com/communications-gateway/v2/manage/prod/types A
   *     campaign could be InApp notification, Email, Push or SMS, the respective category can be
   *     found in CommunicationContext.
   * @param communicationContext the context in which the decorator is to be generated.
   * @return A {@link CommunicationDecorator} task.
   */
  public Task<CommunicationDecorator> generateDecorator(String campaignName,
      CommunicationContext communicationContext) {
    try {
      SalesCommunicationCampaignName campaign = SalesCommunicationCampaignName.valueOf(campaignName);
      return _communicationRendererMap.get(campaign).generateDecorator(communicationContext);
    } catch (IllegalArgumentException e) {
      LOG.error("Failed to decorate notification of campaign: {}", campaignName);
      return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, e));
    }
  }

  // Format individual notification
  private Task<NotificationCard> formatNotification(CommunicationContext communicationContext,
      NotificationsV2 notification) {
    try {
      SalesCommunicationCampaignName campaign = SalesCommunicationCampaignName.valueOf(
          notification.getNotificationTriggerSet().getNotificationTriggerSet().getNotificationType());
      return _communicationRendererMap.get(campaign).formatNotification(notification, communicationContext);
    } catch (IllegalArgumentException e) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, e));
    } catch (Exception e) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, e));
    }
  }

  // Filter out null cards (result of errors occurring during formatting), convert list to array,
  // prep for return type
  private NotificationCard[] toArray(List<NotificationCard> notificationCardList) {
    return notificationCardList.stream().filter(Objects::nonNull).toArray(NotificationCard[]::new);
  }
}