package com.linkedin.sales.service.flagship;

import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.NotificationCard;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.Task;

public interface SalesCommunicationRenderer {
  /**
   * Generates the decorator for the given Sales communication and context.
   *
   * @param communicationContext the context in which the decorator is to be generated.
   * @return A {@link CommunicationDecorator} task.
   */
  Task<CommunicationDecorator> generateDecorator(CommunicationContext communicationContext);

  /**
   * Formats a given Sales Notification to a NotificationCard by applying Learning domain logic.
   *
   * @param notification a notification that needs to be formatted.
   * @param communicationContext the context in which the notification is to be formatted. {@link
   *     com.linkedin.common.AttributedText} with appropriate attributes.
   * @return A {@link NotificationCard}.
   */
  Task<NotificationCard> formatNotification(
      NotificationsV2 notification, CommunicationContext communicationContext);
}