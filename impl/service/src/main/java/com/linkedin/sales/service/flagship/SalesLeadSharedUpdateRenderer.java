package com.linkedin.sales.service.flagship;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.AttributedText;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.DecoSpecification;
import com.linkedin.comms.GhostImageType;
import com.linkedin.comms.Image;
import com.linkedin.comms.ImageAttribute;
import com.linkedin.comms.ImageAttributeArray;
import com.linkedin.comms.Insight;
import com.linkedin.comms.MemberImage;
import com.linkedin.comms.NavigationAction;
import com.linkedin.comms.NotificationCard;
import com.linkedin.comms.SystemImageName;
import com.linkedin.comms.TargetUrl;
import com.linkedin.comms.TextInsight;
import com.linkedin.comms.TextInsightVariant;
import com.linkedin.comms.TextProperty;
import com.linkedin.comms.helpers.CommsLocalizationHandler;
import com.linkedin.comms.helpers.DecoratedDataHelper;
import com.linkedin.data.DataList;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.DataTemplateUtil;
import com.linkedin.identity.Position;
import com.linkedin.identity.Profile;
import com.linkedin.ligradle.product.SimpleVersion;
import com.linkedin.ligradle.product.Version;
import com.linkedin.notifications.Element;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.LocalizationHelper;
import com.linkedin.sales.service.flagship.helpers.DecoHelper;
import com.linkedin.sales.service.utils.FlagshipLeadSharedUpdateMetadata;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.ugc.UserGeneratedContent;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.flagship.helpers.ActionTargetUrlHelper.*;


public class SalesLeadSharedUpdateRenderer implements SalesCommunicationRenderer {
  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadSharedUpdateRenderer.class);
  private static final String MEMBER_URN_SPEC_TEMPLATE = "~member:isbMini!prune=0"
      + "(localizedFirstName,localizedLastName,positionsOrder,"
      + "positions*(localizedCompanyName,localizedTitle))";
  private static final String UGC_POST_URN_SPEC_TEMPLATE =
      "!prune=0(visibility,specificContent(com.linkedin.ugc.ShareContent(shareCommentary(text))))";
  private static final String ITEM_URN_SPEC_STR =
      String.format("~share:ugcPost%s~ugcPost%s", UGC_POST_URN_SPEC_TEMPLATE, UGC_POST_URN_SPEC_TEMPLATE);

  // TODO: https://jira01.corp.linkedin.com:8443/browse/LSS-79952
  //  After the team considers there are enough users whose apps have already updated to the compatible versions,
  //  we should clean up the mp and version check.
  private static final String VOYAGER_WEB_MP_NAME = "voyager-web";
  private static final String VOYAGER_IOS_MP_NAME = "voyager-ios";
  private static final String VOYAGER_ANDROID_MP_NAME = "voyager-android";
  private static final Map<String, Version> MINIMAL_VOYAGER_MP_VERSIONS_CONTAINING_CORRECT_BLUE_COMPASS_ICON =
      ImmutableMap.of(
          VOYAGER_IOS_MP_NAME, SimpleVersion.parseVersion("9.29.7124"),
          VOYAGER_ANDROID_MP_NAME, SimpleVersion.parseVersion("1.22.110"));

  private final CommsLocalizationHandler _commsLocalizationHandler;

  public SalesLeadSharedUpdateRenderer(CommsLocalizationHandler commsLocalizationHandler) {
    _commsLocalizationHandler = commsLocalizationHandler;
  }

  private static Image createImageFromProfile(Profile profile, Urn actorUrn) {
    ImageAttribute.Attribute attribute =
        actorUrn != null
            ? ImageAttribute.Attribute.create(new MemberImage().setSource(new MemberUrn(actorUrn.getIdAsLong())))
            : ImageAttribute.Attribute.create(GhostImageType.PROFILE);
    return new Image().setAttributes(new ImageAttributeArray(new ImageAttribute().setAttribute(attribute)));
  }

  private static boolean isEqualOrHigherVersionThan(Version version, Version other) {
    return version.compareTo(other) == 0 || version.isHigherVersionThan(other);
  }

  private static InsightIcon getInsightIcon(boolean isUserSalesNavigatorMember,
      CommunicationContext communicationContext) {
    if (isUserSalesNavigatorMember) {
      if (shouldShowBlueCrossIcon(communicationContext)) {
        return InsightIcon.BLUE_COMPASS;
      } else {
        return InsightIcon.NONE;
      }
    } else {
      return InsightIcon.PREMIUM_CHIP;
    }
  }

  private static boolean shouldShowBlueCrossIcon(CommunicationContext communicationContext) {
    if (communicationContext.getAppId() == null) {
      return false;
    }

    String mpName = communicationContext.getAppId().toLowerCase();
    switch (mpName) {
      case VOYAGER_WEB_MP_NAME:
        return true;
      case VOYAGER_IOS_MP_NAME:
      case VOYAGER_ANDROID_MP_NAME:
        if (communicationContext.getAppVersion() == null) {
          return false;
        }
        Version appVersion = SimpleVersion.parseVersion(communicationContext.getAppVersion());
        return isEqualOrHigherVersionThan(appVersion,
            MINIMAL_VOYAGER_MP_VERSIONS_CONTAINING_CORRECT_BLUE_COMPASS_ICON.get(mpName));
      default:
        return false;
    }
  }

  private Insight createInsight(InsightIcon insightIcon, CommunicationContext communicationContext) {
    AttributedText attributedText =
        _commsLocalizationHandler.getAtrributedText(LocalizationHelper.KEY_FS_LEAD_SHARED_UPDATE_BRANDING_INSIGHT_TEXT,
            ImmutableMap.of(), communicationContext);
    Insight.Detail insightDetail = new Insight.Detail();
    TextInsight textInsight = new TextInsight();
    textInsight.setText(attributedText);
    if (insightIcon != InsightIcon.NONE) {
      textInsight.setVariant(TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
      textInsight.setSystemImageName(
          insightIcon == InsightIcon.BLUE_COMPASS ? SystemImageName.SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL
              : SystemImageName.SYS_ICN_PREMIUM_CHIP_SMALL);
    }
    insightDetail.setTextInsight(textInsight);
    return new Insight().setDetail(insightDetail);
  }

  private FlagshipLeadSharedUpdateMetadata getFlagshipLeadSharedUpdateMetadata(NotificationsV2 notification) {
    String groupBy = notification.getNotificationId().getGroupBy();
    return new FlagshipLeadSharedUpdateMetadata(groupBy);
  }

  private Position getFirstNonNullPosition(DataList positionsOrderDataList, DataMap positionsDataMap) {
    if (positionsOrderDataList == null || positionsDataMap == null) {
      return null;
    }

    for (Object val : positionsOrderDataList.values()) {
      String positionIdStr = val.toString();
      if (!positionsDataMap.containsKey(positionIdStr)) {
        continue;
      }
      Object positionData = positionsDataMap.get(positionIdStr);
      if (positionData == null) {
        continue;
      }
      Position position = DataTemplateUtil.wrap(positionData, Position.class);
      if (position != null) {
        return position;
      }
    }

    return null;
  }

  @Override
  public Task<CommunicationDecorator> generateDecorator(CommunicationContext communicationContext) {
    // According to {CommunicationDecorator.Fields.cachable},
    // DecoSpecs which varies based on member id or other entities are not cachable.
    // This decorator is cachable because the deco spec is fixed string and doesn't depend on any condition at all.
    DecoSpecification decoSpecification =
        new DecoSpecification().setActorValue(MEMBER_URN_SPEC_TEMPLATE).setItemValue(ITEM_URN_SPEC_STR);
    return Task.value(new CommunicationDecorator().setDetail(CommunicationDecorator.Detail.create(decoSpecification))
        .setCachable(true));
  }

  public Task<NotificationCard> formatNotification(NotificationsV2 notification,
      CommunicationContext communicationContext) {
    MemberUrn recipientMemberUrn = UrnUtils.toMemberUrn(communicationContext.getTargetEntity().getIdAsLong());
    Element element = DecoratedDataHelper.getElementsInAllUrnLists(notification).stream().findFirst().orElse(null);
    Urn entityUrn = DecoratedDataHelper.getFirstElementUrnInFirstUrnList(notification).orElse(null);
    Urn actorUrn = DecoHelper.getFirstActorUrnInFirstUrnList(notification).orElse(null);
    UserGeneratedContent userGeneratedContent = element == null ? null
        : DecoratedDataHelper.getResolvedElementItemData(element, UserGeneratedContent.class).orElse(null);
    Profile profile =
        element == null ? null : DecoratedDataHelper.getResolvedElementActorData(element, Profile.class).orElse(null);
    DataList positionsOrderDataList =
        profile == null ? null : DecoratedDataHelper.resolvedDataList(profile.data(), "positionsOrder").orElse(null);
    DataMap positionsDataMap =
        profile == null ? null : DecoHelper.resolvedData(profile.data(), "positions", LOG).orElse(null);
    Position position = getFirstNonNullPosition(positionsOrderDataList, positionsDataMap);
    Image headerImage = profile == null ? null : createImageFromProfile(profile, actorUrn);

    if (recipientMemberUrn.getIdAsLong() == 0L || element == null || entityUrn == null || userGeneratedContent == null
        || profile == null || positionsOrderDataList == null || positionsDataMap == null || position == null
        || headerImage == null) {
      LOG.error(
          "Cannot format notification: Some required data is missing, check: memberUrn={}, element={}, entityUrn={}, "
              + "userGeneratedContent={}, profile={}, positionsOrderDataList={}, positionsDataMap={}, position={}, headerImage={}",
          recipientMemberUrn, element, entityUrn, userGeneratedContent, profile, positionsOrderDataList, positionsDataMap,
          position, headerImage);
      return Task.value(null);
    }

    String ugcCommentText = userGeneratedContent.getSpecificContent().getShareContent().getShareCommentary().getText();
    String localizedFirstName = profile.getLocalizedFirstName();
    String localizedLastName = profile.getLocalizedLastName();
    String localizedTitle = position.getLocalizedTitle();
    String localizedCompanyName = position.getLocalizedCompanyName();

    FlagshipLeadSharedUpdateMetadata flagshipLeadSharedUpdateMetadata =
        getFlagshipLeadSharedUpdateMetadata(notification);

    TargetUrl feedUpdateTargetUrl =
        getFeedUpdateTargetUrl(entityUrn, flagshipLeadSharedUpdateMetadata.isUserSalesNavigatorMember());

    if (localizedFirstName == null || localizedLastName == null || localizedTitle == null
        || localizedCompanyName == null || feedUpdateTargetUrl == null || StringUtils.isBlank(localizedFirstName)
        || StringUtils.isBlank(localizedLastName) || StringUtils.isBlank(localizedTitle) || StringUtils.isBlank(
        localizedCompanyName) || StringUtils.isBlank(feedUpdateTargetUrl.getValue().getUrl().toString())) {
      LOG.error("Cannot format notification: memberUrn={}, leadProfile={}, position={}, localizedFirstName={}, "
              + "localizedLastName={}, localizedTitle={}, localizedCompanyName={}, targetUrl={}, some texts don't exist or are empty",
          recipientMemberUrn, profile, position, localizedFirstName, localizedLastName, localizedTitle, localizedCompanyName,
          feedUpdateTargetUrl);
      return Task.value(null);
    }

    AttributedText headerImageAccessibilityAttributedText =
        _commsLocalizationHandler.getAtrributedText(LocalizationHelper.KEY_FS_HEAD_SHARED_UPDATE_HEADERIMAGE_A11Y_TEXT_V2,
            ImmutableMap.of(), communicationContext);

    String headlineTranslationKey = flagshipLeadSharedUpdateMetadata.isLeadSavedByUser()
        ? LocalizationHelper.KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_FOR_SAVED_LEAD
        : LocalizationHelper.KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_FOR_POTENTIAL_LEAD;

    AttributedText headlineAttributedText =
        _commsLocalizationHandler.getAtrributedText(headlineTranslationKey,
            ImmutableMap.of(
                LocalizationHelper.I18N_USER_KEY, LocalizationHelper.getNameMap(localizedFirstName, localizedLastName),
                LocalizationHelper.I18N_COMPANY_KEY, localizedCompanyName,
                LocalizationHelper.I18N_COMMENT_KEY, ugcCommentText),
            communicationContext);

    AttributedText headlineAccessibilityAttributedText =
        _commsLocalizationHandler.getAtrributedText(LocalizationHelper.KEY_FS_LEAD_SHARED_UPDATE_HEADLINE_V2_A11Y_TEXT,
            ImmutableMap.of(), communicationContext);

    AttributedText cardActionAccessibilityAttributedText =
        _commsLocalizationHandler.getAtrributedText(LocalizationHelper.KEY_FS_LEAD_SHARED_UPDATE_CARD_ACTION_A11Y_TEXT,
            ImmutableMap.of(), communicationContext);

    NotificationCard notificationCard = new NotificationCard().setNotificationKey(notification.getNotificationId())
        .setHeaderImage(headerImage.setAccessibilityText(headerImageAccessibilityAttributedText.getText()))
        .setHeadline(
            new TextProperty()
                .setText(headlineAttributedText)
                .setAccessibilityText(headlineAccessibilityAttributedText.getText())
        )
        .setCardAction(new NavigationAction().setTargetUrlData(feedUpdateTargetUrl)
            .setAccessibilityText(cardActionAccessibilityAttributedText.getText()))
        .setInsight(createInsight(
            getInsightIcon(flagshipLeadSharedUpdateMetadata.isUserSalesNavigatorMember(), communicationContext),
            communicationContext));
    return Task.value(notificationCard);
  }


  private enum InsightIcon {
    BLUE_COMPASS, PREMIUM_CHIP, NONE
  }
}
