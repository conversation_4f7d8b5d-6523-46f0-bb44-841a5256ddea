package com.linkedin.sales.service.flagship;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.ScoredEntity;
import com.linkedin.common.ScoredEntityArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.container.ic.api.ICFinder;
import com.linkedin.lss.InvocationContextUtils;
import com.linkedin.lss.clients.SimpleSettingsClient;
import com.linkedin.lss.utils.SimpleSettingUtils;
import com.linkedin.mnybe.shared.identity.Entitlement;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.sales.client.common.MemberRestrictionClient;
import com.linkedin.sales.client.pymk.SalesPymkVeniceClient;
import com.linkedin.sales.monitoring.flagship.SalesPymkRecommendationsMetrics;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.PYMKUtils;
import com.linkedin.salespymk.GroupReason;
import com.linkedin.salespymk.PymkRecommendationsGroup;
import com.linkedin.settings.SettingValue;
import com.linkedin.settingsmt.enums.UspSettingType;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * This class is for implementation for the SalesPeopleYouMayKnowRecommendationResource.
 */
public class SalesPYMKRecommendationsService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesPYMKRecommendationsService.class);

  private final LixService _lixService;
  private final ICFinder _icFinder;
  private final MemberRestrictionClient _memberRestrictionClient;
  private final PremiumEntitlementsService _premiumEntitlementsService;
  private final SalesPymkRecommendationsMetrics _salesPymkRecommendationsMetrics;
  private final SalesPymkVeniceClient _salesPymkVeniceClient;
  private final SimpleSettingsClient _simpleSettingsClient;

  // Entitlements to check for premium status
  @VisibleForTesting
  static final Set<Entitlement> ENTITLEMENTS_TO_CHECK =
      ImmutableSet.of(Entitlement.SUB_PLAN, Entitlement.CAN_ACCESS_CAP, Entitlement.CAN_ACCESS_SALES);

  private static final String MOCK_DATA_LIX_TREATMENT = "mockData";
  private static final BasicCollectionResult<PymkRecommendationsGroup> NO_RESULT =
      new BasicCollectionResult<>(Collections.emptyList());
  @VisibleForTesting
  static final Set<UspSettingType> INVITATION_SETTINGS =
      ImmutableSet.of(UspSettingType.allowShownInMeetTheTeam, UspSettingType.blockUnwantedReconnectInvitations,
          UspSettingType.blockUnwantedInvitations);
  private static final String LIX_CLIENT_PLATFORM_KEY = "clientPlatform";
  private static final String LIX_CLIENT_VERSION_KEY = "clientVersion";
  private static final Map<InvocationContextUtils.ClientPlatformType, String> CLIENT_PLATFORM_TO_NAME = ImmutableMap.of(
      InvocationContextUtils.ClientPlatformType.DESKTOP, "desktop",
      InvocationContextUtils.ClientPlatformType.MOBILE_WEB, "mobile-web",
      InvocationContextUtils.ClientPlatformType.IOS, "ios",
      InvocationContextUtils.ClientPlatformType.ANDROID, "android"
  );

  public SalesPYMKRecommendationsService(LixService lixService, ICFinder icFinder,
      MemberRestrictionClient memberRestrictionClient,
      PremiumEntitlementsService premiumEntitlementsService,
      SalesPymkRecommendationsMetrics salesPymkRecommendationsMetrics, SalesPymkVeniceClient salesPymkVeniceClient,
      SimpleSettingsClient simpleSettingsClient) {
    _lixService = lixService;
    _icFinder = icFinder;
    _memberRestrictionClient = memberRestrictionClient;
    _premiumEntitlementsService = premiumEntitlementsService;
    _salesPymkRecommendationsMetrics = salesPymkRecommendationsMetrics;
    _salesPymkVeniceClient = salesPymkVeniceClient;
    _simpleSettingsClient = simpleSettingsClient;
  }

  /**
   * This method is for finding the recommendations for a given member.
   * It implements the following:
   *  1. Hitting the venice store to fetch the recommendations.
   *  2. Filter out members who have turned off the meet the team setting
   *  3. Sort by the highest relevance score.
   * @param memberUrn the member urn of viewer, for whom the recommendations are served.
   * @param groupReason the group reason for the recommendations.
   * @return a list of recommendations.
   */
  public Task<BasicCollectionResult<PymkRecommendationsGroup>> findRecommendations(MemberUrn memberUrn,
      GroupReason groupReason) {
    _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.TOTAL_REQUESTS);
    return _lixService.getLixTreatment(memberUrn, LixUtils.LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION, null)
        .flatMap(lixTreatment -> {
          if (MOCK_DATA_LIX_TREATMENT.equals(lixTreatment)) {
            return getRecommendationsFromMockedData(groupReason);
          } else if (LixUtils.ENABLED.equals(lixTreatment)) {
            return getRecommendationsFromVenice(memberUrn, groupReason);
          }

          _salesPymkRecommendationsMetrics.incCounter(
              SalesPymkRecommendationsMetrics.Counter.FEATURE_GATING_LIX_DISABLED);
          return Task.value(NO_RESULT);
        }).recover(t -> {
          LOG.error("Failed to fetch recommendations for member: " + memberUrn, t);
          _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.TOTAL_ERRORS);
          return NO_RESULT;
        });
  }

  /**
   * Fetches the recommendations response from venice
   * @param memberUrn the member urn of viewer, for whom the recommendations are served.
   * @param groupReason the group reason for the recommendations.
   * @return BasicCollectionResult with recommendations from venice
   */
  private Task<BasicCollectionResult<PymkRecommendationsGroup>> getRecommendationsFromVenice(MemberUrn memberUrn,
      GroupReason groupReason) {
    _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.FETCH_RECOMMENDATIONS);
    return checkMemberPremiumStatus(memberUrn).flatMap(hasPremium -> {
      // If the member has premium, return no results
      if (hasPremium) {
        _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.HAS_PREMIUM);
        return Task.value(NO_RESULT);
      }

      return _salesPymkVeniceClient.getFreemiumRecommendations(memberUrn, groupReason)
          .flatMap(group -> filterInvitationRestrictions(memberUrn, group))
          .map(pymkRecommendationsGroup -> {
            if (pymkRecommendationsGroup.getRecommendations().size() > 0) {
              _salesPymkRecommendationsMetrics.incCounter(
                  SalesPymkRecommendationsMetrics.Counter.SUCCESSFUL_RESULT);
            } else {
              _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.NO_RESULTS);
            }
            return new BasicCollectionResult<>(Collections.singletonList(pymkRecommendationsGroup));
          });
    });
  }

  /**
   * Task used to fetch whether a user has premium already
   * @param memberUrn Member to check
   * @return True if the member has premium, false otherwise
   */
  private Task<Boolean> checkMemberPremiumStatus(MemberUrn memberUrn) {
    return _premiumEntitlementsService.getEntitlements(memberUrn, ENTITLEMENTS_TO_CHECK)
        .map(entitlements -> entitlements.size() > 0);
  }

  /**
   * Filter member urns from a group, who the viewer is not allowed to send invitations to
   * (e.g. has meetTheTeam disabled or the invitation requires knowledge of the viewee's email)
   * Note: This will modify the input group
   * @param group PymkRecommendationsGroup to filter
   * @return The same group with filtered members
   */
  private Task<PymkRecommendationsGroup> filterInvitationRestrictions(MemberUrn memberUrn, PymkRecommendationsGroup group) {
    List<ScoredEntity> scoredEntities = group.getRecommendations();

    if (scoredEntities.isEmpty()) {
      return Task.value(group);
    }

    return Task.par(fetchInvitationSettings(scoredEntities), fetchM2MRestrictions(memberUrn, scoredEntities))
        .map((invitationSettings, m2mRestrictions) -> scoredEntities.stream().filter(scoredEntity -> {
          Long mid = scoredEntity.getEntity().getIdAsLong();
          // Check if invitation attempt is allowed (has meetTheTeam and doesn't require email connect)
          Boolean allowMeetTheTeam = getSimpleSettingBooleanValue(invitationSettings, mid, UspSettingType.allowShownInMeetTheTeam);
          Boolean blockUnwantedInvitations = getSimpleSettingBooleanValue(invitationSettings, mid, UspSettingType.blockUnwantedInvitations);
          Boolean blockUnwantedReconnectInvitations = getSimpleSettingBooleanValue(invitationSettings, mid, UspSettingType.blockUnwantedReconnectInvitations);
          Boolean allowedInvitation = allowMeetTheTeam && !(blockUnwantedInvitations || blockUnwantedReconnectInvitations);

          // Check if they have M2M blocking restrictions
          boolean hasM2MRestriction = m2mRestrictions.contains(mid);

          return allowedInvitation && !hasM2MRestriction;
        }).collect(Collectors.toList()))
        .map(filteredRecommendations -> {
          group.setRecommendations(new ScoredEntityArray(filteredRecommendations));
          return group;
        });
  }

  /**
   * Fetch settings related to invitations for these members
   * @param scoredEntities List of scored entities (of MIDs) to fetch settings for
   * @return Map from MID to map of settings
   */
  private Task<Map<Long, Map<UspSettingType, SettingValue>>> fetchInvitationSettings(
      List<ScoredEntity> scoredEntities) {
    Set<Long> memberIds =
        scoredEntities.stream().map(scoredEntity -> scoredEntity.getEntity().getIdAsLong()).collect(Collectors.toSet());
    return _simpleSettingsClient.batchGet(memberIds, INVITATION_SETTINGS);
  }

  /**
   * Fetch M2M restrictions with the viewer
   * @param memberUrn Member urn of the viewer
   * @param scoredEntities List of scored entities (of MIDs) to filter
   * @return Set of members who the viewer is restricted from seeing
   */
  private Task<Set<Long>> fetchM2MRestrictions(MemberUrn memberUrn, List<ScoredEntity> scoredEntities) {
    List<Long> recommendedMids = scoredEntities.stream()
        .map(ScoredEntity::getEntity)
        .map(Urn::getIdAsLong)
        .collect(Collectors.toList());

    return Task.callable(() -> _memberRestrictionClient.areMembersRestricted(recommendedMids, memberUrn.getIdAsLong())
        .entrySet()
        .stream()
        .filter(Map.Entry::getValue)
        .map(Map.Entry::getKey)
        .collect(Collectors.toSet()));
  }

  /**
   * Generates a response with mocked data
   * @param groupReason Reason to set in the mocked data
   * @return BasicCollectionResult with mocked data
   */
  private Task<BasicCollectionResult<PymkRecommendationsGroup>> getRecommendationsFromMockedData(GroupReason groupReason) {
    List<Long> mockMemberIds =
        ImmutableList.of(1089446093L, 678940L, 251749025L, 19186432L, 11932467L, 429858318L, 99141443L, 550936589L, 519976357L,
            554801571L, 54509324L);
    Random random = new Random();
    PymkRecommendationsGroup group = new PymkRecommendationsGroup().setRecommendations(new ScoredEntityArray(mockMemberIds.stream()
        .map(mockMemberId -> new ScoredEntity().setRecommendationTrackingId(PYMKUtils.createTrackingId())
            .setEntity(new MemberUrn(mockMemberId))
            .setScore(random.nextFloat()))
        .sorted((p1, p2) -> p2.getScore().compareTo(p1.getScore()))
        .collect(Collectors.toList()))).setGroupReason(groupReason);

    _salesPymkRecommendationsMetrics.incCounter(SalesPymkRecommendationsMetrics.Counter.MOCK_DATA);
    return Task.value(new BasicCollectionResult<>(Collections.singletonList(group)));
  }

  /**
   * Helper to fetch non-null boolean value from simple settings
   * @param settingsMap Simple settings map
   * @param mid MID to fetch setting value for
   * @param uspSettingType Setting type to fetch
   * @return Boolean value of setting
   */
  private Boolean getSimpleSettingBooleanValue(Map<Long, Map<UspSettingType, SettingValue>> settingsMap, Long mid,
      UspSettingType uspSettingType) {
    return Boolean.TRUE.equals(
        SimpleSettingUtils.getSimpleSettingValue(settingsMap, mid, uspSettingType, Boolean.FALSE, Boolean.class));
  }
}
