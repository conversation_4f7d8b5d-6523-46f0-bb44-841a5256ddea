package com.linkedin.sales.service.flagship;

import com.linkedin.parseq.Task;
import com.linkedin.sales.client.flagship.PremiumUpsellFlowsClient;
import com.linkedin.util.IdGenerator;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.MemberUrn;
import proto.com.linkedin.premium.PremiumFlowContext;
import proto.com.linkedin.premium.PremiumUpsellCardData;
import proto.com.linkedin.premium.PremiumUpsellFlowContext;
import proto.com.linkedin.premium.PremiumUpsellSlot;
import proto.com.linkedin.salesupsell.UpsellFlowCTAConfig;
import proto.com.linkedin.salesupsell.UpsellFlowConfig;


public class SalesUpsellFlowConfigService {
  private static final String CONTROL_URN_APPEND_STRING = "_click";
  private static final String BASE_SWITCHER_URL = "https://www.linkedin.com/premium/switcher/?";
  // TODO update the utype param when premium dependency task is complete
  private static final String SALES_LADDERING_UTYPE = "sales_ms";
  private static final String UTYPE_URL_QUERY_PARAM = "utype=";
  private static final String UPSELL_ORDER_ORIGIN_URL_QUERY_PARAM = "&upsellOrderOrigin=";
  private static final String REFERENCE_ID_URL_QUERY_PARAM = "&referenceId=";
  private static final Logger LOG = LoggerFactory.getLogger(SalesUpsellFlowConfigService.class);
  private final PremiumUpsellFlowsClient _premiumUpsellFlowsClient;

  public SalesUpsellFlowConfigService(PremiumUpsellFlowsClient premiumUpsellFlowsClient) {
    _premiumUpsellFlowsClient = premiumUpsellFlowsClient;
  }

  /**
   * Fetch upsell config from backend subscriptions database and format it.
   */
  public Task<List<UpsellFlowConfig>> getUpsellConfig(MemberUrn member, PremiumUpsellSlot upsellSlot, PremiumFlowContext flowContext) {
    PremiumUpsellFlowContext premiumUpsellFlowContext = buildPremiumUpsellFlowContext(member, flowContext);
    return _premiumUpsellFlowsClient.getPremiumUpsellCardData(member, upsellSlot, premiumUpsellFlowContext)
        .recoverWith(e -> {
          LOG.warn("Returning empty list since upsell backend threw exception {} "
              + "for member {}, slot {} and context {}", e, member, upsellSlot, flowContext);
          return Task.value(null);
        })
        .map(upsellCard -> {
          if (upsellCard != null) {
            // TODO emit metrics in follow-up
            return toUpsellFlowConfig(member, upsellSlot, upsellCard);
          }
          LOG.warn("Returning empty list due to error fetching the upsell config for member {} and slot {}, "
              + "user is either not eligible or slot is not setup correctly. "
              + "Flow context for more details: {}", member, upsellSlot, flowContext);
          return Collections.emptyList();
        });
  }

  /**
   * Build flow context required to fetch upsell metadata from subscriptions backend
   */
  private PremiumUpsellFlowContext buildPremiumUpsellFlowContext(MemberUrn member, PremiumFlowContext flowContext) {
    return PremiumUpsellFlowContext.newBuilder()
    .setMember(member).setPremiumFlowContext(flowContext).build();
  }

  /**
   * Format upsell flow config from the backend upsell metadata object
   */
  private List<UpsellFlowConfig> toUpsellFlowConfig(MemberUrn member, PremiumUpsellSlot upsellSlot,
      PremiumUpsellCardData premiumUpsellCardData) {
    try {
      UpsellFlowConfig.Builder upsell = UpsellFlowConfig.newBuilder()
          .setMember(member)
          .setUpsellSlot(upsellSlot.name())
          .setControlName(
              getControlName(premiumUpsellCardData.getPrimaryCta().getControlName(), upsellSlot))
          .setTitle(premiumUpsellCardData.getTitle())
          .setSubtitle(premiumUpsellCardData.getSubtitle())
          .setFooterText(premiumUpsellCardData.getFooterText())
          .setImage(premiumUpsellCardData.getImage())
          .setUpsellCTA(getUpsellCTA(premiumUpsellCardData));
      return Collections.singletonList(upsell.build());
    } catch (Exception e) {
      LOG.warn("Returning empty list due to error fetching the upsell config for member {} and slot {}, "
          + "Please check Chameleon or UpsellV2 to ensure that the slot is setup correctly. ", member, upsellSlot);
      return Collections.emptyList();
    }
  }

  private static UpsellFlowCTAConfig getUpsellCTA(PremiumUpsellCardData premiumUpsellCardData) {
    String referenceId = IdGenerator.randomUUID();
    String utype =
        premiumUpsellCardData.hasUtype() ? Objects.requireNonNull(premiumUpsellCardData.getUtype()).toString()
            : SALES_LADDERING_UTYPE;
    UpsellFlowCTAConfig.Builder upsellCTA = UpsellFlowCTAConfig.newBuilder()
        .setCtaText(premiumUpsellCardData.getPrimaryCta().getCtaText())
        .setReferenceId(referenceId)
        .setUpsellOrderOrigin(premiumUpsellCardData.getUpsellOrderOrigin())
        .setActionUrl(getActionUrl(premiumUpsellCardData.getPrimaryCta().getActionUrl(),
            premiumUpsellCardData.getUpsellOrderOrigin(), referenceId, utype));
    return upsellCTA.build();
  }

  private static String getControlName(@Nullable String controlName, PremiumUpsellSlot upsellSlot) {
    return controlName == null ? upsellSlot.name() + CONTROL_URN_APPEND_STRING : controlName;
  }

  /**
   * Format action url for click through for when a user clicks on an upsell
   */
  private static String getActionUrl(@Nullable String actionUrl, String upsellOrderOrigin,
      String referenceId, String utype) {
    if (actionUrl != null) {
      return actionUrl;
    }
    return BASE_SWITCHER_URL + UTYPE_URL_QUERY_PARAM + utype
        + UPSELL_ORDER_ORIGIN_URL_QUERY_PARAM + upsellOrderOrigin
        + REFERENCE_ID_URL_QUERY_PARAM + referenceId;
  }
}
