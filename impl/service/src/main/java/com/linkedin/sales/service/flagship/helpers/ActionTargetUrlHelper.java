package com.linkedin.sales.service.flagship.helpers;

import com.google.common.escape.UnicodeEscaper;
import com.google.common.net.PercentEscaper;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.Urn;
import com.linkedin.comms.TargetUrl;

import static com.linkedin.sales.service.flagship.enums.SalesCommunicationCampaignName.*;


public final class ActionTargetUrlHelper {
  private static final UnicodeEscaper PERCENT_ESCAPER = new PercentEscaper("", false);
  // /feed/update/<updateUrn>
  private static final String FEED_UPDATE = "/feed/update/%s";
  private static final String FEED_UPDATE_FOR_UPSELL = FEED_UPDATE + "?type=" + FS_LEAD_SHARED_UPDATE.name();

  private ActionTargetUrlHelper() {
  }

  public static TargetUrl getFeedUpdateTargetUrl(Urn updateUrn, boolean isUserSalesNavigatorMember) {
    String feedUpdateUrlStr = String.format(isUserSalesNavigatorMember ? FEED_UPDATE : FEED_UPDATE_FOR_UPSELL,
        PERCENT_ESCAPER.escape(updateUrn.toString()));
    return new TargetUrl().setValue(TargetUrl.Value.createWithUrl(new Url(feedUpdateUrlStr)));
  }
}
