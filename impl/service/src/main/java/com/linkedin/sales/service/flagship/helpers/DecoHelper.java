package com.linkedin.sales.service.flagship.helpers;

import com.google.common.collect.Streams;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.DataTemplateUtil;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.notifications.UrnList;
import com.linkedin.restli.common.ErrorResponse;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.util.text.StringUtils;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.comms.helpers.DecoratedDataHelper.*;
import static com.linkedin.comms.helpers.OptionalHelpers.*;


/**
 * Utility functions related to Deco.
 * Copied from learning-common which copies from voyager-api.
 */
public final class DecoHelper {
  private static final Logger LOG = LoggerFactory.getLogger(DecoHelper.class);

  private DecoHelper() {
  }

  /**
   * Retrieves Deco resolved data from the given data map for the given field.
   *
   * @param data data map containing Deco resolved data
   * @param field Deco field name
   * @param logger {@link Logger} to use to log errors if they occur
   * @return Deco resolved data if it exists, otherwise an empty option
   */
  public static Optional<DataMap> resolvedData(DataMap data, String field, Logger logger) {
    Optional<ErrorResponse> exception = resolvedErrorResponse(data, field);
    if (exception.isPresent()) {
      logError(field, data, exception.get(), logger);
      return Optional.empty();
    }
    return Optional.ofNullable(data).map(d -> d.getDataMap(field));
  }

  public static Optional<ErrorResponse> resolvedErrorResponse(DataMap sourceDataMap, String field) {
    if (StringUtils.isBlank(field)) {
      return Optional.empty();
    }

    String errorFieldName = makeErrorFieldName(field);

    return Optional.ofNullable(sourceDataMap)
        .map(dataMap -> dataMap.getDataMap(errorFieldName))
        .map(error -> DataTemplateUtil.wrap(error, ErrorResponse.class));
  }

  /**
   * Gets the first actor urn in the first urnList of the urnLists array in a generic notification trigger set.
   *
   * @param notifications A generic {@link NotificationsV2}
   * @return An optional item urn.
   */
  public static Optional<Urn> getFirstActorUrnInFirstUrnList(NotificationsV2 notifications) {
    return getFirstUrnList(notifications).map(UrnList::getElements)
        .flatMap(elements -> elements.stream()
            .map(element -> getOptional(element::getActor))
            .flatMap(Streams::stream)
            .findFirst());
  }

  /**
   * Given a field name it returns the deco error field name. It handles both the original or deco result field names.
   * @param sourceField {@link String} value of the field name
   * @return {@link String} representing the deco error field name
   */
  private static String makeErrorFieldName(String sourceField) {
    if (sourceField.startsWith("~")) {
      return "!" + extractRawFieldName(sourceField);
    }
    return extractRawFieldName(sourceField) + "!";
  }

  /**
   * Logs a given deco error with the supplied logger.
   *
   * @param field name of the field that the deco value was expected in
   * @param data source {@link DataMap} that had the field that was to be decoed
   * @param error error that was extracted
   * @param log {@link Logger} that we want to log the error with
   */
  private static void logError(String field, DataMap data, ErrorResponse error, Logger log) {
    String rawField = extractRawFieldName(field);
    String urn = Optional.ofNullable(data).map(map -> map.get(rawField)).map(Object::toString).orElse("{Empty}");
    String message = "DECO ERROR: Unable to resolve URN " + urn + " for field " + rawField + " Error:" + error;

    // Due to deco errors polluting harrier logs with exceptions, we are opting to log 404's as info logs.
    if (error != null && error.hasStatus() && HttpStatus.S_404_NOT_FOUND.getCode() == error.getStatus()) {
      log.info(message);
    } else {
      log.error(message);
    }
  }

  /**
   * Attempts to extract the original field name given a deco field name.
   * @param decoFieldName {@link String} of the deco field name
   * @return the original field name if it can be determined, otherwise the supplied value is returned back
   */
  private static String extractRawFieldName(String decoFieldName) {
    if (decoFieldName.endsWith("~")) {
      return decoFieldName.substring(0, decoFieldName.length() - 1);
    }
    if (decoFieldName.startsWith("~")) {
      return decoFieldName.substring(1);
    }
    return decoFieldName;
  }
}
