package com.linkedin.sales.service.integration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.BiMap;
import com.google.common.collect.ImmutableBiMap;
import com.linkedin.ambry.client.AmbryBlobPropertiesBuilder;
import com.linkedin.common.UrlArray;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.SalesConnectedCrmSetting;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.data.template.SetMode;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.CrmDataValidationExportJob;
import com.linkedin.sales.CrmDataValidationExportJobStatus;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.client.integration.CrmSettingClient;
import com.linkedin.sales.ds.db.CrmDataValidationExportJobDB;
import com.linkedin.sales.model.CrmDataValidationServiceErrorCode;
import com.linkedin.sales.monitoring.CounterMetricsSensor;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.*;
import static com.linkedin.sales.model.CrmDataValidationServiceErrorCode.*;


/**
 * Created by jiawang on 8/14/2018
 * Service layer for interacting with CrmDataValidationExportJob
 */
public class CrmDataValidationExportJobService {
  final static String AZKABAN_PROJECT = "lss-crm-data-validation";
  final static String EXPORT_START_AT = "exportStartAt";
  final static String EXPORT_END_AT = "exportEndAt";
  final static String CRM_INSTANCE_ID = "crmInstanceId";
  final static String CRM_SOURCE = "crmSource";
  final static int MAX_RETRIES = 5;
  final static int EXECUTE_INTERVAL_IN_MILLISECONDS = 1000;
  private final static int URL_TTL_IN_MINUTES = 60;
  final static Duration DOWNLOAD_URL_TTL = Duration.of(URL_TTL_IN_MINUTES, ChronoUnit.MINUTES);
  private final static int TIMEOUT_IN_HOURS = 2;
  private final static long DATA_VALIDATION_BULK_EXPORT_CREATE_JOB_ID = 10000L;
  private final static long AMBRY_BLOB_TTL_IN_SECONDS = 3 * 24 * 60 * 60L; // 3-day TTL
  private final static String DV_AMBRY_FILE_HEADER = "crmID,crmRecordType,expiredAt,createdAt\n";

  private final static BiMap<CrmDataValidationExportJobStatus, com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus>
      PEGASUS_TO_AVRO_JOB_STATUS_ENUM_MAP = ImmutableBiMap.of(
      CrmDataValidationExportJobStatus.PROCESSING,
      com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.PROCESSING,
      CrmDataValidationExportJobStatus.COMPLETED,
      com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.COMPLETED);
  private final static Logger LOG = LoggerFactory.getLogger(CrmDataValidationExportJobService.class);

  private final CrmDataValidationExportJobDB _crmDataValidationExportJobDB;
  private final CrmPairingClient _crmPairingClient;
  private final CrmSettingClient _crmSettingClient;
  private final SalesExternalizationService _salesExternalizationService;
  private final LixService _lixService;
  private final CounterMetricsSensor _counterMetricsSensor;
  private final Set<String> _trustedCrmInstanceIds;

  public CrmDataValidationExportJobService(CrmPairingClient crmPairingClient,
      CrmDataValidationExportJobDB crmDataValidationExportJobDB,
      CrmSettingClient crmSettingClient,
      SalesExternalizationService salesExternalizationService, LixService lixService,
      CounterMetricsSensor counterMetricsSensor,
      Set<String> trustedCrmInstanceIds) {
    _crmPairingClient = crmPairingClient;
    _crmSettingClient = crmSettingClient;
    _crmDataValidationExportJobDB = crmDataValidationExportJobDB;
    _salesExternalizationService = salesExternalizationService;
    _lixService = lixService;
    _counterMetricsSensor = counterMetricsSensor;
    _trustedCrmInstanceIds = Collections.unmodifiableSet(trustedCrmInstanceIds);
  }

  /**
   * Request CRM validation data export.
   * @param exportJob export job requested from the third-party user,
   *                  which should only contain 'exportStartAt' and 'exportEndAt' field.
   * @param crmInstanceUrn the CRM instance urn of the exported data, which is an identifier for the requester.
   * @return the job identifier id.
   */
  @NonNull
  public Task<Long> createJob(@NonNull CrmDataValidationExportJob exportJob, @NonNull CrmInstanceUrn crmInstanceUrn) {
    String crmInstanceId = CrmUrnUtils.getInstanceId(crmInstanceUrn);

    Task<Boolean> isEligibleTask = Task.value(Boolean.TRUE);
    // Skip check for trusted crm instances
    if (!_trustedCrmInstanceIds.contains(crmInstanceId)) {
      // For real user requests, check if the request application is requesting data for its own crm instance,
      // and the crm instance is connected to at least one SN contract,
      // and make sure at least one of the SN contracts has turned on Data Validation feature.
      isEligibleTask =
          _salesExternalizationService.isRequestTrustedForApplication(crmInstanceUrn).flatMap(isTrusted -> {
            if (!isTrusted) {
              throw newErrorWithCode(HttpStatus.S_403_FORBIDDEN, REQUEST_NOT_TRUSTED);
            }
            return _crmPairingClient.findByCrmInstance(crmInstanceUrn, true).flatMap(crmPairingsResponse -> {
              if (crmPairingsResponse.getElements().isEmpty()) {
                throw newErrorWithCode(HttpStatus.S_403_FORBIDDEN, NOT_SYNCED_TO_SN);
              }
              Set<CrmPairingUrn> crmPairingUrns = crmPairingsResponse.getElements()
                  .stream()
                  .filter(CrmPairing::hasContract)
                  .map(pairing -> new CrmPairingUrn(pairing.getContract(), pairing.getCrmPairingId()))
                  .collect(Collectors.toSet());
              List<Task<SalesConnectedCrmSetting>> dataValidationSettingTasks = crmPairingUrns.stream()
                  .map(crmPairingUrn -> _crmSettingClient.get(crmPairingUrn,
                      SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED))
                  .collect(Collectors.toList());
              return Task.par(dataValidationSettingTasks)
                  .map(dataValidationSettings -> dataValidationSettings.stream()
                      .anyMatch(setting -> setting != null && setting.hasValue() && setting.getValue().isBoolean()
                          && setting.getValue().getBoolean()));
            });
          });
    }
    return isEligibleTask.map(isEligible -> {
      if (Boolean.FALSE.equals(isEligible)) {
        throw newErrorWithCode(HttpStatus.S_403_FORBIDDEN, FEATURE_NOT_ENABLED);
      }
      LOG.info("Bulk Export is Enabled. Returning job id {} for crm instance id {}", DATA_VALIDATION_BULK_EXPORT_CREATE_JOB_ID, crmInstanceId);
      _counterMetricsSensor.incrementCrmDataValidationBulkExportCreateCallCounter();
      // Returning a dummy value 10000L
      return DATA_VALIDATION_BULK_EXPORT_CREATE_JOB_ID;
    }).recoverWith(t -> Task.failure("Unable to create Data Validation job for " + crmInstanceId, t));
  }

  /**
   * Get the crm data validation export based on the job id.
   * @param jobId the job identifier id.
   * @param crmInstanceUrn the CRM instance urn of the exported data, which is an identifier for the requester.
   * @return {@link CrmDataValidationExportJob} if processing or completed.
   *         500 Error Code if there's any internal error.
   *         504 Error Code if the request is timed out.
   */
  @NonNull
  public Task<CrmDataValidationExportJob> getJob(long jobId, @NonNull CrmInstanceUrn crmInstanceUrn) {
    String crmInstanceId = CrmUrnUtils.getInstanceId(crmInstanceUrn);

    Task<Boolean> isTrustedTask = Task.value(Boolean.TRUE);
    if (!_trustedCrmInstanceIds.contains(crmInstanceId)) {
      isTrustedTask = _salesExternalizationService.isRequestTrustedForApplication(crmInstanceUrn);
    }

    return isTrustedTask.flatMap(isTrusted -> {
      if (!isTrusted) {
        throw newErrorWithCode(HttpStatus.S_403_FORBIDDEN, REQUEST_NOT_TRUSTED);
      }

      _counterMetricsSensor.incrementCrmDataValidationBulkExportGetCallCounter();
      return getMostRecentBulkProcessedJob(jobId, crmInstanceUrn);
    }).recoverWith(t -> Task.failure("Unable to get Data Validation job status for " + crmInstanceId, t));
  }

  /**
   * Get the most recently processed job with bulk export flow by job id and crm instance id.
   * The records processed with bulk flow are inserted by lss-samza with ttl of 3 days.
   * The method _crmDataValidationExportJobDB.find will return max of 3 records because of 3 days TTL.
   * @param jobId job id
   * @param crmInstanceUrn crm instance urn
   * @return task of crmDataValidationExportJob
   */
  private Task<CrmDataValidationExportJob> getMostRecentBulkProcessedJob(long jobId, @NonNull CrmInstanceUrn crmInstanceUrn) {
    long timeOneDayAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
    String crmInstanceId = CrmUrnUtils.getInstanceId(crmInstanceUrn);
    LOG.info("Bulk Export is Enabled. Looking Espresso for crm instance id {}", crmInstanceId);

    Task<Boolean> isFilterAmbryFilesForLastOneDayLixEnabledTask =
        _lixService.isCrmInstanceBasedLixEnabled(crmInstanceUrn, LixUtils.LSS_CRM_DV_FILTER_FILES_FOR_LAST_ONE_DAY);
    Task<List<com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob>> dataValidationExportJobTask
        = _crmDataValidationExportJobDB.find(crmInstanceId);
    return Task.par(dataValidationExportJobTask, isFilterAmbryFilesForLastOneDayLixEnabledTask)
        .map((crmDataValidationExportJobs, isFilterAmbryFilesForLastOneDayLixEnabled) -> crmDataValidationExportJobs.stream()
            .filter(crmDataValidationExportJob -> crmDataValidationExportJob.status == COMPLETED_WITH_BULK_EXPORT)
            // Filter Ambry files created only in the last 24 hours to not return the same Ambry file more than once.
            // Potentially fixes LSS-69766
            .filter(crmDataValidationExportJob -> !isFilterAmbryFilesForLastOneDayLixEnabled
                || crmDataValidationExportJob.getCreatedTime() > timeOneDayAgo)
            .max(Comparator.comparingLong(com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob::getCreatedTime)))
        .flatMap(maybeCrmDataValidationExportJob -> maybeCrmDataValidationExportJob
            .map(avroExportJob -> convertFromAvro(avroExportJob, jobId, crmInstanceId)
                .map(exportJob -> {
                  if (avroExportJob.getAmbryBlobId() == null) {
                    LOG.error("AmbryBlobId is null for crm instance id {}", crmInstanceId);
                    throw newErrorWithCode(HttpStatus.S_500_INTERNAL_SERVER_ERROR, BULK_EXPORT_MISSING_AMBRY_BLOB_ID);
                  }
                  exportJob.setExpireAt(Date.from(Instant.now().plus(URL_TTL_IN_MINUTES, ChronoUnit.MINUTES)).getTime());
                  exportJob.setDownloadUrls(createAmbryDownloadUrls(avroExportJob.getAmbryBlobId().toString()));
                  // NextExportStartAt field must be a valid timestamp and is validated by the dynamics package
                  exportJob.setNextExportStartAt(System.currentTimeMillis());
                  LOG.info("Returning ambry blob id {} for crm instance id {}", avroExportJob.getAmbryBlobId(), crmInstanceId);
                  return exportJob;
        })).orElseGet(() -> {
              // return ambry file with empty content
              _counterMetricsSensor.incrementCrmDataValidationNoRecentAmbryBlobFoundCounter();
              return Task.value(createDataValidationJobWithEmptyBlob(crmInstanceUrn, jobId));
        }))
        .recoverWith(t -> Task.failure("Unable to get most recently processed data validation job for " + crmInstanceId, t));
  }

  /**
   * Translate from {@link com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob} to {@link CrmDataValidationExportJob}
   * @param avroExportJob The export job to read, in avro form
   * @return pegasus object of the export job
   */
  @VisibleForTesting
  @NonNull
  Task<CrmDataValidationExportJob> convertFromAvro(
      @NonNull com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob, long jobId,
      @NonNull String crmInstanceId) {
    long createdTime = avroExportJob.createdTime;
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus avroJobStatus = avroExportJob.status;
    Task<CrmDataValidationExportJobStatus> pegasusJobStatusTask;
    if (isSLAExceeded(createdTime)
        && avroJobStatus == com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.PROCESSING) {
      // With Bulk export being enabled, none of the export jobs are created with Processing as status.
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Export Job Status Shouldn't be Processing");
    } else if (isJobFailed(avroJobStatus)) {
      // Based on Espresso DB, it has failed
      LOG.error("job {} has failed for {} due to Gobblin error: {}", jobId, crmInstanceId, avroExportJob.errorMessage);
      pegasusJobStatusTask = Task.value(CrmDataValidationExportJobStatus.FAILED_DUE_TO_INTERNAL_ERROR);
    } else if (avroJobStatus == COMPLETED_WITH_BULK_EXPORT) {
      pegasusJobStatusTask = Task.value(CrmDataValidationExportJobStatus.COMPLETED);
    } else {
      pegasusJobStatusTask = Task.value(PEGASUS_TO_AVRO_JOB_STATUS_ENUM_MAP.inverse().get(avroExportJob.status));
    }

    return pegasusJobStatusTask.map(pegasusJobStatus -> {
      Preconditions.checkArgument(pegasusJobStatus != null,
          "Other status should already be handled previously: " + avroExportJob.status);

      return new CrmDataValidationExportJob().setJobId(jobId)
          .setExportStartAt(avroExportJob.exportStartTime)
          .setExportEndAt(avroExportJob.exportEndTime)
          .setNextExportStartAt(avroExportJob.nextStartTime, SetMode.IGNORE_NULL)
          .setStatus(pegasusJobStatus);
    });
  }

  /**
   * Construct URLs for user to download the Data Validation Reports
   * @param ambryBlobIdField value of ambryBlobId field in the Espresso table
   * @return URLs for user to download the Data Validation Reports
   */
  @NonNull
  private UrlArray createAmbryDownloadUrls(@NonNull String ambryBlobIdField) {
    String[] ambryBlobIds = ambryBlobIdField.split(ServiceConstants.COMMA_DELIMITER);
    try {
      return _salesExternalizationService.createSignedAmbryUrls(ambryBlobIds, null, DOWNLOAD_URL_TTL);
    } catch (RestLiServiceException e) {
      throw newErrorWithCode(HttpStatus.S_500_INTERNAL_SERVER_ERROR, AMBRY_URL_CREATION_FAILURE);
    }
  }

  @NonNull
  private RestLiServiceException newErrorWithCode(@NonNull HttpStatus httpStatus,
      @Nullable CrmDataValidationServiceErrorCode errorCode) {
    RestLiServiceException exception = new RestLiServiceException(httpStatus);
    if (errorCode != null) {
      exception.setServiceErrorCode(errorCode.getCode());
    }
    return exception;
  }

  private boolean isSLAExceeded(long createdTime) {
    return Date.from(Instant.now().minus(TIMEOUT_IN_HOURS, ChronoUnit.HOURS)).after(new Date(createdTime));
  }

  /**
   * Check if the job processing is failed based on the status logged in the Espresso table
   * @param avroJobStatus status logged in the Espresso table
   * @return
   */
  private boolean isJobFailed(com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus avroJobStatus) {
    return avroJobStatus != com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.COMPLETED
        && avroJobStatus != com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.PROCESSING
        && avroJobStatus != COMPLETED_WITH_BULK_EXPORT;
  }

  @VisibleForTesting
  CrmDataValidationExportJob createDataValidationJobWithEmptyBlob(@NonNull CrmInstanceUrn crmInstanceUrn, long jobId) {
    // the content is only the header row
    String blobWithEmptyContent = createDataValidationBlob(crmInstanceUrn, DV_AMBRY_FILE_HEADER);
    CrmDataValidationExportJob exportJob = new CrmDataValidationExportJob();
    exportJob.setJobId(jobId);
    exportJob.setStatus(CrmDataValidationExportJobStatus.COMPLETED);
    exportJob.setExportStartAt(System.currentTimeMillis());
    exportJob.setExportEndAt(System.currentTimeMillis());
    exportJob.setExpireAt(Date.from(Instant.now().plus(URL_TTL_IN_MINUTES, ChronoUnit.MINUTES)).getTime());
    // NextExportStartAt field must be a valid timestamp and is validated by the dynamics package
    exportJob.setNextExportStartAt(System.currentTimeMillis());
    exportJob.setDownloadUrls(createAmbryDownloadUrls(blobWithEmptyContent));
    return exportJob;
  }

  private String createDataValidationBlob(@NonNull CrmInstanceUrn crmInstanceUrn, @NonNull String content) {
    AmbryBlobPropertiesBuilder ambryBlobPropertiesBuilder = new AmbryBlobPropertiesBuilder("text/csv", "LssCrmDataValidation", crmInstanceUrn)
        .accountName("LssCrmDataValidation")
        .timeToLiveInSeconds(AMBRY_BLOB_TTL_IN_SECONDS);
    InputStream blobStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
    return _salesExternalizationService.putBlob(ambryBlobPropertiesBuilder.build(), blobStream);
  }
}
