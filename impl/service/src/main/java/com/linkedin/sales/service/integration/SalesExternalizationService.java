package com.linkedin.sales.service.integration;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.ambry.client.AmbryBlobProperties;
import com.linkedin.ambry.client.AmbryClient;
import com.linkedin.ambry.client.AmbryClientException;
import com.linkedin.ambry.client.AuthMethod;
import com.linkedin.ambry.client.SignedUrlParamsBuilder;
import com.linkedin.common.UrlArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.sales.client.externalization.ProvisionedApplicationClient;
import com.linkedin.sales.service.utils.UrnUtils;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.InputStream;
import java.time.Duration;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xeril.util.Utils;


/**
 * Service layer for interacting with externalization related operations
 * <AUTHOR>
 */
public class SalesExternalizationService {
  private final static Logger LOG = LoggerFactory.getLogger(SalesExternalizationService.class);

  private final GatewayCallerFinder _gatewayCallerFinder;
  private final ProvisionedApplicationClient _provisionedApplicationClient;
  private final AmbryClient _ambryClient;

  public SalesExternalizationService(GatewayCallerFinder gatewayCallerFinder,
      ProvisionedApplicationClient provisionedApplicationClient, AmbryClient ambryClient) {
    _gatewayCallerFinder = gatewayCallerFinder;
    _provisionedApplicationClient = provisionedApplicationClient;
    _ambryClient = ambryClient;
  }

  /**
   * Check to see if the request application is requesting data for its own crm instance
   * @param crmInstanceFromRequest urn of the crm instance from the user request
   * @return true if the request could be trusted
   */
  public Task<Boolean> isRequestTrustedForApplication(@NonNull CrmInstanceUrn crmInstanceFromRequest) {
    DeveloperApplicationUrn developerApplicationUrn = getDeveloperApplicationUrn();

    return _provisionedApplicationClient.get(developerApplicationUrn).map(provisionedApplication -> {
      boolean isTrusted =
          Objects.equals(CrmUrnUtils.getInstanceId(crmInstanceFromRequest), provisionedApplication.getUniqueForeignId());
      if (!isTrusted) {
        LOG.warn("Requested data for instance {} does not match the instance {} in the app", crmInstanceFromRequest,
            provisionedApplication.getUniqueForeignId());
      }
      LOG.info("DeveloperApplication {} for {}", developerApplicationUrn, provisionedApplication.getUniqueForeignId());
      return isTrusted;
    }).recover(t -> Boolean.FALSE);
  }

  /**
   * Construct signed URLs for user to download data from Ambry storage
   * @param ambryBlobIds Ambry blob ids that are associated with the data to be exported
   * @param memberUrn The member urn of the user requesting data. Null if it is two-legged-auth
   * @param urlTtl ttl for signed url
   * @return Signed URLs
   */
  @NonNull
  public UrlArray createSignedAmbryUrls(@NonNull String[] ambryBlobIds, @Nullable MemberUrn memberUrn,
      @NonNull Duration urlTtl) throws RestLiServiceException {
    DeveloperApplicationUrn developerApplicationUrn = getDeveloperApplicationUrn();
    return Arrays.stream(ambryBlobIds)
        .filter(blobId -> !Utils.isEmptyString(blobId))
        .map(blobId -> createSignedAmbryUrl(developerApplicationUrn, blobId, memberUrn, urlTtl))
        .collect(Collectors.toCollection(UrlArray::new));
  }

  /**
   * Construct a signed URL for user to download data from Ambry storage
   * @param ambryBlobId Ambry blob id that is associated with the data to be exported
   * @param memberUrn The member urn of the user requesting data. Null if it is two-legged-auth
   * @param urlTtl ttl for signed url
   * @return A signed URL
   */
  @NonNull
  public Url createSignedAmbryUrl(@NonNull String ambryBlobId, @Nullable MemberUrn memberUrn, @NonNull Duration urlTtl)
      throws RestLiServiceException {
    DeveloperApplicationUrn developerApplicationUrn = getDeveloperApplicationUrn();
    return createSignedAmbryUrl(developerApplicationUrn, ambryBlobId, memberUrn, urlTtl);
  }

  @VisibleForTesting
  @NonNull
  Url createSignedAmbryUrl(@NonNull DeveloperApplicationUrn developerApplicationUrn,
      @NonNull String ambryBlobId, @Nullable MemberUrn memberUrn, @NonNull Duration urlTtl)
      throws RestLiServiceException {
    SignedUrlParamsBuilder signedUrlParamsBuilder = SignedUrlParamsBuilder.forGet(ambryBlobId)
        .authMethod(AuthMethod.AUTH_OAUTH2)
        .applicationUrn(developerApplicationUrn)
        .urlTtlSecs(urlTtl.getSeconds());
    if (memberUrn != null) {
      signedUrlParamsBuilder.memberUrn(memberUrn);
    }
    try {
      return new Url(_ambryClient.getSignedUrl(signedUrlParamsBuilder.build()));
    } catch (AmbryClientException e) {
      LOG.error("Unable to get signed url for {}", ambryBlobId, e);
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
  }

  @VisibleForTesting
  @NonNull
  public String putBlob(@NonNull AmbryBlobProperties blobProperties, @NonNull InputStream blob)
      throws RestLiServiceException {
    try {
      return _ambryClient.putBlob(blobProperties, blob);
    } catch (AmbryClientException e) {
      LOG.error("Unable to create blob", e);
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
  }

  @NonNull
  private DeveloperApplicationUrn getDeveloperApplicationUrn() throws RestLiServiceException {
    DeveloperApplicationUrn developerApplicationUrn =
        UrnUtils.createDeveloperApplicationUrn(_gatewayCallerFinder.getCaller().getApplicationId());
    if (developerApplicationUrn == null) {
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
    return developerApplicationUrn;
  }
}
