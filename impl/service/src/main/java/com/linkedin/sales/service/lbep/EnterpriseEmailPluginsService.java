package com.linkedin.sales.service.lbep;

import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.platform.api.EnterpriseOnboardingEmailException;
import com.linkedin.platform.email.EnterpriseOnboardingEmailCustomData;
import com.linkedin.platform.email.EnterpriseOnboardingEmailTriggerContext;


/**
 * Service class interface for plugin methods called by EP when sending emails for LSS specific logic
 */
public interface EnterpriseEmailPluginsService {


  /**
   * Should send onboarding email based on the provided context.
   * EP performs basic filtering logic through configurations, and will not call this if events are triggered by
   * applicationInst<PERSON>Move, profileMerge, migration viewer and cs viewers.
   * This method is responsible for additional custom filtering required by LSS.
   *
   * @param context The context includes all information in the triggering flow
   * @param viewer The viewer of the action
   * @return Boolean result of whether onboarding email should be sent
   */
  Task<Boolean> shouldSendOnboardingEmail(EnterpriseOnboardingEmailTriggerContext context, Urn viewer)
      throws EnterpriseOnboardingEmailException;

  /**
   * Provide onboarding email custom data for <PERSON> to render the email.
   * This includes redirectUrl
   *
   * @param context The context includes all information in the triggering flow
   * @param viewer The viewer of the action
   * @return EnterpriseOnboardingEmailCustomData
   */
  Task<EnterpriseOnboardingEmailCustomData> fetchOnboardingEmailData(
      EnterpriseOnboardingEmailTriggerContext context, Urn viewer) throws EnterpriseOnboardingEmailException;
}
