package com.linkedin.sales.service.lbep;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.AclRoleUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseBudgetGroupUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.GetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.EnterpriseLicenseType;
import com.linkedin.enterprise.account.ApplicationInstanceType;
import com.linkedin.enterprise.acl.EisRole;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignment;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentArray;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentState;
import com.linkedin.enterprise.license.LicenseAssignmentStatusEnum;
import com.linkedin.parseq.Task;
import com.linkedin.platform.api.EnterpriseOnboardingEmailException;
import com.linkedin.platform.email.EnterpriseOnboardingEmailCustomData;
import com.linkedin.platform.email.EnterpriseOnboardingEmailTriggerContext;
import com.linkedin.platform.email.LicenseAssignment;
import com.linkedin.platform.email.LicenseAssignmentArray;
import com.linkedin.platform.email.RenderingProduct;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.SalesNavigatorEmailService;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.lbep.EnterpriseEmailPluginsServiceMetrics.Counter.*;


public class EnterpriseEmailPluginsServiceImpl implements EnterpriseEmailPluginsService {

  private static final Logger LOG = LoggerFactory.getLogger(EnterpriseEmailPluginsServiceImpl.class);

  private final SalesNavigatorEmailService _emailService;
  private final EnterpriseEmailPluginsServiceMetrics _metrics;
  private final LixService _lixService;

  private static final Set<EnterpriseLicenseType> LSS_LICENSE_TYPES = ImmutableSet.of(
      EnterpriseLicenseType.SALES_NAVIGATOR_TIER0,
      EnterpriseLicenseType.SALES_NAVIGATOR_TIER1,
      EnterpriseLicenseType.SALES_NAVIGATOR_TIER2,
      EnterpriseLicenseType.SALES_NAVIGATOR_TIER3,
      EnterpriseLicenseType.SALES_NAVIGATOR_TIER4,
      EnterpriseLicenseType.SALES_NAVIGATOR_TEAMLINK_EXTEND
  );

  private static final Set<EisRole> LSS_ROLE_TYPES = ImmutableSet.of(
      EisRole.SALES_NAVIGATOR_PRODUCT_ADMIN,
      EisRole.SALES_NAVIGATOR_TEAMSKU_PRODUCT_ADMIN,
      EisRole.SALES_NAVIGATOR_PLATFORM_SKU_ADMIN,
      EisRole.SALES_NAVIGATOR_REPORTING_ADMIN
  );

  public EnterpriseEmailPluginsServiceImpl(SalesNavigatorEmailService emailService,
      EnterpriseEmailPluginsServiceMetrics metrics, LixService lixService) {
    _emailService = emailService;
    _metrics = metrics;
    _lixService = lixService;
  }

  @Override
  public Task<Boolean> shouldSendOnboardingEmail(EnterpriseOnboardingEmailTriggerContext context, Urn viewer)
      throws EnterpriseOnboardingEmailException {
    _metrics.incCounter(FILTER_PLUGIN_TOTAL_COUNT);
    LOG.info("LSS EnterpriseEmailPluginsService shouldSendOnboardingEmail called by {} with context {}", viewer, context);

    if (!validateOnboardingEmailTriggerContext(context)) {
      _metrics.incCounter(FILTER_PLUGIN_INVALID_CONTEXT_COUNT);
      LOG.warn("LSS CustomFilterPlugin being called by {} with invalid context {}", viewer, context);
      return Task.value(false);
    }

    LicenseAssignmentArray currentLicenseAssignments = context.getCurrentLicenseAssignments(GetMode.NULL);
    LicenseAssignmentArray previousLicenseAssignments = context.getPreviousLicenseAssignments(GetMode.NULL);
    EnterpriseRoleAssignmentArray currentAclRoles = context.getCurrentAclRoles(GetMode.NULL) == null
        ? null : context.getCurrentAclRoles().stream()
        .filter(role -> role.getState().equals(EnterpriseRoleAssignmentState.APPROVED))
        .collect(Collectors.toCollection(EnterpriseRoleAssignmentArray::new));

    // context should have at least one non-empty license/role field
    if ((currentLicenseAssignments == null || currentLicenseAssignments.isEmpty())
        && (currentAclRoles == null || currentAclRoles.isEmpty())) {
      _metrics.incCounter(FILTER_PLUGIN_FILTERED_OUT_COUNT);
      LOG.warn("LSS CustomFilterPlugin being called by {} with no new license/role assignment "
          + "in context {}", viewer, context);
      return Task.value(false);
    }

    // only 1 license or role assignment at a time is allowed
    if ((currentLicenseAssignments != null && currentLicenseAssignments.size() > 1)
        || (currentAclRoles != null && currentAclRoles.size() > 1)) {
      _metrics.incCounter(FILTER_PLUGIN_FILTERED_OUT_COUNT);
      LOG.warn("LSS CustomFilterPlugin being called by {} with more than 1 current license/role assignment in context {}",
          viewer, context);
      return Task.value(false);
    }

    // license assignment needs to be LSS license with INVITED status
    if (currentLicenseAssignments != null && currentLicenseAssignments.size() == 1) {
      LicenseAssignment currentLicenseAssignment = currentLicenseAssignments.get(0);
      if (!isLssLicenseType(currentLicenseAssignment)) {
        _metrics.incCounter(FILTER_PLUGIN_FILTERED_OUT_COUNT);
        LOG.warn("LSS CustomFilterPlugin being called by {} with non-LSS current license type in context {}",
            viewer, context);
        return Task.value(false);
      }

      // this check can be cleaned up once TLE status in settings gets migrated to EP. TODO: LSS-80965
      if (!LicenseAssignmentStatusEnum.INVITED.equals(currentLicenseAssignment.getStatus().getStatus())) {
        _metrics.incCounter(FILTER_PLUGIN_FILTERED_OUT_COUNT);
        LOG.warn("LSS CustomFilterPlugin being called by {} with non-invited current license in context {}",
            viewer, context);
        return Task.value(false);
      }
    }

    // role assignment needs to be LSS role
    if (currentAclRoles != null && currentAclRoles.size() == 1) {
      EnterpriseRoleAssignment currentRoleAssignment = currentAclRoles.get(0);
      AclRoleUrn currentAclRoleUrn = currentRoleAssignment.getKey().getRole();
      if (!isLssRoleType(currentAclRoleUrn)) {
        _metrics.incCounter(FILTER_PLUGIN_FILTERED_OUT_COUNT);
        LOG.warn("LSS CustomFilterPlugin being called by {} with non-LSS role assignment in context {}, ", viewer,
            context);
        return Task.value(false);
      }
    }

    _metrics.incCounter(FILTER_PLUGIN_PASSED_COUNT);
    LOG.info("LSS EnterpriseEmailPluginsService shouldSendOnboardingEmail=true called by {} with context {}", viewer, context);
    return Task.value(true);
  }

  @Override
  public Task<EnterpriseOnboardingEmailCustomData> fetchOnboardingEmailData(
      EnterpriseOnboardingEmailTriggerContext context, Urn viewer) throws EnterpriseOnboardingEmailException {
    _metrics.incCounter(RENDERING_PLUGIN_TOTAL_COUNT);
    LOG.info("LSS EnterpriseEmailPluginsService fetchOnboardingEmailData called by {} with context {}", viewer, context);

    EnterpriseOnboardingEmailCustomData customData = new EnterpriseOnboardingEmailCustomData();
    boolean isTLE = false;
    EnterpriseRoleAssignmentArray currentAclRoles = context.getCurrentAclRoles(GetMode.NULL) == null
        ? null : context.getCurrentAclRoles().stream()
        .filter(role -> role.getState().equals(EnterpriseRoleAssignmentState.APPROVED))
        .collect(Collectors.toCollection(EnterpriseRoleAssignmentArray::new));

    if (context.hasCurrentLicenseAssignments() && context.getCurrentLicenseAssignments().size() == 1) {
      LicenseAssignment currentLicenseAssignment = context.getCurrentLicenseAssignments().get(0);
      if (EnterpriseLicenseType.SALES_NAVIGATOR_TEAMLINK_EXTEND.getUrn()
          .equals(currentLicenseAssignment.getLicenseType())) {
        customData.setProduct(RenderingProduct.SALES_NAVIGATOR_TEAMLINK_EXTEND);
        isTLE = true;
      } else {
        customData.setProduct(RenderingProduct.DEFAULT_SALES_NAVIGATOR);
      }
    } else if (currentAclRoles != null && currentAclRoles.size() == 1) {
      customData.setProduct(RenderingProduct.SALES_NAVIGATOR_ADMIN);
    }

    try {
      Url redirectUrl = _emailService.buildLighthouseWebRedirectUrl(context.getApplicationInstance(),
          context.getProfile(), isTLE);
      customData.setRedirectUrl(redirectUrl);
      _metrics.incCounter(RENDERING_PLUGIN_SUCCESS_COUNT);
      return Task.value(customData);
    } catch (Exception ex) {
      _metrics.incCounter(RENDERING_PLUGIN_FAILURE_COUNT);
      LOG.warn("LSS EnterpriseEmailPluginsService fetchOnboardingEmailData failed to generate email redirect url: ", ex);
      throw new EnterpriseOnboardingEmailException("Failed to fetch LSS onboarding email custom data with "
          + "exception in generating email redirect url", ex);
    }
  }

  private boolean isLssLicenseType(LicenseAssignment currentLicenseAssignment) {
    return LSS_LICENSE_TYPES.stream()
        .map(EnterpriseLicenseType::getUrn)
        .anyMatch(lssLicenseTypeUrn -> lssLicenseTypeUrn.equals(currentLicenseAssignment.getLicenseType()));
  }

  private boolean isLssRoleType(AclRoleUrn currentRoleAssignment) {
    return LSS_ROLE_TYPES.stream()
        .map(EisRole::getUrn)
        .anyMatch(lssRoleTypeUrn -> lssRoleTypeUrn.equals(currentRoleAssignment));
  }

  /**
   * Returns whether the email trigger context is valid.
   * @param context EnterpriseOnboardingEmailTriggerContext
   * @return true if context is valid. false o.w.
   */
  private boolean validateOnboardingEmailTriggerContext(EnterpriseOnboardingEmailTriggerContext context) {
    if (context == null) {
      LOG.warn("LSS CustomFilterPlugin being called with missing context");
      return false;
    }

    if (!EnterpriseApplication.SALES_NAVIGATOR.getUrn().equals(context.getApplication())
        || !ApplicationInstanceType.SALES_DEFAULT.equals(context.getApplicationInstanceType())) {
      LOG.warn("LSS CustomFilterPlugin being called with non LSS products");
      throw new EnterpriseOnboardingEmailException("LSS CustomFilterPlugin being called with non-LSS products "
          + "in context " + context);
    }

    // validate sales use case required fields' existence
    ContractUrn contractUrn = context.getContract(GetMode.NULL);
    EnterpriseApplicationInstanceUrn appInstanceUrn = context.getApplicationInstance(GetMode.NULL);
    EnterpriseBudgetGroupUrn budgetGroupUrn = context.getBudgetGroup(GetMode.NULL);
    EnterpriseProfileUrn profileUrn = context.getProfile(GetMode.NULL);
    return contractUrn != null && appInstanceUrn != null && budgetGroupUrn != null && profileUrn != null;
  }
}
