package com.linkedin.sales.service.lbep;

import com.linkedin.util.AbstractEnumMetrics;
import com.linkedin.util.EnumMetrics;
import java.util.EnumSet;


public class EnterpriseEmailPluginsServiceMetrics extends AbstractEnumMetrics<EnterpriseEmailPluginsServiceMetrics.Counter,
    EnumMetrics.EmptyEnum> {

  public EnterpriseEmailPluginsServiceMetrics() {
    super(EnterpriseEmailPluginsServiceMetrics.Counter.class, EnumMetrics.EmptyEnum.class, EnumSet.noneOf(
        EnumMetrics.Stat.class));
  }

  public enum Counter {
    FILTER_PLUGIN_TOTAL_COUNT,
    FILTER_PLUGIN_INVALID_CONTEXT_COUNT,
    FILTER_PLUGIN_FILTERED_OUT_COUNT,
    FILTER_PLUGIN_PASSED_COUNT,
    RENDERING_PLUGIN_TOTAL_COUNT,
    RENDERING_PLUGIN_INVALID_CONTEXT_COUNT,
    RENDERING_PLUGIN_SUCCESS_COUNT,
    RENDERING_PLUGIN_FAILURE_COUNT
  }

}
