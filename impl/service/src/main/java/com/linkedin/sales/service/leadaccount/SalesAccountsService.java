package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.Lists;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.ChangeAuditStamps;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.espresso.SavedAccount;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.SalesEntitiesBatchUtils;
import com.linkedin.salesleadaccount.LeadAccountStarredInfo;
import com.linkedin.salesleadaccount.SalesAccount;
import com.linkedin.salesleadaccount.SalesAccountFilter;
import com.linkedin.salesleadaccount.SalesAccountSortCriteria;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Service class for SalesSavedAccountsResource
 */
public class SalesAccountsService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesAccountsService.class);
  private final LssSavedLeadAccountDB _lssSavedLeadAccountDB;
  private final Integer _maxSavedAccountLimitAllTiers;
  private final LixService _lixService;
  private static final int BATCH_CREATE_ENTITY_SIZE = 100;

  public SalesAccountsService(LssSavedLeadAccountDB lssSavedLeadAccountDB, LixService lixService,
      Integer maxSavedAccountLimitAllTiers) {
    _lssSavedLeadAccountDB = lssSavedLeadAccountDB;
    _lixService = lixService;
    _maxSavedAccountLimitAllTiers = maxSavedAccountLimitAllTiers;
  }

  /**
   * service method to create savedAccount
   * @param salesAccount saved account to be created
   * @return CompoundKey to CreateResponse Pair
   */
  public Task<Pair<CompoundKey, CreateResponse>> createSavedAccount(@NonNull SalesAccount salesAccount) {
    SeatUrn owner = salesAccount.getOwner();
    Task<Integer> totalSavedAccountsTask = _lssSavedLeadAccountDB.getSavedAccountCountForSeat(owner);
    OrganizationUrn organization = salesAccount.getOrganization();
    SavedAccount savedAccount = buildSavedAccountFromSalesAccount(salesAccount);
    CompoundKey compoundKey = getCompoundKey(salesAccount);

    return totalSavedAccountsTask.flatMap(totalSavedAccounts -> {
      if (totalSavedAccounts >= _maxSavedAccountLimitAllTiers) {
        String errorMessage = "Exceeded saved account limit for seat: {}" + owner;
        return Task.failure(
            new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, errorMessage));
      }
      return createSavedAccountInternal(owner, organization, savedAccount, compoundKey);
    });
  }

  private Task<Pair<CompoundKey, CreateResponse>> createSavedAccountInternal(SeatUrn owner, OrganizationUrn organization,
      SavedAccount savedAccount, CompoundKey compoundKey) {
    return _lssSavedLeadAccountDB.createSavedAccount(owner, organization, savedAccount).map(response -> {
      if (response == HttpStatus.S_200_OK) {
        LOG.info("Same entity has been created {}", compoundKey);
      }
      return new Pair<>(compoundKey, new CreateResponse(compoundKey, response));
    }).onFailure(t -> LOG.error("Failed to create saved account {}.", savedAccount, t));
  }

  /**
   * Batch create savedAccounts by Espresso multi-put request with certain level of concurrency
   * @param salesAccounts the saved accounts to be created
   * @return CompoundKey to CreateResponse map
   */
  public Task<Map<CompoundKey, CreateResponse>> batchCreateSavedAccounts(@NonNull List<SalesAccount> salesAccounts) {
    if (salesAccounts.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    // Owner should be identical for one batch call
    if (salesAccounts.stream().map(SalesAccount::getOwner).distinct().count() == 1) {
      Set<CompoundKey> keys = salesAccounts.stream().map(this::getCompoundKey).collect(Collectors.toSet());
      SeatUrn owner = salesAccounts.get(0).getOwner();

      Task<Integer> totalSavedAccountsTask = _lssSavedLeadAccountDB.getSavedAccountCountForSeat(owner);
      return Task.par(totalSavedAccountsTask, batchGetSalesAccounts(keys))
          .flatMap((totalSavedAccounts, salesAccountMap) -> {
            Map<CompoundKey, CreateResponse> savedAccountResultMap = new HashMap<>();
            // response of batchGetSalesAccounts only includes entries with sales account found
            salesAccountMap.keySet().forEach(key -> savedAccountResultMap.put(key, new CreateResponse(key, HttpStatus.S_200_OK)));

            List<SalesAccount> unsavedSalesAccounts = salesAccounts.stream()
                .filter(salesAccount -> !salesAccountMap.containsKey(getCompoundKey(salesAccount)))
                .collect(Collectors.toList());
            if (totalSavedAccounts + unsavedSalesAccounts.size() > _maxSavedAccountLimitAllTiers) {
              String errorMessage = "Exceeded saved account limit for seat: {}" + owner;
              return Task.failure(
                  new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, errorMessage));
            }

            return batchCreateSavedAccountsInternal(unsavedSalesAccounts, savedAccountResultMap, owner);
          }).recover(t -> {
            LOG.error("Failed to create the savedAccounts for seat: {}", owner, t);
            return salesAccounts.stream().map(salesAccount -> {
              CompoundKey compoundKey = getCompoundKey(salesAccount);
              return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                      "Failed to create the savedAccount " + compoundKey)));
            }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
          });
    } else {
      return Task.value(salesAccounts.stream().map(salesAccount -> {
        CompoundKey compoundKey = getCompoundKey(salesAccount);
        return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
            new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Owner is not identical for " + compoundKey)));
      }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }
  }

  private Task<Map<CompoundKey, CreateResponse>> batchCreateSavedAccountsInternal(List<SalesAccount> unsavedSalesAccounts,
      Map<CompoundKey, CreateResponse> savedAccountResultMap, SeatUrn owner) {
    if (unsavedSalesAccounts.isEmpty()) {
      return Task.value(savedAccountResultMap);
    }
    List<List<SalesAccount>> salesAccountBatches =
        Lists.partition(unsavedSalesAccounts, BATCH_CREATE_ENTITY_SIZE);

    return _lixService.getEntityBatchCreateConcurrencyLevel(owner,
        LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL).flatMap(
        batchCreateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(salesAccountBatches,
            this::upsertSavedAccountsHelper, batchCreateConcurrencyLevel).map(nestedCreateAccountsResultLists -> {
          Map<CompoundKey, CreateResponse> resultMap = nestedCreateAccountsResultLists.stream()
              .flatMap(List::stream)
              .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
          resultMap.putAll(savedAccountResultMap);
          return resultMap;
        }));
  }

  /**
   * Helper function to upsert saved accounts
   * @param salesAccounts the saved accounts to be upserted
   * @return list of entries of CompoundKey to CreateResponse map
   */
  private Task<List<AbstractMap.SimpleEntry<CompoundKey, CreateResponse>>> upsertSavedAccountsHelper(@NonNull List<SalesAccount> salesAccounts) {
    if (salesAccounts.isEmpty()) {
      return Task.value(Collections.emptyList());
    }

    Map<OrganizationUrn, SavedAccount> orgUrnToSavedAccountMap = salesAccounts.stream()
        .map(salesAccount -> new AbstractMap.SimpleEntry<>(salesAccount.getOrganization(),
            buildSavedAccountFromSalesAccount(salesAccount)))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    SeatUrn owner = salesAccounts.get(0).getOwner();

    return _lssSavedLeadAccountDB.createSavedAccounts(owner, orgUrnToSavedAccountMap)
        .map(responseMap -> responseMap.entrySet().stream().map(entry -> {
          OrganizationUrn organizationUrn = entry.getKey();
          CompoundKey compoundKey = buildCompoundKey(owner, organizationUrn);

          if (entry.getValue() == HttpStatus.S_201_CREATED || entry.getValue() == HttpStatus.S_200_OK) {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(compoundKey, entry.getValue()));
          } else {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    "Failed to create the savedAccount " + compoundKey)));
          }
        }).collect(Collectors.toList()))
        .recover(t -> {
          LOG.error("Failed to create the savedAccounts for seat: {}", owner, t);
          return salesAccounts.stream()
              .map(salesAccount -> new AbstractMap.SimpleEntry<>(getCompoundKey(salesAccount), new CreateResponse(
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                      "Failed to create the savedAccount " + getCompoundKey(salesAccount)))))
              .collect(Collectors.toList());
        });
  }

  /**
   * service method to delete savedAccount
   * @param compoundKey compoundKey including owner seat urn and the organization urn
   * @return UpdateResponse
   */
  private Task<UpdateResponse> deleteSavedAccount(@NonNull CompoundKey compoundKey) {
    Pair<SeatUrn, OrganizationUrn> keyPair = getKeyPartsFromCompoundKey(compoundKey);
    if (keyPair == null) {
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatUrn owner = keyPair.getFirst();
    OrganizationUrn organization = keyPair.getSecond();

    return _lssSavedLeadAccountDB.deleteSavedAccount(owner, organization).map(response -> {
      if (response == HttpStatus.S_404_NOT_FOUND) {
        LOG.info("Cannot find the savedAccount to be deleted with key {}", compoundKey);
        // Return 200 instead of 404 if record to delete doesn't exists
        return new UpdateResponse(HttpStatus.S_200_OK);
      }
      return new UpdateResponse(HttpStatus.S_204_NO_CONTENT);
    }).recover(throwable -> {
      LOG.error("Failed to delete saved account with key {}.", compoundKey, throwable);
      return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    });
  }

  /**
   * Batch delete savedAccounts
   * @param compoundKeys a set of compoundKeys for deletion
   * @return CompoundKey to UpdateResponse map
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeleteSavedAccounts(@NonNull Set<CompoundKey> compoundKeys) {
    return SalesEntitiesBatchUtils.batchDeleteEntities(compoundKeys, compoundKey -> deleteSavedAccount(compoundKey).map(
        response -> new AbstractMap.SimpleEntry<>(compoundKey, response)), _lixService);
  }

  /**
   * Get salesAccounts for give owner with pagination
   * @param owner owner's seatUrn
   * @param start paging start
   * @param count paging count
   * @param filterCriteria the condition to apply when fetching the accounts, for example, STAR_ONLY
   * @param sortCriteria how the result should be sorted, for example, by account created time
   * @param sortOrder how the result should be ordered if it is sorted, for example, either in descending or ascending order
   * @return paginatedList of salesAccounts
   */
  public Task<PaginatedList<SalesAccount>> getSalesAccountsForGivenOwner(@NonNull SeatUrn owner, int start, int count,
      @Nullable SalesAccountFilter filterCriteria, @Nullable SalesAccountSortCriteria sortCriteria, @Nullable SortOrder sortOrder) {
    return _lssSavedLeadAccountDB.getSavedAccountCountForSeat(owner).flatMap(totalCount -> {
      // Get total count only if the passed in count param is 0
      if (count == 0) {
        return Task.value(PaginatedList.createForPage(Collections.<SalesAccount>emptyList(), start, count, totalCount));
      }
      // Note: if the result is filtered, the total count may not be accurate, since the SaveAccountCount MV cannot be filtered.
      return _lssSavedLeadAccountDB.getSavedAccounts(owner, null, start, count, filterCriteria, sortCriteria, sortOrder)
          .map(resultList -> {
            List<SalesAccount> results = resultList.stream()
                .map(pair -> buildSalesAccountFromSavedAccount(pair.getSecond(), pair.getFirst(), owner))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            return PaginatedList.createForPage(results, start, count, totalCount);
      });
    }).recover(t -> {
      LOG.warn("Fail to get savedAccounts for seat: {}", owner, t);
      return PaginatedList.createForPage(Collections.<SalesAccount>emptyList(), start, count, 0);
    });
  }

  /**
   * Get salesAccount for given compoundKey
   * @param compoundKey compoundKey including owner seat urn and the organization urn
   * @return salesAccount
   */
  public Task<SalesAccount> getSalesAccountForGivenCompoundKey(@NonNull CompoundKey compoundKey) {
    Pair<SeatUrn, OrganizationUrn> keyPair = getKeyPartsFromCompoundKey(compoundKey);
    if (keyPair == null) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatUrn owner = keyPair.getFirst();
    OrganizationUrn organization = keyPair.getSecond();

    return _lssSavedLeadAccountDB.getSavedAccounts(owner, organization, DEFAULT_START, DEFAULT_COUNT, null, null, null)
        .flatMap(resultList -> {
          if (resultList.isEmpty()) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
          }
          SalesAccount salesAccount =
              buildSalesAccountFromSavedAccount(resultList.get(0).getSecond(), organization, owner);
          if (salesAccount == null) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                String.format("Failed to get salesAccount for seat: %d , account: %d", owner.getIdAsLong(),
                    organization.getIdAsLong())));
          }
          return Task.value(salesAccount);
        });
  }

  /**
   * Batch get salesAccounts for given compoundKey set
   * @param compoundKeys set of compoundKeys including owner seat urn and the organization urn
   * @return compound key to sales account mapping
   */
  public Task<Map<CompoundKey, SalesAccount>> batchGetSalesAccounts(@NonNull Set<CompoundKey> compoundKeys) {
    List<Task<AbstractMap.SimpleEntry<CompoundKey, SalesAccount>>> getSalesAccountTasks = compoundKeys.stream()
        .map(key -> getSalesAccountForGivenCompoundKey(key).map(
            salesAccount -> new AbstractMap.SimpleEntry<>(key, salesAccount)).recover(t -> null))
        .collect(Collectors.toList());

    return Task.par(getSalesAccountTasks)
        .map(salesAccountList -> salesAccountList.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
        .onFailure(t -> LOG.error("Failed to batch get salesAccounts for keys: {}", compoundKeys, t));
  }

  /**
   * Batch partial update sales accounts.  The updates only apply to the accounts that are already present.
   *
   * @param accountsToUpdateRequestMap map of (compoundKey, salesAccount) pairs for the accounts to be updated
   * @return result of the update operation
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchPartialUpdateSalesAccounts(
      @NonNull Map<CompoundKey, PatchRequest<SalesAccount>> accountsToUpdateRequestMap) {
    if (accountsToUpdateRequestMap.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    // Owner key in the request sales accounts should match the specified owner or else abort the whole operation.
    //  This is consistent with the the check in the batchCreateSavedAccounts.
    Set<CompoundKey> keys = accountsToUpdateRequestMap.keySet();
    Pair<SeatUrn, OrganizationUrn> keyPair = getKeyPartsFromCompoundKey(keys.stream().findFirst().get());
    SeatUrn owner = (keyPair == null) ? null : keyPair.getFirst();

    if (owner != null && keys.stream().allMatch(entry -> owner.equals(entry.getPart(OWNER_COMPOUND_KEY)))) {

      return batchGetSalesAccounts(keys).flatMap(existingSalesAccountMap -> {
        List<SalesAccount> salesAccountsToUpdate = Lists.newArrayList();
        Map<CompoundKey, UpdateResponse> updateAccountResultMap = new HashMap<>();

        // Only update already saved accounts.
        accountsToUpdateRequestMap.forEach((compoundKey, salesAccountInRequest) -> {
          if (existingSalesAccountMap.get(compoundKey) != null) {
            SalesAccount existingSalesAccount = existingSalesAccountMap.get(compoundKey);
            try {
              PatchApplier.applyPatch(existingSalesAccount, salesAccountInRequest);
              salesAccountsToUpdate.add(existingSalesAccount);
            } catch (DataProcessingException e) {
              updateAccountResultMap.put(compoundKey, new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
            }
          } else {
            updateAccountResultMap.put(compoundKey, new UpdateResponse(HttpStatus.S_404_NOT_FOUND));
          }
        });

        if (salesAccountsToUpdate.isEmpty()) {
          return Task.value(updateAccountResultMap);
        }

        List<List<SalesAccount>> salesAccountBatches = Lists.partition(salesAccountsToUpdate, BATCH_CREATE_ENTITY_SIZE);

        return _lixService.getEntityBatchCreateConcurrencyLevel(owner,
            LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL).flatMap(
            batchUpdateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(salesAccountBatches,
                this::upsertSavedAccountsHelper, batchUpdateConcurrencyLevel).map(nestedUpdateAccountsResultLists -> {
              Map<CompoundKey, UpdateResponse> resultMap = nestedUpdateAccountsResultLists.stream()
                  .flatMap(List::stream)
                  .collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                    HttpStatus status = entry.getValue() != null ? entry.getValue().getStatus()
                        : HttpStatus.S_500_INTERNAL_SERVER_ERROR;
                    switch (status) {
                      case S_200_OK:
                        break;
                      case S_201_CREATED:
                        LOG.warn("A previously unsaved account was created/updated: {} ", entry.getKey());
                        break;
                      default:
                        status = HttpStatus.S_500_INTERNAL_SERVER_ERROR;
                        break;
                    }

                    return new UpdateResponse(status);
                  }));
              resultMap.putAll(updateAccountResultMap);
              return resultMap;
            }));
      }).recover(t -> {
        LOG.error("Failed to update the savedAccounts for seat: {}", owner, t);
        return accountsToUpdateRequestMap.keySet()
            .stream()
            .map(ck -> new AbstractMap.SimpleEntry<>(ck,
                new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR)))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
      });
    } else {
      return Task.value(accountsToUpdateRequestMap.keySet().stream().map(compoundKey -> {
        LOG.error("Owner key in the batch partial update should be non-null and identical: {}", compoundKey);
        return new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
      }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }
  }

  /**
   * build espresso savedAccount from salesAccount
   * @param salesAccount
   * @return savedAccount
   */
  private SavedAccount buildSavedAccountFromSalesAccount(@NonNull SalesAccount salesAccount) {
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = salesAccount.getContract().toString();
    savedAccount.createdTime = salesAccount.hasChangeAuditStamps() && salesAccount.getChangeAuditStamps().hasCreated()
        && salesAccount.getChangeAuditStamps().getCreated().hasTime() ? salesAccount.getChangeAuditStamps()
        .getCreated()
        .getTime() : System.currentTimeMillis();
    // If dataSource field is not present, default value USER_GENERATED would be fetched
    savedAccount.dataSource =
        ACCOUNT_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.get(salesAccount.getDataSource(GetMode.DEFAULT));

    // There are espresso secondary indexes on starred and starLastModifiedTime.  Since secondary indexes don't support
    // filtering on null values, these fields need default values if not present.
    savedAccount.starred = salesAccount.hasStarredInfo() ? salesAccount.getStarredInfo().isStarred() : Boolean.FALSE;
    savedAccount.starLastModifiedTime =
        salesAccount.hasStarredInfo() ? salesAccount.getStarredInfo().getLastModifiedAt() : System.currentTimeMillis();

    return savedAccount;
  }

  /**
   * Helper function to get CompoundKey
   * @param salesAccount
   * @return CompoundKey
   */
  @NonNull
  public CompoundKey getCompoundKey(@NonNull SalesAccount salesAccount) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, salesAccount.getOwner())
        .append(ORGANIZATION_COMPOUND_KEY, salesAccount.getOrganization());
  }

  /**
   * Helper function to build CompoundKey from SeatUrn and OrganizationUrn
   * @param owner
   * @param organizationUrn
   * @return CompoundKey
   */
  @NonNull
  public CompoundKey buildCompoundKey(@NonNull SeatUrn owner, @NonNull OrganizationUrn organizationUrn) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, owner)
        .append(ORGANIZATION_COMPOUND_KEY, organizationUrn);
  }

  /**
   * Create salesAccount from espresso savedAccount
   * @param savedAccount
   * @param organization account's organizationUrn
   * @param owner owner's seatUrn
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "ACCOUNT_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get value is not null")
  private SalesAccount buildSalesAccountFromSavedAccount(
      @NonNull SavedAccount savedAccount, @NonNull OrganizationUrn organization,
      @NonNull SeatUrn owner
  ) {
    SalesAccount salesAccount = new SalesAccount();
    ContractUrn contract;
    try {
      contract = ContractUrn.deserialize(savedAccount.contractUrn.toString());
    } catch (URISyntaxException e) {
      LOG.warn("Fail to create contractUrn from {}", savedAccount.contractUrn, e);
      return null;
    }
    ChangeAuditStamps changeAuditStamps = new ChangeAuditStamps();
    AuditStamp createdAtAuditStamp = new AuditStamp();
    createdAtAuditStamp.setTime(savedAccount.createdTime);
    createdAtAuditStamp.setActor(owner);
    changeAuditStamps.setCreated(createdAtAuditStamp);
    // changeAuditStamps field of salesAccount cannot be modified after created, set LastModified same as Created
    changeAuditStamps.setLastModified(createdAtAuditStamp);

    salesAccount.setOwner(owner);
    salesAccount.setOrganization(organization);
    salesAccount.setContract(contract);
    salesAccount.setChangeAuditStamps(changeAuditStamps);
    if (savedAccount.dataSource != null) {
      checkArgument(ACCOUNT_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().containsKey(savedAccount.dataSource),
          String.format("Unsupported saved account data source [%s]", savedAccount.dataSource));
      salesAccount.setDataSource(ACCOUNT_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(savedAccount.dataSource));
    }

    if (savedAccount.starred != null && savedAccount.starLastModifiedTime != null) {
      salesAccount.setStarredInfo(new LeadAccountStarredInfo().setStarred(savedAccount.starred)
          .setLastModifiedAt(savedAccount.starLastModifiedTime));
    }

    return salesAccount;
  }

  /**
   * Helper function to check if input compoundKey is valid and get key parts
   * @param compoundKey compoundKey including owner seat urn and the organization urn
   * @return a pair of owner seatUrn and account organizationUrn. Return null if the compoundKey is invalid
   */
  private Pair<SeatUrn, OrganizationUrn> getKeyPartsFromCompoundKey(@NonNull CompoundKey compoundKey) {
    if (compoundKey.getPart(OWNER_COMPOUND_KEY) == null || compoundKey.getPart(ORGANIZATION_COMPOUND_KEY) == null) {
      String errMsg =
          String.format("No seatUrn or OrganizationUrn available for deletion with key %s", compoundKey);
      LOG.warn(errMsg);
      return null;
    }
    String ownerString = compoundKey.getPart(OWNER_COMPOUND_KEY).toString();
    String organizationString = compoundKey.getPart(ORGANIZATION_COMPOUND_KEY).toString();
    SeatUrn owner;
    OrganizationUrn organization;
    try {
      owner = SeatUrn.deserialize(ownerString);
      organization = OrganizationUrn.deserialize(organizationString);
    } catch (URISyntaxException e) {
      String errMsg =
          String.format("Fail to delete since the Urn type of the compound key: %s is not correct", compoundKey);
      LOG.warn(errMsg);
      return null;
    }

    return new Pair<>(owner, organization);
  }
}


