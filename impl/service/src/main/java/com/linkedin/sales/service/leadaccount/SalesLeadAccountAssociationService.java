package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.Lists;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.ChangeAuditStamps;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.ParseqUtils;
import com.linkedin.lss.restli.BatchHelper;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.espresso.AccountToLeadAssociationView;
import com.linkedin.sales.espresso.LeadToAccountAssociation;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.SalesEntitiesBatchUtils;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.ACCOUNT_COMPOUND_KEY;
import static com.linkedin.sales.service.utils.ServiceConstants.CREATOR_COMPOUND_KEY;
import static com.linkedin.sales.service.utils.ServiceConstants.DEFAULT_COUNT;
import static com.linkedin.sales.service.utils.ServiceConstants.GET_ALL_COUNT;
import static com.linkedin.sales.service.utils.ServiceConstants.LEAD_COMPOUND_KEY;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;


public class SalesLeadAccountAssociationService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadAccountAssociationService.class);
  private static final int DEFAULT_START = 0;
  static final int BATCH_CREATE_ENTITY_SIZE = 100;
  static final int BATCH_SIZE = 100;

  private final LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  private final LixService _lixService;

  public SalesLeadAccountAssociationService(LssSavedLeadAccountDB lssSavedLeadAccountDB, LixService lixService) {
    _lssSavedLeadAccountDB = lssSavedLeadAccountDB;
    _lixService = lixService;
  }

  /**
   * Delete all the existing associations of the lead and then create a new leadAccountAssociation for the lead.
   * If deletion fails, the entire creation request fails.
   * @param leadAccountAssociation leadAccountAssociation to be created.
   * @return compoundKey to createResponse pair
   */
  public Task<Pair<CompoundKey, CreateResponse>> createLeadAccountAssociation(
      @NonNull LeadAccountAssociation leadAccountAssociation) {
    MemberUrn lead = leadAccountAssociation.getLead();
    OrganizationUrn account = leadAccountAssociation.getAccount();
    SeatUrn creator = leadAccountAssociation.getCreator();
    com.linkedin.sales.espresso.LeadToAccountAssociation leadToAccountAssociation =
        createLeadToAccountAssociationFromLeadAccountAssociation(leadAccountAssociation);
    CompoundKey compoundKey = getCompoundKey(leadAccountAssociation);
    return deleteAssociationsForLead(lead, creator).flatMap(isDeleted -> {
      //One lead should only be associated with one account. Before creating a new association, we need to make sure
      //all the existing associations of the lead have been deleted.
      if (!isDeleted) {
        return Task.value(new Pair<>(compoundKey, new CreateResponse(
            new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "Failed to delete existing leadAccountAssociations for lead"))));
      }
      //If create successfully, DB will return status 201, service level will return 201.
      //If resource already exists, DB will return status 412, service level will return 200.
      return _lssSavedLeadAccountDB.createLeadAccountAssociation(account, lead, creator, leadToAccountAssociation)
          .map(createResponse -> {
            if (createResponse == HttpStatus.S_201_CREATED) {
              return new Pair<>(compoundKey, new CreateResponse(compoundKey, HttpStatus.S_201_CREATED));
            } else {
              LOG.warn("Not all associations of the account {} are deleted before re-associating", account);
              return new Pair<>(compoundKey, new CreateResponse(compoundKey, HttpStatus.S_200_OK));
            }
          });
    }).onFailure(throwable -> LOG.error("Failed to create the leadAccountAssociation {}", compoundKey, throwable));
  }

  //Help create leadToAccountAssociations
  private Task<List<AbstractMap.SimpleEntry<CompoundKey, CreateResponse>>>
  createLeadAccountAssociationsHelper(@NonNull List<LeadAccountAssociation> leadAccountAssociations) {
    if (leadAccountAssociations.isEmpty()) {
      return Task.value(Collections.emptyList());
    }
    SeatUrn seatUrn = leadAccountAssociations.get(0).getCreator();
    Map<Pair<MemberUrn, OrganizationUrn>, LeadToAccountAssociation> associationMap =
        leadAccountAssociations.stream().map(leadAccountAssociation -> {
          MemberUrn memberUrn = leadAccountAssociation.getLead();
          OrganizationUrn organizationUrn = leadAccountAssociation.getAccount();
          LeadToAccountAssociation leadToAccountAssociation =     createLeadToAccountAssociationFromLeadAccountAssociation(leadAccountAssociation);
          return new AbstractMap.SimpleEntry<>(new Pair<>(memberUrn, organizationUrn), leadToAccountAssociation);
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    return _lssSavedLeadAccountDB.createLeadAccountAssociations(associationMap, seatUrn)
        .map(results -> results.entrySet().stream().map(entry -> {
            MemberUrn memberUrn = entry.getKey().getFirst();
            OrganizationUrn organizationUrn = entry.getKey().getSecond();
            CompoundKey compoundKey = buildCompoundKey(seatUrn, memberUrn, organizationUrn);
          if (entry.getValue() == HttpStatus.S_201_CREATED) {
            return new AbstractMap.SimpleEntry<>(compoundKey,
                new CreateResponse(compoundKey, HttpStatus.S_201_CREATED));
          } else {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    "Failed to create the leadToAccountAssociation " + compoundKey)));
          }
        }).collect(Collectors.toList()))
        .recover(t -> {
          LOG.error("Failed to create the leadToAccountAssociation for seat: {}", seatUrn, t);
          return leadAccountAssociations.stream()
              .map(leadAccountAssociation -> new AbstractMap.SimpleEntry<>(getCompoundKey(leadAccountAssociation), new CreateResponse(
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                      "Failed to create the leadToAccountAssociation " + getCompoundKey(leadAccountAssociation)))))
              .collect(Collectors.toList());
        });
  }

  /**
   * Delete all the existing leadAccountAssociations for a lead
   * @return a boolean indicating whether the associations no longer exist.
   *         Note that regardless of successful deletion or no association found for the lead, we return true because
   *         the content no longer exists.
   */
  public Task<Boolean> deleteAssociationsForLead(@NonNull MemberUrn leadMemberUrn, SeatUrn ownerSeatUrn) {
    return _lssSavedLeadAccountDB.deleteLeadAccountAssociation(null, leadMemberUrn, ownerSeatUrn)
        .map(isDeletedOrNotFound -> TRUE)
        .recoverWith(throwable -> {
          LOG.error("Failed to delete leadAccountAssociation for lead {} and seat {}", leadMemberUrn, ownerSeatUrn,
              throwable);
          return Task.value(FALSE);
        });
  }

  /**
   * Delete leadAccountAssociation
   * @param compoundKey leadAccountAssociation key
   * @return updateResponse with delete results
   */
  public Task<UpdateResponse> deleteLeadAccountAssociation(@NonNull CompoundKey compoundKey) {
    if (compoundKey.getPart(LEAD_COMPOUND_KEY) == null || compoundKey.getPart(ACCOUNT_COMPOUND_KEY) == null
        || compoundKey.getPart(CREATOR_COMPOUND_KEY) == null) {
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }
    String leadString = compoundKey.getPart(LEAD_COMPOUND_KEY).toString();
    String accountString = compoundKey.getPart(ACCOUNT_COMPOUND_KEY).toString();
    String seatString = compoundKey.getPart(CREATOR_COMPOUND_KEY).toString();
    MemberUrn lead;
    OrganizationUrn account;
    SeatUrn creator;
    try {
      lead = MemberUrn.deserialize(leadString);
      account = OrganizationUrn.deserialize(accountString);
      creator = SeatUrn.deserialize(seatString);
    } catch (URISyntaxException e) {
      String message = "Fail to delete since the Urn type of the key urn is not correct ";
      LOG.error(message);
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }
    return _lssSavedLeadAccountDB.deleteLeadAccountAssociation(account, lead, creator).map(isDeleted ->
        new UpdateResponse(HttpStatus.S_204_NO_CONTENT)).recoverWith(throwable -> {
      LOG.error("Failed to delete leadAccountAssociation {}", compoundKey, throwable);
      return Task.value(new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
    });
  }

  /**
   * Batch delete leadAccountAssociations
   * @param compoundKeys leadAccountAssociation keys
   * @return compoundKey to UpdateResponse map
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeleteLeadAccountAssociations(
      @NonNull Set<CompoundKey> compoundKeys) {
    return SalesEntitiesBatchUtils.batchDeleteEntities(compoundKeys, compoundKey -> deleteLeadAccountAssociation(compoundKey).map(
        response -> new AbstractMap.SimpleEntry<>(compoundKey, response)), _lixService);
  }
  /**
   * Batch create leadAccountAssociations. One lead should only be associated with one account
   * Currently, it's only be used by seat transfer (transferring to a new seat),
   * If it be applied to other use cases, we need to make sure all the existing associations of the lead
   * have been deleted before create new association
   * @param  leadAccountAssociations leadAccountAssociations to be created
   * @return CompoundKey to CreateResponse map
   */
  public Task<Map<CompoundKey, CreateResponse>> batchCreateLeadAccountAssociations(
      @NonNull List<LeadAccountAssociation> leadAccountAssociations) {
    if (leadAccountAssociations.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }
    // Owner should be identical for one batch call, otherwise will return 400 bad request
    if (leadAccountAssociations.stream().map(LeadAccountAssociation::getCreator).distinct().count() == 1) {
      SeatUrn owner = leadAccountAssociations.get(0).getCreator();
      List<AbstractMap.Entry<CompoundKey, CreateResponse>> results = new ArrayList<>();
      List<List<LeadAccountAssociation>> associationsToCreateBatches =
          Lists.partition(leadAccountAssociations, BATCH_CREATE_ENTITY_SIZE)
              .stream()
              .map(ArrayList::new)
              .collect(Collectors.toList());
      return _lixService.getEntityBatchCreateConcurrencyLevel(owner,
          LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL)
          .flatMap(batchCreateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(associationsToCreateBatches,
              this::createLeadAccountAssociationsHelper, batchCreateConcurrencyLevel)
              .map(nestedCreateLeadAccountAssociationResultLists -> {
                List<AbstractMap.SimpleEntry<CompoundKey, CreateResponse>> createAccountsResultList =
                    nestedCreateLeadAccountAssociationResultLists.stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                results.addAll(createAccountsResultList);
                return results.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
              }))
          .recover(t -> leadAccountAssociations.stream().map(leadAccountAssociation -> {
            CompoundKey compoundKey = getCompoundKey(leadAccountAssociation);
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    "Failed to create the leadToAccountAssociation " + compoundKey)));
          }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    } else {
      return Task.value(leadAccountAssociations.stream().map(leadAccountAssociation -> {
        CompoundKey compoundKey = getCompoundKey(leadAccountAssociation);
        return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
            new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Owner is not identical for " + compoundKey)));
      }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }
  }

  /**
   * Get leadAccountAssociations for give leads.
   * @param leads find by leads
   * @param creator creator's seatUrn
   * @return A collections of leadAccountAssociation
   */
  public Task<List<LeadAccountAssociation>> getLeadAccountAssociationForLeads(@NonNull List<MemberUrn> leads,
      @NonNull SeatUrn creator) {
    List<Task<LeadAccountAssociation>> getLeadAccountAssociationTasks = leads.stream()
        .map(
            lead -> _lssSavedLeadAccountDB.getAccountsAssociatedWithGivenLead(lead, creator, DEFAULT_START,
                DEFAULT_COUNT)
                .map(responsePairs -> {
                  if (responsePairs == null || responsePairs.isEmpty()) {
                    return null;
                  }
                  if (responsePairs.size() > 1) {
                    LOG.warn("Lead {} associated with more then one account", lead);
                  }
                  //If we find more than one association for this lead, we will return the latest association
                  Pair<OrganizationUrn, LeadToAccountAssociation> latestLeadToAccountAssociation =
                      responsePairs.stream().max(Comparator.comparing(pair -> pair.getSecond().createdTime)).get();
                  OrganizationUrn account = latestLeadToAccountAssociation.getFirst();
                  return createLeadAccountAssociationFromLeadToAccountAssociation(
                      latestLeadToAccountAssociation.getSecond(), lead, creator, account);
                })
                .recover(throwable -> {
                  LOG.error("Failed to get the leadAccountAssociation for lead{}", lead, throwable);
                  return null;
                }))
        .collect(Collectors.toList());

    return Task.par(getLeadAccountAssociationTasks)
        .map(leadAccountAssociations -> leadAccountAssociations.stream().filter(Objects::nonNull).collect(
        Collectors.toList()));
  }

  /**
   * Get leadAccountAssociations for the given accounts with pagination.
   * @param accounts account's organizationUrn
   * @param owner creator's seatUrn
   * @param start paging start
   * @param count paging count
   */
  public Task<PaginatedList<LeadAccountAssociation>> getLeadAccountAssociationForAccounts(
      @NonNull List<OrganizationUrn> accounts, @NonNull SeatUrn owner, int start, int count) {
    return _lssSavedLeadAccountDB.getAssociatedLeadCounts(new HashSet<>(accounts), owner)
        .flatMap(accountToLeadCntMap -> {
          // Retrieve Lead Account associations in batches from Espresso.
          List<Task<List<LeadAccountAssociation>>> getLeadAccountAssociationTasks = accountToLeadCntMap.entrySet()
              .stream()
              .map(entrySet -> BatchHelper.batchedDataFinderAsync(entrySet.getValue(), BATCH_SIZE,
                  (pagingContext -> getLeadAccountAssociationForAccount(pagingContext, owner, entrySet.getKey()))))
              .collect(Collectors.toList());

          return Task.par(getLeadAccountAssociationTasks).map(leadAccountAssociations -> {
            List<LeadAccountAssociation> leadAccountAssociationsForAccounts =
                leadAccountAssociations.stream().flatMap(Collection::stream).collect(Collectors.toList());

            //If count = -1, we will return all results.
            if (count == GET_ALL_COUNT) {
              return PaginatedList.createForPage(leadAccountAssociationsForAccounts, start, count,
                  leadAccountAssociationsForAccounts.size());
            }

            return PaginatedList.extractPage(leadAccountAssociationsForAccounts, start, count);
          });
        });
  }

  /**
   * Get leadAccountAssociations for a given account.
   */
  private Task<List<LeadAccountAssociation>> getLeadAccountAssociationForAccount(PagingContext pagingContext,
      SeatUrn owner, OrganizationUrn orgUrn) {
    return _lssSavedLeadAccountDB.getLeadsAssociatedWithGivenAccount(orgUrn, owner, pagingContext.getStart(),
        pagingContext.getCount())
        .map(pairs -> pairs.stream()
            .map(pair -> createLeadAccountAssociationFromAccountToLeadAssociationView(pair.getSecond(), pair.getFirst(),
                owner, orgUrn))
            .collect(Collectors.toList()))
        .recover(t -> {
          LOG.warn("Failed to get leadAccountAssociations for account: {}, seat: {}", orgUrn, owner, t);
          return Collections.emptyList();
        });
  }

  /**
   * Get lead account associations owned by the given seat with pagination.
   */
  public Task<List<LeadAccountAssociation>> getLeadAccountAssociations(
      @NonNull SeatUrn owner, int start, int count) {
    return _lssSavedLeadAccountDB.getLeadAccountAssociations(owner, start, count)
        .map(pairs -> pairs.stream()
            .map(pair -> createLeadAccountAssociationFromLeadToAccountAssociation(pair.getSecond(),
                pair.getFirst().getMemberUrn(), owner, pair.getFirst().getOrgUrn()))
            .collect(Collectors.toList()))
        .recover(t -> {
          LOG.error("Fail to get leadAccountAssociations for seat: {}", owner, t);
          return Collections.emptyList();
        });
  }

  /**
   * create espresso leadToAccountAssociation from leadToAccountAssociation
   */
  private LeadToAccountAssociation createLeadToAccountAssociationFromLeadAccountAssociation(
      @NonNull LeadAccountAssociation leadAccountAssociation) {
    LeadToAccountAssociation leadToAccountAssociation = new LeadToAccountAssociation();
    leadToAccountAssociation.contractUrn = leadAccountAssociation.getContract().toString();
    leadToAccountAssociation.createdTime =
        leadAccountAssociation.hasChangeAuditStamps() && leadAccountAssociation.getChangeAuditStamps().hasCreated()
            && leadAccountAssociation.getChangeAuditStamps().getCreated().hasTime()
            ? leadAccountAssociation.getChangeAuditStamps().getCreated().getTime() : System.currentTimeMillis();
    leadToAccountAssociation.lastModifiedTime =
        leadAccountAssociation.hasChangeAuditStamps() && leadAccountAssociation.getChangeAuditStamps().hasLastModified()
            && leadAccountAssociation.getChangeAuditStamps().getLastModified().hasTime()
            ? leadAccountAssociation.getChangeAuditStamps().getLastModified().getTime() : System.currentTimeMillis();
    return leadToAccountAssociation;
  }

  /**
   * create espresso leadToAccountAssociation from AccountToLeadAssociationView
   * @param accountToLeadAssociationView
   * @param lead lead's memberUrn
   * @param creator creator's seatUrn
   * @param account account's OrganizationUrn
   */
  private LeadAccountAssociation createLeadAccountAssociationFromAccountToLeadAssociationView(
      @NonNull AccountToLeadAssociationView accountToLeadAssociationView, @NonNull MemberUrn lead,
      @NonNull SeatUrn creator, @NonNull OrganizationUrn account) {
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    ContractUrn contract;
    try {
      contract = ContractUrn.deserialize(accountToLeadAssociationView.contractUrn.toString());
    } catch (URISyntaxException e) {
      LOG.error("Fail to create contractUrn from {}", accountToLeadAssociationView.contractUrn, e);
      return null;
    }
    leadAccountAssociation.setAccount(account);
    leadAccountAssociation.setLead(lead);
    leadAccountAssociation.setContract(contract);
    leadAccountAssociation.setCreator(creator);
    ChangeAuditStamps changeAuditStamps = new ChangeAuditStamps();
    AuditStamp createdAt = new AuditStamp();
    createdAt.setTime(accountToLeadAssociationView.createdTime);
    createdAt.setActor(creator);
    changeAuditStamps.setCreated(createdAt);
    //No usecase will modify leadAccountAssociation, we set createdAt as LastModified
    changeAuditStamps.setLastModified(createdAt);
    leadAccountAssociation.setChangeAuditStamps(changeAuditStamps);
    return leadAccountAssociation;
  }

  /**
   * Helper function create leadAccountAssociation from espresso object LeadToAccountAssociation
   * @param leadToAccountAssociation leadToAccountAssociation to be created
   */
  private LeadAccountAssociation createLeadAccountAssociationFromLeadToAccountAssociation(
      @NonNull LeadToAccountAssociation leadToAccountAssociation, @NonNull MemberUrn lead, @NonNull SeatUrn creator,
      @NonNull OrganizationUrn account) {
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    ContractUrn contract;
    try {
      contract = ContractUrn.deserialize(leadToAccountAssociation.contractUrn.toString());
    } catch (URISyntaxException e) {
      LOG.error("Fail to create contractUrn from {}", leadToAccountAssociation.contractUrn, e);
      return null;
    }
    leadAccountAssociation.setAccount(account);
    leadAccountAssociation.setLead(lead);
    leadAccountAssociation.setContract(contract);
    leadAccountAssociation.setCreator(creator);
    ChangeAuditStamps changeAuditStamps = new ChangeAuditStamps();
    AuditStamp createdAt = new AuditStamp();
    createdAt.setTime(leadToAccountAssociation.createdTime);
    createdAt.setActor(creator);
    changeAuditStamps.setCreated(createdAt);
    //we set createdAt as LastModified
    changeAuditStamps.setLastModified(createdAt);
    leadAccountAssociation.setChangeAuditStamps(changeAuditStamps);
    return leadAccountAssociation;
  }

  @NonNull
  public CompoundKey getCompoundKey(@NonNull LeadAccountAssociation leadAccountAssociation) {
    return new CompoundKey().append(CREATOR_COMPOUND_KEY, leadAccountAssociation.getCreator())
        .append(LEAD_COMPOUND_KEY, leadAccountAssociation.getLead())
        .append(ACCOUNT_COMPOUND_KEY, leadAccountAssociation.getAccount());
  }

  /**
   * Helper function to build CompoundKey from SeatUrn, MemberUrn and OrganizationUrn
   */
  @NonNull
  public CompoundKey buildCompoundKey(@NonNull SeatUrn owner, @NonNull MemberUrn memberUrn, @NonNull OrganizationUrn organizationUrn) {
    return new CompoundKey().append(CREATOR_COMPOUND_KEY, owner)
        .append(LEAD_COMPOUND_KEY, memberUrn)
        .append(ACCOUNT_COMPOUND_KEY, organizationUrn);
  }
}
