package com.linkedin.sales.service.leadaccount;

import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.salesleadaccount.LeadAccountMetadata;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service class for SalesLeadAccountMetadata
 */
public class SalesLeadAccountMetadataService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadAccountMetadataService.class);
  private final LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  public SalesLeadAccountMetadataService(LssSavedLeadAccountDB lssSavedLeadAccountDB) {
    _lssSavedLeadAccountDB = lssSavedLeadAccountDB;
  }

  /**
   * service to return the total number of leads associated with each account for a given seat
   * @param urns set of Organization Urns
   * @param requester the seat Urn of the user in Sales Navigator
   * @return the map between Organization urn and LeadAccountMetadata
   */

  public Task<Map<OrganizationUrn, LeadAccountMetadata>> batchGetAssociatedLeadCounts(
      @NonNull Set<OrganizationUrn> urns, SeatUrn requester) {
    if (urns.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    return _lssSavedLeadAccountDB.getAssociatedLeadCounts(urns, requester)
        .map(leadCountPairs -> leadCountPairs.entrySet()
            .stream()
            .collect(Collectors.toMap(Map.Entry::getKey, e -> new LeadAccountMetadata().setTotalCount(e.getValue()))))
        .onFailure(
            throwable -> LOG.error("Fail to get associated lead number for the OrganizationUrns: {}, with seat: {}",
                urns, requester, throwable)
        );
  }
}
