package com.linkedin.sales.service.leadaccount;

import com.google.common.base.Preconditions;
import com.linkedin.common.AddressArray;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.PhoneNumber;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.Address;
import com.linkedin.sales.espresso.Email;
import com.linkedin.sales.espresso.LeadEditableContactInfo;
import com.linkedin.sales.espresso.SocialHandle;
import com.linkedin.sales.espresso.Website;
import com.linkedin.sales.espresso.TypedPhoneNumber;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfo;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfoKey;
import com.linkedin.salesleadaccount.SocialHandleArray;
import com.linkedin.salesleadaccount.TypedPhoneNumberArray;
import com.linkedin.salesleadaccount.WebsiteArray;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.eclipse.jgit.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.*;

/**
 * Service to get or upsert lead editabe contact info
 */
public class SalesLeadEditableContactInfoService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadEditableContactInfoService.class);
  private final LssLeadExtendedInfoDB _lssLeadExtendedInfoDB;

  public SalesLeadEditableContactInfoService(LssLeadExtendedInfoDB lssLeadExtendedInfoDB) {
    _lssLeadExtendedInfoDB = lssLeadExtendedInfoDB;
  }

  /**
   * Get lead editable contact info for given salesLeadEditableContactInfo key
   * @param salesLeadEditableContactInfoKey compoundKey including memberUrn of sales lead and the contractUrn of the creator
   * @return salesLeadEditableContactInfo
   */
  public Task<SalesLeadEditableContactInfo> getLeadEditableContactInfo(
      @NonNull SalesLeadEditableContactInfoKey salesLeadEditableContactInfoKey) {
    return _lssLeadExtendedInfoDB.getLeadEditableContactInfo(salesLeadEditableContactInfoKey)
        .map(leadEditableContactInfo -> convertToSalesLeadEditableContactInfo(leadEditableContactInfo,
            salesLeadEditableContactInfoKey))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.value(
                //To avoid increase the number of results with 4XX return status, getLeadEditableContactInfo will return
                //an dummy object by using dummy auditStamp , as it is not meaningful to clients
                new SalesLeadEditableContactInfo().setContract(salesLeadEditableContactInfoKey.getContract())
                    .setCreated(
                        new AuditStamp().setActor(new SeatUrn(DEFAULT_SEAT_ID)).setTime(System.currentTimeMillis()))
                    .setLastModified(
                        new AuditStamp().setActor(new SeatUrn(DEFAULT_SEAT_ID)).setTime(System.currentTimeMillis()))
                    .setMember(salesLeadEditableContactInfoKey.getMember()));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Upsert saleLeadEditableContactInfo
   * @param key salesLeadEditableContactInfoKey
   * @param salesLeadEditableContactInfo content that need to be updated or created
   * @return update response
   */
  public Task<UpdateResponse> upsertLeadEditableInfo(@NonNull SalesLeadEditableContactInfoKey key,
      @NonNull SalesLeadEditableContactInfo salesLeadEditableContactInfo) {
    Preconditions.checkArgument(key.getContract().equals(salesLeadEditableContactInfo.getContract()) && key.getMember()
        .equals(salesLeadEditableContactInfo.getMember()), String.format(
        "Not allowed to execute upsert the leadEditableContactInfo %s since contractUrn "
            + "and memberUrn within salesLeadEditableContactInfo can't match to the salesLeadEditableContactInfoKey %s",
        salesLeadEditableContactInfo, key));

    //LSS-59950 If all LeadEditableContactInfo fields are empty, we should delete the whole record in DB
    if ((!salesLeadEditableContactInfo.hasAddresses() || salesLeadEditableContactInfo.getAddresses().isEmpty())
        && (!salesLeadEditableContactInfo.hasEmails() || salesLeadEditableContactInfo.getEmails().isEmpty())
        && (!salesLeadEditableContactInfo.hasSocialHandles() || salesLeadEditableContactInfo.getSocialHandles().isEmpty())
        && (!salesLeadEditableContactInfo.hasPhoneNumbers() || salesLeadEditableContactInfo.getPhoneNumbers().isEmpty())
        && (!salesLeadEditableContactInfo.hasWebsites() || salesLeadEditableContactInfo.getWebsites().isEmpty())
    ) {
      return _lssLeadExtendedInfoDB.deleteEditableContactInfo(key)
          .map(httpStatus ->
            new UpdateResponse(HttpStatus.S_200_OK)
          ).recover(throwable -> {
            throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "Failed to delete (empty upsert) the leadEditableContactInfo by key: " + key, throwable);
          });
    } else {
      return getLeadEditableContactInfo(key).flatMap(salesLeadEditableContactInfoReadFromEspresso -> {
        AuditStamp created = salesLeadEditableContactInfo.getCreated();
        //If the key for leadEditableContactInfo already exists, we won't update the creator seatUrn of this record.
        if (salesLeadEditableContactInfoReadFromEspresso.getCreated().hasActor()
            && salesLeadEditableContactInfoReadFromEspresso.getCreated().getActor().getIdAsLong() != DEFAULT_SEAT_ID) {
          created = salesLeadEditableContactInfoReadFromEspresso.getCreated();
        }
        LeadEditableContactInfo leadEditableContactInfoToUpdate = convertToEspressoLeadEditableContactInfo(salesLeadEditableContactInfo, created);
        return _lssLeadExtendedInfoDB.upsertLeadEditableContactInfo(key, leadEditableContactInfoToUpdate).map(UpdateResponse::new);
      }).recover(throwable -> {
        throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
            "Failed to upsert the leadEditableContactInfo by key: " + key, throwable);
      });
    }
  }

  // convert sales leadEditableContactInfo object to espresso object
  private LeadEditableContactInfo convertToEspressoLeadEditableContactInfo(
      @NonNull SalesLeadEditableContactInfo salesLeadEditableContactInfo, @NonNull AuditStamp created) {
    LeadEditableContactInfo leadEditableContactInfo = new LeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = created.getActor().toString();
    leadEditableContactInfo.createdTime = created.getTime();
    leadEditableContactInfo.lastModifiedBySeatUrn =
        salesLeadEditableContactInfo.getLastModified().getActor().toString();
    leadEditableContactInfo.lastModifiedTime = salesLeadEditableContactInfo.getLastModified().getTime();
    if (salesLeadEditableContactInfo.hasWebsites()) {
      leadEditableContactInfo.websites = salesLeadEditableContactInfo.getWebsites().stream().map(res -> {
        Website website = new Website();
        website.category = WEBSITE_CATEGORY_SERVICE_TO_ESPRESSO_MAPPING.get(res.getCategory());
        website.url = res.getUrl();
        return website;
      }).collect(Collectors.toList());
    } else {
      //the espresso default value of websites is empty list
      leadEditableContactInfo.websites = Collections.emptyList();
    }
    if (salesLeadEditableContactInfo.hasSocialHandles()) {
      leadEditableContactInfo.socialHandles = salesLeadEditableContactInfo.getSocialHandles().stream().map(res -> {
        SocialHandle socialHandle = new SocialHandle();
        socialHandle.provider = SOCIAL_HANDLE_SERVICE_TO_ESPRESSO_MAPPING.get(res.getProvider());
        socialHandle.username = res.getUsername();
        return socialHandle;
      }).collect(Collectors.toList());
    } else {
      //the default value of socialHandles is empty list
      leadEditableContactInfo.socialHandles = Collections.emptyList();
    }
    if (salesLeadEditableContactInfo.hasEmails()) {
      leadEditableContactInfo.emails = salesLeadEditableContactInfo.getEmails().stream().map(res -> {
        Email email = new Email();
        email.email = res;
        return email;
      }).collect(Collectors.toList());
    } else {
      //the espress default value of emails is empty list
      leadEditableContactInfo.emails = Collections.emptyList();
    }
    if (salesLeadEditableContactInfo.hasPhoneNumbers()) {
      leadEditableContactInfo.typedPhoneNumbers = salesLeadEditableContactInfo.getPhoneNumbers().stream().map(res -> {
        TypedPhoneNumber phoneNumber = new TypedPhoneNumber();
        phoneNumber.number = res.getNumber().getNumber();
        phoneNumber.type = PHONE_NUMNER_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(res.getType());
        return phoneNumber;
      }).collect(Collectors.toList());
    } else {
      //The espresso default value of typedPhoneNumbers is empty list
      leadEditableContactInfo.typedPhoneNumbers = Collections.emptyList();
    }
    if (salesLeadEditableContactInfo.hasAddresses()) {
      leadEditableContactInfo.addresses = salesLeadEditableContactInfo.getAddresses().stream().map(res -> {
        Address address = new Address();
        address.fullAddress = res.getLine1();
        return address;
      }).collect(Collectors.toList());
    } else {
      //the espresso default value of addresses is empty list
      leadEditableContactInfo.addresses = Collections.emptyList();
    }
    return leadEditableContactInfo;
  }

  //convert espresso leadEditableContactInfo to salesLeadEditableContactInfo
  private SalesLeadEditableContactInfo convertToSalesLeadEditableContactInfo(
      @NonNull LeadEditableContactInfo leadEditableContactInfo, @NonNull SalesLeadEditableContactInfoKey key) {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = new SalesLeadEditableContactInfo();
    AddressArray addresses = convertToSalesAddressArray(leadEditableContactInfo.addresses);
    StringArray emails = null;
    if (leadEditableContactInfo.emails != null) {
      emails = new StringArray(leadEditableContactInfo.emails
          .stream().map(res -> res.email.toString()).collect(Collectors.toList()));
    }
    SeatUrn lastModifiedSeatUrn = null;
    SeatUrn creatorSeatUrn = null;
    try {
      lastModifiedSeatUrn = SeatUrn.deserialize(leadEditableContactInfo.lastModifiedBySeatUrn.toString());
      creatorSeatUrn = SeatUrn.deserialize(leadEditableContactInfo.creatorSeatUrn.toString());
    } catch (URISyntaxException e) {
      LOG.error("Unable to deserialize seatUrn from espresso seatUrn {}, {}", lastModifiedSeatUrn, creatorSeatUrn);
    }
    AuditStamp createAuditStamp =
        new AuditStamp().setActor(creatorSeatUrn, SetMode.IGNORE_NULL).setTime(leadEditableContactInfo.createdTime);

    AuditStamp lastModifiedAuditStamp = new AuditStamp().setActor(lastModifiedSeatUrn, SetMode.IGNORE_NULL)
        .setTime(leadEditableContactInfo.lastModifiedTime);
    TypedPhoneNumberArray phoneNumbers = null;
    if (leadEditableContactInfo.typedPhoneNumbers != null) {
      phoneNumbers = new TypedPhoneNumberArray(leadEditableContactInfo.typedPhoneNumbers.stream()
          .map(res -> new com.linkedin.salesleadaccount.TypedPhoneNumber()
              .setNumber(new PhoneNumber().setNumber(res.number.toString()))
              .setType(PHONE_NUMNER_TYPE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(res.type)))
          .collect(Collectors.toList()));
    }
    SocialHandleArray socialHandles = null;
    if (leadEditableContactInfo.socialHandles != null) {
      socialHandles = new SocialHandleArray(leadEditableContactInfo.socialHandles.stream()
          .map(res ->
              new com.linkedin.salesleadaccount.SocialHandle()
                  .setUsername(res.username.toString())
                  .setProvider(SOCIAL_HANDLE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(res.provider)))
          .collect(Collectors.toList()));
    }
    WebsiteArray websites = null;
    if (leadEditableContactInfo.websites != null) {
      websites = new WebsiteArray(leadEditableContactInfo.websites.stream()
          .map(res ->
              new com.linkedin.salesleadaccount.Website()
                  .setCategory(WEBSITE_CATEGORY_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(res.category))
                  .setUrl(res.url.toString()))
          .collect(Collectors.toList()));
    }
    salesLeadEditableContactInfo.setAddresses(addresses, SetMode.IGNORE_NULL);
    salesLeadEditableContactInfo.setEmails(emails, SetMode.IGNORE_NULL);
    salesLeadEditableContactInfo.setPhoneNumbers(phoneNumbers, SetMode.IGNORE_NULL);
    salesLeadEditableContactInfo.setSocialHandles(socialHandles, SetMode.IGNORE_NULL);
    salesLeadEditableContactInfo.setWebsites(websites, SetMode.IGNORE_NULL);
    salesLeadEditableContactInfo.setContract(key.getContract());
    salesLeadEditableContactInfo.setMember(key.getMember());
    salesLeadEditableContactInfo.setCreated(createAuditStamp);
    salesLeadEditableContactInfo.setLastModified(lastModifiedAuditStamp);
    return salesLeadEditableContactInfo;
  }

  // convert espresso address array to sales address array
  @Nullable
  private AddressArray convertToSalesAddressArray(@NonNull List<Address> espressoAddresses) {
    if (espressoAddresses.isEmpty()) {
      return null;
    }
    AddressArray addresses = new AddressArray(espressoAddresses.stream().map(espressoAddresse -> {
      com.linkedin.common.Address salesAddress = new com.linkedin.common.Address();
      salesAddress.setLine1(espressoAddresse.fullAddress.toString(), SetMode.IGNORE_NULL);
      return salesAddress;
    }).collect(Collectors.toList()));
    return addresses.size() == 0 ? null : addresses;
  }
}
