package com.linkedin.sales.service.leadaccount;

import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.espresso.LeadProfileUnlockInfo;
import com.linkedin.salesleadaccount.SalesLeadProfileUnlockInfo;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Service to get or create leadProfileUnlockInfo
 */
public class SalesLeadProfileUnlockInfoService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadProfileUnlockInfoService.class);
  private final LssLeadExtendedInfoDB _lssLeadExtendedInfoDB;

  public SalesLeadProfileUnlockInfoService(LssLeadExtendedInfoDB lssLeadExtendedInfoDB) {
    _lssLeadExtendedInfoDB = lssLeadExtendedInfoDB;
  }

  /**
   * Service method to create leadProfileUnlockInfo. I the key already exists, it will not update the existing record
   * @param leadProfileUnlockInfo content of the the the profile unlock info
   * @return create response to tell if the leadProfileUnlockInfo is created successfully.
   */
  public Task<Pair<CompoundKey, CreateResponse>> createLeadProfileUnlockInfo(
      @NonNull SalesLeadProfileUnlockInfo leadProfileUnlockInfo) {
    com.linkedin.sales.espresso.LeadProfileUnlockInfo espressoLeadProfileUnlockInfo = new LeadProfileUnlockInfo();
    espressoLeadProfileUnlockInfo.unlockedTime = System.currentTimeMillis();
    espressoLeadProfileUnlockInfo.unlockedBySeatUrn = leadProfileUnlockInfo.getUnlockedAt().getActor().toString();
    ContractUrn contractUrn = leadProfileUnlockInfo.getContract();
    MemberUrn memberUrn = leadProfileUnlockInfo.getMember();
    CompoundKey compoundKey = getCompoundKey(leadProfileUnlockInfo);
    return _lssLeadExtendedInfoDB.createLeadProfileUnlockInfo(memberUrn, contractUrn, espressoLeadProfileUnlockInfo)
        .map(response -> {
          if (response == HttpStatus.S_200_OK) {
            LOG.info("Same entity has been created {}", compoundKey);
          }
          return new Pair<>(compoundKey, new CreateResponse(compoundKey, response));
        })
        .recover(throwable -> {
          CompoundKey key = getCompoundKey(leadProfileUnlockInfo);
          return new Pair<>(key, new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
              "Failed to create the lead profile unlock info " + compoundKey)));
        });
  }

  /**
   * Batch get leadProfileUnlockInfo
   * @param compoundKeys keys to batch get lead profile unlock info
   * @return compoundKey to profile unlock info map,
   * the returning map only contain the key for which sales lead profile unlock info is found.
   */
  public Task<Map<CompoundKey, SalesLeadProfileUnlockInfo>> batchGetLeadProfileUnlockInfo(
      @NonNull Set<CompoundKey> compoundKeys) {
    List<Task<AbstractMap.SimpleEntry<CompoundKey, SalesLeadProfileUnlockInfo>>> tasks =
        compoundKeys.stream().map(compoundKey -> getLeadProfileUnlockInfo(compoundKey).map(response -> {
          if (response.isPresent()) {
            return new AbstractMap.SimpleEntry<>(compoundKey, response.get());
          } else {
            return null;
          }
        })).collect(Collectors.toList());
    return Task.par(tasks)
        .map(responses -> responses.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /**
   * Get SalesLeadProfileUnlockInfo for given compoundKey
   * @param compoundKey
   * @return salesLeadProfileUnlock info if found. Return Optional.empty() if no record be found
   */
  private Task<Optional<SalesLeadProfileUnlockInfo>> getLeadProfileUnlockInfo(@NonNull CompoundKey compoundKey) {
    MemberUrn memberUrn = (MemberUrn) compoundKey.getPart(MEMBER_COMPOUND_KEY);
    ContractUrn contractUrn = (ContractUrn) compoundKey.getPart(CONTRACT_COMPOUND_KEY);
    return _lssLeadExtendedInfoDB.getLeadProfileUnlockInfo(memberUrn, contractUrn)
        .map(leadProfileUnlockInfo -> Optional.of(
            createSalesLeadProfileUnlockInfo(leadProfileUnlockInfo, memberUrn, contractUrn)))
        .recover(t -> Optional.empty());
  }

  /**
   * Helper function to get CompoundKey
   * @param leadProfileUnlockInfo
   * @return CompoundKey
   */
  @NonNull
  private CompoundKey getCompoundKey(@NonNull SalesLeadProfileUnlockInfo leadProfileUnlockInfo) {
    return new CompoundKey().append(MEMBER_COMPOUND_KEY, leadProfileUnlockInfo.getMember())
        .append(CONTRACT_COMPOUND_KEY, leadProfileUnlockInfo.getContract());
  }

  // create sale leadEditableContactInfo record from espresso object
  @NonNull
  private SalesLeadProfileUnlockInfo createSalesLeadProfileUnlockInfo(
      @NonNull LeadProfileUnlockInfo espressoLeadProfileUnlockInfo, @NonNull MemberUrn memberUrn,
      @NonNull ContractUrn contractUrn) {
    SalesLeadProfileUnlockInfo salesLeadProfileUnlockInfo = new SalesLeadProfileUnlockInfo();
    SeatUrn seatUrn;
    try {
      seatUrn = SeatUrn.deserialize(espressoLeadProfileUnlockInfo.unlockedBySeatUrn.toString());
    } catch (URISyntaxException e) {
      LOG.warn("Fail to deserialize seatUrn from {}", espressoLeadProfileUnlockInfo.unlockedBySeatUrn, e);
      //seatUrn is not an necessary field for salesProfileUnlockInfo.
      //if seatUrn deserialize failed from espresso data, we will return the default seatUrn.
      seatUrn = new SeatUrn(DEFAULT_SEAT_ID);
    }
    salesLeadProfileUnlockInfo.setContract(contractUrn);
    salesLeadProfileUnlockInfo.setMember(memberUrn);
    AuditStamp createdAtAuditStamp = new AuditStamp();
    createdAtAuditStamp.setTime(espressoLeadProfileUnlockInfo.unlockedTime);
    createdAtAuditStamp.setActor(seatUrn);
    salesLeadProfileUnlockInfo.setUnlockedAt(createdAtAuditStamp);
    return salesLeadProfileUnlockInfo;
  }
}
