package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.Lists;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.ChangeAuditStamps;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.lss.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.espresso.SavedLead;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.SalesEntitiesBatchUtils;
import com.linkedin.salesleadaccount.SalesLead;
import com.linkedin.util.Pair;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Service for CRUD of sales lead
 * <AUTHOR> on 10/25/2019
 */
public class SavedLeadService {
  private static final Logger LOG = LoggerFactory.getLogger(SavedLeadService.class);
  private static final int BATCH_CREATE_ENTITY_SIZE = 100;
  //Max saved lead limits
  private final Integer _maxLeadLimitAllTiers;
  private final LssSavedLeadAccountDB _lssSavedLeadAccountDB;
  private final LixService _lixService;

  public SavedLeadService(LssSavedLeadAccountDB lssSavedLeadAccountDB, LixService lixService,
      Integer maxLeadLimitAllTiers) {
    _lssSavedLeadAccountDB = lssSavedLeadAccountDB;
    _lixService = lixService;
    _maxLeadLimitAllTiers = maxLeadLimitAllTiers;
  }

  /**
   * Create a saved lead
   * @param salesLead salesLead to be created
   * @return CreateResponse with status 201 for success, 200 for key conflict, 500 for other types of exception.
   * It throws 412 exception for exceeding lead limit.
   */
  public Task<CreateResponse> createSavedLead(SalesLead salesLead) {
    SeatUrn ownerSeatUrn = salesLead.getOwner();
    Task<Integer> totalSavedLeadCountTask = _lssSavedLeadAccountDB.getSavedLeadCountForSeat(ownerSeatUrn);
    CompoundKey compoundKey = getSavedLeadCompoundKey(salesLead);
    Task<SalesLead> getSalesLeadTask = getSalesLead(compoundKey).recover(t -> null);

    return Task.par(totalSavedLeadCountTask, getSalesLeadTask)
        .flatMap((totalSavedLeadCount, foundSalesLead) -> {
          if (foundSalesLead != null) {
            return Task.value(new CreateResponse(compoundKey, HttpStatus.S_200_OK));
          }
          if (totalSavedLeadCount + 1 > _maxLeadLimitAllTiers) {
            return Task.failure(
                new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded lead limit"));
          }

          MemberUrn leadMemberUrn = salesLead.getMember();
          SavedLead espressoSavedLead = buildSavedLeadFromSalesLead(salesLead);

          return _lssSavedLeadAccountDB.createSavedLead(ownerSeatUrn, leadMemberUrn, espressoSavedLead)
              .map(httpStatus -> {
                switch (httpStatus) {
                  case S_201_CREATED:
                  case S_200_OK:
                    return new CreateResponse(compoundKey, httpStatus);
                  default:
                    return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
                }
              })
              .recover(throwable -> {
                LOG.error("Failed to create saved lead {}.", salesLead, throwable);
                return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
              });
        });
  }

  /**
   * Create multiple saved leads
   * @param salesLeads a set of salesLeads to be created
   * @return a map between each compound key of salesLead to its createResponse.
   *         Each createResponse has status 201 for success, 200 for key conflict, 500 for other types of exception.
   *         It throws 400 exception for non identity owner, 412 exception for exceeding lead limit.
   */
  public Task<Map<CompoundKey, CreateResponse>> batchCreateSavedLeads(@NonNull Set<SalesLead> salesLeads) {
    if (salesLeads.isEmpty()) {
      return Task.value(new HashMap<>());
    }

    if (salesLeads.stream().map(SalesLead::getOwner).distinct().count() > 1) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Owner is not identical"));
    }

    SalesLead firstSalesLead = salesLeads.iterator().next();
    SeatUrn ownerSeatUrn = firstSalesLead.getOwner();
    Task<Integer> totalSavedLeadCountTask = _lssSavedLeadAccountDB.getSavedLeadCountForSeat(ownerSeatUrn);
    Set<CompoundKey> keys = salesLeads.stream().map(this::getSavedLeadCompoundKey).collect(Collectors.toSet());
    Task<Map<CompoundKey, SalesLead>> batchGetSalesLeadsTask = batchGetSalesLeads(keys);

    return Task.par(totalSavedLeadCountTask, batchGetSalesLeadsTask)
        .flatMap((totalSavedLeadCount, salesLeadMap) -> {

          Map<CompoundKey, CreateResponse> savedLeadResultMap = new HashMap<>();
          // response of batchGetSalesLeads only includes entries with sales lead found
          salesLeadMap.keySet()
              .forEach(key -> savedLeadResultMap.put(key, new CreateResponse(key, HttpStatus.S_200_OK)));

          List<SalesLead> unsavedSalesLeads = salesLeads.stream()
              .filter(salesLead -> !salesLeadMap.containsKey(getSavedLeadCompoundKey(salesLead)))
              .collect(Collectors.toList());
          if (unsavedSalesLeads.isEmpty()) {
            return Task.value(savedLeadResultMap);
          }

          if (totalSavedLeadCount + unsavedSalesLeads.size() > _maxLeadLimitAllTiers) {
            return Task.failure(
                new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded lead limit"));
          }

          List<List<SalesLead>> salesLeadBatches = Lists.partition(unsavedSalesLeads, BATCH_CREATE_ENTITY_SIZE);

          return _lixService.getEntityBatchCreateConcurrencyLevel(ownerSeatUrn,
              LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL)
              .flatMap(batchCreateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(salesLeadBatches,
                  this::createSavedLeadsHelper, batchCreateConcurrencyLevel).map(nestedCreateLeadsResultLists -> {
                Map<CompoundKey, CreateResponse> resultMap = nestedCreateLeadsResultLists.stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                resultMap.putAll(savedLeadResultMap);
                return resultMap;
              }))
              .recover(t -> {
                LOG.error("Failed to create the savedLeads for seat: {}", ownerSeatUrn, t);
                return salesLeads.stream().map(salesLead -> {
                  CompoundKey compoundKey = getSavedLeadCompoundKey(salesLead);
                  return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                      new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                          "Failed to create the savedLead " + compoundKey)));
                }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
              });
        });
  }

  /**
   * Helper function to create saved leads
   * @param salesLeads the saved leads to be created
   * @return list of entries of CompoundKey to CreateResponse map
   */
  private Task<List<AbstractMap.SimpleEntry<CompoundKey, CreateResponse>>> createSavedLeadsHelper(
      @NonNull List<SalesLead> salesLeads) {
    if (salesLeads.isEmpty()) {
      return Task.value(Collections.emptyList());
    }

    Map<MemberUrn, SavedLead> memberUrnSavedLeadMap = salesLeads.stream()
        .map(salesLead -> new AbstractMap.SimpleEntry<>(salesLead.getMember(), buildSavedLeadFromSalesLead(salesLead)))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    SeatUrn owner = salesLeads.get(0).getOwner();

    return _lssSavedLeadAccountDB.createSavedLeads(owner, memberUrnSavedLeadMap)
        .map(responseMap -> responseMap.entrySet().stream().map(entry -> {
          MemberUrn memberUrn = entry.getKey();
          CompoundKey compoundKey = buildCompoundKey(owner, memberUrn);

          if (entry.getValue() == HttpStatus.S_201_CREATED || entry.getValue() == HttpStatus.S_200_OK) {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(compoundKey, entry.getValue()));
          } else {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    "Failed to create the savedLead " + compoundKey)));
          }
        }).collect(Collectors.toList()))
        .recover(t -> {
          LOG.error("Failed to create the savedLeads for seat: {}", owner, t);
          return salesLeads.stream()
              .map(salesLead -> new AbstractMap.SimpleEntry<>(getSavedLeadCompoundKey(salesLead), new CreateResponse(
                  new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                      "Failed to create the savedLead " + getSavedLeadCompoundKey(salesLead)))))
              .collect(Collectors.toList());
        });
  }

  /**
   * Delete a saved lead
   * @param compoundKey compound key of the saved lead to be deleted
   * @return UpdateResponse with status 204 for success, 200 for key nonexistence, 400 for invalid input and 500 for any other exception.
   */
  public Task<UpdateResponse> deleteSavedLead(CompoundKey compoundKey) {
    Object ownerSeatUrnObj = compoundKey.getPart(OWNER_COMPOUND_KEY);
    Object leadMemberUrnObj = compoundKey.getPart(MEMBER_COMPOUND_KEY);
    SeatUrn ownerSeatUrn;
    MemberUrn leadMemberUrn;
    try {
      ownerSeatUrn = SeatUrn.deserialize(ownerSeatUrnObj.toString());
      leadMemberUrn = MemberUrn.deserialize(leadMemberUrnObj.toString());
    } catch (URISyntaxException | NullPointerException e) {
      LOG.warn(
          "The owner seat urn or the lead member urn provided in the compoundKey {} is not valid for lead deletion ",
          compoundKey);
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }

    return _lssSavedLeadAccountDB.deleteSavedLead(ownerSeatUrn, leadMemberUrn).map(httpStatus -> {
      switch (httpStatus) {
        case S_204_NO_CONTENT:
        case S_200_OK:
          return new UpdateResponse(httpStatus);
        default:
          return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
      }
    }).recover(throwable -> {
      LOG.error("Failed to delete saved lead with key {}.", compoundKey, throwable);
      return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    });
  }

  /**
   * Delete multiple saved leads
   * @param compoundKeys compound keys of the saved leads to be deleted
   * @return a map between each compound key of sales lead to its UpdateResponse.
   *         Each UpdateResponse has status 204 for success, 200 for key nonexistence, 400 for invalid input and 500 for any other exception.
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeleteSavedLeads(@NonNull Set<CompoundKey> compoundKeys) {
    return SalesEntitiesBatchUtils.batchDeleteEntities(compoundKeys, compoundKey -> deleteSavedLead(compoundKey).map(
        response -> new AbstractMap.SimpleEntry<>(compoundKey, response)), _lixService);
  }

  /**
   * Get sales leads for give owner with pagination
   * @param owner owner's seatUrn
   * @param start paging start
   * @param count paging count
   * @return paginatedList of sales leads
   */
  public Task<PaginatedList<SalesLead>> getSalesLeadsForGivenOwner(@NonNull SeatUrn owner, int start, int count) {
    return _lssSavedLeadAccountDB.getSavedLeadCountForSeat(owner).flatMap(totalCount -> {
      // Get total count only if the passed in count param is 0
      if (count == 0) {
        return Task.value(PaginatedList.createForPage(Collections.<SalesLead>emptyList(), start, count, totalCount));
      }
      return _lssSavedLeadAccountDB.getSavedLeads(owner, null, start, count).map(resultList -> {
        List<SalesLead> results = resultList.stream()
            .map(pair -> buildSalesLeadFromSavedLead(pair.getSecond(), pair.getFirst(), owner))
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        return PaginatedList.createForPage(results, start, count, totalCount);
      });
    }).recover(t -> {
      LOG.warn("Fail to get saved leads for seat: {}", owner, t);
      return PaginatedList.createForPage(Collections.emptyList(), start, count, 0);
    });
  }

  /**
   * Get sales lead for given compoundKey
   * @param compoundKey compoundKey that contains owner seat urn and the member urn as strings
   * @return salesLead
   */
  public Task<SalesLead> getSalesLead(@NonNull CompoundKey compoundKey) {
    Pair<SeatUrn, MemberUrn> keyPair = getEspressoKeyPartsFromCompoundKey(compoundKey);
    if (keyPair == null) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatUrn ownerSeatUrn = keyPair.getFirst();
    MemberUrn leadMemberUrn = keyPair.getSecond();

    return _lssSavedLeadAccountDB.getSavedLeads(ownerSeatUrn, leadMemberUrn, DEFAULT_START, DEFAULT_COUNT)
        .flatMap(list -> {
          if (list.isEmpty()) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
          }
          SavedLead savedLead = list.get(0).getSecond();
          SalesLead salesLead = buildSalesLeadFromSavedLead(savedLead, leadMemberUrn, ownerSeatUrn);
          if (salesLead == null) {
            LOG.error("Failed to build salesLead from savedLead {}", savedLead);
            return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
          }
          return Task.value(salesLead);
        });
  }

  /**
   * Batch get sales leads for given compoundKey set
   * @param compoundKeys set of compoundKeys that contains owner seat urn and the member urn as strings
   * @return compound key to sales lead mapping
   */
  public Task<Map<CompoundKey, SalesLead>> batchGetSalesLeads(@NonNull Set<CompoundKey> compoundKeys) {
    List<Task<AbstractMap.SimpleEntry<CompoundKey, SalesLead>>> getSalesLeadTasks = compoundKeys.stream()
        .map(key -> getSalesLead(key).map(
            salesLead -> new AbstractMap.SimpleEntry<>(key, salesLead)).recover(t -> null))
        .collect(Collectors.toList());

    return Task.par(getSalesLeadTasks)
        .map(salesLeadList -> salesLeadList.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
        .onFailure(t -> LOG.error("Failed to batch get salesLeads for keys: {}", compoundKeys, t));
  }

  /**
   * Get sales lead compound key for a given sales lead
   * @return compound key of the sales lead
   */
  public CompoundKey getSavedLeadCompoundKey(@NonNull SalesLead salesLead) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, salesLead.getOwner())
        .append(MEMBER_COMPOUND_KEY, salesLead.getMember());
  }

  /**
   * Build SalesLead object from Espresso saveLead document and key
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "LEAD_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get value is not null")
  private SalesLead buildSalesLeadFromSavedLead(
      @NonNull SavedLead savedLead, @NonNull MemberUrn leadMemberUrn,
      @NonNull SeatUrn ownerSeatUrn
  ) {
    SalesLead salesLead = new SalesLead();
    ContractUrn contractUrn;
    try {
      contractUrn = ContractUrn.deserialize(savedLead.contractUrn.toString());
    } catch (URISyntaxException e) {
      LOG.warn("Fail to create contractUrn from {}", savedLead.contractUrn, e);
      return null;
    }
    ChangeAuditStamps changeAuditStamps = new ChangeAuditStamps();
    AuditStamp createdAtAuditStamp = new AuditStamp();
    createdAtAuditStamp.setTime(savedLead.createdTime);
    createdAtAuditStamp.setActor(ownerSeatUrn);
    changeAuditStamps.setCreated(createdAtAuditStamp);
    // changeAuditStamps field of salesLead cannot be modified after created, set LastModified same as Created
    changeAuditStamps.setLastModified(createdAtAuditStamp);

    salesLead.setOwner(ownerSeatUrn);
    salesLead.setMember(leadMemberUrn);
    salesLead.setContract(contractUrn);
    salesLead.setChangeAuditStamps(changeAuditStamps);
    if (savedLead.dataSource != null) {
      checkArgument(LEAD_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().containsKey(savedLead.dataSource),
          String.format("Unsupported saved lead data source [%s]", savedLead.dataSource));
      salesLead.setDataSource(LEAD_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(savedLead.dataSource));
    }

    return salesLead;
  }

  /**
   * Build Espresso SavedLead object from salesLead
   */
  private SavedLead buildSavedLeadFromSalesLead(@NonNull SalesLead salesLead) {
    SavedLead espressoSavedLead = new SavedLead();
    espressoSavedLead.contractUrn = salesLead.getContract().toString();
    // If dataSource field is not present, default value USER_GENERATED would be fetched
    espressoSavedLead.dataSource =
        LEAD_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.get(salesLead.getDataSource(GetMode.DEFAULT));
    espressoSavedLead.createdTime = salesLead.hasChangeAuditStamps() && salesLead.getChangeAuditStamps().hasCreated()
        && salesLead.getChangeAuditStamps().getCreated().hasTime() ? salesLead.getChangeAuditStamps()
        .getCreated()
        .getTime() : System.currentTimeMillis();
    return espressoSavedLead;
  }

  /**
   * Helper function to get CompoundKey
   */
  @NonNull
  public CompoundKey buildCompoundKey(@NonNull SeatUrn owner, @NonNull MemberUrn memberUrn) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, owner)
        .append(MEMBER_COMPOUND_KEY, memberUrn);
  }

  /**
   * Helper function to check if input compoundKey is valid and get key parts
   * @param compoundKey compoundKey that contains owner seat urn and the member urn as strings
   * @return a pair of owner seatUrn and lead memberUrn. Return null if the compoundKey is invalid
   */
  private Pair<SeatUrn, MemberUrn> getEspressoKeyPartsFromCompoundKey(@NonNull CompoundKey compoundKey) {
    Object ownerSeatUrnObj = compoundKey.getPart(OWNER_COMPOUND_KEY);
    Object leadMemberUrnObj = compoundKey.getPart(MEMBER_COMPOUND_KEY);
    try {
      SeatUrn ownerSeatUrn = SeatUrn.deserialize(ownerSeatUrnObj.toString());
      MemberUrn leadMemberUrn = MemberUrn.deserialize(leadMemberUrnObj.toString());
      return new Pair<>(ownerSeatUrn, leadMemberUrn);
    } catch (URISyntaxException | NullPointerException e) {
      LOG.warn(
          "The owner seat urn or the lead member urn provided in the compoundKey {} is not valid for lead deletion ",
          compoundKey);
      return null;
    }
  }

  /**
   * Returns max saved lead limit for all Sales Navigator Tiers
   * @return max saved lead limit
   */
  public int getMaxSavedLeadLimitAllTiers() {
    return _maxLeadLimitAllTiers;
  }
}
