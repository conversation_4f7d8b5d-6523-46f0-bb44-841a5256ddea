package com.linkedin.sales.service.list;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.AccountToListMapping;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Service for handling the mapping between an account map and the list created to represent it.
 */
public class SalesAccountToListMappingService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesAccountToListMappingService.class);

  private final LssListDB _lssListDB;

  public SalesAccountToListMappingService(LssListDB lssListDB) {
    _lssListDB = lssListDB;
  }

  /**
   * Create an accountToListMapping.  This mapping is used to relate a custom list that was created to
   * represent an account map to an account.
   *
   * @param accountToListMapping accountToListMapping to be created.
   * @return compoundKey to createResponse pair
   */
  public Task<CreateResponse> createAccountToListMapping(AccountToListMapping accountToListMapping) {
    SalesListUrn list = accountToListMapping.getList();
    OrganizationUrn account = accountToListMapping.getAccount();
    SeatUrn owner = accountToListMapping.getOwner();

    CompoundKey compoundKey = getCompoundKey(accountToListMapping);

    //If create successfully, DB will return status 201, service level will return 201.
    //If resource already exists, DB will return status 412, service level will return 200.
    return _lssListDB.createAccountToListMapping(owner, account, list.getIdAsLong(),
        createEspressoAccountToListMappingFromService(accountToListMapping)).map(createResponse -> {
      if (createResponse == HttpStatus.S_201_CREATED) {
        return new CreateResponse(compoundKey, HttpStatus.S_201_CREATED);
      } else {
        return new CreateResponse(compoundKey, HttpStatus.S_200_OK);
      }
    }).onFailure(throwable -> LOG.error("Failed to create the accountToListMapping " + compoundKey, throwable));
  }

  /**
   * Get accountToListMapping's for a given account.
   *
   * @param owner owner's seatUrn
   * @param account account's OrganizationUrn
   * @param start paging start
   * @param count paging count
   */
  public Task<PaginatedList<AccountToListMapping>> getAccountToListMappingForAccount(SeatUrn owner,
      OrganizationUrn account, int start, int count) {
    Task<List<AccountToListMapping>> getAccountToListMappingTask =
        _lssListDB.getAccountMapListIdsForGivenSeatAndAccount(owner, account, start, count)
            .map(pairs -> pairs.stream()
                .map(pair -> createAccountToListMapping(owner, account, pair.getFirst(),
                    UrnUtils.createContractUrn(pair.getSecond().contractUrn)))
                .collect(Collectors.toList()))
            .recover(t -> {
              LOG.warn("Failed to get accountToListMapping for account: {}, seat: {}", account, owner, t);
              return Collections.emptyList();
            });

    return getAccountToListMappingTask.map(
        accountToListMappings -> PaginatedList.extractPage(accountToListMappings, start, count));
  }

  /**
   * Delete accountToListMapping
   * @param compoundKey accountToListMapping key
   * @return updateResponse with delete results
   */
  public Task<UpdateResponse> deleteAccountToListMapping(@NonNull CompoundKey compoundKey) {
    if (compoundKey.getPart(OWNER_COMPOUND_KEY) == null || compoundKey.getPart(ACCOUNT_COMPOUND_KEY) == null
        || compoundKey.getPart(LIST_COMPOUND_KEY) == null) {
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }

    String seatString = compoundKey.getPart(OWNER_COMPOUND_KEY).toString();
    String accountString = compoundKey.getPart(ACCOUNT_COMPOUND_KEY).toString();
    String listString = compoundKey.getPart(LIST_COMPOUND_KEY).toString();

    SeatUrn owner;
    OrganizationUrn account;
    SalesListUrn salesList;

    try {
      owner = SeatUrn.deserialize(seatString);
      account = OrganizationUrn.deserialize(accountString);
      salesList = SalesListUrn.deserialize(listString);
    } catch (URISyntaxException e) {
      String message = "Fail to delete since the Urn type of the key urn is not correct ";
      LOG.error(message);
      return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
    }
    return _lssListDB.deleteAccountToListMapping(owner, account, salesList.getIdAsLong()).map(isDeleted ->
        new UpdateResponse(HttpStatus.S_204_NO_CONTENT)).recoverWith(throwable -> {
      LOG.error("Failed to delete accountToListMapping " + compoundKey, throwable);
      return Task.value(new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
    });
  }

  /**
   * Create restli accountToListMapping given seat urn, organization urn, and list id.
   *
   * @param owner owner's seatUrn
   * @param account account's OrganizationUrn
   * @param listId id of the list created for an account name
   * @param contract contract urn
   * @return
   */
  @VisibleForTesting
  protected AccountToListMapping createAccountToListMapping(
      @NonNull SeatUrn owner, @NonNull OrganizationUrn account, long listId, @NonNull ContractUrn contract) {
    AccountToListMapping accountToListMapping = new AccountToListMapping();
    accountToListMapping.setOwner(owner);
    accountToListMapping.setAccount(account);
    accountToListMapping.setList(UrnUtils.createSalesListUrn(listId));
    accountToListMapping.setContract(contract);

    return accountToListMapping;
  }

  /**
   * Create espresso AccountToListMapping from restli AccountToListMapping
   *
   * @param accountToListMapping restli AccountToListMapping
   * @return espresso AccountToListMapping
   */
  @VisibleForTesting
  protected com.linkedin.sales.espresso.AccountToListMapping createEspressoAccountToListMappingFromService(
      @NonNull AccountToListMapping accountToListMapping) {
    com.linkedin.sales.espresso.AccountToListMapping espressoAccountToListMapping =
        new com.linkedin.sales.espresso.AccountToListMapping();
    espressoAccountToListMapping.contractUrn = accountToListMapping.getContract().toString();
    return espressoAccountToListMapping;
  }

  /**
   * Helper function getCompoundKey
   * @param accountToListMapping
   * @return CompoundKey
   */
  @NonNull
  public CompoundKey getCompoundKey(@NonNull AccountToListMapping accountToListMapping) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, accountToListMapping.getOwner())
        .append(LIST_COMPOUND_KEY, accountToListMapping.getList())
        .append(ACCOUNT_COMPOUND_KEY, accountToListMapping.getAccount());
  }
}
