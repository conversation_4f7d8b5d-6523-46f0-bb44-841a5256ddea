package com.linkedin.sales.service.list;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.ambry.client.AmbryConstants;
import com.linkedin.common.urn.AmbryBlobUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.omni.utils.ambry.AmbryDocumentService;
import com.linkedin.omni.utils.common.csv.AutoCloseCSVReader;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesinsights.formatter.csv.CsvHelper;
import com.linkedin.saleslist.CsvImportListSource;
import com.linkedin.saleslist.ListCsvImport;
import com.linkedin.saleslist.ListCsvImportStartRequest;
import io.grpc.Status;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections4.ListUtils;
import proto.com.linkedin.saleslist.ListCsvImportBridge;
import proto.com.linkedin.saleslist.ListCsvImportStartRequestBridge;


public class SalesAdminCsvImportService {

  private final AmbryDocumentService _ambryDocumentService;
  private final SalesListCsvImportService _salesListCsvImportService;
  private final LixService _lixService;
  private final SalesSeatClient _salesSeatClient;
  private static final int MAX_NUMBER_OF_SEATS_SUPPORTED = 50;
  private static final int MAX_NUMBER_OF_ACCOUNTS_PER_SEAT = 1000;

  private static final int DEFAULT_INDEX_FOR_SEAT_IDENTIFIER = 1;
  @VisibleForTesting
  static final PathSpec[] SEAT_FIELDS = new PathSpec[]{
      SalesSeat.fields().contract(), SalesSeat.fields().id(),
      SalesSeat.fields().entitlements()
  };

  public SalesAdminCsvImportService(AmbryDocumentService ambryDocumentService,
      SalesListCsvImportService salesListCsvImportService, LixService lixService, SalesSeatClient salesSeatClient) {
    _ambryDocumentService = ambryDocumentService;
    _salesListCsvImportService = salesListCsvImportService;
    _lixService = lixService;
    _salesSeatClient = salesSeatClient;
  }

  /**
   * Create the entities that comprise a List CSV Import workflow, start the import workflow,
   * and returns a list of {@link com.linkedin.saleslist.ListCsvImport} that represents the created import workflows
   * for all seats mentioned in csv
   * @param startRequest parameters needed to create and start the CSV import workflow
   * @return List of {@link com.linkedin.saleslist.ListCsvImport}
   */
  public Task<List<proto.com.linkedin.saleslist.ListCsvImport>> createAndStartUploadByAdmin(
      proto.com.linkedin.saleslist.ListCsvImportStartRequest startRequest) {
    if (startRequest.hasList()) {
      throw Status.INVALID_ARGUMENT.withDescription("Uploading to same list is not supported for the admin."
              + " CSV import for contract: " + startRequest.getContract())
          .asRuntimeException();
    }
    return _lixService.isContractBasedLixWithAdminEnabled(new ContractUrn(startRequest.getContract().getContractId()),
            new SeatUrn(startRequest.getCreator().getSeatId()),
            LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)
        .flatMap(isBookOfBusinessListUploadByAdminEnabled -> {
          if (!isBookOfBusinessListUploadByAdminEnabled) {
            throw Status.PERMISSION_DENIED.withDescription("Admin Upload is not allowed for this contract " + startRequest.getContract())
                .asRuntimeException();
          }
          return createAndStart(ListCsvImportStartRequestBridge.INSTANCE.fromProto(startRequest))
              .map(listCsvImports -> listCsvImports.stream().map(ListCsvImportBridge.INSTANCE::fromPegasus).collect(
                  Collectors.toList()));
        });
  }

  private Task<List<ListCsvImport>> createAndStart(ListCsvImportStartRequest startRequest) {
    String rawInputFileBlobUrn = startRequest.getRawInputFile().getId();

    return _ambryDocumentService.downloadBlob(rawInputFileBlobUrn)
        .flatMap(stream -> {
          try (AutoCloseCSVReader csvReader = new AutoCloseCSVReader(
              new InputStreamReader(stream.getInputStream(), StandardCharsets.UTF_8))) {
            String[] headerRow = csvReader.readNext();
            return fetchRecordsGroupedBySeat(csvReader, startRequest.getContract(),
                startRequest.getCreator())
                .flatMap(recordsGroupedBySeat -> buildImportStartRequestsForSeats(recordsGroupedBySeat, headerRow, startRequest))
                .flatMap(startRequestsForSeat -> Task.par(startRequestsForSeat.stream()
                    .map(_salesListCsvImportService::createAndStart)
                    .collect(Collectors.toList())));
          } catch (IOException e) {
            throw Status.INVALID_ARGUMENT.withDescription("Failed to process given CSV for contract: " + startRequest.getContract())
                .withCause(e).asRuntimeException();
          }
        });
  }

  private Task<Map<SeatUrn, List<String[]>>> fetchRecordsGroupedBySeat(AutoCloseCSVReader csvReader,
      ContractUrn contractUrn,
      SeatUrn viewer) throws IOException {
    String[] currentRow;
    Map<String, List<String[]>> seatIdentifiersToAccounts = new HashMap<>();

    int seatIdentifierIndex = DEFAULT_INDEX_FOR_SEAT_IDENTIFIER;
    while ((currentRow = csvReader.readNext()) != null) {
      List<String[]> records = seatIdentifiersToAccounts.getOrDefault(currentRow[seatIdentifierIndex], new ArrayList<>());
      records.add(currentRow);
      seatIdentifiersToAccounts.put(currentRow[seatIdentifierIndex], records);
    }

    if (seatIdentifiersToAccounts.size() > MAX_NUMBER_OF_SEATS_SUPPORTED) {
      throw Status.INVALID_ARGUMENT.withDescription("More than 50 Distinct Seats available in CSV for the contract: "
          + contractUrn).asRuntimeException();
    }

    seatIdentifiersToAccounts.forEach((seatIdentifier, accounts) -> {
      if (accounts.size() > MAX_NUMBER_OF_ACCOUNTS_PER_SEAT) {
        throw Status.INVALID_ARGUMENT.withDescription(
            String.format("More than 1000 accounts are not allowed for the Seat : %s , contract : %s", seatIdentifier,
                contractUrn)).asRuntimeException();
      }
    });

    List<Task<Pair<String, SeatUrn>>> fetchSeatUrnByEmailTasks = seatIdentifiersToAccounts.keySet()
        .stream().map(emailAsString -> _salesSeatClient.findBySeatEmail(emailAsString, contractUrn, viewer, SEAT_FIELDS)
            .map(salesSeats -> salesSeats.stream().findFirst()
                .map(salesSeat -> {
                  if (!salesSeat.getEntitlements().contains(SalesEntitlement.LIST_CSV_UPLOAD)) {
                    throw Status.PERMISSION_DENIED
                        .withDescription(String.format("Seat with email id %s does not have upload entitlement", emailAsString))
                        .asRuntimeException();
                  }
                  return new Pair<>(emailAsString, new SeatUrn(salesSeat.getId()));
                })
                .orElseThrow(() -> Status.INVALID_ARGUMENT.withDescription("Failed to find seat for the email id: " + emailAsString)
                    .asRuntimeException()))
        ).collect(Collectors.toList());

    return Task.par(fetchSeatUrnByEmailTasks)
        .map(emailToSeatUrn -> emailToSeatUrn.stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)))
        .map(seatUrnsByEmail -> seatIdentifiersToAccounts.entrySet().stream()
            .filter(accountsBySeat -> seatUrnsByEmail.containsKey(accountsBySeat.getKey()))
            .collect(Collectors.toMap(accountsBySeat -> seatUrnsByEmail.get(accountsBySeat.getKey()),
                Map.Entry::getValue)));
  }


  private Task<List<ListCsvImportStartRequest>> buildImportStartRequestsForSeats(Map<SeatUrn, List<String[]>> recordsBySeatUrn,
      String[] headerRow, ListCsvImportStartRequest startRequest) {
    List<Task<Pair<SeatUrn, AmbryBlobUrn>>> ambryBlobIdTasksBySeatUrn = recordsBySeatUrn.entrySet()
        .stream()
        .map(recordBySeatUrn -> {
          ByteArrayOutputStream stream = CsvHelper.writeCsvByteArrayOutputStream(ListUtils.union(ImmutableList.of(headerRow),
              recordBySeatUrn.getValue()));
          return _ambryDocumentService.uploadBlob(ImmutableMap.of(AmbryConstants.FileMetadataKeys.FILENAME, startRequest.getListName()),
                  new ByteArrayInputStream(stream.toByteArray()), recordBySeatUrn.getKey())
              .map(blobId -> new Pair<>(recordBySeatUrn.getKey(), UrnUtils.createAmbryBlobUrn(blobId)));
        })
        .collect(Collectors.toList());
    return Task.par(ambryBlobIdTasksBySeatUrn)
        .map(ambryBlobUrnsBySeatUrn -> ambryBlobUrnsBySeatUrn.stream()
            .map(seatUrnAmbryBlobUrnPair -> new ListCsvImportStartRequest()
                .setContract(startRequest.getContract())
                .setListName(startRequest.getListName())
                .setCreator(seatUrnAmbryBlobUrnPair.getFirst())
                .setDefaultListUponImport(true)
                .setTotalLineCount(recordsBySeatUrn.get(seatUrnAmbryBlobUrnPair.getFirst()).size())
                .setCsvImportListSource(CsvImportListSource.BOOK_OF_BUSINESS)
                .setTaxonomyMapping(startRequest.getTaxonomyMapping())
                .setRawInputFileHeader(startRequest.getRawInputFileHeader())
                .setRawInputFile(seatUrnAmbryBlobUrnPair.getSecond())).collect(Collectors.toList()));
  }
}
