package com.linkedin.sales.service.list;

import com.google.common.annotations.VisibleForTesting;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.DefaultList;
import com.linkedin.saleslist.DefaultListKey;
import com.linkedin.saleslist.ListType;
import edu.umd.cs.findbugs.annotations.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service for handling the default list of a seat associated with a list type.
 */
public class SalesDefaultListService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesDefaultListService.class);

  private final LssListDB _lssListDB;

  public SalesDefaultListService(LssListDB lssListDB) {
    _lssListDB = lssListDB;
  }

  public Task<UpdateResponse> upsertDefaultList(@NonNull DefaultListKey key, @NonNull DefaultList defaultList) {
    if (key.getListType() != ListType.ACCOUNT) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          String.format("Only ACCOUNT default list is supported. seat:%s; listType: %s", key.getSeat(),
              key.getListType())));
    }

    return _lssListDB.upsertDefaultList(key.getSeat(),
        ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(key.getListType()),
        createEspressoDefaultListFromService(defaultList)).map(httpStatus -> new UpdateResponse(httpStatus));
  }

  /**
   * Get a default list.
   *
   * @param key key of the default list
   * @return a default list
   */
  public Task<DefaultList> getDefaultList(@NonNull DefaultListKey key) {
    return _lssListDB.getDefaultList(key.getSeat(),
            ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(key.getListType()))
        .map(foundDbDefaultList -> createServiceDefaultList(key.getSeat(), key.getListType(),
            UrnUtils.createContractUrn(foundDbDefaultList.contractUrn),
            UrnUtils.createSalesListUrn(foundDbDefaultList.listUrn)))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
                String.format("Failed to get the default list for seat:%s, list type:%s", key.getSeat(),
                    key.getListType()), t));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Delete a default list.
   *
   * @param key key of the default list
   * @return response status of the delete operation
   */
  public Task<UpdateResponse> deleteDefaultList(@NonNull DefaultListKey key) {
    return _lssListDB.deleteDefaultList(key.getSeat(),
            ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(key.getListType()))
        .map(isDeleted -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT))
        .recoverWith(t -> {
          LOG.error("Failed to delete the default list for {}", key, t);
          return Task.value(new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        });
  }

  /**
   * Create a restli DefaultList.
   *
   * @param seat seat urn
   * @param listType list type
   * @param contract contract urn
   * @param list sales list urn
   *
   * @return a restli DefaultList
   */
  @VisibleForTesting
  protected DefaultList createServiceDefaultList(@NonNull SeatUrn seat, @NonNull ListType listType,
      @NonNull ContractUrn contract, @NonNull SalesListUrn list) {
    DefaultList defaultList = new DefaultList();
    defaultList.setSeat(seat).setListType(listType).setContract(contract).setList(list);

    return defaultList;
  }

  /**
   * Create espresso DefaultList from restli DefaultList
   *
   * @param defaultList restli DefaultList
   * @return espresso DefaultList
   */
  @VisibleForTesting
  protected com.linkedin.sales.espresso.DefaultList createEspressoDefaultListFromService(
      @NonNull DefaultList defaultList) {
    com.linkedin.sales.espresso.DefaultList espressoDefaultList = new com.linkedin.sales.espresso.DefaultList();
    espressoDefaultList.contractUrn = defaultList.getContract().toString();
    espressoDefaultList.listUrn = defaultList.getList().toString();

    return espressoDefaultList;
  }
}
