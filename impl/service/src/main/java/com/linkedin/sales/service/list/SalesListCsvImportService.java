package com.linkedin.sales.service.list;

import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.client.salesinsights.CsvImportTaskClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.CreatorToListCsvImportView;
import com.linkedin.sales.espresso.ImportTaskToListCsvImportView;
import com.linkedin.sales.espresso.ListToListCsvImportView;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesinsights.CsvImportTask;
import com.linkedin.salesinsights.CsvImportTaskState;
import com.linkedin.salesinsights.CsvImportUseCase;
import com.linkedin.saleslist.ListCsvImport;
import com.linkedin.saleslist.ListCsvImportStartRequest;
import com.linkedin.saleslist.ListCsvImportState;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.*;


/**
 * Service for persisting and managing the {@link com.linkedin.saleslist.ListCsvImport} workflow
 */
public class SalesListCsvImportService {

  private static final Logger LOG = LoggerFactory.getLogger(SalesListCsvImportService.class);

  private static final CsvImportUseCase CSV_IMPORT_TASK_USE_CASE = CsvImportUseCase.SALES_NAVIGATOR_LIST;
  private static final CsvImportTaskState CSV_IMPORT_TASK_DEFAULT_STATE = CsvImportTaskState.CREATED;
  private static final int CSV_IMPORT_TASK_DEFAULT_PROCESS_LINE_COUNT = 0;
  private static final int CSV_IMPORT_TASK_DEFAULT_REMAINING_TIME_MS = -1;
  private static final ImmutableBiMap<com.linkedin.sales.espresso.ListCsvImportState, ListCsvImportState> STATE_MAP =
      ImmutableBiMap.of(
          com.linkedin.sales.espresso.ListCsvImportState.IN_PROGRESS, ListCsvImportState.IN_PROGRESS,
          com.linkedin.sales.espresso.ListCsvImportState.FAILED, ListCsvImportState.FAILED,
          com.linkedin.sales.espresso.ListCsvImportState.SUCCEEDED, ListCsvImportState.SUCCEEDED);
  private static final Map<ListCsvImportState, com.linkedin.sales.espresso.ListCsvImportState> INVERTED_STATE_MAP =
      STATE_MAP.inverse();
  private static final Map<com.linkedin.sales.espresso.ListCsvImportState, Set<com.linkedin.sales.espresso.ListCsvImportState>>
      ALLOWED_STATE_UPDATES = ImmutableMap.of(
          com.linkedin.sales.espresso.ListCsvImportState.IN_PROGRESS, ImmutableSet.of(
              com.linkedin.sales.espresso.ListCsvImportState.IN_PROGRESS,
              com.linkedin.sales.espresso.ListCsvImportState.FAILED,
              com.linkedin.sales.espresso.ListCsvImportState.SUCCEEDED),
          com.linkedin.sales.espresso.ListCsvImportState.FAILED, ImmutableSet.of(
              com.linkedin.sales.espresso.ListCsvImportState.FAILED),
          com.linkedin.sales.espresso.ListCsvImportState.SUCCEEDED, ImmutableSet.of(
              com.linkedin.sales.espresso.ListCsvImportState.SUCCEEDED));
  private static final Set<CsvImportTaskState> ALLOWED_CURRENT_STATE_FOR_CANCELING_CSV_IMPORT_TASK =
      ImmutableSet.of(CsvImportTaskState.IN_PROGRESS);

  private static final BasicCollectionResult<ListCsvImport> EMPTY_RESPONSE = new BasicCollectionResult<>(
      Collections.emptyList(), 0);

  private final LssListDB _lssListDB;
  private final CsvImportTaskClient _csvImportTaskClient;
  private final SalesListIdService _salesListIdService;
  private final SalesListService _salesListService;

  public SalesListCsvImportService(LssListDB lssListDB, CsvImportTaskClient csvImportTaskClient,
      SalesListIdService salesListIdService, SalesListService salesListService) {
    _lssListDB = lssListDB;
    _csvImportTaskClient = csvImportTaskClient;
    _salesListIdService = salesListIdService;
    _salesListService = salesListService;
  }

  /**
   * Create the entities that comprise a List CSV Import workflow, start the import workflow,
   * and return a {@link com.linkedin.saleslist.ListCsvImport} that represents the created import workflow
   * @param startRequest parameters need to create and start the CSV import workflow
   * @return The created {@link com.linkedin.saleslist.ListCsvImport}
   */

  public Task<ListCsvImport> createAndStart(ListCsvImportStartRequest startRequest) {
    return validateListCsvImportStartRequest(startRequest)
        .flatMap(result -> {
          Task<Long> csvImportTaskTask = createAndStartCsvImportTask(startRequest);
          Task<Long> salesListIdTask = startRequest.hasList()
              ? Task.value(startRequest.getList().getIdAsLong())
              : _salesListIdService.generateNextId();

          return Task.par(salesListIdTask, csvImportTaskTask)
              .flatMap((salesListId, csvImportTaskId) ->
                  createEspressoListCsvImport(salesListId, csvImportTaskId, startRequest)
                      .flatMap(this::get));
        });
  }

  private Task<Void> validateListCsvImportStartRequest(ListCsvImportStartRequest startRequest) {
    // Importing to a new list
    if (!startRequest.hasList()) {
      return Task.value(null);
    }

    // Importing to an existing list
    SalesListUrn salesListUrn = startRequest.getList();

    return _lssListDB.getListCsvImportsBySalesList(salesListUrn.getIdAsLong())
        .recoverWith(t -> ExceptionUtils.isEntityNotFoundException(t) ? Task.value(null) : Task.failure(t))
        .flatMap(listToListCsvImportView -> {
          if (listToListCsvImportView != null) {
            // Need to ensure one-to-one mapping between listCsvImport and salesList.
            throw new RestLiServiceException(HttpStatus.S_409_CONFLICT, String.format(
                "Unable to start CSV import due to an existing listCsvImport record found for list: %s. "
                    + "Existing listCsvImport: %s", salesListUrn, listToListCsvImportView));
          }
          return Task.value(null);
        });
  }

  /**
   * Get a {@link ListCsvImport} using its ID
   * @param listCsvImportId the ID of the {@link ListCsvImport}
   * @return the {@link ListCsvImport} found using the ID
   */
  public Task<ListCsvImport> get(Long listCsvImportId) {
    return _lssListDB.getListCsvImport(listCsvImportId)
        .map(espressoListCsvImport -> constructListCsvImport(listCsvImportId, espressoListCsvImport));
  }

  /**
   * Batch Get a set of List CSV Import records
   * @param ids the IDs of the List CSV Import records to get
   * @return a map of List CSV Import ID to List CSV Import
   */
  public Task<BatchResult<Long, ListCsvImport>> batchGet(Set<Long> ids) {

    List<Task<ListCsvImport>> listCsvImportTasks = ids.stream()
        .map(id -> _lssListDB.getListCsvImport(id)
            .map(espressoListCsvImport -> constructListCsvImport(id, espressoListCsvImport))
            .recoverWith(t -> {
              if (ExceptionUtils.isEntityNotFoundException(t)) {
                return Task.value(null);
              }
              return Task.failure(t);
            }))
        .collect(Collectors.toList());

    return Task.par(listCsvImportTasks)
        .map(results -> {
          Map<Long, ListCsvImport> foundImports = results.stream()
              .filter(Objects::nonNull)
              .collect(Collectors.toMap(
                  ListCsvImport::getId, Function.identity()));
          Map<Long, RestLiServiceException> errors = ids.stream()
              .filter(listCsvImportId -> !foundImports.containsKey(listCsvImportId))
              .collect(Collectors.toMap(
                  Function.identity(), listCsvImportId -> new RestLiServiceException(HttpStatus.S_404_NOT_FOUND)));

          return new BatchResult<>(foundImports, errors);
        });
  }

  /**
   * Find a collection of {@link ListCsvImport} created by the provided creator
   * @param pagingContext the paging context
   * @param creator the creator
   * @param states the to limit results to. If none are provided, results will include all states.
   * @return the {@link ListCsvImport} found using the given creator
   */
  public Task<BasicCollectionResult<ListCsvImport>> findByCreator(
      PagingContext pagingContext,
      SeatUrn creator,
      ListCsvImportState[] states) {

    return _lssListDB.getListCsvImportsByCreator(creator, pagingContext.getStart(), pagingContext.getCount(), states)
        .map(totalHitsAndEntities ->
            new BasicCollectionResult<>(
                constructListCsvImports(creator, totalHitsAndEntities.getSecond()),
                totalHitsAndEntities.getFirst()))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.value(EMPTY_RESPONSE);
          }
          return Task.failure(t);
        });
  }

  /**
   * Find a collection of {@link ListCsvImport} associated with the provided CsvImportTaskUrn.
   * The provided collection should have one and only one entity since there is exactly one {@link CsvImportTask}
   * per {@link ListCsvImport}
   * @param csvImportTask the {@link CsvImportTaskUrn} associated with the list {@link ListCsvImport}
   * @return a collection with the single {@link ListCsvImport}
   */
  public Task<BasicCollectionResult<ListCsvImport>> findByCsvImportTask(CsvImportTaskUrn csvImportTask) {

    return _lssListDB.getListCsvImportsByCsvImportTask(csvImportTask)
        .map(importTaskToListCsvImportView -> constructListCsvImport(csvImportTask, importTaskToListCsvImportView))
        .map(listCsvImport ->
            new BasicCollectionResult<>(
                Collections.singletonList(listCsvImport)))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
                String.format("No listCsvImport record found for csvImportTask: %s", csvImportTask)));
          }
          return Task.failure(t);
        });
  }

  /**
   * Find a collection of {@link ListCsvImport} that created the given sales list.
   * The provided collection should have one and only one entity since there is exactly one sales list
   * per {@link ListCsvImport}
   * @param salesList the list that the ListCsvImport created
   * @return a collection with the single {@link ListCsvImport}
   */
  public Task<BasicCollectionResult<ListCsvImport>> findByList(SalesListUrn salesList) {

    return _lssListDB.getListCsvImportsBySalesList(salesList.getIdAsLong())
        .map(listToListCsvImportView -> constructListCsvImport(salesList, listToListCsvImportView))
        .map(listCsvImport ->
            new BasicCollectionResult<>(
                Collections.singletonList(listCsvImport)))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
                String.format("No listCsvImport record found for list: %s", salesList)));
          }
          return Task.failure(t);
        });
  }

  /**
   * Perform a partial update of a a {@link ListCsvImport}. This allows only the "state" field to be updated.
   * It also sets the "lastModifiedTime" automatically. When importing to a new list and the CSV import task is being
   * marked as FAILED, clean up any list and list entities that may have been created, even if the DB update call fails.
   * It also cancels the CSVImportTask if ListCsvImport is being marked as FAILED and CSVImportTask is cancelable.
   * @param listCsvImportId the ID of the {@link ListCsvImport} to be updated
   * @param patch the patched list csv import
   * @return whether or not the update succeeded
   */
  public Task<Boolean> partialUpdate(Long listCsvImportId, PatchRequest<ListCsvImport> patch) {
    com.linkedin.sales.espresso.ListCsvImport patchedListCsvImport = constructEspressoListCsvImport(patch);
    return _lssListDB.getListCsvImport(listCsvImportId).flatMap(listCsvImport -> {
      if (!ALLOWED_STATE_UPDATES.get(listCsvImport.state).contains(patchedListCsvImport.state)) {
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("Can't move an import from state %s to state %s", listCsvImport.state,
                patchedListCsvImport.state));
      }
      return _lssListDB.updateListCsvImport(listCsvImportId, patchedListCsvImport).recoverWith(t -> {
        if (BooleanUtils.isNotTrue(listCsvImport.getIsImportingToExistingList())
            && com.linkedin.sales.espresso.ListCsvImportState.FAILED == patchedListCsvImport.state) {
          return cleanUpListAndListEntitiesForFailedCsvImport(listCsvImport).flatMap(result -> Task.failure(t));
        }
        return Task.failure(t);
      }).flatMap(patchSucceeded -> {
        if (!patchSucceeded || patchedListCsvImport.state != com.linkedin.sales.espresso.ListCsvImportState.FAILED
            || Boolean.TRUE.equals(listCsvImport.getIsImportingToExistingList())) {
          return Task.value(patchSucceeded);
        }
        return cleanUpListAndListEntitiesForFailedCsvImport(listCsvImport).map(result -> patchSucceeded);
      }).flatMap(patchSucceeded -> {
        if (patchSucceeded && patchedListCsvImport.state == com.linkedin.sales.espresso.ListCsvImportState.FAILED) {
          CsvImportTaskUrn csvImportTaskUrn = UrnUtils.createCsvImportTaskUrn(listCsvImport.csvImportTaskUrn);
          return _csvImportTaskClient.get(csvImportTaskUrn.getCsvImportTaskIdEntity()).flatMap(csvImportTask -> {
            if (ALLOWED_CURRENT_STATE_FOR_CANCELING_CSV_IMPORT_TASK.contains(csvImportTask.getState())) {
              return _csvImportTaskClient.update(csvImportTask.getId(), constructCsvImportTaskForCancellation())
                  .map(ignoreResult -> patchSucceeded);
            }
            return Task.value(patchSucceeded);
          }).recover(t -> {
            LOG.error("Error occurred in canceling the CsvImportTask for {}", csvImportTaskUrn, t);
            return patchSucceeded;
          });
        }
        return Task.value(patchSucceeded);
      });
    });
  }

  /**
   * Cancel csvImportTask task by setting state as SYSTEM_CANCELED remaining time to 0.
   * @return csvImportTask with updated state and remaining time set to 0
   */
  private CsvImportTask constructCsvImportTaskForCancellation() {
    CsvImportTask csvImportTask = new CsvImportTask();
    csvImportTask.setRemainingTimeMs(0L);
    csvImportTask.setState(CsvImportTaskState.SYSTEM_CANCELED);
    return csvImportTask;
  }

  /**
   * Delete a {@link ListCsvImport} given its ID
   * @param listCsvImportId the ID of the {@link ListCsvImport} to delete
   * @return whether or not the delete succeeded
   */
  public Task<Boolean> delete(Long listCsvImportId) {
    return _lssListDB.deleteListCsvImport(listCsvImportId);
  }

  /**
   * Delete any list entities and the list itself that may have been created prior to the failure of a CSV import task.
   * @param listCsvImport The failed csv import task
   * @return
   */
  private Task<Void> cleanUpListAndListEntitiesForFailedCsvImport(@NonNull com.linkedin.sales.espresso.ListCsvImport listCsvImport) {
    Task<Boolean> deleteAllListEntitiesTask = _salesListService.getDeleteListEntitiesTask(
        listCsvImport.listId, UrnUtils.createSeatUrn(listCsvImport.creatorSeatUrn), UrnUtils.createContractUrn(listCsvImport.contractUrn));
    Task<Boolean> deleteListTask = _lssListDB.deleteList(listCsvImport.listId);
    return Task.par(deleteAllListEntitiesTask, deleteListTask)
        .map((deleteEntitiesSucceeded, deleteListSucceeded) -> {
          LOG.info("List entities deleted: {}. List deleted: {}", deleteEntitiesSucceeded, deleteEntitiesSucceeded);
          return null;
        });
  }

  private Task<Long> createAndStartCsvImportTask(ListCsvImportStartRequest startRequest) {
    CsvImportTask csvImportTask = new CsvImportTask()
        .setDataSourceName(startRequest.getListName())
        .setFileName(startRequest.getListName())
        .setRawInputFile(startRequest.getRawInputFile())
        .setRawInputFileHeader(startRequest.getRawInputFileHeader(GetMode.NULL), SetMode.IGNORE_NULL)
        .setTotalLineCount(startRequest.getTotalLineCount())
        .setTaxonomyMapping(startRequest.getTaxonomyMapping())
        .setUploader(CsvImportTask.Uploader.createWithSeatUrn(startRequest.getCreator()))
        // Set additional fields that are common to all start requests
        .setUseCase(CSV_IMPORT_TASK_USE_CASE)
        .setState(CSV_IMPORT_TASK_DEFAULT_STATE)
        .setProcessedLineCount(CSV_IMPORT_TASK_DEFAULT_PROCESS_LINE_COUNT)
        .setRemainingTimeMs(CSV_IMPORT_TASK_DEFAULT_REMAINING_TIME_MS);

    return _csvImportTaskClient.create(csvImportTask)
        .flatMap(csvImportTaskId ->
            _csvImportTaskClient.start(csvImportTaskId)
              .flatMap(unused -> Task.value(csvImportTaskId)));
  }

  private Task<Long> createEspressoListCsvImport(Long salesListId, Long csvImportTaskId, ListCsvImportStartRequest startRequest) {
    com.linkedin.sales.espresso.ListCsvImport listCsvImport =
        constructEspressoListCsvImport(salesListId, csvImportTaskId, startRequest);
    return _lssListDB.createListCsvImport(listCsvImport);
  }

  private com.linkedin.sales.espresso.ListCsvImport constructEspressoListCsvImport(
      Long salesListId, Long csvImportTaskId, ListCsvImportStartRequest startRequest) {

    long now = System.currentTimeMillis();
    com.linkedin.sales.espresso.ListCsvImport listCsvImport = new com.linkedin.sales.espresso.ListCsvImport();

    listCsvImport.createdTime = now;
    listCsvImport.lastModifiedTime = now;
    listCsvImport.state = com.linkedin.sales.espresso.ListCsvImportState.IN_PROGRESS;
    listCsvImport.creatorSeatUrn = startRequest.getCreator().toString();
    listCsvImport.contractUrn = startRequest.getContract().toString();
    listCsvImport.csvImportTaskUrn = UrnUtils.createCsvImportTaskUrn(csvImportTaskId).toString();
    listCsvImport.listId = salesListId;
    listCsvImport.listName = startRequest.getListName();
    listCsvImport.listDescription = startRequest.getListDescription(GetMode.NULL);
    listCsvImport.isImportingToExistingList = startRequest.hasList();
    listCsvImport.isDefaultListUponImport = startRequest.isDefaultListUponImport();

    return listCsvImport;
  }

  private com.linkedin.sales.espresso.ListCsvImport constructEspressoListCsvImport(PatchRequest<ListCsvImport> patch) {
    ListCsvImport listCsvImport = new ListCsvImport();
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport = new com.linkedin.sales.espresso.ListCsvImport();

    try {
      PatchApplier.applyPatch(listCsvImport, patch);
    } catch (DataProcessingException e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format("Unable to apply patch %s", patch));
    }

    if (!listCsvImport.hasState()) {
      return espressoListCsvImport;
    }

    espressoListCsvImport.lastModifiedTime = System.currentTimeMillis();
    espressoListCsvImport.state = INVERTED_STATE_MAP.get(listCsvImport.getState());

    return espressoListCsvImport;
  }

  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "STATE_MAP.get value is not null")
  private ListCsvImport constructListCsvImport(
      Long listCsvImportId, com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {
    checkArgument(STATE_MAP.containsKey(espressoListCsvImport.state),
        String.format("Unsupported espressoListCsvImport state [%s]", espressoListCsvImport.state));

    String listDescription = Optional.ofNullable(espressoListCsvImport.listDescription)
        .map(CharSequence::toString)
        .orElse(null);

    return new ListCsvImport()
        .setId(listCsvImportId)
        .setCreated(espressoListCsvImport.createdTime)
        .setLastModified(espressoListCsvImport.lastModifiedTime)
        .setState(STATE_MAP.get(espressoListCsvImport.state))
        .setCreator(UrnUtils.createSeatUrn(espressoListCsvImport.creatorSeatUrn))
        .setContract(UrnUtils.createContractUrn(espressoListCsvImport.contractUrn))
        .setCsvImportTask(UrnUtils.createCsvImportTaskUrn(espressoListCsvImport.csvImportTaskUrn))
        .setList(UrnUtils.createSalesListUrn(espressoListCsvImport.listId))
        .setListName(espressoListCsvImport.listName.toString())
        .setListDescription(listDescription, SetMode.IGNORE_NULL)
        .setImportingToExistingList(espressoListCsvImport.isImportingToExistingList, SetMode.IGNORE_NULL)
        .setDefaultListUponImport(espressoListCsvImport.isDefaultListUponImport, SetMode.IGNORE_NULL);
  }

  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "STATE_MAP.get value is not null")
  private ListCsvImport constructListCsvImport(
      SeatUrn creator, Long listCsvImportId, CreatorToListCsvImportView creatorToListCsvImportView) {
    checkArgument(STATE_MAP.containsKey(creatorToListCsvImportView.state),
        String.format("Unsupported creatorToListCsvImportView state [%s]", creatorToListCsvImportView.state));

    String listDescription = Optional.ofNullable(creatorToListCsvImportView.listDescription)
        .map(CharSequence::toString)
        .orElse(null);

    return new ListCsvImport()
        .setId(listCsvImportId)
        .setCreated(creatorToListCsvImportView.createdTime)
        .setLastModified(creatorToListCsvImportView.lastModifiedTime)
        .setState(STATE_MAP.get(creatorToListCsvImportView.state))
        .setCreator(creator)
        .setContract(UrnUtils.createContractUrn(creatorToListCsvImportView.contractUrn))
        .setCsvImportTask(UrnUtils.createCsvImportTaskUrn(creatorToListCsvImportView.csvImportTaskUrn))
        .setList(UrnUtils.createSalesListUrn(creatorToListCsvImportView.listId))
        .setListName(creatorToListCsvImportView.listName.toString())
        .setListDescription(listDescription, SetMode.IGNORE_NULL)
        .setImportingToExistingList(creatorToListCsvImportView.isImportingToExistingList, SetMode.IGNORE_NULL);
  }

  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "STATE_MAP.get value is not null")
  private ListCsvImport constructListCsvImport(
      CsvImportTaskUrn csvImportTaskUrn, ImportTaskToListCsvImportView importTaskToListCsvImportView) {
    checkArgument(STATE_MAP.containsKey(importTaskToListCsvImportView.state),
        String.format("Unsupported importTaskToListCsvImportView state [%s]", importTaskToListCsvImportView.state));

    String listDescription = Optional.ofNullable(importTaskToListCsvImportView.listDescription)
        .map(CharSequence::toString)
        .orElse(null);

    return new ListCsvImport()
        .setId(importTaskToListCsvImportView.listCsvImportId)
        .setCreated(importTaskToListCsvImportView.createdTime)
        .setLastModified(importTaskToListCsvImportView.lastModifiedTime)
        .setState(STATE_MAP.get(importTaskToListCsvImportView.state))
        .setCreator(UrnUtils.createSeatUrn(importTaskToListCsvImportView.creatorSeatUrn))
        .setContract(UrnUtils.createContractUrn(importTaskToListCsvImportView.contractUrn))
        .setCsvImportTask(csvImportTaskUrn)
        .setList(UrnUtils.createSalesListUrn(importTaskToListCsvImportView.listId))
        .setListName(importTaskToListCsvImportView.listName.toString())
        .setListDescription(listDescription, SetMode.IGNORE_NULL)
        .setImportingToExistingList(importTaskToListCsvImportView.isImportingToExistingList, SetMode.IGNORE_NULL);
  }

  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "STATE_MAP.get value is not null")
  private ListCsvImport constructListCsvImport(
      SalesListUrn salesList, ListToListCsvImportView listToListCsvImportView) {
    checkArgument(STATE_MAP.containsKey(listToListCsvImportView.state),
        String.format("Unsupported listToListCsvImportView state [%s]", listToListCsvImportView.state));

    String listDescription = Optional.ofNullable(listToListCsvImportView.listDescription)
        .map(CharSequence::toString)
        .orElse(null);

    return new ListCsvImport()
        .setId(listToListCsvImportView.listCsvImportId)
        .setCreated(listToListCsvImportView.createdTime)
        .setLastModified(listToListCsvImportView.lastModifiedTime)
        .setState(STATE_MAP.get(listToListCsvImportView.state))
        .setCreator(UrnUtils.createSeatUrn(listToListCsvImportView.creatorSeatUrn))
        .setContract(UrnUtils.createContractUrn(listToListCsvImportView.contractUrn))
        .setCsvImportTask(UrnUtils.createCsvImportTaskUrn(listToListCsvImportView.csvImportTaskUrn))
        .setList(salesList)
        .setListName(listToListCsvImportView.listName.toString())
        .setListDescription(listDescription, SetMode.IGNORE_NULL)
        .setImportingToExistingList(listToListCsvImportView.isImportingToExistingList, SetMode.IGNORE_NULL);
  }

  private List<ListCsvImport> constructListCsvImports(
      SeatUrn creator, Collection<Pair<Long, CreatorToListCsvImportView>> creatorToListCsvImports) {

    return creatorToListCsvImports.stream()
        .map(csvListImportIdAndEntity ->
            constructListCsvImport(creator, csvListImportIdAndEntity.getFirst(), csvListImportIdAndEntity.getSecond()))
        .collect(Collectors.toList());
  }
}
