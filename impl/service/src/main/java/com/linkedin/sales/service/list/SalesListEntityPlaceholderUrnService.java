package com.linkedin.sales.service.list;

import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.TorrentClient;
import edu.umd.cs.findbugs.annotations.NonNull;


/**
 * Service for generating a new SalesListEntityPlaceholderUrn.
 */
public class SalesListEntityPlaceholderUrnService {
  private final TorrentClient _torrentClient;

  public SalesListEntityPlaceholderUrnService(TorrentClient torrentClient) {
    _torrentClient = torrentClient;
  }

  /**
   * Generate a new SalesListEntityPlaceholderUrn with globally unique placeholder ID.
   * @param salesListUrn Parent list urn for placeholder
   */
  public Task<SalesListEntityPlaceholderUrn> generateUrn(@NonNull SalesListUrn salesListUrn) {
    Task<Long> placeholderIdTask = _torrentClient.generateNextId();
    return placeholderIdTask.map(placeholderId -> new SalesListEntityPlaceholderUrn(salesListUrn, placeholderId));
  }
}
