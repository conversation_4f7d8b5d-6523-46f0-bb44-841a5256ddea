package com.linkedin.sales.service.list;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.function.Tuple3;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.LeadManager;
import com.linkedin.sales.espresso.LeadOwner;
import com.linkedin.sales.espresso.LeadRelationshipStrength;
import com.linkedin.sales.espresso.LeadRole;
import com.linkedin.sales.espresso.LeadText;
import com.linkedin.sales.espresso.ListSource;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.SearchIndexUpdatePriorityType;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.acl.SubResourceType;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.SalesEntitiesBatchUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.saleslist.AccountMapTier;
import com.linkedin.saleslist.LeadManagerEntityUrn;
import com.linkedin.saleslist.ListEntity;
import com.linkedin.saleslist.ListEntityPriorityInfo;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import org.apache.commons.lang.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static com.linkedin.sales.service.utils.UrnUtils.*;
import static java.util.stream.Collectors.*;
import static com.linkedin.lss.workflow.constants.Constants.*;



public class SalesListEntityService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesListEntityService.class);
  private static final int BATCH_CREATE_ENTITY_SIZE = 100;
  private static final Set<String> RELATIONSHIP_MAP_LIX_KEY_SET =
      ImmutableSet.of(LixUtils.LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_LIST_VIEW,
          LixUtils.LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_MAP_VIEW);

  private final LssListDB _lssListDB;
  private final AclServiceDispatcher _aclServiceDispatcher;
  private final LixService _lixService;
  private final SalesListEntityPlaceholderUrnService _salesListEntityPlaceholderUrnService;

  public SalesListEntityService(LssListDB lssListDB, AclServiceDispatcher aclServiceDispatcher, LixService lixService,
      SalesListEntityPlaceholderUrnService salesListEntityPlaceholderUrnService) {
    _lssListDB = lssListDB;
    _aclServiceDispatcher = aclServiceDispatcher;
    _lixService = lixService;
    _salesListEntityPlaceholderUrnService = salesListEntityPlaceholderUrnService;
  }

  /**
   * Batch create list entities
   * @param listEntities the list entities that need to be created
   * @param contract contract for permission check
   * @return the map that contains the create response
   */
  public Task<Map<CompoundKey, CreateResponse>> batchCreateListEntities(@NonNull List<ListEntity> listEntities,
      @NonNull ContractUrn contract) {
   return batchCreateListEntities(listEntities, contract, false);
  }


  /**
   * Batch create list entities
   * @param listEntities the list entities that need to be created
   * @param contract contract for permission check
   * @param isLowPriorityOperation Uses LOW priority Quota for espresso. Can result in downstream 429 errors.
   * @return the map that contains the create response
   */
  public Task<Map<CompoundKey, CreateResponse>> batchCreateListEntities(@NonNull List<ListEntity> listEntities,
      @NonNull ContractUrn contract, boolean isLowPriorityOperation) {
    if (listEntities.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }
    // Creator should be identical for one batch call
    if (listEntities.stream().map(ListEntity::getCreator).distinct().count() == 1) {
      long seatId = listEntities.get(0).getCreator().getSeatIdEntity();
      // Generate listIdToListEntitySet map
      Map<Long, Set<ListEntity>> listIdToListEntityMap = listEntities.stream()
          .collect(groupingBy(listEntity -> listEntity.getList().getIdAsLong(), Collectors.toSet()));
      Set<Long> listIds = listIdToListEntityMap.keySet();

      Map<Long, HttpStatus> errorListIdToStatusMap = new ConcurrentHashMap<>();

      Task<Map<Long, Pair<ListType, Optional<ListSource>>>> listTypeAndSourceTask = getListTypeAndSourceTask(listIds, seatId, errorListIdToStatusMap);

      return Task.par(listTypeAndSourceTask, getListCountTask(listIds))
              .flatMap((listTypeAndSourceMap, listIdToCountMap) -> {
                long listSizeLimit = ServiceConstants.LIST_ENTITY_LIMIT;

                //filter listEntity to ensure list count under upper limit and has permission
                Set<ListEntity> listEntityToCreate =
                    filteredListEntities(listIds, listIdToListEntityMap, listIdToCountMap, errorListIdToStatusMap,
                        listSizeLimit);

                // Pre-process the entities to be acted upon first by partitioning them on the list id.  Within each
                // partition, create batches of size recommended by espresso and use the multi-put operation.
                // Batches belonging to different lists are processed in parallel.
                Map<Long, List<ListEntity>> listIdToListEntitiesToCreateMap = listEntityToCreate.stream()
                    .collect(groupingBy(listEntity -> listEntity.getList().getIdAsLong(), Collectors.toList()));

                return Task.par(listIdToListEntitiesToCreateMap.entrySet()
                    .stream()
                    .map(entry -> batchCreateListEntities(entry.getKey(), entry.getValue(), contract, seatId,
                        listTypeAndSourceMap, isLowPriorityOperation))
                    .collect(Collectors.toList())).map(listEntitiesCreationResponses -> {
                  Map<CompoundKey, CreateResponse> responseMap = listEntitiesCreationResponses.stream()
                      .flatMap(m -> m.entrySet().stream())
                      .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

                  return buildCreateResponseMap(listEntities, responseMap, errorListIdToStatusMap, listEntityToCreate);
                });
              })
              .recover(t -> {
                LOG.error("Failed to create list entities", t);
                return listEntities.stream()
                    .map(listEntity -> getListEntityCreationErrorResponse(listEntity,
                        HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to create listEntity "))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
              });
    } else {
      return Task.value(listEntities.stream()
          .map(listEntity -> getListEntityCreationErrorResponse(listEntity, HttpStatus.S_400_BAD_REQUEST,
              "Creator is not identical for all the list entities in the batch"))
          .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }
  }

  /**
   * Service to check the ownership and then delete the list entity
   * @param compoundKey compoundKey including the list urn and the entity urn
   * @param seat seat information for the ownership check
   * @return boolean to tell if the deletion succeeds
   */
  public Task<UpdateResponse> deleteListEntity(@NonNull CompoundKey compoundKey, @NonNull SeatUrn seat) {
    return deleteListEntityHelper(compoundKey, seat, Collections.emptyMap(), false);
  }

  /**
   * Create placeholder entity. The placeholder card entity will have default positionInLevel.
   * @param listUrn listUrn the entity will belong to
   * @param creatorContractUrn contractUrn for creator
   * @param creatorSeat seatUrn for creator
   * @param createdAt the time (ms since epoch) the entity was created and added to list.
   * @return SalesListEntityPlaceholderUrn of newly created entity.
   */
  public Task<ActionResult<SalesListEntityPlaceholderUrn>> createPlaceholderEntity(
      @NonNull SalesListUrn listUrn, @NonNull ContractUrn creatorContractUrn, @NonNull SeatUrn creatorSeat, long createdAt) {
    Task<SalesListEntityPlaceholderUrn> generatePlaceholderUrnTask =
        _salesListEntityPlaceholderUrnService.generateUrn(listUrn);

    return generatePlaceholderUrnTask.flatMap(placeholderUrn -> {
      //Set fields for create flow.
      ListEntity listEntity = new ListEntity().setList(listUrn)
          .setCreator(creatorSeat)
          .setCreatedAt(createdAt)
          .setEntity(placeholderUrn)
          .setPositionInLevel(RELATIONSHIP_MAP_ENTITY_DEFAULT_POSITION_IN_LEVEL);

      return batchCreateListEntities(Collections.singletonList(listEntity), creatorContractUrn).map(resultMap -> {
        CompoundKey compoundKey = getCompoundKey(listEntity);
        if (!resultMap.containsKey(compoundKey)) {
          throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to create the entity.");
        }
        HttpStatus responseStatus = resultMap.get(compoundKey).getStatus();
        if (responseStatus != HttpStatus.S_201_CREATED) {
          throw resultMap.get(compoundKey).hasError() ? resultMap.get(compoundKey).getError()
              : new RestLiServiceException(responseStatus, "Failed to create the entity.");
        } else {
          return new ActionResult<>(placeholderUrn, responseStatus);
        }
      });
    });
  }


  /**
   * Batch delete list entities
   * @param compoundKeys a set of compoundKeys for deletion
   * @param seat seat for permission check
   * @param isLowPriorityOperation Uses LOW priority Quota for espresso. Can result in downstream 429 errors.
   * @return the map that contains the update response
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeleteListEntities(@NonNull Set<CompoundKey> compoundKeys,
      @NonNull SeatUrn seat, boolean isLowPriorityOperation) {
    Set<Long> listIds = compoundKeys.stream().map(compoundKey -> {
      try {
        return SalesListUrn.deserialize(compoundKey.getPart(ServiceConstants.LIST_COMPOUND_KEY).toString())
            .getIdAsLong();
      } catch (URISyntaxException e) {
        LOG.warn("Unable to parse listUrn form compoundKey : {}", compoundKey, e);
        return null;
      }
    }).filter(Objects::nonNull).collect(Collectors.toSet());

    return Task.par(listIds.stream()
        .map(listId -> getListTypeAndAccessDecision(seat, listId,
            AccessAction.UPDATE).map(accessDecisionPair -> new AbstractMap.SimpleEntry<>(listId, accessDecisionPair))
            .recover(t -> {
              if (ExceptionUtils.isEntityNotFoundException(t)) {
                LOG.warn("The list {} does not exist.", listId, t);
                return new AbstractMap.SimpleEntry<>(listId, null);
              } else {
                return null;
              }
            }))
        .filter(Objects::nonNull)
        .collect(toList())).flatMap(listIdToListEntry -> {

      Map<Long, Pair<ListType, AccessDecision>> listIdToAccessDecisionMap = Maps.newHashMap();

      listIdToListEntry.forEach((entry) -> listIdToAccessDecisionMap.put(entry.getKey(), entry.getValue()));

      return SalesEntitiesBatchUtils.batchDeleteEntities(seat, compoundKeys,
          compoundKey -> deleteListEntityHelper(compoundKey, seat, listIdToAccessDecisionMap, isLowPriorityOperation).map(
              deleteResponse -> new AbstractMap.SimpleEntry<>(compoundKey, deleteResponse)).recover(throwable -> {
            LOG.warn("Failed to delete the list entity " + compoundKey, throwable);

            return new AbstractMap.SimpleEntry<>(compoundKey,
                new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
          }), _lixService);
    });
  }

  /**
   * get the list entities
   * @param viewer viewer urn, either SeatUrn or MemberUrn
   * @param listId list id
   * @param start start
   * @param count count
   * @param sortOrder sort order, currently it only sorts by last modified time.
   * @return return the list entities and the total hit
   */
  public Task<Pair<Integer, List<ListEntity>>> getListEntities(@NonNull Urn viewer, long listId, int start, int count,
      @NonNull SortOrder sortOrder) {
    if (!ServiceConstants.SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES.contains(viewer.getEntityType())) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format(
          "Unsupported viewer type %s. Supported viewer types are %s", viewer,
          ServiceConstants.SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES)));
    }

    SalesListUrn salesListUrn = createSalesListUrn(listId);

    return getListTypeAndAccessDecision(viewer, listId, AccessAction.READ)
        .flatMap(typeAndPermission -> {
          if (typeAndPermission.getSecond() == AccessDecision.ALLOWED) {
            return _lssListDB.getListEntities(listId, start, count, sortOrder)
                .map(listEntityPairWithTotalHits -> {
                  int totalHits = listEntityPairWithTotalHits.getFirst();
                  List<Pair<Urn, com.linkedin.sales.espresso.ListEntity>> listEntityPairs =
                      listEntityPairWithTotalHits.getSecond();
                  List<ListEntity> listEntities = listEntityPairs.stream()
                      .map(listEntityPair -> convertEspressoListEntityToRestliListEntity(listEntityPair.getSecond(),
                          salesListUrn, listEntityPair.getFirst()))
                      .collect(Collectors.toList());
                  return new Pair<>(totalHits, listEntities);
                });
          } else {
            return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
                String.format("viewer %s does not have permission to view list %d", viewer, listId)));
          }
        }).recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(
                new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, "The list does not exist " + listId, t));
          } else if (t instanceof RestLiServiceException
              && ((RestLiServiceException) t).getStatus() == HttpStatus.S_403_FORBIDDEN) {
            return Task.failure(t);
          } else {
            return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "fail to get the permission for the list " + listId, t));
          }
        });
  }

  /**
   * get the list of entity Urns accessible to the viewer for a set of listIds.
   * @param seatId seat id
   * @param listIds Set of list ids
   * @param start start
   * @param count count
   * @return a list of entity urns
   */
  public Task<List<Urn>> getListEntityUrnsByListIds(long seatId, @NonNull Set<Long> listIds, int start,
      int count) {
    return checkReadAccessForLists(seatId, listIds).flatMap(accessMapPair -> {
      Set<Long> listIdsWithAccess = listIds.stream()
          .filter(listId -> accessMapPair.getFirst().getOrDefault(listId, Boolean.FALSE))
          .collect(toSet());
      return _lssListDB.getEntityUrnsForListEntitiesForMultipleLists(listIdsWithAccess, start, count);
    });
  }

  /**
   * Helper function to create response object with error status.
   */
  private AbstractMap.SimpleEntry<CompoundKey, CreateResponse> getListEntityCreationErrorResponse(
      @NonNull ListEntity listEntity,
      @NonNull  HttpStatus httpStatus, String msg) {
    CompoundKey compoundKey = getCompoundKey(listEntity);

    return new AbstractMap.SimpleEntry<>(compoundKey,
        new CreateResponse(new RestLiServiceException(httpStatus, msg + compoundKey)));
  }

  /**
   * Helper function to create list entities that belong to a single list in batches.
   * Multiple batches of list entities are created and entities within each batch are created using espresso's
   * multi-put request.
   */
  @VisibleForTesting
  Task<Map<CompoundKey, CreateResponse>> batchCreateListEntities(long listId,
      @NonNull List<ListEntity> listEntities, @NonNull ContractUrn contract, long seatId,
      @NonNull Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap,
      boolean isLowPriorityOperation) {
    final SalesListUrn listUrn = createSalesListUrn(listId);

    Task<Map<CompoundKey, CreateResponse>> result =
        _lixService.getEntityBatchCreateConcurrencyLevel(new SeatUrn(seatId),
            LixUtils.LSS_LIST_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL)
            .flatMap(batchCreateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(
                Lists.partition(listEntities, BATCH_CREATE_ENTITY_SIZE),
                listEntitiesToCreate -> createListEntitiesHelper(listUrn, listEntitiesToCreate, contract, listIdToTypeAndSourceMap, isLowPriorityOperation),
                batchCreateConcurrencyLevel)
                .map(nestedCreateListEntitiesResultLists -> nestedCreateListEntitiesResultLists.stream()
                    .flatMap(m -> m.entrySet().stream())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))));

    return result.flatMap(responseMap -> {
      final Optional<CreateResponse> createdStatus =
          responseMap.values().stream().filter(resp -> resp.getStatus() == HttpStatus.S_201_CREATED).findAny();
      if (createdStatus.isPresent()) {
        // If one or more entities were created, update the lastModified timestamp of the list.
        return updateLastModifiedTimeAndModifier(listId, null, new SeatUrn(seatId).toString()).map(
            ignore -> responseMap);
      } else {
        return Task.value(responseMap);
      }
    });
  }

  /**
   * Helper function to update list entities that belong to a single list in batches.
   * Multiple batches of list entities are updated and entities within each batch are updated using espresso's
   * multi-post request.
   */
  private Task<Map<CompoundKey, UpdateResponse>> batchUpdateListEntities(long listId,
      @NonNull List<ListEntity> listEntities, @NonNull SeatUrn seatUrn, @NonNull ContractUrn contractUrn,
      @NonNull Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap) {
    final SalesListUrn listUrn = createSalesListUrn(listId);

    // We reuse the same lix that is used for batch creation since the operations are similar.
    return _lixService.getEntityBatchCreateConcurrencyLevel(seatUrn,
        LixUtils.LSS_LIST_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL)
        .flatMap(batchUpdateConcurrencyLevel -> ParseqUtils.parInSpecificConcurrency(
            Lists.partition(listEntities, BATCH_CREATE_ENTITY_SIZE),
            listEntitiesToUpdate -> updateListEntitiesHelper(listUrn, listEntitiesToUpdate, listIdToTypeAndSourceMap, seatUrn,
                contractUrn), batchUpdateConcurrencyLevel)
            .map(nestedUpdateListEntitiesResultLists -> nestedUpdateListEntitiesResultLists.stream()
                .flatMap(m -> m.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue))));
  }

  /**
   *  Helper function to update list entities belonging to a single list.
   */
  private Task<Map<CompoundKey, UpdateResponse>> updateListEntitiesHelper(@NonNull SalesListUrn listUrn,
      @NonNull List<ListEntity> listEntitiesToUpdate, @NonNull Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap,
      @NonNull SeatUrn seatUrn, @NonNull ContractUrn contractUrn) {
    List<ListEntity> filteredListEntities = Lists.newArrayList();

    final Map<CompoundKey, UpdateResponse> errorResponseMap = listEntitiesToUpdate.stream().map(listEntity -> {
      Urn entityUrn = listEntity.getEntity();

      CompoundKey compoundKey = getCompoundKey(listEntity);

      ListType listType = Optional.ofNullable(listIdToTypeAndSourceMap.get(listEntity.getList().getIdAsLong()))
          .map(Pair::getFirst)
          .orElse(null);
      String entityTypeStr = entityUrn.getEntityType();
      Set<String> expectedEntityType = LIST_TYPE_ESPRESSO_TO_ENTITY_TYPE_MAPPING.get(listType);
      if (expectedEntityType == null || !expectedEntityType.contains(entityTypeStr)) {
        return new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(HttpStatus.S_403_FORBIDDEN));
      } else {
        filteredListEntities.add(listEntity);
        return null;
      }
    }).filter(Objects::nonNull).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    if (filteredListEntities.isEmpty()) {
      Map<CompoundKey, UpdateResponse> compoundKeyUpdateResponseMap = Maps.newHashMap();
      compoundKeyUpdateResponseMap.putAll(errorResponseMap);
      return Task.value(compoundKeyUpdateResponseMap);
    }

    Pair<Map<Urn, com.linkedin.sales.espresso.ListEntity>, Map<ListEntity, Exception>> espressoConversionResult =
        createEspressoListEntities(seatUrn, contractUrn, filteredListEntities, listIdToTypeAndSourceMap);
    Map<Urn, com.linkedin.sales.espresso.ListEntity> espressoConversionSuccessEntities = espressoConversionResult.getFirst();
    Map<ListEntity, Exception> espressoConversionFailedEntities = espressoConversionResult.getSecond();

    errorResponseMap.putAll(espressoConversionFailedEntities.entrySet()
        .stream()
        .map(entry -> new AbstractMap.SimpleEntry<>(getCompoundKey(entry.getKey()),
            new UpdateResponse(HttpStatus.S_400_BAD_REQUEST)))
        .collect(toMap(Map.Entry::getKey, Map.Entry::getValue)));

    // The createListEntities method of _lssListDB will update the listEntity if the entity is already present in the DB.
    // The listEntity to be updated is sure to be present in the DB at this point.
    return _lssListDB.upsertListEntities(listUrn, espressoConversionSuccessEntities, false)
        .map(responseMap -> responseMap.entrySet()
            .stream()
            .map(entry -> new AbstractMap.SimpleEntry<>(getCompoundKey(listUrn, entry.getKey()),
                new UpdateResponse(entry.getValue())))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
        .map(respMap -> {
          Map<CompoundKey, UpdateResponse> compoundKeyUpdateResponseMap = Maps.newHashMap();
          compoundKeyUpdateResponseMap.putAll(errorResponseMap);
          compoundKeyUpdateResponseMap.putAll(respMap);

          return compoundKeyUpdateResponseMap;
        });
  }

  /**
   *  Helper function to batch create list entities belonging to a single list.
   */
  private Task<Map<CompoundKey, CreateResponse>> createListEntitiesHelper(SalesListUrn listUrn,
      @NonNull List<ListEntity> listEntities, @NonNull ContractUrn contractUrn,
      @NonNull Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap,
      boolean isLowPriorityOperation) {
    List<ListEntity> filteredListEntities = Lists.newArrayList();

    final Map<CompoundKey, CreateResponse> errorResponseMap = listEntities.stream().map(listEntity -> {
      Urn entityUrn = listEntity.getEntity();

      CompoundKey compoundKey = getCompoundKey(listEntity);
      ListType listType = Optional.ofNullable(listIdToTypeAndSourceMap.get(listEntity.getList().getIdAsLong()))
          .map(Pair::getFirst)
          .orElse(null);
      String entityTypeStr = entityUrn.getEntityType();
      Set<String> expectedEntityType = LIST_TYPE_ESPRESSO_TO_ENTITY_TYPE_MAPPING.get(listType);
      if (expectedEntityType == null || !expectedEntityType.contains(entityTypeStr)) {
        return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
            new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
                "the entity does not align with the list type " + compoundKey)));
      } else {
        filteredListEntities.add(listEntity);
        return null;
      }
    }).filter(Objects::nonNull).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    Pair<Map<Urn, com.linkedin.sales.espresso.ListEntity>, Map<ListEntity, Exception>> espressoConversionResult =
        createEspressoListEntities(null, contractUrn, filteredListEntities, listIdToTypeAndSourceMap);
    Map<Urn, com.linkedin.sales.espresso.ListEntity> espressoConversionSuccessEntities = espressoConversionResult.getFirst();
    Map<ListEntity, Exception> espressoConversionFailedEntities = espressoConversionResult.getSecond();

    errorResponseMap.putAll(espressoConversionFailedEntities.entrySet()
        .stream()
        .map(entry -> new AbstractMap.SimpleEntry<>(getCompoundKey(entry.getKey()), new CreateResponse(
            new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, entry.getValue().getMessage()))))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    return _lssListDB.upsertListEntities(listUrn, espressoConversionSuccessEntities, isLowPriorityOperation)
        .map(responseMap -> responseMap.entrySet().stream().map(entry -> {
          CompoundKey compoundKey = getCompoundKey(listUrn, entry.getKey());

          if (entry.getValue() == HttpStatus.S_201_CREATED) {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(compoundKey, entry.getValue()));
          } else if (entry.getValue() == HttpStatus.S_200_OK) {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_409_CONFLICT,
                    "Entity has already been created " + compoundKey)));
          } else {
            return new AbstractMap.SimpleEntry<>(compoundKey, new CreateResponse(
                new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    "Failed to create the listEntity " + compoundKey)));
          }
        }).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
        .map(respMap -> {
          Map<CompoundKey, CreateResponse> compoundKeyCreateResponseMap = Maps.newHashMap();
          compoundKeyCreateResponseMap.putAll(respMap);
          compoundKeyCreateResponseMap.putAll(errorResponseMap);

          return compoundKeyCreateResponseMap;
        });
  }

  /**
   * Helper function to create Espresso List Entity object.
   * @param maybeLastModifierSeatUrn Will be null when new list entities are created.
   * @return Pair with first being Map of success entities and second being map of failed entities.
   */
  private Pair<Map<Urn, com.linkedin.sales.espresso.ListEntity>, Map<ListEntity, Exception>> createEspressoListEntities(
      @Nullable SeatUrn maybeLastModifierSeatUrn, @NonNull ContractUrn contractUrn,
      @NonNull List<ListEntity> filteredListEntities,
      @NonNull Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap) {
    Map<ListEntity, Exception> errorEntitiesMap = Maps.newHashMap();
    Map<Urn, com.linkedin.sales.espresso.ListEntity> espressoEntitiesMap =
        filteredListEntities.stream().map(listEntity -> {
          try {
            com.linkedin.sales.espresso.ListEntity espressoListEntity = new com.linkedin.sales.espresso.ListEntity();
            espressoListEntity.contractId = contractUrn.getContractIdEntity();
            espressoListEntity.createdTime = listEntity.getCreatedAt();
            espressoListEntity.ownerSeatId = listEntity.getCreator().getSeatIdEntity();
            // For now we do not support any customized sort so the sortOrder will always be same as createdTime;
            espressoListEntity.sortOrder = listEntity.getCreatedAt();
            espressoListEntity.lastModifiedTime =
                listEntity.hasLastModifiedAt() ? listEntity.getLastModifiedAt() : listEntity.getCreatedAt();
            SeatUrn lastModifierSeatUrn =
                maybeLastModifierSeatUrn != null ? maybeLastModifierSeatUrn : listEntity.getCreator();

            if (listEntity.getPriorityInfo() != null) {
              espressoListEntity.priority =
                  PRIORITY_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(listEntity.getPriorityInfo().getPriority());
              espressoListEntity.priorityLastModifiedSeatUrn = lastModifierSeatUrn.toString();
              espressoListEntity.priorityLastModifiedTime = System.currentTimeMillis();
            }

            // For account map lists, sortOrder will store the entity's position within a tier.  Each map tier can contain
            // up to (MAX_TIER_POSITION + 1), currently 10, entities.  Sorting is done at sales-api as the index on sortOrder
            // cannot support this particular use case.  Entity order are based on (1) the entity's tier position (2) value
            // of createdAt, i.e. in the case where multiple entities have the same tier position, they will be sorted based
            // on createdAt with the latter time takes precedence.
            ListType listType = Optional.ofNullable(listIdToTypeAndSourceMap.get(listEntity.getList().getIdAsLong()))
                .map(Pair::getFirst)
                .orElse(null);
            if (ListType.ACCOUNT_MAP == listType) {
              Integer tierAsInt = ACCOUNT_MAP_TIER_INTEGER_MAP.get(listEntity.getTier());
              if (tierAsInt != null) {
                espressoListEntity.tier = tierAsInt;
              }

              Integer tierPosition = listEntity.getPositionInTier();
              if (tierPosition != null) {
                espressoListEntity.sortOrder = tierPosition;
              }

              //Check if the memberUrn is null. Set the actor and lastModified time.
              if (listEntity.hasLeadManager()) {
                LeadManager leadManager = new LeadManager();
                //EntityUrn field is prioritized over memberUrn field.
                if (listEntity.getLeadManager().hasEntityUrn()) {
                  LeadManagerEntityUrn managerUrn = listEntity.getLeadManager().getEntityUrn();
                  if (managerUrn.isMember()) {
                    leadManager.setMemberUrn(managerUrn.getMember().toString());
                  } else if (managerUrn.isSalesListEntityPlaceholder()) {
                    leadManager.setSalesListEntityPlaceholderUrn(managerUrn.getSalesListEntityPlaceholder().toString());
                  }
                } else if (listEntity.getLeadManager().hasMemberUrn()) {
                  leadManager.setMemberUrn(listEntity.getLeadManager().getMemberUrn().toString());
                }
                leadManager.setLastModifiedBySeatUrn(lastModifierSeatUrn.toString());
                leadManager.setLastModifiedTime(System.currentTimeMillis());
                espressoListEntity.setLeadManager(leadManager);
              }

              if (listEntity.hasLeadOwner()) {
                LeadOwner leadOwner = new LeadOwner();
                if (listEntity.getLeadOwner().hasSeatUrn()) {
                  leadOwner.setSeatUrn(listEntity.getLeadOwner().getSeatUrn().toString());
                }
                leadOwner.setLastModifiedBySeatUrn(lastModifierSeatUrn.toString());
                leadOwner.setLastModifiedTime(System.currentTimeMillis());
                espressoListEntity.setLeadOwner(leadOwner);
              }

              if (listEntity.hasLeadRole()) {
                LeadRole leadRole = new LeadRole();
                if (listEntity.getLeadRole().hasRoleType()) {
                  checkArgument(
                      API_ROLE_TYPE_TO_ESPRESSO_ROLE_TYPE_MAPPING.containsKey(listEntity.getLeadRole().getRoleType()),
                      String.format(
                          "Unsupported RoleType field mismatch between restli schema and espresso schema: %s.",
                          listEntity.getLeadRole().getRoleType()));
                  leadRole.setRoleType(
                      API_ROLE_TYPE_TO_ESPRESSO_ROLE_TYPE_MAPPING.get(listEntity.getLeadRole().getRoleType()));
                }
                leadRole.setLastModifiedBySeatUrn(lastModifierSeatUrn.toString());
                leadRole.setLastModifiedTime(System.currentTimeMillis());
                espressoListEntity.setLeadRole(leadRole);
              }

              if (listEntity.hasLeadRelationshipStrength()) {
                LeadRelationshipStrength leadRelationshipStrength = new LeadRelationshipStrength();
                if (listEntity.getLeadRelationshipStrength().hasRelationshipStrength()) {
                  checkArgument(
                      API_RELATIONSHIP_STRENGTH_TYPE_TO_ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_MAPPING.containsKey(
                          listEntity.getLeadRelationshipStrength().getRelationshipStrength()), String.format(
                          "Unsupported RelationshipStrengthType field mismatch between restli schema and espresso schema: %s.",
                          listEntity.getLeadRelationshipStrength().getRelationshipStrength()));
                  leadRelationshipStrength.setRelationshipStrengthType(
                      API_RELATIONSHIP_STRENGTH_TYPE_TO_ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_MAPPING.get(
                          listEntity.getLeadRelationshipStrength().getRelationshipStrength()));
                }
                leadRelationshipStrength.setLastModifiedBySeatUrn(lastModifierSeatUrn.toString());
                leadRelationshipStrength.setLastModifiedTime(System.currentTimeMillis());
                espressoListEntity.setLeadRelationshipStrength(leadRelationshipStrength);
              }

              if (listEntity.hasPositionInLevel()) {
                espressoListEntity.setPositionInLevel(listEntity.getPositionInLevel());
              }
            }

            if (listEntity.hasLeadText()) {
              checkArgument(
                  Objects.equals(listEntity.getEntity().getEntityType(), SalesListEntityPlaceholderUrn.ENTITY_TYPE),
                  "LeadText field is only supported for salesListEntityPlaceholder entity type");
              LeadText leadText = new LeadText();
              if (listEntity.getLeadText().hasTextBody()) {
                leadText.setTextBody(listEntity.getLeadText().getTextBody());
              }
              leadText.setLastModifiedBySeatUrn(lastModifierSeatUrn.toString());
              leadText.setLastModifiedTime(System.currentTimeMillis());
              espressoListEntity.setLeadText(leadText);
            }

            Optional<ListSource> maybeListSource =
                Optional.ofNullable(listIdToTypeAndSourceMap.get(listEntity.getList().getIdAsLong()))
                    .flatMap(Pair::getSecond);
            if (maybeListSource.isPresent()) {
              if (LOW_PRIORITY_SEARCH_INDEX_UPDATE_LIST_SOURCES.contains(maybeListSource.get())) {
                espressoListEntity.setSearchIndexUpdatePriority(SearchIndexUpdatePriorityType.LOW);
              }
            }
            return new AbstractMap.SimpleEntry<>(listEntity.getEntity(), espressoListEntity);
          } catch (Exception e) {
            errorEntitiesMap.put(listEntity, e);
            return null;
          }
        }).filter(Objects::nonNull).collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
    return Pair.make(espressoEntitiesMap, errorEntitiesMap);
  }

  /**
   * Helper function get get compoundkey of list entity
   */
  private CompoundKey getCompoundKey(@NonNull ListEntity listEntity) {
    return new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity.getEntity());
  }

  /**
   * Helper function get get compoundkey of list entity
   */
  private CompoundKey getCompoundKey(@NonNull SalesListUrn listUrn, @NonNull Urn entityUrn) {
    return new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
  }

  /**
   * Helper function to return task to get count of each list id
   * @param listIds
   * @return
   */
  private Task<Map<Long, Long>> getListCountTask(
      @NonNull Set<Long> listIds) {
    //get current list counts for each list id
    List<Task<AbstractMap.SimpleEntry<Long, Long>>> getListsCountTasks =
        listIds.stream().map(listId -> _lssListDB.getListEntityCount(listId)
            .map(listCount -> new AbstractMap.SimpleEntry<>(listId, listCount))
            .recover(throwable -> {
              LOG.warn("Failed to get the total entity count of the list, just return the upper limit to stop the save", throwable);
              return new AbstractMap.SimpleEntry<>(listId, LIST_ENTITY_LIMIT);
            })).collect(Collectors.toList());
    return Task.par(getListsCountTasks).map(listCountEntries ->
        listCountEntries.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /**
   * Helper function to return task to get list type of each list id and check permission
   * @param listIds
   * @param seatId
   * @param errorListIdToStatusMap list id that does not exist or current seat does not have enough permission to
   * @return Pair of ListId to ListType and ListId to ListSource maps.
   */
  private Task<Map<Long, Pair<ListType, Optional<ListSource>>>> getListTypeAndSourceTask(
      @NonNull Set<Long> listIds, long seatId, @NonNull Map<Long, HttpStatus> errorListIdToStatusMap) {

    //get type and permission for each list id
    List<Task<AbstractMap.SimpleEntry<Long, Tuple3<ListType, Optional<ListSource>, AccessDecision>>>> getListTypeAndPermissionTasks =
        listIds.stream().map(listId ->  getListTypeAndSourceAndAccessDecision(UrnUtils.createSeatUrn(seatId), listId, AccessAction.UPDATE)
            .map(typeSourceAndPermission -> new AbstractMap.SimpleEntry<>(listId, typeSourceAndPermission))
            .recover(t -> {
              if (ExceptionUtils.isEntityNotFoundException(t)) {
                errorListIdToStatusMap.put(listId, HttpStatus.S_400_BAD_REQUEST);
              } else {
                errorListIdToStatusMap.put(listId, HttpStatus.S_500_INTERNAL_SERVER_ERROR);
              }
              return null;
            })).filter(Objects::nonNull)
            .collect(Collectors.toList());

    return Task.par(getListTypeAndPermissionTasks).map(
        typeAndPermissions -> {
          Map<Long, Pair<ListType, Optional<ListSource>>> listIdToTypeAndSourceMap = typeAndPermissions.stream().filter(Objects::nonNull)
              .collect(Collectors.toMap(Map.Entry::getKey, entry -> new Pair<>(entry.getValue()._1(), entry.getValue()._2())));
          Map<Long, AccessDecision> listIdToPermissionMap = typeAndPermissions.stream().filter(Objects::nonNull)
              .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()._3()));

          listIdToPermissionMap.keySet()
              .stream()
              .filter(listId -> listIdToPermissionMap.get(listId) != AccessDecision.ALLOWED)
              .forEach(listId -> errorListIdToStatusMap.put(listId, HttpStatus.S_403_FORBIDDEN));
          return listIdToTypeAndSourceMap;
        });
  }

  /**
   * Helper function to filter out list entities that do not need to be created
   * @param listIds
   * @param listIdToListEntityMap
   * @param listIdToExistingEntityCountMap
   * @param errorListIdToStatusMap list id that does not exist or current seat does not have enough permission to
   * @param listSizeLimit the maximum number of entities a list can have
   * @return list entities that will not exceed count limit and pass the permission check
   */
  private Set<ListEntity> filteredListEntities(@NonNull Set<Long> listIds, @NonNull Map<Long, Set<ListEntity>> listIdToListEntityMap,
      @NonNull Map<Long, Long> listIdToExistingEntityCountMap,
      @NonNull Map<Long, HttpStatus> errorListIdToStatusMap,
      long listSizeLimit) {
    return listIds.stream()
        .filter(listId -> !errorListIdToStatusMap.containsKey(listId)
            && (listIdToListEntityMap.get(listId).size() + listIdToExistingEntityCountMap.get(listId)) <= listSizeLimit)
        .map(listId -> listIdToListEntityMap.get(listId))
        .flatMap(Set::stream)
        .collect(Collectors.toSet());
  }

  /**
   * Helper function to build createResponseMap for batch create
   * @param listEntities all list entities from request
   * @param createResponseMap response from actual created list
   * @param errorListIdToStatusMap error list ids and their corresponding return status
   * @param listEntityNeedToCreate list entities that need to be created after filter out those have errors or exceed the limit
   * @return Response for batch create
   */
  private Map<CompoundKey, CreateResponse> buildCreateResponseMap(@NonNull List<ListEntity> listEntities,
      @NonNull Map<CompoundKey, CreateResponse> createResponseMap,
      @NonNull Map<Long, HttpStatus> errorListIdToStatusMap, @NonNull Set<ListEntity> listEntityNeedToCreate) {
    Map<CompoundKey, CreateResponse> responseMap = Maps.newHashMap(createResponseMap);

    listEntities.forEach(listEntity -> {
      CompoundKey compoundKey = getCompoundKey(listEntity);
      Long listId = listEntity.getList().getIdAsLong();
      HttpStatus errorStatus = errorListIdToStatusMap.get(listId);
      // if errorListIdToStatusMap contains key listId, return error message
      if (errorStatus != null) {
        responseMap.put(compoundKey, new CreateResponse(
            new RestLiServiceException(errorListIdToStatusMap.get(listId), "Error retrieving list " + listId)));
      } else if (!listEntityNeedToCreate.contains(listEntity)) {
        //listEntity can't be created since list count exceed upper limit, will return 412(exceed upper limit)
        responseMap.put(compoundKey, new CreateResponse(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED,
            "hit the upper limit of the list " + listId)));
      }
    });
    return responseMap;
  }

  /**
   * Helper function to build buildUpdateResponseMap for batch update
   * @param listEntities all list entities from request
   * @param updateResponseMap response from actual updated list
   * @param errorListIdToStatusMap error list ids and their corresponding return status
   * @param errorListEntityToStatusMap
   * @return Response for batch update
   */
  private Map<CompoundKey, UpdateResponse> buildUpdateResponseMap(@NonNull List<ListEntity> listEntities,
      @NonNull Map<CompoundKey, UpdateResponse> updateResponseMap,
      @NonNull Map<Long, HttpStatus> errorListIdToStatusMap,
      @NonNull Map<CompoundKey, HttpStatus> errorListEntityToStatusMap) {
    Map<CompoundKey, UpdateResponse> responseMap = Maps.newHashMap(updateResponseMap);

    listEntities.forEach(listEntity -> {
      CompoundKey compoundKey = getCompoundKey(listEntity);
      Long listId = listEntity.getList().getIdAsLong();
      HttpStatus errorStatus = errorListIdToStatusMap.get(listId);
      // if errorListIdToStatusMap contains key listId, return error status
      if (errorStatus != null) {
        responseMap.put(compoundKey, new UpdateResponse(errorStatus));
      } else {
        errorStatus = errorListEntityToStatusMap.get(compoundKey);
        if (errorStatus != null) {
          responseMap.put(compoundKey, new UpdateResponse(errorStatus));
        }
      }
    });

    return responseMap;
  }

  /**
   * Service to check the ownership and then delete the list entity
   * @param compoundKey compoundKey including the list urn and the entity urn
   * @param seat seat information for the ownership check
   * @param listIdToAccessDecisionMap map of listId to (listType, accessDecision) pair.
   * If listIdToAccessDecisionMap is empty, code will try to fetch the (listType, accessDecision) pair.
   * If listIdToAccessDecisionMap is not empty and has a value for the listId, (listType, accessDecision) will be
   * picked from the map. If no value is found for listId, Access check will be bypassed.
   * @param isLowPriorityOperation Sets the espresso quota usage priority to LOW for deleting list entity.
   * @return boolean to tell if the deletion succeeds
   */
  private Task<UpdateResponse> deleteListEntityHelper(@NonNull CompoundKey compoundKey, @NonNull SeatUrn seat,
      @NonNull Map<Long, Pair<ListType, AccessDecision>> listIdToAccessDecisionMap, boolean isLowPriorityOperation) {
    String listUrnString = compoundKey.getPart(ServiceConstants.LIST_COMPOUND_KEY).toString();
    String entityUrnString = compoundKey.getPart(ServiceConstants.ENTITY_COMPOUND_KEY).toString();

    if (listUrnString == null || entityUrnString == null) {
      String message = "No correct listUrn or entity Urn available for deletion " + compoundKey;
      return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, message));
    }

    SalesListUrn listUrn;
    Urn entityUrn;

    try {
      listUrn = SalesListUrn.deserialize(listUrnString);
      entityUrn = Urn.createFromString(entityUrnString);
    } catch (URISyntaxException e) {
      String message = "Fail to delete since the entity type of the list urn is not correct " + listUrnString;
      LOG.error(message);
      return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, message, e));
    }
    String entityType = entityUrn.getEntityType();
    if (!SUPPORTED_ENTITY_TYPES.contains(entityType)) {
      String message = "Fail to delete since the key type of the entity urn is not correct " + entityUrn;
      LOG.error(message);
      return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, message));
    }

    long listId = listUrn.getIdAsLong();

    if (!listIdToAccessDecisionMap.containsKey(listId)) {
      return getListTypeAndAccessDecision(seat, listId, AccessAction.UPDATE).flatMap(
          typeAndPermission -> deleteListEntity(seat, entityUrn, listId, typeAndPermission, isLowPriorityOperation))
          .recoverWith(t -> {
            if (ExceptionUtils.isEntityNotFoundException(t)) {
              LOG.warn("The list does not exist {}", listId, t);
              return Task.value(new UpdateResponse(HttpStatus.S_400_BAD_REQUEST));
            } else {
              return Task.failure(t);
            }
          });
    } else if (listIdToAccessDecisionMap.get(listId) != null) {
      return deleteListEntity(seat, entityUrn, listId, listIdToAccessDecisionMap.get(listId), isLowPriorityOperation);
    } else {
      // This can happen if the list was already deleted earlier and we are now trying to delete all its associated
      // list entities.
      return _lssListDB.deleteListEntity(listId, entityUrn, isLowPriorityOperation)
          .map(isDeleted -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
    }
  }

  private Task<UpdateResponse> deleteListEntity(@NonNull SeatUrn seat,
      @NonNull Urn entityUrn, long listId, @NonNull Pair<ListType, AccessDecision> typeAndPermission, boolean isLowPriorityOperation) {
    if (typeAndPermission.getSecond() == AccessDecision.ALLOWED) {
      return _lssListDB.deleteListEntity(listId, entityUrn, isLowPriorityOperation).flatMap(isDeleted -> {
        if (isDeleted) {
          //if lastModifiedTime is not set, need to update last modified time to current time.
          return updateLastModifiedTimeAndModifier(listId, null, seat.toString()).map(
              updateSucceed -> new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
        } else {
          //Check stackoverflow and it seems we should always return 204 instead 404 even this deletion does not actually delete anything
          return Task.value(new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
        }
      });
    } else {
      return Task.value(new UpdateResponse(HttpStatus.S_403_FORBIDDEN));
    }
  }

  /**
   * Batch get list entities
   * @param seatId seat used for permission check
   * @param keys keys of the entities to be retrieved
   * @return the batch result with compound key to list entity mapping
   */
  public Task<BatchResult<CompoundKey, ListEntity>> batchGetListEntities(long seatId, @NonNull Set<CompoundKey> keys) {

    Set<Long> listIds = keys.stream()
        .map(key -> ((SalesListUrn) key.getPart(LIST_COMPOUND_KEY)).getIdAsLong())
        .collect(Collectors.toSet());

    if (listIds.isEmpty()) {
      return Task.value(new BatchResult<>(Collections.emptyMap(), Collections.emptyMap()));
    }

    return checkReadAccessForLists(seatId, listIds).flatMap(accessMapPair -> {
      Map<Long, Boolean> accessCheckResultMap = accessMapPair.getFirst();
      List<Pair<SalesListUrn, Urn>> listEntityKeysWithAccess = keys.stream()
          .filter(key -> {
            SalesListUrn listUrn = (SalesListUrn) key.getPart(LIST_COMPOUND_KEY);
            return accessCheckResultMap.getOrDefault(listUrn.getIdAsLong(), Boolean.FALSE);
          })
          .map(key -> new Pair<>((SalesListUrn) key.getPart(LIST_COMPOUND_KEY), (Urn) key.getPart(ENTITY_COMPOUND_KEY)))
          .collect(toList());

      Task<Map<CompoundKey, ListEntity>> batchGetListEntitiesTask = listEntityKeysWithAccess.isEmpty()
          ? Task.value(Collections.emptyMap())
          : _lssListDB.batchGetListEntities(listEntityKeysWithAccess)
              .map(pairListEntityMap -> pairListEntityMap.entrySet().stream()
                  .collect(Collectors.toMap(
                    entry -> getCompoundKey(entry.getKey().getFirst(), entry.getKey().getSecond()),
                    entry -> convertEspressoListEntityToRestliListEntity(entry.getValue(), entry.getKey().getFirst(),
                        entry.getKey().getSecond()))))
            .onFailure(t -> LOG.error("Fail to batch get list entities with keys: {}", keys, t));

      return batchGetListEntitiesTask.map(resultMap -> {
        Map<CompoundKey, RestLiServiceException> errorMap = new HashMap<>();
        Map<Long, RestLiServiceException> accessCheckErrorMap = accessMapPair.getSecond();
        keys.forEach(key -> {
          if (!resultMap.containsKey(key)) {
            SalesListUrn listUrn = (SalesListUrn) key.getPart(LIST_COMPOUND_KEY);
            long listId = listUrn.getIdAsLong();
            RestLiServiceException exception = accessCheckResultMap.containsKey(listId)
                ? new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, String.format("Seat %d has no access to list %d", seatId, listId))
                : accessCheckErrorMap.get(listId);
            errorMap.put(key, exception);
          }
        });
        return new BatchResult<>(resultMap, errorMap);
      });
    });
  }

  /**
   * Batch Partial Update List Entities
   * @param seat the seat id of the viewer
   * @param contract the contract of the viewer
   * @param listEntitiesToUpdateRequestMap collection of list entities to update.
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchUpdateListEntities(@NonNull SeatUrn seat,
      @NonNull ContractUrn contract,
      @NonNull Map<CompoundKey, PatchRequest<ListEntity>> listEntitiesToUpdateRequestMap) {
    if (listEntitiesToUpdateRequestMap.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    // First, Get all the existing list entities.
    return batchGetListEntities(seat.getIdAsLong(), listEntitiesToUpdateRequestMap.keySet()).flatMap(
        existingListEntitiesMap -> {
          List<ListEntity> listEntitiesToUpdate = Lists.newArrayList();
          Map<Long, HttpStatus> errorListIdToStatusMap = Maps.newHashMap();
          Map<CompoundKey, HttpStatus> errorListEntityToStatusMap = Maps.newHashMap();

          // Second, Patch the existing list entities with the fields in the Patch Request.
          listEntitiesToUpdateRequestMap.forEach((compoundKey, listEntityInRequest) -> {
            ListEntity existingListEntity = existingListEntitiesMap.get(compoundKey);
            if (existingListEntity == null) {
              errorListEntityToStatusMap.put(compoundKey, existingListEntitiesMap.getErrors().get(compoundKey).getStatus());
            } else {
              try {
                PatchApplier.applyPatch(existingListEntity, listEntityInRequest);
                listEntitiesToUpdate.add(existingListEntity);
              } catch (DataProcessingException e) {
                errorListEntityToStatusMap.put(compoundKey, HttpStatus.S_400_BAD_REQUEST);
              }
            }
          });

          Map<Long, List<ListEntity>> listIdToListEntitiesToUpdateMap = listEntitiesToUpdate.stream()
              .collect(groupingBy(listEntity -> listEntity.getList().getIdAsLong(), Collectors.toList()));

          Set<Long> listIds = listIdToListEntitiesToUpdateMap.keySet();

          Task<Map<Long, Pair<ListType, Optional<ListSource>>>> listTypeAndSourceTask =
              getListTypeAndSourceTask(listIds, seat.getIdAsLong(), errorListIdToStatusMap);

          return listTypeAndSourceTask.flatMap((listIdToTypeAndSourceMap) -> {
            // For now, batch updates are allowed only for Lead Lists, Account Lists and Account Map.
            listIdToTypeAndSourceMap.forEach((listId, listTypeOptionalSourcePair) -> {
              if (!ServiceConstants.BATCH_UPDATE_ALLOWED_LIST_TYPE.contains(listTypeOptionalSourcePair.getFirst())) {
                errorListIdToStatusMap.put(listId, HttpStatus.S_405_METHOD_NOT_ALLOWED);
              }
            });

            // Filter out listId's that have errors.
            listIdToListEntitiesToUpdateMap.keySet().removeAll(errorListIdToStatusMap.keySet());

            // Within each list partition, create batches of size recommended by espresso and use the multi-post operation.
            // Batches belonging to different lists are processed in parallel.
            return Task.par(listIdToListEntitiesToUpdateMap.entrySet()
                .stream()
                .map(
                    entry -> batchUpdateListEntities(entry.getKey(), entry.getValue(), seat, contract, listIdToTypeAndSourceMap))
                .collect(Collectors.toList())).map(listEntitiesUpdationResponses -> {
              Map<CompoundKey, UpdateResponse> responseMap = listEntitiesUpdationResponses.stream()
                  .flatMap(m -> m.entrySet().stream())
                  .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

              return buildUpdateResponseMap(listEntitiesToUpdate, responseMap, errorListIdToStatusMap,
                  errorListEntityToStatusMap);
            }).recover(t -> {
              LOG.error("Failed to update list entities", t);
              return listEntitiesToUpdateRequestMap.keySet()
                  .stream()
                  .map(ck -> new AbstractMap.SimpleEntry<>(ck,
                      new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR)))
                  .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            });
          });
        });
  }

  /**
   * Check if the seat has the read permission on each of the given lists.
   * @param seatId seat id
   * @param listIds Set of list ids
   * @return a pair of maps, first one containing successful access check results, second one containing errors.
   * It is guaranteed that each list id has an entry in the map.
   */
  private Task<com.linkedin.util.Pair<Map<Long, Boolean>, Map<Long, RestLiServiceException>>> checkReadAccessForLists(
      long seatId, @NonNull Set<Long> listIds) {
    Map<Long, RestLiServiceException> errorMap = new HashMap<>();
    Task<Map<Long, Boolean>> accessMapTask = com.linkedin.omni.utils.common.parseq.ParseqUtils.batchTask(listIds,
        listId -> getListTypeAndAccessDecision(UrnUtils.createSeatUrn(seatId), listId, AccessAction.READ)
            .map(typeAndPermission -> typeAndPermission.getSecond() == AccessDecision.ALLOWED)
            .onFailure(t -> {
              if (ExceptionUtils.isEntityNotFoundException(t)) {
                errorMap.put(listId, new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
                    String.format("list %d is not found. ", listId), t));
              } else {
                errorMap.put(listId, new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                    String.format("Failed to get list type and access decision for list id %d, seat id %d", listId, seatId), t));
              }
            }));

    return accessMapTask.map(accessMap -> com.linkedin.util.Pair.of(accessMap, errorMap));
  }

  /**
   * checks to see if the given viewer has permission to perform an action on the given list
   * checks both ownership and sharing platform role
   * @param viewer viewer urn, either SeatUrn or MemberUrn
   * @param listId list id
   * @param accessAction the type of access action that is trying to be performed
   * @return return a pair with the list type and permission
   */
  private Task<Pair<ListType, AccessDecision>> getListTypeAndAccessDecision(@NonNull Urn viewer, long listId, @NonNull AccessAction accessAction) {
    return getListTypeAndSourceAndAccessDecision(viewer, listId, accessAction).map(res -> Pair.make(res._1(), res._3()));
  }

  /**
   * checks to see if the given viewer has permission to perform an action on list entities in the given list
   * checks both ownership and sharing platform role
   * @param viewer viewer urn, either SeatUrn or MemberUrn
   * @param listId list id
   * @param accessAction the type of access action that is trying to be performed
   * @return return a tuple with list type, optional list source and access decision
   */
  private Task<Tuple3<ListType, Optional<ListSource>, AccessDecision>> getListTypeAndSourceAndAccessDecision(
      @NonNull Urn viewer, long listId, @NonNull AccessAction accessAction) {
    boolean isSeatViewer = UrnUtils.isSeatUrn(viewer);

    return _lssListDB.getList(listId).flatMap(list -> {
      PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(list.listType);
      SalesListUrn salesListUrn = UrnUtils.createSalesListUrn(listId);

      Task<Map<String, Boolean>> batchGetRelationshipMapLixesTask = isSeatViewer
          ? _lixService.batchGetIsEEPLixEnabledForSeat(UrnUtils.createSeatUrn(viewer), RELATIONSHIP_MAP_LIX_KEY_SET)
          : Task.value(Collections.emptyMap());

      if (policyType == PolicyType.ACCOUNT_MAP) {
        return batchGetRelationshipMapLixesTask
            .map(lixMap -> lixMap.values().stream().anyMatch(BooleanUtils::isTrue))
            .flatMap(isRelationshipMapLixEnabled -> {
              SubResourceType subResourceType =
                  isRelationshipMapLixEnabled ? SubResourceType.LIST_ENTITY : SubResourceType.NONE;
              return _aclServiceDispatcher.checkAccessDecision(viewer, policyType, salesListUrn, accessAction,
                      subResourceType)
                  .map(accessDecision -> new Tuple3<>(list.listType, Optional.ofNullable(list.listSource),
                      accessDecision));
            });
      } else {
        return _aclServiceDispatcher.checkAccessDecision(viewer, policyType, salesListUrn, accessAction)
            .map(accessDecision -> new Tuple3<>(list.listType, Optional.ofNullable(list.listSource), accessDecision));
      }
    }).recoverWith(t -> {
      // If the List was not found, it may be that entities are being added to a list is part of the List CSV
      // Import flow and doesn't exist yet.  In that case, check the ListCsvImport for an access decision.
      // Only Update actions are currently allowed as there this no use-case for Read, Append etc
      if (ExceptionUtils.isEntityNotFoundException(t) && accessAction == AccessAction.UPDATE && isSeatViewer) {
        return getListCsvImportListTypeAndAccessDecision(viewer.getIdAsLong(), listId, accessAction);
      }
      return Task.failure(t);
    });
  }

  // give a null lastModifiedTime will update the last modified time as current time.
  private Task<Boolean> updateLastModifiedTimeAndModifier(long listId, @Nullable Long lastModifiedTime, @NonNull CharSequence modifier) {
    com.linkedin.sales.espresso.List list = new com.linkedin.sales.espresso.List();
    list.lastModifiedTime = (lastModifiedTime != null) ? lastModifiedTime : System.currentTimeMillis();
    list.lastModifiedBySeatUrn = modifier;
    return _lssListDB.updateList(listId, list);
  }

  /**
   * Helper method to create a RestliServiceException that wraps errors for S_404_NOT_FOUND, S_403_FORBIDDEN, or S_500_INTERNAL_SERVER_ERROR
   * @param throwable the cause of the error as a Throwable
   * @param messageForNotFoundError message to be included in the exception for S_404_NOT_FOUND
   * @param messageForInternalServerError message to be included in the exception for S_500_INTERNAL_SERVER_ERROR
   * @return RestliServiceException
   */
  private static RestLiServiceException createRestliServiceException(Throwable throwable,
      String messageForNotFoundError, String messageForInternalServerError) {
    if (ExceptionUtils.isEntityNotFoundException(throwable)) {
      return new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, messageForNotFoundError, throwable);
    } else if (throwable instanceof RestLiServiceException
        && ((RestLiServiceException) throwable).getStatus() == HttpStatus.S_403_FORBIDDEN) {
      return (RestLiServiceException) throwable;
    } else {
      return new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, messageForInternalServerError,
          throwable);
    }
  }

  /**
   * Convert an espresso ListEntity to the corresponding restli entity that can be consumed by the client
   * @param espressoListEntity ListEntity from espresso
   * @param salesListUrn sales list urn of the list that contains the entity
   * @param entityUrn the urn of the entity
   * @return the corresponding restli entity
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "PRIORITY_TYPE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get value is not null")
  private static ListEntity convertEspressoListEntityToRestliListEntity(
      com.linkedin.sales.espresso.ListEntity espressoListEntity, SalesListUrn salesListUrn, Urn entityUrn) {
    ListEntity restliEntity = new ListEntity();
    restliEntity.setCreatedAt(espressoListEntity.createdTime);
    restliEntity.setCreator(new SeatUrn(espressoListEntity.ownerSeatId));
    restliEntity.setList(salesListUrn);
    restliEntity.setEntity(entityUrn);
    // Because lastModifiedAt field was added to the espresso document in version 2. Use createdTime as
    // lastModifiedTime if lastModifiedTime is absent.
    // To address bug in seatTransfer where lastModifiedTime is not updated to be after createdTime, use createdTime to ensure list entities sorted correctly
    // https://jira01.corp.linkedin.com:8443/browse/LSS-88823
    restliEntity.setLastModifiedAt(espressoListEntity.lastModifiedTime != null && espressoListEntity.lastModifiedTime >= espressoListEntity.createdTime
        ? espressoListEntity.lastModifiedTime
        : espressoListEntity.createdTime);

    if (espressoListEntity.priority != null) {
      checkArgument(PRIORITY_TYPE_SERVICE_TO_ESPRESSO_MAPPING.inverse().containsKey(espressoListEntity.priority),
          String.format("Unsupported espressoListEntity priority [%s]", espressoListEntity.priority));

      ListEntityPriorityInfo priorityInfo = new ListEntityPriorityInfo();
      priorityInfo.setPriority(PRIORITY_TYPE_SERVICE_TO_ESPRESSO_MAPPING.inverse().get(espressoListEntity.priority));
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoListEntity.getPriorityLastModifiedSeatUrn()));
      auditStamp.setTime(espressoListEntity.priorityLastModifiedTime);
      // We dont distinguish Priority created time from last modified time. So, we set the same time to both the fields
      // since both fields are mandatory in the ListEntity response schema.
      priorityInfo.setCreated(auditStamp);
      priorityInfo.setLastModified(auditStamp);

      restliEntity.setPriorityInfo(priorityInfo);
    }

    if (Objects.nonNull(espressoListEntity.getLeadManager())) {
      com.linkedin.saleslist.LeadManager restliLeadManager = new com.linkedin.saleslist.LeadManager();
      LeadManager espressoLeadManager = espressoListEntity.getLeadManager();
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoLeadManager.getLastModifiedBySeatUrn()));
      //We don't differentiate between created and lastmodified time.
      auditStamp.setTime(espressoLeadManager.getLastModifiedTime());
      restliLeadManager.setCreated(auditStamp);
      restliLeadManager.setLastModified(auditStamp);

      if (Objects.nonNull(espressoLeadManager.getMemberUrn())) {
        MemberUrn managerMemberUrn = createMemberUrn(espressoLeadManager.getMemberUrn());
        //Todo: LSS-76002 Cleanup MemberUrn field after readers/writers have migrated to EntityUrn field.
        restliLeadManager.setMemberUrn(managerMemberUrn);
        restliLeadManager.setEntityUrn(LeadManagerEntityUrn.createWithMember(managerMemberUrn));
      }
      if (Objects.nonNull(espressoLeadManager.getSalesListEntityPlaceholderUrn())) {
        restliLeadManager.setEntityUrn(
            LeadManagerEntityUrn.createWithSalesListEntityPlaceholder(
                createSalesListEntityPlaceholderUrn(espressoLeadManager.getSalesListEntityPlaceholderUrn())));
      }

      restliEntity.setLeadManager(restliLeadManager);
    }

    if (Objects.nonNull(espressoListEntity.getLeadOwner())) {
      com.linkedin.saleslist.LeadOwner restliLeadOwner = new com.linkedin.saleslist.LeadOwner();
      LeadOwner espressoLeadOwner = espressoListEntity.getLeadOwner();
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoLeadOwner.getLastModifiedBySeatUrn()));
      //We don't differentiate between created and lastmodified time.
      auditStamp.setTime(espressoLeadOwner.getLastModifiedTime());
      restliLeadOwner.setCreated(auditStamp);
      restliLeadOwner.setLastModified(auditStamp);

      if (Objects.nonNull(espressoLeadOwner.getSeatUrn())) {
        restliLeadOwner.setSeatUrn(UrnUtils.createSeatUrn(espressoLeadOwner.getSeatUrn()));
      }

      restliEntity.setLeadOwner(restliLeadOwner);
    }

    if (Objects.nonNull(espressoListEntity.getLeadRole())) {
      com.linkedin.saleslist.LeadRole restliLeadRole = new com.linkedin.saleslist.LeadRole();
      LeadRole espressoLeadRole = espressoListEntity.getLeadRole();
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoLeadRole.getLastModifiedBySeatUrn()));
      //We don't differentiate between created and lastmodified time.
      auditStamp.setTime(espressoLeadRole.getLastModifiedTime());
      restliLeadRole.setCreated(auditStamp);
      restliLeadRole.setLastModified(auditStamp);

      if (Objects.nonNull(espressoLeadRole.getRoleType())) {
        checkArgument(ESPRESSO_ROLE_TYPE_TO_API_ROLE_TYPE_MAPPING.containsKey(espressoLeadRole.getRoleType()),
            String.format("Unsupported espressoListEntity LeadRole.RoleType [%s]", espressoLeadRole.getRoleType()));
        restliLeadRole.setRoleType(ESPRESSO_ROLE_TYPE_TO_API_ROLE_TYPE_MAPPING.get(espressoLeadRole.getRoleType()));
      }
      restliEntity.setLeadRole(restliLeadRole);
    }

    if (Objects.nonNull(espressoListEntity.getLeadRelationshipStrength())) {
      com.linkedin.saleslist.LeadRelationshipStrength restliLeadRelationshipStrength =
          new com.linkedin.saleslist.LeadRelationshipStrength();
      LeadRelationshipStrength espressoLeadRelationshipStrength = espressoListEntity.getLeadRelationshipStrength();
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoLeadRelationshipStrength.getLastModifiedBySeatUrn()));
      //We don't differentiate between created and lastmodified time.
      auditStamp.setTime(espressoLeadRelationshipStrength.getLastModifiedTime());
      restliLeadRelationshipStrength.setCreated(auditStamp);
      restliLeadRelationshipStrength.setLastModified(auditStamp);

      if (Objects.nonNull(espressoLeadRelationshipStrength.getRelationshipStrengthType())) {
        checkArgument(ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_TO_API_RELATIONSHIP_STRENGTH_TYPE_MAPPING.containsKey(
            espressoLeadRelationshipStrength.getRelationshipStrengthType()),
            String.format("Unsupported espressoListEntity LeadRelationshipStrength.RelationshipStrengthType [%s]",
                espressoLeadRelationshipStrength.getRelationshipStrengthType()));
        restliLeadRelationshipStrength.setRelationshipStrength(
            ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_TO_API_RELATIONSHIP_STRENGTH_TYPE_MAPPING.get(
                espressoLeadRelationshipStrength.getRelationshipStrengthType()));
      }
      restliEntity.setLeadRelationshipStrength(restliLeadRelationshipStrength);
    }

    if (Objects.nonNull(espressoListEntity.getPositionInLevel())) {
      restliEntity.setPositionInLevel(espressoListEntity.getPositionInLevel());
    }

    if (Objects.nonNull(espressoListEntity.getLeadText())) {
      com.linkedin.saleslist.LeadText restliLeadText = new com.linkedin.saleslist.LeadText();
      LeadText espressoLeadText = espressoListEntity.getLeadText();
      AuditStamp auditStamp = new AuditStamp();
      auditStamp.setActor(UrnUtils.createSeatUrn(espressoLeadText.getLastModifiedBySeatUrn()));
      //We don't differentiate between created and lastmodified time.
      auditStamp.setTime(espressoLeadText.getLastModifiedTime());
      restliLeadText.setCreated(auditStamp);
      restliLeadText.setLastModified(auditStamp);
      restliLeadText.setTextBody(espressoLeadText.getTextBody().toString(), SetMode.IGNORE_NULL);
      restliEntity.setLeadText(restliLeadText);
    }

    AccountMapTier accountMapTier = INVERTED_ACCOUNT_MAP_TIER_INTEGER_MAP.get(espressoListEntity.tier);
    if (accountMapTier != null) {
      restliEntity.setTier(accountMapTier);

      // This is to handle the cases where the account maps were created before tier position support was added.
      restliEntity.setPositionInTier((int) Math.min(ACCOUNT_MAP_TIER_ENTITY_LIMIT - 1, espressoListEntity.sortOrder));
    }

    return restliEntity;
  }

  /**
   * Determines if a ListCsvImport may be accessed.  Only Update actions are currently allowed as there this
   * no use-case for Read, Append etc
   * @param seatId the seat accessing the List CSV Import
   * @param listId the list ID associated with the List CSV Import
   * @return the access decision, optional list source and default list type.
   */
  private Task<Tuple3<ListType, Optional<ListSource>, AccessDecision>> getListCsvImportListTypeAndAccessDecision(
      long seatId, long listId, AccessAction accessAction) {

    if (accessAction != AccessAction.UPDATE) {
      return Task.value(new Tuple3<>(DEFAULT_LIST_CSV_IMPORT_LIST_TYPE, Optional.of(DEFAULT_LIST_CSV_IMPORT_LIST_SOURCE), AccessDecision.DENIED));
    }

    return _lssListDB.getListCsvImportsBySalesList(listId)
        .map(listCsvImport -> {
          Optional<ListSource> csvImportListSource =
              Optional.ofNullable(ESPRESSO_LIST_SOURCES_TO_ESPRESSO_CSV_IMPORT_LIST_SOURCES_MAPPING.inverse().get(listCsvImport.csvImportListSource));
          if (UrnUtils.createSeatUrn(listCsvImport.creatorSeatUrn).getSeatIdEntity() == seatId) {
            return new Tuple3<>(DEFAULT_LIST_CSV_IMPORT_LIST_TYPE, csvImportListSource, AccessDecision.ALLOWED);
          }
          return new Tuple3<>(DEFAULT_LIST_CSV_IMPORT_LIST_TYPE, csvImportListSource, AccessDecision.DENIED);
        });
  }
}
