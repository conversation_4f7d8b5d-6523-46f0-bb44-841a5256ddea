package com.linkedin.sales.service.list;

import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.workflow.constants.Constants;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.saleslist.ListEntitySummary;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import com.linkedin.talent.decorator.PathSpecSet;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesListEntitySummaryService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesListEntitySummaryService.class);

  private static final PathSpecSet LIST_SEAT_FINDER_REQUIRED_FIELDS =
      PathSpecSet.of(com.linkedin.saleslist.List.fields().id(), com.linkedin.saleslist.List.fields().creatorContract());

  private LssListDB _lssListDB;
  private SalesListService _salesListService;
  private SalesSeatClient _salesSeatClient;

  public SalesListEntitySummaryService(LssListDB lssListDB, SalesListService salesListService, SalesSeatClient salesSeatClient) {
    _lssListDB = lssListDB;
    _salesListService = salesListService;
    _salesSeatClient = salesSeatClient;
  }

  /**
   * service to return the counts for the entities, including the lists owned/ shared with the user
   * @param entityUrns set of entities, could be either Member Urn or Organization Urn
   * @param contract contract for the user
   * @param seat seat for the user
   * @return the map between Entity urn and ListEntitySummary
   */
  public Task<Map<Urn, ListEntitySummary>> batchGetEntityCounts(@NonNull Set<Urn> entityUrns,
      @NonNull ContractUrn contract, @NonNull SeatUrn seat) {
    if (entityUrns.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    long seatId = seat.getSeatIdEntity();

    return isSeatCRMEntitled(seat, contract).flatMap(crmEntitled -> {
      // The Urn Set could only be of one type of ListType for each batchGet
      ListType listType = getListTypeForEntity(entityUrns.iterator().next());
      // We only include visible lists into list count.
      // SYSTEM lists are not visible to end users. MANUAL and LINKEDIN_SALES_INSIGHT lists are always visible.
      Set<ListSource> listSources = new HashSet<>(Constants.VISIBLE_LIST_SOURCES);
      // CRM related lists are visible only if seat has CRM_BASIC entitlements.
      if (!crmEntitled) {
        listSources.remove(ListSource.CRM_BLUEBIRD);
        listSources.remove(ListSource.CRM_AT_RISK_OPPORTUNITY);
        listSources.remove(ListSource.CRM_SYNC);
        listSources.remove(ListSource.CRM_PERSON_ACCOUNT);
      }
      // Lists that are owned by or shared with the user
      Task<Pair<Integer, java.util.List<com.linkedin.saleslist.List>>> getListTask =
          _salesListService.getListsForSeat(seatId, listType, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, null,
              listSources, null, ServiceConstants.DEFAULT_START, ServiceConstants.GET_ALL_COUNT, LIST_SEAT_FINDER_REQUIRED_FIELDS);
      Task<Set<Long>> totalListTask = getListTask.map(
          listPair -> listPair.getSecond().stream().map(list -> list.getId()).collect(Collectors.toSet()));
      //List sharing could happen with multiple contracts, so to fetch the listEntity count for each of those contracts
      Task<Set<Long>> getListOwnerContractIdsTask = getListTask.map(listPair -> listPair.getSecond()
          .stream()
          .filter(com.linkedin.saleslist.List::hasCreatorContract)
          .map(list -> list.getCreatorContract().getContractIdEntity())
          .collect(Collectors.toSet()));
      //Get listIds for entity within each contract
      Task<List<Pair<Urn, List<Long>>>> listIdsForEntitiesTask = getListOwnerContractIdsTask.flatMap(
          listOwnerContractIds -> Task.par(entityUrns.stream()
              .map(entity -> getListIdsForEntityWithUrn(entity, listOwnerContractIds))
              .collect(Collectors.toList())));

      return Task.par(totalListTask, listIdsForEntitiesTask)
          .map((viewerListSet, listIdLists) -> listIdLists.stream()
                .collect(Collectors.toMap(Pair::getFirst, e -> new ListEntitySummary().setListCount(
                    (int) e.getSecond().stream().filter(viewerListSet::contains).count()))))
          .onFailure(throwable -> LOG.error("Fail to find the count for the list entities {}, contract {}, seat {}",
              entityUrns, contract, seat, throwable));
    });
  }

  // Get the ListType (LEAD/ ACCOUNT) of entity based on entityUrn
  private ListType getListTypeForEntity(@NonNull Urn entityUrn) {
    switch (entityUrn.getEntityType()) {
      case MemberUrn.ENTITY_TYPE:
        return ListType.LEAD;
      case OrganizationUrn.ENTITY_TYPE:
        return ListType.ACCOUNT;
      default: {
        LOG.error("Unknown ListType: {}", entityUrn);
        throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Unknown ListType.");
      }
    }
  }

  // Get the listIds an entity belongs to the given contracts
  private Task<Pair<Urn, List<Long>>> getListIdsForEntityWithUrn(@NonNull Urn entityUrn,
      Set<Long> contractIds) {
     Task<List<Pair<Urn, List<Long>>>> tasks =  Task.par(contractIds.stream().map(contractId ->  _lssListDB.getListIdsForEntity(entityUrn, contractId, null)
        .map(listIds -> new Pair<>(entityUrn, listIds))
        .recover(t -> {
          if (t instanceof EntityNotFoundException) {
            return new Pair<>(entityUrn, Collections.emptyList());
          } else {
            LOG.error("Fail to get listIds for entity {} in contract {}", entityUrn.getId(), contractId, t);
            return new Pair<>(entityUrn, Collections.emptyList());
          }
        })).collect(Collectors.toList()));
        return tasks.map(pairs ->
        new Pair<>(entityUrn, pairs.stream().map(pair -> pair.getSecond()).flatMap(List::stream).collect(Collectors.toList())));
  }

  // check if seat has CRM_BASIC entitlement
  private Task<Boolean> isSeatCRMEntitled(SeatUrn seatUrn, ContractUrn contractUrn) {
    EnterpriseApplicationUsageUrn viewer =
        new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(), "getSeatEntitlements");

    return _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), contractUrn, viewer, new PathSpec[]{SalesSeat.fields().entitlements()})
        .map(seat -> seat.getEntitlements().contains(SalesEntitlement.CRM_BASICS));
  }
}
