package com.linkedin.sales.service.list;

import com.linkedin.data.template.LongArray;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.TorrentClient;


/**
 * Service for generating an ID for a new {@link com.linkedin.saleslist.List}
 */
public class SalesListIdService {

  private final TorrentClient _torrentClient;

  public SalesListIdService(TorrentClient torrentClient) {
    _torrentClient = torrentClient;
  }

  /**
   * Generate a new globally unique ID to identity a {@link com.linkedin.saleslist.List}
   * @return the new ID
   */
  public Task<Long> generateNextId() {
    return _torrentClient.generateNextId();
  }

  /**
   * Generate a new list of globally unique IDs to identity a list of {@link com.linkedin.saleslist.List}
   * @return the new ID
   */
  public Task<LongArray> generateNextIds(int count) {
    return _torrentClient.generateNextIds(count);
  }
}
