package com.linkedin.sales.service.list;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.salesleadaccount.clients.TrackingClient;
import com.linkedin.lss.salesleadaccount.models.tracking.TrackingContext;
import com.linkedin.messages.sales.SalesCustomListEntityBatchOperationRequestMessage;
import com.linkedin.messages.sales.SalesCustomListEntityOperationType;
import com.linkedin.omni.utils.common.parseq.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.client.integration.CrmSettingClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.SeatToListView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.LocalizationHelper;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.AccountToListMapping;
import com.linkedin.saleslist.List;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListOwnership;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.talent.decorator.PathSpecSet;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.google.common.base.Preconditions.*;
import static com.linkedin.lss.salesleadaccount.constants.Constants.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static com.linkedin.sales.service.utils.UrnUtils.*;
import static java.lang.Boolean.*;


/**
 * service class for List
 * <AUTHOR>
 */
public class SalesListService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesListService.class);
  private static final Comparator<com.linkedin.saleslist.List> ACCOUNT_MAP_SORT_ORDER = Comparator.comparing(
      list -> list.getLastViewedAt(GetMode.NULL),
      Comparator.nullsLast(Comparator.reverseOrder()));

  private static final Set<ListSource> AUTOGEN_LIST_SOURCES = ImmutableSet.of(
      ListSource.CRM_SYNC,
      ListSource.CRM_BLUEBIRD,
      ListSource.BUYER_INTEREST,
      ListSource.CRM_AT_RISK_OPPORTUNITY,
      ListSource.RECOMMENDATION,
      ListSource.CRM_PERSON_ACCOUNT,
      ListSource.LEADS_TO_FOLLOW_UP,
      ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS,
      ListSource.AUTO_PROSPECTOR_REC
  );

  private static final int ZERO_TOTAL_HITS = 0;
  private static final int DEFAULT_START = 0;
  private static final int MIN_SHARING_POLICY_COUNT_FOR_SHARED_LIST = 2;
  private static final String PERSON_ACCOUNT = "PERSON_ACCOUNT";

  private final LssListDB _lssListDB;
  private final SalesListIdService _salesListIdService;
  private final LssSharingDB _lssSharingDB;
  private final LixService _lixService;
  private final AclServiceDispatcher _aclServiceDispatcher;
  private final CrmPairingClient _crmPairingClient;
  private final LocalizationHelper _localizationHelper;
  private final SalesSeatClient _salesSeatClient;
  private final TrackingClient _trackingClient;
  private final CrmSettingClient _crmSettingClient;
  private final SalesAccountToListMappingService _salesAccountToListMappingService;

  private static final PathSpec[] SALES_SEAT_PATH_SPECS = new PathSpec[]{
      SalesSeat.fields().member()
  };

  public SalesListService(LssListDB lssListDB, SalesListIdService salesListIdService, LssSharingDB lssSharingDB,
      LixService lixService, AclServiceDispatcher aclServiceDispatcher, CrmPairingClient crmPairingClient,
      LocalizationHelper localizationHelper, SalesSeatClient salesSeatClient, TrackingClient trackingClient,
      SalesAccountToListMappingService salesAccountToListMappingService, CrmSettingClient crmSettingClient) {
    _lssListDB = lssListDB;
    _salesListIdService = salesListIdService;
    _lssSharingDB = lssSharingDB;
    _lixService = lixService;
    _aclServiceDispatcher = aclServiceDispatcher;
    _crmPairingClient = crmPairingClient;
    _localizationHelper = localizationHelper;
    _salesSeatClient = salesSeatClient;
    _trackingClient = trackingClient;
    _salesAccountToListMappingService = salesAccountToListMappingService;
    _crmSettingClient = crmSettingClient;
  }

  /**
   * create list
   * @param list list object
   * @param contractId contract Id of seat that create the list
   * @return list id of the created list
   */
  public Task<Long> createList(@NonNull List list, long contractId) {
    return _salesListIdService.generateNextId()
        .flatMap(listId -> createList(list, listId, contractId))
        .recoverWith(t -> {
          LOG.error(String.format("fail to create list for contract:%d", contractId), t);
          return Task.failure(t);
        });
  }

  /**
   * batch create list
   * @param lists lists object
   * @param contractId contract id of seat that create these lists
   * @return batch creat result with all successful creation
   */
  public Task<BatchCreateResult<Long, List>> batchCreateLists(@NonNull java.util.List<List> lists, long contractId) {
    if (!lists.isEmpty()) {
      return _salesListIdService.generateNextIds(lists.size()).flatMap(listIds -> {
        if (listIds.size() != lists.size()) {
          LOG.error("Fail to get enough auto generated Ids when creating the lists");
          return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
        }
        java.util.List<Task<CreateResponse>> batchCreateTasks = new ArrayList<>(listIds.size());
        for (int i = 0; i < listIds.size(); i++) {
          List list = lists.get(i);
          Task<CreateResponse> createTask = createList(list, listIds.get(i), contractId)
              .map(listId -> new CreateResponse(listId, HttpStatus.S_201_CREATED))
              .recover(throwable -> {
                // For batch create we do not want to throw entire exception just for one creation failure
                String errorMsg = String.format("Fail to create for contract %d with list %s", contractId, list);
                LOG.warn(errorMsg, throwable);
                return new CreateResponse(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMsg));
              });
          batchCreateTasks.add(createTask);
        }
        return Task.par(batchCreateTasks).map(createResponses -> {
          java.util.List<CreateResponse> validCreateResponses =
              createResponses.stream().filter(Objects::nonNull).collect(Collectors.toList());
          return new BatchCreateResult<Long, List>(validCreateResponses);
        });
      }).recoverWith(throwable -> {
        String errMsg = String.format("Fail to generate the unique ids for lists %s, contract %d", lists, contractId);
        return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errMsg, throwable));
      });
    } else {
      // if the request is empty, just return an empty batch create result
      return Task.value(new BatchCreateResult<>(Collections.emptyList()));
    }
  }

  /**
   * fundamental method for creating a list when listId has been generated
   * @param list list object
   * @param listId list id from torrent generator
   * @param contractId contract id
   * @return the successful generated list id.
   */
  private Task<Long> createList(@NonNull List list, long listId, long contractId) {
    long creatorSeatId = list.getCreator().getSeatIdEntity();
    com.linkedin.sales.espresso.ListType listType =
        ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(list.getListType());
    com.linkedin.sales.espresso.ListSource listSource =
        LIST_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.get(list.getListSource());
    long currentTime = System.currentTimeMillis();
    // create espresso list
    com.linkedin.sales.espresso.List espressoList = new com.linkedin.sales.espresso.List();
    espressoList.creatorSeatId = creatorSeatId;
    espressoList.listType = listType;
    espressoList.createdTime = currentTime;
    espressoList.lastModifiedTime = currentTime;
    espressoList.contractId = contractId;
    if (list.hasListSource()) {
      espressoList.listSource = listSource;
    }
    if (list.hasDescription()) {
      espressoList.description = list.getDescription();
    }
    espressoList.name = list.getName();

    return _lssListDB.createList(listId, espressoList);
  }

  /**
   * delete list
   * @param listId list Id
   * @param seatUrn seatUrn
   * @param contractUrn contractUrn
   * @return if the deletion succeed. True if succeed. False if not found
   */
  public Task<Boolean> deleteList(long listId, SeatUrn seatUrn, ContractUrn contractUrn) {
    long seatId = seatUrn.getSeatIdEntity();
    Task<com.linkedin.sales.espresso.List> getListTask = _lssListDB.getList(listId);

    return getListTask.flatMap(list -> {
      // new sharing flow
      PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(list.listType);
      return getAccessDecision(seatUrn, listId, policyType, AccessAction.ADMIN).flatMap(accessDecision -> {
        if (accessDecision != AccessDecision.ALLOWED) {
          String errMsg = String.format("seat:%d does not have permission to delete list:%d", seatId, listId);
          return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, errMsg));
        }
        // when deleting the list, we also need to delete all the entities inside the list.
        // Otherwise the entity to list count won't be correct.
        Task<Boolean> deleteListTask = _lssListDB.deleteList(listId);
        Task<Boolean> deleteRelationshipMapChangeLogsTask = Task.value(FALSE);
        if (list.listType == com.linkedin.sales.espresso.ListType.ACCOUNT_MAP) {
          deleteRelationshipMapChangeLogsTask =
              _lssListDB.deleteAllRelationshipMapChangeLogsForListId(listId).recoverWith(e -> {
                LOG.warn("Removal of all change log's for listId {} failed with error", listId, e);
                return Task.value(FALSE);
              });
        }
        Task<Boolean> deleteListEntityTask = getDeleteListEntitiesTask(listId, seatUrn, contractUrn);
        Urn resourceUrn = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, listId);
        Task<PaginatedList<Pair<Urn, ShareRole>>> subjectPoliciesTask =
            _lssSharingDB.getPoliciesByResource(resourceUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT);
        return Task.par(deleteListTask, deleteListEntityTask, subjectPoliciesTask, deleteRelationshipMapChangeLogsTask)
            .flatMap((isDeleteListSuccessful, isDeleteEntitySuccessful, subjectPolicies, isChangeLogDeleteSuccessful) -> {
              if (isDeleteListSuccessful && isDeleteEntitySuccessful) {
                // delete all sharing policies associated with the list
                return Task.par(subjectPolicies.getResult().stream().map(subjectPolicy -> {
                  PolicyKey policyKey = new PolicyKey();
                  policyKey.setPolicyType(policyType);
                  policyKey.setResource(resourceUrn);
                  policyKey.setSubject(subjectPolicy.getFirst());
                  return _lssSharingDB.deleteSubjectPolicy(policyKey);
                }).collect(Collectors.toList())).map(successes -> successes.stream().allMatch(success -> success));
              }
              return Task.value(FALSE);
            });
      });
    }).recoverWith(t -> {
      LOG.error(String.format("fail to delete list for list:%d, seat:%d", listId, seatId), t);
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.value(FALSE);
      } else {
        return Task.failure(t);
      }
    });
  }

  /**
   * Helper method to either return the Task to delete all the list entities (if size <=250) or send a kafka message
   * to delete the entities by downstream.
   */
  @VisibleForTesting
  Task<Boolean> getDeleteListEntitiesTask(long listId, SeatUrn seatUrn, ContractUrn contractUrn) {
    Task<Long> getListEntityCountTask = _lssListDB.getListEntityCount(listId).recover(t -> {
      LOG.warn("unable to get listEntity total count {}", listId, t);
      return LIST_ENTITY_LIMIT;
    });
    return getListEntityCountTask.flatMap(listEntityCount -> {
      if (listEntityCount <= LIST_ENTITY_ONLINE_PROCESSING_LIMIT) {
        return _lssListDB.deleteAllListEntities(listId);
      } else {
        LOG.info("Proceeding to delete the list entities by triggering a kafka event for list: {}", listId);
        Task<java.util.List<Urn>> listEntityUrnsTask =
            _lssListDB.getEntityUrnsForListEntitiesForMultipleLists(Sets.newHashSet(listId), DEFAULT_START,
                (int) LIST_ENTITY_LIMIT);

        EnterpriseApplicationUsageUrn viewer =
            new EnterpriseApplicationUsageUrn(EnterpriseApplication.SALES_NAVIGATOR.getUrn(),
                "Delete ListEntities");
        Task<TrackingContext> trackingContextTask =
            _salesSeatClient.getSeat(seatUrn.getSeatIdEntity(), contractUrn, viewer, SALES_SEAT_PATH_SPECS)
                .map(seat -> new TrackingContext(
                    Urn.createFromTuple(SALES, seat.getMember().getIdAsLong(), seatUrn.getSeatIdEntity(),
                        contractUrn.getContractIdEntity()).toString()));

        return Task.par(listEntityUrnsTask, trackingContextTask).flatMap((listEntityUrns, trackingContext) -> {

          SalesCustomListEntityBatchOperationRequestMessage message =
              trackingContext.createSalesCustomListEntityBatchOperationRequestMessage(
                  listEntityUrns.stream().map(Urn::toString).collect(Collectors.toList()),
                  SalesCustomListEntityOperationType.REMOVE, createSalesListUrn(listId).toString());

          // Send a Kafka message for the Samza job to pick up the deletion of list entities.
          _trackingClient.send(SALES_CUSTOM_LIST_ENTITY_BATCH_OPERATION_MSG_NAME, message);

          return Task.value(TRUE);
        });
      }
    });
  }

  /**
   * get list
   * @param listId list Id
   * @param viewer viewer urn, either seat urn or member urn
   * @param locale the locale for the system generated list
   * @return a list
   */
  public Task<List> getList(long listId, @NonNull Urn viewer, @Nullable Locale locale) {
    if (!ServiceConstants.SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES.contains(viewer.getEntityType())) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, String.format(
          "Unsupported viewer type %s. Supported viewer types are %s", viewer,
          ServiceConstants.SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES)));
    }

    Task<com.linkedin.sales.espresso.List> getListTask = _lssListDB.getList(listId);
    //If unable get listEntity total, return 0 as default value
    Task<Long> getListEntityCountTask = _lssListDB.getListEntityCount(listId).recover(t -> {
      LOG.warn("Unable get listEntity total count {}", listId, t);
      return 0L;
    });
    return Task.par(getListTask, getListEntityCountTask)
        .flatMap((foundList, entityCount) -> {
          // new sharing flow
          PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(foundList.listType);
          ListSource listSource = ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.getOrDefault(foundList.listSource, ListSource.MANUAL);

          return getAccessDecision(viewer, listId, policyType, AccessAction.READ).flatMap(accessDecision -> {
            if (accessDecision != AccessDecision.ALLOWED) {
              return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
                  String.format("viewer:%s does not have permission to view list:%d", viewer, listId)));
            }
            Task<Integer> getResourceSharingTotalTask = _lssSharingDB.getResourceSharingPolicyTotal(createSalesListUrn(listId), policyType);
            Task<SubjectPolicy> getSubjectPolicyTask = getSubjectPolicyForSubscribableList(listSource, viewer, listId, policyType);

            return Task.par(getResourceSharingTotalTask, getSubjectPolicyTask)
                .map((resourceSharingTotal, subjectPolicy) -> constructListFromEspresso(listId, foundList, entityCount,
                    resourceSharingTotal, locale, subjectPolicy));
          });
        })
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
                    String.format("fail to get list for list:%d, viewer:%s", listId, viewer), t));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * batch get lists based on the listIds
   * @param listIds list ids
   * @param ownerSeatId list owner's seat id. Return lists that are accessible by the owner.
   * @param locale the locale for the system generated list
   * @return the map between listId and lists. Only return the available ones.
   */
  public Task<Map<Long, List>> batchGetLists(@NonNull Set<Long> listIds, @NonNull Long ownerSeatId, @Nullable Locale locale) {
    return batchGetLists(listIds, ownerSeatId, locale, true, true);
  }

  /**
   * batch get lists based on the listIds
   * @param listIds list ids
   * @param ownerSeatId list owner's seat id. Return lists that are accessible by the owner.
   * @param locale the locale for the system generated list
   * @param decorateListEntityCount whether to decorate EntityCount field for each list
   * @param decorateSharingFields whether to decorate sharing info for each list. Sharing info fields include: collaboratorCount, shared, accepted, sharedBy
   * @return the map between listId and lists. Only return the available ones.
   */
  public Task<Map<Long, List>> batchGetLists(@NonNull Set<Long> listIds, @NonNull Long ownerSeatId, @Nullable Locale locale,
      boolean decorateListEntityCount, boolean decorateSharingFields) {
    // For all the par tasks, we do not want to fail the entire task just because of one entity retrieval failure
    // Therefore, for all the below parTasks, we always swallow the failure and return null.
    // get lists data in parallel
    Task<Map<Long, com.linkedin.sales.espresso.List>> getListsTask = Task.par(listIds.stream()
        .map(listId -> _lssListDB.getList(listId)
            .map(list -> new AbstractMap.SimpleEntry<>(listId, list))
            .recover(t -> {
              LOG.warn("Fail to get the list for {}", listId, t);
              return null;
            }))
        .collect(Collectors.toList())
    ).map(listEntries -> listEntries.stream().filter(Objects::nonNull)
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

    // get list entity count data in parallel
    Task<Map<Long, Long>> getListEntityCountsTask = ParseqUtils.batchTask(listIds, listId -> decorateListEntityCount
        ? _lssListDB.getListEntityCount(listId).onFailure(t -> LOG.warn("Failed to get entity count for list {}", listId, t))
        : Task.value(0L));

    return Task.par(getListsTask, getListEntityCountsTask)
        .flatMap((listMap, listEntityCountMap) -> {
          // new sharing flow
          return Task.par(listMap.entrySet().stream().map(listEntry -> {
            long listId = listEntry.getKey();
            PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(listEntry.getValue().listType);
            ListSource listSource = ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.getOrDefault(listEntry.getValue().listSource, ListSource.MANUAL);
            return getAccessDecision(createSeatUrn(ownerSeatId), listId, policyType, AccessAction.READ).flatMap(accessDecision -> {
              com.linkedin.sales.espresso.List list = listMap.get(listId);
              Long listEntityCount = listEntityCountMap.get(listId);
              if (accessDecision == AccessDecision.ALLOWED && list != null && listEntityCount != null) {
                Task<Integer> getResourceSharingTotalTask =
                    decorateSharingFields ? _lssSharingDB.getResourceSharingPolicyTotal(createSalesListUrn(listId),
                        policyType) : Task.value(0);
                Task<SubjectPolicy> getSubjectPolicyTask =
                    getSubjectPolicyForSubscribableList(listSource, createSeatUrn(ownerSeatId), listId, policyType);

                return Task.par(getResourceSharingTotalTask, getSubjectPolicyTask)
                    .map((resourceSharingTotal, subjectPolicy) -> new AbstractMap.SimpleEntry<>(listId,
                        constructListFromEspresso(listId, list, listEntityCount, resourceSharingTotal, locale, subjectPolicy)));
              }
              // user is not allowed to view the list so we skip it
              return Task.value(null);
            }).recover(t -> {
              LOG.error("Fail to get access decision for list: {}, seat: {}, AccessAction: {}", listId, ownerSeatId,
                  AccessAction.READ, t);
              return null;
            });
          }).collect(Collectors.toList()))
              .map(seatListEntries -> seatListEntries.stream()
                  .filter(Objects::nonNull)
                  .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
        })
        .onFailure(t -> LOG.error("Fail to batch get the lists for {}", listIds, t));
  }

  /**
   * Helper function to get Lists from SeatToListView with certain listSource and listType with total count
   * @param seatId requester seatId
   * @param listType list type, could be LEAD/ACCOUNT
   * @param listSource listSources
   * @param locale the locale for the system generated list
   * @param start paging start
   * @param countInput paging count
   * @return return total list number to a collection of lists pair
   */
  private Task<Pair<Integer, java.util.List<List>>> getListsFromSeatToListViewHelper(long seatId,
      @NonNull com.linkedin.sales.espresso.ListType listType,
      @NonNull com.linkedin.sales.espresso.ListSource listSource, @Nullable Locale locale, int start, int countInput) {

    final int count;
    // CRM list always needs exact lists to do filtering to determine the actual CRM list count
    if ((listSource == com.linkedin.sales.espresso.ListSource.CRM_SYNC || listSource == com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT)
        && countInput == 0) {
      count = DEFAULT_COUNT;
    } else {
      count = countInput;
    }

    return _lssListDB.getListsForSeatToListView(seatId, listType, listSource, start, count)
        .flatMap(pair -> {
      int listCount = pair.getFirst();
      if (listCount == 0) {
        return Task.value(new Pair<>(ZERO_TOTAL_HITS, Collections.emptyList()));
      }
      java.util.List<Pair<Long, SeatToListView>> idToSeatToListViews = pair.getSecond();
      if ((listSource == com.linkedin.sales.espresso.ListSource.CRM_SYNC || listSource == com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT)
          && !idToSeatToListViews.isEmpty()) {
        Long contractId = idToSeatToListViews.get(0).getSecond().contractId;
        Task<CrmPairing> crmPairingTask = getCrmPairing(contractId, seatId);
        return crmPairingTask.flatMap(crmPairing -> {
          // if person account is not enabled, should not show CRM_PERSON_ACCOUNT list
          if (listSource == com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT) {
            Task<Boolean> getIsPersonAccountEnabledTask = getIsPersonAccountEnabled(crmPairing);
            return getIsPersonAccountEnabledTask.flatMap(isPersonAccountEnabled -> {
              if (!isPersonAccountEnabled) {
                return Task.value(new Pair<>(ZERO_TOTAL_HITS, Collections.emptyList()));
              }
              return constructCountAndListsPairForCrmLists(idToSeatToListViews, seatId,
                  listType, listSource, locale, crmPairing, contractId);
            });
          }
          return constructCountAndListsPairForCrmLists(idToSeatToListViews, seatId,
              listType, listSource, locale, crmPairing, contractId);
        });
      }
      return constructCountAndListsPairFromSeatToListView(idToSeatToListViews, seatId, listCount, listType, listSource, locale, false);
    }).recoverWith(t -> {
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.value(new Pair<>(ZERO_TOTAL_HITS, Collections.emptyList()));
      } else {
        return Task.failure(t);
      }
    });
  }

  /**
   * get the CRM list belongs to the latest connected CRM instance of a seat with certain listType and listSource
   * @return one seatList of List<Pair<listId, seatLIst>> for the seat
   */
  private Task<Pair<Integer, java.util.List<List>>> constructCountAndListsPairForCrmLists(
      @NonNull java.util.List<Pair<Long, SeatToListView>> idToSeatToListViews, long seatId,
      @NonNull com.linkedin.sales.espresso.ListType listType, @NonNull com.linkedin.sales.espresso.ListSource listSource,
      @Nullable Locale locale, CrmPairing crmPairing, Long contractId) {
    String crmListName = deriveCrmListNameForSeat(crmPairing, listType, ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.get(listSource));
    // Get CRM list name for filtering the CRM list that belongs to latest connected CRM instance
    if (crmListName == null) {
      LOG.error("Failed to get CRM list name of listType {} for contract {}, CRM list will not be displaying", listType,
          contractId);
      return Task.value(new Pair<>(ZERO_TOTAL_HITS, Collections.emptyList()));
    }
    // Only return the CRM list that belongs to latest connected CRM instance
    java.util.List<Pair<Long, SeatToListView>> list = idToSeatToListViews.stream().filter(idToSeatToListView ->
        crmListName.equals(idToSeatToListView.getSecond().name.toString())).collect(Collectors.toList());
    if (list.isEmpty()) {
      return Task.value(new Pair<>(ZERO_TOTAL_HITS, Collections.emptyList()));
    }
    if (list.size() > 1) {
      LOG.error("Found more than one CRM list with listType {} for seat {}", listType, seatId);
      //Only get the latest modified list if found more than one CRM lists, should not happen though
      Collections.sort(list, (a, b) -> Long.compare(b.getSecond().lastModifiedTime, a.getSecond().lastModifiedTime));
      list = list.subList(0, 1);
    }
    // At most one CRM list per CRM instance per seat
    return constructCountAndListsPairFromSeatToListView(list, seatId, 1, listType, listSource, locale, false);
  }

  /**
   * construct salesList object from espresso SeatToListView
   * @param listId list Id
   * @param seatToListView espresso list object
   * @param entityCount entity count of list
   * @param locale the locale for the system generated list
   * @return list
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.get value and "
          + "LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.getOrDefault value is not null")
  private List constructListFromSeatToListViewEspresso(long listId, long creatorSeatId,
      @NonNull com.linkedin.sales.espresso.SeatToListView seatToListView, long entityCount,
      @NonNull com.linkedin.sales.espresso.ListType listType,
      @NonNull com.linkedin.sales.espresso.ListSource listSource,
      @Nullable SubjectPolicy subjectPolicy,
      @Nullable Locale locale) {
    checkArgument(ServiceConstants.LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.containsKey(listType),
        String.format("Unsupported listType [%s]", listType));

    SeatUrn creator = new SeatUrn(creatorSeatId);
    ListType type = ServiceConstants.LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.get(listType);
    ListSource source = ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.getOrDefault(listSource, ListSource.MANUAL);
    String name = seatToListView.name.toString();
    String description = seatToListView.description != null ? seatToListView.description.toString() : null;

    List list = new List().setId(listId)
        .setListType(type)
        .setListSource(source)
        .setCreator(creator)
        .setCreatorContract(new ContractUrn(seatToListView.contractId))
        .setName(name)
        .setLocalizedName(getLocalizedListName(type, source, locale, name), SetMode.IGNORE_NULL)
        .setLastModifiedAt(seatToListView.lastModifiedTime)
        .setLastViewedAt(seatToListView.lastViewedTime, SetMode.IGNORE_NULL)
        .setEntityCount((int) entityCount) // possible narrowing conversion here from long to int
        .setCreatedTime(seatToListView.createdTime, SetMode.IGNORE_NULL)
        .setDescription(description, SetMode.IGNORE_NULL)
        .setLocalizedDescription(getLocalizedListDescription(type, source, locale, description), SetMode.IGNORE_NULL);

    if (subjectPolicy != null) {
      // Override last viewed for account map only
      if (type == ListType.ACCOUNT_MAP) {
        list.setLastViewedAt(subjectPolicy.lastViewedTime, SetMode.IGNORE_NULL);
      }
      list.setSubscribed(subjectPolicy.isSubscribed, SetMode.IGNORE_NULL);
    }

    if (seatToListView.lastModifiedBySeatUrn != null) {
      try {
        SeatUrn modifier = SeatUrn.deserialize(seatToListView.lastModifiedBySeatUrn.toString());
        list.setLastModifiedBy(modifier);
      } catch (URISyntaxException e) {
        LOG.error("Fail to deserialize the seat urn string {}",
            seatToListView.lastModifiedBySeatUrn, e);
        return list;
      }
    }
    return list;
  }

  /**
   * Generate the localized list name for autogen lists, or My Current Accounts lists whose name matches
   * its original name when created by the system. Otherwise, return null.
   */
  @Nullable
  private String getLocalizedListName(@NonNull ListType listType, @Nullable ListSource listSource,
      @Nullable Locale locale, @NonNull String espressoListName) {
    if (AUTOGEN_LIST_SOURCES.contains(listSource) || (listSource == ListSource.BOOK_OF_BUSINESS
        && ListSource.BOOK_OF_BUSINESS.name().equals(espressoListName))) {
      return _localizationHelper.getLocalizedListName(listSource, listType, locale);
    }
    return null;
  }

  /**
   * Generate the localized list description for autogen lists, or My Current Accounts lists whose
   * list description is null in espresso record. Otherwise, return null.
   */
  @Nullable
  private String getLocalizedListDescription(@NonNull ListType listType, @Nullable ListSource listSource,
      @Nullable Locale locale, @Nullable String espressoListDescription) {
    if (AUTOGEN_LIST_SOURCES.contains(listSource) || (listSource == ListSource.BOOK_OF_BUSINESS
        && espressoListDescription == null)) {
      return _localizationHelper.getLocalizedListDescription(listSource, listType, locale);
    }
    return null;
  }

  /**
   * partial update list, support list name, last modified time and last viewed time
   * @param listId list Id of the list that need to be updated
   * @param list list object with fields that need to be updated
   * @param seatId seat Id of user that do the update
   * @return if the update succeed. True if succeed. False if not found
   */
  public Task<Boolean> updateList(long listId, @NonNull List list, long seatId) {
    return partialUpdateList(listId, list, seatId)
        .recoverWith(t -> {
          LOG.error(String.format("fail to update list for list:%d, seat:%d", listId, seatId), t);
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.value(FALSE);
          }
          return Task.failure(t);
        });
  }

  /**
   * Upsert a list.
   * If the ID matches an existing List ID, an updated is performed, otherwise a create is performed.
   * If an update is performed, an access policy check must first succeed, and only list name, last modified time
   * and last viewed time may be modified.
   * @param listId the ID of the list to upsert
   * @param list the list
   * @param contractId contract in which the user is doing the upsert which is used to perform access control
   * @return an update response indicating if the list was created or updated
   */
  public Task<UpdateResponse> upsertList(long listId, List list, long contractId) {
    return partialUpdateList(listId, list, list.getCreator().getIdAsLong())
        .map(isUpdateSuccessful -> {
          if (!isUpdateSuccessful) {
            throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
                String.format("Update not successful for list id %d", listId));
          }
          return new UpdateResponse(HttpStatus.S_204_NO_CONTENT);
        })
        .recoverWith(t -> {
          // If a List with the given ID was not found, then create the List
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return createList(list, listId, contractId)
                .map(created -> new UpdateResponse(HttpStatus.S_201_CREATED))
                .recoverWith(e -> {
                  LOG.error(String.format("fail to insert list during upsert for contract:%d", contractId), e);
                  return Task.failure(e);
                });
          }
          return Task.failure(t);
        });
  }

  /**
   * get lists for seat, support sort and paging.
   * The method gets data with full projection.
   * @param seatId seat Id of the requester
   * @param listType list type
   * @param sortCriteria sort criteria. LAST_VIEWED, LAST_MODIFIED or NAME.
   * @param sortOrder sort order. ASCENDING or DESCENDING.
   * @param ownership list ownership to filter the results on. Return all the lists the seat has access to if ownership is absent.
   * @param listSources A collection of listSources. Refer to ListSource.pdl object for supported list sources.
   * @param locale the locale for the system generated list
   * @param start paging start
   * @param count paging count
   * @return a pair of (totalHit, lists)
   */
  public Task<Pair<Integer, java.util.List<List>>> getListsForSeat(long seatId,
      @NonNull com.linkedin.saleslist.ListType listType, @NonNull ListOrdering sortCriteria,
      @NonNull SortOrder sortOrder, @Nullable ListOwnership ownership, @NonNull Set<ListSource> listSources,
      @Nullable Locale locale, int start, int count) {
    return getListsForSeat(seatId, listType, sortCriteria, sortOrder, ownership, listSources, locale, start, count, PathSpecSet.fullProjection());
  }

  /**
   * get lists for seat, support sort and paging
   * @param seatId seat Id of the requester
   * @param listType list type
   * @param sortCriteria sort criteria. LAST_VIEWED, LAST_MODIFIED or NAME.
   * @param sortOrder sort order. ASCENDING or DESCENDING.
   * @param ownership list ownership to filter the results on. Return all the lists the seat has access to if ownership is absent.
   * @param listSources A collection of listSources, could be CRM_SYNC, LINKEDIN_SALES_INSIGHTS, MANUAL, SYSTEM
   * @param locale the locale for the system generated list
   * @param start paging start
   * @param count paging count
   * @param projections field projections for decorating List object.
   * @return a pair of (totalHit, lists)
   */
  public Task<Pair<Integer, java.util.List<List>>> getListsForSeat(long seatId,
      @NonNull com.linkedin.saleslist.ListType listType, @NonNull ListOrdering sortCriteria,
      @NonNull SortOrder sortOrder, @Nullable ListOwnership ownership, @NonNull Set<ListSource> listSources,
      @Nullable Locale locale, int start, int count, @NonNull PathSpecSet projections) {

    // lists that are owned by the seat
    Task<Pair<Integer, java.util.List<List>>> ownedListsWithTotalCountTask =
        Task.value(new Pair<>(ZERO_TOTAL_HITS, new ArrayList<>()));
    // lists that are shared with the seat
    Task<java.util.List<List>> sharedWithListsTask = Task.value(Collections.emptyList());

    boolean decorateListEntityCount = isDecorateListEntityCountRequired(projections);
    boolean decorateSharingFields = isDecorateSharingFieldsRequired(projections);

    // Fetch owned lists if fetching all or filtering for owned lists
    if (doFetchOwnedLists(ownership)) {
      ownedListsWithTotalCountTask = getOwnedListsWithTotalCount(seatId, listType, listSources, locale);
    }

      // Fetch the list that are shared with the requester if fetching all or filtering for shared lists
      if (doFetchSharedLists(ownership)) {
        Set<ShareRole> shareRoleSet = ownership == ListOwnership.EDITABLE_BY_VIEWER ? Sets.newHashSet(ShareRole.WRITER)
            : Sets.newHashSet(ShareRole.READER, ShareRole.WRITER);
        sharedWithListsTask =
            getSharedWithLists(seatId, listType, shareRoleSet, listSources, locale, decorateSharingFields);
      }

    return Task.par(ownedListsWithTotalCountTask, sharedWithListsTask)
        .flatMap((ownedListsWithTotalCount, sharedWithListsWithTotalCount) -> {
          //LSS-64357 duplicated lists from invalid sharing policy need to be removed to avoid client error
          Set<Long> ownedListIds = ownedListsWithTotalCount.getSecond().stream().map(List::getId).collect(Collectors.toSet());
          java.util.List<List> dedupedSharedLists = sharedWithListsWithTotalCount
              .stream()
              .filter(shareList -> !ownedListIds.contains(shareList.getId()))
              .collect(Collectors.toList());

          //All share lists are fetched so list size can also be used to get total
          int combinedTotal = ownedListsWithTotalCount.getFirst() + dedupedSharedLists.size();

          java.util.List<List> combinedLists = Stream.concat(ownedListsWithTotalCount.getSecond().stream(), dedupedSharedLists.stream())
              .collect(Collectors.toList());

          // sort and get paginated list
          java.util.List<List> paginatedLists =
              paginateLists(sortLists(combinedLists, sortCriteria, sortOrder), start, count);

          Set<Long> paginatedListIds = paginatedLists.stream().map(List::getId).collect(Collectors.toSet());
          Set<Long> ownedListsToCheckShared =
              paginatedListIds.stream().filter(ownedListIds::contains).collect(Collectors.toSet());

          PolicyType policyType = LIST_TYPE_TO_POLICY_TYPE_MAPPING.get(listType);
          Set<Urn> resourceUrns =
              ownedListsToCheckShared.stream().map(UrnUtils::createSalesListUrn).collect(Collectors.toSet());

          Task<Map<Urn, Integer>> sharingPolicyTask =
              decorateSharingFields ? _lssSharingDB.batchGetResourceSharingPolicyTotal(resourceUrns, policyType)
                  : Task.value(Collections.emptyMap());
          Task<Set<Long>> areOwnedListsSharedTask = sharingPolicyTask.map(
              resourcesSharingTotalMap -> resourcesSharingTotalMap.entrySet()
                  .stream()
                  .filter(entry -> entry.getValue() >= MIN_SHARING_POLICY_COUNT_FOR_SHARED_LIST)
                  .map(entry -> entry.getKey().getIdAsLong())
                  .collect(Collectors.toSet()));
          Task<Map<Long, Long>> getListEntityCountMapTask =
              decorateListEntityCount ? ParseqUtils.batchTask(paginatedListIds,
                  listId -> _lssListDB.getListEntityCount(listId)
                      .onFailure(t -> LOG.warn("Failed to get entity count for list {}", listId, t)))
                  : Task.value(Collections.emptyMap());

          return Task.par(areOwnedListsSharedTask, getListEntityCountMapTask).map((sharedByListIds, listEntityCountMap) -> {
            paginatedLists.forEach(list -> {
              Long listId = list.getId();
              Long entityCount = listEntityCountMap.get(listId);
              list.setEntityCount(entityCount == null ? null : entityCount.intValue(), SetMode.IGNORE_NULL);
              // mark as shared for the lists that are shared by the requester
              if (sharedByListIds.contains(listId)) {
                list.setShared(true);
              }
            });
            return new Pair<>(combinedTotal, paginatedLists);
          });
        });
  }

  // Check if fields that require fetching total sharing policies are present in projection.
  private boolean isDecorateSharingFieldsRequired(@NonNull PathSpecSet projections) {
    return projections.contains((com.linkedin.saleslist.List.fields().collaboratorCount())) || projections.contains(
        (com.linkedin.saleslist.List.fields().shared())) || projections.contains(
        com.linkedin.saleslist.List.fields().accepted()) || projections.contains(
        com.linkedin.saleslist.List.fields().sharedBy());
  }

  private boolean isDecorateListEntityCountRequired(@NonNull PathSpecSet projections) {
    return projections.contains((com.linkedin.saleslist.List.fields().entityCount()));
  }

  /**
   * get all lists for entity that are owned by or shared with the given seat, support sort
   * @param entityUrn entity urn, member or organization
   * @param contractId contract if of list
   * @param seatId viewer seat Id
   * @param ownership list ownership to filter the results on. Return all the lists the seat has access to if ownership is absent.
   * @param sortCriteria sort criteria. LAST_VIEWED, LAST_MODIFIED or NAME.
   * @param sortOrder sort order. ASCENDING or DESCENDING.
   * @param listSources A collection of listSources, could be CRM_SYNC, LINKEDIN_SALES_INSIGHTS, MANUAL, SYSTEM
   * @param locale the locale for the system generated list
   * @param listTypes types of lists to be included in the result
   * @return lists
   */
  public Task<java.util.List<List>> getListsForEntity(@NonNull Urn entityUrn, long contractId, long seatId,
      @Nullable ListOwnership ownership, @NonNull ListOrdering sortCriteria, @NonNull SortOrder sortOrder, @NonNull Set<ListSource> listSources,
      @Nullable Locale locale, @NonNull Set<ListType> listTypes) {
    boolean isValidEntityType = false;
    String entityType = entityUrn.getEntityType();

    Task<java.util.List<List>> totalListPairsTask = Task.value(Collections.emptyList());
    ListType standardListType = ENTITY_TYPE_TO_LIST_TYPE_SERVICE_MAPPING.get(entityType);
    if (listTypes.contains(standardListType)) {
      // Get lists that are owned/ shared/ owned + shared with the given seat, based on ownership value
      totalListPairsTask = getListsForSeat(seatId, standardListType,
              sortCriteria, sortOrder, ownership, listSources, locale, ServiceConstants.DEFAULT_START,
              ServiceConstants.GET_ALL_COUNT).map(Pair::getSecond);

      isValidEntityType = true;
    }

    Task<java.util.List<List>> totalAccountMapListPairsTask = Task.value(Collections.emptyList());
    ListType accountMapListType = ACCOUNT_MAP_ENTITY_TYPE_TO_LIST_TYPE_SERVICE_MAPPING.get(entityType);
    if (listTypes.contains(accountMapListType)) {
      // Get all lists that were created to represent account maps.
      totalAccountMapListPairsTask =
          getListsForSeat(seatId, ListType.ACCOUNT_MAP,
              sortCriteria, sortOrder, ownership, listSources, locale, ServiceConstants.DEFAULT_START,
              ServiceConstants.GET_ALL_COUNT).map(Pair::getSecond);

      isValidEntityType = true;
    }

    if (!isValidEntityType) {
        return Task.failure(
            new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                String.format("No valid ListType available for the entity: %s", entityUrn)));
    }

    return Task.par(totalAccountMapListPairsTask, totalListPairsTask).flatMap((viewerAccountMapList, viewerList) -> {
      //the list could be shared from multiple outside contracts
      Set<Long> contractIds = Stream.concat(viewerList.stream(), viewerAccountMapList.stream())
          .map(list -> list.getCreatorContract().getContractIdEntity())
          .collect(Collectors.toSet());
      //Add contractId of user to cover corner case where lists are shared cross contract. Check LSS-74863 for details.
      contractIds.add(contractId);
      //find list by listEntity by all contracts
      Task<Set<Long>> getListByEntityAndContract = Task.par(contractIds.stream()
          .map(id -> _lssListDB.getListIdsForEntity(entityUrn, id,
              ownership == ListOwnership.OWNED_BY_VIEWER ? seatId : null).recover(t -> Collections.emptyList()))
          .collect(Collectors.toList()))
          .map(lists -> lists.stream().flatMap(Collection::stream).collect(Collectors.toSet()));
      //Filter list
      return getListByEntityAndContract.map(
          listIdSetForEntity -> Stream.concat(viewerList.stream(), viewerAccountMapList.stream())
              .filter(e -> listIdSetForEntity.contains(e.getId()))
              .distinct()
              .collect(Collectors.toList()));
    }).recover(t -> {
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Collections.emptyList();
      } else {
        throw new RuntimeException(String.format("fail to get lists for Entity: %s", entityUrn), t);
      }
    });
  }

  /**
   * get all lists for entity that are owned by the given seat, support sort
   * @param entityUrn entity urn, member or organization
   * @param contractId contract if of list
   * @param seatId viewer seat Id
   * @param sortCriteria sort criteria. LAST_VIEWED, LAST_MODIFIED or NAME.
   * @param sortOrder sort order. ASCENDING or DESCENDING.
   * @param locale the locale for the system generated list
   * @return lists
   */
  private Task<java.util.List<List>> getOwnedListsForEntity(@NonNull Urn entityUrn, long contractId, long seatId,
      @NonNull ListOrdering sortCriteria, @NonNull SortOrder sortOrder, @Nullable Locale locale) {

    return _lssListDB.getListIdsForEntity(entityUrn, contractId, seatId).flatMap(listIds -> {
      java.util.List<Task<List>> tasks = listIds.stream()
          .map(listId -> getList(listId, createSeatUrn(seatId), locale).recoverWith(t -> Task.value(null)))
          .collect(Collectors.toList());
      return Task.par(tasks).map(foundLists -> sortLists(foundLists, sortCriteria, sortOrder));
    }).recoverWith(t -> {
      if (ExceptionUtils.isEntityNotFoundException(t)) {
        return Task.value(Collections.emptyList());
      } else {
        return Task.failure(t);
      }
    });
  }


  /**
   * Sort lists. When sorting by LAST_VIEWED or LAST_MODIFIED, lists that are missing lastViewedAt or lastModified field,
   * respectively, will be ordered last in the result regardless of the sort order.
   * @param lists original lists
   * @param sortCriteria sort criteria. LAST_VIEWED, LAST_MODIFIED or NAME.
   * @param sortOrder sort order. ASCENDING or DESCENDING.
   * @return sorted lists
   */
  java.util.List<List> sortLists(
      @NonNull java.util.List<List> lists,
      @NonNull ListOrdering sortCriteria,
      @NonNull SortOrder sortOrder) {
    Comparator<List> listComparator = sortOrder == SortOrder.ASCENDING
        ? ServiceConstants.LIST_ORDER_TO_COMPARATOR_MAPPING.get(sortCriteria)
        : ServiceConstants.LIST_ORDER_TO_COMPARATOR_MAPPING_REVERSE_ORDER.get(sortCriteria);

    return lists.stream()
        .filter(Objects::nonNull) // filter out null objects
        .sorted(listComparator)
        .collect(Collectors.toList());
  }

  /**
   * return lists with pagination
   * @param lists original lists
   * @param start paging start
   * @param count paging count
   * @return a pair of (totalHit, lists)
   */
  private java.util.List<List> paginateLists(@NonNull java.util.List<List> lists, int start, int count) {
    if (count == ServiceConstants.GET_ALL_COUNT) { // return all lists
      return lists;
    }

    int listSize = lists.size();
    if (start < listSize) {
      int end = Math.min(start + count, listSize);
      return lists.subList(start, end);
    } else {
      return Collections.emptyList();
    }
  }

  /**
   * construct list object from espresso list
   * @param listId list Id
   * @param espressoList espresso list object
   * @param entityCount entity count of list
   * @param locale the locale for the system generated list
   * @return list
   */
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.get value is not null")
  private List constructListFromEspresso(
      long listId,
      @NonNull com.linkedin.sales.espresso.List espressoList,
      long entityCount,
      int listSharingTotal,
      @Nullable Locale locale,
      @Nullable SubjectPolicy policy) {
    checkArgument(ServiceConstants.LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.containsKey(espressoList.listType),
        String.format("Unsupported espressoList listType [%s]", espressoList.listType));

    SeatUrn creator = new SeatUrn(espressoList.creatorSeatId);
    ContractUrn creatorContractUrn = new ContractUrn(espressoList.contractId);
    ListType type = ServiceConstants.LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.get(espressoList.listType);
    ListSource source = ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.get(espressoList.listSource);
    String name = espressoList.name.toString();
    String description = espressoList.description != null ? espressoList.description.toString() : null;
    boolean isListShared = listSharingTotal >= MIN_SHARING_POLICY_COUNT_FOR_SHARED_LIST;

    List list = new List()
        .setId(listId)
        .setListType(type)
        .setCreator(creator)
        .setCreatorContract(creatorContractUrn)
        .setName(name)
        .setLocalizedName(getLocalizedListName(type, source, locale, name), SetMode.IGNORE_NULL)
        .setLastModifiedAt(espressoList.lastModifiedTime)
        .setEntityCount((int) entityCount) // possible narrowing conversion here from long to int
        .setShared(isListShared, SetMode.IGNORE_NULL)
        // Set the collaborator count only if the list is shared. Collaborator count needs to exclude the viewer itself.
        .setCollaboratorCount(isListShared ? listSharingTotal - 1 : null, SetMode.REMOVE_IF_NULL)
        .setCreatedTime(espressoList.createdTime, SetMode.IGNORE_NULL)
        .setDescription(description, SetMode.IGNORE_NULL)
        .setLocalizedDescription(getLocalizedListDescription(type, source, locale, description), SetMode.IGNORE_NULL)
        .setLastViewedAt(espressoList.lastViewedTime, SetMode.IGNORE_NULL);

    if (policy != null) {
      // Override last viewed for account map only
      if (type == ListType.ACCOUNT_MAP) {
        list.setLastViewedAt(policy.lastViewedTime, SetMode.IGNORE_NULL);
        // Set if the shared account map has been accepted by the viewer
        if (BooleanUtils.isTrue(isListShared)) {
          boolean accepted = WRITER_ROLES.contains(policy.role);
          // Set the seat who shared the account map with the viewer, only when the map is shared and pending acceptance
          SeatUrn sharedBySeatUrn = (policy.creatorSeatUrn == null || accepted)
              ? null : UrnUtils.createSeatUrn(policy.creatorSeatUrn);
          list.setAccepted(accepted);
          list.setSharedBy(sharedBySeatUrn, SetMode.IGNORE_NULL);
        }
      }
      list.setSubscribed(policy.isSubscribed, SetMode.IGNORE_NULL);
    }

    //todo: Add warning here if listSource == null after list backfill work completed.
    if (source != null) {
      list.setListSource(source);
    }

    if (espressoList.lastModifiedBySeatUrn != null) {
      try {
        SeatUrn modifier = SeatUrn.deserialize(espressoList.lastModifiedBySeatUrn.toString());
        list.setLastModifiedBy(modifier);
      } catch (URISyntaxException e) {
        LOG.error("Fail to deserialize the seat urn string {}", espressoList.lastModifiedBySeatUrn, e);
        return list;
      }
    }
    return list;
  }

  /**
   * Get all lists with certain listType and listSources for seat holder with Total count by querying SeatToListView table.
   * @param requesterSeatId requester seatId
   * @param listType list type, could be LEAD/ACCOUNT
   * @param listSources listSources, a collection of listSource
   * @param locale the locale for the system generated list
   * @return return total list number to a collection of lists pair
   */
  private Task<Pair<Integer, java.util.List<List>>> getOwnedListsWithTotalCount(@NonNull Long requesterSeatId,
      @NonNull ListType listType, @NonNull Set<ListSource> listSources, @Nullable Locale locale) {
    com.linkedin.sales.espresso.ListType dsListType =
        ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(listType);
    Set<com.linkedin.sales.espresso.ListSource> dsListSources = listSources.stream()
        .map(listSource -> LIST_SOURCE_SERVICE_TO_ESPRESSO_MAPPING.getOrDefault(listSource,
            com.linkedin.sales.espresso.ListSource.MANUAL))
        .collect(Collectors.toSet());

    java.util.List<Task<Pair<Integer, java.util.List<List>>>> getListNumberToListsPairTasks = dsListSources.stream()
        .map(listSource -> getListsFromSeatToListViewHelper(requesterSeatId, dsListType, listSource, locale,
            DEFAULT_START, ServiceConstants.GET_COUNT_LIMIT))
        .collect(Collectors.toList());
    return Task.par(getListNumberToListsPairTasks).map(listNumberToListsPair -> {
      java.util.List<com.linkedin.saleslist.List> lists = new ArrayList<>();
      int totalCount = listNumberToListsPair.stream().map(Pair::getFirst).mapToInt(Integer::intValue).sum();
      listNumberToListsPair.forEach(list -> lists.addAll(list.getSecond()));
      return new Pair<>(totalCount, lists);
    });
  }

  // Query SubjectPolicy table to get the lists that are shared with the requester, the result could be filtered based on roles
  // result lists will be filtered based on listSources
  @SuppressFBWarnings(value = "NP_NULL_ON_SOME_PATH_FROM_RETURN_VALUE",
      justification = "ServiceConstants.LIST_TYPE_TO_POLICY_TYPE_MAPPING.get value is not null")
  private Task<java.util.List<List>> getSharedWithLists(Long requesterSeatId,
      com.linkedin.saleslist.ListType listType, Set<ShareRole> roles, @NonNull Set<ListSource> listSources,
      @Nullable Locale locale, boolean decorateSharingFields) {
    checkArgument(ServiceConstants.LIST_TYPE_TO_POLICY_TYPE_MAPPING.containsKey(listType),
        String.format("Unsupported listType [%s]", listType));

    PolicyType policyType = ServiceConstants.LIST_TYPE_TO_POLICY_TYPE_MAPPING.get(listType);
    return _lssSharingDB.getPoliciesBySubject(new SeatUrn(requesterSeatId), policyType.toString(),
        Sets.newHashSet(roles), 0, SHARING_POLICY_GET_ALL_COUNT).flatMap(paginateList -> {
      // Each entity in the paginateList is a pair of listUrn + SubjectPolicy
      Set<Long> listIdSet =
          paginateList.getResult().stream().map(Pair::getFirst).map(Urn::getIdAsLong).collect(Collectors.toSet());
      //TODO: Add warning to list.hasSource = false after all saved lead/account migration completed.
      return batchGetLists(listIdSet, requesterSeatId, locale, false, decorateSharingFields).map(listMap ->
        listMap.values().stream()
            .filter(list -> !list.hasListSource() || listSources.contains(list.getListSource())).collect(Collectors.toList())
      );
    }).recover(t -> {
      LOG.error("unable get shared list with totalCount by seat {}", requesterSeatId, t);
      return Collections.emptyList();
    });
  }

  // check if it needs to fetch owned list based on the list ownership filter
  private boolean doFetchOwnedLists(ListOwnership ownershipFilter) {
    return ownershipFilter == null || ListOwnership.OWNED_BY_VIEWER == ownershipFilter
        || ListOwnership.EDITABLE_BY_VIEWER == ownershipFilter;
  }

  // check if it needs to fetch shared list based on the list ownership filter
  private boolean doFetchSharedLists(ListOwnership ownershipFilter) {
    return ownershipFilter == null || ListOwnership.SHARED_WITH_VIEWER == ownershipFilter
        || ListOwnership.EDITABLE_BY_VIEWER == ownershipFilter;
  }

  private Task<AccessDecision> getAccessDecision(@NonNull Urn viewer, Long listId, PolicyType policyType,
      AccessAction accessAction) {
    Urn resourceUrn = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, listId);
    return _aclServiceDispatcher.checkAccessDecision(viewer, policyType, resourceUrn, accessAction)
        .onFailure(
            t -> LOG.error("Fail to get access decision for list: {}, viewer: {}, AccessAction: {}", listId,
                viewer, accessAction, t));
  }

  private Task<Pair<Integer, java.util.List<List>>> constructCountAndListsPairFromSeatToListView(
      @NonNull java.util.List<Pair<Long, SeatToListView>> idToSeatToListViews, long seatId, int listCount,
      @NonNull com.linkedin.sales.espresso.ListType listType,
      @NonNull com.linkedin.sales.espresso.ListSource listSource, @Nullable Locale locale, boolean decorateListEntityCount) {
    java.util.List<Task<List>> listTasks = idToSeatToListViews.stream().map(idToSeatToListView -> {
      Long listId = idToSeatToListView.getFirst();
      //If unable to get listEntity total count or decorateListEntityCount is false, return 0 as default value
      Task<Long> entityCountTask = Task.value(0L);
      if (decorateListEntityCount) {
        entityCountTask = _lssListDB.getListEntityCount(listId).recover(t -> {
          LOG.warn("unable get listEntity total count {}", listId, t);
          return 0L;
        });
      }
      PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(listType);
      ListSource salesListSource =
          ServiceConstants.LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.getOrDefault(listSource, ListSource.MANUAL);
      Task<SubjectPolicy> getSubjectPolicyTask =
          getSubjectPolicyForSubscribableList(salesListSource, createSeatUrn(seatId), listId, policyType);

      return Task.par(entityCountTask, getSubjectPolicyTask)
          .flatMap((entityCount, subjectPolicy) -> Task.value(
              constructListFromSeatToListViewEspresso(listId, seatId, idToSeatToListView.getSecond(), entityCount,
                  listType, listSource, subjectPolicy, locale)));
    }).collect(Collectors.toList());
    return Task.par(listTasks).map(salesLists -> new Pair<>(listCount, salesLists));
  }

  /**
   * Get the SubjectPolicy for lists that are allowed to be subscribed.
   * Return null if list is not subscribable or policy not found.
   */
  private Task<SubjectPolicy> getSubjectPolicyForSubscribableList(@NonNull ListSource listSource, @NonNull Urn subjectUrn,
      long listId, @NonNull PolicyType policyType) {
    if (SUBSCRIBABLE_LIST_SOURCES.contains(listSource) || SUBSCRIBABLE_POLICY_TYPES.contains(
        policyType)) {
      return _lssSharingDB.getSubjectPolicy(subjectUrn, policyType.name(), createSalesListUrn(listId))
          .recover(t -> {
            if (!ExceptionUtils.isEntityNotFoundException(t)) {
              LOG.warn("Failed to get SubjectPolicy for List {}", listId, t);
            }
            return null;
          });
    }
    return Task.value(null);
  }

  private Task<CrmPairing> getCrmPairing(Long contractId, Long seatId) {
    return _crmPairingClient.findCrmPairingsBySeat(new ContractUrn(contractId), new SeatUrn(seatId), false, null)
        .map(crmPairings -> crmPairings.stream()
            .findFirst().orElse(null));
  }

  /**
   * Get the CRM list name for the seat. The format of CrmListName is CRM_<targetListType>_<CrmInstanceId>
   * CRM_PERSON_ACCOUNT_<CrmInstanceId> is ListType.LEAD but ListSource.CRM_PERSON_ACCOUNT
   */
  private String deriveCrmListNameForSeat(CrmPairing crmPairing, com.linkedin.sales.espresso.ListType listType,
  ListSource listSource) {
    if (crmPairing == null) {
      return null;
    }
    return String.format("CRM_%s_%s", listSource == ListSource.CRM_PERSON_ACCOUNT ? PERSON_ACCOUNT : listType.name(),
        CrmUrnUtils.getInstanceId(crmPairing.getCrmInstanceUrn()));
  }

  /**
   * Get if person account setting is enabled or not
   */
  private Task<Boolean> getIsPersonAccountEnabled(CrmPairing crmPairing) {
    if (crmPairing == null) {
      return Task.value(FALSE);
    }

    CrmPairingUrn crmPairingUrn = new CrmPairingUrn(crmPairing.getContract(), crmPairing.getCrmPairingId());
    return _crmSettingClient.get(crmPairingUrn, SalesConnectedCrmSettingType.PERSON_ACCOUNT_ENABLED)
        .map(setting -> setting != null && setting.hasValue() && setting.getValue().isBoolean()
            && setting.getValue().getBoolean());
  }

  /**
   * partial update list, support list name, last modified time and last viewed time
   * @param listId list Id of the list that need to be updated
   * @param list list object with fields that need to be updated
   * @param seatId seat Id of user that do the update
   * @return if the update succeed. True if succeed. False if not found
   */
  private Task<Boolean> partialUpdateList(long listId, @NonNull List list, long seatId) {

    return _lssListDB.getList(listId).flatMap(foundList -> {
      PolicyType policyType = LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING.get(foundList.listType);
      return getAccessDecision(createSeatUrn(seatId), listId, policyType, AccessAction.UPDATE).flatMap(accessDecision -> {
        if (accessDecision != AccessDecision.ALLOWED) {
          String errMsg = String.format("seat:%d does not have permission to update list:%d", seatId, listId);
          return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, errMsg));
        }
        String listName = list.getName(GetMode.NULL);
        String description = list.getDescription(GetMode.NULL);
        Long lastModifiedTime = list.getLastModifiedAt(GetMode.NULL);
        Long lastViewedAt = list.getLastViewedAt(GetMode.NULL);
        Task<Boolean> updateListTask = Task.value(TRUE);
        if (listName != null || lastModifiedTime != null || description != null || lastViewedAt != null) {
          // need to update list
          SeatUrn seatUrn = new SeatUrn(seatId);
          com.linkedin.sales.espresso.List espressoList = new com.linkedin.sales.espresso.List();
          espressoList.name = listName;
          espressoList.lastModifiedTime = (lastModifiedTime != null) ? lastModifiedTime : 0;
          espressoList.lastViewedTime = lastViewedAt;
          espressoList.description = description;
          espressoList.lastModifiedBySeatUrn = seatUrn.toString();
          updateListTask = _lssListDB.updateList(listId, espressoList);
        }
        return updateListTask;
      });
    });
  }

  public Task<BasicCollectionResult<List>> findAccountMapsByOrg(SeatUrn seat, OrganizationUrn organization) {
    Task<PaginatedList<AccountToListMapping>> ownedAccountMapsTask =
        _salesAccountToListMappingService.getAccountToListMappingForAccount(seat, organization, 0, ServiceConstants.GET_COUNT_LIMIT);
    Policy.ResourceContext resourceContext = new Policy.ResourceContext();
    resourceContext.setOrganization(organization);
    Task<Map<Urn, SubjectPolicy>> policyMapTask =
        _lssSharingDB.getPoliciesByResourceContextAndSubject(seat, resourceContext, PolicyType.ACCOUNT_MAP,
                ServiceConstants.READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)
            .map(policyList -> policyList.getResult().stream().collect(Collectors.toMap(Pair::getFirst, Pair::getSecond)));

    return Task.par(ownedAccountMapsTask, policyMapTask).flatMap((ownedAccountMaps, policyMap) -> {
      Set<Urn> accountMapListUrns = policyMap.keySet();
      // LSS-69448: owned account maps without a sharing policy may or may not be accessible by the seat.
      // - If there are other collaborators on the map, it means the map was created prior to the map sharing feature
      //   and was never viewed by the seat, thus the seat should have access to it;
      // - otherwise, the map has been unshared with seat and the seat should not have access to it.
      Set<Urn> ownedAccountMapsWithoutPolicy = ownedAccountMaps.getResult().stream()
          .map(AccountToListMapping::getList)
          .filter(mapUrn -> !accountMapListUrns.contains(mapUrn))
          .collect(Collectors.toSet());
      Task<Map<Urn, Integer>> mapCollaboratorCountMapTask = ParseqUtils.batchTask(ownedAccountMapsWithoutPolicy,
          mapUrn -> _lssSharingDB.getPoliciesByResource(mapUrn, PolicyType.ACCOUNT_MAP, ServiceConstants.READER_ROLES, 0, 1)
              .map(PaginatedList::getTotal)
              .onFailure(t -> LOG.warn("Failed to get sharing policies for account map {}", mapUrn, t)));

      return mapCollaboratorCountMapTask.flatMap(mapCollaboratorCountMap -> {
        Set<Urn> ownedAccountMapsWithAccess = mapCollaboratorCountMap.entrySet().stream()
            .filter(entry -> entry.getValue() == 0)
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
        Set<Urn> allAccessibleMaps = Stream.concat(accountMapListUrns.stream(), ownedAccountMapsWithAccess.stream())
            .collect(Collectors.toSet());
        if (allAccessibleMaps.isEmpty()) {
          return Task.value(new BasicCollectionResult<>(Collections.emptyList()));
        }

        Set<Long> listIds = allAccessibleMaps.stream().map(Urn::getIdAsLong).collect(Collectors.toSet());
        Task<Map<Long, com.linkedin.sales.espresso.List>> getListMapTask = ParseqUtils.batchTask(listIds,
            listId -> _lssListDB.getList(listId).onFailure(t -> LOG.warn("Failed to get list {}", listId, t)));
        Task<Map<Long, Long>> getListEntityCountMapTask = ParseqUtils.batchTask(listIds,
            listId -> _lssListDB.getListEntityCount(listId).onFailure(t -> LOG.warn("Failed to get entity count for list {}", listId, t)));
        Task<Map<Urn, Integer>> getListsSharingTotalTask =
            _lssSharingDB.batchGetResourceSharingPolicyTotal(accountMapListUrns, PolicyType.ACCOUNT_MAP)
                .onFailure(t -> LOG.warn("Failed to get sharing policies for account map {}", accountMapListUrns, t));

        return Task.par(getListMapTask, getListEntityCountMapTask, getListsSharingTotalTask).map((listMap, listEntityCountMap, listSharingTotalMap) -> {
          java.util.List<List> accountMapLists = listMap.entrySet().stream().map(entry -> {
                long listId = entry.getKey();
                Urn listUrn = UrnUtils.createSalesListUrn(listId);
                int listSharingTotal = listSharingTotalMap.getOrDefault(listUrn, 0);
                long entityCount = listEntityCountMap.getOrDefault(listId, 0L);

                return constructListFromEspresso(listId, entry.getValue(), entityCount, listSharingTotal, null, policyMap.get(listUrn));
              }).sorted(ACCOUNT_MAP_SORT_ORDER).collect(Collectors.toList());
          return new BasicCollectionResult<>(accountMapLists);
        });
      });
    });
  }
}