package com.linkedin.sales.service.list;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.SeatUrnArray;
import com.linkedin.common.TimeRange;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.GetMode;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.sales.urn.SalesRelationshipMapChangeLogsTopicUrn;
import com.linkedin.sales.client.realtime.RealtimeDispatcherClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ChangeTypes;
import com.linkedin.sales.espresso.ResourcePolicyView;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.acl.SubResourceType;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.CollaboratorChangeValue;
import com.linkedin.saleslist.LeadBodyTextChangeValue;
import com.linkedin.saleslist.LeadChangeValue;
import com.linkedin.saleslist.LeadManagerEntityUrn;
import com.linkedin.saleslist.LeadPositionInMapChangeValue;
import com.linkedin.saleslist.ManagerChangeValue;
import com.linkedin.saleslist.MapNameChangeValue;
import com.linkedin.saleslist.OwnerChangeValue;
import com.linkedin.saleslist.RelationshipMapEntity;
import com.linkedin.saleslist.RelationshipStrengthChangeValue;
import com.linkedin.saleslist.RelationshipStrengthType;
import com.linkedin.saleslist.RoleType;
import com.linkedin.saleslist.RoleTypeChangeValue;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import com.linkedin.saleslist.RelationshipMapChangeLog;
import com.linkedin.saleslist.RelationshipMapChangeLogFieldType;
import com.linkedin.saleslist.RelationshipMapChangeLogOperationType;
import com.linkedin.saleslist.RelationshipMapChangeLogValue;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesRelationshipMapChangeLogService {
  private static final String ERROR_MSG_FOR_MISSING_FIELD_IN_CHANGE_VALUE =
      "ChangeLog missing required fields";
  private static final Logger LOG = LoggerFactory.getLogger(SalesRelationshipMapChangeLogService.class);
  private static final int DEFAULT_START_TIME_DAYS_FROM_NOW = 30;
  private static final int MINIMUM_NUMBER_OF_POLICIES_REQUIRED_FOR_SHARING = 2;
  private static final MemberUrn DUMMY_MEMBER_URN = new MemberUrn(0L);

  private final LssListDB _lssListDB;
  private final AclServiceDispatcher _aclServiceDispatcher;
  private final LssSharingDB _lssSharingDB;
  private final RealtimeDispatcherClient _realtimeDispatcherClient;
  private final LixService _lixService;

  public SalesRelationshipMapChangeLogService(LssListDB lssListDB, LssSharingDB lssSharingDB,
      AclServiceDispatcher aclServiceDispatcher, RealtimeDispatcherClient realtimeDispatcherClient,
      LixService lixService) {
    _lssListDB = lssListDB;
    _lssSharingDB = lssSharingDB;
    _aclServiceDispatcher = aclServiceDispatcher;
    _realtimeDispatcherClient = realtimeDispatcherClient;
    _lixService  = lixService;
  }

  /**
   * Create change log for given object.
   * It also publishes changeLog to realtime topic. The result code does not depend on publish results.
   * Error Code-> 400 if error in validating the change log. 5xx -> DB errors.
   */
  public Task<HttpStatus> createChangeLog(@NonNull RelationshipMapChangeLog salesChangeLog,
      @NonNull ContractUrn contractUrn) {
    //Todo:Add acl check. The acl check will include, if the list is a AccountMap, user has access to the AccountMap and the accountMap is a shared map.
    //No acl check has been added on write as writing a changelog happens after a change is successfully completed in RM. No additional checks are required.
    long listId = salesChangeLog.getRelationshipMap().getIdAsLong();
    return _lssListDB.createRelationshipMapChangeLog(listId, validateAndFormatChangeLog(salesChangeLog, contractUrn))
        .flatMap(changeLogId -> {
          RelationshipMapChangeLog publishChangeLog = salesChangeLog.copy().setId(changeLogId);
          return publishChangeLog(publishChangeLog);
        })
        .map(ignoreResult -> HttpStatus.S_201_CREATED);
  }

  /**
   * Find all change logs in given query.
   * StartTime is checked for acl access. StartTime is taken as max of either the default startTime of 30 days
   * or the time user had access to the map.
   * For the first user that shared the map, we take the time map became shared. This is the time when the first collaborator was added to the map.
   * Returns 200 if query complete. 200 is also returned if no entities were found.
   * Error Code -> 400 provided start time is greater then end time. 5xx -> Result decoration failed.
   */
  public Task<BasicCollectionResult<RelationshipMapChangeLog>> findByRelationshipMap(
      @NonNull SalesListUrn relationshipMapListUrn, @NonNull SeatUrn viewerSeat, int start, int count,
      @Nullable TimeRange timeRange) {
    long listId = relationshipMapListUrn.getIdAsLong();
    //Same access as reading entities for Account Map.
    Task<AccessDecision> accessDecisionTask =
        _aclServiceDispatcher.checkAccessDecision(viewerSeat, PolicyType.ACCOUNT_MAP, relationshipMapListUrn,
            AccessAction.READ, SubResourceType.LIST_ENTITY);

    Task<Boolean> isReadOnPlaceholderCardChangeLogEnabledTask = _lixService.isEEPLixEnabledForSeat(viewerSeat,
        LixUtils.LSS_RELATIONSHIP_MAP_ENABLE_PLACEHOLDER_CARD_CHANGE_LOG);

    return Task.par(accessDecisionTask, isReadOnPlaceholderCardChangeLogEnabledTask)
        .flatMap((accessDecision, isReadOnPlaceholderCardChangeLogEnabled) -> {
      if (accessDecision != AccessDecision.ALLOWED) {
        String errMsg = String.format("seat:%d does not have permission to view change logs for relationship map:%d",
            viewerSeat.getSeatIdEntity(), relationshipMapListUrn.getIdAsLong());
        return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, errMsg));
      }

      long endTime = (timeRange != null && timeRange.hasEnd()) ? timeRange.getEnd() : Long.MAX_VALUE;
      long startTime = (timeRange != null && timeRange.hasStart()) ? timeRange.getStart() : 0;
      if (startTime > endTime) {
        return Task.failure(
            new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Start time should be less than end time"));
      }
      //User is only allowed to view change logs from their acl start time.
      return getChangeLogAclStartTime(relationshipMapListUrn, viewerSeat).flatMap(
          aclStartTime -> _lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(listId,
              Long.max(aclStartTime, startTime), endTime, start, count).map(res -> {
            int totalCount = res.getFirst();
            int changeLogCountBeforeFiltering = res.getSecond().size();
            List<RelationshipMapChangeLog> changeLogs = res.getSecond()
                .stream()
                .map(espressoChangeLog -> convertEspressoChangeLog(listId, espressoChangeLog.getSecond(), espressoChangeLog.getFirst()))
                .filter(Objects::nonNull)
                .filter(changeLog -> isReadOnChangeLogAllowed(changeLog, isReadOnPlaceholderCardChangeLogEnabled))
                .collect(Collectors.toList());
            int removedChangeLogCount = changeLogCountBeforeFiltering - changeLogs.size();
            return new BasicCollectionResult<>(changeLogs, totalCount - removedChangeLogCount);
          }));
    });
  }

  //Publish RelationshipMapChangeLog to SalesRelationshipMapChangeLogsTopic topic in realtime platform. Any errors/exceptions are logged inside.
  private Task<Boolean> publishChangeLog(RelationshipMapChangeLog relationshipMapChangeLog) {
    SalesListUrn listUrn = relationshipMapChangeLog.getRelationshipMap();
    SalesRelationshipMapChangeLogsTopicUrn topicUrn = new SalesRelationshipMapChangeLogsTopicUrn(listUrn);
    SeatUrn creatorSeatUrn = relationshipMapChangeLog.getActorSeat();
    return _lixService.isEEPLixEnabledForSeat(creatorSeatUrn,
        LixUtils.LSS_RELATIONSHIP_MAP_ENABLE_PLACEHOLDER_CARD_CHANGE_LOG).flatMap(isReadOnPlaceholderCardChangeLogEnabled -> {
          //We can only publish changeLogs if we are allowed to read on them.
      if (isReadOnChangeLogAllowed(relationshipMapChangeLog, isReadOnPlaceholderCardChangeLogEnabled)) {
        return _realtimeDispatcherClient.publishEvent(topicUrn, relationshipMapChangeLog, null).map(httpStatus -> {
          if (HttpStatus.S_202_ACCEPTED == httpStatus) {
            return Boolean.TRUE;
          }
          LOG.error("Unexpected status was returned by realtime dispatcher {}", httpStatus.getCode());
          return Boolean.FALSE;
        }).recoverWith(e -> {
          LOG.error("Exception occurred in publishing change log: {}", e.getMessage());
          return Task.value(Boolean.FALSE);
        });
      }
      LOG.warn("Realtime publish of change log is disabled for seat: {}", creatorSeatUrn);
      return Task.value(Boolean.FALSE);
    });
  }

  @Nullable
  private RelationshipMapChangeLog convertEspressoChangeLog(long relationshipMapListId,
      @NonNull com.linkedin.sales.espresso.RelationshipMapChangeLog espressoChangeLog, long changeLogId) {
    RelationshipMapChangeLog changeLog = new RelationshipMapChangeLog();
    changeLog.setRelationshipMap(UrnUtils.createSalesListUrn(relationshipMapListId));
    changeLog.setPerformedAt(espressoChangeLog.getEventTime());
    changeLog.setActorSeat(UrnUtils.createSeatUrn(espressoChangeLog.getActorSeatUrn()));
    changeLog.setId(changeLogId);

    if (espressoChangeLog.getPreviousChangeValue() == null && espressoChangeLog.getChangeValue() == null
            && espressoChangeLog.getAddedValues() == null && espressoChangeLog.getRemovedValues() == null) {
      LOG.error(
          "Unable to determine OperationType for change log when converting espresso object for relationshipMap {}",
          relationshipMapListId);
      return null;
    }
    ImmutableSet<ChangeTypes> leadUpdateFields =
        ImmutableSet.of(ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, ChangeTypes.UPDATE_ROLE, ChangeTypes.UPDATE_MANAGER,
            ChangeTypes.UPDATE_OWNER, ChangeTypes.UPDATE_LEAD_POSITION_IN_MAP);

    if (leadUpdateFields.contains(espressoChangeLog.changeType) && espressoChangeLog.getTargetMemberUrn() == null
        && espressoChangeLog.getTargetSalesListEntityPlaceholderUrn() == null) {
      LOG.error(
          "Target Lead not found in espresso change log for field type that requires it for relationshipMap {}",
          relationshipMapListId);
      return null;
    }

    Urn targetLeadUrn = null;
    Pair<MemberUrn, RelationshipMapEntity> targetLeadEntityUrns = null;

    switch (espressoChangeLog.changeType) {
      case EDIT_LEAD:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        LeadChangeValue leadChangeValue = new LeadChangeValue();
        if (espressoChangeLog.getChangeValue() != null) {
          Pair<MemberUrn, RelationshipMapEntity> currentValueEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(
              UrnUtils.createUrnFromString(espressoChangeLog.getChangeValue().toString()));
          //todo: LSS-78258 Cleanup CurrentValue
          leadChangeValue.setCurrentValue(currentValueEntityUrns.getFirst());
          leadChangeValue.setCurrentValueEntity(currentValueEntityUrns.getSecond());
        }
        if (espressoChangeLog.getPreviousChangeValue() != null) {
          Pair<MemberUrn, RelationshipMapEntity> previousValueEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(
              UrnUtils.createUrnFromString(espressoChangeLog.getPreviousChangeValue().toString()));
          //todo: LSS-78258 Cleanup PreviousValue
          leadChangeValue.setPreviousValue(previousValueEntityUrns.getFirst());
          leadChangeValue.setPreviousValueEntity(previousValueEntityUrns.getSecond());
        }
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithLead(leadChangeValue));
        return changeLog;

      case UPDATE_MANAGER:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        ManagerChangeValue managerChangeValue = new ManagerChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        //todo: LSS-78258 Cleanup TargetLead
        managerChangeValue.setTargetLead(targetLeadEntityUrns.getFirst());
        managerChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        if (espressoChangeLog.getChangeValue() != null) {
          Pair<MemberUrn, LeadManagerEntityUrn> currentManagerValueEntityUrns =
              createLeadManagerEntityUrnsForChangeLogValue(
                  UrnUtils.createUrnFromString(espressoChangeLog.getChangeValue().toString()));
          //todo: LSS-78258 Cleanup CurrentValue
          managerChangeValue.setCurrentValue(currentManagerValueEntityUrns.getFirst());
          managerChangeValue.setCurrentValueEntity(currentManagerValueEntityUrns.getSecond());
        }
        if (espressoChangeLog.getPreviousChangeValue() != null) {
          Pair<MemberUrn, LeadManagerEntityUrn> previousManagerValueEntityUrns =
              createLeadManagerEntityUrnsForChangeLogValue(
                  UrnUtils.createUrnFromString(espressoChangeLog.getPreviousChangeValue().toString()));
          //todo: LSS-78258 Cleanup PreviousValue
          managerChangeValue.setPreviousValue(previousManagerValueEntityUrns.getFirst());
          managerChangeValue.setPreviousValueEntity(previousManagerValueEntityUrns.getSecond());
        }
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithManager(managerChangeValue));
        return changeLog;

      case UPDATE_OWNER:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        OwnerChangeValue ownerChangeValue = new OwnerChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        //todo: LSS-78258 Cleanup TargetLead
        ownerChangeValue.setTargetLead(targetLeadEntityUrns.getFirst());
        ownerChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        if (espressoChangeLog.getChangeValue() != null) {
          ownerChangeValue.setCurrentValue(UrnUtils.createSeatUrn(espressoChangeLog.getChangeValue()));
        }
        if (espressoChangeLog.getPreviousChangeValue() != null) {
          ownerChangeValue.setPreviousValue(UrnUtils.createSeatUrn(espressoChangeLog.getPreviousChangeValue()));
        }
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithOwner(ownerChangeValue));
        return changeLog;

      case UPDATE_ROLE:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        RoleTypeChangeValue roleTypeChangeValue = new RoleTypeChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        //todo: LSS-78258 Cleanup TargetLead
        roleTypeChangeValue.setTargetLead(targetLeadEntityUrns.getFirst());
        roleTypeChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        try {
          if (espressoChangeLog.getChangeValue() != null) {
            roleTypeChangeValue.setCurrentValue(RoleType.valueOf(espressoChangeLog.getChangeValue().toString()));
          }
          if (espressoChangeLog.getPreviousChangeValue() != null) {
            roleTypeChangeValue.setPreviousValue(
                RoleType.valueOf(espressoChangeLog.getPreviousChangeValue().toString()));
          }
        } catch (IllegalArgumentException e) {
          LOG.error(
              "Unable to convert changeValue/previousChangeValue stored in espresso to roleType for relationshipMap {}",
              relationshipMapListId);
          return null;
        }
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithRoleType(roleTypeChangeValue));
        return changeLog;

      case UPDATE_RELATIONSHIP_STRENGTH:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH);
        RelationshipStrengthChangeValue relationshipStrengthChangeValue = new RelationshipStrengthChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        //todo: LSS-78258 Cleanup TargetLead
        relationshipStrengthChangeValue.setTargetLead(targetLeadEntityUrns.getFirst());
        relationshipStrengthChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        try {
          if (espressoChangeLog.getChangeValue() != null) {
            relationshipStrengthChangeValue.setCurrentValue(
                RelationshipStrengthType.valueOf(espressoChangeLog.getChangeValue().toString()));
          }
          if (espressoChangeLog.getPreviousChangeValue() != null) {
            relationshipStrengthChangeValue.setPreviousValue(
                RelationshipStrengthType.valueOf(espressoChangeLog.getPreviousChangeValue().toString()));
          }
        } catch (IllegalArgumentException e) {
          LOG.error(
              "Unable to convert changeValue/previousChangeValue stored in espresso to roleType for relationshipMap {}",
              relationshipMapListId);
          return null;
        }
        changeLog.setChangeValue(
            RelationshipMapChangeLogValue.createWithRelationshipStrength(relationshipStrengthChangeValue));
        return changeLog;

      case UPDATE_MAP_NAME:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_NAME);
        MapNameChangeValue mapNameChangeValue = new MapNameChangeValue();
        try {
          mapNameChangeValue.setCurrentValue(espressoChangeLog.getChangeValue().toString());
          mapNameChangeValue.setPreviousValue(espressoChangeLog.getPreviousChangeValue().toString());
          changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithMapName(mapNameChangeValue));
          changeLog.setOperationType(RelationshipMapChangeLogOperationType.REPLACE);
        } catch (NullPointerException e) {
          LOG.error(
              "Unable to convert changeValue/previousChangeValue stored in espresso for MAP_NAME field type for relationshipMap {} and changelogID {}",
              relationshipMapListId, changeLogId);
          return null;
        }
        return changeLog;

      case UPDATE_LEAD_POSITION_IN_MAP:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_POSITION_IN_MAP);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        LeadPositionInMapChangeValue leadPositionInMapChangeValue = new LeadPositionInMapChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        //todo: LSS-78258 Cleanup TargetLead
        leadPositionInMapChangeValue.setTargetLead(targetLeadEntityUrns.getFirst());
        leadPositionInMapChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        try {
          leadPositionInMapChangeValue.setCurrentValue(Integer.valueOf(espressoChangeLog.getChangeValue().toString()));
          if (espressoChangeLog.getPreviousChangeValue() != null) {
            leadPositionInMapChangeValue.setPreviousValue(
                Integer.valueOf(espressoChangeLog.getPreviousChangeValue().toString()));
          }
          changeLog.setChangeValue(
              RelationshipMapChangeLogValue.createWithLeadPositionInMap(leadPositionInMapChangeValue));
        } catch (Exception e) {
          LOG.error(
              "Unable to convert changeValue/previousChangeValue stored in espresso for LEAD_POSITION_IN_MAP field type "
                  + "for relationshipMap {} and changelogID {}", relationshipMapListId, changeLogId);
          return null;
        }
        return changeLog;

      case UPDATE_MAP_COLLABORATORS:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_COLLABORATORS);
        CollaboratorChangeValue collaboratorChangeValue = new CollaboratorChangeValue();
        if (espressoChangeLog.getAddedValues() != null) {
          collaboratorChangeValue.setCurrentValuesAdded(new SeatUrnArray(
              espressoChangeLog.getAddedValues().stream().map(UrnUtils::createSeatUrn).collect(Collectors.toList())));
        }
        if (espressoChangeLog.getRemovedValues() != null) {
          collaboratorChangeValue.setPreviousValuesRemoved(new SeatUrnArray(
              espressoChangeLog.getRemovedValues().stream().map(UrnUtils::createSeatUrn).collect(Collectors.toList())));
        }
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getAddedValues(), espressoChangeLog.getRemovedValues()));
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithMapCollaborators(collaboratorChangeValue));
        return changeLog;

      case UPDATE_LEAD_TEXT:
        changeLog.setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_BODY_TEXT);
        changeLog.setOperationType(
            getOperationType(espressoChangeLog.getChangeValue(), espressoChangeLog.getPreviousChangeValue()));
        LeadBodyTextChangeValue leadBodyTextChangeValue = new LeadBodyTextChangeValue();
        targetLeadUrn = getEspressoTargetLeadUrn(espressoChangeLog);
        targetLeadEntityUrns = createRelationshipMapEntityUrnsForChangeLogValue(targetLeadUrn);
        leadBodyTextChangeValue.setTargetLeadEntity(targetLeadEntityUrns.getSecond());
        if (espressoChangeLog.getChangeValue() != null) {
          leadBodyTextChangeValue.setCurrentValue(String.valueOf(espressoChangeLog.getChangeValue()));
        }
        if (espressoChangeLog.getPreviousChangeValue() != null) {
          leadBodyTextChangeValue.setPreviousValue(espressoChangeLog.getPreviousChangeValue().toString());
        }
        changeLog.setChangeValue(RelationshipMapChangeLogValue.createWithLeadBodyText(leadBodyTextChangeValue));
        return changeLog;

      default:
        LOG.error("Unable to format espresso changelog enum to corresponding api changeLog field type and value");
        return null;
    }
  }

  RelationshipMapChangeLogOperationType getOperationType(Object currentValue, Object previousValue) {
    RelationshipMapChangeLogOperationType operationType = null;
    if (currentValue == null && previousValue == null) {
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
          "Both currentValue && previousValue cannot be null for given changelog");
    }
    if (currentValue != null) {
      operationType = RelationshipMapChangeLogOperationType.ADD;
    }
    if (previousValue != null) {
      operationType = operationType != null ? RelationshipMapChangeLogOperationType.REPLACE
          : RelationshipMapChangeLogOperationType.REMOVE;
    }
    return operationType;
  }

  /**
   * It assumes given user has access to the map and the map is a shared map.
   * Get policy for currentViewer. If not found set the acl time to default time.
   * If there are other collaborators which don't have the acceptedTime set, meaning the map was shared before the field was introduced,
   * in this case max of currentViewerAccess time and default time is returned.
   * The original sharer of the map can have a owner or writer policy. If everybody has acceptedTime set, we take max of the time from policy or default time.
   * Default time is set to 30 days.
   * The logic assumes every collaborator has a sharing policy. It can be writer or owner policy.
   * Returns max value when the map doesn't satisfy the required number of sharing policies to be able to view change logs.
   */
  private Task<Long> getChangeLogAclStartTime(SalesListUrn relationshipMapListUrn, SeatUrn seatUrn) {
    long defaultTime = Instant.now().minus(DEFAULT_START_TIME_DAYS_FROM_NOW, ChronoUnit.DAYS).toEpochMilli();

    Task<PaginatedList<Pair<Urn, ResourcePolicyView>>> res =
        _lssSharingDB.getPolicyViewsByResource(relationshipMapListUrn, PolicyType.ACCOUNT_MAP,
            ServiceConstants.WRITER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT);

    return res.map(resourcePolicyPairsRes -> {
      List<Pair<Urn, ResourcePolicyView>> resourcePolicyPairs = resourcePolicyPairsRes.getResult();
      if (resourcePolicyPairs.size() < MINIMUM_NUMBER_OF_POLICIES_REQUIRED_FOR_SHARING) {
        LOG.warn(
            "The requested relationshipMap has {} writer/owner sharing policies which is less than required number of sharing policies.",
            resourcePolicyPairs.size());
        return Long.MAX_VALUE;
      }
      long viewerSharingPolicyAcceptedTime = defaultTime;
      long minSharingPolicyAcceptedTimeForOtherCollaborators = Long.MAX_VALUE;

      for (Pair<Urn, ResourcePolicyView> policyPair : resourcePolicyPairs) {
        if (policyPair.getFirst().equals(seatUrn)) {
          if (policyPair.getSecond().acceptedTime != null) {
            viewerSharingPolicyAcceptedTime =
                Long.max(viewerSharingPolicyAcceptedTime, policyPair.getSecond().acceptedTime);
          }
        } else {
          //handle null case. Null -> -1
          minSharingPolicyAcceptedTimeForOtherCollaborators =
              policyPair.getSecond().acceptedTime != null ? Math.min(minSharingPolicyAcceptedTimeForOtherCollaborators,
                  policyPair.getSecond().acceptedTime) : -1;
        }
      }

      if (minSharingPolicyAcceptedTimeForOtherCollaborators == -1
          || minSharingPolicyAcceptedTimeForOtherCollaborators == Long.MAX_VALUE) {
        minSharingPolicyAcceptedTimeForOtherCollaborators = defaultTime;
      }
      return Long.max(viewerSharingPolicyAcceptedTime, minSharingPolicyAcceptedTimeForOtherCollaborators);
    }).onFailure(e -> LOG.error("Failed to get change log acl startTime for relationshipMap {} with exception: ",
            relationshipMapListUrn.getIdAsLong(), e));
  }

  private com.linkedin.sales.espresso.RelationshipMapChangeLog validateAndFormatChangeLog(
      RelationshipMapChangeLog salesChangeLog, ContractUrn contractUrn) {
    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoObject =
        new com.linkedin.sales.espresso.RelationshipMapChangeLog();

    espressoObject.setActorSeatUrn(salesChangeLog.getActorSeat().toString());
    espressoObject.setActorContractUrn(contractUrn.toString());
    espressoObject.setEventTime(salesChangeLog.getPerformedAt());

    RelationshipMapChangeLogFieldType changeLogFieldType = salesChangeLog.getChangeFieldType();
    RelationshipMapChangeLogOperationType changeLogOperationType = salesChangeLog.getOperationType();
    RelationshipMapChangeLogValue changeValue = salesChangeLog.getChangeValue();

    String currentValue = null;
    String previousValue = null;
    RelationshipMapEntity targetLead = null;
    List<CharSequence> addedValues = null;
    List<CharSequence> removedValues = null;
    try {
      boolean shouldCurrentValueOrAddedValuesExists =
          changeLogOperationType == RelationshipMapChangeLogOperationType.ADD
              || changeLogOperationType == RelationshipMapChangeLogOperationType.REPLACE;

      boolean shouldPreviousValueOrRemovedValuesExists =
          changeLogOperationType == RelationshipMapChangeLogOperationType.REMOVE
              || changeLogOperationType == RelationshipMapChangeLogOperationType.REPLACE;

      switch (changeLogFieldType) {
        case LEAD:
          espressoObject.setChangeType(ChangeTypes.EDIT_LEAD);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = getValueForLeadChange(changeValue.getLead().getCurrentValue(GetMode.NULL),
                changeValue.getLead().getCurrentValueEntity(GetMode.NULL));
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = getValueForLeadChange(changeValue.getLead().getPreviousValue(GetMode.NULL),
                changeValue.getLead().getPreviousValueEntity(GetMode.NULL));
          }
          break;
        case MANAGER:
          espressoObject.setChangeType(ChangeTypes.UPDATE_MANAGER);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = getValueForManagerChange(changeValue.getManager().getCurrentValue(GetMode.NULL),
                changeValue.getManager().getCurrentValueEntity(GetMode.NULL));
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = getValueForManagerChange(changeValue.getManager().getPreviousValue(GetMode.NULL),
                changeValue.getManager().getPreviousValueEntity(GetMode.NULL));
          }
          targetLead = getTargetLead(changeValue.getManager().getTargetLead(GetMode.NULL),
              changeValue.getManager().getTargetLeadEntity(GetMode.NULL));
          break;
        case ROLE:
          espressoObject.setChangeType(ChangeTypes.UPDATE_ROLE);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = changeValue.getRoleType().getCurrentValue(GetMode.NULL).toString();
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = changeValue.getRoleType().getPreviousValue(GetMode.NULL).toString();
          }
          targetLead = getTargetLead(changeValue.getRoleType().getTargetLead(GetMode.NULL),
              changeValue.getRoleType().getTargetLeadEntity(GetMode.NULL));
          break;
        case RELATIONSHIP_STRENGTH:
          espressoObject.setChangeType(ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = changeValue.getRelationshipStrength().getCurrentValue(GetMode.NULL).toString();
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = changeValue.getRelationshipStrength().getPreviousValue(GetMode.NULL).toString();
          }
          targetLead = getTargetLead(changeValue.getRelationshipStrength().getTargetLead(GetMode.NULL),
              changeValue.getRelationshipStrength().getTargetLeadEntity(GetMode.NULL));
          break;
        case OWNER:
          espressoObject.setChangeType(ChangeTypes.UPDATE_OWNER);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = changeValue.getOwner().getCurrentValue(GetMode.NULL).toString();
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = changeValue.getOwner().getPreviousValue(GetMode.NULL).toString();
          }
          targetLead = getTargetLead(changeValue.getOwner().getTargetLead(GetMode.NULL),
              changeValue.getOwner().getTargetLeadEntity(GetMode.NULL));
          break;
        case MAP_NAME:
          espressoObject.setChangeType(ChangeTypes.UPDATE_MAP_NAME);
          //Only replace operation is supported.
          currentValue = Objects.requireNonNull(changeValue.getMapName().getCurrentValue(GetMode.NULL));
          previousValue = Objects.requireNonNull(changeValue.getMapName().getPreviousValue(GetMode.NULL));
          break;
        case LEAD_POSITION_IN_MAP:
          espressoObject.setChangeType(ChangeTypes.UPDATE_LEAD_POSITION_IN_MAP);
          //Only Add and Replace operation are supported.
          currentValue = changeValue.getLeadPositionInMap().getCurrentValue(GetMode.NULL).toString();
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = changeValue.getLeadPositionInMap().getPreviousValue(GetMode.NULL).toString();
          }
          targetLead = getTargetLead(changeValue.getLeadPositionInMap().getTargetLead(GetMode.NULL),
              changeValue.getLeadPositionInMap().getTargetLeadEntity(GetMode.NULL));
          break;
        case MAP_COLLABORATORS:
          espressoObject.setChangeType(ChangeTypes.UPDATE_MAP_COLLABORATORS);
          if (shouldCurrentValueOrAddedValuesExists) {
            addedValues = changeValue.getMapCollaborators()
                .getCurrentValuesAdded(GetMode.NULL)
                .stream()
                .map(Urn::toString)
                .collect(Collectors.toList());
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            removedValues = changeValue.getMapCollaborators()
                .getPreviousValuesRemoved(GetMode.NULL)
                .stream()
                .map(Urn::toString)
                .collect(Collectors.toList());
          }
          break;
        case LEAD_BODY_TEXT:
          espressoObject.setChangeType(ChangeTypes.UPDATE_LEAD_TEXT);
          if (shouldCurrentValueOrAddedValuesExists) {
            currentValue = Objects.requireNonNull(changeValue.getLeadBodyText().getCurrentValue(GetMode.NULL));
          }
          if (shouldPreviousValueOrRemovedValuesExists) {
            previousValue = Objects.requireNonNull(changeValue.getLeadBodyText().getPreviousValue(GetMode.NULL));
          }
          targetLead = changeValue.getLeadBodyText().getTargetLeadEntity();
          break;
        default:
          String error = String.format("No valid espresso operation type found for given field type: %s", changeLogFieldType);
          throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, error);
      }
    } catch (NullPointerException e) {
      throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, ERROR_MSG_FOR_MISSING_FIELD_IN_CHANGE_VALUE);
    }

    espressoObject.setChangeValue(currentValue);
    espressoObject.setPreviousChangeValue(previousValue);
    if (targetLead != null) {
      if (targetLead.isMember()) {
        espressoObject.setTargetMemberUrn(targetLead.getMember().toString());
      } else if (targetLead.isSalesListEntityPlaceholder()) {
        espressoObject.setTargetSalesListEntityPlaceholderUrn(targetLead.getSalesListEntityPlaceholder().toString());
      } else {
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("TargetLeadEntity type not supported. %s", targetLead));
      }
    }
    espressoObject.setAddedValues(addedValues);
    espressoObject.setRemovedValues(removedValues);
    return espressoObject;
  }

  //todo: LSS-78258 Remove usage of changeValue
  private String getValueForManagerChange(@Nullable MemberUrn changeValue, @Nullable LeadManagerEntityUrn changeValueEntity) {
    if (Objects.nonNull(changeValueEntity)) {
      if (changeValueEntity.isMember()) {
        return changeValueEntity.getMember().toString();
      } else if (changeValueEntity.isSalesListEntityPlaceholder()) {
        return changeValueEntity.getSalesListEntityPlaceholder().toString();
      } else {
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("changeValueEntity type not supported for value: %s", changeValueEntity));
      }
    }
    return Objects.requireNonNull(changeValue).toString();
  }
  //todo: LSS-78258 Remove usage of changeValue
  private String getValueForLeadChange(@Nullable MemberUrn changeValue, @Nullable RelationshipMapEntity changeValueEntity) {
    if (Objects.nonNull(changeValueEntity)) {
      if (changeValueEntity.isMember()) {
        return changeValueEntity.getMember().toString();
      } else if (changeValueEntity.isSalesListEntityPlaceholder()) {
        return changeValueEntity.getSalesListEntityPlaceholder().toString();
      } else {
        throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            String.format("changeValueEntity type not supported for value: %s", changeValueEntity));
      }
    }
    return Objects.requireNonNull(changeValue).toString();
  }

  //todo: LSS-78258 Remove usage of targetMemberUrn
  private RelationshipMapEntity getTargetLead(@Nullable MemberUrn targetMemberUrn,
      @Nullable RelationshipMapEntity targetLeadEntity) {
    if (Objects.nonNull(targetLeadEntity)) {
      return targetLeadEntity;
    } else {
      return RelationshipMapEntity.createWithMember(Objects.requireNonNull(targetMemberUrn));
    }
  }
  //todo: LSS-78258 Cleanup targetLeadMemberUrn
  private Urn getEspressoTargetLeadUrn(
      @NonNull com.linkedin.sales.espresso.RelationshipMapChangeLog espressoChangeLog) {
    CharSequence targetLeadMemberUrn = espressoChangeLog.getTargetMemberUrn();
    CharSequence targetLeadSalesListPlaceholderUrn = espressoChangeLog.getTargetSalesListEntityPlaceholderUrn();
    CharSequence targetLead;
    if (targetLeadMemberUrn == null && targetLeadSalesListPlaceholderUrn == null) {
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
          "Both targetLeadMemberUrn && targetLeadSalesListPlaceholderUrn cannot be null for given changelog");
    } else if (targetLeadMemberUrn == null) {
      targetLead = targetLeadSalesListPlaceholderUrn;
    } else {
      targetLead = targetLeadMemberUrn;
    }
    return UrnUtils.createUrnFromString(targetLead.toString());
  }

  //todo: LSS-78258 Return only LeadManagerEntityUrn.
  @NonNull
  private Pair<MemberUrn, LeadManagerEntityUrn> createLeadManagerEntityUrnsForChangeLogValue(
      @NonNull Urn espressoEntityUrn) {
    if (MemberUrn.ENTITY_TYPE.equals(espressoEntityUrn.getEntityType())) {
      MemberUrn memberLead = UrnUtils.createMemberUrn(espressoEntityUrn.toString());
      return Pair.make(memberLead, LeadManagerEntityUrn.createWithMember(memberLead));
    } else if (SalesListEntityPlaceholderUrn.ENTITY_TYPE.equals(espressoEntityUrn.getEntityType())) {
      return Pair.make(DUMMY_MEMBER_URN, LeadManagerEntityUrn.createWithSalesListEntityPlaceholder(
          UrnUtils.createSalesListEntityPlaceholderUrn(espressoEntityUrn.toString())));
    }
    throw new UnsupportedOperationException(
        String.format("RelationshipMapEntity Type not supported for given entity: %s", espressoEntityUrn));
  }
  //todo: LSS-78258 Return only RelationshipMapEntity.
  @NonNull
  private Pair<MemberUrn, RelationshipMapEntity> createRelationshipMapEntityUrnsForChangeLogValue(
      @NonNull Urn espressoEntityUrn) {
    if (MemberUrn.ENTITY_TYPE.equals(espressoEntityUrn.getEntityType())) {
      MemberUrn memberLead = UrnUtils.createMemberUrn(espressoEntityUrn.toString());
      return Pair.make(memberLead, RelationshipMapEntity.createWithMember(memberLead));
    } else if (SalesListEntityPlaceholderUrn.ENTITY_TYPE.equals(espressoEntityUrn.getEntityType())) {
      return Pair.make(DUMMY_MEMBER_URN, RelationshipMapEntity.createWithSalesListEntityPlaceholder(
          UrnUtils.createSalesListEntityPlaceholderUrn(espressoEntityUrn.toString())));
    }
    throw new UnsupportedOperationException(
        String.format("RelationshipMapEntity Type not supported for given entity: %s", espressoEntityUrn));
  }

  private boolean isReadOnChangeLogAllowed(@NonNull RelationshipMapChangeLog relationshipMapChangeLog,
      boolean isReadOnPlaceholderCardChangeLogAllowed) {
    boolean isPlaceholderCardChangeLog = checkChangeLogForPlaceholderCard(relationshipMapChangeLog);
    return !isPlaceholderCardChangeLog || isReadOnPlaceholderCardChangeLogAllowed;
  }

  private boolean checkChangeLogForPlaceholderCard(@NonNull RelationshipMapChangeLog relationshipMapChangeLog) {
    RelationshipMapChangeLogValue changeLogValue = relationshipMapChangeLog.getChangeValue();
    boolean isPlaceholderCard = false;
    if (changeLogValue.isLead()) {
      isPlaceholderCard =
          isRelationshipMapEntityPlaceholderCard(changeLogValue.getLead().getCurrentValueEntity(GetMode.NULL))
              || isRelationshipMapEntityPlaceholderCard(changeLogValue.getLead().getPreviousValueEntity(GetMode.NULL));
    } else if (changeLogValue.isOwner()) {
      isPlaceholderCard =
          isRelationshipMapEntityPlaceholderCard(changeLogValue.getOwner().getTargetLeadEntity(GetMode.NULL));
    } else if (changeLogValue.isLeadBodyText()) {
      isPlaceholderCard =
          isRelationshipMapEntityPlaceholderCard(changeLogValue.getLeadBodyText().getTargetLeadEntity(GetMode.NULL));
    } else if (changeLogValue.isLeadPositionInMap()) {
      isPlaceholderCard = isRelationshipMapEntityPlaceholderCard(
          changeLogValue.getLeadPositionInMap().getTargetLeadEntity(GetMode.NULL));
    } else if (changeLogValue.isRelationshipStrength()) {
      isPlaceholderCard = isRelationshipMapEntityPlaceholderCard(
          changeLogValue.getRelationshipStrength().getTargetLeadEntity(GetMode.NULL));
    } else if (changeLogValue.isRoleType()) {
      isPlaceholderCard =
          isRelationshipMapEntityPlaceholderCard(changeLogValue.getRoleType().getTargetLeadEntity(GetMode.NULL));
    } else if (changeLogValue.isManager()) {
      ManagerChangeValue managerChangeValue = changeLogValue.getManager();
      isPlaceholderCard = isRelationshipMapEntityPlaceholderCard(managerChangeValue.getTargetLeadEntity(GetMode.NULL))
          || isLeadManagerPlaceholderCard(managerChangeValue.getCurrentValueEntity(GetMode.NULL))
          || isLeadManagerPlaceholderCard(managerChangeValue.getPreviousValueEntity(GetMode.NULL));
    }
    return isPlaceholderCard;
  }

  private boolean isRelationshipMapEntityPlaceholderCard(@Nullable RelationshipMapEntity relationshipMapEntity) {
    if (relationshipMapEntity == null) {
      return false;
    }
    return relationshipMapEntity.isSalesListEntityPlaceholder();
  }

  private boolean isLeadManagerPlaceholderCard(@Nullable LeadManagerEntityUrn leadManagerEntityUrn) {
    if (leadManagerEntityUrn == null) {
      return false;
    }
    return leadManagerEntityUrn.isSalesListEntityPlaceholder();
  }
}
