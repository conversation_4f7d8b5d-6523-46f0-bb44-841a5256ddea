package com.linkedin.sales.service.note;

import com.linkedin.common.AttributeArray;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssNoteDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.AttributedText;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.Note;
import com.linkedin.salesnote.NoteKey;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static java.lang.Boolean.*;


/**
 * Sales Note Service
 */
public class SalesNoteService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesNoteService.class);

  private final LssNoteDB _lssNoteDB;
  private final LssSharingDB _lssSharingDB;
  private static final Long RETENTION_TIME_SHARE_SEARCH = 91L;

  public SalesNoteService(LssNoteDB lssNoteDB, LssSharingDB lssSharingDB) {
    _lssNoteDB = lssNoteDB;
    _lssSharingDB = lssSharingDB;
  }

  /**
   * create a Note
   * @param  note content of the Note
   * @return create response to tell if the Note is created successfully
   */
  public Task<CreateResponse> createNote(@NonNull Note note) {
    if (!isNoteComplete(note)) {
      return Task.failure(
          new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Not all the required fields are present"));
    }
    // Performs note creation in db
    com.linkedin.sales.espresso.Note espressoNote = convertNoteToEspressoNoteForCreation(note);
    SeatUrn seatUrn = note.getSeat();
    AnnotatableEntityUrn entityUrn = note.getEntity();
    Long retentionDaysFromEntity = calculateRetentionDaysFromEntity(entityUrn);
    return _lssNoteDB.createNote(seatUrn, entityUrn, espressoNote, retentionDaysFromEntity).map(noteId -> {
     NoteKey noteKey = new NoteKey().setNoteId(noteId).setSeat(seatUrn).setEntity(entityUrn);
     return new CreateResponse(new ComplexResourceKey<>(noteKey, new EmptyRecord()));
    }).onFailure(t -> LOG.warn("Failed to create the note", t));
  }

  /**
   * delete a Note
   * @param noteKey key of the Note
   * @param viewerSeat requester SeatUrn
   * @param contractUrn requester's contractUrn
   * @return whether delete the note successfully
   */
  public Task<UpdateResponse> deleteNote(@NonNull NoteKey noteKey, @NonNull SeatUrn viewerSeat,
      @NonNull ContractUrn contractUrn) {
    SeatUrn seatUrn = noteKey.getSeat();
    if (!viewerSeat.getIdAsLong().equals(seatUrn.getIdAsLong())) {
      return Task.failure(
          new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, "Access denied when requesting delete note"));
    }

    Task<UpdateResponse> deleteNoteTask =
        _lssNoteDB.deleteNote(noteKey.getSeat(), noteKey.getEntity(), noteKey.getNoteId()).flatMap(isDeleted -> {
          if (!isDeleted) {
            LOG.error("Failed to delete note. Note ID is : {}", noteKey.getNoteId());
          }
          return Task.value(new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
        }).onFailure(t -> LOG.warn("Failed to delete note", t));

    Urn entityUrn = LssNoteDB.convertAnnotatableEntityUrnToGeneralUrn(noteKey.getEntity());
    Urn noteUrn = Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, noteKey.getSeat(), entityUrn, noteKey.getNoteId());
    Task<Boolean> deleteSharingPoliciesTask = deleteSharingPolicies(noteUrn, seatUrn, contractUrn);
    return Task.par(deleteNoteTask, deleteSharingPoliciesTask).map((deleteNoteResult, deleteSharingPoliciesResult)
        -> deleteNoteResult);
  }

  /**
   * find the Notes based on the seat and entity urn
   * @param seatUrn seat of the user to find the notes for
   * @param urn Organization, member or salesShareSearchUrn organization
   * @param viewerContractUrn contract of viewer for permission check
   * @param start start
   * @param count count
   * @return BasicCollectionResult of Note
   */
  public Task<BasicCollectionResult<Note>> findBySeatAndEntity(@NonNull SeatUrn seatUrn,
      @NonNull Urn urn, @NonNull ContractUrn viewerContractUrn, int start, int count) {
    AnnotatableEntityUrn entityUrn;
    entityUrn = UrnUtils.convertUrnToAnnotatableEntityUrn(urn);
    if (entityUrn == null) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Entity type not supported by AnnotatableEntityUrn"));
    }

    return _lssNoteDB.findBySeatAndEntity(seatUrn, entityUrn, start, count).map(pairs -> {
      List<Note> notes = pairs.stream().map(pair -> {
        Note note = convertEspressoNoteToNote(pair);
        // Permission to check if the viewer and the note are related to the same contract
        if (note != null && !note.getContract().equals(viewerContractUrn)) {
          LOG.error("Viewer {} does not have access to the note {}", viewerContractUrn, pair.getSecond());
          return null;
        }
        return note;
      }).filter(Objects::nonNull).collect(Collectors.toList());
      return new BasicCollectionResult<>(notes, notes.size());
    }).recover(throwable -> {
      LOG.warn("Failed to get the notes under the seat {} and entity {}", seatUrn, entityUrn, throwable);
      return new BasicCollectionResult<>(Collections.emptyList(), 0);
    });
  }

  /**
   * find the notes based on the creator seat
   * @param seatUrn seat urn of the user to find the notes for
   * @param start start
   * @param count count
   * @return BasicCollectionResult of notes
   */
  public Task<BasicCollectionResult<Note>> findBySeat(@NonNull SeatUrn seatUrn, int start, int count) {
    return _lssNoteDB.findBySeat(seatUrn, start, count).map(pairs -> {
      List<Note> notes = pairs.stream().map(this::convertEspressoNoteToNote)
          .filter(Objects::nonNull).collect(Collectors.toList());
      return new BasicCollectionResult<>(notes, notes.size());
    }).recover(throwable -> {
      LOG.warn("Failed to get the notes created by the seat {} ", seatUrn, throwable);
      return new BasicCollectionResult<>(Collections.emptyList(), 0);
    });
  }

  /**
   * Find entity notes by seat in batches
   * @param seatUrn owner of entity notes
   */
  public Task<List<Note>> findAllSavedEntityNotesBySeat(@NonNull SeatUrn seatUrn, int limit) {
    return findAllSavedEntityNotesBySeatRecursive(seatUrn, new ArrayList<>(), limit);
  }

  private Task<List<Note>> findAllSavedEntityNotesBySeatRecursive(SeatUrn owner, List<Note> allResults, int limit) {
    int start = allResults.size();
    int noteBatchSize = 200;

    return findBySeat(owner, start, noteBatchSize).flatMap(salesNotesResponse -> {
      List<Note> salesNotesBatch = salesNotesResponse.getElements();
      allResults.addAll(salesNotesBatch);
      if (allResults.size() >= limit || salesNotesBatch.size() < noteBatchSize) {
        return Task.value(allResults);
      }
      if (salesNotesBatch.size() == noteBatchSize) {
        return findAllSavedEntityNotesBySeatRecursive(owner, allResults, limit);
      }
      return Task.value(allResults);
    });
  }

  /**
   * Update an existing {@link Note} with new value
   * @param note the updating Note, should replace the old value
   * @param viewerSeat requester of the change
   * @param viewerContract the contract that the viewerSeat belongs
   * @return a boolean whether the update is successful. Return false if acl service denies the access.
   */
  public Task<Boolean> updateNote(@NonNull Note note, @NonNull SeatUrn viewerSeat,
      @NonNull ContractUrn viewerContract) {
    if (!note.hasBody() || !note.getBody().hasText()) {
      // Nothing to update so return success
      return Task.value(TRUE);
    }

    boolean isNoteKeyComplete = note.hasNoteId() && note.hasEntity() && note.hasSeat();
    if (!isNoteKeyComplete) {
      return Task.failure(
          new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Not all required fields are present"));
    }

    if (!viewerSeat.getIdAsLong().equals(note.getSeat().getIdAsLong())) {
      return Task.failure(
          new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, "Access denied when requesting update Note"));
    }
    com.linkedin.sales.espresso.Note espressoNote = createEspressoNoteWithText(note.getBody().getText());

    SeatUrn seatUrn = note.getSeat();
    AnnotatableEntityUrn entityUrn = note.getEntity();
    espressoNote.lastModifiedTime =  System.currentTimeMillis();

    return _lssNoteDB.updateNote(seatUrn, entityUrn, note.getNoteId(), espressoNote).onFailure(t ->
        LOG.warn("Failed to update note", t));
  }

  public Task<Note> getNote(@NonNull NoteKey noteKey) {
    return _lssNoteDB.getNote(noteKey)
        .map(espressoNote -> convertEspressoNoteToNote(Pair.of(noteKey, espressoNote)))
        .recoverWith(t -> {
          LOG.info(String.format("fail to get note for noteKey %s", noteKey), t);
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, t.getMessage()));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Batch get notes on the given note keys
   * @param noteKeys
   * @return a list contains mappings from NoteKey to Note
   */
  public Task<List<Pair<NoteKey, Note>>> batchGet(List<NoteKey> noteKeys) {
    return _lssNoteDB.batchGetNotes(noteKeys).map(pairs -> pairs.stream()
        .map(pair -> Pair.of(pair.getFirst(), convertEspressoNoteToNote(pair)))
        .filter(Objects::nonNull).collect(Collectors.toList())
    ).recover(throwable -> {
      LOG.warn("Fail to batch get notes for notekeys {}", noteKeys, throwable);
      return Collections.emptyList();
    });
  }

  /**
   * Delete sharing policies where the note is the resource. Currently 2 sharing policies are created when a note is
   * shared publicly. One with seat as the subject and role as OWNER, one with contract as the subject and role as
   * READER
   * @param resourceUrn the note resource whose sharing policies has to be delted
   * @param seatUrn seatUrn of the owner of the note
   * @param contractUrn contractUrn of the the seat
   * @return true if no error deleting both policies
   */
  private Task<Boolean> deleteSharingPolicies(@NonNull Urn resourceUrn, @NonNull SeatUrn seatUrn,
      @NonNull ContractUrn contractUrn) {

        PolicyKey seatAsSubjectPolicyKey =
            new PolicyKey().setSubject(seatUrn).setPolicyType(PolicyType.NOTE).setResource(resourceUrn);
        PolicyKey contractAsSubjectPolicyKey =
        new PolicyKey().setSubject(contractUrn).setPolicyType(PolicyType.NOTE).setResource(resourceUrn);
    Task<Boolean> deleteSeatPolicy = _lssSharingDB.deleteSubjectPolicy(seatAsSubjectPolicyKey);
    Task<Boolean> deleteContractPolicy = _lssSharingDB.deleteSubjectPolicy(contractAsSubjectPolicyKey);
    return Task.par(deleteSeatPolicy, deleteContractPolicy).map((isDeleteSeatPolicySuccessful, isDeleteContractPolicySuccessful) ->
        isDeleteContractPolicySuccessful && isDeleteSeatPolicySuccessful);
  }

  private static com.linkedin.sales.espresso.Note createEspressoNoteWithText(String text) {
    com.linkedin.sales.espresso.Note espressoNote = new com.linkedin.sales.espresso.Note();
    AttributedText attributedText = new AttributedText();
    attributedText.text = text;
    // For now we do not have attributes. Will implement after we support more rich format comments later on.
    attributedText.attributes = Collections.emptyList();
    espressoNote.body = attributedText;
    return espressoNote;
  }

  private static com.linkedin.common.AttributedText getCommonAttributedTextFromEspressoAttributedText(
        AttributedText espressoAttributedText) {
    com.linkedin.common.AttributedText attributedText = new com.linkedin.common.AttributedText();
    attributedText.setText(espressoAttributedText.text.toString());
    // TODO: For v1 there is no attributes available so we just set it as empty.
    // TODO : Will change that once we support at-mentions or hyperlink
    attributedText.setAttributes(new AttributeArray());
    return attributedText;
  }

  private static boolean isNoteComplete(Note note) {
    return note.hasBody() && note.hasContract() && note.hasEntity() && note.hasSeat();
  }

  private static Long calculateRetentionDaysFromEntity(AnnotatableEntityUrn entityUrn) {
    //Share Search notification will be deleted after 90 days. Add one day for buffer
    if (entityUrn.isSalesSharedSearchUrn()) {
      return RETENTION_TIME_SHARE_SEARCH;
    } else {
      return null;
    }
  }

  private Note convertEspressoNoteToNote(Pair<NoteKey, com.linkedin.sales.espresso.Note> pair) {
    NoteKey noteKey = pair.getFirst();
    com.linkedin.sales.espresso.Note espressoNote = pair.getSecond();
    ContractUrn espressoContractUrn;
    try {
      espressoContractUrn = ContractUrn.deserialize(espressoNote.contractUrn.toString());
    } catch (URISyntaxException e) {
      LOG.error("fail to create the contractUrn for {}", espressoNote, e);
      return null;
    }
    SeatUrn seatUrn = noteKey.getSeat();
    return new Note().setBody(getCommonAttributedTextFromEspressoAttributedText(espressoNote.body))
        .setContract(espressoContractUrn)
        .setEntity(noteKey.getEntity())
        .setNoteId(noteKey.getNoteId())
        .setSeat(seatUrn)
        .setExpireAt(espressoNote.expireTime, SetMode.IGNORE_NULL)
        .setCreated(new AuditStamp().setTime(espressoNote.createdTime).setActor(seatUrn))
        .setLastModified(new AuditStamp().setTime(espressoNote.lastModifiedTime).setActor(seatUrn));
  }

  private com.linkedin.sales.espresso.Note convertNoteToEspressoNoteForCreation(Note note) {
    com.linkedin.sales.espresso.Note espressoNote = createEspressoNoteWithText(note.getBody().getText());
    // enable createdTime and lastModifiedTime from client for seat transfer
    if (note.hasCreated() && note.getCreated().hasTime()) {
      espressoNote.createdTime = note.getCreated().getTime();
    } else {
      espressoNote.createdTime = System.currentTimeMillis();
    }
    if (note.hasLastModified() && note.getLastModified().hasTime()) {
      espressoNote.lastModifiedTime = note.getLastModified().getTime();
    } else {
      espressoNote.lastModifiedTime = espressoNote.createdTime;
    }
    espressoNote.contractUrn = note.getContract().toString();
    return espressoNote;
  }

}
