package com.linkedin.sales.service.seattransfer;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferKey;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferRequestsClient;
import com.linkedin.sales.service.seattransfer.salescustomlists.SalesCustomListsTransferService;
import com.linkedin.sales.service.seattransfer.salesentitynotes.SalesEntityNotesTransferService;
import com.linkedin.sales.service.seattransfer.salesleadaccountassociations.SalesLeadAccountAssociationsTransferService;
import com.linkedin.sales.service.seattransfer.salesaccounts.SalesAccountsTransferService;
import com.linkedin.sales.service.seattransfer.salesleads.SalesLeadsTransferService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Service to handle seat transfers for lss pages entities.
 */
public class LssSeatTransferActionsService {
  private static final Logger LOG = LoggerFactory.getLogger(LssSeatTransferActionsService.class);
  private final SalesSeatTransferRequestsClient _salesSeatTransferRequestsClient;
  private final SalesLeadsTransferService _salesLeadsTransferService;
  private final SalesCustomListsTransferService _salesCustomListsTransferService;
  private final SalesEntityNotesTransferService _salesEntityNotesTransferService;
  private final SalesLeadAccountAssociationsTransferService _salesLeadsAccountAssociationsTransferService;
  private final SalesAccountsTransferService _salesAccountsTransferService;
  private static final String DEFAULT_BATCH_SIZE_AND_DELAY = "batch_12_delay_750";


  public LssSeatTransferActionsService(
      SalesSeatTransferRequestsClient salesSeatTransferRequestsClient,
      SalesLeadsTransferService salesLeadsTransferService,
      SalesEntityNotesTransferService salesEntityNotesTransferService,
      SalesLeadAccountAssociationsTransferService salesLeadAccountAssociationsTransferService,
      SalesAccountsTransferService salesAccountsTransferService,
      SalesCustomListsTransferService salesCustomListsTransferService
      ) {
    _salesSeatTransferRequestsClient = salesSeatTransferRequestsClient;
    _salesLeadsTransferService = salesLeadsTransferService;
    _salesEntityNotesTransferService = salesEntityNotesTransferService;
    _salesLeadsAccountAssociationsTransferService = salesLeadAccountAssociationsTransferService;
    _salesAccountsTransferService = salesAccountsTransferService;
    _salesCustomListsTransferService = salesCustomListsTransferService;
  }

  /**
   * Parse seat transfer batch size and delay treatment string
   * @param lixTreatment lix treatment in the format batch_X_delay_Y where X and Y are integers
   * @return
   */
  public static int[] parseBatchSizeAndDelayFromLixTreatment(String lixTreatment) {
    String batchSizeAndDelay = lixTreatment.equals(LixUtils.CONTROL) ? DEFAULT_BATCH_SIZE_AND_DELAY : lixTreatment;
    String[] lixTreatmentValues = batchSizeAndDelay.split("_");
    return new int[] {Integer.parseInt(lixTreatmentValues[1]), Integer.parseInt(lixTreatmentValues[3])};
  }

  public Task<Void> executeSeatTransferRequest(
      SalesSeatTransferRequestUrn seatTransferRequestUrn, OwnershipTransferEntityType entityType, SeatUrn actor
  ) {
    return _salesSeatTransferRequestsClient.get(buildOwnershipTransferKey(seatTransferRequestUrn))
        .flatMap(maybeOwnershipTransferRequest -> maybeOwnershipTransferRequest.map(ownershipTransferRequest -> {
          SalesEntityTransferService salesEntityTransferService = getSalesEntityTransferService(entityType);
          switch (ownershipTransferRequest.getOwnershipTransferType()) {
            case TRANSFER:
              return salesEntityTransferService.transfer(ownershipTransferRequest, actor);
            case REVERT_TRANSFER:
              return salesEntityTransferService.revertTransfer(ownershipTransferRequest, actor);
            case REASSIGNMENT:
              return salesEntityTransferService.reassign(ownershipTransferRequest, actor);
            case REVERT_REASSIGNMENT:
              return salesEntityTransferService.revertReassign(ownershipTransferRequest, actor);
            default:
              throw new UnsupportedOperationException(
                  String.format("Unsupported transfer type: %s", ownershipTransferRequest.getOwnershipTransferType()));
          }
        }).orElseGet(() -> Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, "Ownership transfer request not found"))));
  }
  private OwnershipTransferKey buildOwnershipTransferKey(SalesSeatTransferRequestUrn seatTransferUrn) {
    return new OwnershipTransferKey()
        .setContractUrn(seatTransferUrn.getSourceContractEntity())
        .setId(seatTransferUrn.getRequestIdEntity());
  }

  private SalesEntityTransferService getSalesEntityTransferService(OwnershipTransferEntityType entityType) {
    switch (entityType) {
      case SALES_LEADS:
        return _salesLeadsTransferService;
      case SALES_CUSTOM_LISTS:
        return _salesCustomListsTransferService;
      case SALES_ENTITY_NOTES:
        return _salesEntityNotesTransferService;
      case SALES_LEAD_ACCOUNT_ASSOCIATIONS:
        return _salesLeadsAccountAssociationsTransferService;
      case SALES_ACCOUNTS:
        return _salesAccountsTransferService;
      default:
        throw new UnsupportedOperationException(String.format("Unsupported entity type: %s", entityType));
    }
  }
}
