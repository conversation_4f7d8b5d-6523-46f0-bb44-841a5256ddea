package com.linkedin.sales.service.seattransfer;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;


/**
 * Service class interface to perform seat transfers for search entities. All implementations should ensure idempotency for each method.
 *
 */
public interface SalesEntityTransferService {

  /**
   * Transfer record(s) corresponding to an entity type from a source seat to a target seat in another contract.
   *
   * @param ownershipTransferRequest the request object containing all info required to perform a seat transfer.
   * @param actor the entity which initiated this request.
   * @return a void task.
   */
  default Task<Void> transfer(
      OwnershipTransferRequest ownershipTransferRequest, SeatUrn actor) {
    return Task.value(null);
  }

  /**
   * Revert the actions performed by a previous transfer.
   *
   * @param ownershipTransferRequest the request object containing all info required to perform a seat transfer.
   * @param actor the entity which initiated this request.
   * @return a void task.
   */
  default Task<Void> revertTransfer(
      OwnershipTransferRequest ownershipTransferRequest, SeatUrn actor) {
    return Task.value(null);
  }

  /**
   * Transfer ownership of all record(s) corresponding to an entity type from a source seat to a target seat within the
   * same contract.
   *
   * @param ownershipTransferRequest the request object containing all info required to perform a seat transfer.
   * @param actor the entity which initiated this request.
   * @return a void task.
   */
  default Task<Void> reassign(
      OwnershipTransferRequest ownershipTransferRequest, SeatUrn actor) {
    return Task.value(null);
  }

  /**
   * Revert the actions performed by a previous reassign.
   *
   * @param ownershipTransferRequest the request object containing all info required to perform a seat transfer.
   * @param actor the entity which initiated this request.
   * @return a void task.
   */
  default Task<Void> revertReassign(
      OwnershipTransferRequest ownershipTransferRequest, SeatUrn actor) {
    return Task.value(null);
  }
}
