package com.linkedin.sales.service.seattransfer.salesaccounts;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.lss.salesleadaccount.SalesAccountActionResult;
import com.linkedin.lss.salesleadaccount.SalesAccountActionResultArray;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.seattransfer.SalesEntityTransferService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesSavedAccountUrn;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import com.linkedin.salesleadaccount.SalesAccount;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.ownershiptransfer.OwnershipTransferEntityType.*;
import static com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService.*;


public class SalesAccountsTransferService implements SalesEntityTransferService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesAccountsTransferService.class);
  private final SalesLeadAccountCommonService _salesLeadAccountCommonService;
  private final SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  private final int _maxSavedAccountLimitAllTiers;
  private final LixService _lixService;

  private static final OwnershipTransferEntityType ENTITY_TYPE = SALES_ACCOUNTS;

  public SalesAccountsTransferService(SalesLeadAccountCommonService salesLeadAccountCommonService,
      SalesSeatTransferCopyAssociationsClient salesSeatTransferCopyAssociationsClient, int maxSavedAccountLimitAllTiers,
      LixService lixService) {
    _salesLeadAccountCommonService = salesLeadAccountCommonService;
    _copyAssociationsClient = salesSeatTransferCopyAssociationsClient;
    _maxSavedAccountLimitAllTiers = maxSavedAccountLimitAllTiers;
    _lixService = lixService;
  }

  SalesAccount convertToDestSalesAccount(SalesAccount account, ContractUrn targetContract, SeatUrn targetSeat) {
    try {
      return account.clone().setContract(targetContract).setOwner(targetSeat);
    } catch (CloneNotSupportedException e) {
      // Can't happen since clone is implemented in RecordTemplate class
      throw new RuntimeException(e);
    }
  }

  @Override
  public Task<Void> transfer(@NonNull OwnershipTransferRequest ownershipTransferRequest, @NonNull SeatUrn actor) {
    // Retrieve sourceSeat and targetSeat
    SeatUrn sourceSeat = ownershipTransferRequest.getSourceSeat();
    SeatUrn targetSeat = ownershipTransferRequest.getTargetSeat();
    ContractUrn targetContract = ownershipTransferRequest.getTargetContract();

    LOG.info("Beginning {} transfer for sourceSeat {} and targetSeat {} for request {}",
        ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

    // Check saved account limit for targetSeat
    Task<Integer> totalTargetSeatSavedAccountsTask = _salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(targetSeat);
    return totalTargetSeatSavedAccountsTask.flatMap(totalTargetSeatSavedAccounts -> {
      if (totalTargetSeatSavedAccounts >= _maxSavedAccountLimitAllTiers) {
        LOG.info("{} transfer to targetSeat {} exceeds limit, exiting transfer with request id {}.",
            ENTITY_TYPE, targetSeat, ownershipTransferRequest.getId());
        return Task.value(null);
      }

      // Retrieve the sourceSeat's SalesAccount list
      Task<List<SalesAccount>> sourceSeatSavedAccountsListTask =
          _salesLeadAccountCommonService.findAllSavedAccountsForSeatUpToLimit(sourceSeat, _maxSavedAccountLimitAllTiers,
              null, null, SortOrder.DESCENDING);

      return sourceSeatSavedAccountsListTask.flatMap(sourceSeatSavedAccountsList -> {
        if (sourceSeatSavedAccountsList == null || sourceSeatSavedAccountsList.isEmpty()) {
          LOG.info("No {} for sourceSeat {} found, exiting transfer with request id {}",
              ENTITY_TYPE, sourceSeat, ownershipTransferRequest.getId());
          return Task.value(null);
        }

        List<Urn> salesSavedAccountUrns = sourceSeatSavedAccountsList.stream()
            .map(savedAccount -> Urn.createFromTuple(SalesSavedAccountUrn.ENTITY_TYPE, sourceSeat,
                savedAccount.getOrganization().getIdAsLong()))
            .collect(Collectors.toList());

        LOG.info("Entering into for loop for {} transfer from sourceSeat {} to targetSeat {} for request {}",
            ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
        Task<List<OwnershipTransferCopyAssociation>> ownershipTransferCopyAssociationsListTask =
            _copyAssociationsClient.findPreviousTransfers(salesSavedAccountUrns, targetContract);

        return ownershipTransferCopyAssociationsListTask.flatMap(ownershipTransferCopyAssociations -> {
          LOG.info("Finished getting previous {} copyAssociation records for transfer from sourceSeat {} to targetSeat {} for request {}",
              ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
          // Convert copy association records to set of ids so that already transferred organizationIds are not transferred again
          Set<Long> previouslyTransferredSavedAccounts =
              ownershipTransferCopyAssociations.stream().map(copyAssociation -> {
                try {
                  SalesSavedAccountUrn salesSavedAccountUrn =
                      SalesSavedAccountUrn.createFromUrn(copyAssociation.getSourceEntity());
                  return salesSavedAccountUrn.getOrganizationIdEntity();
                } catch (URISyntaxException e) {
                  throw new RuntimeException(e);
                }
              }).collect(Collectors.toSet());

          // Remove already transferred saved leads from list of sourceSeat's saved leads
          List<SalesAccount> notTransferredSavedAccounts = sourceSeatSavedAccountsList.stream()
              .filter(salesAccount -> !previouslyTransferredSavedAccounts.contains(
                  salesAccount.getOrganization().getIdAsLong()))
              .collect(Collectors.toList());

          if (notTransferredSavedAccounts.isEmpty()) {
            LOG.info(
                "All {} transferred already from sourceSeat {} to targetSeat {} exiting transfer with request id {}",
                ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
            return Task.value(null);
          }

          // Get list of saved accounts to be transferred with respect to max saved accounts limit
          List<SalesAccount> savedAccountsToBeTransferred =
              getSavedAccountsToTransfer(targetSeat, notTransferredSavedAccounts, totalTargetSeatSavedAccounts,
                  _maxSavedAccountLimitAllTiers);

          // Update seat and contract of SalesAccounts to be transferred
          List<SalesAccount> newTargetSeatSavedAccounts = savedAccountsToBeTransferred.stream()
              .map(salesAccount -> convertToDestSalesAccount(salesAccount, targetContract, targetSeat))
              .collect(Collectors.toList());

          return _lixService.getLixTreatment(targetContract, LixUtils.LSS_PAGES_SEAT_TRANSFER_BATCH_SIZE_AND_DELAY, null).flatMap(treatment -> {
            int[] batchSizeAndDelay = parseBatchSizeAndDelayFromLixTreatment(treatment);
            int batchSize = batchSizeAndDelay[0];
            int delay = batchSizeAndDelay[1];
            // Create transferred saved accounts for targetSeat
            SalesAccountActionResultArray salesAccountActionResultsArray =
                _salesLeadAccountCommonService.batchCreateSavedAccounts(newTargetSeatSavedAccounts,
                    batchSize, delay);

            // Get the organizationIds of all saved accounts successfully created for targetSeat
            List<Long> successfullyTransferredOrganizationIds = salesAccountActionResultsArray.stream()
                .filter(result -> result.getStatus() == ActionStatus.SUCCESS)
                .map(SalesAccountActionResult::getOrganizationId)
                .collect(Collectors.toList());

            LOG.info("{} {} were successfully created in the targetSeat {} for request id {}",
                successfullyTransferredOrganizationIds.size(), ENTITY_TYPE, targetSeat, ownershipTransferRequest.getId());

            // Since accounts that the sourceSeat and targetSeat already have in common will produce an ActionStatus.CONFLICT,
            // Check if any attempts to create saved accounts in targetSeat were a result of actual failure
            List<SalesAccountActionResult> failedTransfers = salesAccountActionResultsArray.stream()
                .filter(
                    result -> result.getStatus() != ActionStatus.SUCCESS && result.getStatus() != ActionStatus.CONFLICT)
                .collect(Collectors.toList());

            // Create copyAssociationsClient table entries for all successfully transferred saved accounts
            Task<List<Long>> newCopyAssociationsListTask =
                updateCopyAssociationsClientTable(sourceSeat, targetSeat, targetContract,
                    successfullyTransferredOrganizationIds, actor, ownershipTransferRequest);

            LOG.info("Creating {} copyAssociations for sourceSeat {} to targetSeat {} for request {}", ENTITY_TYPE,
                sourceSeat, targetSeat, ownershipTransferRequest.getId());
            return newCopyAssociationsListTask.flatMap(newCopyAssociationsList -> {
              LOG.info("Finished creating {} {} copyAssociations for sourceSeat {} to targetSeat {} for request {}",
                  newCopyAssociationsList.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

              if (failedTransfers.isEmpty()) {
                LOG.info("Completed {} transfer for sourceSeat {} and targetSeat {} for request {}", ENTITY_TYPE,
                    sourceSeat, targetSeat, ownershipTransferRequest.getId());
                return Task.value(null);
              } else {
                LOG.warn(
                    "Unable to create {} {} out of {} total saved accounts in targetSeat {}, for request {}, for accounts: {}\nBecause of: {}",
                    failedTransfers.size(), ENTITY_TYPE, salesAccountActionResultsArray.size(), targetSeat,
                    ownershipTransferRequest.getId(), new ArrayList<>(failedTransfers.stream()
                        .map(SalesAccountActionResult::getOrganizationId)
                        .collect(Collectors.toList())), new ArrayList<>(
                        failedTransfers.stream().map(SalesAccountActionResult::getStatus).collect(Collectors.toList())));
                String errorMessage = String.format(
                    "%s were partially transferred, expected %d accounts to be transferred"
                        + " but %d accounts failed to be transferred for request %d", ENTITY_TYPE,
                    newTargetSeatSavedAccounts.size(), failedTransfers.size(), ownershipTransferRequest.getId());
                LOG.error(errorMessage);
                throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMessage);
              }
            });
          });
        });
      });
    });
  }

  private List<SalesAccount> getSavedAccountsToTransfer(@NonNull SeatUrn targetSeat, @NonNull List<SalesAccount> notTransferredSavedAccounts,
      int targetSeatSavedAccountsTotal, int maxSavedAccountLimit) {
    // If newly transferred saved accounts would not cause the targetSeat to exceed the saved account limit, continue as normal
    if (notTransferredSavedAccounts.size() + targetSeatSavedAccountsTotal <= maxSavedAccountLimit) {
      return notTransferredSavedAccounts;
    }

    // Only transfer the most recently saved leads up until max saved lead limit
    LOG.info("targetSeat {} has {} saved accounts but account limit is {} so only transferring {} accounts out of a total of {}",
        targetSeat,
        targetSeatSavedAccountsTotal,
        maxSavedAccountLimit,
        maxSavedAccountLimit - targetSeatSavedAccountsTotal,
        notTransferredSavedAccounts.size());
    return notTransferredSavedAccounts.stream()
        .limit((long) maxSavedAccountLimit - targetSeatSavedAccountsTotal)
        .collect(Collectors.toList());
  }

  private Task<List<Long>> updateCopyAssociationsClientTable(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull ContractUrn targetContract, @NonNull List<Long> successfullyTransferredOrganizationIds, @NonNull SeatUrn actor,
      @NonNull OwnershipTransferRequest ownershipTransferRequest) {
    // Storing SeatUrn and SalesLead memberId for undo, reassignment and undo reassignment
    AuditStamp auditStamp = new AuditStamp().setActor(actor);
    SalesSeatTransferRequestUrn transferRequestUrn = getSalesSeatTransferRequestUrn(ownershipTransferRequest.getSourceContract(),
        ownershipTransferRequest.getId());
    List<OwnershipTransferCopyAssociation> newSuccessfulTransferRecords = successfullyTransferredOrganizationIds.stream()
        .map(organizationId -> new OwnershipTransferCopyAssociation()
            .setSourceSeat(sourceSeat)
            .setTargetSeat(targetSeat)
            .setCreated(auditStamp)
            .setOwnershipTransferEntityType(ENTITY_TYPE)
            .setOwnershipTransferRequest(transferRequestUrn)
            .setTargetContract(targetContract)
            .setSourceEntity(Urn.createFromTuple(SalesSavedAccountUrn.ENTITY_TYPE, sourceSeat, organizationId))
            .setTargetEntity(Urn.createFromTuple(SalesSavedAccountUrn.ENTITY_TYPE, targetSeat, organizationId)))
        .collect(Collectors.toList());
    return _copyAssociationsClient.createCopyAssociations(newSuccessfulTransferRecords);
  }

  /**
   * Function to compose SalesSeatTransferRequestUrn.
   * @param contractUrn contract urn
   * @param seatTransferRequestId id of seat transfer request
   * @return SavedSearchEntityKey
   */
  static SalesSeatTransferRequestUrn getSalesSeatTransferRequestUrn(ContractUrn contractUrn, Long seatTransferRequestId) {
    try {
      Urn seatTransferRequestUrn = SalesSeatTransferRequestUrn.createFromTuple(SalesSeatTransferRequestUrn.ENTITY_TYPE,
          ImmutableList.of(contractUrn, seatTransferRequestId));
      return SalesSeatTransferRequestUrn.createFromUrn(seatTransferRequestUrn);
    } catch (URISyntaxException ex) {
      throw new RuntimeException(ex);
    }
  }
}
