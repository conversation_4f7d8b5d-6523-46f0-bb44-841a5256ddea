package com.linkedin.sales.service.seattransfer.salescustomlists;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.list.SalesListEntityService;
import com.linkedin.sales.service.list.SalesListService;
import com.linkedin.sales.service.seattransfer.SalesEntityTransferService;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.urn.SalesListV2Urn;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import com.linkedin.saleslist.ListEntity;
import com.linkedin.saleslist.ListEntityPriorityInfo;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListOwnership;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import com.linkedin.saleslist.List;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.ownershiptransfer.OwnershipTransferEntityType.*;

public class SalesCustomListsTransferService implements SalesEntityTransferService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesCustomListsTransferService.class);
  private static final Set<ListSource> CUSTOM_LIST_SOURCES_TO_COPY =
      new HashSet<>(Lists.newArrayList(ListSource.MANUAL, ListSource.CSV_IMPORT, ListSource.LINKEDIN_SALES_INSIGHTS));
  private static final OwnershipTransferEntityType ENTITY_TYPE = SALES_CUSTOM_LISTS;
  private final SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  private final SalesListService _salesListService;
  private final SalesListEntityService _salesListEntityService;
  public SalesCustomListsTransferService(SalesSeatTransferCopyAssociationsClient salesSeatTransferCopyAssociationsClient,
  SalesListService salesListService, SalesListEntityService salesListEntityService) {
    _copyAssociationsClient = salesSeatTransferCopyAssociationsClient;
    _salesListEntityService = salesListEntityService;
    _salesListService = salesListService;
  }

  @Override
  public Task<Void> transfer(@NonNull OwnershipTransferRequest ownershipTransferRequest, @NonNull SeatUrn actor) {
    // Retrieve sourceSeat and targetSeat
    SeatUrn sourceSeat = ownershipTransferRequest.getSourceSeat();
    SeatUrn targetSeat = ownershipTransferRequest.getTargetSeat();
    ContractUrn targetContract = ownershipTransferRequest.getTargetContract();

    LOG.info("Beginning {} transfer for sourceSeat {} and targetSeat {} for request {}",
        ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

    // Get custom lists for sourceSeat
    Task<java.util.List<List>> sourceSeatCustomListsTask = getOwnedCustomListsForSeat(sourceSeat);
    return sourceSeatCustomListsTask.flatMap(sourceSeatCustomLists -> {
      if (sourceSeatCustomLists.isEmpty()) {
        LOG.info("No {} found for sourceSeat {} for transfer with request {}, exiting transfer", ENTITY_TYPE,
            sourceSeat, ownershipTransferRequest.getId());
        return Task.value(null);
      }
      java.util.List<Urn> salesSavedCustomListsUrns = sourceSeatCustomLists.stream()
          .map(customList -> Urn.createFromTuple(SalesListV2Urn.ENTITY_TYPE, sourceSeat, customList.getId()))
          .collect(Collectors.toList());

      LOG.info("Retrieved {} custom lists for sourceSeat {} to transfer to targetSeat {} for request {}", salesSavedCustomListsUrns.size(),
          sourceSeat, targetSeat, ownershipTransferRequest.getId());

      // Get already transferred lists from the copyAssociationsClient table to ensure idempotency
      Task<java.util.List<OwnershipTransferCopyAssociation>> ownershipTransferCopyAssociationsTask =
          _copyAssociationsClient.findPreviousTransfers(salesSavedCustomListsUrns, targetContract);
      return ownershipTransferCopyAssociationsTask.flatMap(ownershipTransferCopyAssociations -> {
        LOG.info("Retrieved {} {} copy associations for sourceSeat {} to transfer to targetSeat {} for request {}",
            ownershipTransferCopyAssociations.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
        Set<Long> alreadyTransferredListIds = ownershipTransferCopyAssociations.stream().map(copyAssociation -> {
          try {
            return SalesListV2Urn.createFromUrn(copyAssociation.getSourceEntity()).getListIdEntity();
          } catch (URISyntaxException e) {
            throw new RuntimeException(e);
          }
        }).collect(Collectors.toSet());
        LOG.info("sourceSeat {} has {} {} ids that can be transferred and {} {} that already have transfer copy association records for request {}",
            sourceSeat, salesSavedCustomListsUrns.size(), ENTITY_TYPE, ownershipTransferCopyAssociations.size(), ENTITY_TYPE,
            ownershipTransferRequest.getId());

        // Remove custom lists already transferred to target seat
        java.util.List<List> notTransferredSourceSeatLists = sourceSeatCustomLists.stream()
            .filter(sourceSeatCustomList -> !alreadyTransferredListIds.contains(sourceSeatCustomList.getId()))
            .collect(Collectors.toList());

        LOG.info("{} {} were not transferred from sourceSeat {} to targetSeat {} already for request {}",
            notTransferredSourceSeatLists.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

        if (alreadyTransferredListIds.size() >= salesSavedCustomListsUrns.size()) {
          LOG.info("All {} transferred already from sourceSeat {} to targetSeat {} for request {}", ENTITY_TYPE,
              sourceSeat, targetSeat, ownershipTransferRequest.getId());
          return Task.value(null);
        }

        // Create custom lists in targetSeat and keep mapping of new custom lists' ids to old source seat lists' ids
        Task<BiMap<Long, Long>> sourceSeatToTargetSeatCustomListIdsMapTask =
            transferCustomLists(notTransferredSourceSeatLists, sourceSeat, targetSeat, targetContract);
        return sourceSeatToTargetSeatCustomListIdsMapTask.flatMap(sourceSeatToTargetSeatCustomListIdsMap -> {
          LOG.info("Created {} {} sourceSeat {} to targetSeat {} list id mappings for request {}",
              sourceSeatToTargetSeatCustomListIdsMap.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
          Task<java.util.List<Long>> newCopyAssociationsListTask =
              updateCopyAssociationsClientTable(sourceSeat, targetSeat, targetContract,
                  sourceSeatToTargetSeatCustomListIdsMap, actor, ownershipTransferRequest);
          return newCopyAssociationsListTask.flatMap(newCopyAssociationsList -> {
            LOG.info("Successfully created {} {} copyAssociations for sourceSeat {} to targetSeat {} for request {}",
                newCopyAssociationsList.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
            if (notTransferredSourceSeatLists.size() != sourceSeatToTargetSeatCustomListIdsMap.size()) {
              String errorMessage = String.format("%s were partially transferred, expected %d lists to be transferred"
                      + " with %d mappings, but %d lists were transferred for request %d", ENTITY_TYPE,
                  notTransferredSourceSeatLists.size(), sourceSeatToTargetSeatCustomListIdsMap.size(),
                  newCopyAssociationsList.size(), ownershipTransferRequest.getId());
              LOG.error(errorMessage);
              throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMessage);
            }
            return Task.value(null);
          });
        });
      });
    });
  }

  private Task<Pair<Integer, java.util.List<List>>> findAllSavedListsBySeatAndListType(@NonNull SeatUrn seatUrn,
      @NonNull com.linkedin.saleslist.ListType listType) {
    return findAllSavedListsBySeatAndListTypeRecursive(seatUrn, listType, ListOrdering.NAME, SortOrder.DESCENDING, ListOwnership.OWNED_BY_VIEWER,
        SalesCustomListsTransferService.CUSTOM_LIST_SOURCES_TO_COPY,
        new Pair<>(0, new ArrayList<>()), 10000);
  }

  private Task<Pair<Integer, java.util.List<List>>> findAllSavedListsBySeatAndListTypeRecursive(@NonNull SeatUrn owner,
      @NonNull com.linkedin.saleslist.ListType listType, @NonNull ListOrdering sortCriteria,
      @NonNull SortOrder sortOrder, @Nullable ListOwnership ownership, @NonNull Set<ListSource> listSources,
      Pair<Integer, java.util.List<List>> allResults, int limit) {
    int start = allResults.getSecond().size();
    int customListBatchSize = 50;

    return _salesListService.getListsForSeat(owner.getSeatIdEntity(), listType, sortCriteria, sortOrder, ownership, listSources, null,
        start, customListBatchSize).flatMap(customListsBatchPair -> {
      // Retrieve new total count of custom lists retrieved
      java.util.List<List> customListsBatch = customListsBatchPair.getSecond();
      Integer newTotal = customListsBatchPair.getFirst() + customListsBatch.size();

      // Add new custom lists retrieved to previously stored custom lists
      java.util.List<List> newCustomListsList = allResults.getSecond();
      newCustomListsList.addAll(customListsBatch);

      // Create new Pair<Integer, List<Lists>> to serve as allResults in the next recursive call
      Pair<Integer, java.util.List<List>> newAllResults = new Pair<>(newTotal, newCustomListsList);
      if (newCustomListsList.size() >= limit || customListsBatch.size() < customListBatchSize) {
        return Task.value(newAllResults);
      }
      if (customListsBatch.size() == customListBatchSize) {
        return findAllSavedListsBySeatAndListTypeRecursive(owner, listType, sortCriteria, sortOrder, ownership, listSources,
            newAllResults, limit);
      }
      return Task.value(newAllResults);
    });
  }

  private Task<java.util.List<List>> getOwnedCustomListsForSeat(@NonNull SeatUrn seat) {
    // Fetch all account and lead lists from sourceSeat
    // Less than 0.0001% of sales navigator seats have more than 10000 custom lists.
    // Average custom lists transferred in a month per seat transfer is ~11
    // https://docs.google.com/document/d/1lvXKPEUEBUqEHxaGJZFrpfsg8IUYqlpZA3tligMiw-s/edit?tab=t.0#heading=h.3y1fgmozcfdw
    Task<Pair<Integer, java.util.List<List>>> getAccountListsTask = findAllSavedListsBySeatAndListType(seat,
        ListType.ACCOUNT);

    Task<Pair<Integer, java.util.List<List>>> getLeadsListsTask = findAllSavedListsBySeatAndListType(seat,
        ListType.LEAD);

    return Task.par(getAccountListsTask, getLeadsListsTask).flatMap((accountListsPair, leadsListPair) -> {
      java.util.List<List> allSourceSeatLists = Lists.newArrayListWithCapacity(
          leadsListPair.getFirst() + accountListsPair.getFirst());
      allSourceSeatLists.addAll(accountListsPair.getSecond());
      allSourceSeatLists.addAll(leadsListPair.getSecond());
      return Task.value(allSourceSeatLists);
    });
  }

  private Task<java.util.List<Long>> updateCopyAssociationsClientTable(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull ContractUrn targetContract, BiMap<Long, Long> successfullyTransferredListIds, @NonNull SeatUrn actor,
      @NonNull OwnershipTransferRequest ownershipTransferRequest) {
    AuditStamp auditStamp = new AuditStamp().setActor(actor);
    SalesSeatTransferRequestUrn transferRequestUrn = getSalesSeatTransferRequestUrn(ownershipTransferRequest.getSourceContract(),
        ownershipTransferRequest.getId());
    java.util.List<OwnershipTransferCopyAssociation> newSuccessfulTransferRecords = successfullyTransferredListIds.entrySet().stream()
        .map(entrySet -> new OwnershipTransferCopyAssociation()
            .setSourceSeat(sourceSeat)
            .setTargetSeat(targetSeat)
            .setCreated(auditStamp)
            .setOwnershipTransferEntityType(ENTITY_TYPE)
            .setOwnershipTransferRequest(transferRequestUrn)
            .setTargetContract(targetContract)
            .setSourceEntity(Urn.createFromTuple(SalesListV2Urn.ENTITY_TYPE, sourceSeat, entrySet.getKey()))
            .setTargetEntity(Urn.createFromTuple(SalesListV2Urn.ENTITY_TYPE, targetSeat, entrySet.getValue())))
        .collect(Collectors.toList());
    LOG.info("Constructed {} {} copy associations to be sent to copy associations client for request {}", newSuccessfulTransferRecords.size(), ENTITY_TYPE,
        ownershipTransferRequest.getId());
    return _copyAssociationsClient.batchCreate(newSuccessfulTransferRecords);
  }

  private Task<BiMap<Long, Long>> transferCustomLists(@NonNull java.util.List<List> sourceCustomListsToTransfer, @NonNull SeatUrn sourceSeat,
      @NonNull SeatUrn targetSeat, @NonNull ContractUrn targetContract) {
    // Retrieve all the list entities for each of the sourceSeat's lists. This is done before creating the lists in the dest seat to ensure
    // that we proceed to create the lists in the dest seat iff we were able to retrieve the list entities of each
    // of the corresponding src lists.
    BiMap<Long, Long> sourceSeatToTargetSeatListMap = HashBiMap.create();
    try {
      Task<java.util.List<ListEntity>> sourceSeatListEntitiesTask = getListEntities(sourceCustomListsToTransfer, sourceSeat);
      return sourceSeatListEntitiesTask.flatMap(sourceSeatListEntities -> {
        // Create new targetSeat lists with same parameters as source list but with updated fields for creator and contract
        java.util.List<List> targetSeatLists = sourceCustomListsToTransfer.stream()
            .map(salesList -> convertSourceListToTargetList(salesList, targetSeat, targetContract)).collect(Collectors.toList());

        Task<BatchCreateResult<Long, List>> batchCreateListsTask =
            _salesListService.batchCreateLists(targetSeatLists, targetContract.getContractIdEntity());
        return batchCreateListsTask.flatMap(batchCreateResult -> {
          // Keep a map of sourceSeat list ids to the newly created targetSeat list ids for checkpointing
          // Create lists in target seat
          java.util.List<CreateResponse> createResponseList = batchCreateResult.getResults();

          // Map the newly created target list ids with the source list ids.
          for (int i = 0; i < createResponseList.size(); i++) {
            CreateResponse newListResponse = createResponseList.get(i);
            if (newListResponse.hasError()) {
              LOG.info("Unable to create targetSeat list for source list with id: {}, error: {}",
                  sourceCustomListsToTransfer.get(i).getId(), newListResponse.getError());
            } else {
              sourceSeatToTargetSeatListMap.put(sourceCustomListsToTransfer.get(i).getId(), (Long) newListResponse.getId());
            }
          }

          // Update list entities with targetSeat information for the newly created lists
          java.util.List<ListEntity> targetSeatListEntities =
              convertSourceSeatEntitiesToTargetSeatEntities(sourceSeat, targetSeat, sourceSeatToTargetSeatListMap, sourceSeatListEntities);

          // Creat targetSeat listEntities
          Task<Map<CompoundKey, CreateResponse>> convertSourceSeatEntitiesToTargetSeatEntitiesTask =
              createListEntitiesInTargetSeat(targetContract, targetSeatListEntities);
          return convertSourceSeatEntitiesToTargetSeatEntitiesTask.flatMap(targetSeatListEntitiesMap -> {
            //
            Set<Long> targetSeatListsToDeleteSet = new HashSet<>();
            BiMap<Long, Long> targetSeatToSourceSeatListIdsMap = sourceSeatToTargetSeatListMap.inverse();
            targetSeatListEntitiesMap.forEach((key, value) -> {
              if (value.getStatus() != HttpStatus.S_201_CREATED) {
                try {
                  SalesListUrn salesListUrn = SalesListUrn.deserialize(key.getPart(ServiceConstants.LIST_COMPOUND_KEY).toString());
                  targetSeatListsToDeleteSet.add(Long.valueOf(salesListUrn.getEntityKey().get(0)));
                } catch (URISyntaxException e) {
                  LOG.error("Unable to retrieve SalesListUrn from the compoundKey list part: {}", key.getPart(
                      ServiceConstants.LIST_COMPOUND_KEY), e);
                }
              }
            });
            // Create a list of which of the new targetSeat lists need to be deleted as at least one the list entities failed to be created
            java.util.List<Task<Boolean>> targetSeatListsToDeleteTaskList = targetSeatListsToDeleteSet.stream()
                .map(newlyCreatedTargetSeatListId -> {
                  targetSeatToSourceSeatListIdsMap.remove(newlyCreatedTargetSeatListId);
                  return _salesListService.deleteList(newlyCreatedTargetSeatListId, targetSeat, targetContract);
                })
                .collect(Collectors.toList());

            Task<Boolean> successfullyDeletedFailedCreatedListsTask = deleteSeatListsInDestSeat(sourceSeat, targetSeat, targetSeatListsToDeleteTaskList);
            return successfullyDeletedFailedCreatedListsTask.flatMap(successfullyDeletedFailedCreatedLists -> {
              if (Boolean.FALSE.equals(successfullyDeletedFailedCreatedLists)) {
                LOG.info("Unable to delete all custom lists in targetSeat {} that failed on creation", targetSeat);
              }
              return Task.value(targetSeatToSourceSeatListIdsMap.inverse());
            });
          });
        });
      });
    } catch (Exception pe) {
      LOG.error("Unable to create custom lists {} in the targetSeat {}",
          sourceCustomListsToTransfer.stream().map(List::getId).collect(Collectors.toList()), targetSeat, pe);
      return Task.value(sourceSeatToTargetSeatListMap);
    }
  }

  List convertSourceListToTargetList(@NonNull List sourceList, @NonNull SeatUrn targetSeat, @NonNull ContractUrn targetContract) {
    List targetList = new List()
        .setCreator(targetSeat)
        .setCreatorContract(targetContract)
        .setName(sourceList.getName())
        .setListType(sourceList.getListType())
        .setListSource(sourceList.getListSource());
    if (sourceList.getDescription() != null) {
      targetList.setDescription(sourceList.getDescription());
    }
    return targetList;
  }

  private Task<java.util.List<ListEntity>> getListEntities(@NonNull java.util.List<List> customListsToBeTransferred, @NonNull SeatUrn sourceSeat) {
    // Retrieve all the list entities associated with the current batch of custom lists
    java.util.List<Task<Pair<Integer, java.util.List<ListEntity>>>> findListEntitiesTaskList =
        getFindListEntitiesTask(sourceSeat, customListsToBeTransferred);

    return Task.par(findListEntitiesTaskList).flatMap(getListEntitiesTasks -> {
      java.util.List<ListEntity> sourceSeatListEntities = new ArrayList<>();
      getListEntitiesTasks.forEach(listPair -> sourceSeatListEntities.addAll(listPair.getSecond()));
      return Task.value(sourceSeatListEntities);
    });
  }

  private java.util.List<Task<Pair<Integer, java.util.List<ListEntity>>>> getFindListEntitiesTask(@NonNull SeatUrn sourceSeat,
      @NonNull java.util.List<List> customListsBatch) {
    java.util.List<Task<Pair<Integer, java.util.List<ListEntity>>>> findListEntitiesTaskList = Lists.newArrayList();
    // Retrieve listEntities in batches for each List
    for (List customList: customListsBatch) {
      findListEntitiesTaskList.add(findAllListEntitiesByListId(sourceSeat,
          customList.getId(), new Pair<>(0, new ArrayList<>())));
    }
    return findListEntitiesTaskList;
  }

  private Task<Pair<Integer, java.util.List<ListEntity>>> findAllListEntitiesByListId(@NonNull SeatUrn owner,
       long listId, @NonNull Pair<Integer, java.util.List<ListEntity>> allResults) {
    return findAllListEntitiesByListIdRecursive(owner, listId, allResults, 1000);
  }

  private Task<Pair<Integer, java.util.List<ListEntity>>> findAllListEntitiesByListIdRecursive(@NonNull SeatUrn owner,
      long listId, @NonNull Pair<Integer, java.util.List<ListEntity>> allResults, int limit) {
    int start = allResults.getSecond().size();
    int listEntityRetrievalSize = 100;

    return _salesListEntityService.getListEntities(owner, listId, start, listEntityRetrievalSize, SortOrder.DESCENDING)
        .flatMap(listEntityBatchPair -> {
          // Retrieve new total count of custom lists retrieved
          java.util.List<ListEntity> listEntityBatch = listEntityBatchPair.getSecond();
          Integer newTotal = listEntityBatch.size() + allResults.getFirst();

          // Add new custom lists retrieved to previously stored custom lists
          java.util.List<ListEntity> newListEntityList = allResults.getSecond();
          newListEntityList.addAll(listEntityBatch);

          // Create new Pair<Integer, List<Lists>> to serve as allResults in the next recursive call
          Pair<Integer, java.util.List<ListEntity>> newAllResults = new Pair<>(newTotal, newListEntityList);
          if (newListEntityList.size() >= limit || listEntityBatch.size() < listEntityRetrievalSize) {
            return Task.value(newAllResults);
          }
          if (listEntityBatch.size() == listEntityRetrievalSize) {
            return findAllListEntitiesByListIdRecursive(owner, listId, newAllResults, limit);
          }
          return Task.value(newAllResults);
        });
  }

  private java.util.List<ListEntity> convertSourceSeatEntitiesToTargetSeatEntities(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull BiMap<Long, Long> sourceSeatToTargetSeatListMap, @NonNull java.util.List<ListEntity> sourceSeatListEntities) {
    java.util.List<ListEntity> targetSeatListEntities = new ArrayList<>(sourceSeatListEntities.size());
    sourceSeatListEntities.forEach(listEntity -> {
      if (sourceSeatToTargetSeatListMap.get(listEntity.getList().getIdAsLong()) != null) {
        try {
          SalesListUrn listUrn = SalesListUrn.createFromUrn(
              Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, sourceSeatToTargetSeatListMap.get(listEntity.getList().getIdAsLong())));
          ListEntity targetEntity = listEntity
              .setCreator(targetSeat)
              .setList(listUrn)
              .setCreatedAt(System.currentTimeMillis())
              .setLastModifiedAt(System.currentTimeMillis());
          if (listEntity.getPriorityInfo() != null) {
            ListEntityPriorityInfo listEntityPriorityInfo = new ListEntityPriorityInfo();
            listEntityPriorityInfo.setPriority(listEntity.getPriorityInfo().getPriority());
            targetEntity.setPriorityInfo(listEntityPriorityInfo);
          }
          targetSeatListEntities.add(targetEntity);
        } catch (URISyntaxException ex) {
          LOG.error("Failed to generate SalesListUrn for list: {} in source seat: {} and target seat: {}",
              listEntity.getList().getIdAsLong(), sourceSeat, targetSeat, ex);
          throw new RuntimeException(ex);
        }
      }
    });
    return targetSeatListEntities;
  }

  private Task<Map<CompoundKey, CreateResponse>> createListEntitiesInTargetSeat(@NonNull ContractUrn targetContract,
      @NonNull java.util.List<ListEntity> listEntities) {
    try {
      return _salesListEntityService.batchCreateListEntities(listEntities, targetContract);
    } catch (Exception e) {
      LOG.error("Unable to create list entities for target seat lists: {}",
          listEntities.stream().map(listEntity -> listEntity.getList().getIdAsLong()).collect(Collectors.toSet()), e);
      throw new RuntimeException(e);
    }
  }

  private Task<Boolean> deleteSeatListsInDestSeat(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      java.util.List<Task<Boolean>> targetSeatListsToDeleteTaskList) {
    if (targetSeatListsToDeleteTaskList == null || targetSeatListsToDeleteTaskList.isEmpty()) {
      return Task.value(true);
    }
    LOG.info("Unable to transfer all the list entities associated with one or more source lists from"
        + " source seat: {} to destination seat: {}", sourceSeat, targetSeat);
    return Task.par(targetSeatListsToDeleteTaskList)
        .map(deleteTargetListResponse -> deleteTargetListResponse.stream()
            .allMatch(isSuccessfullyDeleted -> isSuccessfullyDeleted))
        .onFailure(throwable -> LOG.error("Could not delete one or more newly created lists", throwable));
  }

  /**
   * Function to compose SalesSeatTransferRequestUrn.
   * @param contractUrn contract urn
   * @param seatTransferRequestId id of seat transfer request
   * @return SavedSearchEntityKey
   */
  static SalesSeatTransferRequestUrn getSalesSeatTransferRequestUrn(ContractUrn contractUrn, Long seatTransferRequestId) {
    try {
      Urn seatTransferRequestUrn = SalesSeatTransferRequestUrn.createFromTuple(SalesSeatTransferRequestUrn.ENTITY_TYPE,
          ImmutableList.of(contractUrn, seatTransferRequestId));
      return SalesSeatTransferRequestUrn.createFromUrn(seatTransferRequestUrn);
    } catch (URISyntaxException ex) {
      throw new RuntimeException(ex);
    }
  }
}
