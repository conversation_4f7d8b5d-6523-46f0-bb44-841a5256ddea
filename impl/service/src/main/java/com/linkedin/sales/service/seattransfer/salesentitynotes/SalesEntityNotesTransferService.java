package com.linkedin.sales.service.seattransfer.salesentitynotes;


import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.TupleKey;
import com.linkedin.common.urn.Urn;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.note.SalesNoteService;
import com.linkedin.sales.service.seattransfer.SalesEntityTransferService;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import com.linkedin.salesnote.Note;
import com.linkedin.salesnote.NoteKey;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SalesEntityNotesTransferService implements SalesEntityTransferService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesEntityNotesTransferService.class);
  private final SalesNoteService _salesNoteService;
  private final SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  private final int _noteBatchSize;
  private static final OwnershipTransferEntityType ENTITY_TYPE = OwnershipTransferEntityType.SALES_ENTITY_NOTES;

  public SalesEntityNotesTransferService(SalesNoteService salesNoteService,
      SalesSeatTransferCopyAssociationsClient salesSeatTransferCopyAssociationsClient,
      int noteBatchSize
  ) {
    _salesNoteService = salesNoteService;
    _copyAssociationsClient = salesSeatTransferCopyAssociationsClient;
    _noteBatchSize = noteBatchSize;
  }

  @Override
  public Task<Void> transfer(@NonNull OwnershipTransferRequest ownershipTransferRequest, @NonNull SeatUrn actor) {
    // Retrieve sourceSeat and targetSeat info
    SeatUrn sourceSeat = ownershipTransferRequest.getSourceSeat();
    SeatUrn targetSeat = ownershipTransferRequest.getTargetSeat();
    ContractUrn targetContract = ownershipTransferRequest.getTargetContract();

    LOG.info("Transferring {} from sourceSeat {} to targetSeat {} for SeatTransferRequest: {}",
        ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

    // Fetch the first batch of notes in the source seat
    // Only 0.0004% of sales navigator seats have more than 10000 entity notes.
    // Average entity notes transferred in a month per seat transfer is ~15
    // https://docs.google.com/document/d/1lvXKPEUEBUqEHxaGJZFrpfsg8IUYqlpZA3tligMiw-s/edit?tab=t.0#heading=h.3y1fgmozcfdw
    Task<List<Note>> sourceSeatSalesNotesTask = _salesNoteService.findAllSavedEntityNotesBySeat(sourceSeat, 10000);
    return sourceSeatSalesNotesTask.flatMap(sourceSeatSalesNotes -> {
      if (sourceSeatSalesNotes.isEmpty()) {
        LOG.info("Exiting notes transfer because no entity notes were found for sourceSeat: {}", sourceSeat);
        return Task.value(null);
      }

      // Get already transferred entity notes from copyAssociations client
      Task<List<OwnershipTransferCopyAssociation>> ownershipTransferCopyAssociationsListTask = getOwnershipTransferCopyAssociationsList(
          sourceSeatSalesNotes, sourceSeat, targetContract);
      return ownershipTransferCopyAssociationsListTask.flatMap(ownershipTransferCopyAssociations -> {
        // Extract noteIds of sales entity notes that already have transfer copy associations for them
        Set<Long> alreadyTransferredNoteIds =
            ownershipTransferCopyAssociations.stream().map(ownershipTransferCopyAssociation -> {
              TupleKey entityKey = ownershipTransferCopyAssociation.getSourceEntity().getEntityKey();
              return entityKey.getAs(2, Long.class);
            }).collect(Collectors.toSet());

        // Remove SalesNotes already transferred
        List<Note> notTransferredSourceSeatSalesNotes = sourceSeatSalesNotes.stream()
            .filter(salesNote -> !alreadyTransferredNoteIds.contains(salesNote.getNoteId())).collect(Collectors.toList());

        if (notTransferredSourceSeatSalesNotes.isEmpty()) {
          LOG.info("All sales entity notes transferred already from sourceSeat {} to targetSeat {}", sourceSeat, targetSeat);
          return Task.value(null);
        }

        // Convert Notes to have targetSeat as owner and targetContract as contract
        List<Note> newTargetSeatSalesEntityNotes = notTransferredSourceSeatSalesNotes.stream()
            .map(note -> convertToDestNote(note, targetContract, targetSeat))
            .collect(Collectors.toList());

        // Create SalesNotes in targetSeat
        Task<List<CreateResponse>> successfullyCreatedSalesNotesTask =
            batchCreateSalesEntityNotes(newTargetSeatSalesEntityNotes);
        return successfullyCreatedSalesNotesTask.flatMap(successfullyCreatedSalesNotesCreateResponses -> {
          // Create a BiMap<Note, Note> mapping sourceSeat entityNotes to the targetSeat notes
          BiMap<Note, Note> sourceSeatToTargetSeatNoteMap = HashBiMap.create();
          for (int i = 0; i < notTransferredSourceSeatSalesNotes.size(); i++) {
            CreateResponse newNoteResponse = successfullyCreatedSalesNotesCreateResponses.get(i);
            if (newNoteResponse.getStatus() != HttpStatus.S_201_CREATED) {
              LOG.warn("Unable to create targetSeat entity note for sourceSeat entity note with id: {}, error: {}",
                  notTransferredSourceSeatSalesNotes.get(i).getNoteId(), newNoteResponse.getError());
            } else {
              sourceSeatToTargetSeatNoteMap.put(notTransferredSourceSeatSalesNotes.get(i),
                  convertCreateResponseToNote(newNoteResponse));
            }
          }

          // If there are sales entity notes that failed to be created keep a count of them
          long numberOfFailedCreatedNotes = successfullyCreatedSalesNotesCreateResponses.stream()
              .filter(createResponse -> createResponse.getStatus() != HttpStatus.S_201_CREATED)
              .count();

          LOG.info("Creating entity note copyAssociations for sourceSeat {} to targetSeat {}", sourceSeat, targetSeat);
          // Create ownership copy associations records
          Task<List<Long>> newCopyAssociationsListTask = updateCopyAssociationsClientTable(sourceSeat, targetSeat, targetContract,
              sourceSeatToTargetSeatNoteMap, actor, ownershipTransferRequest);
          return newCopyAssociationsListTask.flatMap(newCopyAssociationsList -> {
            LOG.info("Finished creating {} entity note copyAssociations for sourceSeat {} to targetSeat {}",
                newCopyAssociationsList.size(), sourceSeat, targetSeat);
            // Throw error if desired amount of saved leads to be transferred doesn't match the amount actually transferred
            if (numberOfFailedCreatedNotes > 0L) {
              String errorMessage = String.format(
                  "Sales entity notes were partially transferred for request %d, expected %d entity notes to be transferred"
                      + " but %d entity notes failed to be transferred", ownershipTransferRequest.getId(),
                  newTargetSeatSalesEntityNotes.size(), numberOfFailedCreatedNotes);
              LOG.error(errorMessage);
              throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMessage);
            }

            LOG.info("Completed {} transfer for sourceSeat {} and targetSeat {} for request {}", ENTITY_TYPE,
                sourceSeat, targetSeat, ownershipTransferRequest.getId());
            return Task.value(null);
          });
        });
      });
    });
  }

  private Task<List<Long>> updateCopyAssociationsClientTable(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull ContractUrn targetContract, @NonNull BiMap<Note, Note> sourceSeatNoteToTargetSeatNoteMap, @NonNull SeatUrn actor,
      @NonNull OwnershipTransferRequest ownershipTransferRequest) {
    AuditStamp auditStamp = new AuditStamp().setActor(actor);
    SalesSeatTransferRequestUrn transferRequestUrn = getSalesSeatTransferRequestUrn(ownershipTransferRequest.getSourceContract(),
        ownershipTransferRequest.getId());
    List<OwnershipTransferCopyAssociation> newSuccessfulTransferRecords = sourceSeatNoteToTargetSeatNoteMap.entrySet().stream()
        .map(entryPair -> new OwnershipTransferCopyAssociation()
            .setSourceSeat(sourceSeat)
            .setTargetSeat(targetSeat)
            .setCreated(auditStamp)
            .setOwnershipTransferEntityType(ENTITY_TYPE)
            .setOwnershipTransferRequest(transferRequestUrn)
            .setTargetContract(targetContract)
            .setSourceEntity(Urn.createFromTuple(
                SalesNoteUrn.ENTITY_TYPE,
                sourceSeat,
                entryPair.getKey().getEntity(),
                entryPair.getKey().getNoteId())
            )
            .setTargetEntity(Urn.createFromTuple(
                SalesNoteUrn.ENTITY_TYPE,
                targetSeat,
                entryPair.getValue().getEntity(),
                entryPair.getValue().getNoteId())
            ))
        .collect(Collectors.toList());
    return _copyAssociationsClient.createCopyAssociations(newSuccessfulTransferRecords);
  }

  private Task<List<OwnershipTransferCopyAssociation>> getOwnershipTransferCopyAssociationsList(
      @NonNull List<Note> sourceSeatEntityNotes, @NonNull SeatUrn sourceSeat, @NonNull ContractUrn targetContract) {
    List<Urn> salesSavedEntityNoteUrns = sourceSeatEntityNotes.stream()
        .map(savedEntityNote -> Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, sourceSeat, savedEntityNote.getEntity(),
            savedEntityNote.getNoteId()))
        .collect(Collectors.toList());

    return _copyAssociationsClient.findPreviousTransfers(salesSavedEntityNoteUrns, targetContract);
  }

  private Task<List<CreateResponse>> batchCreateSalesEntityNotes(@NonNull List<Note> salesEntityNotesToTransfer) {
    if (salesEntityNotesToTransfer.isEmpty()) {
      return Task.value(new ArrayList<>());
    }
    List<List<Note>> batches = Lists.partition(salesEntityNotesToTransfer, Math.max(_noteBatchSize, 1));
    Task<List<CreateResponse>> currentTask = executeCreateResponseForSalesEntityNotesList(batches.get(0));

    List<CreateResponse> accumulator = new ArrayList<>();
    for (int i = 1; i < batches.size(); i++) {
      int k = i;
      currentTask = currentTask.flatMap(associationIds -> {
        accumulator.addAll(associationIds);
        return executeCreateResponseForSalesEntityNotesList(batches.get(k));
      });
    }

    return currentTask.map(associations -> {
      accumulator.addAll(associations);
      return accumulator;
    });
  }

  private Task<List<CreateResponse>> executeCreateResponseForSalesEntityNotesList(@NonNull List<Note> salesEntityNotesToTransfer) {
    if (salesEntityNotesToTransfer.isEmpty()) {
      return Task.value(new ArrayList<>());
    }
    List<Task<CreateResponse>> notesCreateResponseTaskList = salesEntityNotesToTransfer.stream()
        .map(note -> _salesNoteService.createNote(note))
        .collect(Collectors.toList());

    return Task.par(notesCreateResponseTaskList).flatMap(notesCreateResponseTasks -> Task.value(notesCreateResponseTasks));
  }

  Note convertToDestNote(Note account, ContractUrn targetContract, SeatUrn targetSeat) {
    try {
      return account.clone().setContract(targetContract).setSeat(targetSeat);
    } catch (CloneNotSupportedException e) {
      // Can't happen since clone is implemented in RecordTemplate class
      throw new RuntimeException(e);
    }
  }

  private Note convertCreateResponseToNote(CreateResponse response) {
    ComplexResourceKey<?, ?> complexResourceKey = (ComplexResourceKey<?, ?>) response.getId();
    NoteKey noteKey = (NoteKey) complexResourceKey.getKey();
    return new Note().setEntity(noteKey.getEntity()).setNoteId(noteKey.getNoteId()).setSeat(noteKey.getSeat());
  }

  /**
   * Function to compose SalesSeatTransferRequestUrn.
   * @param contractUrn contract urn
   * @param seatTransferRequestId id of seat transfer request
   * @return SavedSearchEntityKey
   */
  static SalesSeatTransferRequestUrn getSalesSeatTransferRequestUrn(ContractUrn contractUrn, Long seatTransferRequestId) {
    try {
      Urn seatTransferRequestUrn = Urn.createFromTuple(SalesSeatTransferRequestUrn.ENTITY_TYPE,
          ImmutableList.of(contractUrn, seatTransferRequestId));
      return SalesSeatTransferRequestUrn.createFromUrn(seatTransferRequestUrn);
    } catch (URISyntaxException ex) {
      throw new RuntimeException(ex);
    }
  }
}

