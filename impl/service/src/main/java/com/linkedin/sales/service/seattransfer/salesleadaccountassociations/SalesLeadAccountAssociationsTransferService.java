package com.linkedin.sales.service.seattransfer.salesleadaccountassociations;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.lss.salesleadaccount.SalesLeadAccountAssociationResult;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.seattransfer.SalesEntityTransferService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesSavedLeadAccountAssociationUrn;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.NonNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.ownershiptransfer.OwnershipTransferEntityType.*;
import static com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient.*;
import static com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService.*;


public class SalesLeadAccountAssociationsTransferService implements SalesEntityTransferService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadAccountAssociationsTransferService.class);
  private final SalesLeadAccountCommonService _salesLeadAccountCommonService;
  private final SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  private final LixService _lixService;
  private static final int PARALLELISM = 1;
  private static final OwnershipTransferEntityType ENTITY_TYPE = SALES_LEAD_ACCOUNT_ASSOCIATIONS;

  public SalesLeadAccountAssociationsTransferService(SalesLeadAccountCommonService salesLeadAccountCommonService,
      SalesSeatTransferCopyAssociationsClient salesSeatTransferCopyAssociationsClient, LixService lixService) {
    _salesLeadAccountCommonService = salesLeadAccountCommonService;
    _copyAssociationsClient = salesSeatTransferCopyAssociationsClient;
    _lixService = lixService;
  }

  @Override
  public Task<Void> transfer(@NonNull OwnershipTransferRequest ownershipTransferRequest, @NonNull SeatUrn actor) {
    // Retrieve sourceSeat and targetSeat
    SeatUrn sourceSeat = ownershipTransferRequest.getSourceSeat();
    SeatUrn targetSeat = ownershipTransferRequest.getTargetSeat();
    ContractUrn targetContract = ownershipTransferRequest.getTargetContract();

    LOG.info("Beginning {} transfer for sourceSeat {} and targetSeat {} for request {}",
        ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

    Task<List<LeadAccountAssociation>> sourceSeatLeadAccountAssociationsTask = getLeadAccountAssociations(sourceSeat);
    return sourceSeatLeadAccountAssociationsTask.flatMap(sourceSeatLeadAccountAssociations -> {
      if (sourceSeatLeadAccountAssociations.isEmpty()) {
        LOG.info("sourceSeat {} does not have any {}, exiting transfer for request {}",
            sourceSeat, ENTITY_TYPE, ownershipTransferRequest.getId());
        return Task.value(null);
      }

      List<Urn> salesLeadAccountAssociationUrns = sourceSeatLeadAccountAssociations.stream()
          .map(salesLeadAccountAssociation -> Urn.createFromTuple(
              SalesSavedLeadAccountAssociationUrn.ENTITY_TYPE,
              sourceSeat,
              salesLeadAccountAssociation.getAccount().getIdAsLong(),
              salesLeadAccountAssociation.getLead().getMemberIdEntity()))
          .collect(Collectors.toList());

      // Query copyAssociationsClient table to get ownership transfer copy association records
      Task<List<OwnershipTransferCopyAssociation>> ownershipTransferCopyAssociationsListTask =
          _copyAssociationsClient.findPreviousTransfers(salesLeadAccountAssociationUrns, targetContract);
      return ownershipTransferCopyAssociationsListTask.flatMap(ownershipTransferCopyAssociations -> {
        // Convert copy association records to set of LeadAccountAssociations so that already transferred organizationIds are not transferred again
        Map<Long, Set<Long>> accountToLeadMap = new HashMap<>();
        for (OwnershipTransferCopyAssociation copyAssociation: ownershipTransferCopyAssociations) {
          try {
            // Extract sourceEntityUrn containing lead account association information
            SalesSavedLeadAccountAssociationUrn salesSavedLeadAccountAssociationUrn =
                SalesSavedLeadAccountAssociationUrn.createFromUrn(copyAssociation.getSourceEntity());
            Long organizationId = salesSavedLeadAccountAssociationUrn.getOrganizationIdEntity();
            Long memberId = salesSavedLeadAccountAssociationUrn.getMemberIdEntity();

            // Add map entry of account to lead association
            Set<Long> associatedSavedLeads = accountToLeadMap.getOrDefault(organizationId, new HashSet<>());
            associatedSavedLeads.add(memberId);
            accountToLeadMap.put(organizationId, associatedSavedLeads);
          } catch (URISyntaxException e) {
            throw new RuntimeException(e);
          }
        }

        // Remove already transferred lead account associations from list of sourceSeat's lead account associations
        List<LeadAccountAssociation> notTransferredLeadAccountAssociations = sourceSeatLeadAccountAssociations.stream()
            .filter(leadAccountAssociation -> {
              Long organizationId = leadAccountAssociation.getAccount().getIdAsLong();
              Long memberId = leadAccountAssociation.getLead().getMemberIdEntity();
              // Filter out lead account associations that already have transfer copy association records
              Set<Long> leadToAccountAssociations = accountToLeadMap.get(organizationId);
              return leadToAccountAssociations == null || !leadToAccountAssociations.contains(memberId);
            }).collect(Collectors.toList());

        if (notTransferredLeadAccountAssociations.isEmpty()) {
          LOG.info("All {} transferred already from sourceSeat {} to targetSeat {}, exiting transfer with request {}", ENTITY_TYPE,
              sourceSeat, targetSeat, ownershipTransferRequest.getId());
          return Task.value(null);
        }

        // Retrieve targetSeat's lead account associations
        Task<List<LeadAccountAssociation>> targetSeatLeadAccountAssociationsTask =
            getLeadAccountAssociations(targetSeat);
        return targetSeatLeadAccountAssociationsTask.flatMap(targetSeatLeadAccountAssociations -> {
          // Determine what lead account associations to transfer
          List<LeadAccountAssociation> leadAccountAssociationsToTransfer =
              getLeadAccountAssociationsToTransfer(notTransferredLeadAccountAssociations,
                  targetSeatLeadAccountAssociations);

          if (leadAccountAssociationsToTransfer.isEmpty()) {
            LOG.info("No {} from sourceSeat {} were eligible for transfer for request {}",
                ENTITY_TYPE, sourceSeat, ownershipTransferRequest.getId());
            return Task.value(null);
          }

          // Update lead account associations with updated targetSeat owner and contract
          List<LeadAccountAssociation> newTargetSeatAssociations = leadAccountAssociationsToTransfer.stream()
              .map(leadAccountAssociation -> convertToDestAssociation(leadAccountAssociation, targetContract,
                  targetSeat))
              .collect(Collectors.toList());

          return _lixService.getLixTreatment(targetContract, LixUtils.LSS_PAGES_SEAT_TRANSFER_BATCH_SIZE_AND_DELAY, null).flatMap(treatment -> {
            int[] batchSizeAndDelay = parseBatchSizeAndDelayFromLixTreatment(treatment);
            int batchSize = batchSizeAndDelay[0];
            int delay = batchSizeAndDelay[1];
            // Create new lead account associations for targetSeat
            List<SalesLeadAccountAssociationResult> newTargetSeatLeadAccountAssociationsResult =
                _salesLeadAccountCommonService.batchCreateLeadAccountAssociations(newTargetSeatAssociations,
                    batchSize, delay);

            List<SalesLeadAccountAssociationResult> successfulLeadToAccountAssociationCreations =
                newTargetSeatLeadAccountAssociationsResult.stream()
                    .filter(result -> result.getStatus() == ActionStatus.SUCCESS)
                    .collect(Collectors.toList());
            LOG.info("{} {} were successfully created in the targetSeat {} for request {}",
                successfulLeadToAccountAssociationCreations.size(), ENTITY_TYPE, targetSeat, ownershipTransferRequest.getId());

            List<SalesLeadAccountAssociationResult> failedTransfers =
                newTargetSeatLeadAccountAssociationsResult.stream()
                    .filter(result -> result.getStatus() != ActionStatus.SUCCESS
                        && result.getStatus() != ActionStatus.CONFLICT)
                    .collect(Collectors.toList());


            LOG.info("Creating {} copyAssociations for sourceSeat {} to targetSeat {} for request {}",
                ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

            Task<List<Long>> newCopyAssociationsListTask =
                updateCopyAssociationsClientTable(sourceSeat, targetSeat, targetContract,
                    successfulLeadToAccountAssociationCreations, actor, ownershipTransferRequest);
            return newCopyAssociationsListTask.flatMap(newCopyAssociationsList -> {
              LOG.info("Finished creating {} {} copyAssociations for sourceSeat {} to targetSeat {} for request {}",
                  newCopyAssociationsList.size(), ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

              if (failedTransfers.isEmpty()) {
                return Task.value(null);
              } else {
                LOG.warn("Unable to create {} {} out of {} total in targetSeat {}, for request {}, for organizations: {}\nReason: {}",
                    failedTransfers.size(),
                    ENTITY_TYPE,
                    newTargetSeatLeadAccountAssociationsResult.size(),
                    targetSeat,
                    ownershipTransferRequest.getId(),
                    new ArrayList<>(failedTransfers.stream().map(SalesLeadAccountAssociationResult::getOrganizationId).collect(Collectors.toList())),
                    new ArrayList<>(failedTransfers.stream().map(SalesLeadAccountAssociationResult::getStatus).collect(Collectors.toList())));
                String errorMessage = String.format(
                    "%s were partially transferred, expected %d lead account associations to be transferred"
                        + " but %d lead account associations failed to be transferred for request %d", ENTITY_TYPE,
                    newTargetSeatAssociations.size(), failedTransfers.size(), ownershipTransferRequest.getId());
                LOG.error(errorMessage);
                throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMessage);
              }
            });
          });
        });
      });
    });
  }

  private Task<List<LeadAccountAssociation>> getLeadAccountAssociations(@NonNull SeatUrn owner) {
    return _salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(owner);
  }

  /**
   * Filter out the associations to transfer using the following criteria for a given association A in the source seat:
   *  - If an association with the same lead exists in the destination, do not transfer A
   *  - Otherwise transfer A
   * Determines which lead account associations to transfer from sourceSeat to targetSeat
   * @param sourceSeatLeadAccountAssociations sourceSeat lead account associations
   * @param targetSeatLeadAccountAssociations targetSeat lead account associations
   * @return
   */
  private List<LeadAccountAssociation> getLeadAccountAssociationsToTransfer(
      @NonNull List<LeadAccountAssociation> sourceSeatLeadAccountAssociations,
      @NonNull List<LeadAccountAssociation> targetSeatLeadAccountAssociations
  ) {
    Set<Long> targetSeatLeadsWithAssociations = targetSeatLeadAccountAssociations.stream()
        .map(association -> association.getLead().getMemberIdEntity()).collect(Collectors.toSet());
    return sourceSeatLeadAccountAssociations.stream()
        .filter(association -> {
          Long memberId = association.getLead().getMemberIdEntity();
          return !targetSeatLeadsWithAssociations.contains(memberId);
        })
        .collect(Collectors.toMap(
            LeadAccountAssociation::getLead,
            Function.identity(),
            SalesLeadAccountAssociationsTransferService::getMostRecentAssociation
        )).values().stream().collect(Collectors.toList());
  }

  private Task<List<Long>> updateCopyAssociationsClientTable(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull ContractUrn targetContract, @NonNull List<SalesLeadAccountAssociationResult> successfulLeadAccountAssociations,
      @NonNull SeatUrn actor, @NonNull OwnershipTransferRequest ownershipTransferRequest) {
    // Storing SeatUrn and SalesLead memberId for undo, reassignment and undo reassignment
    AuditStamp auditStamp = new AuditStamp().setActor(actor);
    SalesSeatTransferRequestUrn transferRequestUrn = getSalesSeatTransferRequestUrn(ownershipTransferRequest.getSourceContract(),
        ownershipTransferRequest.getId());
    List<OwnershipTransferCopyAssociation> newSuccessfulTransferRecords = successfulLeadAccountAssociations.stream()
        .map(leadAccountAssociation -> new OwnershipTransferCopyAssociation()
            .setSourceSeat(sourceSeat)
            .setTargetSeat(targetSeat)
            .setCreated(auditStamp)
            .setOwnershipTransferEntityType(ENTITY_TYPE)
            .setOwnershipTransferRequest(transferRequestUrn)
            .setTargetContract(targetContract)
            .setSourceEntity(Urn.createFromTuple(
                SalesSavedLeadAccountAssociationUrn.ENTITY_TYPE,
                sourceSeat,
                leadAccountAssociation.getOrganizationId(),
                leadAccountAssociation.getMemberId())
            )
            .setTargetEntity(Urn.createFromTuple(
                SalesSavedLeadAccountAssociationUrn.ENTITY_TYPE,
                targetSeat,
                leadAccountAssociation.getOrganizationId(),
                leadAccountAssociation.getMemberId()
            )))
        .collect(Collectors.toList());
    return _copyAssociationsClient.batchCreate(newSuccessfulTransferRecords);
  }

  /**
   * Return the lead account association that has the most recent modified time.
   */
  @VisibleForTesting
  static LeadAccountAssociation getMostRecentAssociation(@NonNull LeadAccountAssociation association1,
      @NonNull LeadAccountAssociation association2) {
    long association1ModifiedTime = association1.getChangeAuditStamps().getLastModified().getTime();
    long association2ModifiedTime = association2.getChangeAuditStamps().getLastModified().getTime();
    if (association1ModifiedTime >= association2ModifiedTime) {
      return association1;
    }
    return association2;
  }

  /**
   * Convert the lead account association into a new association using the dest seat and contract.
   */
  @VisibleForTesting
  static LeadAccountAssociation convertToDestAssociation(@NonNull LeadAccountAssociation leadAccountAssociation,
      @NonNull ContractUrn targetContract, @NonNull SeatUrn targetSeat) {
    try {
      return leadAccountAssociation.clone().setContract(targetContract).setCreator(targetSeat);
    } catch (CloneNotSupportedException e) {
      // Can't happen since clone is implemented in RecordTemplate class
      throw new RuntimeException(e);
    }
  }

  /**
   * Function to compose SalesSeatTransferRequestUrn.
   * @param contractUrn contract urn
   * @param seatTransferRequestId id of seat transfer request
   * @return SavedSearchEntityKey
   */
  static SalesSeatTransferRequestUrn getSalesSeatTransferRequestUrn(ContractUrn contractUrn, Long seatTransferRequestId) {
    try {
      Urn seatTransferRequestUrn = SalesSeatTransferRequestUrn.createFromTuple(SalesSeatTransferRequestUrn.ENTITY_TYPE,
          ImmutableList.of(contractUrn, seatTransferRequestId));
      return SalesSeatTransferRequestUrn.createFromUrn(seatTransferRequestUrn);
    } catch (URISyntaxException ex) {
      throw new RuntimeException(ex);
    }
  }
}
