package com.linkedin.sales.service.seattransfer.salesleads;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.lss.salesleadaccount.SalesLeadActionResult;
import com.linkedin.lss.salesleadaccount.SalesLeadActionResultArray;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.service.seattransfer.SalesEntityTransferService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesSavedLeadUrn;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import com.linkedin.salesleadaccount.SalesLead;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService.*;

public class SalesLeadsTransferService implements SalesEntityTransferService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesLeadsTransferService.class);
  private final SalesLeadAccountCommonService _salesLeadAccountCommonService;
  private final SavedLeadService _savedLeadService;
  private final SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  private static final OwnershipTransferEntityType ENTITY_TYPE = OwnershipTransferEntityType.SALES_LEADS;
  private final LixService _lixService;

  public SalesLeadsTransferService(SalesSeatTransferCopyAssociationsClient copyAssociationsClient,
  SalesLeadAccountCommonService salesLeadAccountCommonService, SavedLeadService savedLeadService, LixService lixService) {
    _copyAssociationsClient = copyAssociationsClient;
    _salesLeadAccountCommonService = salesLeadAccountCommonService;
    _savedLeadService = savedLeadService;
    _lixService = lixService;
  }

  @Override
  public Task<Void> transfer(@NonNull OwnershipTransferRequest ownershipTransferRequest, @NonNull SeatUrn actor) {
    // Retrieve sourceSeat and targetSeat
    SeatUrn sourceSeat = ownershipTransferRequest.getSourceSeat();
    SeatUrn targetSeat = ownershipTransferRequest.getTargetSeat();
    ContractUrn targetContract = ownershipTransferRequest.getTargetContract();

    LOG.info("Beginning {} transfer for sourceSeat {} and targetSeat {} for request {}",
        ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());

    // Get targetSeat saved lead count and the max saved lead limit
    int maxSavedLeadsLimit = _savedLeadService.getMaxSavedLeadLimitAllTiers();
    Task<Integer> targetSeatSavedLeadCountTask = _salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(targetSeat);
    return targetSeatSavedLeadCountTask.flatMap(targetSeatSavedLeadCount -> {
      // If targetSeat is already at limit for saved leads, exit transfer
      if (targetSeatSavedLeadCount >= maxSavedLeadsLimit) {
        LOG.info("targetSeat {} saved lead count {} exceeds the lead limit. Exiting {} transfer for request {}.", targetSeat, targetSeatSavedLeadCount,
            ENTITY_TYPE, ownershipTransferRequest.getId());
        return Task.value(null);
      }

      // Get a list of all the sourceSeat's saved leads
      Task<List<SalesLead>> sourceSeatSavedLeadsListTask =
          _salesLeadAccountCommonService.findAllSavedLeadsForSeatUpToLimit(sourceSeat, maxSavedLeadsLimit);

      return sourceSeatSavedLeadsListTask.flatMap(sourceSeatSavedLeadsList -> {
        if (sourceSeatSavedLeadsList == null || sourceSeatSavedLeadsList.isEmpty()) {
          LOG.info("No {} for sourceSeat {}, exiting transfer with request {}",
              ENTITY_TYPE, sourceSeat, ownershipTransferRequest.getId());
          return Task.value(null);
        }

        // Query copyAssociationsClient table to get ownership transfer copy association records
        List<Urn> salesSavedLeadUrns = sourceSeatSavedLeadsList.stream()
            .map(savedLead -> Urn.createFromTuple(SalesSavedLeadUrn.ENTITY_TYPE, sourceSeat,
                savedLead.getMember().getMemberIdEntity()))
            .collect(Collectors.toList());

        LOG.info("Entering into for loop for {} transfer from sourceSeat {} to targetSeat {} for request {}",
            ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
        Task<List<OwnershipTransferCopyAssociation>> ownershipTransferCopyAssociationsListTask =
            _copyAssociationsClient.findPreviousTransfers(salesSavedLeadUrns, targetContract);
        return ownershipTransferCopyAssociationsListTask.flatMap(ownershipTransferCopyAssociations -> {
          LOG.info("Finished getting previous {} copyAssociation records for transfer from sourceSeat {} to targetSeat {} for request {}",
              ENTITY_TYPE, sourceSeat, targetSeat, ownershipTransferRequest.getId());
          // Convert copy association records to set of ids so that already transferred memberIds are not transferred again
          Set<Long> previouslyTransferredSavedLeads =
              ownershipTransferCopyAssociations.stream().map(copyAssociation -> {
                try {
                  SalesSavedLeadUrn salesSavedLeadUrn =
                      SalesSavedLeadUrn.createFromUrn(copyAssociation.getSourceEntity());
                  return salesSavedLeadUrn.getMemberIdEntity();
                } catch (URISyntaxException e) {
                  throw new RuntimeException(e);
                }
              }).collect(Collectors.toSet());
          // Remove already transferred saved leads from list of sourceSeat's saved leads
          List<SalesLead> notTransferredSavedLeads = sourceSeatSavedLeadsList.stream()
              .filter(savedLead -> !previouslyTransferredSavedLeads.contains(savedLead.getMember().getMemberIdEntity()))
              .collect(Collectors.toList());

          if (notTransferredSavedLeads.isEmpty()) {
            LOG.info("All {} transferred already from sourceSeat {} to targetSeat {} for request {}", ENTITY_TYPE,
                sourceSeat, targetSeat, ownershipTransferRequest.getId());
            return Task.value(null);
          }

          // Get list of saved leads to be transferred with respect to max saved leads limit
          List<SalesLead> savedLeadsToBeTransferred =
              getSavedLeadsToTransfer(targetSeat, notTransferredSavedLeads, targetSeatSavedLeadCount,
                  maxSavedLeadsLimit);

          // Prepare list of SalesLeads with owner and contract fields updated to be targetSeat and targetContract
          List<SalesLead> newTargetSeatSavedLeads = savedLeadsToBeTransferred.stream()
              .map(salesLead -> convertToTargetSalesLead(salesLead, targetSeat, targetContract))
              .collect(Collectors.toList());

          return _lixService.getLixTreatment(targetContract, LixUtils.LSS_PAGES_SEAT_TRANSFER_BATCH_SIZE_AND_DELAY, null).flatMap(treatment -> {
            int[] batchSizeAndDelay = parseBatchSizeAndDelayFromLixTreatment(treatment);
            int batchSize = batchSizeAndDelay[0];
            int delay = batchSizeAndDelay[1];
            // Create transferred saved leads for targetSeat
            SalesLeadActionResultArray salesLeadActionResultArray =
                _salesLeadAccountCommonService.batchCreateSavedLeads(newTargetSeatSavedLeads,
                    batchSize, delay);

            // Get the memberIds of all savedLeads successfully created for targetSeat
            List<Long> successfullyTransferredMemberIds = salesLeadActionResultArray.stream()
                .filter(result -> result.getStatus() == ActionStatus.SUCCESS)
                .map(SalesLeadActionResult::getMemberId)
                .collect(Collectors.toList());

            LOG.info("{} {} were successfully created in the targetSeat {} for request {}",
                successfullyTransferredMemberIds.size(), ENTITY_TYPE, targetSeat, ownershipTransferRequest.getId());

            // Since leads that the sourceSeat and targetSeat already have in common will product a ActionStatus.CONFLICT,
            // Check if any attempts to create saved leads in targetSeat were a result of actual failure
            List<SalesLeadActionResult> failedTransfers = salesLeadActionResultArray.stream()
                .filter(
                    result -> result.getStatus() != ActionStatus.SUCCESS && result.getStatus() != ActionStatus.CONFLICT)
                .collect(Collectors.toList());

            // Create copyAssociationsClient table entries for all successfully transferred saved leads
            Task<List<Long>> newCopyAssociationsListTask =
                updateCopyAssociationsClientTable(sourceSeat, targetSeat, targetContract,
                    successfullyTransferredMemberIds, actor, ownershipTransferRequest);

            LOG.info("Creating {} copyAssociations for sourceSeat {} to targetSeat {} for request {}", ENTITY_TYPE,
                sourceSeat, targetSeat, ownershipTransferRequest.getId());
            return newCopyAssociationsListTask.flatMap(newCopyAssociationsList -> {
              LOG.info("Finished creating {} {} copyAssociations for sourceSeat {} to targetSeat {} for request {}",
                  newCopyAssociationsList.size(), ENTITY_TYPE, sourceSeat, targetSeat,
                  ownershipTransferRequest.getId());

              if (failedTransfers.isEmpty()) {
                LOG.info("Completed {} transfer for sourceSeat {} and targetSeat {} for request {}", ENTITY_TYPE,
                    sourceSeat, targetSeat, ownershipTransferRequest.getId());
                return Task.value(null);
              } else {
                LOG.warn(
                    "Unable to create {} {} out of {} total saved leads in targetSeat {}, for request {}, for member ids: {}\nBecause of: {}",
                    failedTransfers.size(), ENTITY_TYPE, salesLeadActionResultArray.size(), targetSeat,
                    ownershipTransferRequest.getId(), new ArrayList<>(
                        failedTransfers.stream().map(SalesLeadActionResult::getMemberId).collect(Collectors.toList())),
                    new ArrayList<>(
                        failedTransfers.stream().map(SalesLeadActionResult::getStatus).collect(Collectors.toList())));
                String errorMessage = String.format("%s were partially transferred, expected %d leads to be transferred"
                        + " but %d leads failed to be transferred for request %d", ENTITY_TYPE,
                    newTargetSeatSavedLeads.size(), failedTransfers.size());
                LOG.error(errorMessage);
                throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, errorMessage);
              }
            });
          });
        });
      });
    });
  }

  private List<SalesLead> getSavedLeadsToTransfer(@NonNull SeatUrn targetSeat, @NonNull List<SalesLead> notTransferredSavedLeads,
      int targetSeatSavedLeadCount, int maxSavedLeadsLimit) {
    // If newly transferred saved leads would not cause the targetSeat to exceed the saved lead limit, continue as normal
    if (notTransferredSavedLeads.size() + targetSeatSavedLeadCount <= maxSavedLeadsLimit) {
      return notTransferredSavedLeads;
    }

    // Only transfer the most recently saved leads up until max saved lead limit
    LOG.info("targetSeat {} has {} saved leads but lead limit is {} so only transferring {} leads out of a total of {}",
        targetSeat,
        targetSeatSavedLeadCount,
        maxSavedLeadsLimit,
        maxSavedLeadsLimit - targetSeatSavedLeadCount,
        notTransferredSavedLeads.size());
    return notTransferredSavedLeads.stream()
        .filter(SalesLead::hasChangeAuditStamps)
        .sorted(Comparator.comparing(lead -> lead.getChangeAuditStamps().getLastModified().getTime(), Comparator.reverseOrder()))
        .limit((long) maxSavedLeadsLimit - targetSeatSavedLeadCount)
        .collect(Collectors.toList());
  }

  private SalesLead convertToTargetSalesLead(@NonNull SalesLead lead, @NonNull SeatUrn targetSeat, @NonNull ContractUrn targetContract) {
    try {
      return lead.clone().setContract(targetContract).setOwner(targetSeat);
    } catch (CloneNotSupportedException e) {
      throw new RuntimeException(e);
    }
  }

  private Task<List<Long>> updateCopyAssociationsClientTable(@NonNull SeatUrn sourceSeat, @NonNull SeatUrn targetSeat,
      @NonNull ContractUrn targetContract, List<Long> successfullyTransferredMemberIds, @NonNull SeatUrn actor,
      @NonNull OwnershipTransferRequest ownershipTransferRequest) {
    // Storing SeatUrn and SalesLead memberId for undo, reassignment and undo reassignment
    AuditStamp auditStamp = new AuditStamp().setActor(actor);
    SalesSeatTransferRequestUrn transferRequestUrn = getSalesSeatTransferRequestUrn(ownershipTransferRequest.getSourceContract(),
        ownershipTransferRequest.getId());
    List<OwnershipTransferCopyAssociation> newSuccessfulTransferRecords = successfullyTransferredMemberIds.stream()
        .map(memberId -> new OwnershipTransferCopyAssociation()
            .setSourceSeat(sourceSeat)
            .setTargetSeat(targetSeat)
            .setCreated(auditStamp)
            .setOwnershipTransferEntityType(ENTITY_TYPE)
            .setOwnershipTransferRequest(transferRequestUrn)
            .setTargetContract(targetContract)
            .setSourceEntity(Urn.createFromTuple(SalesSavedLeadUrn.ENTITY_TYPE, sourceSeat, memberId))
            .setTargetEntity(Urn.createFromTuple(SalesSavedLeadUrn.ENTITY_TYPE, targetSeat, memberId)))
        .collect(Collectors.toList());
    return _copyAssociationsClient.createCopyAssociations(newSuccessfulTransferRecords);
  }

  /**
   * Function to compose SalesSeatTransferRequestUrn.
   * @param contractUrn contract urn
   * @param seatTransferRequestId id of seat transfer request
   * @return SavedSearchEntityKey
   */
  static SalesSeatTransferRequestUrn getSalesSeatTransferRequestUrn(ContractUrn contractUrn, Long seatTransferRequestId) {
    try {
      Urn seatTransferRequestUrn = SalesSeatTransferRequestUrn.createFromTuple(SalesSeatTransferRequestUrn.ENTITY_TYPE,
          ImmutableList.of(contractUrn, seatTransferRequestId));
      return SalesSeatTransferRequestUrn.createFromUrn(seatTransferRequestUrn);
    } catch (URISyntaxException ex) {
      throw new RuntimeException(ex);
    }
  }
}
