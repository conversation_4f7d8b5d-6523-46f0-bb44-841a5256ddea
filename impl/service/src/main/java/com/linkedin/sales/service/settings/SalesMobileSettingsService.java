package com.linkedin.sales.service.settings;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.template.StringArray;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.MobileSettings;
import com.linkedin.salesseatpreference.CalendarSyncSettings;
import com.linkedin.salesseatpreference.SalesMobileSettings;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.stream.Collectors;


/**
 * Sales Mobile Settings Service
 */
public class SalesMobileSettingsService {
  private final LssSeatSettingDB _lssSeatSettingDB;

  public SalesMobileSettingsService(LssSeatSettingDB lssSeatSettingDB) {
    _lssSeatSettingDB = lssSeatSettingDB;
  }

  protected static MobileSettings convertToEspressoModel(SalesMobileSettings setting) {
    MobileSettings mobileSettings = new MobileSettings();
    if (setting.hasContract()) {
      mobileSettings.contractUrn = setting.getContract().toString();
    }
    if (setting.hasCalendarSync()) {
      CalendarSyncSettings calendarSync = setting.getCalendarSync();
      mobileSettings.calendarSyncSettings = new com.linkedin.sales.espresso.CalendarSyncSettings();
      mobileSettings.calendarSyncSettings.isEnabled = calendarSync.isEnabled();
      mobileSettings.calendarSyncSettings.isEventWithoutAttendeesShown = calendarSync.isEventWithoutAttendeesShown();
      mobileSettings.calendarSyncSettings.isAllEventsForTodayShown = calendarSync.isAllEventsForTodayShown();
      if (calendarSync.hasSyncedCalendars()) {
        mobileSettings.calendarSyncSettings.syncedCalendars = new ArrayList<>(calendarSync.getSyncedCalendars());
      } else {
        mobileSettings.calendarSyncSettings.syncedCalendars = Collections.emptyList();
      }
    }
    mobileSettings.isCallLoggingEnabled = setting.isCallLoggingEnabled(GetMode.NULL);
    mobileSettings.rateTheAppLastShowAt = setting.getRateTheAppLastShowAt(GetMode.NULL);
    return mobileSettings;
  }

  protected static SalesMobileSettings convertToSalesMobileSettings(
      @NonNull MobileSettings mobileSettings, @NonNull SeatUrn seatUrn) {
    ContractUrn contractUrn;
    try {
      contractUrn = ContractUrn.deserialize(mobileSettings.contractUrn.toString());
    } catch (URISyntaxException e) {
      throw new RuntimeException("Fail to create contractUrn from " + mobileSettings.contractUrn, e);
    }
    SalesMobileSettings salesMobileSettings = new SalesMobileSettings().setSeat(seatUrn).setContract(contractUrn)
        .setCallLoggingEnabled(mobileSettings.isCallLoggingEnabled, SetMode.IGNORE_NULL)
        .setRateTheAppLastShowAt(mobileSettings.rateTheAppLastShowAt, SetMode.IGNORE_NULL);
    if (mobileSettings.calendarSyncSettings != null) {
      salesMobileSettings.setCalendarSync(new CalendarSyncSettings()
          .setEnabled(mobileSettings.calendarSyncSettings.isEnabled)
          .setEventWithoutAttendeesShown(mobileSettings.calendarSyncSettings.isEventWithoutAttendeesShown)
          .setAllEventsForTodayShown(mobileSettings.calendarSyncSettings.isAllEventsForTodayShown)
          .setSyncedCalendars(new StringArray(mobileSettings.calendarSyncSettings.syncedCalendars
              .stream().map(CharSequence::toString).collect(Collectors.toList()))));
    }
    return salesMobileSettings;
  }

  /**
   * Partial update the mobile setting of the given seat, also used for creation when there is no record on this seat
   * @param seat the owner seat urn
   * @param patch patch of SalesMobileSettings that has to be created/updated for the seat
   * @return HTTP update response we want the clients to receive, otherwise exception will be thrown
   */
  public Task<UpdateResponse> updateMobileSetting(@NonNull SeatUrn seat, PatchRequest<SalesMobileSettings> patch) {
    SalesMobileSettings salesMobileSettings = new SalesMobileSettings();
    try {
      PatchApplier.applyPatch(salesMobileSettings, patch);
    } catch (DataProcessingException e) {
      return Task.failure("Error applying patch during updateSeatSetting",
              new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    MobileSettings mobileSettings = convertToEspressoModel(salesMobileSettings);
    return _lssSeatSettingDB.updateMobileSetting(seat, mobileSettings).map(UpdateResponse::new);
  }


  /**
   * Get the espresso mobile setting record owned by the seat
   * @param seat the owner seat urn
   * @return seat's mobile settings, if not exists, return the default object
   */
  public Task<SalesMobileSettings> getMobileSetting(@NonNull SeatUrn seat) {
    return _lssSeatSettingDB.getMobileSetting(seat)
        .map(mobileSettings -> convertToSalesMobileSettings(mobileSettings, seat))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            // if record not found, we return an empty object, with a dummy contract urn, so no need to fetch the seat to get the actual contract id
            // this object instead of exception will help us to get default value from client side
            return Task.value(new SalesMobileSettings().setSeat(seat).setContract(new ContractUrn(0L)));
          } else {
            return Task.failure(t);
          }
        });
  }

}


