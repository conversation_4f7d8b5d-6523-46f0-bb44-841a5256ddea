package com.linkedin.sales.service.settings;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.data.template.BooleanMap;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.AIMessagingPreferences;
import com.linkedin.sales.espresso.CrmAutoSaveSettingType;
import com.linkedin.sales.espresso.CrmAutoSaveSettings;
import com.linkedin.sales.espresso.SeatSetting;
import com.linkedin.salesseatpreference.AIMessageType;
import com.linkedin.salesseatpreference.AccountDashboardColumnSettings;
import com.linkedin.salesseatpreference.AccountDashboardColumnSettingsArray;
import com.linkedin.salesseatpreference.AccountDashboardSettings;
import com.linkedin.salesseatpreference.CrmSeatSetting;
import com.linkedin.salesseatpreference.CrmSeatSettingArray;
import com.linkedin.salesseatpreference.SalesSeatSetting;
import com.linkedin.salesseatpreference.VisibilityAsProfileViewer;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Sales Seat Settings Service
 */
public class SalesSeatSettingsService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesSeatSettingsService.class);

  private final LssSeatSettingDB _lssSeatSettingDB;

  public SalesSeatSettingsService(LssSeatSettingDB lssSeatSettingDB) {
    _lssSeatSettingDB = lssSeatSettingDB;
  }

  protected static SeatSetting convertToEspressoModel(SalesSeatSetting setting) {
    SeatSetting seatSetting = new SeatSetting();
    if (setting.hasContract()) {
      seatSetting.contractUrn = setting.getContract().toString();
    }
    if (setting.hasEmailPreferences()) {
      seatSetting.emailPreferences = new HashMap<>(setting.getEmailPreferences());
    }
    if (setting.hasCrmAutoSaveSettings()) {
      seatSetting.crmAutoSavePreferences = setting.getCrmAutoSaveSettings()
          .stream()
          .map(salesSeatSetting -> new CrmAutoSaveSettings(
              CrmAutoSaveSettingType.valueOf(salesSeatSetting.getType().name()), salesSeatSetting.isValue()))
          .collect(Collectors.toList());
    }
    seatSetting.inMailMessageSignature = setting.getInMailMessageSignature(GetMode.NULL);
    seatSetting.pushNotificationEnabled = setting.isPushNotificationEnabled(GetMode.NULL);
    seatSetting.teamlinkOptedOut = setting.isTeamlinkOptedOut(GetMode.NULL);
    seatSetting.usageReportingFlagshipDataOptOut = setting.isUsageReportingFlagshipDataOptOut(GetMode.NULL);
    seatSetting.onboardingCompletedTime = setting.getOnboardingCompletedAt(GetMode.NULL);
    seatSetting.lastLoggedInTime = setting.getLastLoggedInAt(GetMode.NULL);
    seatSetting.messageLearningEnabled = setting.isMessageLearningEnabled(GetMode.NULL);
    if (setting.hasVisibilityAsProfileViewer()
        && setting.getVisibilityAsProfileViewer() != VisibilityAsProfileViewer.$UNKNOWN) {
      seatSetting.visibilityAsProfileViewer =
          com.linkedin.sales.espresso.VisibilityAsProfileViewer.valueOf(setting.getVisibilityAsProfileViewer().name());
    }
    if (setting.hasDismissedBuyerIntentCardAt()) {
      seatSetting.setDismissedBuyerIntentCardTime(setting.getDismissedBuyerIntentCardAt());
    }
    if (setting.hasDismissedAccountIqCardAt()) {
      seatSetting.setDismissedAccountIqCardTime(setting.getDismissedAccountIqCardAt());
    }
    if (setting.hasAiMessagingPreferences()) {
      com.linkedin.salesseatpreference.AIMessagingPreferences preferences = setting.getAiMessagingPreferences();
      if (preferences != null) {
        AIMessagingPreferences aiMessagingPreferences = new AIMessagingPreferences();
        Urn productUrn = setting.getAiMessagingPreferences().getLastUsedProduct();
        if (productUrn != null) {
          aiMessagingPreferences.setLastUsedProductUrn(productUrn.toString());
        }
        AIMessageType aiMessageType = setting.getAiMessagingPreferences().getLastUsedAIMessageType(GetMode.NULL);
        if (aiMessageType != null) {
          aiMessagingPreferences.setLastUsedMessageType(
              com.linkedin.sales.espresso.AIMessageType.valueOf(String.valueOf(aiMessageType)));
        }
        seatSetting.setAiMessagingPreferences(aiMessagingPreferences);
      }
    }
    if (setting.hasAccountDashboardSettings()) {
      AccountDashboardSettings accountDashboardSettings = setting.getAccountDashboardSettings();
      com.linkedin.sales.espresso.AccountDashboardSettings settings = new com.linkedin.sales.espresso.AccountDashboardSettings();
      // set the last viewed list id
      settings.setLastViewedList(accountDashboardSettings.getLastViewedList());

      // set the column settings
      List<com.linkedin.sales.espresso.AccountDashboardColumnSettings> settingsList =
          accountDashboardSettings.getColumns()
              .stream()
              .map(value -> new com.linkedin.sales.espresso.AccountDashboardColumnSettings(value.getColumn(), value.isUserSelected()))
              .collect(Collectors.toList());
      settings.setColumns(settingsList);
      seatSetting.setAccountDashboardSettings(settings);
    }
    if (setting.hasTransformOnboardingCompletedAt()) {
      seatSetting.setTransformOnboardingCompletedTime(setting.getTransformOnboardingCompletedAt());
    }
    return seatSetting;
  }

  protected static SalesSeatSetting convertToSalesSeatSetting(@NonNull SeatSetting seatSetting,
      @NonNull SeatUrn seatUrn) {
    ContractUrn contractUrn;
    try {
      contractUrn = ContractUrn.deserialize(seatSetting.contractUrn.toString());
    } catch (URISyntaxException e) {
      throw new RuntimeException("Fail to create contractUrn from " + seatSetting.contractUrn, e);
    }
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting().setSeat(seatUrn).setContract(contractUrn);
    if (seatSetting.emailPreferences != null) {
      BooleanMap emailPreferences = new BooleanMap();
      seatSetting.emailPreferences.forEach((type, value) -> emailPreferences.put(type.toString(), value));
      salesSeatSetting.setEmailPreferences(emailPreferences);
    }
    if (seatSetting.crmAutoSavePreferences != null) {
      List<CrmSeatSetting> settingList = seatSetting.crmAutoSavePreferences.stream()
          .map(espressoCrmSeatSetting -> new CrmSeatSetting().setType(
                  SalesConnectedCrmSettingType.valueOf(String.valueOf(espressoCrmSeatSetting.CrmAutoSaveSettingType)))
              .setValue(espressoCrmSeatSetting.getValue()))
          .collect(Collectors.toList());
      salesSeatSetting.setCrmAutoSaveSettings(new CrmSeatSettingArray(settingList));
    }

    if (seatSetting.inMailMessageSignature != null) {
      salesSeatSetting.setInMailMessageSignature(seatSetting.inMailMessageSignature.toString());
    }
    if (seatSetting.visibilityAsProfileViewer != null) {
      salesSeatSetting.setVisibilityAsProfileViewer(
          VisibilityAsProfileViewer.valueOf(seatSetting.visibilityAsProfileViewer.name()));
    }
    if (seatSetting.dismissedBuyerIntentCardTime != null) {
      salesSeatSetting.setDismissedBuyerIntentCardAt(seatSetting.dismissedBuyerIntentCardTime);
    }
    if (seatSetting.dismissedAccountIqCardTime != null) {
      salesSeatSetting.setDismissedAccountIqCardAt(seatSetting.dismissedAccountIqCardTime);
    }
    if (seatSetting.aiMessagingPreferences != null) {
      com.linkedin.salesseatpreference.AIMessagingPreferences aiMessagingPreferences =
          new com.linkedin.salesseatpreference.AIMessagingPreferences();
      if (seatSetting.aiMessagingPreferences.lastUsedProductUrn != null) {
        String lastProductUrnString = seatSetting.aiMessagingPreferences.lastUsedProductUrn.toString();
        try {
          Urn productUrn = Urn.createFromString(lastProductUrnString);
          aiMessagingPreferences.setLastUsedProduct(productUrn);
        } catch (URISyntaxException e) {
          throw new IllegalArgumentException("Failed to parse the urn from " + lastProductUrnString);
        }
      }
      if (seatSetting.aiMessagingPreferences.lastUsedMessageType != null) {
        aiMessagingPreferences.setLastUsedAIMessageType(
            AIMessageType.valueOf(String.valueOf(seatSetting.aiMessagingPreferences.lastUsedMessageType)));
      }
      salesSeatSetting.setAiMessagingPreferences(aiMessagingPreferences);
    }

    if (seatSetting.accountDashboardSettings != null) {
      AccountDashboardSettings accountDashboardSettings = new AccountDashboardSettings();
      // set column settings
      List<AccountDashboardColumnSettings> columnPreferences =
          seatSetting.accountDashboardSettings.columns.stream()
              .map(value -> new AccountDashboardColumnSettings().setColumn(value.getColumn().toString())
                  .setUserSelected(value.getUserSelected()))
              .collect(Collectors.toList());
      accountDashboardSettings.setColumns(new AccountDashboardColumnSettingsArray(columnPreferences));

      // set last viewed list
      accountDashboardSettings.setLastViewedList(seatSetting.accountDashboardSettings.lastViewedList, SetMode.IGNORE_NULL);
      salesSeatSetting.setAccountDashboardSettings(accountDashboardSettings);
    }

    if (seatSetting.transformOnboardingCompletedTime != null) {
      salesSeatSetting.setTransformOnboardingCompletedAt(seatSetting.transformOnboardingCompletedTime);
    }
    return salesSeatSetting.setPushNotificationEnabled(seatSetting.pushNotificationEnabled, SetMode.IGNORE_NULL)
        .setTeamlinkOptedOut(seatSetting.teamlinkOptedOut, SetMode.IGNORE_NULL)
        .setUsageReportingFlagshipDataOptOut(seatSetting.usageReportingFlagshipDataOptOut, SetMode.IGNORE_NULL)
        .setOnboardingCompletedAt(seatSetting.onboardingCompletedTime, SetMode.IGNORE_NULL)
        .setLastLoggedInAt(seatSetting.lastLoggedInTime, SetMode.IGNORE_NULL)
        .setMessageLearningEnabled(seatSetting.messageLearningEnabled, SetMode.IGNORE_NULL);
  }

  /**
   * Partial update the user setting of the given seat, also used for creation when there is no record on this seat
   * @param seat the owner seat urn
   * @param patch patch of salesSeatSetting that has to be created/updated for the seat
   * @return HTTP update response we want the clients to receive, otherwise exception will be thrown
   */
  public Task<UpdateResponse> updateSeatSetting(@NonNull SeatUrn seat, PatchRequest<SalesSeatSetting> patch) {
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting();
    try {
      PatchApplier.applyPatch(salesSeatSetting, patch);
    } catch (DataProcessingException e) {
      return Task.failure("Error applying patch during updateSeatSetting",
          new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }
    SeatSetting seatSetting = convertToEspressoModel(salesSeatSetting);
    return _lssSeatSettingDB.updateSeatSetting(seat, seatSetting).map(UpdateResponse::new);
  }

  /**
   * Get the espresso setting record owned by the seat
   * @param seat the owner seat urn
   * @return seat's setting, if not exists, return the default object
   */
  public Task<SalesSeatSetting> getSeatSetting(@NonNull SeatUrn seat) {
    return _lssSeatSettingDB.getSeatSetting(seat)
        .map(seatSetting -> convertToSalesSeatSetting(seatSetting, seat))
        .recoverWith(t -> {
          if (ExceptionUtils.isEntityNotFoundException(t)) {
            // if record not found, we return an empty object, with a dummy contract urn, so no need to fetch the seat to get the actual contract id
            // this object instead of exception will help us to get default value from client side
            return Task.value(new SalesSeatSetting().setSeat(seat).setContract(new ContractUrn(0L)));
          } else {
            return Task.failure(t);
          }
        });
  }

  /**
   * Batch get SalesSeatSettings on the set of seat urns
   * @param seatUrns set of seat urns
   * @return a mapping of Seat Urn's to SalesSeatSetting
   */
  public Task<Map<SeatUrn, SalesSeatSetting>> batchGet(Set<SeatUrn> seatUrns) {
    return _lssSeatSettingDB.batchGetSeatSettings(seatUrns)
        .map(map -> map.entrySet()
            .stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey, e -> convertToSalesSeatSetting(e.getValue(), e.getKey()))));
  }
}


