package com.linkedin.sales.service.sharing;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.SetMode;
import com.linkedin.data.transform.DataProcessingException;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.restli.server.util.PatchApplier;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.ExceptionUtils;
import com.linkedin.sales.espresso.ResourcePolicyView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.salessharing.SharingRole;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Sales Sharing Service
 * <AUTHOR>
 */
// TODO: Add finder for policies. Can be single cumulative finder or two separate ones.
public class SalesSharingService {
  private static final Logger LOG = LoggerFactory.getLogger(SalesSharingService.class);
  private static final ImmutableSet<HttpStatus> GOOD_UPDATE_RESPONSE_STATUSES =
      ImmutableSet.of(HttpStatus.S_200_OK, HttpStatus.S_201_CREATED, HttpStatus.S_204_NO_CONTENT);

  private final LssSharingDB _lssSharingDB;
  private final AclServiceDispatcher _aclServiceDispatcher;

  // an immutable set contains all the simple sharing types
  private final Set<PolicyType> simpleSharingTypes = ImmutableSet.of(PolicyType.PEOPLE_SEARCH);

  public SalesSharingService(LssSharingDB lssSharingDB, AclServiceDispatcher aclServiceDispatcher) {
    _lssSharingDB = lssSharingDB;
    _aclServiceDispatcher = aclServiceDispatcher;
  }

  /**
   * Batch get policies for the given policy keys
   * It calls single get for each given policy key. The returning map only contains the successfully fetched policies with their keys
   * @param policyKeys
   * @return a map from each policy key to policy
   */
  public Task<Map<PolicyKey, Policy>> batchGet(Set<PolicyKey> policyKeys) {
    List<Task<AbstractMap.SimpleEntry<PolicyKey, Policy>>> getPolicyTasks =
        policyKeys.stream().map(policyKey -> _lssSharingDB.getSubjectPolicy(policyKey.getSubject(),
            policyKey.getPolicyType().toString(), policyKey.getResource()).map(subjectPolicy -> {
              SeatUrn creatorSeatUrn = subjectPolicy.creatorSeatUrn == null
                  ? null : UrnUtils.createSeatUrn(subjectPolicy.creatorSeatUrn);
              Policy policy = new Policy().setResource(policyKey.getResource())
                  .setPolicyType(policyKey.getPolicyType())
                  .setSubject(policyKey.getSubject())
                  .setRole(ServiceConstants.SHARE_ROLE_ESPRESSO_TO_SERVICE_MAPPING.get(subjectPolicy.role))
                  .setLastViewedAt(subjectPolicy.lastViewedTime, SetMode.IGNORE_NULL)
                  .setSubscribed(subjectPolicy.isSubscribed, SetMode.IGNORE_NULL)
                  .setCreatedAt(subjectPolicy.createdTime, SetMode.IGNORE_NULL)
                  .setCreator(creatorSeatUrn, SetMode.IGNORE_NULL)
                  .setAcceptedAt(subjectPolicy.acceptedTime, SetMode.IGNORE_NULL);
              return new AbstractMap.SimpleEntry<>(policyKey, policy);
            }).recover(t -> {
              if (!ExceptionUtils.isEntityNotFoundException(t)) {
                LOG.warn("Failed to get policy for {}.", policyKey, t);
              }
              return null;
            })).collect(Collectors.toList());

    return Task.par(getPolicyTasks)
        .map(policyList -> policyList.stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue)));
  }

  /**
   * Update existing or create new sharing policies in batch
   * Currently only supports a single resource and policy type
   *
   * Steps:
   * 1. Check if there are any existing owners of the resource
   *   a. if not, create owner record with the requester's seat
   *   b. if so, check for requester ownership and proceed
   * 2. Create given policy
   *
   * @param policies List of policy changes
   * @param requester the logged in user who requested the change.
   * @return the map that contains the update response
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchPartialUpdatePolicies(@NonNull List<Policy> policies,
      @NonNull Urn requester, @Nullable ContractUrn contractUrn) {

    if (policies.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    if (policies.stream().map(Policy::getResource).distinct().count() != 1
        || policies.stream().map(Policy::getPolicyType).distinct().count() != 1) {
      // For now, only supports a single resource type and policy type
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Batch update only supports a single resource and policy type"));
    }

    Urn resourceUrn = policies.get(0).getResource();
    PolicyType policyType = policies.get(0).getPolicyType();

    // if the policy type is simple sharing policy, there is no ownership and ACL permission check, no need to create
    // policy for the owner.
    if (simpleSharingTypes.contains(policyType)) {
      return batchUpdateSharingPoliciesHelper(policies, contractUrn, resourceUrn, policyType)
          .onFailure(t -> LOG.error("Fail to batch update the policies for {}", policies, t));
    }

    // only user with admin access can update policies(either create or partial update)
    return checkAccessForPolicyUpdate(policies, resourceUrn, requester, policyType).flatMap(accessDecision -> {
      if (accessDecision == AccessDecision.DENIED) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            String.format("Requester %s must have admin access to batch update policies", requester)));
      }
      return _lssSharingDB.getPoliciesByResource(resourceUrn, policyType, ServiceConstants.READER_ROLES,
          0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT).flatMap(response -> {
        List<Urn> roles = response.getResult().stream().map(Pair::getFirst).collect(Collectors.toList());
        Task<HttpStatus> createOwnerTask = Task.value(HttpStatus.S_204_NO_CONTENT); // default no-op

        if (roles.isEmpty()) {
          // no role record found (resource is shared first time), need to create a new owner record
          PolicyKey policyKey = new PolicyKey().setSubject(requester)
              .setPolicyType(policyType)
              .setResource(resourceUrn);
          SubjectPolicy subjectPolicy = new SubjectPolicy();
          subjectPolicy.role = ShareRole.OWNER;

          // contract urn can be null for certain policy types (i.e. LSI_METRICS_REPORT)
          Optional.ofNullable(contractUrn).map(contract -> subjectPolicy.contractUrn = contract.toString());

          //Since batch update can be called by one owner and the resourceContext will be the same for all the polices
          //we set the resourceContext from the first policy
          if (policies.get(0).getResourceContext() != null) {
            subjectPolicy.resourceContext =  policies.get(0).getResourceContext().toString();
          }
          createOwnerTask = _lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy);
        }

        return createOwnerTask.flatMap(createOwnerSuccess -> {
          if (!GOOD_UPDATE_RESPONSE_STATUSES.contains(createOwnerSuccess)) {
            return Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
                "Could not create owner record for resource " + resourceUrn));
          }
          return batchUpdateSharingPoliciesHelper(policies, contractUrn, resourceUrn, policyType);
        });
      });
    }).onFailure(t -> LOG.error("Fail to batch update the policies for {}", policies, t));
  }

  /**
   * Batch Delete existing sharing policies
   * @param policyKeys a list of compoundKeys for deletion
   * @param requester the logged in user who requested the change.
   * @return the map that contains the update response
   */
  public Task<Map<CompoundKey, UpdateResponse>> batchDeletePolicies(
      @NonNull List<PolicyKey> policyKeys, @NonNull Urn requester) {

    if (policyKeys.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    if (policyKeys.stream().map(PolicyKey::getResource).distinct().count() != 1
        || policyKeys.stream().map(PolicyKey::getPolicyType).distinct().count() != 1) {
      // For now, only supports a single resource type and policy type
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
          "Batch delete only supports a single resource and policy type"));
    }

    Urn resourceUrn = policyKeys.get(0).getResource();
    PolicyType policyType = policyKeys.get(0).getPolicyType();

    // if the requester is the subject of one of the policies, delete the policies whose subject is the given requester
    boolean isRequesterSubjectOfPolicy = policyKeys.stream().anyMatch(policy -> policy.getSubject().equals(requester));
    if (isRequesterSubjectOfPolicy) {
      return batchDeletePoliciesAsSubject(requester, policyKeys, resourceUrn, policyType);
    }
    // if the requester is not the subject of the policy, check if the requester has admin access, and then delete policies as admin
    return batchDeletePoliciesAsAdmin(requester, policyKeys, resourceUrn, policyType);
  }

  // helper method to batch delete policies when the requester is the subject of some of the policies
  private Task<Map<CompoundKey, UpdateResponse>> batchDeletePoliciesAsSubject(
      @NonNull Urn requester, @NonNull List<PolicyKey> policyKeys, @NonNull Urn resourceUrn, @NonNull PolicyType policyType) {
    return Task.par(policyKeys.stream().map(policyKey -> {
      Urn subject = policyKey.getSubject();
      CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, subject)
          .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, policyType)
          .append(ServiceConstants.RESOURCE_COMPOUND_KEY, resourceUrn);
      // the requester can only delete the policy when he is the subject of the policy
      if (!subject.equals(requester)) {
        return Task.value(new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(HttpStatus.S_403_FORBIDDEN)));
      } else {
        return deleteSubjectPolicy(policyKey, compoundKey);
      }
    }).collect(Collectors.toList())).map(list -> list.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  // helper method to batch delete policies when the requester is admin of the resource
  private Task<Map<CompoundKey, UpdateResponse>> batchDeletePoliciesAsAdmin(
      @NonNull Urn requester, @NonNull List<PolicyKey> policyKeys, @NonNull Urn resourceUrn, @NonNull PolicyType policyType) {
    return _aclServiceDispatcher.checkAccessDecision(requester, policyType, resourceUrn, AccessAction.ADMIN).flatMap(accessDecision -> {
      if (accessDecision == AccessDecision.DENIED) {
        return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            String.format("Requester %s must be a writer to batch delete policies", requester)));
      }
      return Task.par(policyKeys.stream().map(policyKey -> {
            CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, policyKey.getSubject())
                .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, policyType)
                .append(ServiceConstants.RESOURCE_COMPOUND_KEY, resourceUrn);

            return deleteSubjectPolicy(policyKey, compoundKey);
          }).collect(Collectors.toList())
      ).map(list -> list.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }).onFailure(t -> LOG.error("fail to batch delete policies: {}, requester: {}", policyKeys, requester, t));
  }

  // helper method to delete the given policy from sharing DB
  private Task<AbstractMap.SimpleEntry<CompoundKey, UpdateResponse>> deleteSubjectPolicy(PolicyKey policyKey, CompoundKey compoundKey) {
    return _lssSharingDB.deleteSubjectPolicy(policyKey).map(success -> {
      if (success) {
        return new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(HttpStatus.S_204_NO_CONTENT));
      } else {
        return new AbstractMap.SimpleEntry<>(compoundKey,
            new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      }
    }).recover(t -> {
      LOG.error("Failed to delete subject policy for {}", policyKey, t);
      return new AbstractMap.SimpleEntry<>(compoundKey,
          new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
    });
  }

  // helper method to batch update sharing policies.
  private Task<Map<CompoundKey, UpdateResponse>> batchUpdateSharingPoliciesHelper(Collection<Policy> policies,
      ContractUrn contractUrn, @NonNull Urn resourceUrn, @NonNull PolicyType policyType) {
    return Task.par(policies.stream().map(policy -> {
      PolicyKey policyKey =
          new PolicyKey().setSubject(policy.getSubject()).setPolicyType(policyType).setResource(resourceUrn);
      SubjectPolicy subjectPolicy = new SubjectPolicy();
      if (policy.hasRole()) {
        subjectPolicy.role = ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING.get(policy.getRole());
      }

      Optional.ofNullable(contractUrn).map(contract -> subjectPolicy.contractUrn = contract.toString());

      if (policy.getResourceContext() != null) {
        subjectPolicy.resourceContext = policy.getResourceContext().toString();
      }
      if (policy.hasSubscribed()) {
        subjectPolicy.setIsSubscribed(policy.isSubscribed());
      }
      if (policy.hasLastViewedAt()) {
        subjectPolicy.setLastViewedTime(policy.getLastViewedAt());
      }
      if (policy.hasCreatedAt()) {
        subjectPolicy.setCreatedTime(policy.getCreatedAt());
      }
      if (policy.hasCreator()) {
        subjectPolicy.setCreatorSeatUrn(policy.getCreator().toString());
      }
      if (policy.hasAcceptedAt()) {
        subjectPolicy.setAcceptedTime(policy.getAcceptedAt());
      }
      CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, policy.getSubject())
          .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, policyType)
          .append(ServiceConstants.RESOURCE_COMPOUND_KEY, resourceUrn);
      return _lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy).map(
          updateStatus -> new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(updateStatus))).recover(t -> {
        LOG.error("Failed to update subject policy for {}", policyKey, t);
        return new AbstractMap.SimpleEntry<>(compoundKey, new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
      });
    }).collect(Collectors.toList()))
        .map(mapEntries -> mapEntries.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }


  /**
   * find policies for resource
   * @param resourceUrn resource urn
   * @param policyType policy type
   * @param roles roles that want to fetch. if null, default to fetch all roles. Do not allow empty roles.
   * @param requester logged in user's seatUrn (LSS) and {@Link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} (LSI).
   * A requester must have at least read access to the resource.
   * @param start paging start
   * @param count paging count
   * @return paginated list of policies
   */
  public Task<PaginatedList<Policy>> findPoliciesByResource(
      @NonNull Urn resourceUrn,
      @NonNull PolicyType policyType,
      @Nullable Set<SharingRole> roles,
      @NonNull Urn requester,
      int start,
      int count) {
    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
    }
    // only owner(admin) can find policies by resource
    return _aclServiceDispatcher.checkAccessDecision(requester, policyType, resourceUrn, AccessAction.READ)
        .flatMap(accessDecision -> {
          if (accessDecision == AccessDecision.ALLOWED) {
            Set<ShareRole> espressoRoles = roles == null ? null : roles.stream()
                .map(ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING::get).collect(Collectors.toSet());
            return _lssSharingDB.getPolicyViewsByResource(resourceUrn, policyType, espressoRoles, start, count)
                .map(paginatedPairs -> {
                  List<Policy> policies = paginatedPairs.getResult().stream().map(subjectUrnPolicyPair -> {
                  Urn subjectUrn = subjectUrnPolicyPair.getFirst();
                  ResourcePolicyView policyView = subjectUrnPolicyPair.getSecond();
                  return new Policy().setSubject(subjectUrn)
                      .setPolicyType(policyType)
                      .setResource(resourceUrn)
                      .setRole(ServiceConstants.SHARE_ROLE_ESPRESSO_TO_SERVICE_MAPPING.get(policyView.getRole()))
                      .setSubscribed(policyView.isSubscribed, SetMode.IGNORE_NULL)
                      .setCreatedAt(policyView.createdTime, SetMode.IGNORE_NULL)
                      .setAcceptedAt(policyView.acceptedTime, SetMode.IGNORE_NULL);
                  }).collect(Collectors.toList());
                  return PaginatedList.createForPage(policies, start, count, paginatedPairs.getTotal());
                });
          } else {
            String errMsg = String.format("seat:%s does not have permission to find policies:%s by resource:%s", requester,
                policyType, resourceUrn);
            LOG.warn(errMsg);
            return Task.failure(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, errMsg));
          }
        })
        .recoverWith(t -> {
          // if permission deny, return failure. Otherwise return empty result
          if (t instanceof RestLiServiceException && ((RestLiServiceException) t).getStatus() == HttpStatus.S_403_FORBIDDEN) {
            return Task.failure(t);
          }
          LOG.warn("fail to get policies for resource: {}, policyType: {}, roles: {}", resourceUrn, policyType, roles, t);
          return Task.value(PaginatedList.createForPage(Collections.emptyList(), start, count, 0));
        });
  }

  /**
   * find policies for resourceContext and subjectUrn
   * @param resourceContextUrn memberUrn or contractUrn the sharing policy is related to
   * @param policyType policy type
   * @param roles roles that want to fetch. if null, default to fetch all roles. Do not allow empty roles.
   * @param seatUrn logged in user's seatUrn
   * @param contractUrn logged in user's contractUrn
   * @return paginated list of policies
   */
  public Task<PaginatedList<Policy>> findPoliciesByResourceContext(
      @NonNull Urn resourceContextUrn,
      @NonNull PolicyType policyType,
      @Nullable Set<SharingRole> roles,
      @NonNull SeatUrn seatUrn,
      @NonNull ContractUrn contractUrn) {
    //TODO aaramach: If note has been explicitly shared with a seat, we need to find all the polices with the seat as
    //the subject

    int start = ServiceConstants.DEFAULT_START;
    int count = ServiceConstants.SHARING_POLICY_GET_ALL_COUNT;

    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
    }

    Set<ShareRole> espressoRoles = roles == null ? null
        : roles.stream().map(ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING::get).collect(Collectors.toSet());

    Policy.ResourceContext resourceContext = convertUrnToResourceContext(resourceContextUrn);
    if (resourceContext == null) {
      return Task.failure(new RuntimeException("Error converting urn to resourceContext."));
    }

    return _lssSharingDB.getPoliciesByResourceContextAndSubject(contractUrn, resourceContext, policyType, espressoRoles,
        start, count).map(contractPolicies -> {
      List<Policy> contractPolicyList = contractPolicies.getResult().stream().map(resourceUrnSubjectPolicyPair -> {
        Urn resourceUrn = resourceUrnSubjectPolicyPair.getFirst();
        SubjectPolicy subjectPolicy = resourceUrnSubjectPolicyPair.getSecond();
        SeatUrn creatorSeatUrn = subjectPolicy.creatorSeatUrn == null
            ? null : UrnUtils.createSeatUrn(subjectPolicy.creatorSeatUrn);
        return new Policy()
            .setSubject(contractUrn)
            .setPolicyType(policyType)
            .setResource(resourceUrn)
            .setRole(ServiceConstants.SHARE_ROLE_ESPRESSO_TO_SERVICE_MAPPING.get(subjectPolicy.role))
            .setResourceContext(resourceContext)
            .setCreatedAt(subjectPolicy.createdTime, SetMode.IGNORE_NULL)
            .setCreator(creatorSeatUrn, SetMode.IGNORE_NULL)
            .setAcceptedAt(subjectPolicy.acceptedTime, SetMode.IGNORE_NULL);
      }).collect(Collectors.toList());
      return PaginatedList.createForPage(contractPolicyList, start, count, contractPolicies.getTotal());
    }).recoverWith(t -> {
      // if permission deny, return failure. Otherwise return empty result
      if (t instanceof RestLiServiceException && ((RestLiServiceException) t).getStatus() == HttpStatus.S_403_FORBIDDEN) {
        return Task.failure(t);
      }
      LOG.warn("fail to get policies for resourceContext: {}, policyType: {}, roles: {} seat: {}, contract: {}",
          resourceContext, policyType, roles, seatUrn, contractUrn, t);
      return Task.value(PaginatedList.createForPage(Collections.emptyList(), start, count, 0));
    });
  }

  /**
   * find policies for subject
   * @param requester urn of the requester - {@link com.linkedin.common.urn.SeatUrn} for LSS
   * {@link com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn} for LSI
   * @param policyType policy type
   * @param roles roles that want to fetch. if null, default to fetch all roles. Do not allow empty roles.
   * @param start paging start
   * @param count paging count
   * @return paginated list of policies
   */
  public Task<PaginatedList<Policy>> findPoliciesBySubject(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @Nullable Set<SharingRole> roles,
      int start,
      int count) {
    if (roles != null && roles.isEmpty()) {
      return Task.failure(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
    }
    Set<ShareRole> espressoRoles =
        roles == null ? null : roles.stream().map(ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING::get).collect(Collectors.toSet());
    // For now, we only get policies under the requester(seat urn). In future when we support sharing to groups, we may
    // need to get policies under the requester's group.
    return _lssSharingDB.getPoliciesBySubject(requester, policyType.toString(), espressoRoles, start, count)
        .map(paginatedPairs -> {
          List<Policy> policies = paginatedPairs.getResult().stream().map(resourceUrnSubjectPolicyPair -> {
            Urn resourceUrn = resourceUrnSubjectPolicyPair.getFirst();
            SubjectPolicy subjectPolicy = resourceUrnSubjectPolicyPair.getSecond();
            SeatUrn creatorSeatUrn = subjectPolicy.creatorSeatUrn == null
                ? null : UrnUtils.createSeatUrn(subjectPolicy.creatorSeatUrn);
            return new Policy().setSubject(requester)
                .setPolicyType(policyType)
                .setResource(resourceUrn)
                .setRole(ServiceConstants.SHARE_ROLE_ESPRESSO_TO_SERVICE_MAPPING.get(subjectPolicy.role))
                .setCreatedAt(subjectPolicy.createdTime, SetMode.IGNORE_NULL)
                .setCreator(creatorSeatUrn, SetMode.IGNORE_NULL)
                .setAcceptedAt(subjectPolicy.acceptedTime, SetMode.IGNORE_NULL);
          }).collect(Collectors.toList());
          return PaginatedList.createForPage(policies, start, count, paginatedPairs.getTotal());
        })
        .recover(t -> {
          LOG.warn("fail to get policies for subject: {}, policyType: {}, roles: {}", requester, policyType, roles, t);
          return PaginatedList.createForPage(Collections.emptyList(), start, count, 0);
        });
  }

  /**
   * check access action for requester and resource
   * @param requester urn of the user who requested access
   * @param policyType policy type checking access for
   * @param resource urn of resource checking access for
   * @param accessAction access action request
   * @return whether or not user has access
   */
  public Task<AccessDecision> checkAccessDecision(
      @NonNull Urn requester,
      @NonNull PolicyType policyType,
      @NonNull Urn resource,
      @NonNull AccessAction accessAction) {
      return _aclServiceDispatcher.checkAccessDecision(requester, policyType, resource, accessAction);
  }

  @VisibleForTesting
  private Policy.ResourceContext convertUrnToResourceContext(Urn urn) {
    Policy.ResourceContext resourceContext = new Policy.ResourceContext();
    try {
      if (MemberUrn.ENTITY_TYPE.equals(urn.getEntityType())) {
        resourceContext.setMember(MemberUrn.createFromUrn(urn));
        return resourceContext;
    } else if (OrganizationUrn.ENTITY_TYPE.equals(urn.getEntityType())) {
        resourceContext.setOrganization(OrganizationUrn.createFromUrn(urn));
        return resourceContext;
      } else {
        LOG.error("Type not supported by AnnotatableEntityUrn. Type - {}", urn.getEntityType());
        return null;
      }
    } catch (URISyntaxException e) {
      LOG.error("Failed to convert urn to ResourceContext. URN :{}", urn);
      return null;
    }
  }

  public Task<UpdateResponse> partialUpdatePolicy(PolicyKey key, PatchRequest<Policy> patch, Urn requester) {
    Policy policy = new Policy()
        .setSubject(key.getSubject())
        .setPolicyType(key.getPolicyType())
        .setResource(key.getResource());
    try {
      PatchApplier.applyPatch(policy, patch);
    } catch (DataProcessingException e) {
      return Task.failure("Error applying patch during partialUpdatePolicy",
          new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
    }

    return batchPartialUpdatePolicies(Collections.singletonList(policy), requester, null).map(response -> {
      if (response.isEmpty()) {
        return new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
      }

      return response.values().stream().findFirst().get();
    });
  }


  /**
   * Check if the requester has the access to update sharing policies on a resource. There is one special case:
   * for relationship map, the requester is allowed to update his/her own sharing policy regardless of his/her role.
   * In this case, the regular ACL check is bypassed.
   * @return AccessDecision
   */
  private Task<AccessDecision> checkAccessForPolicyUpdate(@NonNull List<Policy> policies, @NonNull Urn resourceUrn,
      @NonNull Urn requesterUrn, @NonNull PolicyType policyType) {
    Task<Boolean> canByPassAccessCheck = Task.value(Boolean.FALSE);
    if (policyType == PolicyType.ACCOUNT_MAP && policies.size() == 1 && policies.get(0).getSubject().equals(requesterUrn)) {
      canByPassAccessCheck = _lssSharingDB.getSubjectPolicy(requesterUrn, policyType.name(), resourceUrn)
          .map(subjectPolicy -> Boolean.TRUE)
          .recoverWith(t -> {
            if (ExceptionUtils.isEntityNotFoundException(t)) {
              return Task.value(Boolean.FALSE);
            }
            LOG.error("Fail to get the subject policy for requester {} and resource {}", requesterUrn, resourceUrn, t);
            return Task.failure(t);
          });
    }

    return canByPassAccessCheck.flatMap(canBypass -> canBypass
        ? Task.value(AccessDecision.ALLOWED)
        : _aclServiceDispatcher.checkAccessDecision(requesterUrn, policyType, resourceUrn, AccessAction.ADMIN));
  }
}
