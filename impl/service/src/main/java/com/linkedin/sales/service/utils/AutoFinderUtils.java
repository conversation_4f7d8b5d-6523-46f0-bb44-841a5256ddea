package com.linkedin.sales.service.utils;

import com.linkedin.common.MemberUrnArray;
import com.linkedin.common.urn.MemberUrn;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


public final class AutoFinderUtils {

  private AutoFinderUtils() {

  }
  public static List<Long> getMemberIds(List<MemberUrn> memberUrns) {
    return Optional.ofNullable(memberUrns)
        .map(mids -> mids.stream().map(MemberUrn::getMemberIdEntity)
            .distinct().collect(Collectors.toList()))
        .orElseGet(Collections::emptyList);
  }

  public static MemberUrnArray getMemberUrnArray(List<Long> memberIds) {
    return Optional.ofNullable(memberIds)
        .map(mids -> mids.stream().distinct()
            .map(MemberUrn::new).collect(Collectors.toList()))
        .map(MemberUrnArray::new)
        .orElseGet(MemberUrnArray::new);
  }
}
