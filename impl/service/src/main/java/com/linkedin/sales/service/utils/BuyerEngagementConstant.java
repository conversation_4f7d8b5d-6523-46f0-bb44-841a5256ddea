package com.linkedin.sales.service.utils;

public final class BuyerEngagementConstant {

  // Pinot Clusters
  public static final String BUYER_ENGAGEMENT_PINOT_CLUSTER = "buyerInsightsPinotQuery";

  // Pinot Table
  public static final String BUYER_ENGAGEMENT_METRICS_TABLE = "BuyerEngagementMetrics";

  // Segment Facet
  public static final String ACTOR_SENIORITY_ID = "actorSeniorityId";
  public static final String ACTOR_FUNCTION_ID = "actorFunctionId";
  public static final String ACTOR_GEO_ID = "actorGeoId";
  public static final String ACTOR_ORGANIZATION_ID = "actorOrganizationId";
  public static final String TARGET_ORGANIZATION_ID = "targetOrganizationId";
  public static final String SIGNAL_TYPE = "signalType";
  public static final String METRIC_TYPE = "metricType";
  public static final String DAYS_SINCE_EPOCH = "daysSinceEpoch";
  public static final String ENGAGEMENT_COUNT = "engagementCount";

  private BuyerEngagementConstant() {
    // do nothing
  }
}
