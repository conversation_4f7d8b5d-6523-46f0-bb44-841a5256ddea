package com.linkedin.sales.service.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.linkedin.sales.service.utils.UrnUtils.*;

public class FlagshipLeadSharedUpdateMetadata {
  // urn:li:save:(urn:li:member:314833386,true)
  // urn:li:save:(urn:li:member:314833386,true,false)
  // urn:li:uniqueSuffix:(urn:li:save:(urn:li:member:1267253012,false),5gcDjuC0Q3CpVdENVArBdw)
  // urn:li:uniqueSuffix:(urn:li:save:(urn:li:member:1267253012,false,false),5gcDjuC0Q3CpVdENVArBdw)
  private static final Pattern LEAD_FS_SHARED_UPDATE_GROUP_BY_URN_PATTERN = Pattern.compile("\\b(true|false)\\b");

  // Intentionally default both fields to true, as it's safer to assume the lead is saved and the user is a SN member
  // rather than show the unsaved state or upsell wrongly
  private boolean isLeadSavedByUser = true;
  private boolean isUserSalesNavigatorMember = true;

  public FlagshipLeadSharedUpdateMetadata(String urnStr) {
    if (isNoneUrn(urnStr)) {
      return;
    }

    Matcher matcher = LEAD_FS_SHARED_UPDATE_GROUP_BY_URN_PATTERN.matcher(urnStr);
    List<Boolean> matches = new ArrayList<>();

    while (matcher.find()) {
      matches.add(Boolean.parseBoolean(matcher.group()));
    }

    switch (matches.size()) {
      case 2:
        this.isLeadSavedByUser = matches.get(0);
        this.isUserSalesNavigatorMember = matches.get(1);
        break;
      case 1:
        this.isLeadSavedByUser = matches.get(0);
        break;
      default:
        break;
    }
  }

  public boolean isLeadSavedByUser() {
    return isLeadSavedByUser;
  }

  public boolean isUserSalesNavigatorMember() {
    return isUserSalesNavigatorMember;
  }
}
