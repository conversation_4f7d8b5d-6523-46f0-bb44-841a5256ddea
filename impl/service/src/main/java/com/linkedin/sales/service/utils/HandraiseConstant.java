package com.linkedin.sales.service.utils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;

/**
 * Constant file that stores the data mapping of the Handraise-to-admins pilot list.
 * We chose to store it here because:
 * 1. LIX doesn't suppot storing maps.
 * 2. It might not be worth it to store in an external db for an experiment.
 */
public final class HandraiseConstant {

  private HandraiseConstant() {
    // do nothing
  }

  /**
   * Map that stores mapping from a companyId that is on the Handraise pilot list
   * to a list of tier3(Advanced Plus) contractIds of that company
   */
  public static final Map<Long, List<Long>> COMPANY_TO_CONTRACT_MAP = ImmutableMap.<Long, List<Long>>builder()
      .put(1009L, ImmutableList.of(660872510L))
      .put(1337L, ImmutableList.of(1067988907L))
      .put(1116L, ImmutableList.of(444729909L))
      .put(1720L, ImmutableList.of(584581405L))
      .put(1855L, ImmutableList.of(35555105L))
      .put(3335L, ImmutableList.of(594840907L))
      .put(9089L, ImmutableList.of(558618905L))
      .put(13256L, ImmutableList.of(624899802L))
      .put(58360L, ImmutableList.of(521514205L))
      .put(157303L, ImmutableList.of(166923705L))
      .put(157329L, ImmutableList.of(433473505L))
      .put(164245L, ImmutableList.of(643331307L))
      .put(166000L, ImmutableList.of(35558305L))
      .put(3223482L, ImmutableList.of(251681505L))
      .put(5152939L, ImmutableList.of(600717202L))
      .put(24015L, ImmutableList.of(265226905L))
      .put(11257500L, ImmutableList.of(302636109L))
      .put(2775L, ImmutableList.of(456726105L))
      .put(3077431L, ImmutableList.of(267219405L))
      .put(5208813L, ImmutableList.of(937440507L))
      .put(2532L, ImmutableList.of(536220407L))
      .put(1767L, ImmutableList.of(833014105L, 842054902L))
      .put(4060L, ImmutableList.of(37204805L, 169367405L))
      .put(1038L, ImmutableList.of(288951205L, 541887009L, 559619902L, 659198902L, 682043807L))
      .put(3178L, ImmutableList.of(536396109L, 601142305L, 897275307L, 926124907L))
      .put(1068L, ImmutableList.of(32229604L, 153646905L, 263065907L, 291778902L, 570771205L, 766051207L))
      .put(1441L, ImmutableList.of(20970604L, 30689404L, 35571105L, 63807005L, 385907502L, 698443107L, 801981807L))
      .put(1307L, ImmutableList.of(448597105L, 500575409L, 593297807L, 721452102L, 892559405L, 915580802L, 1022507907L, 1023498107L))
      .put(15088102L, ImmutableList.of(237175007L, 421485002L, 547208202L, 599750102L, 602301602L, 602432402L, 773010302L, 786783205L))
      .put(1028L, ImmutableList.of(35556305L, 39743305L, 97244005L, 314773402L, 372330309L, 468400505L, 627794502L, 658160502L, 880655302L))
      .put(2988L, ImmutableList.of(541064809L, 735491802L, 792163607L, 1022828505L))
      .put(1043L, ImmutableList.of(63129205L, 65980605L, 287256502L, 436271407L, 456183307L, 523792107L, 566302805L, 580229805L, 587472102L,
          689862105L, 740113202L, 764627607L, 766820307L, 883273807L, 883276307L, 883278607L, 883280207L, 918155805L, 948750405L))
      .put(1063L, ImmutableList.of(26407704L, 34209104L, 35571005L, 46888005L, 386096805L, 423068205L, 470305905L, 499909209L, 511379807L,
          513084905L, 546458607L, 585346402L, 585873702L, 618009102L, 632771102L, 722773902L, 848177302L, 902515907L, 933035602L, 961484607L))
      .put(1277L, ImmutableList.of(20183904L, 26723704L, 267941902L, 365050602L, 405041805L, 418559802L, 435322905L, 439747207L, 441412002L,
          450086105L, 513406309L, 737670405L, 784317307L, 828019407L, 837789405L, 958163007L))
      .put(157240L, ImmutableList.of(28823204L, 34229604L, 50986405L, 349408709L, 349409209L, 349410009L, 349411209L, 351227609L, 377549809L, 392361805L,
          417982105L, 465087005L, 480080505L, 507165309L, 527530005L, 633940202L, 674520602L, 784897907L, 900674402L, 907350902L, 907352102L, 909828502L))
      .put(5390798L, ImmutableList.of(415983809L, 415975109L, 415971109L))
      .build();
}
