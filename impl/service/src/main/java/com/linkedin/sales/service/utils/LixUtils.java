package com.linkedin.sales.service.utils;

public final class LixUtils {
  private LixUtils() {
  }

  public static final String ENABLED = "enabled";
  public static final String CONTROL = "control";
  public static final String CONTRACT_ID_NUM_PROPERTY = "contractId";
  public static final String CRM_INSTANCE_ID_STRING_PROPERTY = "crmInstanceId";
  public static final String IS_ADMIN_BOOLEAN_PROPERTY = "isAdmin";
  public static final String ENTERPRISE_APPLICATION_INSTANCE = "applicationInstance";
  public static final String ENTERPRISE_ACCOUNT = "account";

  // Default value for entity batch create concurrency level
  public static final int DEFAULT_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL = 5;

  /**
   * Disable displaying Usage reporting data in snap api
   */
  public static final String LSS_DISABLE_USAGE_REPORT = "lss-disable-usage-report";

  /**
   * Lix to get the batch size of the entities that need to be deleted sequentially.
   */
  public static final String LIX_SALES_ENTITIES_DELETION_BATCH_SIZE = "lss-sales-entities-deletion-batch-size";

  /**
   * Lix to get the batch size of the entities that need to be created sequentially.
   */
  public static final String LIX_SALES_ENTITIES_CREATION_BATCH_SIZE = "lss-sales-entities-creation-batch-size";

  /**
   * Set the concurrency level of Espresso multi-put request tasks for batch creating saved leads/ accounts/ associations
   */
  public static final String LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL = "lss-saved-lead-account-batch-create-concurrency-level";

  /**
   * Set the concurrency level of Espresso multi-put request tasks for batch creating lsit entities
   */
  public static final String LSS_LIST_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL = "lss-list-entity-batch-create-concurrency-level";
   /*
   * Determine if lead account association update enabled.
   */
  public static final String LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE = "sales_lead_account_association_update";

  /**
   * Use a shorter TTL for Sales Access Token for testing when lix is enabled. The lix is against Developer Application ID
   */
  public static final String LSS_SAT_TTL_SHORT = "lss-sat-ttl-short";

  /**
   * Lix key to allow reading buyer interest C2C scores generated from the latest DL Model. This key is for the new DL model A/B test.
   * eg, "lss.buyer-interest.dl_model_v2"
   */
  public static final String LSS_BUYER_INTEREST_DL_MODEL_TEST = "";

  /**
   * Lix to determine if http status translation should be handled by lss-mt.
   */
  public static final String LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS = "lss-search.pinned-filters.http-status";

  /**
   * Lix to enable sales intelligence filters for lead search - contract based
   */
  public static final String LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT = "lss.search.sales-intelligence-filters.contract";

  /**
   * Lix to enable sales intelligence filters for lead search - member based
   */
  public static final String LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER = "lss.search.sales-intelligence-filters.member";

  /**
   * Lix to enable filtering ambry files for last 1 day
   */
  public static final String LSS_CRM_DV_FILTER_FILES_FOR_LAST_ONE_DAY = "lss-crm-dv-filter-files-for-last-one-day";

  /**
   * Lix to enable fetching recommendations from Sales People You May Know venice store
   */
  public static final String LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION = "lss-people-you-may-know-recommendation";

  /**
   * Lix for the relationship map - list view MVP
   */
  public static final String LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_LIST_VIEW = "lss.pages.account-page.relationship-map-list-view";

  /**
   * Lix for the relationship map - map view MVP
   */
  public static final String LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_MAP_VIEW = "lss.pages.account-page.relationship-map-map-view";

  /**
   * Lix used by the premium team to determine if we should turn off a members premium entitlements. Used for testing.
   * If enabled, a user should be considered as only having basic entitlements (no premium).
   */
  public static final String PREMIUM_ENTITLEMENTS_TOGGLE_LIX = "voyager.premium.api.premium-toggle";

  /**
   * Lix to enable read on PlaceholderCard change log for Relationship Map
   */
  public static final String LSS_RELATIONSHIP_MAP_ENABLE_PLACEHOLDER_CARD_CHANGE_LOG =
      "lss.relationship-map.enable.placeholder-card.change-log-eep";

  /**
   * Lix to enable migrating salesAnalyticsExportJob to lss-reporting
   * This is part of cap migration to migrate smart queues to quantum
   */
  public static final String LSS_USAGE_REPORTING_MIGRATE_DATA_AVAILABILITY_TO_LSS_REPORTING =
      "lss.usage-reporting-migrate-data-availability-to-lss-reporting";

  /**
   * Lix to enable seat managed products setting
   */
  public static final String LSS_SEAT_MANAGED_PRODUCTS_ENABLED = "lss.seat-managed-products-enabled";

  /**
   * Lix for enabling admin product collection
   */
  public static final String LSS_ADMIN_PRODUCT_COLLECTION = "lss.admin.products-and-services";

  /**
   * Lix for modifying batchSize and delay of seat transfer entity creation
   */
  public static final String LSS_PAGES_SEAT_TRANSFER_BATCH_SIZE_AND_DELAY = "lss.pages.seat-transfer-batch-size-and-delay";

  /**
   * Lix to enable to admin to upload book of business lists for all sellers
   */
  public static final String LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN = "lss.pages.enable-book-of-business-list-upload-by-admin";

  /**
   * Lix to switch to use AccountPlaysMetadataV2 table
   */
  public static final String LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE = "lss.search.switch-to-account-plays-metadata-v2-table";


  /**
   * Helper to determine if lix treatment is enabled (not control)
   * @param treatment Treatment to test
   * @return true if treatment is enabled, false otherwise
   */
  public static Boolean isEnabled(String treatment) {
    return !CONTROL.equals(treatment);
  }
}
