package com.linkedin.sales.service.utils;

import com.linkedin.common.LocaleUtil;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.Locale;

public final class LocaleUtils {
  private LocaleUtils() {
  }

  /**
   * Convert the com.linkedin.common.Locale to java.util.Locale. Fall back to Local.US if the locale passed in is null or
   * incomplete.
   */
  @NonNull
  public static Locale getJavaLocaleOrDefault(@Nullable com.linkedin.common.Locale locale) {
    if (locale == null || !locale.hasCountry() || !locale.hasLanguage()) {
      return Locale.US;
    }
    return LocaleUtil.toJavaLocale(locale);
  }
}
