package com.linkedin.sales.service.utils;

import com.linkedin.parseq.Engine;
import com.linkedin.parseq.Task;
import java.util.List;
import java.util.concurrent.TimeUnit;


public final class ParseqUtil {

  private static final int DEFAULT_MINUTES = 3;


  private ParseqUtil() {
  }

  /**
   * await given task indefinitely
   * @param task the parseq Task need to be resolved
   * @param parseqEngine the engine to run the task
   * @param <T> an applicable return type of {@link Task}, better to be java bean
   * @return value of the task
   */
  public static <T> T await(Task<T> task, Engine parseqEngine) {
    parseqEngine.run(task);
    try {
      task.await();
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    return task.get();
  }
  /**
   * blocking wait a parseqTask to get it's result till timeout
   *
   * @param task the parseq Task need to be resolved
   * @param parseqEngine the engine to run the task
   * @param timeout the value of maximum waiting time
   * @param timeoutUnit the value of maximum waiting time unit
   * @param <T> an applicable return type of {@link Task}, better to be java bean
   * @return value of the task
   * @throws RuntimeException with {@link InterruptedException} as it's cause
   */
  public static <T> T await(Task<T> task, Engine parseqEngine, int timeout, TimeUnit timeoutUnit) {
    parseqEngine.run(task);
    try {
      task.await(timeout, timeoutUnit);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    return task.get();
  }
  public static <T> T awaitByDefaultTimeout(Task<T> task, Engine parseqEngine) {
    return await(task, parseqEngine, DEFAULT_MINUTES, TimeUnit.MINUTES);
  }

  public static <T> List<T> par(Iterable<Task<T>> tasks, Engine parseqEngine) {
    return await(Task.par(tasks), parseqEngine);
  }

  public static <T> List<T> par(Iterable<Task<T>> tasks, Engine parseqEngine, int timeout, TimeUnit timeoutUnit) {
    return await(Task.par(tasks), parseqEngine, timeout, timeoutUnit);
  }



}
