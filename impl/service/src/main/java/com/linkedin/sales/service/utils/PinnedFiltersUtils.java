package com.linkedin.sales.service.utils;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.GetMode;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.salescustomfilterview.PinnedFilters;
import com.linkedin.salescustomfilterview.SearchType;
import com.linkedin.util.Pair;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Nonnull;


/**
 * Utils class for pinned filters related functionality.
 */
public final class PinnedFiltersUtils {
  private PinnedFiltersUtils() { }

  private static final List<String> DEFAULT_LEAD_PINNED_FILTERS = ImmutableList.of("RELATIONSHIP", "GEOGRAPHY", "INDUSTRY",
      "YEARS_OF_EXPERIENCE", "COMPANY_HEADCOUNT", "CURRENT_TITLE", "PAST_TITLE", "LEAD_HIGHLIGHTS", "LEADS_IN_CRM",
      "LEAD_LIST");

  // List of default pinned filters of the new Sales Intelligence (SI) filter layout for lead search
  private static final List<String> DEFAULT_LEAD_PINNED_FILTERS_SI = ImmutableList.of("CURRENT_COMPANY", "COMPANY_HEADCOUNT",
      "SENIORITY_LEVEL", "FUNCTION", "CURRENT_TITLE", "GEOGRAPHY", "INDUSTRY", "BUYER_INTENT_RELATION", "RELATIONSHIP",
      "RECENTLY_CHANGED_JOBS", "POSTED_ON_LINKEDIN");

  private static final List<String> DEFAULT_ACCOUNT_PINNED_FILTERS = ImmutableList.of("ANNUAL_REVENUE", "COMPANY_HEADCOUNT",
      "COMPANY_HEADCOUNT_GROWTH", "HEADQUARTERS_LOCATION", "INDUSTRY", "NUM_OF_FOLLOWERS", "JOB_OPPORTUNITIES",
      "ACCOUNT_ACTIVITIES", "RELATIONSHIP", "ACCOUNTS_IN_CRM", "SAVED_ACCOUNTS", "ACCOUNT_LIST");

  // This list was obtained by the product team, this is a list of the most used filters on mobile
  private static final List<String> DEFAULT_LEAD_MOBILE_PINNED_FILTERS = ImmutableList.of("GEOGRAPHY", "CURRENT_COMPANY",
      "SENIORITY_LEVEL", "CURRENT_TITLE");

  // This list was obtained by the product team, those are the most used filters on mobile
  private static final List<String> DEFAULT_ACCOUNT_MOBILE_PINNED_FILTERS = ImmutableList.of("INDUSTRY",
      "COMPANY_HEADCOUNT");

  /**
   * Maps any NEW filter to the OLD filter(s) it replaced. This map will be used in the following way
   *  - Grab all values associated with the key if found (the key being the new filter we are pinning/unpinning)
   *  - Iterate through the list of filters passed in (from db GET call)
   *  - If any of the old filter values are found, remove them and replace them with the "new" filter value. This ensures
   *  any operations on newly-adding filters are respected and old filter values won't persist.
   * Populate this map with new to old filters. The key should be the "forward" filter and the value should be the past
   * filter values.
   * Eg: Key: PAST_TITLE, Value: [TITLE]
   */
  private static final Map<String, List<String>> FORWARDS_COMPATIBILITY_FILTER_MAP =
      new ImmutableMap.Builder<String, List<String>>()
          .build();

  /**
   * Maps any OLD filter to the NEW filter(s) it replaced. This map will be used in the following way
   *  - Grab all values associated with the key if found (the key being the old filter we are pinning/unpinning)
   *  - Iterate through the list of filters passed in (from db GET call)
   *  - If any of the new filter values are found, remove them and replace them with the "old" filter value. This ensures
   *  any operations on passed in filter (being an older filter) are respected and new filter values won't persist.
   * Populate this map with old to new filters. The key should be the "backwards" filter and the value should be the new
   * filter values.
   * Eg: Key: TITLE, Value: [PAST_TITLE, CURRENT_TITLE]
   */
  private static final Map<String, List<String>> BACKWARDS_COMPATIBILITY_FILTER_MAP =
      new ImmutableMap.Builder<String, List<String>>()
          .build();

  @VisibleForTesting
  static Map<String, List<String>> getForwardsCompatibilityFilterMap() {
    return FORWARDS_COMPATIBILITY_FILTER_MAP;
  }

  @VisibleForTesting
  static Map<String, List<String>> getBackwardsCompatibilityFilterMap() {
    return BACKWARDS_COMPATIBILITY_FILTER_MAP;
  }

  /**
   * If there is forwards/backwards compatibility issues with the pinnedFilter, update the map to have the necessary filter.
   * For example, if the pinnedFilters contains an old filter A and its newer replacement, let's say filter B,
   * exists in the current pinned filter in DB, we will remove B and store A in the returned set.
   *
   * @param filterList list of filters that exist in the DB
   * @param pinnedFilters the list of filters to be updated in the DB
   */
  @SuppressFBWarnings(value = "MUI", justification = "containsKey check is paired with another map check")
  public static Set<String> adjustFilterListForCompatibility(@Nonnull List<String> filterList, @Nonnull List<String> pinnedFilters) {
    Map<String, List<String>> backwardsCompatibilityMap = getBackwardsCompatibilityFilterMap();
    Map<String, List<String>> forwardsCompatibilityMap = getForwardsCompatibilityFilterMap();
    List<Set<String>> filtersToRemoveList = new ArrayList<>(pinnedFilters.size());
    for (String pinnedFilter : pinnedFilters) {
      Set<String> filtersToRemove;
      if (forwardsCompatibilityMap.containsKey(pinnedFilter)
          && !backwardsCompatibilityMap.containsKey(pinnedFilter)) { // Forwards compatibility
        filtersToRemove = new HashSet<>(forwardsCompatibilityMap.get(pinnedFilter));
      } else if (backwardsCompatibilityMap.containsKey(pinnedFilter)
          && !forwardsCompatibilityMap.containsKey(pinnedFilter)) { // Backwards compatibility
        filtersToRemove = new HashSet<>(backwardsCompatibilityMap.get(pinnedFilter));
      } else if (backwardsCompatibilityMap.containsKey(pinnedFilter)
          && forwardsCompatibilityMap.containsKey(pinnedFilter)) { // Error state
        throw new IllegalStateException(
            String.format("Both forwards and backwards compatibility maps contain %s", pinnedFilter));
      } else {
        filtersToRemove = new HashSet<>(); // No-op
      }
      filtersToRemoveList.add(filtersToRemove);
    }
    Set<String> compatibilityFilterSet = new HashSet<>(filterList);
    if (filtersToRemoveList.isEmpty()) {
      return compatibilityFilterSet;
    }
    // Iterates through every filter with an action, and its corresponding "compatibility" map.
    for (int i = 0; i < pinnedFilters.size(); i++) {
      boolean filterListModification = false;
      for (String filter : filterList) {
        if (filtersToRemoveList.get(i).contains(filter)) {
          filterListModification = true;
          compatibilityFilterSet.remove(filter);
        }
      }
      if (filterListModification) {
        compatibilityFilterSet.add(pinnedFilters.get(i));
      }
    }
    return compatibilityFilterSet;
  }

  /**
   * Gets a default list of filters for LEAD and ACCOUNT searchTypes.
   * @param searchType {@link SearchType}
   * @param isSearchSalesIntelligenceEnabled whether Sales Intelligence search filter layout lix is enabled
   * @return default filter list.
   */
  @Nonnull
  public static List<String> getDefaultPinnedFilters(SearchType searchType, boolean isSearchSalesIntelligenceEnabled) {
    switch (searchType) {
      case LEAD:
        if (isSearchSalesIntelligenceEnabled) {
          return DEFAULT_LEAD_PINNED_FILTERS_SI;
        } else {
          return DEFAULT_LEAD_PINNED_FILTERS;
        }
      case ACCOUNT:
        return DEFAULT_ACCOUNT_PINNED_FILTERS;
      case MOBILE_LEAD:
        return DEFAULT_LEAD_MOBILE_PINNED_FILTERS;
      case MOBILE_ACCOUNT:
        return DEFAULT_ACCOUNT_MOBILE_PINNED_FILTERS;
      default:
        throw new IllegalStateException(String.format("SearchType: %s not recognized", searchType));
    }
  }

  /**
   * Translates a {@link PatchRequest} into two lists of filters pinning action.
   * @param patchRequest {@link PatchRequest}
   * @return a Pair containing two lists of strings. The first element in the pair contains the pinned
   * filters to "set" (pin), and the second element contains the pinned filters to "unset" (unpin).
   */
  @Nonnull
  public static Pair<List<String>, List<String>> translatePatchDocument(PatchRequest<PinnedFilters> patchRequest) {
    List<List<String>> filterList = new ArrayList<>();
    for (String dataMapKey : ImmutableList.of("$set", "$unset")) {
      DataMap dataMap = patchRequest.getPatchDocument().getDataMap(dataMapKey);
      List<String> filters = new ArrayList<>();
      if (dataMap != null) {
        List<String> actionFilters = new PinnedFilters(dataMap).getFilters(GetMode.NULL);
        if (actionFilters != null) {
          filters = new ArrayList<>(actionFilters);
        }
      }
      filterList.add(filters);
    }
    return new Pair<>(filterList.get(0), filterList.get(1));
  }
}
