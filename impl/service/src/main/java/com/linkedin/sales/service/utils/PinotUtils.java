package com.linkedin.sales.service.utils;

import com.google.common.base.Preconditions;
import com.linkedin.analytics.QueryRequest;
import com.linkedin.analytics.QueryResponse;
import com.linkedin.ibrik.client.AnalyticsQueryClient;
import com.linkedin.parseq.Task;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;


public class PinotUtils {

  private final AnalyticsQueryClient _analyticsQueryClient;
  private final long _timeOutInSeconds;
  private final ExecutorService _executorService;

  public PinotUtils(AnalyticsQueryClient analyticsQueryClient,
      ExecutorService executorService, long timeOutInSeconds) {
    _analyticsQueryClient = analyticsQueryClient;
    _executorService = executorService;
    _timeOutInSeconds = timeOutInSeconds;
  }

  /**
   * Create a Parseq Task fetching query response from Pinot
   * @param resourceName The name of Pinot resource from which we fetch data
   * @param sql The SQL query we make against Pinot DB
   * @return QueryResponse as the fetched data from Pinot
   */
  public Task<QueryResponse> runPinotQuery(String resourceName, String sql) {
    Preconditions.checkNotNull(resourceName, "resourceName cannot be null!");
    Preconditions.checkNotNull(sql, "query cannot be null!");
    QueryRequest request = new QueryRequest();
    request.setQuery(sql);
    return Task.blocking(() -> _analyticsQueryClient.runPinotQueryWithTimeOut(request, resourceName, _timeOutInSeconds,
        TimeUnit.SECONDS), _executorService);
  }
}
