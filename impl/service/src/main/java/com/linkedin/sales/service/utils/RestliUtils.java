package com.linkedin.sales.service.utils;

import com.google.common.collect.ImmutableMap;
import com.linkedin.data.ByteString;
import com.linkedin.data.DataMap;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.RestConstants;
import com.linkedin.restli.internal.common.DataMapConverter;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.annotations.Key;
import com.linkedin.restli.server.annotations.RestLiAssociation;
import com.linkedin.restli.server.resources.AssociationResourceTask;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import javax.activation.MimeTypeParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Utils that can take general methods that all services share
 * <AUTHOR>
 */
public final class RestliUtils {
  private static final Logger LOG = LoggerFactory.getLogger(RestliUtils.class);

  private RestliUtils() {
    // Do nothing
  }

  /**
   * Helper method for reading the CompoundKey schema from an Association resource.  This parses the annotations since
   * the relevant methods in rest.li are buried pretty deeply there.
   *
   * @param resourceClass Class extending AssociationResource
   * @return ImmutableMap suitable for CompoundKey construction
   */
  public static ImmutableMap<String, CompoundKey.TypeInfo> parseCompoundKeyAnnotation(Class<? extends AssociationResourceTask> resourceClass) {
    ImmutableMap.Builder<String, CompoundKey.TypeInfo> keySchemaMapBuilder = ImmutableMap.builder();
    RestLiAssociation annotation = resourceClass.getAnnotation(RestLiAssociation.class);

    for (Key key : annotation.assocKeys()) {
      Class<?> typeClass = key.type();
      /*
       * the typeRef is needed for non primitives to be able to coerce the type into a primitive
       * Example from TscpSasbeAdAccountUserV2Resource
       *  @Key(name = "user", type = Urn.class, typeref = com.linkedin.common.Urn.class)
       *  Urn.class is not a primitive, so typeref is needed to indicate what the primitive type ultimately is
       */
      Class<?> typerefClass = key.typeref();
      CompoundKey.TypeInfo typeInfo = new CompoundKey.TypeInfo(typeClass, typerefClass);
      keySchemaMapBuilder.put(key.name(), typeInfo);
    }

    return keySchemaMapBuilder.build();
  }

  /**
   * Helper method for deserializing a JSON string to DataMap
   * @param json json string
   * @return DataMap
   */
  public static DataMap deserializeToDataMap(String json) {
    // Usage of UTF-8 to make sure we deserialize international characters correctly instead of using a default charset
    ByteString byteString = ByteString.copyString(json, StandardCharsets.UTF_8);

    try {
      return DataMapConverter.bytesToDataMap(RestConstants.HEADER_VALUE_APPLICATION_JSON, byteString);
    } catch (MimeTypeParseException | IOException e) {
      String errMsg = String.format("Could not convert json %s to DataMap", json);
      throw new RuntimeException(errMsg, e);
    }
  }

  /**
   * Helper method for create CreateResponse with datamap in error
   * @param compoundKey compoundkey
   * @param createResponse create response
   * @param resourceClass resource class
   * @return CreateResponse
   */
  public static CreateResponse createCreateResponseWithDatamapInError(@NonNull CompoundKey compoundKey,
      @NonNull CreateResponse createResponse, Class<? extends AssociationResourceTask> resourceClass) {

    if (createResponse.hasError()) {
      RestLiServiceException exception = createResponse.getError();
      exception.setErrorDetails(compoundKey.toDataMap(RestliUtils.parseCompoundKeyAnnotation(resourceClass)));
      return new CreateResponse(exception);
    } else {
      return createResponse;
    }
  }
}
