package com.linkedin.sales.service.utils;

import com.google.common.base.Preconditions;
import com.linkedin.analytics.ColumnInfoArray;
import com.linkedin.analytics.ColumnType;
import com.linkedin.analytics.QueryResponse;
import com.linkedin.analytics.Row;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.parseq.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xeril.util.Utils;


public class SalesAnalyticsPinotUtils {

  private static final Logger LOG = LoggerFactory.getLogger(SalesAnalyticsPinotUtils.class);
  private static final String SNAP_PINOT_QUERY_RESOURCE = "snapPinotQuery";
  private static final String MAX_TIMESTAMP_COLUMN_NAME_SQL = "MAX(timestamp)";
  private static final String MIN_TIMESTAMP_COLUMN_NAME_SQL = "MIN(timestamp)";

  private PinotUtils _pinotUtils;

  private static final String GET_MAX_TIMESTAMP_QUERY_SQL = "SELECT MAX(\"timestamp\") FROM %s WHERE contract_id = %s";

  private static final String GET_MIN_TIMESTAMP_QUERY_SQL = "SELECT MIN(\"timestamp\") FROM %s WHERE contract_id = %s";

  public SalesAnalyticsPinotUtils(PinotUtils pinotUtils) {
    _pinotUtils = pinotUtils;
  }

  public SalesAnalyticsPinotUtils() { }

  /**
   * Get latest data availability for a contract in a table
   * @param table The table's name; there are three tables in total for now: snapActivity, snapActivityOutcome, snapSeat
   * @param contractUrn The ContractUrn of the contract
   * @return A timestamp as the latest data availability given the table and the contract
   */
  public Task<Long> retrieveLatestDataAvailability(String table, ContractUrn contractUrn) {
      String formattedQuery = String.format(GET_MAX_TIMESTAMP_QUERY_SQL, table, contractUrn.getContractIdEntity());
      return _pinotUtils.runPinotQuery(SNAP_PINOT_QUERY_RESOURCE, formattedQuery)
          .map(result -> retrieveLongFromQueryResponse(result, MAX_TIMESTAMP_COLUMN_NAME_SQL));
  }

  /**
   * Get earliest data availability for a contract in a table
   * @param table The table's name; there are three tables in total for now: snapActivity, snapActivityOutcome, snapSeat
   * @param contractUrn The ContractUrn of the contract
   * @return A timestamp as the earliest data availability given the table and the contract
   */
  public Task<Long> retrieveEarliestDataAvailability(String table, ContractUrn contractUrn) {
    String formattedQuery = String.format(GET_MIN_TIMESTAMP_QUERY_SQL, table, contractUrn.getContractIdEntity());
    return _pinotUtils.runPinotQuery(SNAP_PINOT_QUERY_RESOURCE, formattedQuery)
        .map(result -> retrieveLongFromQueryResponse(result, MIN_TIMESTAMP_COLUMN_NAME_SQL));
  }

  /**
   * Retrieve a Long type variable from a QueryResponse
   * @param queryResponse Response from a query
   * @param colName The name of the column to which the Long type variable belongs
   * @return A Long type variable
   */
  protected Long retrieveLongFromQueryResponse(QueryResponse queryResponse, String colName) {
    Preconditions.checkNotNull(queryResponse, "Argument queryResponse cannot be null");
    Preconditions.checkNotNull(colName, "Argument colName cannot be null");
    Preconditions.checkNotNull(queryResponse.getQueryMetadata(GetMode.NULL),
        "Argument query response metadata cannot be null");

    ColumnInfoArray columns = queryResponse.getQueryMetadata().getSchema(GetMode.NULL);
    if (Utils.isEmptyCollection(queryResponse.getRows(GetMode.NULL)) || Utils.isEmptyCollection(columns)) {
      LOG.error("Empty row or column in query response: {} {}", queryResponse.getRows(), columns);
      return 0L;
    }

    if (colName.equalsIgnoreCase(columns.get(0).getColumnName()) && columns.get(0).getColumnType() == ColumnType.DOUBLE) {
      Row.Value value = queryResponse.getRows().get(0).getValue().get(0);
      Double result = value.getDouble();
      // If the contract does not exist, the variables under columns min(timestamp) and max(timestamp)
      // are Infinity and -Infinity, respectively
      if (result == Double.POSITIVE_INFINITY || result == Double.NEGATIVE_INFINITY) {
        result = 0.0;
      }
      return result.longValue();
    } else {
      LOG.error("Unexpected column type found: {}", columns.get(0));
    }

    return 0L;
  }
}
