package com.linkedin.sales.service.utils;

import com.google.common.collect.Lists;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.lss.ParseqUtils;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.service.LixService;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.AbstractMap;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static com.linkedin.sales.service.utils.LixUtils.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;


/**
 * Utils for Saved Leads, Accounts, Associations and List Entity batch operations.
 */
public final class SalesEntitiesBatchUtils {
  private static final Logger LOG = LoggerFactory.getLogger(SalesEntitiesBatchUtils.class);
  private static final int DEFAULT_BATCH_SIZE_FOR_BULK_OPERATION = 10;

  private SalesEntitiesBatchUtils() {
  }

  /**
   * The given entities are partitioned into batches based on batchSizeForBulkAction
   * (10 by default). The batches of entities are deleted in parallel and the entities within each batch are deleted
   * sequentially. This is to reduce the degree of concurrency writes to the same seat, which alleviates the locking
   * issue on the materialized aggregates views(if any).
   *
   * @return compoundKey to Response map.
   */
  public static Task<Map<CompoundKey, UpdateResponse>> batchDeleteEntities(@NonNull Set<CompoundKey> compoundKeys,
      Function<CompoundKey, Task<AbstractMap.SimpleEntry<CompoundKey, UpdateResponse>>> actionPerformer,
      LixService lixService) {

    return batchDeleteEntities(compoundKeys, actionPerformer,
        getBatchSizeForBulkAction(getSeatUrn(compoundKeys), LixUtils.LIX_SALES_ENTITIES_DELETION_BATCH_SIZE,
            DEFAULT_BATCH_SIZE_FOR_BULK_OPERATION, lixService));
  }

  /**
   * The given entities are partitioned into batches based on batchSizeForBulkAction
   * (10 by default). The batches of entities are deleted in parallel and the entities within each batch are deleted
   * sequentially. This is to reduce the degree of concurrency writes to the same seat, which alleviates the locking
   * issue on the materialized aggregates views(if any).
   * @param seatUrn seatUrn
   * @param compoundKeys compoundKeys
   * @return compoundKey to Response map.
   */
  public static Task<Map<CompoundKey, UpdateResponse>> batchDeleteEntities(@NonNull SeatUrn seatUrn,
      @NonNull Set<CompoundKey> compoundKeys,
      Function<CompoundKey, Task<AbstractMap.SimpleEntry<CompoundKey, UpdateResponse>>> actionPerformer,
      LixService lixService) {

    return batchDeleteEntities(compoundKeys, actionPerformer,
        getBatchSizeForBulkAction(seatUrn, LixUtils.LIX_SALES_ENTITIES_DELETION_BATCH_SIZE,
            DEFAULT_BATCH_SIZE_FOR_BULK_OPERATION, lixService));
  }

  /**
   * The given entities are partitioned into batches based on batchSizeForBulkAction
   * (10 by default). The batches of entities are deleted in parallel and the entities within each batch are deleted
   * sequentially. This is to reduce the degree of concurrency writes to the same seat, which alleviates the locking
   * issue on the materialized aggregates views(if any).
   *
   * @return compoundKey to Response map.
   */
  private static Task<Map<CompoundKey, UpdateResponse>> batchDeleteEntities(@NonNull Set<CompoundKey> compoundKeys,
      Function<CompoundKey, Task<AbstractMap.SimpleEntry<CompoundKey, UpdateResponse>>> actionPerformer,
      Task<Integer> batchSizeForBulkActionTask) {
    if (compoundKeys.isEmpty()) {
      return Task.value(Collections.emptyMap());
    }

    return batchSizeForBulkActionTask.flatMap(batchSizeForBulkAction -> {
      int levelOfConcurrency =
          compoundKeys.size() / batchSizeForBulkAction + (compoundKeys.size() % batchSizeForBulkAction == 0 ? 0 : 1);

      return ParseqUtils.parInSpecificConcurrency(Lists.newArrayList(compoundKeys), actionPerformer, levelOfConcurrency)
          .map(responseList -> responseList.stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    });
  }

  /**
   * Retrieves the SeatUrn from the compoundKey(s).
   */
  private static SeatUrn getSeatUrn(@NonNull Set<CompoundKey> compoundKeys) {
    for (CompoundKey ck : compoundKeys) {
      final Object creator = ck.getPart(CREATOR_COMPOUND_KEY);
      final Object owner = ck.getPart(OWNER_COMPOUND_KEY);
      if (creator != null || owner != null) {
        try {
          return SeatUrn.deserialize(creator != null ? creator.toString() : owner.toString());
        } catch (URISyntaxException ignored) {
          // continue to look for the seat urn in the other compoundKeys.
        }
      }
    }

    String message = "Failed to perform action on entity since seatUrn could not be found in the compoundKey";
    LOG.error(message);
    throw new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, message);
  }

  /**
   *
   */
  /**
   * Retrieve the batch size from the lix if available. If not, fallback to the default batch size.
   * @param seat searUrn.
   * @param lixName Name of the lix
   * @param defaultBatchSize default batch size for bulk action.
   * @param lixService
   * @return
   */
  public static Task<Integer> getBatchSizeForBulkAction(SeatUrn seat, String lixName, int defaultBatchSize,
      LixService lixService) {
    return lixService.getLixTreatment(seat, lixName, null).map(lixTreatment -> {
      int batchSizeForBulkAction = defaultBatchSize;
      if (!CONTROL.equals(lixTreatment)) {
        try {
          // Valid lix treatment is in the format of "batch_size_<value>", so getting the value from 11th character.
          batchSizeForBulkAction = Integer.parseInt(lixTreatment.substring(11));
        } catch (Exception e) {
          LOG.warn("Invalid lix value for {}. Falling back to the default value", lixName, e);
        }
      }
      return batchSizeForBulkAction;
    });
  }
}
