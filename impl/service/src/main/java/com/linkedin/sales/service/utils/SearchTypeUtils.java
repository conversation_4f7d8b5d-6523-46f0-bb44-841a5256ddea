package com.linkedin.sales.service.utils;

import com.google.common.collect.ImmutableSet;
import com.linkedin.salescustomfilterview.SearchType;
import java.util.Set;

/*
 * Class containing utility methods involving SearchType from
 *   com.linkedin.salescustomfilterview.SearchType.pdl
 */
public final class SearchTypeUtils {

  private final static Set<SearchType> MOBILE_SEARCH_TYPES = ImmutableSet.of(SearchType.MOBILE_ACCOUNT, SearchType.MOBILE_LEAD);

  private SearchTypeUtils() { }

  public static boolean isMobileSearchType(SearchType searchType) {
    return MOBILE_SEARCH_TYPES.contains(searchType);
  }

}
