package com.linkedin.sales.service.utils;

import com.linkedin.enterprise.EnterpriseLicenseType;
import com.linkedin.enterprise.license.LicenseAssignment;
import com.linkedin.sales.admin.SeatRole;
import java.util.Collection;
import java.util.Comparator;
import java.util.Optional;

/**
 * Helper methods to handle SeatRoles
 */
public final class SeatRoleUtils {
  private SeatRoleUtils() {
  }

  private static Integer getLssSkuPrecedence(SeatRole seatRole) {
    switch (seatRole) {
      case SALES_SEAT_TIER3: return 3;
      case SALES_SEAT_TIER2: return 2;
      case SALES_SEAT_TIER1: return 1;
      case SALES_SEAT_TIER0: return 0;
      default: return -1;
    }
  }

  private static final Comparator<SeatRole> LSS_SKU_PRECEDENCE = Comparator.comparing(SeatRoleUtils::getLssSkuPrecedence);

  private static final Comparator<SeatRole> LSS_SKU_RANKING = LSS_SKU_PRECEDENCE.reversed();

  public static Optional<SeatRole> getTopSku(Collection<SeatRole> seatRoles) {
    return seatRoles.stream().min(LSS_SKU_RANKING);
  }

  public static boolean hasTLELicense(Collection<LicenseAssignment> licenseAssignments) {
    return licenseAssignments.stream()
        .map(licenseAssignment -> EnterpriseLicenseType.parse(licenseAssignment.getEnterpriseLicenseType()))
        .anyMatch(licenseType -> licenseType == EnterpriseLicenseType.SALES_NAVIGATOR_TEAMLINK_EXTEND);
  }
}
