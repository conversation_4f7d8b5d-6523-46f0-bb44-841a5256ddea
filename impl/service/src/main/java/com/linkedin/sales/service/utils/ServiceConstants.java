package com.linkedin.sales.service.utils;

import com.google.common.collect.BiMap;
import com.google.common.collect.EnumHashBiMap;
import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.NotificationV2Urn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.sales.espresso.CsvImportListSource;
import com.linkedin.sales.espresso.PhoneNumberType;
import com.linkedin.sales.espresso.PriorityType;
import com.linkedin.sales.espresso.SocialHandleProvider;
import com.linkedin.sales.espresso.WebsiteCategory;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.salesbookmark.BookmarkType;
import com.linkedin.salesleadaccount.AccountDataSource;
import com.linkedin.salesleadaccount.LeadDataSource;
import com.linkedin.saleslist.AccountMapTier;
import com.linkedin.saleslist.ListEntityPriorityType;
import com.linkedin.saleslist.ListSource;
import com.linkedin.sales.espresso.SeatListRole;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.saleslist.List;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListType;
import com.linkedin.saleslist.RelationshipStrengthType;
import com.linkedin.saleslist.RoleType;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.salessharing.SharingRole;
import java.util.Comparator;
import java.util.Map;
import java.util.Set;


/**
 * Created by hacao at 6/13/2018
 * Class to store all the constants shared within class
 */
public final class ServiceConstants {
  private ServiceConstants() {
    // do nothing
  }

  public static final int DEFAULT_START = 0;
  public static final int DEFAULT_COUNT = 10;
  // We should not use -1 as get all count, it was not recommended by Espresso team
  public static final int GET_ALL_COUNT = -1;
  public static final long DEFAULT_SEAT_ID = 0L;
  public static final int GET_COUNT_LIMIT = 1000;

  public static final int RELATIONSHIP_MAP_ENTITY_DEFAULT_POSITION_IN_LEVEL = 0;

  // Typically total policies count will not exceed 100, espresso rest api doesn't have a get all api with pagination
  // The current max count per resource is 444, there are only 3 resource has more than 100 policies.
  // Here we use a default number 500, which is good enough to get all the policies
  public static final int SHARING_POLICY_GET_ALL_COUNT = 500;

  // The upper limit that entities can be saved into one list.
  public static final long LIST_ENTITY_LIMIT = 1000;
  public static final long LIST_ENTITY_ONLINE_PROCESSING_LIMIT = 100L;

  public static final String LIST_COMPOUND_KEY = "list";

  public static final String CREATOR_COMPOUND_KEY = "creator";
  public static final String LEAD_COMPOUND_KEY = "lead";
  public static final String ACCOUNT_COMPOUND_KEY = "account";
  public static final String ENTITY_COMPOUND_KEY = "entity";
  public static final String OWNER_COMPOUND_KEY = "owner";
  public static final String MEMBER_COMPOUND_KEY = "member";
  public static final String CONTRACT_COMPOUND_KEY = "contract";
  public static final String ORGANIZATION_COMPOUND_KEY = "organization";
  public static final String SUBJECT_COMPOUND_KEY = "subject";
  public static final String POLICY_TYPE_COMPOUND_KEY = "policyType";
  public static final String RESOURCE_COMPOUND_KEY = "resource";
  public static final String VIEWER_SEAT_COMPOUND_KEY = "viewerSeat";
  public static final String VIEWED_MEMBER_COMPOUND_KEY = "viewedMember";
  public static final String SEAT_COMPOUND_KEY = "seat";
  public static final String INTEREST_ID_COMPOUND_KEY = "interestId";

  //Constant for tracking event
  public static final String NULL_PAGE_KEY = "null-page-key";
  public static final int DEFAULT_NUMBER = 0;
  public static final String TRACKING_EVENT_DEFAULT_POSITION = "0";
  public static final String NULL_MODULE_KEY = "null-module-key";
  public static final String SALES_ACCOUNT = "salesAccount";
  public static final String SALES = "sales";
  public static final String SALES_ACTION_EVENT_NAME = "SalesActionEvent";
  public static final String PRODUCT_COLLECT_FUNNEL_TRACKING_EVENT_NAME = "ProductCollectFunnelTrackingEvent";
  public static final String PRODUCT_COLLECT_SAVE_ERROR_MESSAGE = "Failed to save the product.";

  // Delimiter
  public static final String COMMA_DELIMITER = ",";

  public static final BiMap<BookmarkType, com.linkedin.sales.espresso.BookmarkType> BOOKMARK_TYPE_BI_MAP =
      ImmutableBiMap.of(
          BookmarkType.ALERT, com.linkedin.sales.espresso.BookmarkType.ALERT
      );

  // mapping for listType from espresso list obj to service list obj
  public static final BiMap<com.linkedin.sales.espresso.ListType, ListType> LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING =
      EnumHashBiMap.create(com.linkedin.sales.espresso.ListType.class);
  static {
    LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.put(com.linkedin.sales.espresso.ListType.LEAD, ListType.LEAD);
    LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.put(com.linkedin.sales.espresso.ListType.ACCOUNT, ListType.ACCOUNT);
    LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.put(com.linkedin.sales.espresso.ListType.ACCOUNT_MAP, ListType.ACCOUNT_MAP);
    LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.put(com.linkedin.sales.espresso.ListType.PINNED_NOTIFICATION, ListType.NOTIFICATION_BOOKMARK);
    LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.put(com.linkedin.sales.espresso.ListType.BI_DISCOVER_ACCOUNTS, ListType.BI_DISCOVER_ACCOUNTS);
  }

  // mapping for listType from service list obj to espresso list obj
  public static final Map<ListType, com.linkedin.sales.espresso.ListType> LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING =
      LIST_TYPE_ESPRESSO_TO_SERVICE_MAPPING.inverse();

  // mapping for listType from espresso to urn type
  public static final Map<com.linkedin.sales.espresso.ListType, Set<String>> LIST_TYPE_ESPRESSO_TO_ENTITY_TYPE_MAPPING = ImmutableMap.of(
      com.linkedin.sales.espresso.ListType.LEAD, ImmutableSet.of(MemberUrn.ENTITY_TYPE),
      com.linkedin.sales.espresso.ListType.ACCOUNT, ImmutableSet.of(OrganizationUrn.ENTITY_TYPE),
      com.linkedin.sales.espresso.ListType.ACCOUNT_MAP, ImmutableSet.of(MemberUrn.ENTITY_TYPE,
          SalesListEntityPlaceholderUrn.ENTITY_TYPE),
      com.linkedin.sales.espresso.ListType.PINNED_NOTIFICATION, ImmutableSet.of(NotificationV2Urn.ENTITY_TYPE),
      com.linkedin.sales.espresso.ListType.BI_DISCOVER_ACCOUNTS, ImmutableSet.of(OrganizationUrn.ENTITY_TYPE)
  );

  // mapping for listSource from espresso listSource obj to service listSource obj
  public static final BiMap<com.linkedin.sales.espresso.ListSource, ListSource> LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING =
      ImmutableBiMap.<com.linkedin.sales.espresso.ListSource, ListSource>builder()
          .put(com.linkedin.sales.espresso.ListSource.CRM_SYNC, ListSource.CRM_SYNC)
          .put(com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT, ListSource.CRM_PERSON_ACCOUNT)
          .put(com.linkedin.sales.espresso.ListSource.SYSTEM, ListSource.SYSTEM)
          .put(com.linkedin.sales.espresso.ListSource.MANUAL, ListSource.MANUAL)
          .put(com.linkedin.sales.espresso.ListSource.LINKEDIN_SALES_INSIGHTS, ListSource.LINKEDIN_SALES_INSIGHTS)
          .put(com.linkedin.sales.espresso.ListSource.TAGS_MIGRATION, ListSource.TAGS_MIGRATION)
          .put(com.linkedin.sales.espresso.ListSource.CSV_IMPORT, ListSource.CSV_IMPORT)
          .put(com.linkedin.sales.espresso.ListSource.CRM_BLUEBIRD, ListSource.CRM_BLUEBIRD)
          .put(com.linkedin.sales.espresso.ListSource.BUYER_INTEREST, ListSource.BUYER_INTEREST)
          .put(com.linkedin.sales.espresso.ListSource.CRM_AT_RISK_OPPORTUNITY, ListSource.CRM_AT_RISK_OPPORTUNITY)
          .put(com.linkedin.sales.espresso.ListSource.RECOMMENDATION, ListSource.RECOMMENDATION)
          .put(com.linkedin.sales.espresso.ListSource.FLAGSHIP, ListSource.FLAGSHIP)
          .put(com.linkedin.sales.espresso.ListSource.BOOK_OF_BUSINESS, ListSource.BOOK_OF_BUSINESS)
          .put(com.linkedin.sales.espresso.ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS, ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS)
          .put(com.linkedin.sales.espresso.ListSource.LEADS_TO_FOLLOW_UP, ListSource.LEADS_TO_FOLLOW_UP)
          .put(com.linkedin.sales.espresso.ListSource.AUTO_PROSPECTOR_REC, ListSource.AUTO_PROSPECTOR_REC)
          .build();

  // mapping for listType from espresso list obj to service list obj
  public static final Map<com.linkedin.sales.espresso.ListType, PolicyType> LIST_TYPE_ESPRESSO_TO_POLICY_TYPE_MAPPING =
      ImmutableMap.of(
          com.linkedin.sales.espresso.ListType.LEAD, PolicyType.LEAD_LIST,
          com.linkedin.sales.espresso.ListType.ACCOUNT, PolicyType.ACCOUNT_LIST,
          com.linkedin.sales.espresso.ListType.ACCOUNT_MAP, PolicyType.ACCOUNT_MAP,
          // TODO: sharing is currently not supported for notification bookmarks, so default to LEAD_LIST for now
          com.linkedin.sales.espresso.ListType.NOTIFICATION_BOOKMARK, PolicyType.LEAD_LIST,
          com.linkedin.sales.espresso.ListType.BI_DISCOVER_ACCOUNTS, PolicyType.ACCOUNT_LIST
      );

  // mapping for listSource from service listSource obj to espresso listSource obj
  public static final Map<ListSource, com.linkedin.sales.espresso.ListSource> LIST_SOURCE_SERVICE_TO_ESPRESSO_MAPPING =
      LIST_SOURCE_ESPRESSO_TO_SERVICE_MAPPING.inverse();

  // mapping from list order to sort comparator, used in sorting list
  public static final ImmutableMap<ListOrdering, Comparator<List>> LIST_ORDER_TO_COMPARATOR_MAPPING = ImmutableMap.of(
      ListOrdering.LAST_VIEWED, Comparator.comparing(List::getLastViewedAt, Comparator.nullsLast(Comparator.naturalOrder())),
      ListOrdering.LAST_MODIFIED, Comparator.comparing(List::getLastModifiedAt, Comparator.nullsLast(Comparator.naturalOrder())),
      ListOrdering.NAME, Comparator.comparing(list ->
          list.hasLocalizedName() ? list.getLocalizedName().toLowerCase() : list.getName().toLowerCase())
  );

  public static final ImmutableMap<ListOrdering, Comparator<List>> LIST_ORDER_TO_COMPARATOR_MAPPING_REVERSE_ORDER = ImmutableMap.of(
      ListOrdering.LAST_VIEWED, Comparator.comparing(List::getLastViewedAt, Comparator.nullsLast(Comparator.reverseOrder())),
      ListOrdering.LAST_MODIFIED, Comparator.comparing(List::getLastModifiedAt, Comparator.nullsLast(Comparator.reverseOrder())),
      ListOrdering.NAME, Comparator.comparing(list ->
          list.hasLocalizedName() ? list.getLocalizedName().toLowerCase() : list.getName().toLowerCase(), Comparator.reverseOrder())
  );

  // Set of sources that allow list to be subscribed.
  public static final Set<ListSource> SUBSCRIBABLE_LIST_SOURCES = ImmutableSet.of(ListSource.CRM_SYNC, ListSource.CRM_PERSON_ACCOUNT);
  public static final Set<PolicyType> SUBSCRIBABLE_POLICY_TYPES = ImmutableSet.of(PolicyType.ACCOUNT_MAP);

  // The permission set for list in different levels
  public static final Set<SeatListRole>
      READ_PERMISSION_SET = ImmutableSet.of(SeatListRole.OWNER, SeatListRole.CONTRIBUTOR, SeatListRole.VIEWER);
  public static final Set<SeatListRole> WRITE_PERMISSION_SET = ImmutableSet.of(SeatListRole.OWNER, SeatListRole.CONTRIBUTOR);
  public static final Set<SeatListRole> DELETE_PERMISSION_SET = ImmutableSet.of(SeatListRole.OWNER);

  // the mapping between the urn type and the list type
  public static final Set<String> SUPPORTED_ENTITY_TYPES =
      ImmutableSet.of(MemberUrn.ENTITY_TYPE, OrganizationUrn.ENTITY_TYPE, SalesListEntityPlaceholderUrn.ENTITY_TYPE);

  public static final Set<String> SUPPORTED_LIST_AND_LIST_ENTITY_VIEWER_TYPES =
      ImmutableSet.of(MemberUrn.ENTITY_TYPE, SeatUrn.ENTITY_TYPE);

  // mapping from list type to its policy type in sharing DB
  public static final Map<ListType, PolicyType> LIST_TYPE_TO_POLICY_TYPE_MAPPING = ImmutableMap.of(
      ListType.LEAD, PolicyType.LEAD_LIST,
      ListType.ACCOUNT, PolicyType.ACCOUNT_LIST,
      ListType.ACCOUNT_MAP, PolicyType.ACCOUNT_MAP,
      // TODO: sharing is currently not supported for notification bookmark, so default to LEAD_LIST for now
      ListType.NOTIFICATION_BOOKMARK, PolicyType.LEAD_LIST,
      ListType.BI_DISCOVER_ACCOUNTS, PolicyType.ACCOUNT_LIST
  );

  public static final BiMap<RoleType, com.linkedin.sales.espresso.RoleType>
      API_ROLE_TYPE_TO_ESPRESSO_ROLE_TYPE_MAPPING =
      ImmutableBiMap.of(
          RoleType.EVALUATOR, com.linkedin.sales.espresso.RoleType.EVALUATOR,
          RoleType.CHAMPION, com.linkedin.sales.espresso.RoleType.CHAMPION,
          RoleType.DECISION_MAKER, com.linkedin.sales.espresso.RoleType.DECISION_MAKER,
          RoleType.INFLUENCER, com.linkedin.sales.espresso.RoleType.INFLUENCER,
          RoleType.PROCUREMENT, com.linkedin.sales.espresso.RoleType.PROCUREMENT,
          RoleType.NONE, com.linkedin.sales.espresso.RoleType.NONE);

  public static final Map<com.linkedin.sales.espresso.RoleType, RoleType> ESPRESSO_ROLE_TYPE_TO_API_ROLE_TYPE_MAPPING =
      API_ROLE_TYPE_TO_ESPRESSO_ROLE_TYPE_MAPPING.inverse();

  public static final BiMap<RelationshipStrengthType, com.linkedin.sales.espresso.RelationshipStrengthType>
      API_RELATIONSHIP_STRENGTH_TYPE_TO_ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_MAPPING =
      ImmutableBiMap.of(RelationshipStrengthType.STRONG, com.linkedin.sales.espresso.RelationshipStrengthType.STRONG,
          RelationshipStrengthType.MEDIUM, com.linkedin.sales.espresso.RelationshipStrengthType.MEDIUM,
          RelationshipStrengthType.WEAK, com.linkedin.sales.espresso.RelationshipStrengthType.WEAK,
          RelationshipStrengthType.NONE, com.linkedin.sales.espresso.RelationshipStrengthType.NONE);

  public static final Map<com.linkedin.sales.espresso.RelationshipStrengthType, RelationshipStrengthType>
      ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_TO_API_RELATIONSHIP_STRENGTH_TYPE_MAPPING =
      API_RELATIONSHIP_STRENGTH_TYPE_TO_ESPRESSO_RELATIONSHIP_STRENGTH_TYPE_MAPPING.inverse();

  public static final Set<ShareRole> WRITER_ROLES = ImmutableSet.of(ShareRole.WRITER, ShareRole.OWNER);
  public static final Set<ShareRole> READER_ROLES = ImmutableSet.of(ShareRole.READER, ShareRole.WRITER, ShareRole.OWNER);

  //the mapping between the service sharing role obj to espresso share role obj
  public static final Map<SharingRole, ShareRole> SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING =
      new ImmutableMap.Builder<SharingRole, ShareRole>()
          .put(SharingRole.OWNER, ShareRole.OWNER)
          .put(SharingRole.READER, ShareRole.READER)
          .put(SharingRole.WRITER, ShareRole.WRITER)
          .build();

  //the mapping between espresso share role obj to the service sharing role obj
  public static final Map<ShareRole, SharingRole> SHARE_ROLE_ESPRESSO_TO_SERVICE_MAPPING =
      new ImmutableMap.Builder<ShareRole, SharingRole>()
          .put(ShareRole.OWNER, SharingRole.OWNER)
          .put(ShareRole.READER, SharingRole.READER)
          .put(ShareRole.WRITER, SharingRole.WRITER)
          .build();

  // the mapping between accessAction and the corresponding shareRoles that permit the action
  public static final Map<AccessAction, java.util.Set<ShareRole>> SHARING_ACCESS_ACTION_TO_ROLES_MAP =
      ImmutableMap.of(AccessAction.READ, READER_ROLES,
          AccessAction.UPDATE, WRITER_ROLES,
          AccessAction.ADMIN, ImmutableSet.of(ShareRole.OWNER));

  // the mapping between accessAction and the corresponding shareRoles that permit the action for account map
  public static final Map<AccessAction, java.util.Set<ShareRole>> ACCOUNT_MAP_SHARING_ACCESS_ACTION_TO_ROLES_MAP =
      ImmutableMap.of(AccessAction.READ, READER_ROLES,
          AccessAction.UPDATE, WRITER_ROLES,
          AccessAction.ADMIN, WRITER_ROLES);

  // mapping for urn type to listType from service list obj
  public static final Map<String, ListType> ENTITY_TYPE_TO_LIST_TYPE_SERVICE_MAPPING = ImmutableMap.of(
      MemberUrn.ENTITY_TYPE, ListType.LEAD,
      OrganizationUrn.ENTITY_TYPE, ListType.ACCOUNT
  );

  // mapping for urn type to listType from service list obj
  public static final Map<String, ListType> ACCOUNT_MAP_ENTITY_TYPE_TO_LIST_TYPE_SERVICE_MAPPING = ImmutableMap.of(
      MemberUrn.ENTITY_TYPE, ListType.ACCOUNT_MAP
  );

  // mapping for AccountDataSource between service AccountDataSource object and espresso AccountDataSource object
  public static final ImmutableBiMap<AccountDataSource, com.linkedin.sales.espresso.AccountDataSource>
      ACCOUNT_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.of(AccountDataSource.CRM_SYNC, com.linkedin.sales.espresso.AccountDataSource.CRM_SYNC,
          AccountDataSource.USER_GENERATED, com.linkedin.sales.espresso.AccountDataSource.USER_GENERATED);

  // mapping for SocialHandleType between service SocialHandleType object and espresso SocialHandleType object
  public static final ImmutableBiMap<com.linkedin.salesleadaccount.SocialHandleProvider, SocialHandleProvider>
      SOCIAL_HANDLE_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.<com.linkedin.salesleadaccount.SocialHandleProvider, SocialHandleProvider>builder()
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.AIM, SocialHandleProvider.AIM)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.HANGOUTS, SocialHandleProvider.HANGOUTS)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.YAHOO, SocialHandleProvider.YAHOO)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.ICQ, SocialHandleProvider.ICQ)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.SKYPE, SocialHandleProvider.SKYPE)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.TWITTER, SocialHandleProvider.TWITTER)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.WECHAT, SocialHandleProvider.WECHAT)
          .put(com.linkedin.salesleadaccount.SocialHandleProvider.QQ, SocialHandleProvider.QQ).build();

  // mapping for WebsiteType between espresso WebsiteType object and service WebsiteType object
  public static final ImmutableBiMap<com.linkedin.salesleadaccount.WebsiteCategory, WebsiteCategory>
      WEBSITE_CATEGORY_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.<com.linkedin.salesleadaccount.WebsiteCategory, WebsiteCategory>builder()
          .put(com.linkedin.salesleadaccount.WebsiteCategory.BLOG, WebsiteCategory.BLOG)
          .put(com.linkedin.salesleadaccount.WebsiteCategory.COMPANY, WebsiteCategory.COMPANY)
          .put(com.linkedin.salesleadaccount.WebsiteCategory.PERSONAL, WebsiteCategory.PERSONAL)
          .put(com.linkedin.salesleadaccount.WebsiteCategory.PORTFOLIO, WebsiteCategory.PORTFOLIO)
          .put(com.linkedin.salesleadaccount.WebsiteCategory.RSSFEED, WebsiteCategory.RSSFEED)
          .put(com.linkedin.salesleadaccount.WebsiteCategory.OTHER, WebsiteCategory.OTHER).build();

  // mapping for WebsiteType between espresso WebsiteType object and service WebsiteType object
  public static final ImmutableBiMap<com.linkedin.common.PhoneNumberType, PhoneNumberType>
      PHONE_NUMNER_TYPE_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.<com.linkedin.common.PhoneNumberType, PhoneNumberType>builder()
          .put(com.linkedin.common.PhoneNumberType.FAX, PhoneNumberType.FAX)
          .put(com.linkedin.common.PhoneNumberType.HOME, PhoneNumberType.HOME)
          .put(com.linkedin.common.PhoneNumberType.MOBILE, PhoneNumberType.MOBILE)
          .put(com.linkedin.common.PhoneNumberType.WORK, PhoneNumberType.WORK).build();

  public static final  ImmutableBiMap<com.linkedin.sales.espresso.ListSource, com.linkedin.sales.espresso.CsvImportListSource>
      ESPRESSO_LIST_SOURCES_TO_ESPRESSO_CSV_IMPORT_LIST_SOURCES_MAPPING =
      ImmutableBiMap.<com.linkedin.sales.espresso.ListSource, com.linkedin.sales.espresso.CsvImportListSource>builder()
      .put(com.linkedin.sales.espresso.ListSource.CSV_IMPORT, CsvImportListSource.CSV_IMPORT)
      .put(com.linkedin.sales.espresso.ListSource.BOOK_OF_BUSINESS, CsvImportListSource.BOOK_OF_BUSINESS)
      .put(com.linkedin.sales.espresso.ListSource.MANUAL, CsvImportListSource.MANUAL)
      .build();


  // mapping for LeadDataSource between service LeadDataSource object and espresso LeadDataSource object
  public static final ImmutableBiMap<LeadDataSource, com.linkedin.sales.espresso.LeadDataSource>
      LEAD_DATA_SOURCE_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.of(LeadDataSource.CRM_SYNC, com.linkedin.sales.espresso.LeadDataSource.CRM_SYNC,
          LeadDataSource.USER_GENERATED, com.linkedin.sales.espresso.LeadDataSource.USER_GENERATED);

  // mapping for priorityType from service listEntity obj to espresso listEntity obj
  public static final ImmutableBiMap<ListEntityPriorityType, com.linkedin.sales.espresso.PriorityType>
      PRIORITY_TYPE_SERVICE_TO_ESPRESSO_MAPPING =
      ImmutableBiMap.of(ListEntityPriorityType.HIGH, PriorityType.HIGH, ListEntityPriorityType.CLEARED,
          PriorityType.CLEARED);

  //Set of list sources that would be updated in search with expected delay. Any new autogen list source that needs to be
  //back filled using offline/nearline flows on a regular cadence should be added as a low priority source. The source has to be readonly for users.
  //List sources in which users can edit the list entities should be excluded from it.
  public static final Set<com.linkedin.sales.espresso.ListSource> LOW_PRIORITY_SEARCH_INDEX_UPDATE_LIST_SOURCES = ImmutableSet.of(
      com.linkedin.sales.espresso.ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS, com.linkedin.sales.espresso.ListSource.LEADS_TO_FOLLOW_UP,
      com.linkedin.sales.espresso.ListSource.BUYER_INTEREST);

  // The default espresso list type of all lists created through the List CSV Import workflow
  public static final com.linkedin.sales.espresso.ListType DEFAULT_LIST_CSV_IMPORT_LIST_TYPE =
      com.linkedin.sales.espresso.ListType.ACCOUNT;

  // The default espresso list source of all lists created through the List CSV Import workflow
  public static final com.linkedin.sales.espresso.ListSource DEFAULT_LIST_CSV_IMPORT_LIST_SOURCE =
      com.linkedin.sales.espresso.ListSource.CSV_IMPORT;

  // Mapping tier to corresponding integer value.
  public static final ImmutableBiMap<AccountMapTier, Integer> ACCOUNT_MAP_TIER_INTEGER_MAP =
      ImmutableBiMap.of(AccountMapTier.TIER_1, 1,
          AccountMapTier.TIER_2, 2,
          AccountMapTier.TIER_3, 3);

  // Mapping integer to corresponding tier.
  public static final Map<Integer, AccountMapTier> INVERTED_ACCOUNT_MAP_TIER_INTEGER_MAP =
      ACCOUNT_MAP_TIER_INTEGER_MAP.inverse();

  // List types for which Batch Update operation is allowed.
  public static final Set<com.linkedin.sales.espresso.ListType> BATCH_UPDATE_ALLOWED_LIST_TYPE =
      ImmutableSet.of(com.linkedin.sales.espresso.ListType.LEAD, com.linkedin.sales.espresso.ListType.ACCOUNT,
          com.linkedin.sales.espresso.ListType.ACCOUNT_MAP);
}
