package com.linkedin.sales.service.utils;

import com.google.protobuf.ByteString;
import proto.com.linkedin.common.TrackingId;
import edu.umd.cs.findbugs.annotations.NonNull;


public final class TrackingUtils {
  private TrackingUtils() {
  }

  @NonNull
  public static TrackingId convertEspressoToProtoTrackingId(com.linkedin.sales.espresso.TrackingId trackingId) {
    return TrackingId.newBuilder().setFixedValue(ByteString.copyFrom(trackingId.bytes())).build();
  }

}
