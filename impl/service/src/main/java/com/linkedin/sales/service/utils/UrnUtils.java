package com.linkedin.sales.service.utils;

import com.linkedin.common.FunctionUrnArray;
import com.linkedin.common.urn.AmbryBlobUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.FunctionUrn;
import com.linkedin.common.urn.GeoUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesIdentityUrn;
import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SalesSharedSearchUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.SeniorityUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.deco.urn.exceptions.InvalidUrnException;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * Utils for urn related operations
 */
public final class UrnUtils {
  private static final String NONE_URN_STR = "urn:li:none";


  private final static Logger LOG = LoggerFactory.getLogger(UrnUtils.class);

  private UrnUtils() {
  }

  @NonNull
  public static SalesListUrn createSalesListUrn(long listId) {
    try {
      return SalesListUrn.createFromUrn(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, listId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SalesListUrn createSalesListUrn(Urn urn) {
    try {
      return SalesListUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SalesListUrn createSalesListUrn(CharSequence rawUrn) {
    try {
      return createSalesListUrn(SalesListUrn.createFromCharSequence(rawUrn));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static proto.com.linkedin.common.SalesListUrn createProtoSalesListUrn(String rawUrn) {
    try {
      return proto.com.linkedin.common.SalesListUrn.newBuilder()
          .setListId(com.linkedin.common.urn.SalesListUrn.deserialize(rawUrn).getIdAsLong())
          .build();
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SeniorityUrn createSeniorityUrn(long seniorityId) {
    try {
      return SeniorityUrn.createFromUrn(Urn.createFromTuple(SeniorityUrn.ENTITY_TYPE, seniorityId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static FunctionUrn createFunctionUrn(long functionId) {
    try {
      return FunctionUrn.createFromUrn(Urn.createFromTuple(FunctionUrn.ENTITY_TYPE, functionId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static GeoUrn createGeoUrn(long geoId) {
    try {
      return GeoUrn.createFromUrn(Urn.createFromTuple(GeoUrn.ENTITY_TYPE, geoId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static CsvImportTaskUrn createCsvImportTaskUrn(long csvImportTaskId) {
    try {
      return CsvImportTaskUrn.createFromUrn(Urn.createFromTuple(CsvImportTaskUrn.ENTITY_TYPE, csvImportTaskId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static CsvImportTaskUrn createCsvImportTaskUrn(Urn urn) {
    try {
      return CsvImportTaskUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static CsvImportTaskUrn createCsvImportTaskUrn(CharSequence rawUrn) {
    try {
      return createCsvImportTaskUrn(CsvImportTaskUrn.createFromCharSequence(rawUrn));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SeatUrn createSeatUrn(long seatId) {
    try {
      return SeatUrn.createFromUrn(Urn.createFromTuple(SeatUrn.ENTITY_TYPE, seatId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SeatUrn createSeatUrn(Urn urn) {
    try {
      return SeatUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SeatUrn createSeatUrn(CharSequence rawUrn) {
    try {
      return createSeatUrn(SeatUrn.createFromCharSequence(rawUrn));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static ContractUrn createContractUrn(long listId) {
    try {
      return ContractUrn.createFromUrn(Urn.createFromTuple(ContractUrn.ENTITY_TYPE, listId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static ContractUrn createContractUrn(Urn urn) {
    try {
      return ContractUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static ContractUrn createContractUrn(CharSequence rawUrn) {
    try {
      return createContractUrn(ContractUrn.createFromCharSequence(rawUrn));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static proto.com.linkedin.common.ContractUrn createContractUrn(String contractUrn) {
    try {
      return proto.com.linkedin.common.ContractUrn.newBuilder()
          .setContractId(com.linkedin.common.urn.ContractUrn.deserialize(contractUrn).getIdAsLong())
          .build();
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @NonNull
  public static AmbryBlobUrn createAmbryBlobUrn(String blobId) {
    try {
      return AmbryBlobUrn.createFromUrn(Urn.createFromTuple(AmbryBlobUrn.ENTITY_TYPE, blobId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static OrganizationUrn createOrganizationUrn(long organizationId) {
    try {
      return OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, organizationId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static proto.com.linkedin.common.OrganizationUrn createOrganizationUrn(String rawUrn) {
    try {
      return proto.com.linkedin.common.OrganizationUrn.newBuilder()
          .setOrganizationId(com.linkedin.common.urn.OrganizationUrn.deserialize(rawUrn).getIdAsLong())
          .build();
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  /**
   * Specifies whether the given URN is a Member URN
   */
  public static boolean isMemberUrn(@Nullable  Urn urn) {
    return urn != null && MemberUrn.ENTITY_TYPE.equals(urn.getEntityType());
  }

  /**
   * Specifies whether the given URN is a SalesIdentity URN
   */
  public static boolean isSalesIdentityUrn(@Nullable Urn urn) {
    return urn != null && SalesIdentityUrn.ENTITY_TYPE.equals(urn.getEntityType());
  }

  /**
   * Specifies whether the given URN is a Seat URN
   */
  public static boolean isSeatUrn(@Nullable Urn urn) {
    return urn != null && SeatUrn.ENTITY_TYPE.equals(urn.getEntityType());
  }

  /**
   * Build {@link DeveloperApplicationUrn} from application id
   * @param applicationId id of the application
   * @return {@link DeveloperApplicationUrn}
   */
  @Nullable
  public static DeveloperApplicationUrn createDeveloperApplicationUrn(int applicationId) {
    try {
      return DeveloperApplicationUrn.createFromUrn(
          Urn.createFromTuple(DeveloperApplicationUrn.ENTITY_TYPE, applicationId));
    } catch (URISyntaxException e) {
      LOG.error("Unable to create for developerApplicationUrn", e);
      return null;
    }
  }

  /**
   * Converts a given urn to the type AnnotatableEntityUrn. Throws an error if they type is not defined.
   * @param urn
   * @return
   * @throws URISyntaxException
   */
  public static AnnotatableEntityUrn convertUrnToAnnotatableEntityUrn(Urn urn) {
    AnnotatableEntityUrn entityUrn = new AnnotatableEntityUrn();
    try {
      switch (urn.getEntityType()) {
        case MemberUrn.ENTITY_TYPE:
          entityUrn.setMemberUrn(MemberUrn.createFromUrn(urn));
          return entityUrn;
        case OrganizationUrn.ENTITY_TYPE:
          entityUrn.setOrganizationUrn(OrganizationUrn.createFromUrn(urn));
          return entityUrn;
        case SalesSharedSearchUrn.ENTITY_TYPE:
          entityUrn.setSalesSharedSearchUrn(SalesSharedSearchUrn.createFromUrn(urn));
          return entityUrn;
        default:
          LOG.error("Type not supported by AnnotatableEntityUrn. Type - {}", urn.getEntityType());
          return null;
      }
    } catch (URISyntaxException e) {
      LOG.error("Cannot create typed urn from {}", urn);
      return null;
    }
  }

  @NonNull
  public static EnterpriseProfileApplicationInstanceUrn createEnterpriseProfileApplicationInstanceUrn(Urn urn) {
    try {
      return EnterpriseProfileApplicationInstanceUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static SalesInsightsMetricsReportUrn createSalesInsightsMetricsReportUrn(Urn urn) {
    try {
      return SalesInsightsMetricsReportUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static EnterpriseAccountUrn createEnterpriseAccountUrn(final long enterpriseAccountId) {
    try {
      return EnterpriseAccountUrn.createFromUrn(Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, enterpriseAccountId));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static EnterpriseApplicationInstanceUrn createEnterpriseApplicationInstanceUrn(Urn urn) {
    try {
      return EnterpriseApplicationInstanceUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e); // doesn't happen
    }
  }

  @NonNull
  public static MemberUrn createMemberUrn(CharSequence memberUrn) {
    try {
      return MemberUrn.createFromUrn(MemberUrn.createFromCharSequence(memberUrn));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  public static MemberUrn toMemberUrn(long id) {
    return toMemberUrn(Urn.createFromTuple("member", id));
  }

  public static MemberUrn toMemberUrn(Urn urn) {
    try {
      return MemberUrn.createFromUrn(urn);
    } catch (URISyntaxException e) {
      throw new InvalidUrnException("Could not create member urn.", e);
    }
  }

  @NonNull
  public static EnterpriseApplicationInstanceUrn createEnterpriseApplicationInstanceUrn(final long enterpriseAccountId,
      final long enterpriseApplicationInstanceId) {
    return new EnterpriseApplicationInstanceUrn(createEnterpriseAccountUrn(enterpriseAccountId), enterpriseApplicationInstanceId);
  }

  @NonNull
  public static EnterpriseProfileUrn createEnterpriseProfileUrn(final long enterpriseAccountId, final long enterpriseProfileId) {
    return new EnterpriseProfileUrn(createEnterpriseAccountUrn(enterpriseAccountId), enterpriseProfileId);
  }

  @NonNull
  public static SalesListEntityPlaceholderUrn createSalesListEntityPlaceholderUrn(
      CharSequence salesListEntityPlaceholderUrn) {
    try {
      return SalesListEntityPlaceholderUrn.deserialize(salesListEntityPlaceholderUrn.toString());
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @NonNull
  public static StandardizedProductUrn createStandardizedProductUrn(CharSequence standardizedProductUrn) {
    try {
      return StandardizedProductUrn.deserialize(standardizedProductUrn.toString());
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @NonNull
  public static Urn createUrnFromString(String urnString) {
    try {
      return Urn.createFromString(urnString);
    } catch (URISyntaxException e) {
      throw new RuntimeException("Failed to generate the Urn for the string " + urnString, e);
    }
  }

  public static boolean isNoneUrn(String urnStr) {
    return NONE_URN_STR.equals(urnStr);
  }

  public static FunctionUrnArray formatFunctionUrns(List<CharSequence> customerFunctionUrns) {
    return new FunctionUrnArray(customerFunctionUrns.stream()
        .map(functionUrn -> {
          try {
            return FunctionUrn.deserialize(functionUrn.toString());
          } catch (URISyntaxException e) {
            String errMsg = String.format("Unable to deserialize Function Urn from String: %s", functionUrn);
            LOG.error(errMsg, e);
            return null;
          }
        }).filter(Objects::nonNull).collect(Collectors.toList()));
  }

  @NonNull
  public static StandardizedProductCategoryUrn createStandardizedProductCategoryUrn(CharSequence standardizedProductCategoryUrn) {
    try {
      return StandardizedProductCategoryUrn.deserialize(standardizedProductCategoryUrn.toString());
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }
}
