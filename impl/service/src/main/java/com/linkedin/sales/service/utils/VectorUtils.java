package com.linkedin.sales.service.utils;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.digitalmedia.AssetMediaArtifactFileIdentifiers;
import com.linkedin.digitalmedia.FileIdentifier;
import com.linkedin.digitalmedia.MediaArtifactFileIdentifiers;
import com.linkedin.digitalmedia.MediaArtifactFileIdentifiersArray;
import com.linkedin.identity.Profile;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * Util class for vector related functionality
 * <AUTHOR>
 */
public final class VectorUtils {
  private VectorUtils() {
  }

  /**
   * Profile objects have a profilePicture() field which contains a DigitalMediaUrn that uniquely identifies a profile picture
   * We send those DigitalMediaUrn's downstream to Vector to generate absolute urls of those profile pictures
   * However, the BatchGet response from Vector is List<AssetMediaArtifactFileIdentifiers> not Map<DigitalMediaUrn, AssetMediaArtifactFileIdentifiers>
   * This method is meant to map Profiles -> AssetMediaArtifactFileIdentifiers (absolute urls)
   */
  @NonNull
  public static Map<MemberUrn, Optional<Url>> mapProfilesToVectorUrls(@NonNull List<Profile> profiles,
      @NonNull List<AssetMediaArtifactFileIdentifiers> fileIdentifiers) {
    // Get a map from DigitalmediaAssetUrn to the vector array
    Map<Urn, MediaArtifactFileIdentifiersArray> vectorsMap = fileIdentifiers.stream()
        .filter(AssetMediaArtifactFileIdentifiers::hasAssetUrn)
        .filter(AssetMediaArtifactFileIdentifiers::hasMediaArtifactFileIdentifiers)
        .collect(Collectors.toMap(AssetMediaArtifactFileIdentifiers::getAssetUrn,
            AssetMediaArtifactFileIdentifiers::getMediaArtifactFileIdentifiers));

    // map the profiles (input) to urls
    return profiles.stream()
        .filter(Objects::nonNull)
        .filter(Profile::hasId)
        .collect(Collectors.toMap(profile -> new MemberUrn(profile.getId()), profile -> {
          if (profile.hasProfilePicture() && profile.getProfilePicture().hasDisplayImage()
              && vectorsMap.containsKey(profile.getProfilePicture().getDisplayImage())) {
            return vectorsMap.get(profile.getProfilePicture().getDisplayImage())
                .stream()
                .filter(MediaArtifactFileIdentifiers::hasIdentifiers)
                .flatMap(urls -> urls.getIdentifiers().stream())
                .filter(FileIdentifier::hasIdentifier)
                .map(profilePicUrl -> new Url(profilePicUrl.getIdentifier()))
                .findFirst();
          } else {
            return Optional.empty();
          }
        }));
  }
}
