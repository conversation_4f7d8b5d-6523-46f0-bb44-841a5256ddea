package com.linkedin.sales.service.warmintros;

import com.google.common.base.Preconditions;
import com.google.rpc.Code;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.function.Try;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssWarmIntrosDB;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Nonnull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import proto.com.linkedin.common.MemberUrnBridge;
import proto.com.linkedin.common.SeatUrn;
import proto.com.linkedin.common.SeatUrnBridge;
import proto.com.linkedin.saleswarmintros.BatchCreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.BatchGetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.CreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.GetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.IntroducerRecommendation;
import proto.com.linkedin.saleswarmintros.IntroducerRecommendationStatus;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendation;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendationKey;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendationResponse;


/**
 * Service for interacting with WarmIntroRecommendation Espresso table.
 */
public class WarmIntroRecommendationService {
  private static final Logger LOG = LoggerFactory.getLogger(WarmIntroRecommendationService.class);

  private final LssWarmIntrosDB _lssWarmIntrosDB;

  public WarmIntroRecommendationService(LssWarmIntrosDB lssWarmIntrosDB) {
    _lssWarmIntrosDB = lssWarmIntrosDB;
  }

  /**
   * Creates multiple WarmIntroRecommendation records in a batch operation.
   * @param warmIntroRecommendationList The list of WarmIntroRecommendation objects to be created.
   * @return A Task that resolves to a BatchCreateWarmIntroRecommendationResponse containing
   *         the results of the batch operation.
   */
  public Task<BatchCreateWarmIntroRecommendationResponse> batchCreateWarmIntroRecommendation(
      @Nonnull List<WarmIntroRecommendation> warmIntroRecommendationList) {
    if (warmIntroRecommendationList.isEmpty()) {
      LOG.warn("Received an empty list for batch creation. Returning an empty response.");
      return Task.value(BatchCreateWarmIntroRecommendationResponse.newBuilder()
          .addAllResponses(Collections.emptyList())
          .build());
    }

    List<Task<Try<CreateWarmIntroRecommendationResponse>>> tasks = warmIntroRecommendationList.stream()
        .map(rec -> create(rec).toTry())
        .collect(Collectors.toList());

    return Task.par(tasks).map(results -> {
      List<WarmIntroRecommendationResponse> responses = new ArrayList<>();

      for (int i = 0; i < results.size(); i++) {
        WarmIntroRecommendation warmIntro = warmIntroRecommendationList.get(i);
        Try<CreateWarmIntroRecommendationResponse> result = results.get(i);

        WarmIntroRecommendationKey key = constructWarmIntroRecommendationKey(warmIntro.getSeatUrn(), warmIntro.getLeadMemberUrn());
        WarmIntroRecommendationResponse.Builder responseBuilder = WarmIntroRecommendationResponse.newBuilder().setKey(key);
        if (result.isFailed()) {
          Throwable error = result.getError();
          String errorMessage = String.format("Failed to create WarmIntroRecommendation for %s: %s", key, error.getMessage());
          LOG.error(errorMessage);
          responseBuilder.setError(com.google.rpc.Status.newBuilder().setCode(Code.INTERNAL_VALUE).setMessage(errorMessage).build());
        } else {
          responseBuilder.setValue(result.get().getValue());
        }
        responses.add(responseBuilder.build());
      }

      return BatchCreateWarmIntroRecommendationResponse.newBuilder()
          .addAllResponses(responses)
          .build();
    });
  }


  /**
   * Creates a new WarmIntroRecommendation record.
   * @param warmIntroRecommendation The WarmIntroRecommendation object to be created.
   * @return A Task that resolves to a CreateWarmIntroRecommendationResponse containing the result of the operation.
   */
  public Task<CreateWarmIntroRecommendationResponse> create(@Nonnull WarmIntroRecommendation warmIntroRecommendation) {
    long now = System.currentTimeMillis();
    WarmIntroRecommendation populatedRecommendation = warmIntroRecommendation.toBuilder()
        .clearIntroducerRecommendations()
        .addAllIntroducerRecommendations(
            warmIntroRecommendation.getIntroducerRecommendationsList().stream()
                .map(ir -> ir.toBuilder()
                    .setCreatedTime(now)
                    .setModifiedTime(now)
                    .build())
                .collect(Collectors.toList()))
        .build();

    return _lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(SeatUrnBridge.INSTANCE.fromProto(populatedRecommendation.getSeatUrn()),
            MemberUrnBridge.INSTANCE.fromProto(populatedRecommendation.getLeadMemberUrn()),
            convertToEspressoWarmIntroRecommendation(populatedRecommendation))
        .map(httpStatus -> CreateWarmIntroRecommendationResponse.newBuilder()
            .setKey(constructWarmIntroRecommendationKey(populatedRecommendation.getSeatUrn(), populatedRecommendation.getLeadMemberUrn()))
            .setValue(populatedRecommendation)
            .build())
        .recoverWith(throwable -> {
          LOG.error("Failed to create WarmIntroRecommendation {}: {}", populatedRecommendation, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Retrieves a WarmIntroRecommendation record by its unique key.
   * @param warmIntroRecommendationKey The key of the WarmIntroRecommendation to be retrieved.
   * @return A Task that resolves to a GetWarmIntroRecommendationResponse containing the result of the operation.
   */
  public Task<GetWarmIntroRecommendationResponse> getWarmIntroRecommendation(
      @Nonnull WarmIntroRecommendationKey warmIntroRecommendationKey) {
    Preconditions.checkNotNull(warmIntroRecommendationKey, "WarmIntroRecommendationKey cannot be null");

    return _lssWarmIntrosDB.getWarmIntroRecommendation(SeatUrnBridge.INSTANCE.fromProto(warmIntroRecommendationKey.getSeatUrn()),
            MemberUrnBridge.INSTANCE.fromProto(warmIntroRecommendationKey.getLeadMemberUrn()))
        .flatMap(maybeWarmIntroRecommendation -> maybeWarmIntroRecommendation
                .map(warmIntroRecommendation -> {
                  GetWarmIntroRecommendationResponse response = GetWarmIntroRecommendationResponse.newBuilder()
                      .setValue(convertToProtoWarmIntroRecommendation(warmIntroRecommendation,
                          warmIntroRecommendationKey.getSeatUrn(), warmIntroRecommendationKey.getLeadMemberUrn()))
                      .build();
                  return Task.value(response);
                })
                .orElseGet(() -> Task.failure(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND, "WarmIntroRecommendation does not exist")))
        )
        .recoverWith(throwable -> {
          LOG.error("Failed to get WarmIntroRecommendation with key: {}", warmIntroRecommendationKey, throwable);
          return Task.failure(throwable);
        });
  }

  /**
   * Batch retrieves multiple WarmIntroRecommendation records by their unique keys.
   * @param warmIntroRecommendationKeys The list of keys for the WarmIntroRecommendations to be retrieved.
   * @return A Task that resolves to a BatchGetWarmIntroRecommendationResponse containing the results of the operation.
   */
  public Task<BatchGetWarmIntroRecommendationResponse> batchGetWarmIntroRecommendation(
      @Nonnull List<WarmIntroRecommendationKey> warmIntroRecommendationKeys) {
    List<Task<WarmIntroRecommendationResponse>> tasks = warmIntroRecommendationKeys.stream()
        .map(key -> getWarmIntroRecommendation(key)
            .map(getResponse -> WarmIntroRecommendationResponse.newBuilder()
                .setKey(key)
                .setValue(getResponse.getValue())
                .build())
            .recoverWith(throwable -> {
              String errorMessage = String.format("Failed to get WarmIntroRecommendation for %s: %s", key, throwable.getMessage());
              LOG.error(errorMessage);
              com.google.rpc.Status error = com.google.rpc.Status.newBuilder()
                  .setCode(com.google.rpc.Code.INTERNAL_VALUE)
                  .setMessage(errorMessage)
                  .build();

              return Task.value(WarmIntroRecommendationResponse.newBuilder().setKey(key).setError(error).build());
            })
        )
        .collect(Collectors.toList());

    return Task.par(tasks)
        .map(responses -> BatchGetWarmIntroRecommendationResponse.newBuilder()
            .addAllResponses(responses)
            .build())
        .recoverWith(throwable -> {
          LOG.error("Unexpected error during batchGetWarmIntroRecommendation for {} items", warmIntroRecommendationKeys, throwable);
          return Task.failure(throwable);
        });
  }

  private com.linkedin.sales.espresso.WarmIntroRecommendation convertToEspressoWarmIntroRecommendation(
      WarmIntroRecommendation protoWarmIntroRecommendation) {
    com.linkedin.sales.espresso.WarmIntroRecommendation espressoWarmIntroRecommendation =
        new com.linkedin.sales.espresso.WarmIntroRecommendation();

    List<com.linkedin.sales.espresso.IntroducerRecommendation> espressoIntroducerRecommendations =
        protoWarmIntroRecommendation.getIntroducerRecommendationsList().stream()
            .map(this::convertToEspressoIntroducerRecommendation)
            .collect(Collectors.toList());
    espressoWarmIntroRecommendation.setIntroducerRecommendations(espressoIntroducerRecommendations);

    return espressoWarmIntroRecommendation;
  }

  private com.linkedin.sales.espresso.IntroducerRecommendation convertToEspressoIntroducerRecommendation(
      IntroducerRecommendation protoIntroducerRecommendation) {
    com.linkedin.sales.espresso.IntroducerRecommendation espressoIntroducerRecommendation =
        new com.linkedin.sales.espresso.IntroducerRecommendation();
    if (protoIntroducerRecommendation.hasMemberUrn()) {
      espressoIntroducerRecommendation.setIntroducer(
          MemberUrnBridge.INSTANCE.fromProto(protoIntroducerRecommendation.getMemberUrn()).toString());
    }
    if (!protoIntroducerRecommendation.getRationale().isEmpty()) {
      espressoIntroducerRecommendation.setRationale(protoIntroducerRecommendation.getRationale());
    }
    if (!protoIntroducerRecommendation.getVariant().isEmpty()) {
      espressoIntroducerRecommendation.setVariant(protoIntroducerRecommendation.getVariant());
    }
    espressoIntroducerRecommendation.setScore(protoIntroducerRecommendation.getScore());
    if (protoIntroducerRecommendation.getCreatedTime() != 0) {
      espressoIntroducerRecommendation.setCreatedTime(protoIntroducerRecommendation.getCreatedTime());
    }
    if (protoIntroducerRecommendation.getModifiedTime() != 0) {
      espressoIntroducerRecommendation.setModifiedTime(protoIntroducerRecommendation.getModifiedTime());
    }
    if (protoIntroducerRecommendation.getStatus() != IntroducerRecommendationStatus.IntroducerRecommendationStatus_UNKNOWN) {
      espressoIntroducerRecommendation.setStatus(protoIntroducerRecommendation.getStatus().toString());
    }
    return espressoIntroducerRecommendation;
  }

  private BatchCreateWarmIntroRecommendationResponse buildBatchCreateResponse(List<CreateWarmIntroRecommendationResponse> responses) {
    BatchCreateWarmIntroRecommendationResponse.Builder responseBuilder = BatchCreateWarmIntroRecommendationResponse.newBuilder()
        .setContext(si.ResponseContext.newBuilder().build());

    List<WarmIntroRecommendationResponse> convertedResponses = responses.stream()
        .map(response -> WarmIntroRecommendationResponse.newBuilder()
            .setKey(response.getKey())
            .setValue(response.getValue())
            .build())
        .collect(Collectors.toList());

    responseBuilder.addAllResponses(convertedResponses);
    return responseBuilder.build();
  }

  private WarmIntroRecommendation convertToProtoWarmIntroRecommendation(
      com.linkedin.sales.espresso.WarmIntroRecommendation espressoWarmIntroRecommendation,
      SeatUrn seatUrn,
      proto.com.linkedin.common.MemberUrn leadMemberUrn) {
    WarmIntroRecommendation.Builder protoWarmIntroRecommendationBuilder = WarmIntroRecommendation.newBuilder()
        .setSeatUrn(seatUrn)
        .setLeadMemberUrn(leadMemberUrn);

    List<IntroducerRecommendation> protoIntroducerRecommendations =
        espressoWarmIntroRecommendation.getIntroducerRecommendations().stream()
            .map(this::convertToProtoIntroducerRecommendation)
            .collect(Collectors.toList());
    protoWarmIntroRecommendationBuilder.addAllIntroducerRecommendations(protoIntroducerRecommendations);

    return protoWarmIntroRecommendationBuilder.build();
  }

  private IntroducerRecommendation convertToProtoIntroducerRecommendation(
      com.linkedin.sales.espresso.IntroducerRecommendation espressoIntroducerRecommendation) {
    IntroducerRecommendation.Builder protoIntroducerRecommendationBuilder = IntroducerRecommendation.newBuilder();

    MemberUrn introducerMemberUrn = null;
    try {
      introducerMemberUrn = MemberUrn.deserialize(espressoIntroducerRecommendation.getIntroducer().toString());
    } catch (URISyntaxException e) {
      LOG.error("Failed to deserialize memberUrn {}", espressoIntroducerRecommendation.getIntroducer(), e);
      throw new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, e);
    }
    protoIntroducerRecommendationBuilder.setMemberUrn(MemberUrnBridge.INSTANCE.fromPegasus(introducerMemberUrn));
    protoIntroducerRecommendationBuilder.setRationale(espressoIntroducerRecommendation.getRationale().toString());
    protoIntroducerRecommendationBuilder.setVariant(espressoIntroducerRecommendation.getVariant().toString());
    protoIntroducerRecommendationBuilder.setScore(espressoIntroducerRecommendation.getScore());
    protoIntroducerRecommendationBuilder.setCreatedTime(espressoIntroducerRecommendation.getCreatedTime());
    protoIntroducerRecommendationBuilder.setModifiedTime(espressoIntroducerRecommendation.getModifiedTime());
    protoIntroducerRecommendationBuilder.setStatus(IntroducerRecommendationStatus.valueOf(
        espressoIntroducerRecommendation.getStatus().toString()));
    return protoIntroducerRecommendationBuilder.build();
  }

  private WarmIntroRecommendationKey constructWarmIntroRecommendationKey(SeatUrn seatUrn,
      proto.com.linkedin.common.MemberUrn leadMemberUrn) {
    return WarmIntroRecommendationKey.newBuilder()
        .setSeatUrn(seatUrn)
        .setLeadMemberUrn(leadMemberUrn)
        .build();
  }
}
