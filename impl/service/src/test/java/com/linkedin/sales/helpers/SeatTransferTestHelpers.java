package com.linkedin.sales.helpers;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.ownershiptransfer.OwnershipTransferEntityType;
import com.linkedin.ownershiptransfer.OwnershipTransferKey;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;

import static com.linkedin.ownershiptransfer.OwnershipTransferEntityType.*;


public final class SeatTransferTestHelpers {

  public static final Long OWNERSHIP_TRANSFER_ID = 123L;
  public static final SeatUrn ACTOR = new SeatUrn(0L);
  public static final SeatUrn SOURCE_SEAT = new SeatUrn(1L);
  public static final SeatUrn TARGET_SEAT = new SeatUrn(2L);
  public static final ContractUrn SOURCE_CONTRACT = new ContractUrn(3L);
  public static final ContractUrn TARGET_CONTRACT = new ContractUrn(4L);
  public static final OwnershipTransferEntityType ENTITY_NOTES_ENTITY_TYPE = SALES_ENTITY_NOTES;

  private SeatTransferTestHelpers() {

  }

  public static OwnershipTransferRequest getOwnershipTransferRequest() {
    return new OwnershipTransferRequest()
        .setId(OWNERSHIP_TRANSFER_ID)
        .setSourceSeat(SOURCE_SEAT)
        .setTargetSeat(TARGET_SEAT)
        .setSourceContract(SOURCE_CONTRACT)
        .setTargetContract(TARGET_CONTRACT);
  }

  public static OwnershipTransferKey getOwnershipTransferKey() {
    return new OwnershipTransferKey()
        .setId(OWNERSHIP_TRANSFER_ID)
        .setContractUrn(SOURCE_CONTRACT);
  }

}

