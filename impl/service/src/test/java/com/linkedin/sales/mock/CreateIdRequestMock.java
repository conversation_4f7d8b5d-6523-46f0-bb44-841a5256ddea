package com.linkedin.sales.mock;

import com.linkedin.restli.common.IdResponse;
import com.linkedin.restli.common.ResourceMethod;


public class CreateIdRequestMock<K_RESPONSE> extends RequestMockBase<IdResponse<K_RESPONSE>, CreateIdRequestMock> {
  public CreateIdRequestMock(K_RESPONSE key) {
    super(ResourceMethod.CREATE, new IdResponse<>(key));
  }
  //TODO declare GetRequest specific matcher here
}