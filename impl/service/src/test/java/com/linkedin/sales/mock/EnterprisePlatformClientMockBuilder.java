package com.linkedin.sales.mock;

import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.enterprise.account.ApplicationInstance;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResult;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResultArray;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.identity.Profile;
import com.linkedin.enterprise.identity.ProfileKey;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import java.util.List;


public class EnterprisePlatformClientMockBuilder extends ParSeqRestClientMockBase {
  private static final EmptyRecord EMPTY_RECORD = new EmptyRecord();

  public EnterprisePlatformClientMockBuilder whenFindEnterpriseProfileByEmail(List<Profile> returnProfiles) {
    this.addRequestMock(new FindRequestMock<>(returnProfiles)
        .eqName("externalIdOrEmail"));
    return this;
  }

  public EnterprisePlatformClientMockBuilder whenCreateEnterpriseProfileFromCrmUser(ProfileKey pk, EmailAddressUrn givenEmailUrn) {

    RequestMatcher profile2givenKeyMatcher = request -> {
      if (request.getInputRecord() == null || !(request.getInputRecord() instanceof Profile)) {
        return false;
      }
      Profile profile = (Profile) request.getInputRecord();
      EmailAddressUrn emailUrn = profile.getPrimaryEmailAddress();

      return pk.getAccount().equals(profile.getAccount())
          && givenEmailUrn.equals(emailUrn);
    };

    this.addRequestMock(new CreateIdRequestMock<>(new ComplexResourceKey<>(pk, EMPTY_RECORD))
        .hasInput()
        .add(profile2givenKeyMatcher)
    );
    return this;
  }

  public EnterprisePlatformClientMockBuilder whenAssignLicenses(List<BulkActionBatchInputKeyResult> responseEntities) {
    BulkActionBatchResult bulkActionBatchResult = new BulkActionBatchResult();
    BulkActionBatchInputKeyResultArray array = new BulkActionBatchInputKeyResultArray();
    array.addAll(responseEntities);
    bulkActionBatchResult.setInputKeyResults(array);
    this.addRequestMock(new ActionRequestMock<>(bulkActionBatchResult)
        .eqName("processBatch")
    );
    return this;
  }

  public EnterprisePlatformClientMockBuilder whenGetAppInstanceWithMappedContract(ApplicationInstance returnedVal) {
    this.addRequestMock(new GetRequestMock<>(returnedVal)
    );
    return this;
  }

  public EnterprisePlatformClient build() {
    return new EnterprisePlatformClient(mockParSeqRestClient());
  }

}

