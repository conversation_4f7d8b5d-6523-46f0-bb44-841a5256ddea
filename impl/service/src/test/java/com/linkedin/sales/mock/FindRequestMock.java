package com.linkedin.sales.mock;

import com.linkedin.data.template.RecordTemplate;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.ResourceMethod;
import java.util.List;
import org.mockito.Mockito;


public class FindRequestMock<ENTITY extends RecordTemplate> extends RequestMockBase<CollectionResponse<ENTITY>, FindRequestMock> {
  public FindRequestMock(List<ENTITY> entities) {
    super(ResourceMethod.FINDER, toCollectionResponse(entities));
  }

  public FindRequestMock(CollectionResponse<ENTITY> collectionResponse) {
    super(ResourceMethod.FINDER, collectionResponse);
  }

  private static <T extends RecordTemplate> CollectionResponse<T> toCollectionResponse(List<T> entities) {
    CollectionResponse<T> cResp = Mockito.mock(CollectionResponse.class);
    Mockito.when(cResp.getElements()).thenReturn(entities);
    return cResp;
  }
  //TODO declare GetRequest specific matcher here
}