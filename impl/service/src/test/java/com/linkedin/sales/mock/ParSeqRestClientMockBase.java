package com.linkedin.sales.mock;

import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Request;

import static  org.mockito.Mockito.*;


/**
 * based on Mockito
 */
public class ParSeqRestClientMockBase extends RequestInvoker {

  public ParSeqRestClient mockParSeqRestClient() {
    ParSeqRestClient parSeqRestClient = mock(ParSeqRestClient.class);

    when(parSeqRestClient.createTask(any())).thenAnswer(invocation -> {
      Request request = invocation.getArgument(0);
      return Task.value(super.invokeRequest(request));
    });

    return parSeqRestClient;
  }


}
