package com.linkedin.sales.mock;

import com.linkedin.restli.client.Request;
import com.linkedin.restli.client.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


public class RequestInvoker {

  private final List<RequestMock> _requestMocks = new ArrayList<>();

  public void addRequestMock(RequestMock requestMock) {
    _requestMocks.add(requestMock);
  }


  public <T> Response<T> invokeRequest(Request<T> request) {
    List<RequestMock> matched = _requestMocks.stream()
        .filter(requestMock -> requestMock.isMatch(request))
        .collect(Collectors.toList());
    if (matched.size() == 0) {
      throw new IllegalStateException("cannot find any matched requestMock: " + _requestMocks + ", request:" + request);
    }
    if (matched.size() > 1) {
      throw new IllegalStateException("duplicate reqestMocks matched: [" + matched + "], request:" + request);
    }
    return matched.get(0).getResponse();
  }
}
