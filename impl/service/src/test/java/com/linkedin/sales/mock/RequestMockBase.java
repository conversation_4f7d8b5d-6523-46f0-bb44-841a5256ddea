package com.linkedin.sales.mock;

import com.linkedin.data.template.RecordTemplate;
import com.linkedin.restli.client.Request;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.ResourceMethod;
import com.linkedin.restli.internal.client.ResponseImpl;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
/**
 *
 * This base impl makes a RequestMock also as a RequestMatcher repo, and
 * provided a list of convenient methods for requestMatcher creation.
 *
 * The reason why I don't want to
 *
 *
 * @param <RESPONSE>
 */
public class RequestMockBase<RESPONSE, THIS extends RequestMockBase> implements RequestMock<RESPONSE> {

  protected final RESPONSE _responseEntity;
  private final List<RequestMatcher> _requestMatchers = new ArrayList<>();

  public RequestMockBase(ResourceMethod resourceMethod, RESPONSE responseEntity) {
    _responseEntity = responseEntity;
    this.eqMethod(resourceMethod);
  }

  @Override
  public boolean isMatch(Request<RESPONSE> request) {
    for(RequestMatcher matcher : _requestMatchers) {
      if (!matcher.isMatch(request)) {
        return false;
      }
    }
    return true;
  }

  @Override
  public Response<RESPONSE> getResponse() {
    //TODO error response simulation
    return new ResponseImpl<>(
        HttpStatus.S_200_OK.getCode(),
        Collections.emptyMap(),
        Collections.emptyList(),
        _responseEntity,
        null);
  }

  public THIS add(RequestMatcher matcher) {
    _requestMatchers.add(matcher);
    return (THIS) this;
  }


  public THIS eqMethod(ResourceMethod method) {
    return add(request -> method == request.getMethod());
  }

  public THIS eqName(String methodName) {
    return add(request -> methodName.equals(request.getMethodName()));
  }

  public THIS eqInput(RecordTemplate inputRecord) {
    return add(request -> inputRecord.equals(request.getInputRecord()));
  }

  public THIS hasInput() {
    return add(request -> request.getInputRecord() != null);
  }

  public THIS hasPathKey(String pathKey) {
    return add(request -> {
      Map<String, Object> pathKeyMap = request.getPathKeys();
      if (pathKeyMap == null) {
        return false;
      }
      return pathKeyMap.containsKey(pathKey);
    });
  }

  public THIS hasQueryParam(String qParamName) {
    return add(request -> {
      Map<String, Class<?>> pClasses = request.getQueryParamClasses();
      if (pClasses == null) {
        return false;
      }
      return pClasses.get(qParamName) != null;
    });
  }

  public THIS eqQueryParam(String qParamName, @Nullable Class<?> qParamClass, @Nullable Object qParamValue) {
    return add(request -> {
      Map<String, Object> pObjs = request.getQueryParamsObjects();
      Map<String, Class<?>> pClasses = request.getQueryParamClasses();
      if (pObjs == null || pClasses == null) {
        return false;
      }
      Class<?> clz = pClasses.get(qParamName);
      if (qParamClass != null && !qParamClass.isAssignableFrom(clz)) {
        return false;
      }

      Object obj = pObjs.get(qParamName);
      if (qParamValue != null && !qParamValue.equals(obj)) {
        return false;
      }

      return true;
    });
  }
}
