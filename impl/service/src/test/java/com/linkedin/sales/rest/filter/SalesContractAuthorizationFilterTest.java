package com.linkedin.sales.rest.filter;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.RecordTemplate;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.ParSeqUnitTestHelper;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.ResourceMethod;
import com.linkedin.restli.server.RestLiRequestData;
import com.linkedin.restli.server.RestLiRequestDataImpl;
import com.linkedin.restli.server.filter.FilterRequestContext;
import com.linkedin.restli.server.filter.FilterResourceModel;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.restligateway.util.GatewayCallerIdentity;
import com.linkedin.sales.admin.ContractStatus;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesEntitlementArray;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRoleAllocation;
import com.linkedin.sales.admin.SeatRoleAllocationArray;
import com.linkedin.sales.admin.SeatRoleArray;
import com.linkedin.sales.admin.SeatStatus;
import com.linkedin.sales.client.common.SalesContractClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.SalesContractService;
import java.net.URI;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;



import static com.linkedin.sales.admin.SeatRole.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;


public class SalesContractAuthorizationFilterTest {
  @Mock
  private SalesSeatClient _salesSeatClient;
  @Mock
  private SalesContractAuthorizationFilter _salesContractAuthorizationFilter;
  @Mock
  private AllowListedEndPointsForAuthorizationService _allowListedEndPointsForAuthorizationService;

  private static final Long TEST_CONTRACT_ID = 111L;
  private final ContractUrn TEST_CONTRACT_URN = new ContractUrn(TEST_CONTRACT_ID);
  private static final String CONTRACT_PARAM = "contract";
  private final MemberUrn TEST_MEMBER_URN = new MemberUrn(333L);
  private static final int TEST_APP_ID = 444;
  private SalesContractClient _salesContractClient;
  private LixService _lixService;
  private SalesContractService _salesContractService;

  @BeforeEach
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);
    GatewayCallerFinder gatewayCallerFinder = mockGatewayCallerFinder(TEST_MEMBER_URN.getIdAsLong(), TEST_APP_ID, 1);
    _salesSeatClient = Mockito.mock(SalesSeatClient.class);
    _salesContractClient = Mockito.mock(SalesContractClient.class);
    Engine parseqEngine = Mockito.mock(Engine.class);
    _lixService = Mockito.mock(LixService.class);
    _salesContractService = Mockito.mock(SalesContractService.class);
    when(_lixService.isContractBasedLixEnabled(any(), any())).thenReturn(Task.value(Boolean.TRUE));

    Set<String> allowListedEndpoints = new HashSet<>();
    allowListedEndpoints.add("resource1:finder:finderMethodName");
    allowListedEndpoints.add("resource2:action:actionMethodName");
    allowListedEndpoints.add("DataPullRequest:get");
    allowListedEndpoints.add("DataPullRequest:action:actionName");

    _allowListedEndPointsForAuthorizationService =
        new AllowListedEndPointsForAuthorizationService(allowListedEndpoints);

    _salesContractAuthorizationFilter =
        new SalesContractAuthorizationFilter(parseqEngine, _salesContractService,
            _allowListedEndPointsForAuthorizationService, gatewayCallerFinder);
    ParSeqUnitTestHelper parSeqUnitTestHelper = new ParSeqUnitTestHelper();
    parSeqUnitTestHelper.setUp();
  }

  @Test
  public void testAllowListedEndPointAuthService() {
    FilterRequestContext requestContext = getRequestContext(ResourceMethod.GET, "DataPullRequest", null);
    Assert.assertTrue(_allowListedEndPointsForAuthorizationService.isAllowListedForAuthorization(requestContext));
  }

  @Test
  public void testContractAuthorizationForTier3Contract() {
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(5));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE)
        .setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(LSS_ADMIN_SEAT);
    seatRoleArray.add(SALES_SEAT_TIER3);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlementArray.add(SalesEntitlement.CRM_BASICS);
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    CompletableFuture<Void> future = _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test
  public void testContractAuthorizationForTier3AdminOnlyContract() {
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(LSS_ADMIN_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    CompletableFuture<Void> future = _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test
  public void testContractAuthorizationForTier2AdminOnlyContract() {
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER2).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(LSS_ADMIN_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    CompletableFuture<Void> future = _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test(expected = PromiseException.class)
  public void testContractAuthorizationForTier1Contract() {
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER1).setAllocation(1));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE)
        .setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(LSS_ADMIN_SEAT);
    seatRoleArray.add(SALES_SEAT_TIER1);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    try {
      _salesContractAuthorizationFilter.onRequest(requestContext);
    } catch (Exception p) {
      p.getCause();
      Assert.assertTrue(p.getCause().getMessage().contains("does not have external reporting permission"));
      throw p;
    }
  }

  @Test
  public void testContractAuthorizationForUsageReportOnlyContract() {
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(SALES_USAGE_REPORT_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    CompletableFuture<Void> future = _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test
  public void testContractAuthorizationForTier3WithNoAllocationContract() {
    //mock restli-gw finder
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(0));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(SALES_USAGE_REPORT_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    //no allocation so no entitlements returned

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    try {
      _salesContractAuthorizationFilter.onRequest(requestContext);
    } catch (Exception p) {
      p.getCause();
      Assert.assertTrue(p.getCause().getMessage().contains("does not have external reporting permission"));
      throw p;
    }
  }

  @Test
  public void testContractAuthorizationForTieredSeatOnlyPermission() {
    //mock restli-gw finder
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(SALES_SEAT_TIER2);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    try {
      _salesContractAuthorizationFilter.onRequest(requestContext);
      Assert.fail("Expected PromiseException");
    } catch (PromiseException p) {
      p.getCause();
      Assert.assertTrue(p.getCause().getMessage().contains("User does not have external reporting permission"));
    }
  }

  @Test
  public void testInactiveContract() {
    //mock restli-gw finder
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(SALES_SEAT_TIER3);
    seatRoleArray.add(LSS_ADMIN_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlementArray.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlementArray.add(SalesEntitlement.CRM_BASICS);
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    try {
      _salesContractAuthorizationFilter.onRequest(requestContext);
      Assert.fail("Expected exception");
    } catch (Exception p) {
      Assert.assertTrue(p.getCause().getMessage().contains("contract is not active"), p.getCause().getMessage());
    }
  }

  @Test
  public void testAuthWithPostCall() {
    //mock restli-gw finder
    DataMap postData = new DataMap();
    postData.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext = getRequestContext(ResourceMethod.ACTION, "DataPullRequest", null);
    Mockito.when(requestContext.getActionName()).thenReturn("actionName");

    RecordTemplate mockRecordTemplate = Mockito.mock(RecordTemplate.class);
    Mockito.when(mockRecordTemplate.data()).thenReturn(postData);

    RestLiRequestData restLiRequestData = new RestLiRequestDataImpl.Builder().entity(mockRecordTemplate).build();
    Mockito.when(requestContext.getRequestData()).thenReturn(restLiRequestData);

    SeatRoleAllocationArray allocationArray = new SeatRoleAllocationArray();
    allocationArray.add(new SeatRoleAllocation().setRole(SALES_SEAT_TIER3).setAllocation(10));

    SalesContract contract = new SalesContract().setStatus(ContractStatus.ACTIVE).setSeatRoleAllocations(allocationArray);

    SeatRoleArray seatRoleArray = new SeatRoleArray();
    seatRoleArray.add(SALES_USAGE_REPORT_SEAT);

    SalesEntitlementArray seatEntitlementArray = new SalesEntitlementArray();
    seatEntitlementArray.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesSeat seat =
        new SalesSeat().setContract(TEST_CONTRACT_URN).setStatus(SeatStatus.ACTIVE).setRoles(seatRoleArray).setEntitlements(seatEntitlementArray);
    List<SalesSeat> seats = Collections.singletonList(seat);

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));
    when(_salesContractClient.getContract(eq(TEST_CONTRACT_ID), any())).thenReturn(Task.value(contract));

    CompletableFuture<Void> future =  _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test
  public void testNoAuthForContracts() {
    //mock restli-gw finder
    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "salesContracts", null);
    Mockito.when(requestContext.getFinderName()).thenReturn("contractsByMember");

    CompletableFuture<Void> future = _salesContractAuthorizationFilter.onRequest(requestContext);
    Assert.assertFalse(future.isCompletedExceptionally());
  }

  @Test
  public void testNoSeatInContract() {
    //mock restli-gw finder
    DataMap queryParam = new DataMap();
    queryParam.put(CONTRACT_PARAM, TEST_CONTRACT_URN.toString());

    FilterRequestContext requestContext =
        getRequestContext(ResourceMethod.GET, "DataPullRequest", queryParam);

    List<SalesSeat> seats = Collections.emptyList();

    when(_salesSeatClient.findByMember(eq(TEST_MEMBER_URN), eq(Collections.singleton(TEST_CONTRACT_URN)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(seats));

    try {
      _salesContractAuthorizationFilter.onRequest(requestContext);
      Assert.fail("Expected PromiseException");
    } catch (PromiseException p) {
      Assert.assertTrue(
          p.getCause().getMessage().contains("User does not have any assigned permission on Sales Navigator contract"),
          p.getCause().getMessage());
    }
  }

  /**
   * Create a mock request, it will contain LinkedIn-Gateway-AppId header to identify an external request if required
   */
  private FilterRequestContext getRequestContext(ResourceMethod method, String resourceName,
      DataMap queryParam) {
    FilterRequestContext context = Mockito.mock(FilterRequestContext.class);
    FilterResourceModel resourceModel = Mockito.mock(FilterResourceModel.class);

    Mockito.when(context.getMethodType()).thenReturn(method);
    Mockito.when(context.getFilterResourceModel()).thenReturn(resourceModel);
    Mockito.when(context.getFilterResourceModel().getResourceName()).thenReturn(resourceName);
    Mockito.when(context.getRequestURI()).thenReturn(URI.create("/test"));
    Mockito.when(context.getQueryParameters()).thenReturn(queryParam);

    return context;
  }

  /**
   * Mock gateway caller finder
   * @param memberId
   * @param appId
   * @param scopeId
   * @return
   */
  public static GatewayCallerFinder mockGatewayCallerFinder(long memberId, int appId, int scopeId) {
    GatewayCallerIdentity token = mock(GatewayCallerIdentity.class);
    GatewayCallerFinder finder = mock(GatewayCallerFinder.class);
    when(finder.getCaller()).thenReturn(token);
    when(token.getMemberId()).thenReturn(memberId);
    when(token.getApplicationId()).thenReturn(appId);
    when(token.getScopeId()).thenReturn(scopeId);
    return finder;
  }
}