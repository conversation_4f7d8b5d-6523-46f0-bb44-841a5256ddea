package com.linkedin.sales.service;

import com.linkedin.common.CrmUserUrnArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmUserUrn;
import com.linkedin.common.urn.CsUserUrn;
import com.linkedin.common.urn.EmailAddressUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseBudgetGroupUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.crm.CrmUser;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.account.ApplicationInstance;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyResult;
import com.linkedin.enterprise.bulk.BulkActionBatchInputKeyStatus;
import com.linkedin.enterprise.bulk.BulkActionBatchResult;
import com.linkedin.enterprise.bulk.BulkCrmUserSyncContext;
import com.linkedin.enterprise.bulk.BulkLicenseAssignmentContext;
import com.linkedin.enterprise.identity.ProfileKey;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.BatchCreateIdRequest;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.BatchCreateIdResponse;
import com.linkedin.restli.common.CreateIdStatus;
import com.linkedin.restli.common.ErrorResponse;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.internal.common.AllProtocolVersions;
import com.linkedin.sales.client.EmailAddressClient;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.client.integration.CrmUserClient;
import com.linkedin.sales.mock.EnterprisePlatformClientMockBuilder;
import com.linkedin.sales.test.ParSeqRestClientMockBuilder;
import com.linkedin.util.Pair;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;



public class CrmUserOnboardingBulkActionServiceTest extends BaseEngineParTest {
  @Mock
  private CrmUserClient _crmUserClient;
  @Mock
  private EnterprisePlatformClient _enterprisePlatformClient;
  @Mock
  private EmailAddressClient _emailAddressClient;

  private CrmUserOnboardingBulkActionService _crmUserOnboardingBulkActionService;

  private CsUserUrn _csUserUrn = new CsUserUrn(1L);
  private EnterpriseAccountUrn accountUrn = new EnterpriseAccountUrn(123456L);
  private EnterpriseApplicationInstanceUrn _enterpriseApplicationInstanceUrn =
      new EnterpriseApplicationInstanceUrn(accountUrn, 1234L);
  private ContractUrn _contractUrn = new ContractUrn(10L);
  private static final ApplicationInstance APPLICATION_INSTANCE = new ApplicationInstance().setContractId(10L);

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);

    _crmUserOnboardingBulkActionService =
        new CrmUserOnboardingBulkActionService(this.getEngine(), _crmUserClient, _enterprisePlatformClient,
            _emailAddressClient);
  }

  @Test
  public void testValidate_noLicenseAssignment() {
    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    _crmUserOnboardingBulkActionService.validate(context, _csUserUrn);
  }

  @Test
  public void testValidate_withLicenseAssignment() throws Exception {
    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    BulkLicenseAssignmentContext licenseAssignmentContext = new BulkLicenseAssignmentContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    context.setLicenseAssignmentContext(licenseAssignmentContext);

    _crmUserOnboardingBulkActionService.validate(context, _csUserUrn);
  }




  @Test
  public void testProcessBatch_emptyKeys() throws Exception {
    when(_enterprisePlatformClient.getAppInstanceWithMappedContract(eq(_enterpriseApplicationInstanceUrn), any()))
        .thenReturn(Task.value(Optional.of(APPLICATION_INSTANCE)));
    when(_crmUserClient.findByQuery(eq(_contractUrn), any(), any(), anyBoolean(), anyInt(), anyInt())).thenReturn(
        Task.value(Collections.emptyList()));
    //MOCK EnterprisePlatformClient
    //MOCK EmailAddressClient
    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    BulkLicenseAssignmentContext licenseAssignmentContext = new BulkLicenseAssignmentContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    context.setLicenseAssignmentContext(licenseAssignmentContext);

    EnterpriseAccountUrn acctUrn = _enterpriseApplicationInstanceUrn.getAccountEntity();
    CrmUserUrnArray crmUserUrnArray = new CrmUserUrnArray();

    BulkActionBatchResult result = _crmUserOnboardingBulkActionService.processBatch(
        crmUserUrnArray, context, _csUserUrn);
    Assert.assertNotNull(result);
  }

  @Test
  public void testProcessBatch_oneKey() throws Exception {

    CrmUserUrn crmUserUrn1 = CrmUserUrn.deserialize("urn:li:crmUser:(urn:li:crmInstance:(SystemA,instanceA),externalUserId001)");

    //MOCK EnterpriseCrmClient
    CrmUser crmUser1 = new CrmUser();
    crmUser1.setEmail("<EMAIL>");
    crmUser1.setFirstName("FN");
    crmUser1.setLastName("LN");
    crmUser1.setUrn(crmUserUrn1);
    mockFindCrmUsers(Collections.singletonList(crmUser1));

    List<Pair<EmailAddressUrn, ProfileKey>> emailUrnAndPks = Arrays.asList(
        new Pair<>(new EmailAddressUrn(1001L), new ProfileKey().setAccount(accountUrn).setProfileId(1234L))
        //TODO add more ...
    );

    //MOCK emailAddressUrn
    List<CreateIdStatus<Long>> createIdStatuses = emailUrnAndPks.stream()
        .map(pair -> newCreateIdStatusSuccess(pair.getFirst().getEmailAddressIdEntity()))
        .collect(Collectors.toList());
    EmailAddressClient emailAddressClient = mock_EmailAddressClient_batchCreate(createIdStatuses);

    //MOCK successful AssignLicenses & FindEnterpriseProfileByEmail
    List<BulkActionBatchInputKeyResult> licenseAssignedResults = mockSuccessfulAssignedLicenseBulkActionResults(
        accountUrn,
        emailUrnAndPks.stream()
            .map(pair -> pair.getSecond().getProfileId())
            .collect(Collectors.toList()));

    EnterprisePlatformClientMockBuilder enterprisePlatformClientMockBuilder = new EnterprisePlatformClientMockBuilder()
        .whenFindEnterpriseProfileByEmail(Collections.emptyList())
        .whenAssignLicenses(licenseAssignedResults)
        .whenGetAppInstanceWithMappedContract(APPLICATION_INSTANCE);

    emailUrnAndPks.forEach(pair -> {
      enterprisePlatformClientMockBuilder.whenCreateEnterpriseProfileFromCrmUser(pair.getSecond(), pair.getFirst());
    });
    EnterprisePlatformClient enterprisePlatformClient = spy(enterprisePlatformClientMockBuilder.build());
    CrmUserOnboardingBulkActionService crmUserOnboardingBulkActionService = new CrmUserOnboardingBulkActionService(
        this.getEngine(),
        _crmUserClient,
        enterprisePlatformClient,
        emailAddressClient);

    CrmUserUrnArray inputKeys = new CrmUserUrnArray();
    inputKeys.add(crmUserUrn1);

    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    BulkLicenseAssignmentContext bulkLicenseAssignmentContext = new BulkLicenseAssignmentContext()
        .setBudgetGroup(new EnterpriseBudgetGroupUrn(accountUrn, 5453L))
        .setLicenseApplicationInstance(new EnterpriseApplicationInstanceUrn(accountUrn, 2345L));

    context.setLicenseAssignmentContext(bulkLicenseAssignmentContext);

    BulkActionBatchResult result = crmUserOnboardingBulkActionService.processBatch(inputKeys, context,
        _csUserUrn);
    System.out.println("result: " + result);

    verify(enterprisePlatformClient)
        .findEnterpriseProfileByEmail(eq(_enterpriseApplicationInstanceUrn), any(), any());

    verify(enterprisePlatformClient)
        .assignLicenses(any(), any(), any());

    verify(enterprisePlatformClient, times(1))
        .createEnterpriseProfileFromCrmUser(any(), any(), any(), any(), eq(_enterpriseApplicationInstanceUrn), any());

    Assert.assertNotNull(result);
  }


  @Test
  public void testProcessBatch_noCrmUser() throws Exception {
    CrmUserUrn crmUserUrn1 = CrmUserUrn.deserialize("urn:li:crmUser:(urn:li:crmInstance:(SystemA,instanceA),externalUserId001)");
    mockFindCrmUsers(Collections.emptyList()); // no crmUser

    //MOCK emailAddressUrn
    EmailAddressClient emailAddressClient = mock_EmailAddressClient_batchCreate(Collections.emptyList());

    //MOCK successful AssignLicenses & FindEnterpriseProfileByEmail
    List<BulkActionBatchInputKeyResult> licenseAssignedResults = mockSuccessfulAssignedLicenseBulkActionResults(
        accountUrn,
        Collections.emptyList());

    EnterprisePlatformClientMockBuilder enterprisePlatformClientMockBuilder = new EnterprisePlatformClientMockBuilder()
        .whenFindEnterpriseProfileByEmail(Collections.emptyList())
        .whenAssignLicenses(licenseAssignedResults)
        .whenGetAppInstanceWithMappedContract(APPLICATION_INSTANCE);

    CrmUserOnboardingBulkActionService crmUserOnboardingBulkActionService = new CrmUserOnboardingBulkActionService(
        this.getEngine(),
        _crmUserClient,
        enterprisePlatformClientMockBuilder.build(),
        emailAddressClient);
    CrmUserUrnArray inputKeys = new CrmUserUrnArray();
    inputKeys.add(crmUserUrn1);

    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    BulkLicenseAssignmentContext bulkLicenseAssignmentContext = new BulkLicenseAssignmentContext()
        .setBudgetGroup(new EnterpriseBudgetGroupUrn(accountUrn, 5453L))
        .setLicenseApplicationInstance(new EnterpriseApplicationInstanceUrn(accountUrn, 2345L));

    context.setLicenseAssignmentContext(bulkLicenseAssignmentContext);

    BulkActionBatchResult result = crmUserOnboardingBulkActionService.processBatch(inputKeys, context,
        _csUserUrn);
    System.out.println("result: " + result);
    Assert.assertEquals(result.getInputKeyResults().get(0).getMessage().getKey(),"PLUGIN_CRM_USER_ONBOARDING_PROFILE_CREATION_ERROR");
  }

  @Test
  public void testProcessBatch_emptyCrmUserEmail() throws Exception {

    CrmUserUrn crmUserUrn1 = CrmUserUrn.deserialize("urn:li:crmUser:(urn:li:crmInstance:(SystemA,instanceA),externalUserId001)");
    //MOCK EnterpriseCrmClient
    CrmUser crmUser1 = new CrmUser();
    crmUser1.setFirstName("FN");
    crmUser1.setLastName("LN");
    crmUser1.setUrn(crmUserUrn1);
    mockFindCrmUsers(Collections.singletonList(crmUser1));

    List<Pair<EmailAddressUrn, ProfileKey>> emailUrnAndPks = Arrays.asList(
        new Pair<>(new EmailAddressUrn(1001L), new ProfileKey().setAccount(accountUrn).setProfileId(1234L))
        //TODO add more ...
    );

    //MOCK emailAddressUrn
    EmailAddressClient emailAddressClient = mock_EmailAddressClient_batchCreate(Collections.emptyList());

    //MOCK successful AssignLicenses & FindEnterpriseProfileByEmail
    List<BulkActionBatchInputKeyResult> licenseAssignedResults = mockSuccessfulAssignedLicenseBulkActionResults(
        accountUrn,
        Collections.emptyList());

    EnterprisePlatformClientMockBuilder enterprisePlatformClientMockBuilder = new EnterprisePlatformClientMockBuilder()
        .whenFindEnterpriseProfileByEmail(Collections.emptyList())
        .whenAssignLicenses(licenseAssignedResults)
        .whenGetAppInstanceWithMappedContract(APPLICATION_INSTANCE);

    CrmUserOnboardingBulkActionService crmUserOnboardingBulkActionService = new CrmUserOnboardingBulkActionService(
        this.getEngine(),
        _crmUserClient,
        enterprisePlatformClientMockBuilder.build(),
        emailAddressClient);
    CrmUserUrnArray inputKeys = new CrmUserUrnArray();
    inputKeys.add(crmUserUrn1);

    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    BulkLicenseAssignmentContext bulkLicenseAssignmentContext = new BulkLicenseAssignmentContext()
        .setBudgetGroup(new EnterpriseBudgetGroupUrn(accountUrn, 5453L))
        .setLicenseApplicationInstance(new EnterpriseApplicationInstanceUrn(accountUrn, 2345L));

    context.setLicenseAssignmentContext(bulkLicenseAssignmentContext);

    BulkActionBatchResult result = crmUserOnboardingBulkActionService.processBatch(inputKeys, context, _csUserUrn);
    System.out.println("result: " + result);

    Assert.assertEquals(result.getInputKeyResults().get(0).getMessage().getKey(),"PLUGIN_CRM_USER_ONBOARDING_PROFILE_CREATION_ERROR");
  }

  @Test
  public void testProcessBatch_noEmailAddressUrn() throws Exception {

    CrmUserUrn crmUserUrn1 = CrmUserUrn.deserialize("urn:li:crmUser:(urn:li:crmInstance:(SystemA,instanceA),externalUserId001)");
    //MOCK EnterpriseCrmClient
    CrmUser crmUser1 = new CrmUser();
    crmUser1.setEmail("<EMAIL>");
    crmUser1.setFirstName("FN");
    crmUser1.setLastName("LN");
    crmUser1.setUrn(crmUserUrn1);
    mockFindCrmUsers(Collections.singletonList(crmUser1));

    List<Pair<EmailAddressUrn, ProfileKey>> emailUrnAndPks = Arrays.asList(
        new Pair<>(new EmailAddressUrn(1001L), new ProfileKey().setAccount(accountUrn).setProfileId(1234L))
        //TODO add more ...
    );

    //MOCK emailAddressUrn
    List<CreateIdStatus<Long>> createIdStatuses = emailUrnAndPks.stream()
        .map(pair -> newCreateIdStatusSuccess(pair.getFirst().getEmailAddressIdEntity()))
        .collect(Collectors.toList());
    CreateIdStatus<Long> getOrCreateResult = newCreateIdStatusFailure(1001L);
    EmailAddressClient emailAddressClient = mock_EmailAddressClient_batchCreate(Collections.singletonList(getOrCreateResult));

    //MOCK successful AssignLicenses & FindEnterpriseProfileByEmail
    List<BulkActionBatchInputKeyResult> licenseAssignedResults = mockSuccessfulAssignedLicenseBulkActionResults(
        accountUrn,
        Collections.emptyList());

    EnterprisePlatformClientMockBuilder enterprisePlatformClientMockBuilder = new EnterprisePlatformClientMockBuilder()
        .whenFindEnterpriseProfileByEmail(Collections.emptyList())
        .whenAssignLicenses(licenseAssignedResults)
        .whenGetAppInstanceWithMappedContract(APPLICATION_INSTANCE);

    CrmUserOnboardingBulkActionService crmUserOnboardingBulkActionService = new CrmUserOnboardingBulkActionService(
        this.getEngine(),
        _crmUserClient,
        enterprisePlatformClientMockBuilder.build(),
        emailAddressClient);
    CrmUserUrnArray inputKeys = new CrmUserUrnArray();
    inputKeys.add(crmUserUrn1);

    BulkCrmUserSyncContext context = new BulkCrmUserSyncContext();
    context.setCrmSyncApplicationInstance(_enterpriseApplicationInstanceUrn);
    BulkLicenseAssignmentContext bulkLicenseAssignmentContext = new BulkLicenseAssignmentContext()
        .setBudgetGroup(new EnterpriseBudgetGroupUrn(accountUrn, 5453L))
        .setLicenseApplicationInstance(new EnterpriseApplicationInstanceUrn(accountUrn, 2345L));

    context.setLicenseAssignmentContext(bulkLicenseAssignmentContext);

    BulkActionBatchResult result = crmUserOnboardingBulkActionService.processBatch(inputKeys, context, _csUserUrn);
    System.out.println("result: " + result);

    Assert.assertEquals(result.getInputKeyResults().get(0).getMessage().getKey(),"PLUGIN_CRM_USER_ONBOARDING_PROFILE_CREATION_ERROR");
  }

  private static List<BulkActionBatchInputKeyResult> mockSuccessfulAssignedLicenseBulkActionResults(EnterpriseAccountUrn accountUrn, List<Long> profileIds) {
    return profileIds.stream().map(profileId -> new BulkActionBatchInputKeyResult()
        .setInputKey(new EnterpriseProfileUrn(accountUrn, profileId))
        .setStatus(BulkActionBatchInputKeyStatus.SUCCEEDED)
        .setMessage(null, SetMode.IGNORE_NULL))
        .collect(Collectors.toList());
  }

  private static CreateIdStatus<Long> newCreateIdStatusSuccess(long key) {
    return new CreateIdStatus<>(HttpStatus.S_201_CREATED.getCode(),
        key,
        null,
        AllProtocolVersions.RESTLI_PROTOCOL_2_0_0.getProtocolVersion());
  }

  private static CreateIdStatus<Long> newCreateIdStatusFailure(long key) {
    return new CreateIdStatus<>(HttpStatus.S_500_INTERNAL_SERVER_ERROR.getCode(),
        key,
        new ErrorResponse().setMessage("unknownError"),
        AllProtocolVersions.RESTLI_PROTOCOL_2_0_0.getProtocolVersion());
  }

  private void mockFindCrmUsers(List<CrmUser> crmUsers) {
    when(_crmUserClient.findByQuery(eq(_contractUrn), any(), any(), anyBoolean(), anyInt(), anyInt())).thenReturn(
        Task.value(crmUsers));
  }

  private EmailAddressClient mock_EmailAddressClient_batchCreate(List<CreateIdStatus<Long>> elements) {
    Response<BatchCreateIdResponse<Long>> response = mock(Response.class);
    BatchCreateIdResponse<Long> bciResopnse = new BatchCreateIdResponse<>(elements);
    when(response.getEntity()).thenReturn(bciResopnse);

    ParSeqRestClient parseqReqClient = ParSeqRestClientMockBuilder.getInstance()
        .mockCreateTask(BatchCreateIdRequest.class, Task.value(response))
        .build();

    return new EmailAddressClient(parseqReqClient, this.getEngine());
  }
}
