package com.linkedin.sales.service;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.google.common.primitives.Ints;
import com.linkedin.lix.executor.EvaluationContext;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.lix.dsl.v2.api.impl.LixUserContextImpl;
import com.linkedin.lix.mp.client.LixConstants;
import com.linkedin.lix.mp.client.LixEvaluationResult;
import com.linkedin.lix.mp.client.LixExtendedClient;
import com.linkedin.lix.mp.client.builder.MultiEntityLixEvaluationRequest;
import com.linkedin.lix.mp.client.builder.MultiEntityLixEvaluationRequestBuilder;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRoleArray;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.utils.LixUtils;
import java.util.Map;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class LixServiceTest extends BaseEngineParJunitJupiterTest {
  private static final MemberUrn MEMBER_URN = new MemberUrn(123L);
  private static final SeatUrn SEAT_URN = new SeatUrn(100L);
  private static final String LIX_KEY_1 = "lix-under-test1";
  private static final String LIX_KEY_2 = "lix-under-test2";
  private static final ContractUrn CONTRACT_URN = new ContractUrn(200L);
  @Mock
  private SalesSeatClient _salesSeatClient;
  @Mock
  private LixExtendedClient _lixExtendedClient;
  private LixService _lixService;

  @BeforeEach
  public void setUp() {
    _lixService = new LixService(_lixExtendedClient, _salesSeatClient);
  }

  @Test
  public void testIsMemberBasedLixEnabledForSeat() {
    SalesSeat seat = new SalesSeat().setMember(MEMBER_URN);
    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), any(), any(), any())).thenReturn(
        Task.value(seat));
    when(_lixExtendedClient.getTreatment(eq(MEMBER_URN), eq(LIX_KEY_1), eq(null))).thenReturn(LixConstants.FALLTHROUGH_TREATMENT);

    Assert.assertFalse(runAndWait(_lixService.isMemberBasedLixEnabledForSeat(SEAT_URN, LIX_KEY_1)));
  }

  @Test
  public void testIsEEPLixEnabledForSeat_contractEnabled() {
    SalesSeat seat = new SalesSeat().setMember(MEMBER_URN).setContract(CONTRACT_URN);
    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), any(), any(), any())).thenReturn(Task.value(seat));
    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .addNamedUrn(MemberUrn.ENTITY_TYPE, MEMBER_URN)
        .addNamedUrn(SeatUrn.ENTITY_TYPE, SEAT_URN)
        .setTestkey(LIX_KEY_1)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result = new LixEvaluationResult(CONTRACT_URN,123, LixUtils.ENABLED,123,123, null,null,true);
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest))).thenReturn(Task.value(result));
    Assert.assertTrue(runAndWait(_lixService.isEEPLixEnabledForSeat(SEAT_URN, LIX_KEY_1)));
  }

  @Test
  public void testIsEEPLixEnabledForSeat_memberEnabled() {
    SalesSeat seat = new SalesSeat().setMember(MEMBER_URN).setContract(CONTRACT_URN);
    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), any(), any(), any())).thenReturn(Task.value(seat));
    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .addNamedUrn(MemberUrn.ENTITY_TYPE, MEMBER_URN)
        .addNamedUrn(SeatUrn.ENTITY_TYPE, SEAT_URN)
        .setTestkey(LIX_KEY_1)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result = new LixEvaluationResult(MEMBER_URN,123,LixUtils.ENABLED,123,123, null,null,true);
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest))).thenReturn(Task.value(result));
    Assert.assertTrue(runAndWait(_lixService.isEEPLixEnabledForSeat(SEAT_URN, LIX_KEY_1)));
  }

  @Test
  public void testBatchGetIsEEPLixEnabledForSeat() {
    SalesSeat seat = new SalesSeat().setMember(MEMBER_URN).setContract(CONTRACT_URN);
    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), any(), any(), any())).thenReturn(Task.value(seat));
    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest1 = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .addNamedUrn(MemberUrn.ENTITY_TYPE, MEMBER_URN)
        .addNamedUrn(SeatUrn.ENTITY_TYPE, SEAT_URN)
        .setTestkey(LIX_KEY_1)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result1 = new LixEvaluationResult(CONTRACT_URN,123,LixUtils.ENABLED,123,123, null,null,true);
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest1))).thenReturn(Task.value(result1));

    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .addNamedUrn(MemberUrn.ENTITY_TYPE, MEMBER_URN)
        .addNamedUrn(SeatUrn.ENTITY_TYPE, SEAT_URN)
        .setTestkey(LIX_KEY_2)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result2 = new LixEvaluationResult(MEMBER_URN,123,LixUtils.CONTROL,123,123, null,null,true);
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest))).thenReturn(Task.value(result2));

    Map<String, Boolean> lixMap = runAndWait(_lixService.batchGetIsEEPLixEnabledForSeat(SEAT_URN, ImmutableSet.of(LIX_KEY_1,LIX_KEY_2)));
    Assertions.assertTrue(lixMap.get(LIX_KEY_1));
    Assertions.assertFalse(lixMap.get(LIX_KEY_2));
  }

  @Test
  public void testIsContractBasedLixEnabled() {
    // Mock remote calls
    when(_lixExtendedClient.getTreatment(eq(CONTRACT_URN), eq(LIX_KEY_1), any())).thenReturn(LixUtils.ENABLED);

    // Check result
    Assertions.assertTrue(runAndWait(_lixService.isContractBasedLixEnabled(CONTRACT_URN, LIX_KEY_1)));
  }

  @Test
  public void testIsContractBasedEEPLixWithoutAnalysisEnabled() {
    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .setTestkey(LIX_KEY_1)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result = new LixEvaluationResult(CONTRACT_URN,123,LixUtils.ENABLED,123,1, null,null,true);
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest))).thenReturn(Task.value(result));

    // Run tested method without analysis run
    Assertions.assertTrue(runAndWait(_lixService.isContractBasedEEPLixEnabled(CONTRACT_URN,LIX_KEY_1)));
  }

  @Test
  public void testIsContractBasedEEPLixWithAnalysisEnabled() {
    MultiEntityLixEvaluationRequest multiEntityLixEvaluationRequest = new MultiEntityLixEvaluationRequestBuilder()
        .addNamedUrn(ContractUrn.ENTITY_TYPE, CONTRACT_URN)
        .addNamedUrn(MemberUrn.ENTITY_TYPE, MEMBER_URN)
        .setTestkey(LIX_KEY_1)
        .setLixUserContext(new LixUserContextImpl().put(LixService.MP_NAME_PLACEHOLDER, LixService.MP_NAME_LSS_MT))
        .build();
    LixEvaluationResult result = new LixEvaluationResult(CONTRACT_URN,123,LixUtils.ENABLED,123,123, null,null,true);

    // Mock remote calls
    when(_lixExtendedClient.getTreatmentAsync(eq(multiEntityLixEvaluationRequest))).thenReturn(Task.value(result));

    // Run tested method with analysis run
    Assertions.assertTrue(runAndWait(_lixService.isContractBasedEEPLixEnabled(CONTRACT_URN, LIX_KEY_1, MEMBER_URN)));
  }

  @Test
  public void testIsContractBasedLixWithAdminEnabled() {
    // Set up EvaluationContext
    EvaluationContext context = new EvaluationContext();
    context.put(LixUtils.CONTRACT_ID_NUM_PROPERTY, Ints.checkedCast(CONTRACT_URN.getIdAsLong()));
    context.put(LixUtils.IS_ADMIN_BOOLEAN_PROPERTY, true);

    // Mock remote calls
    SalesSeat seat = new SalesSeat().setMember(MEMBER_URN);
    SeatRoleArray roles = new SeatRoleArray(1);
    roles.add(com.linkedin.sales.admin.SeatRole.LSS_ADMIN_SEAT);
    seat.setRoles(roles);

    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), eq(CONTRACT_URN), any(), any())).thenReturn(Task.value(seat));
    when(_lixExtendedClient.getTreatment(eq(CONTRACT_URN), eq(LIX_KEY_1))).thenReturn(LixUtils.ENABLED);

    // Check result
    Assertions.assertTrue(runAndWait(_lixService.isContractBasedLixWithAdminEnabled(CONTRACT_URN, SEAT_URN, LIX_KEY_1)));
  }

  @Test
  public void testGetLssSavedLeadAccountBatchCreateConcurrencyLevelTreatmentValue() {
    // Mock remote calls
    when(_lixExtendedClient.getTreatment(eq(SEAT_URN), eq(LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL), any())).thenReturn("concurrency_level_10");

    // Run tested method
    int result = runAndWait(_lixService.getEntityBatchCreateConcurrencyLevel(SEAT_URN, LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL));

    // Check result
    Assertions.assertEquals(result, 10);
  }

  @Test
  public void testGetLssSavedLeadAccountBatchCreateConcurrencyLevelDefaultValue() {
    // Mock remote calls
    when(_lixExtendedClient.getTreatment(eq(SEAT_URN), eq(LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL), isNull())).thenReturn("10");

    // Run tested method
    int result = runAndWait(_lixService.getEntityBatchCreateConcurrencyLevel(SEAT_URN,
        LixUtils.LSS_SAVED_LEAD_ACCOUNT_BATCH_CREATE_CONCURRENCY_LEVEL));

    // Check result
    org.testng.Assert.assertEquals(result, LixUtils.DEFAULT_ENTITY_BATCH_CREATE_CONCURRENCY_LEVEL);
  }
}