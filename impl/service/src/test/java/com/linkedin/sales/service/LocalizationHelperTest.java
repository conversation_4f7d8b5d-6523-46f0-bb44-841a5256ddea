package com.linkedin.sales.service;

import com.linkedin.i18n.resource.DynamicResourceBundleManager;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import java.util.Locale;
import java.util.ResourceBundle;
import org.mockito.Mock;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class LocalizationHelperTest {
  private static final java.util.Locale ENGLISH_LOCALE = Locale.US;

  @Mock
  private DynamicResourceBundleManager _dynamicResourceBundleManager;

  private ResourceBundle _resourceBundle;
  private LocalizationHelper _localizationHelper;

  @BeforeMethod
  public void setup() {
    _dynamicResourceBundleManager = mock(DynamicResourceBundleManager.class);
    _resourceBundle = ResourceBundle.getBundle("com.linkedin.sales.lss-mt", java.util.Locale.US);
    when(_dynamicResourceBundleManager.getBundle(any(), any())).thenReturn(_resourceBundle);
    _localizationHelper = new LocalizationHelper(_dynamicResourceBundleManager);
  }

  @Test
  public void testGetLocalizedListNameAndDescription() {
    assertThat(_localizationHelper.getLocalizedListName(ListSource.CRM_SYNC, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.CRM_SYNC, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.CRM_PERSON_ACCOUNT, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_SYNC, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_SYNC, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_PERSON_ACCOUNT, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.CRM_BLUEBIRD, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_BLUEBIRD, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.BUYER_INTEREST, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.BUYER_INTEREST, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.CRM_AT_RISK_OPPORTUNITY, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_AT_RISK_OPPORTUNITY, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.RECOMMENDATION, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.RECOMMENDATION, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.LEADS_TO_FOLLOW_UP, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.LEADS_TO_FOLLOW_UP, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.NEW_EXECS_IN_SAVED_ACCOUNTS, ListType.LEAD, ENGLISH_LOCALE )).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.BOOK_OF_BUSINESS, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.BOOK_OF_BUSINESS, ListType.ACCOUNT, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListName(ListSource.AUTO_PROSPECTOR_REC, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.AUTO_PROSPECTOR_REC, ListType.LEAD, ENGLISH_LOCALE)).isNotNull();
  }

  @Test
  public void testAccountListDescription() {
    String expectedDescription = "Includes accounts that you own in CRM. Rules for accounts that you see can be adjusted by clicking the Customize button. "
        + "Changes may take up to 48 hours to take effect.";
    assertThat(_localizationHelper.getLocalizedListDescription(ListSource.CRM_SYNC, ListType.ACCOUNT, ENGLISH_LOCALE))
        .isEqualTo(expectedDescription);
  }
}
