package com.linkedin.sales.service;

import com.linkedin.badge.internal.BadgeType;
import com.linkedin.badge.internal.BadgeTypeArray;
import com.linkedin.badge.internal.MemberBadges;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.EngineBuilder;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.messaging.GraphDistancesClient;
import com.linkedin.sales.client.messaging.MemberBadgesClient;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import org.mockito.Mockito;


public class ProfileVisibilityServiceTest {
  @Mock
  private GraphDistancesClient _graphDistancesClient;

  @Mock
  private MemberBadgesClient _memberBadgesClient;

  @BeforeTest
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  private final int numCores = Runtime.getRuntime().availableProcessors();
  private final ExecutorService taskScheduler = Executors.newFixedThreadPool(numCores + 1);
  private final ScheduledExecutorService timerScheduler = Executors.newSingleThreadScheduledExecutor();

  private final Engine engine = new EngineBuilder()
      .setTaskExecutor(taskScheduler)
      .setTimerScheduler(timerScheduler)
      .build();

  @Test
  public void testIsProfileVisible_profileInNetwork() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    Mockito.doReturn(Task.value(true))
        .when(profileVisibilityService)
        .isProfileInNetwork(viewer, viewee);

    Mockito.doReturn(Task.value(false))
        .when(profileVisibilityService)
        .isOpenLink(viewee);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isProfileVisible(viewer, viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testIsProfileVisible_openLink() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    Mockito.doReturn(Task.value(false))
        .when(profileVisibilityService)
        .isProfileInNetwork(viewer, viewee);

    Mockito.doReturn(Task.value(true))
        .when(profileVisibilityService)
        .isOpenLink(viewee);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isProfileVisible(viewer, viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testIsProfileVisible_notVisible() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    Mockito.doReturn(Task.value(false))
        .when(profileVisibilityService)
        .isProfileInNetwork(viewer, viewee);

    Mockito.doReturn(Task.value(false))
        .when(profileVisibilityService)
        .isOpenLink(viewee);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isProfileVisible(viewer, viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertFalse(ret);
  }

  @Test
  public void testIsProfileInNetwork_outOfNetwork() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    Mockito.doReturn(Task.value(-1))
        .when(_graphDistancesClient)
        .getGraphDistance(viewer, viewee);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isProfileInNetwork(viewer, viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertFalse(ret);
  }

  @Test
  public void testIsProfileInNetwork_inNetwork() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    Mockito.doReturn(Task.value(3))
        .when(_graphDistancesClient)
        .getGraphDistance(viewer, viewee);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isProfileInNetwork(viewer, viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testIsOpenLink_opneLink() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewee = new MemberUrn(5678L);
    MemberBadges memberBadges = buildMemberBadges(viewee, BadgeType.OPENLINK);
    Mockito.doReturn(Task.value(memberBadges))
        .when(_memberBadgesClient)
        .getMemberBadges(5678L);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isOpenLink(viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testIsOpenLink_notPpneLink() throws Exception {
    //=== prepare test input and mock ===
    ProfileVisibilityService profileVisibilityService =
        Mockito.spy(createProfileVisibilityService());

    MemberUrn viewee = new MemberUrn(5678L);
    MemberBadges memberBadges = buildMemberBadges(viewee, BadgeType.JOBSEEKER);
    Mockito.doReturn(Task.value(memberBadges))
        .when(_memberBadgesClient)
        .getMemberBadges(5678L);

    //=== run tested method ===
    Task<Boolean> task = profileVisibilityService.isOpenLink(viewee);
    engine.run(task);
    task.await();
    Boolean ret = task.get();

    //=== check result ===
    Assert.assertFalse(ret);
  }

  private MemberBadges buildMemberBadges(MemberUrn memberUrn, BadgeType badgeType) {
    MemberBadges memberBadges = new MemberBadges();
    memberBadges.setMemberID(memberUrn);
    BadgeTypeArray badgeTypes = new BadgeTypeArray();
    badgeTypes.add(badgeType);
    memberBadges.setBadges(badgeTypes);
    return memberBadges;
  }

  private ProfileVisibilityService createProfileVisibilityService() {
    return new ProfileVisibilityService(_graphDistancesClient, _memberBadgesClient);
  }

}
