package com.linkedin.sales.service;

import com.linkedin.analytics.QueryResponse;
import com.linkedin.analytics.Row;
import com.linkedin.analytics.RowArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.RestLiResponseException;
import com.linkedin.restli.common.ErrorResponse;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.sales.monitoring.CounterMetricsSensor;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.PinotUtils;
import com.linkedin.salesactivities.SalesActivityTotal;
import com.linkedin.salesactivities.SalesActivityType;
import java.util.Arrays;
import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


public class SalesActivityTotalsJobServiceTest extends ServiceUnitTest {
  private final ContractUrn _contractUrn = new ContractUrn(1234L);
  private final SeatUrn _seatUrn = new SeatUrn(6789L);
  private final MemberUrn _memberUrn = new MemberUrn(2468L);
  private final Map<SalesActivityType, Long> _targetHashMap = new HashMap<>();
  private SalesActivityTotalsJobService _salesActivityTotalsJobService;

  @Mock
  private PinotUtils _pinotUtils;

  @Mock
  private CounterMetricsSensor _counterMetricsSensor;

  @Mock
  private LixService _lixService;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    _salesActivityTotalsJobService = new SalesActivityTotalsJobService(_pinotUtils, _counterMetricsSensor);
    QueryResponse monthResponse = new QueryResponse();
    QueryResponse dayResponse = generateMockList(Collections.singletonList("SAVED_ACCOUNT,7"));

    constructTargetMap();
    when(_pinotUtils.runPinotQuery(any(), any())).thenReturn(Task.value(monthResponse))
        .thenReturn(Task.value(dayResponse));
    when(_lixService.isContractBasedLixEnabled(any(), any())).thenReturn(Task.value(true));

    constructTargetMap();
  }

  private void constructTargetMap() {
    for (SalesActivityType activityType : SalesActivityType.values()) {
      if (activityType == SalesActivityType.$UNKNOWN) {
        continue;
      }
      if (activityType == SalesActivityType.SAVED_ACCOUNT) {
        _targetHashMap.put(activityType, 7L);
      } else {
        _targetHashMap.put(activityType, 0L);
      }
    }
  }

  private QueryResponse generateMockList(List<String> results) {
    QueryResponse queryResponse = new QueryResponse();
    RowArray rows = new RowArray();
    for (String result : results) {
      List<String> parts = Arrays.asList(result.split(","));

      Row.ValueArray values = new Row.ValueArray();
      values.add(0, Row.Value.createWithDouble(Double.parseDouble(parts.get(1))));
      values.add(1, Row.Value.createWithString(parts.get(0)));
      Row row = new Row();
      row.setValue(values);
      rows.add(row);
    }
    queryResponse.setRows(rows);
    return queryResponse;
  }

  private QueryResponse generateMockActivitiesList() {
    List<String> results = Collections.singletonList("SAVED_ACCOUNT,3");
    return generateMockList(results);
  }

  private QueryResponse generateMockActivitiesListWithSnapActivity() {
    List<String> results = Arrays.asList("DUMMY_ACTIVITY,9", "SAVED_ACCOUNT,3", "SAVED_LEAD,4", "LI_PROFILE_VIEW,5");
    return generateMockList(results);
  }

  @Test
  public void testGetActivityTotalsWithSeatAndContract() {
    BasicCollectionResult<SalesActivityTotal> salesActivityTotals =
        await(_salesActivityTotalsJobService.getSalesActivityTotals(_seatUrn, _contractUrn));
    for (SalesActivityTotal activityTotal : salesActivityTotals.getElements()) {
      SalesActivityType activityType = activityTotal.getActivityType();
      Long actualCount = activityTotal.getCount();
      Long targetCount = _targetHashMap.get(activityType);
      Assert.assertEquals(actualCount, targetCount);
    }
  }

  @Test
  public void testGetActivityTotalsWithSeatAndContractError() {
    ErrorResponse errorResponse = new ErrorResponse().setStatus(HttpStatus.S_429_TOO_MANY_REQUESTS.getCode());
    RestLiResponseException backendException = new RestLiResponseException(errorResponse);
    when(_pinotUtils.runPinotQuery(any(), any())).thenReturn(Task.failure(backendException));
    try {
      BasicCollectionResult<SalesActivityTotal> salesActivityTotals =
          await(_salesActivityTotalsJobService.getSalesActivityTotals(_seatUrn, _contractUrn));
    }catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiResponseException.class);
    }
  }

  @Test
  public void testGetActivityTotalsWithMember() {
    BasicCollectionResult<SalesActivityTotal> salesActivityTotals =
        await(_salesActivityTotalsJobService.getSalesActivityTotals(_memberUrn));
    for (SalesActivityTotal activityTotal : salesActivityTotals.getElements()) {
      SalesActivityType activityType = activityTotal.getActivityType();
      Long actualCount = activityTotal.getCount();
      Long targetCount = _targetHashMap.get(activityType);
      Assert.assertEquals(actualCount, targetCount);
    }
  }

  @Test
  public void testGetActivityTotalsWithMemberError() {
    ErrorResponse errorResponse = new ErrorResponse().setStatus(HttpStatus.S_429_TOO_MANY_REQUESTS.getCode());
    RestLiResponseException backendException = new RestLiResponseException(errorResponse);
    when(_pinotUtils.runPinotQuery(any(), any())).thenReturn(Task.failure(backendException));
    RestLiResponseException exception = runAndWaitException(_salesActivityTotalsJobService.getSalesActivityTotals(_memberUrn), RestLiResponseException.class);
    Assert.assertEquals(exception, backendException);
  }

  @Test
  public void testGetActivityTotalsWithContract() {
    BasicCollectionResult<SalesActivityTotal> salesActivityTotals =
        await(_salesActivityTotalsJobService.getSalesActivityTotals(_contractUrn));
    for (SalesActivityTotal activityTotal : salesActivityTotals.getElements()) {
      SalesActivityType activityType = activityTotal.getActivityType();
      Long actualCount = activityTotal.getCount();
      Long targetCount = _targetHashMap.get(activityType);
      Assert.assertEquals(actualCount, targetCount);
    }
  }

  @Test
  public void testGetActivityTotalsWithContractError() {
    ErrorResponse errorResponse = new ErrorResponse().setStatus(HttpStatus.S_429_TOO_MANY_REQUESTS.getCode());
    RestLiResponseException backendException = new RestLiResponseException(errorResponse);
    when(_pinotUtils.runPinotQuery(any(), any())).thenReturn(Task.failure(backendException));
    RestLiResponseException exception = runAndWaitException(_salesActivityTotalsJobService.getSalesActivityTotals(_contractUrn), RestLiResponseException.class);
    Assert.assertEquals(exception, backendException);
  }

  @Test
  public void testRetrieveCompletionStatusFromStatisticsList() {
    Map<SalesActivityType, Long> expectedResult = new EnumMap<>(SalesActivityType.class);
    expectedResult.put(SalesActivityType.SAVED_ACCOUNT, 3L);
    Map<SalesActivityType, Long> result = _salesActivityTotalsJobService.retrieveCompletionStatusFromQueryResponse(
        generateMockActivitiesList());
    Assert.assertEquals(expectedResult, result);
  }

  @Test
  public void testFilteringWithSnapActivities() {
    Map<SalesActivityType, Long> expectedResult = new EnumMap<>(SalesActivityType.class);
    expectedResult.put(SalesActivityType.SAVED_LEAD, 4L);
    expectedResult.put(SalesActivityType.SAVED_ACCOUNT, 3L);
    Map<SalesActivityType, Long> result = _salesActivityTotalsJobService.retrieveCompletionStatusFromQueryResponse(
        generateMockActivitiesListWithSnapActivity());
    Assert.assertEquals(expectedResult, result);
  }
}
