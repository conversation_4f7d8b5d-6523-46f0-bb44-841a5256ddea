package com.linkedin.sales.service;

import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.PinotUtils;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;


/**
 * <AUTHOR>
 */
public class SalesAnalyticsExportJobServiceTest extends ServiceUnitTest{

  @Mock
  private PinotUtils _pinotUtils;

  private SalesAnalyticsExportJobService _salesAnalyticsExportJobService;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesAnalyticsExportJobService =
        new SalesAnalyticsExportJobService(_pinotUtils);
  }

  // There were no existing tests for SalesAnalyticsExportJobService.retrieveDataAvailability
  // Adding new tests is tricky because we need to mock _pinotUtils.runPinotQuery
  // Since we need to migrate retrieveDataAvailability to lss-reporting, not adding new tests

}
