package com.linkedin.sales.service;

import com.linkedin.common.ClosedTimeRange;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.lighthouse.client.queue.QueueType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.analytics.SalesAnalyticsExportJobsApiClient;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.SalesAnalyticsConstants;
import com.linkedin.salesanalytics.SalesAnalyticsStatus;
import java.util.concurrent.ExecutionException;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import proto.com.linkedin.sales.report.SalesAnalyticsExportDataAvailability;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJob;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJobStatus;
import proto.com.linkedin.sales.report.SalesAnalyticsExportJobType;

import static org.mockito.Mockito.*;


public class SalesAnalyticsExportJobServiceV2Test extends ServiceUnitTest {

  @Mock
  private LixService _lixService;
  @Mock
  private SalesAnalyticsExportJobsApiClient _salesAnalyticsExportJobsApiClient;
  @Mock
  private SalesAnalyticsExportJobService _salesAnalyticsExportJobService;

  private SalesAnalyticsExportJobServiceV2 _salesAnalyticsExportJobServiceV2;

  private static final long CONTRACT_ID = 123L;
  private static final long MEMBER_ID = 234L;
  private static final long JOB_ID = 435645L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final String ERROR_MESSAGE = "Error message";
  private static final String CSV_URL = "csv url";
  private static final long START_AT = 3456L;
  private static final long END_AT = 37687687L;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesAnalyticsExportJobServiceV2 =
        new SalesAnalyticsExportJobServiceV2(_lixService, _salesAnalyticsExportJobsApiClient,
            _salesAnalyticsExportJobService);
  }

  @Test
  public void testCreateSalesAnalyticsExportJobSuccess() throws ExecutionException {
    SalesAnalyticsExportJob exportJob = SalesAnalyticsExportJob.newBuilder().build();
    when(_salesAnalyticsExportJobsApiClient.submitJobRequest(any(), any(), any(), anyLong(), anyLong())).thenReturn(Task.value(exportJob));

    Long jobId = await(
        _salesAnalyticsExportJobServiceV2.createSalesAnalyticsExportJob(QueueType.SalesAnalyticsActivity(), START_AT,
            END_AT, CONTRACT_URN, MEMBER_URN));

    verify(_salesAnalyticsExportJobsApiClient).submitJobRequest(
        SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_DATA, CONTRACT_URN, MEMBER_URN, START_AT,
        END_AT);
  }

  @Test
  public void testCreateSalesAnalyticsExportJobException() throws ExecutionException {
    when(_salesAnalyticsExportJobsApiClient.submitJobRequest(SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_ACTIVITY_DATA,
        CONTRACT_URN, MEMBER_URN, START_AT, END_AT)).thenThrow(new RuntimeException(ERROR_MESSAGE));

    try {
      Long jobId = await(
          _salesAnalyticsExportJobServiceV2.createSalesAnalyticsExportJob(QueueType.SalesAnalyticsActivity(), START_AT,
              END_AT, CONTRACT_URN, MEMBER_URN));
    } catch (RuntimeException e) {
      Assert.assertEquals(e.getMessage().contains(ERROR_MESSAGE), true);
    }
  }

  @Test
  public void testGetSalesAnalyticsExportJobSuccess() throws ExecutionException {
    SalesAnalyticsExportJob exportJob = SalesAnalyticsExportJob.newBuilder()
        .setId(JOB_ID)
        .setCsvUrl(CSV_URL)
        .setStatus(SalesAnalyticsExportJobStatus.SalesAnalyticsExportJobStatus_JOB_COMPLETED)
        .build();
    when(_salesAnalyticsExportJobsApiClient.retrieveJobDetails(anyLong(), any(), any())).thenReturn(Task.value(exportJob));

    com.linkedin.salesanalytics.SalesAnalyticsExportJob result = runAndWait(
        _salesAnalyticsExportJobServiceV2.getSalesAnalyticsExportJob(MEMBER_URN, CONTRACT_URN,JOB_ID));

    Assert.assertEquals(result.getId(), JOB_ID);
    Assert.assertEquals(result.getDownloadUrl(), new Url(CSV_URL));
    Assert.assertEquals(result.getStatus(), SalesAnalyticsStatus.COMPLETED);
  }

  @Test
  public void testGetSalesAnalyticsExportJobException() throws ExecutionException {
    when(_salesAnalyticsExportJobsApiClient.retrieveJobDetails(JOB_ID, CONTRACT_URN, MEMBER_URN))
        .thenThrow(new RuntimeException(ERROR_MESSAGE));

    try {
      com.linkedin.salesanalytics.SalesAnalyticsExportJob result =
          await(_salesAnalyticsExportJobServiceV2.getSalesAnalyticsExportJob(MEMBER_URN, CONTRACT_URN, JOB_ID));
    } catch (RuntimeException e) {
      Assert.assertEquals(e.getMessage().contains(ERROR_MESSAGE), true);
    }
  }

  @Test
  public void testRetrieveDataAvailabilitySuccess() throws ExecutionException {
    when(_lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_USAGE_REPORTING_MIGRATE_DATA_AVAILABILITY_TO_LSS_REPORTING))).thenReturn(Task.value(true));
    proto.com.linkedin.common.ClosedTimeRange timeRange = proto.com.linkedin.common.ClosedTimeRange.newBuilder()
        .setStart(START_AT)
        .setEnd(END_AT)
        .build();
    SalesAnalyticsExportDataAvailability
        dataAvailability = SalesAnalyticsExportDataAvailability.newBuilder().setTimeRange(timeRange).build();
    when(_salesAnalyticsExportJobsApiClient.retrieveDataAvailability(
        SalesAnalyticsExportJobType.SalesAnalyticsExportJobType_SEAT_DATA, CONTRACT_URN)).thenReturn(
        Task.value(dataAvailability));

    ClosedTimeRange result = runAndWait(
        _salesAnalyticsExportJobServiceV2.retrieveDataAvailability(SalesAnalyticsConstants.SEAT_TABLE,
            CONTRACT_URN));

    Assert.assertEquals(result.getStart(), START_AT);
    Assert.assertEquals(result.getEnd(), END_AT);
  }

  @Test
  public void testRetrieveDataAvailabilityException() throws ExecutionException {
    when(_lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_USAGE_REPORTING_MIGRATE_DATA_AVAILABILITY_TO_LSS_REPORTING))).thenReturn(Task.value(true));
    RestLiServiceException failedExec = new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, ERROR_MESSAGE);
    when(_salesAnalyticsExportJobsApiClient.retrieveDataAvailability(any(), any())).thenThrow(failedExec);

    RestLiServiceException exec = runAndWaitException(
        _salesAnalyticsExportJobServiceV2.retrieveDataAvailability(SalesAnalyticsConstants.ACTIVITY_TABLE, CONTRACT_URN), RestLiServiceException.class);
    Assert.assertEquals(exec.getMessage(), ERROR_MESSAGE);
  }

  @Test
  public void testRetrieveDataAvailabilityLixDisabled() {
    when(_lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_USAGE_REPORTING_MIGRATE_DATA_AVAILABILITY_TO_LSS_REPORTING))).thenReturn(Task.value(false));
    ClosedTimeRange timeRange = new ClosedTimeRange()
        .setStart(START_AT)
        .setEnd(END_AT);
    when(_salesAnalyticsExportJobService.retrieveDataAvailability(any(), any())).thenReturn(Task.value(timeRange));

    ClosedTimeRange result = runAndWait(
        _salesAnalyticsExportJobServiceV2.retrieveDataAvailability(SalesAnalyticsConstants.SEAT_TABLE,
            CONTRACT_URN));

    Assert.assertEquals(result.getStart(), START_AT);
    Assert.assertEquals(result.getEnd(), END_AT);
  }
}
