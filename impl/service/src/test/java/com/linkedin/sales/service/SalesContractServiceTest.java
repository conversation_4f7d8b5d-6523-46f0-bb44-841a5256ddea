package com.linkedin.sales.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.restli.client.response.BatchKVResponse;
import com.linkedin.restli.client.testutils.MockBatchEntityResponseFactory;
import com.linkedin.restli.common.EntityResponse;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.Contract;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesEntitlementArray;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRoleAllocation;
import com.linkedin.sales.admin.SeatRoleAllocationArray;
import com.linkedin.sales.client.common.SalesContractClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.util.Pair;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.testng.Assert;

import static java.util.Collections.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class SalesContractServiceTest extends BaseEngineParJunitJupiterTest {
  @Mock
  private SalesContractClient _salesContractClient;
  @Mock
  private SalesSeatClient _salesSeatClient;
  @Mock
  private LixService _lixService;
  private SalesContractService _salesContractService;

  @BeforeEach
  public void setUp() {
    when(_lixService.isContractBasedLixEnabled(any(), any()));
    _salesContractService = new SalesContractService(_salesContractClient, _salesSeatClient, _lixService);
  }

  @Test
  public void test_getContractById() {
    SalesContract salesContract = new SalesContract();
    when(_salesContractClient.getContract(eq(1L), any())).thenReturn(Task.value(salesContract));

    SalesContract result = runAndWait(_salesContractService.getSalesContractById(1L));
    Assertions.assertNotNull(result);
  }

  @Test
  public void test_findContractByMember() {
    MemberUrn memberUrn = new MemberUrn(1234L);

    ContractUrn c1 = new ContractUrn(1L);
    SalesContract contract1 = new SalesContract().setId(c1.getContractIdEntity())
        .setSeatRoleAllocations(new SeatRoleAllocationArray(
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3).setAllocation(2),
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER2).setAllocation(2)));

    ContractUrn c2 = new ContractUrn(2L);
    SalesContract contract2 = new SalesContract().setId(c2.getContractIdEntity())
        .setSeatRoleAllocations(new SeatRoleAllocationArray(
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3).setAllocation(2),
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER2).setAllocation(2)));

    SalesEntitlementArray seatEntitlements1 = new SalesEntitlementArray();
    seatEntitlements1.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlements1.add(SalesEntitlement.CRM_BASICS);
    seatEntitlements1.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlements1.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    SalesEntitlementArray seatEntitlements2 = new SalesEntitlementArray();
    seatEntitlements2.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);

    List<SalesSeat> findContractsBySeats = new SalesSeatListBuilder().append(memberUrn, 10L, c1, seatEntitlements1,
            com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3, com.linkedin.sales.admin.SeatRole.LSS_ADMIN_SEAT)
        .append(memberUrn, 20L, c2, seatEntitlements2, com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER2)
        .build();
    when(_salesSeatClient.findByMember(any(MemberUrn.class), isNull(), any(EnterpriseApplicationUsageUrn.class),
        any(PagingContext.class), any())).thenReturn(Task.value(findContractsBySeats));

    BatchKVResponse<Long, EntityResponse<SalesContract>> contractResponse =
        MockBatchEntityResponseFactory.createWithPrimitiveKey(Long.class, SalesContract.class,
            ImmutableMap.of(1L, contract1, 2L, contract2), emptyMap(), emptyMap());
    when(_salesContractClient.getContracts(eq(Sets.newHashSet(1L, 2L)), any())).thenReturn(Task.value(contractResponse));

    List<Pair<SalesContract, Boolean>> results =
        runAndWait(_salesContractService.findSalesContractsByMember(memberUrn, new PagingContext(0, 50)));

    // verify
    Assert.assertTrue(results.stream().anyMatch(Pair::getSecond));
  }

  @Test
  public void test_checkMemberAllowedToUseSNAP_no_permission() {
    MemberUrn memberUrn = new MemberUrn(1234L);

    // test no assigned permission
    ContractUrn c2 = new ContractUrn(2L);

    List<SalesSeat> emptySeats = Collections.emptyList();
    when(_salesSeatClient.findByMember(eq(memberUrn), eq(Collections.singleton(c2)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(emptySeats));

    RestLiServiceException exception =
        runAndWaitException(_salesContractService.checkMemberAllowedToUseSNAP(c2, memberUrn), RestLiServiceException.class);
    Assert.assertTrue(exception.getMessage().contains("does not have any assigned permission"));
  }

  public void test_checkMemberAllowedToUseSNAP_advancedPlusContract_pass() {
    MemberUrn memberUrn = new MemberUrn(1234L);

    // test normal case
    ContractUrn c1 = new ContractUrn(1L);
    SalesContract contract1 = new SalesContract().setId(c1.getContractIdEntity())
        .setSeatRoleAllocations(new SeatRoleAllocationArray(
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3).setAllocation(2)));
    when(_salesContractClient.getContract(eq(c1.getContractIdEntity()), any())).thenReturn(Task.value(contract1));

    SalesEntitlementArray seatEntitlements = new SalesEntitlementArray();
    seatEntitlements.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlements.add(SalesEntitlement.CRM_BASICS);
    seatEntitlements.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlements.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    List<SalesSeat> findContractsBySeats = new SalesSeatListBuilder().append(memberUrn, 10L, c1, seatEntitlements,
        com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3, com.linkedin.sales.admin.SeatRole.LSS_ADMIN_SEAT).build();
    when(_salesSeatClient.findByMember(eq(memberUrn), eq(Collections.singleton(c1)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(findContractsBySeats));

    runAndWait(_salesContractService.checkMemberAllowedToUseSNAP(c1, memberUrn));
  }

  @Test
  public void test_checkMemberAllowedToUseSNAP_advancedContract_pass() {
    MemberUrn memberUrn = new MemberUrn(1234L);

    // Advanced contract
    ContractUrn c3 = new ContractUrn(3L);
    SalesContract contract3 = new SalesContract().setId(c3.getContractIdEntity())
        .setLocked(false)
        .setSeatRoleAllocations(new SeatRoleAllocationArray(
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER2).setAllocation(2)));
    when(_salesContractClient.getContract(eq(c3.getContractIdEntity()), any())).thenReturn(Task.value(contract3));

    SalesEntitlementArray seatEntitlements = new SalesEntitlementArray();
    seatEntitlements.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlements.add(SalesEntitlement.SALES_NAVIGATOR_ADMIN_BASICS);
    seatEntitlements.add(SalesEntitlement.USAGE_REPORTING_EXTERNAL_EXPORT);

    List<SalesSeat> findContractsBySeats = new SalesSeatListBuilder().append(memberUrn, 30L, c3, seatEntitlements,
        com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER2, com.linkedin.sales.admin.SeatRole.LSS_ADMIN_SEAT).build();
    when(_salesSeatClient.findByMember(eq(memberUrn), eq(Collections.singleton(c3)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(findContractsBySeats));

    runAndWait(_salesContractService.checkMemberAllowedToUseSNAP(c3, memberUrn));
  }

  @Test
  public void test_checkMemberAllowedToUseSNAP_seatHasNoReportPermission() {
    MemberUrn memberUrn = new MemberUrn(1234L);

    ContractUrn c3 = new ContractUrn(3L);
    SalesContract contract3 = new SalesContract().setId(c3.getContractIdEntity())
        .setSeatRoleAllocations(new SeatRoleAllocationArray(
            new SeatRoleAllocation().setRole(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3).setAllocation(2)));
    when(_salesContractClient.getContract(eq(c3.getContractIdEntity()), any())).thenReturn(Task.value(contract3));

    SalesEntitlementArray seatEntitlements = new SalesEntitlementArray();
    seatEntitlements.add(SalesEntitlement.SALES_NAVIGATOR_BASICS);
    seatEntitlements.add(SalesEntitlement.CRM_BASICS);

    List<SalesSeat> tier3Seats = new SalesSeatListBuilder().append(memberUrn, 30L, c3, seatEntitlements,
        com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3).build();
    when(_salesSeatClient.findByMember(eq(memberUrn), eq(Collections.singleton(c3)),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any())).thenReturn(Task.value(tier3Seats));
    RestLiServiceException exception =
        runAndWaitException(_salesContractService.checkMemberAllowedToUseSNAP(c3, memberUrn), RestLiServiceException.class);
    Assert.assertTrue(exception.getMessage().contains("does not have external reporting permission"));
  }

  @Test
  public void test_getContractsByHandraisePilotCompany_success() {
    Contract contract = new Contract().setContract(new ContractUrn(660872510L))
        .setName("")
        .setHasReportingAccess(false);

    List<Contract> result = runAndWait(_salesContractService.getContractsByHandraisePilotCompany(1009L));
    Assert.assertTrue(result.contains(contract));
  }

  @Test
  public void test_getContractsByHandraisePilotCompany_failure_nonPilotCompany() {
    List<Contract> result = runAndWait(_salesContractService.getContractsByHandraisePilotCompany(12345L));
    Assert.assertEquals(result, Collections.emptyList());
  }

  static class SalesSeatListBuilder {
    List<SalesSeat> seats = new ArrayList<>();

    SalesSeatListBuilder append(MemberUrn member, long seatId, ContractUrn contract,
        com.linkedin.sales.admin.SalesEntitlementArray seatEntitlements,
        com.linkedin.sales.admin.SeatRole... seatRoles) {
      com.linkedin.sales.admin.SeatRoleArray seatRoleArray =
          new com.linkedin.sales.admin.SeatRoleArray(Arrays.asList(seatRoles));
      seats.add(new SalesSeat().setId(seatId)
          .setMember(member)
          .setContract(contract)
          .setRoles(seatRoleArray)
          .setEntitlements(seatEntitlements));
      return this;
    }

    List<SalesSeat> build() {
      return seats;
    }
  }
}