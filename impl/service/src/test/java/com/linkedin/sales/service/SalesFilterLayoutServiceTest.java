package com.linkedin.sales.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.espresso.FilterLayout;
import com.linkedin.sales.espresso.FilterLayoutConfig;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salescustomfilterview.FilterEntityGroup;
import com.linkedin.salescustomfilterview.FilterLayoutColumn;
import com.linkedin.salescustomfilterview.FilterLayoutElement;
import com.linkedin.salescustomfilterview.FilterLayoutElementArray;
import com.linkedin.salescustomfilterview.SearchViewType;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.when;


/**
 * Test class for {@link SalesFilterLayoutService}
 * <AUTHOR>
 */
public class SalesFilterLayoutServiceTest extends ServiceUnitTest {

  @Test(description = "Test upsert filter layout")
  public void testUpsertFilterLayout() {
    Mocks mocks = new Mocks();
    FilterLayoutConfig dbFilterLayoutConfig = new FilterLayoutConfig();
    dbFilterLayoutConfig.setEntityNames(ImmutableList.of("ROLE", "PERSONAL"));
    dbFilterLayoutConfig.setEntityColumn(1);
    FilterLayout dbFilterLayout = new FilterLayout();
    dbFilterLayout.setContractUrn(Mocks.SERIALIZED_CONTRACT_URN);
    dbFilterLayout.setFilterOrder(new ImmutableMap.Builder<CharSequence, FilterLayoutConfig>()
        .put("OVERALL", dbFilterLayoutConfig).build());
    when(mocks._lssCustomFilterViewDB.upsertFilterLayout(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        eq(dbFilterLayout))).thenReturn(Task.value(Boolean.TRUE));
    com.linkedin.salescustomfilterview.FilterLayout mtFilterLayout = new com.linkedin.salescustomfilterview.FilterLayout();
    mtFilterLayout.setLayout(new FilterLayoutElementArray(ImmutableList.of(new FilterLayoutElement()
        .setGroup(FilterEntityGroup.OVERALL)
        .setConfig(new com.linkedin.salescustomfilterview.FilterLayoutConfig()
            .setEntityColumn(FilterLayoutColumn.RIGHT)
            .setEntities(new StringArray(ImmutableList.of("ROLE", "PERSONAL")))))));
    Boolean ret = runAndWait(mocks._salesFilterLayoutService.upsertFilterLayout(Mocks.SEAT_URN, Mocks.CONTRACT_URN,
        Mocks.SEARCH_VIEW_TYPE, mtFilterLayout));
    assertThat(ret).isTrue();
  }

  @Test(description = "Test delete filter layout")
  public void testDeleteFilterLayout() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deleteFilterLayout(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE)))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    HttpStatus ret = runAndWait(mocks._salesFilterLayoutService.deleteFilterLayout(Mocks.SEAT_URN, Mocks.SEARCH_VIEW_TYPE));
    assertThat(ret).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test(description = "Test getFilterLayout")
  public void testGetFilterLayout() {
    Mocks mocks = new Mocks();
    FilterLayoutConfig dbFilterLayoutConfig = new FilterLayoutConfig();
    dbFilterLayoutConfig.setEntityNames(ImmutableList.of("ROLE", "PERSONAL"));
    dbFilterLayoutConfig.setEntityColumn(1);
    FilterLayout dbFilterLayout = new FilterLayout();
    dbFilterLayout.setContractUrn(Mocks.SERIALIZED_CONTRACT_URN);
    dbFilterLayout.setFilterOrder(new ImmutableMap.Builder<CharSequence, FilterLayoutConfig>()
        .put("OVERALL", dbFilterLayoutConfig).build());
    when(mocks._lssCustomFilterViewDB.getFilterLayout(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE)))
        .thenReturn(Task.value(dbFilterLayout));
    com.linkedin.salescustomfilterview.FilterLayout
        ret = runAndWait(mocks._salesFilterLayoutService.getFilterLayout(Mocks.SEAT_URN, Mocks.SEARCH_VIEW_TYPE));
    com.linkedin.salescustomfilterview.FilterLayout mtFilterLayout = new com.linkedin.salescustomfilterview.FilterLayout();
    mtFilterLayout.setLayout(new FilterLayoutElementArray(ImmutableList.of(new FilterLayoutElement()
        .setGroup(FilterEntityGroup.OVERALL)
        .setConfig(new com.linkedin.salescustomfilterview.FilterLayoutConfig()
            .setEntityColumn(FilterLayoutColumn.RIGHT)
            .setEntities(new StringArray(ImmutableList.of("ROLE", "PERSONAL")))))));
    assertThat(ret).isEqualTo(mtFilterLayout);
  }

  static class Mocks {
    @Mock
    private final LssCustomFilterViewDB _lssCustomFilterViewDB;

    private static final String SERIALIZED_SEAT_URN = "urn:li:seat:2000";
    private static final String SERIALIZED_CONTRACT_URN = "urn:li:contract:8000";
    private static final String STRING_SEARCH_TYPE = "COLLAPSED_LEAD";
    private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
    private static final ContractUrn CONTRACT_URN = new ContractUrn(8000L);
    private static final SearchViewType SEARCH_VIEW_TYPE = SearchViewType.COLLAPSED_LEAD;

    private final SalesFilterLayoutService _salesFilterLayoutService;
    Mocks() {
      _lssCustomFilterViewDB = Mockito.mock(LssCustomFilterViewDB.class);
      _salesFilterLayoutService = new SalesFilterLayoutService(_lssCustomFilterViewDB);
    }
  }
}
