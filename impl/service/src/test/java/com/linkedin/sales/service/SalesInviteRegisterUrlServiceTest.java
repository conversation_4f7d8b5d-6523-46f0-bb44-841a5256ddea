package com.linkedin.sales.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.sales.test.MockUtils;

import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class SalesInviteRegisterUrlServiceTest extends BaseEngineParTest {

  private static final String INVITE_URL_PREFIX = "http://unit-test.linkedin.com/register";
  private SalesInviteRegisterUrlService salesInviteRegisterUrlService;
  private SalesCryptoService salesCryptoService = MockUtils.getSimpleSalesCryptoService();

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    salesInviteRegisterUrlService = new SalesInviteRegisterUrlService(
        INVITE_URL_PREFIX,
        salesCryptoService
    );
  }

  @Test
  public void test_generateEPRegistrationUrl_normal() {

    EnterpriseAccountUrn accountUrn = new EnterpriseAccountUrn(9999L);
    String url = salesInviteRegisterUrlService.generateLighthouseFrontendRegistrationUrl(
        new EnterpriseApplicationInstanceUrn(accountUrn, 1234L),
        new EnterpriseProfileUrn(accountUrn, 678L));
    System.out.println("EPRegistrationUrl: " + url);
    Assert.assertTrue(url.startsWith(INVITE_URL_PREFIX));

  }

  @Test
  public void test_toRegisterUrl_failure() throws Exception {
    ObjectMapper objMapper = Mockito.mock(ObjectMapper.class);
    Mockito.when(objMapper.writeValueAsString(Mockito.any()))
      .thenThrow(new TestJsonError());
    SalesInviteRegisterUrlService salesInviteRegisterUrlService = new SalesInviteRegisterUrlService(
        INVITE_URL_PREFIX,
        salesCryptoService,
        objMapper
    );
    //TEST Subject: generateEPRegistrationUrl
    Assert.assertThrows(() ->{
      EnterpriseAccountUrn accountUrn = new EnterpriseAccountUrn(9999L);
      salesInviteRegisterUrlService.generateLighthouseFrontendRegistrationUrl(
          new EnterpriseApplicationInstanceUrn(accountUrn, 1234L),
          new EnterpriseProfileUrn(accountUrn, 678L));
    });
  }

  private static class TestJsonError extends JsonProcessingException {
    public TestJsonError() {
      super("for test purpose only");
    }
  }
}
