package com.linkedin.sales.service;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.enterprise.EnterpriseLicenseType;
import com.linkedin.enterprise.account.ApplicationInstance;
import com.linkedin.enterprise.identity.ActivationLink;
import com.linkedin.enterprise.identity.Profile;
import com.linkedin.enterprise.license.LicenseAssignment;
import com.linkedin.handle.EmailAddress;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.restli.client.CreateIdRequest;
import com.linkedin.restli.client.GetRequest;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.sales.admin.ContractProvisionType;
import com.linkedin.sales.admin.SalesAccount;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRole;
import com.linkedin.sales.admin.SeatRoleArray;
import com.linkedin.sales.client.EmailClient;
import com.linkedin.sales.client.ep.EnterpriseProfileActivationLinksClient;
import com.linkedin.sales.test.MockUtils;
import com.linkedin.sales.test.ParSeqRestClientMockBuilder;
import com.linkedin.sales.test.RestliUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.Assert;
import org.xeril.util.url.URLBuilder;
import org.xeril.wafwk.gui.url.AdapterUrlFactory;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class SalesNavigatorEmailServiceTest extends BaseEngineParJunitJupiterTest {
  @Mock
  AdapterUrlFactory mockAdapterUrlFactory;
  private final ApplicationInstance applicationInstance = new ApplicationInstance().setContractId(1234L);
  private final ContractUrn contractUrn = new ContractUrn(1234L);
  private final MemberUrn senderUrn = new MemberUrn(12345L);
  private final EnterpriseAccountUrn accountUrn = new EnterpriseAccountUrn(123L);
  private final EnterpriseApplicationInstanceUrn
      applicationInstanceUrn = new EnterpriseApplicationInstanceUrn(accountUrn, 456L);
  private final EnterpriseProfileUrn enterpriseProfileUrn = new EnterpriseProfileUrn(accountUrn, 789L);
  private final SalesAccount accountV2 = new SalesAccount()
      .setId(123L)
      .setName("Test Account Name");
  private final Long seatId = 1234L;
  private final SeatUrn seatUrn = new SeatUrn(seatId);
  private final SalesSeat salesSeat = new SalesSeat()
      .setId(seatId)
      .setMember(new MemberUrn(1234L))
      .setContract(contractUrn)
      .setRoles(new SeatRoleArray(Collections.singletonList(
          SeatRole.SALES_SEAT_TIER2
      )));
  private URLBuilder url;

  @BeforeEach
  public void setUp() {
    URLBuilder builder = new URLBuilder();
    try {
      builder.setScheme("https");
      builder.setHost("www.linkedin-ei.com/");
      builder.setPath("sales/activate");
      builder.addQueryParameter("enterpriseProfile", enterpriseProfileUrn.toString());
      builder.addQueryParameter("applicationInstance", applicationInstanceUrn.toString());
    } catch (Exception e) {
      throw new RuntimeException("Failed to build a redirect url", e);
    }
    url = builder;
  }

  @Test
  public void testBuildLighthouseWebRedirectUrl() {
    AdapterUrlFactory mockAdapterUrlFactory = Mockito.mock(AdapterUrlFactory.class);
    url.addQueryParameter("isTLE", "false");
    when(mockAdapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(url);

    SalesNavigatorEmailService snEmailService = new SalesNavigatorEmailService(mockEmailClient(),
        MockUtils.mockSalesContractServiceGetContractByIdFailed(),
        MockUtils.mockSeatClientGetSeatWithAny(salesSeat),
        MockUtils.mockInviteRegisterUrlServiceByUrlWithAny("test.linkedin-ei.com/register"),
        MockUtils.getEnterpriseMappingClientMockBuilder()
            .mockGetEnterpriseProfileByAny(Task.value(new Profile())).build(),
        Mockito.mock(EnterpriseProfileActivationLinksClient.class),
        mockAdapterUrlFactory,
        MockUtils.mockSalesAccountClientGetAccountByIdForWelcomeEmail(accountV2)
    );

    Url actual = snEmailService.buildLighthouseWebRedirectUrl(applicationInstanceUrn, enterpriseProfileUrn, false);
    String expected = "https://www.linkedin-ei.com/sales/activate?enterpriseProfile=urn%3Ali%3AenterpriseProfile%3A%28urn%3Ali%3AenterpriseAccount%3A123%2C789%29&applicationInstance=urn%3Ali%3AenterpriseApplicationInstance%3A%28urn%3Ali%3AenterpriseAccount%3A123%2C456%29&isTLE=false";
    Assert.assertEquals(actual.toString(), expected);
  }

  @Test
  public void testBuildLighthouseWebRedirectUrlIsTLE() {
    AdapterUrlFactory mockAdapterUrlFactory = Mockito.mock(AdapterUrlFactory.class);
    url.addQueryParameter("isTLE", "true");
    when(mockAdapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(url);

    SalesNavigatorEmailService snEmailService = new SalesNavigatorEmailService(mockEmailClient(),
        MockUtils.mockSalesContractServiceGetContractByIdFailed(),
        MockUtils.mockSeatClientGetSeatWithAny(salesSeat),
        MockUtils.mockInviteRegisterUrlServiceByUrlWithAny("test.linkedin-ei.com/register"),
        MockUtils.getEnterpriseMappingClientMockBuilder()
            .mockGetEnterpriseProfileByAny(Task.value(new Profile())).build(),
        Mockito.mock(EnterpriseProfileActivationLinksClient.class),
        mockAdapterUrlFactory,
        MockUtils.mockSalesAccountClientGetAccountByIdForWelcomeEmail(accountV2)
    );

    Url actual = snEmailService.buildLighthouseWebRedirectUrl(applicationInstanceUrn, enterpriseProfileUrn, true);
    String expected = "https://www.linkedin-ei.com/sales/activate?enterpriseProfile=urn%3Ali%3AenterpriseProfile%3A%28urn%3Ali%3AenterpriseAccount%3A123%2C789%29&applicationInstance=urn%3Ali%3AenterpriseApplicationInstance%3A%28urn%3Ali%3AenterpriseAccount%3A123%2C456%29&isTLE=true";
    Assert.assertEquals(actual.toString(), expected);
  }

  @Test
  public void testGenerateInvitationEmailUrl() {
    MockUtils.EnterprisePlatformClientMockBuilder enterprisePlatformClientMockBuilder = MockUtils.getEnterpriseMappingClientMockBuilder();

    EnterpriseAccountUrn accountUrn = new EnterpriseAccountUrn(9999L);

    EnterpriseProfileUrn recipientProfileUrn = new EnterpriseProfileUrn(accountUrn, 688L);
    EnterpriseProfileUrn senderUrn = new EnterpriseProfileUrn(accountUrn, 1782L);

    List<LicenseAssignment> licenseAssignments = new ArrayList<>();
    LicenseAssignment licenseAssignment = new LicenseAssignment()
        .setContract(contractUrn)
        .setEnterpriseLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER2.getUrn());

    licenseAssignments.add(licenseAssignment);

    enterprisePlatformClientMockBuilder
        .mockFindLicenseAssignments(Task.value(licenseAssignments))
        .mockGetEpMappedContractUrn(Task.value(Optional.of(applicationInstance)));

    when(mockAdapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(url);

    SalesContract contract = new SalesContract()
        .setId(contractUrn.getContractIdEntity())
        .setProvisionType(ContractProvisionType.OFFLINE)
        .setMigratedToAccountCenter(true);

    ActivationLink link = new ActivationLink().setUrl(new Url(url.toString()));
    SalesNavigatorEmailService snEmailService = new SalesNavigatorEmailService(
        mockEmailClient(),
        MockUtils.mockSalesContractServiceGetContractByIdWithAny(contract),
        MockUtils.mockSeatClientGetSeatWithAny(salesSeat),
        MockUtils.mockInviteRegisterUrlServiceByUrlWithAny("test.linkedin-ei.com/register"),
        enterprisePlatformClientMockBuilder.build(),
        MockUtils.mockActivationLinkClientWithGenerateActivationLinkAny(link),
        mockAdapterUrlFactory,
        MockUtils.mockSalesAccountClientGetAccountByIdForInviteEmail(accountV2)
    );

    //Perform generateInvitationEmailUrl
    String sendResult = runAndWait(
        snEmailService.generateInvitationEmailUrl(recipientProfileUrn, new EnterpriseApplicationInstanceUrn(accountUrn, 1234L),
            senderUrn, contractUrn.getContractIdEntity(), false));

    Assert.assertEquals(sendResult, url.toString());
  }

  private static final String GUEST_EMAIL = "<EMAIL>";
  private static EmailClient mockEmailClient() {
    Response<EmailAddress> emailAddressResponse = RestliUtils.newSuccessResponse(new EmailAddress()
        .setId(1234L)
        .setEmailAddress(GUEST_EMAIL));

    Response<IdResponse<String>> mailSenderResponse = RestliUtils.newSuccessResponse(
        new IdResponse<>("email_resp_001"));

    return new EmailClient(ParSeqRestClientMockBuilder.getInstance()
        .mockCreateTask(GetRequest.class, Task.value(emailAddressResponse))
        .mockCreateTask(CreateIdRequest.class, Task.value(mailSenderResponse)).build());
  }
}