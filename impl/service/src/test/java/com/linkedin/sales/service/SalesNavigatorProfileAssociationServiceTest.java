package com.linkedin.sales.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.ChangeTimeStamps;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.DigitalmediaAssetUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.Channel;
import com.linkedin.crm.CrmLinkedInEntityMapping;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.DerivedMapping;
import com.linkedin.data.schema.PathSpec;
import com.linkedin.digitalmedia.AssetMediaArtifactFileIdentifiers;
import com.linkedin.digitalmedia.FileIdentifier;
import com.linkedin.digitalmedia.FileIdentifierArray;
import com.linkedin.digitalmedia.MediaArtifactFileIdentifiers;
import com.linkedin.digitalmedia.MediaArtifactFileIdentifiersArray;
import com.linkedin.identity.Profile;
import com.linkedin.identity.ProfilePicture;
import com.linkedin.noniterableprofileid.ProfileIdGenerator;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.CrmMappingClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.ds.rest.ProfileClient;
import com.linkedin.sales.ds.rest.VectorClient;
import com.linkedin.sales.model.ProfileAssociationsCrmContractModel;
import com.linkedin.sales.model.ProfileAssociationsServiceErrorCode;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.integration.SalesExternalizationService;
import com.linkedin.salesgateway.Partner;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociation;
import com.linkedin.salesgateway.SalesNavigatorProfileAssociationKey;
import com.linkedin.security.crypto.CryptoException;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.xeril.util.url.URLBuilder;
import org.xeril.wafwk.gui.url.AdapterUrlFactory;

import static org.mockito.Mockito.*;

public class SalesNavigatorProfileAssociationServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final String CRM_RECORD_ID = "fd89c380-d33a-e311-8c20-78e3b5103ee3";

  private String _crmInstanceId;
  private CrmInstanceUrn _crmInstanceUrn;

  @Mock
  private CrmPairingClient _crmPairingClient;
  @Mock
  private ProfileClient _profileClient;
  @Mock
  private SalesNavigatorProfileAssociationService _salesNavigatorProfileAssociationService;
  @Mock
  private AdapterUrlFactory _adapterUrlFactory;
  @Mock
  private ProfileIdGenerator _profileIdGenerator;
  @Mock
  private VectorClient _vectorClient;
  @Mock
  private CrmMappingClient _crmMappingClient;
  @Mock
  private SalesExternalizationService _salesExternalizationService;

  @BeforeMethod(alwaysRun = true)
  public void setUp() throws URISyntaxException {
    MockitoAnnotations.initMocks(this);
    _salesNavigatorProfileAssociationService =
        new SalesNavigatorProfileAssociationService(_crmPairingClient, _profileClient, _adapterUrlFactory,
            _profileIdGenerator, _vectorClient, _crmMappingClient, _salesExternalizationService);
    _crmInstanceId = "fd89c380-d33a-e311-8c20-78e3b5103ee3";
    _crmInstanceUrn = CrmInstanceUrn.deserialize(String.format("urn:li:crmInstance:(DYNAMICS,%s)", _crmInstanceId));
  }

  @Test
  public void testBatchGetSalesNavProfileAssociations_testCsaV2AndUuidInstance() throws URISyntaxException {
    String crmId1 = "test_crm_id_1";
    String crmId2 = "test_crm_id_2";
    MemberUrn memberUrn = new MemberUrn(2000L);

    DerivedMapping derivedMapping21 = new DerivedMapping().setChangeTimeStamps(new ChangeTimeStamps().setLastModified(new Date().getTime()))
        .setLinkedInEntity(memberUrn)
        .setCrmEntityId(crmId2)
        .setConfidence(0.9);

    DerivedMapping derivedMapping22 = new DerivedMapping().setChangeTimeStamps(new ChangeTimeStamps().setLastModified(new Date().getTime() - 1000))
        .setLinkedInEntity(memberUrn)
        .setCrmEntityId(crmId2)
        .setConfidence(0.9);

    List<CrmPairing> crmPairingsList = new ArrayList();
    crmPairingsList.add(new CrmPairing().setContract(CONTRACT_URN));
    crmPairingsList.add(new CrmPairing().setContract(CONTRACT_URN));

    SalesNavigatorProfileAssociationKey key1 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId1)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    SalesNavigatorProfileAssociationKey key2 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId2)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    Set<SalesNavigatorProfileAssociationKey> keys = ImmutableSet.of(key1, key2);

    //Mock CrmPairing Response
    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(crmPairingsList);

    //Mock CrmMappingClient
    when(_crmMappingClient.findDerivedMappingByCrmEntityId(CONTRACT_URN, _crmInstanceUrn, Channel.CRM_SYNC,
        crmId1)).thenReturn(Task.value(Collections.emptyList()));
    when(_crmMappingClient.findDerivedMappingByCrmEntityId(CONTRACT_URN, _crmInstanceUrn, Channel.CRM_SYNC,
        crmId2)).thenReturn(Task.value(Arrays.asList(derivedMapping21, derivedMapping22)));

    // mock profile decoration
    List<Profile> profiles = new ArrayList<>();
    profiles.add(0, new Profile().setId(2000L)
        .setProfilePicture(new ProfilePicture().setDisplayImage(new DigitalmediaAssetUrn("digitalmediaAsset1"))));
    when(_profileClient.batchGet(any(), anyLong(), any())).thenReturn(Task.value(profiles));

    List<AssetMediaArtifactFileIdentifiers> photoIds = buildMediaArtifactFileIds();
    when(_vectorClient.batchGetLinkedinProfilePhotoUrl(anyList(), anyInt(), anyInt())).thenReturn(Task.value(photoIds));

    URLBuilder builder = new URLBuilder();
    builder.setScheme("https");
    builder.setHost("www.linkedin.com");
    builder.setPath("/sales/people/mock");
    when(_adapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(builder);

    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        result = await(_salesNavigatorProfileAssociationService
        .batchGetSalesNavProfileAssociations(keys));

    //check result
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey1 = new ComplexResourceKey<>(key1, new EmptyRecord());
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey2 = new ComplexResourceKey<>(key2, new EmptyRecord());

    Assert.assertTrue(result.containsKey(complexKey2));
    Assert.assertEquals(result.get(complexKey2).getMember(), memberUrn);
    Assert.assertTrue(result.getErrors().containsKey(complexKey1));
  }

  @Test
  public void testBatchGetSalesNavProfileAssociationsWithoutTrust() {
    String crmId1 = "test_crm_id_1";
    String crmId2 = "test_crm_id_2";

    SalesNavigatorProfileAssociationKey key1 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId1)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    SalesNavigatorProfileAssociationKey key2 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId2)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    Set<SalesNavigatorProfileAssociationKey> keys = ImmutableSet.of(key1, key2);
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(false));

    RestLiServiceException error =
        runAndWaitException(_salesNavigatorProfileAssociationService.batchGetSalesNavProfileAssociations(keys),
            RestLiServiceException.class);
    Assert.assertEquals(error.getStatus(), HttpStatus.S_403_FORBIDDEN);
    Assert.assertEquals(error.getServiceErrorCode().intValue(), ProfileAssociationsServiceErrorCode.REQUEST_NOT_TRUSTED.getCode());
  }

  @Test
  public void testBatchGetMemberCrmAssociations_csaV2_singleContractMatched() throws Exception {
    //prepare mock
    ContractUrn contractUrn = new ContractUrn(1000L);
    final boolean isContractPickedFromMultipleResults = false;
    String crmId1 = "test_crm_id_1";
    String crmId2 = "test_crm_id_2";
    MemberUrn memberUrn = new MemberUrn(2000L);

    CrmLinkedInEntityMapping crmMapping2 = new CrmLinkedInEntityMapping().setLinkedInEntity(
        CrmLinkedInEntityMapping.LinkedInEntity.createWithMember(memberUrn));

    when(_crmMappingClient.batchGetMappingsByCrmEntity(contractUrn, _crmInstanceUrn,
        ImmutableSet.of(crmId1, crmId2))).thenReturn(Task.value(ImmutableMap.of(crmId2, crmMapping2)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));

    SalesNavigatorProfileAssociationKey key1 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId1)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    SalesNavigatorProfileAssociationKey key2 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId2)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);

    SalesNavigatorProfileAssociationService underTest =
        new SalesNavigatorProfileAssociationService(_crmPairingClient, _profileClient, _adapterUrlFactory,
            _profileIdGenerator, _vectorClient, _crmMappingClient, _salesExternalizationService) {
          @Override
          @NonNull
          ProfileAssociationsCrmContractModel getCrmContractModelFromCrmPairing(
              @NonNull CollectionResponse<CrmPairing> crmPairingsResponse, @NonNull SalesNavigatorProfileAssociationKey key) {
            return new ProfileAssociationsCrmContractModel(contractUrn, isContractPickedFromMultipleResults);
          }

          @Override
          Task<BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>> decorate(
              @NonNull BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> profileAssociations) {
            return Task.value(profileAssociations);
          }
        };

    //execute
    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        result = await(underTest.batchGetSalesNavProfileAssociations(ImmutableSet.of(key1, key2)));

    //check result
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey1 = new ComplexResourceKey<>(key1, new EmptyRecord());
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey2 = new ComplexResourceKey<>(key2, new EmptyRecord());

    Assert.assertTrue(result.containsKey(complexKey2));
    Assert.assertEquals(result.get(complexKey2).getMember(), memberUrn);
    Assert.assertTrue(result.getErrors().containsKey(complexKey1));
  }

  @Test
  public void testBatchGetMemberCrmAssociations_csaV2_multipleContractMatched() throws Exception {
    //prepare mock
    final boolean isContractPickedFromMultipleResults = true;
    String crmId1 = "test_crm_id_1";
    String crmId2 = "test_crm_id_2";
    MemberUrn memberUrn = new MemberUrn(2000L);

    DerivedMapping derivedMapping2 = new DerivedMapping().setChangeTimeStamps(new ChangeTimeStamps().setLastModified(new Date().getTime()))
        .setLinkedInEntity(memberUrn)
        .setCrmEntityId(crmId2)
        .setConfidence(0.9);
    when(_crmMappingClient.findDerivedMappingByCrmEntityId(CONTRACT_URN, _crmInstanceUrn, Channel.CRM_SYNC,
        crmId1)).thenReturn(Task.value(Collections.emptyList()));
    when(_crmMappingClient.findDerivedMappingByCrmEntityId(CONTRACT_URN, _crmInstanceUrn, Channel.CRM_SYNC,
        crmId2)).thenReturn(Task.value(Collections.singletonList(derivedMapping2)));

    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));

    SalesNavigatorProfileAssociationKey key1 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId1)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);
    SalesNavigatorProfileAssociationKey key2 = new SalesNavigatorProfileAssociationKey()
        .setRecordId(crmId2)
        .setPartner(Partner.MS)
        .setInstanceId(_crmInstanceId);

    SalesNavigatorProfileAssociationService underTest =
        new SalesNavigatorProfileAssociationService(_crmPairingClient, _profileClient, _adapterUrlFactory,
            _profileIdGenerator, _vectorClient, _crmMappingClient, _salesExternalizationService) {
          @Override
          @NonNull
          ProfileAssociationsCrmContractModel getCrmContractModelFromCrmPairing(
              @NonNull CollectionResponse<CrmPairing> crmPairingsResponse, @NonNull SalesNavigatorProfileAssociationKey key) {
            return new ProfileAssociationsCrmContractModel(CONTRACT_URN, isContractPickedFromMultipleResults);
          }

          @Override
          Task<BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>> decorate(
              @NonNull BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> profileAssociations) {
            return Task.value(profileAssociations);
          }
        };

    //execute
    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        result = await(underTest.batchGetSalesNavProfileAssociations(ImmutableSet.of(key1, key2)));

    //check result
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey1 = new ComplexResourceKey<>(key1, new EmptyRecord());
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> complexKey2 = new ComplexResourceKey<>(key2, new EmptyRecord());

    Assert.assertTrue(result.containsKey(complexKey2));
    Assert.assertEquals(result.get(complexKey2).getMember(), memberUrn);
    Assert.assertTrue(result.getErrors().containsKey(complexKey1));
  }

  @Test
  public void testEncodeMemberUrn() throws CryptoException {
    when(_profileIdGenerator.generateProfileId(anyLong())).thenReturn("AKK345");
    String encoded = _salesNavigatorProfileAssociationService.encode(new MemberUrn(1L));
    Assert.assertEquals(encoded,"AKK345");
  }

  @Test
  public void testGetVectorUrlsFromMemberIds() throws URISyntaxException {
    Set<Long> mids = new HashSet<>();
    mids.add(1L);
    mids.add(2L);
    PathSpec[] fields = {Profile.fields().pictureInfo(), Profile.fields().profilePicture(), Profile.fields().id()};

    List<Profile> profiles = new ArrayList<>();
    profiles.add(0, new Profile().setId(1L)
        .setProfilePicture(new ProfilePicture().setDisplayImage(new DigitalmediaAssetUrn("digitalmediaAsset1"))));
    profiles.add(1, new Profile().setId(2L)
        .setProfilePicture(new ProfilePicture().setDisplayImage(new DigitalmediaAssetUrn("digitalmediaAsset2"))));
    profiles.add(null);
    when(_profileClient.batchGet(mids, -1L, fields)).thenReturn(Task.value(profiles));
    List<AssetMediaArtifactFileIdentifiers> photoIds = buildMediaArtifactFileIds();
    when(_vectorClient.batchGetLinkedinProfilePhotoUrl(any(), anyInt(), anyInt())).thenReturn(Task.value(photoIds));
    Map<MemberUrn, Optional<Url>> urls = await(_salesNavigatorProfileAssociationService.getVectorPublicProfileUrls(mids));
    Assert.assertEquals(urls.size(), 2);
    Assert.assertTrue(urls.entrySet()
        .stream()
        .anyMatch(u -> u.getKey().getMemberIdEntity() == 1L && "www.linkedin.com/profile/pic/1".equals(
            u.getValue().get().toString())));
    Assert.assertTrue(urls.entrySet()
        .stream()
        .anyMatch(u -> u.getKey().getMemberIdEntity() == 2L && "www.linkedin.com/profile/pic/2".equals(
            u.getValue().get().toString())));
  }

  private List<AssetMediaArtifactFileIdentifiers> buildMediaArtifactFileIds() throws URISyntaxException {
    List<AssetMediaArtifactFileIdentifiers> photoIds = new ArrayList<>();
    MediaArtifactFileIdentifiersArray m1 = new MediaArtifactFileIdentifiersArray(Collections.singleton(
        new MediaArtifactFileIdentifiers().setIdentifiers(new FileIdentifierArray(
            Collections.singleton(new FileIdentifier().setIdentifier("www.linkedin.com/profile/pic/1"))))));
    m1.add(new MediaArtifactFileIdentifiers().setArtifact(new Urn("urn:li:media:1")));
    MediaArtifactFileIdentifiersArray m2 = new MediaArtifactFileIdentifiersArray(Collections.singleton(
        new MediaArtifactFileIdentifiers().setIdentifiers(new FileIdentifierArray(
            Collections.singleton(new FileIdentifier().setIdentifier("www.linkedin.com/profile/pic/2"))))));
    m1.add(new MediaArtifactFileIdentifiers().setArtifact(new Urn("urn:li:media:2")));
    photoIds.add(new AssetMediaArtifactFileIdentifiers()
        .setAssetUrn(new DigitalmediaAssetUrn("digitalmediaAsset1"))
        .setMediaArtifactFileIdentifiers(m1));
    photoIds.add(new AssetMediaArtifactFileIdentifiers()
        .setAssetUrn(new DigitalmediaAssetUrn("digitalmediaAsset2"))
        .setMediaArtifactFileIdentifiers(m2));
    return photoIds;
  }

  @Test
  public void testDecorateProfileAssociationsEndToEnd() throws URISyntaxException {
    //setup profile associations to be decorated
    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        profileAssociations = buildBatchProfileAssociations();

    //setup the mocks to get thru the test code boilerplate
    List<Profile> profiles = new ArrayList<>();
    profiles.add(0, new Profile().setId(1L)
        .setProfilePicture(new ProfilePicture().setDisplayImage(new DigitalmediaAssetUrn("digitalmediaAsset1"))));
    when(_profileClient.batchGet(any(), anyLong(), any())).thenReturn(Task.value(profiles));

    List<AssetMediaArtifactFileIdentifiers> photoIds = buildMediaArtifactFileIds();
    when(_vectorClient.batchGetLinkedinProfilePhotoUrl(anyList(), anyInt(), anyInt())).thenReturn(Task.value(photoIds));

    URLBuilder builder = new URLBuilder();
    builder.setScheme("https");
    builder.setHost("www.linkedin.com");
    builder.setPath("/sales/people/mock");
    when(_adapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(builder);

    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        actual = await(_salesNavigatorProfileAssociationService.decorate(profileAssociations));

    // verify the shape of the data
    Assert.assertEquals(actual.size(), 1);
    Assert.assertEquals(actual.getErrors().size(), 1);

    // verify the hit has all 3 fields
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> keyForRecord1 =
        generateProfileAssociationKey(Partner.MS, "1", "https://www.foo.com");
    Assert.assertEquals(actual.get(keyForRecord1).getProfilePhoto().toString(), "www.linkedin.com/profile/pic/1");
    Assert.assertEquals(actual.get(keyForRecord1).getProfile().toString(),
        "https://www.linkedin.com/sales/people/mock");
    Assert.assertEquals(actual.get(keyForRecord1).getMember().getMemberIdEntity(), (Long) 1L);

    // verify the miss is part of the batch result "errors" section and has no fields
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> keyForRecord2 =
        generateProfileAssociationKey(Partner.MS, "2", "https://www.foo.com");
    Assert.assertTrue(actual.getErrors().containsKey(keyForRecord2));
  }

  private BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
  buildBatchProfileAssociations() {
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> hits = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key1 =
        generateProfileAssociationKey(Partner.MS, "1", "https://www.foo.com");
    SalesNavigatorProfileAssociation association1 = new SalesNavigatorProfileAssociation().setMember(new MemberUrn(1L));
    hits.put(key1, association1);
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, RestLiServiceException> misses = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key2 =
        generateProfileAssociationKey(Partner.MS, "2", "https://www.foo.com");
    misses.put(key2, new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
    return new BatchResult<>(hits, misses);
  }

  @Test
  public void testAddPublicProfilePhotoUrl() {
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> hits = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key1 =
        generateProfileAssociationKey(Partner.MS, "1", "https://www.foo.com");
    SalesNavigatorProfileAssociation association1 = new SalesNavigatorProfileAssociation()
        .setMember(new MemberUrn(1L)).setProfile(new Url("https://www.linkedin.com/in/alexanderpower/"));
    hits.put(key1, association1);

    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, RestLiServiceException> misses = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key2 =
        generateProfileAssociationKey(Partner.MS, "2", "https://www.foo.com");
    misses.put(key2, new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));

    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        profileAssociations = new BatchResult<>(hits, misses);

    Map<MemberUrn, Optional<Url>> profilePhotos = new HashMap<>();
    profilePhotos.put(new MemberUrn(1L), Optional.of(new Url("https://media.licdn.com/dms/image/bar")));

    _salesNavigatorProfileAssociationService.addPublicProfilePhotoUrl(profileAssociations, profilePhotos);

    Assert.assertTrue(profileAssociations.get(key1).hasProfilePhoto());
    Assert.assertEquals(profileAssociations.get(key1).getProfilePhoto().toString(), "https://media.licdn.com/dms/image/bar");
    Assert.assertTrue(profileAssociations.getErrors().containsKey(key2)); //make sure we don't lose the errors somehow while doing the updates
  }

  @Test
  public void testAddSalesNavProfileUrl() {
    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation> hits = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key1 =
        generateProfileAssociationKey(Partner.MS, "1", "https://www.foo.com");
    SalesNavigatorProfileAssociation association1 = new SalesNavigatorProfileAssociation()
        .setMember(new MemberUrn(1L)).setProfilePhoto(new Url("https://media.licdn.com/dms/image/foo"));
    hits.put(key1, association1);

    Map<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, RestLiServiceException> misses = new HashMap<>();
    ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> key2 =
        generateProfileAssociationKey(Partner.MS, "2", "https://www.foo.com");
    misses.put(key2, new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));

    BatchResult<ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord>, SalesNavigatorProfileAssociation>
        profileAssociations = new BatchResult<>(hits, misses);

    Map<MemberUrn, Optional<Url>> profilePhotos = new HashMap<>();
    profilePhotos.put(new MemberUrn(1L), Optional.of(new Url("https://media.licdn.com/dms/image/foo")));

    URLBuilder builder = new URLBuilder();
    builder.setScheme("https");
    builder.setHost("www.linkedin.com");
    builder.setPath("/sales/people/ACwAAAaXyVsBMJ1V1zhzEEC58zAX1EiMZbxjU_U,NAME_SEARCH,pLKQ");
    when(_adapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(builder);
    _salesNavigatorProfileAssociationService.addSalesNavProfileUrl(profileAssociations);

    Assert.assertTrue(profileAssociations.get(key1).hasProfile());
    Assert.assertEquals(profileAssociations.get(key1).getProfile().toString(),
        "https://www.linkedin.com/sales/people/ACwAAAaXyVsBMJ1V1zhzEEC58zAX1EiMZbxjU_U,NAME_SEARCH,pLKQ");
    Assert.assertTrue(profileAssociations.getErrors().containsKey(key2)); //make sure we don't lose the errors somehow while doing the updates
  }

  private static ComplexResourceKey<SalesNavigatorProfileAssociationKey, EmptyRecord> generateProfileAssociationKey(
      Partner partner, String recordId, String instanceId) {
    SalesNavigatorProfileAssociationKey key1 = new SalesNavigatorProfileAssociationKey()
        .setPartner(partner).setRecordId(recordId).setInstanceId(instanceId);
    return new ComplexResourceKey<>(key1, new EmptyRecord());
  }

  @Test
  public void testBuildSalesNavProfileUrl() {
    MemberUrn memberUrn = new MemberUrn(1L);
    URLBuilder builder = new URLBuilder();
    builder.setScheme("https");
    builder.setHost("www.linkedin.com");
    builder.setPath("/sales/people/ACwAAAaXyVsBMJ1V1zhzEEC58zAX1EiMZbxjU_U,NAME_SEARCH,pLKQ");
    when(_adapterUrlFactory.makeAppUrl(any(), any(), any(), any())).thenReturn(builder);
    Url url = _salesNavigatorProfileAssociationService.buildSalesNavProfileUrl(memberUrn);
    Assert.assertEquals(url.toString(), "https://www.linkedin.com/sales/people/ACwAAAaXyVsBMJ1V1zhzEEC58zAX1EiMZbxjU_U,NAME_SEARCH,pLKQ");
  }

  @Test
  public void testGetContractFromCrmPairing_testNoMatchForCrmInstance() {
    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(Collections.emptyList());
    try {
      SalesNavigatorProfileAssociationKey key = new SalesNavigatorProfileAssociationKey()
          .setInstanceId(_crmInstanceId)
          .setPartner(Partner.MS)
          .setRecordId(CRM_RECORD_ID);
      _salesNavigatorProfileAssociationService.getCrmContractModelFromCrmPairing(collectionResponse, key);
    } catch (Exception e) {
      Assert.assertEquals(e.getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e).getStatus(), HttpStatus.S_400_BAD_REQUEST);
      Assert.assertEquals(((RestLiServiceException) e).getServiceErrorCode().intValue(),
          ProfileAssociationsServiceErrorCode.CRM_SYNC_NOT_ENABLED.getCode());
    }

  }
  @Test
  public void testGetContractFromCrmPairing_testOneMatchForCrmInstance() {
    List<CrmPairing> crmPairingsList = new ArrayList<>();
    crmPairingsList.add(new CrmPairing().setContract(CONTRACT_URN));

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(crmPairingsList);
    try {
      SalesNavigatorProfileAssociationKey key = new SalesNavigatorProfileAssociationKey()
          .setInstanceId(_crmInstanceId)
          .setPartner(Partner.MS)
          .setRecordId(CRM_RECORD_ID);
      _salesNavigatorProfileAssociationService.getCrmContractModelFromCrmPairing(collectionResponse, key);
    } catch (Exception e) {
      Assert.assertEquals(e.getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e).getStatus(), HttpStatus.S_400_BAD_REQUEST);
      Assert.assertEquals(((RestLiServiceException) e).getServiceErrorCode().intValue(),
          ProfileAssociationsServiceErrorCode.CRM_SYNC_NOT_ENABLED.getCode());
    }
  }

  @Test
  public void testGetContractFromCrmPairing_testManyMatchesForCrmInstance() {
    List<CrmPairing> crmPairingsList = new ArrayList<>();
    crmPairingsList.add(new CrmPairing().setContract(CONTRACT_URN));
    crmPairingsList.add(new CrmPairing().setContract(CONTRACT_URN));

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(crmPairingsList);
    try {
      SalesNavigatorProfileAssociationKey key = new SalesNavigatorProfileAssociationKey()
          .setInstanceId(_crmInstanceId)
          .setPartner(Partner.MS)
          .setRecordId(CRM_RECORD_ID);
      _salesNavigatorProfileAssociationService.getCrmContractModelFromCrmPairing(collectionResponse, key);
    } catch (Exception e) {
      Assert.assertEquals(e.getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e).getStatus(), HttpStatus.S_400_BAD_REQUEST);
      Assert.assertEquals(((RestLiServiceException) e).getServiceErrorCode().intValue(),
          ProfileAssociationsServiceErrorCode.CRM_SYNC_NOT_ENABLED.getCode());
    }
  }
}
