package com.linkedin.sales.service;

import com.linkedin.common.urn.CompanyUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.events.UnifiedActionType;
import com.linkedin.events.federator.UnifiedAction;
import com.linkedin.identity.Position;
import com.linkedin.identity.PositionMap;
import com.linkedin.identity.Profile;
import com.linkedin.lss.LixConstants;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.admin.ContractSetting;
import com.linkedin.sales.admin.ContractSettingType;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesContractSettingsClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.ds.rest.ProfileClient;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.leadaccount.SalesAccountsService;
import com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.service.utils.LixUtils;
import java.util.Collections;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.Optional;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.scalactic.Bool;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.SalesNavigatorSaveLeadService.*;
import static java.lang.Boolean.*;
import static org.mockito.Mockito.*;

public class SalesNavigatorSaveLeadServiceTest extends ServiceUnitTest {
  private static final MemberUrn VIEWER_MEMBER_URN = new MemberUrn(567888L);

  @Mock
  protected SalesSeatClient _salesSeatClient;

  @Mock
  private SalesContractSettingsClient _salesContractSettingsClient;

  @Mock
  protected ProfileClient _isbProfileClient;

  @Mock
  private SavedLeadService _savedLeadService;

  @Mock
  private SalesAccountsService _salesAccountService;

  @Mock
  private SalesLeadAccountAssociationService _salesLeadAccountAssociationService;

  @Mock
  private LixService _lixService;

  @Mock
  private TrackingService _trackingService;


  @BeforeMethod
  public void setupMethod() {
    MockitoAnnotations.initMocks(this);
    doNothing().when(_trackingService).createAndSendOutSalesActionTrackingEvent(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testSaveLead_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;

    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_200_OK)));
    when(_salesAccountService.createSavedAccount(any())).thenReturn(Task.value(null));
    when(_salesLeadAccountAssociationService.createLeadAccountAssociation(any())).thenReturn(Task.value(null));
    doReturn(Task.value(FALSE)).when(_lixService)
        .isContractBasedLixEnabled(contractUrn, LixUtils.LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE);
    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testSaveLeadAndSaveAccount_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;
    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    doReturn(Task.value(TRUE)).when(_lixService)
        .isContractBasedLixEnabled(contractUrn, LixUtils.LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_200_OK)));
    Pair<CompoundKey, CreateResponse> pair = new Pair<>(new CompoundKey(),new CreateResponse(HttpStatus.S_201_CREATED));
    when(_salesAccountService.createSavedAccount(any())).thenReturn(Task.value(pair));
    when(_salesLeadAccountAssociationService.createLeadAccountAssociation(any())).thenReturn(Task.value(null));
    when(_salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(any(), eq(seatUrn), eq(0), eq(-1)))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));
    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    verify(_salesLeadAccountAssociationService, times(0))
        .getLeadAccountAssociationForAccounts(any(), eq(seatUrn), eq(0), eq(-1));
    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testSaveLead_SaveLeadFail() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;

    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    Assert.assertFalse(ret);
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }


  @Test
  public void testSaveLead_LimitExceeded() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;

    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    when(_savedLeadService.createSavedLead(any())).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded lead limit")));

    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    Assert.assertFalse(ret);
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testSaveLead_SaveAccountFail() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;

    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_200_OK)));
    when(_salesAccountService.createSavedAccount(any())).thenReturn(Task.failure(new Exception()));
    when(_salesLeadAccountAssociationService.createLeadAccountAssociation(any())).thenReturn(Task.value(null));
    doReturn(Task.value(FALSE)).when(_lixService)
        .isContractBasedLixEnabled(contractUrn, LixUtils.LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE);
    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    Assert.assertTrue(ret);
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testSaveLead_SaveAccountWhenSavedAccountLimitExceeded() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    Long companyId = 5555L;

    doReturn(Task.value(Optional.of(companyId))).when(salesNavigatorSaveLeadService)
        .getCompanyIdFromAccountName(viewer, viewee, accountName);
    doReturn(Task.value(TRUE)).when(_lixService)
        .isContractBasedLixEnabled(contractUrn, LixUtils.LSS_LEAD_ACCOUNT_ASSOCIATION_UPDATE);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_200_OK)));
    when(_salesAccountService.createSavedAccount(any())).thenReturn(Task.failure(
        new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded saved account limit")
    ));
    when(_salesLeadAccountAssociationService.createLeadAccountAssociation(any())).thenReturn(Task.value(null));
    when(_salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(any(), eq(seatUrn), eq(0), eq(-1)))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));

    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, accountName));
    Assert.assertTrue(ret);
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testSaveLead_NoCompany() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);
    when(_savedLeadService.createSavedLead(any())).thenReturn(Task.value(new CreateResponse(HttpStatus.S_201_CREATED)));

    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(contractUrn, seatUrn, viewer, viewee, null));
    Assert.assertTrue(ret);
    verify(_trackingService, times(1)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.SAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }


  @Test
  public void testSaveLead_noSeatContractEnabled() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    doReturn(Task.value(Optional.empty()))
        .when(salesNavigatorSaveLeadService)
        .getSeatAndCheckContractEnabled(viewer);

    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(viewer, viewee, null));

    //=== check result ===
    Assert.assertFalse(ret);
  }


  @Test
  public void testSaveLead_seatEnabled() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contracturn = new ContractUrn(2222L);
    SalesSeat seat = new SalesSeat();
    seat.setId(seatUrn.getSeatIdEntity());
    seat.setContract(contracturn);

    doReturn(Task.value(Optional.of(seat)))
        .when(salesNavigatorSaveLeadService)
        .getSeatAndCheckContractEnabled(viewer);

    doReturn(Task.value(true))
        .when(salesNavigatorSaveLeadService)
        .saveLead(contracturn, seatUrn, viewer, viewee, accountName);


    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.saveLead(viewer, viewee, accountName));


    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testGetCompanyIdFromAccountName_nullAccountName() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = null;

    //=== run tested method ===
    Optional<Long> ret = await(salesNavigatorSaveLeadService.getCompanyIdFromAccountName(viewer, viewee, accountName));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetCompanyIdFromAccountName_emptyProfile() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";

    doReturn(Task.failure(new RuntimeException("fail to get profile")))
        .when(_isbProfileClient)
        .getProfile(1234L, 5678L, SalesNavigatorSaveLeadService.ISB_PROFILE_FIELDS);

    //=== run tested method ===
    Optional<Long> ret = await(salesNavigatorSaveLeadService.getCompanyIdFromAccountName(viewer, viewee, accountName));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetCompanyIdFromAccountName_emptyPosition() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    Profile profile = new Profile(); // no position

    doReturn(Task.value(profile))
        .when(_isbProfileClient)
        .getProfile(1234L, 5678L, SalesNavigatorSaveLeadService.ISB_PROFILE_FIELDS);

    //=== run tested method ===
    Optional<Long> ret = await(salesNavigatorSaveLeadService.getCompanyIdFromAccountName(viewer, viewee, accountName));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetCompanyIdFromAccountName_noMatchCompanyName() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    Profile profile = new Profile();
    PositionMap positionMap = new PositionMap();
    profile.setPositions(positionMap);

    doReturn(Task.value(profile))
        .when(_isbProfileClient)
        .getProfile(1234L, 5678L, SalesNavigatorSaveLeadService.ISB_PROFILE_FIELDS);

    doReturn(Optional.empty())
        .when(salesNavigatorSaveLeadService)
        .findCompanyIdFromPositionMap(positionMap, accountName);

    //=== run tested method ===
    Optional<Long> ret = await(salesNavigatorSaveLeadService.getCompanyIdFromAccountName(viewer, viewee, accountName));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetCompanyIdFromAccountName_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    String accountName = "account name";
    Profile profile = new Profile();
    PositionMap positionMap = new PositionMap();
    profile.setPositions(positionMap);
    Long companyId = 5555L;

    doReturn(Task.value(profile))
        .when(_isbProfileClient)
        .getProfile(1234L, 5678L, SalesNavigatorSaveLeadService.ISB_PROFILE_FIELDS);

    doReturn(Optional.of(companyId))
        .when(salesNavigatorSaveLeadService)
        .findCompanyIdFromPositionMap(positionMap, accountName);

    //=== run tested method ===
    Optional<Long> ret = await(salesNavigatorSaveLeadService.getCompanyIdFromAccountName(viewer, viewee, accountName));

    //=== check result ===
    Assert.assertTrue(ret.isPresent());
    Assert.assertEquals(ret.get(), companyId);

  }

  @Test
  public void testFindCompanyIdFromPositionMap_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    Long companyId = 5555L;
    String accountName = "account name";
    PositionMap positionMap = new PositionMap();
    Position position = new Position();
    position.setLocalizedCompanyName(accountName);
    position.setCompany(new CompanyUrn(companyId.intValue()));
    positionMap.put("dummy", position);

    //=== run tested method ===
    Optional<Long> ret = salesNavigatorSaveLeadService.findCompanyIdFromPositionMap(positionMap, accountName);

    //=== check result ===
    Assert.assertTrue(ret.isPresent());
    Assert.assertEquals(ret.get(), companyId);
  }

  @Test
  public void testGetSeatAndCheckContractEnabled_noSeat() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);

    doReturn(Task.value(Optional.empty()))
        .when(salesNavigatorSaveLeadService)
        .getLastChosenContractSeat(viewer);

    //=== run tested method ===
    Optional<SalesSeat> ret = await(salesNavigatorSaveLeadService.getSeatAndCheckContractEnabled(viewer));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetSeatAndCheckContractEnabled_contractNotEnabled() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contracturn = new ContractUrn(2222L);
    SalesSeat seat = new SalesSeat();
    seat.setId(seatUrn.getSeatIdEntity());
    seat.setContract(contracturn);

    doReturn(Task.value(Optional.of(seat)))
        .when(salesNavigatorSaveLeadService)
        .getLastChosenContractSeat(viewer);

    doReturn(Task.value(false))
        .when(salesNavigatorSaveLeadService)
        .isContractEnabled(contracturn);

    //=== run tested method ===
    Optional<SalesSeat> ret = await(salesNavigatorSaveLeadService.getSeatAndCheckContractEnabled(viewer));

    //=== check result ===
    Assert.assertFalse(ret.isPresent());
  }

  @Test
  public void testGetSeatAndCheckContractEnabled_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contracturn = new ContractUrn(2222L);
    SalesSeat seat = new SalesSeat();
    seat.setId(seatUrn.getSeatIdEntity());
    seat.setContract(contracturn);

    doReturn(Task.value(Optional.of(seat)))
        .when(salesNavigatorSaveLeadService)
        .getLastChosenContractSeat(viewer);

    doReturn(Task.value(true))
        .when(salesNavigatorSaveLeadService)
        .isContractEnabled(contracturn);

    //=== run tested method ===
    Optional<SalesSeat> ret = await(salesNavigatorSaveLeadService.getSeatAndCheckContractEnabled(viewer));

    //=== check result ===
    Assert.assertTrue(ret.isPresent());
    Assert.assertEquals(ret.get(), seat);
  }

  @Test
  public void testIsContactEnabled_normal() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    ContractUrn contracturn = new ContractUrn(2222L);

    ContractSetting contractSetting = new ContractSetting().setType(ContractSettingType.OFFICE_365_INTEGRATION_ENABLED)
        .setValue(ContractSetting.Value.createWithBoolean(FALSE));

    doReturn(Task.value(contractSetting))
        .when(_salesContractSettingsClient)
        .get(2222L, ContractSettingType.OFFICE_365_INTEGRATION_ENABLED);

    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.isContractEnabled(contracturn));

    //=== check result ===
    Assert.assertFalse(ret);
  }

  @Test
  public void testIsContactEnabled_exception() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    ContractUrn contracturn = new ContractUrn(2222L);

    doReturn(Task.failure(new RuntimeException("no contract setting")))
        .when(_salesContractSettingsClient)
        .get(2222L, ContractSettingType.OFFICE_365_INTEGRATION_ENABLED);

    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.isContractEnabled(contracturn));

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testUnsaveLead_noSeatContractEnabled() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);

    doReturn(Task.value(Optional.empty()))
        .when(salesNavigatorSaveLeadService)
        .getSeatAndCheckContractEnabled(viewer);

    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(viewer, viewee));

    //=== check result ===
    Assert.assertFalse(ret);
  }


  @Test
  public void testUnsaveLead_seatEnabled() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contracturn = new ContractUrn(2222L);
    SalesSeat seat = new SalesSeat();
    seat.setId(seatUrn.getSeatIdEntity());
    seat.setContract(contracturn);

    when(_salesSeatClient.findByMemberLastUsed(viewer, SEAT_FIELDS))
        .thenReturn(Task.value(Collections.singletonList(seat)));

    doReturn(Task.value(Optional.of(seat)))
        .when(salesNavigatorSaveLeadService)
        .getSeatAndCheckContractEnabled(viewer);

    doReturn(Task.value(true))
        .when(salesNavigatorSaveLeadService)
        .unsaveLead(contracturn, seatUrn, viewee, viewer);

    //=== run tested method ===
    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(viewer, viewee));

    //=== check result ===
    Assert.assertTrue(ret);
  }

  @Test
  public void testUnsaveLead_DeleteLeadFail() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);

    when(_savedLeadService.deleteSavedLead(any())).thenReturn(Task.value(new UpdateResponse(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(contractUrn, seatUrn, viewee, VIEWER_MEMBER_URN));
    Assert.assertFalse(ret);
    verify(_trackingService, times(0)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.UNSAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testUnsaveLead_DeleteLeadAccountAssociationFail() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);

    when(_savedLeadService.deleteSavedLead(any())).thenReturn(Task.value(new UpdateResponse(HttpStatus.S_204_NO_CONTENT)));
    when(_salesLeadAccountAssociationService.deleteAssociationsForLead(eq(viewee), eq(seatUrn)))
        .thenReturn(Task.value(false));

    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(contractUrn, seatUrn, viewee, VIEWER_MEMBER_URN));
    Assert.assertTrue(ret);
    verify(_trackingService, times(1)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.UNSAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testUnsaveLead_DeleteLeadAccountAssociationException() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);

    when(_savedLeadService.deleteSavedLead(any())).thenReturn(Task.value(new UpdateResponse(HttpStatus.S_204_NO_CONTENT)));
    when(_salesLeadAccountAssociationService.deleteAssociationsForLead(eq(viewee), eq(seatUrn)))
        .thenReturn(Task.failure(new Exception()));

    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(contractUrn, seatUrn, viewee, VIEWER_MEMBER_URN));
    Assert.assertTrue(ret);
    verify(_trackingService, times(1)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.UNSAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testUnsaveLead_AlreadyUnsaved() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);

    when(_savedLeadService.deleteSavedLead(any())).thenReturn(Task.value(new UpdateResponse(HttpStatus.S_200_OK)));

    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(contractUrn, seatUrn, viewee, VIEWER_MEMBER_URN));
    Assert.assertTrue(ret);
  }

  @Test
  public void testUnsaveLead_Success() {
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewee = new MemberUrn(5678L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contractUrn = new ContractUrn(2222L);

    when(_savedLeadService.deleteSavedLead(any())).thenReturn(Task.value(new UpdateResponse(HttpStatus.S_204_NO_CONTENT)));
    when(_salesLeadAccountAssociationService.deleteAssociationsForLead(eq(viewee), eq(seatUrn)))
        .thenReturn(Task.value(true));

    Boolean ret = await(salesNavigatorSaveLeadService.unsaveLead(contractUrn, seatUrn, viewee, VIEWER_MEMBER_URN));
    Assert.assertTrue(ret);
    verify(_trackingService, times(1)).createAndSendOutSalesActionTrackingEvent(eq(UnifiedAction.UNSAVE), any(), eq(
        UnifiedActionType.SINGLE), anyMap(), any(), any(), any());
  }

  @Test
  public void testGetLastChosenContractSeat() {
    //=== prepare test input and mock ===
    SalesNavigatorSaveLeadService salesNavigatorSaveLeadService =
        spy(createSalesNavigatorSaveLeadService());

    MemberUrn viewer = new MemberUrn(1234L);
    SeatUrn seatUrn = new SeatUrn(1111L);
    ContractUrn contracturn = new ContractUrn(2222L);
    SalesSeat seat = new SalesSeat();
    seat.setId(seatUrn.getSeatIdEntity());
    seat.setContract(contracturn);

    when(_salesSeatClient.findByMemberLastUsed(viewer, SEAT_FIELDS))
        .thenReturn(Task.value(Collections.singletonList(seat)));

    //=== run tested method ===
    Optional<SalesSeat> ret = await(salesNavigatorSaveLeadService.getLastChosenContractSeat(viewer));

    //=== check result ===
    Assert.assertTrue(ret.isPresent());
    Assert.assertEquals(ret.get(), seat);
  }

  private SalesNavigatorSaveLeadService createSalesNavigatorSaveLeadService() {
    return new SalesNavigatorSaveLeadService(
        _salesSeatClient,
        _salesContractSettingsClient,
        _isbProfileClient,
        _savedLeadService,
        _salesAccountService,
        _salesLeadAccountAssociationService,
        _trackingService,
        _lixService);
  }

}
