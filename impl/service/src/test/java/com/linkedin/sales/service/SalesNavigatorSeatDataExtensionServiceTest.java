package com.linkedin.sales.service;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.common.urn.EnterpriseSeatUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.CrmUserMapping;
import com.linkedin.crm.CrmUserMappingKey;
import com.linkedin.enterprise.appsconnector.dataextension.CrmSyncStatus;
import com.linkedin.enterprise.appsconnector.dataextension.SalesNavigatorSeatDataExtension;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.CrmUserMappingClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.service.common.ServiceUnitTest;
import java.util.Map;
import java.util.Set;
import org.assertj.core.util.Lists;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


/**
 * Test for {@link SalesNavigatorSeatDataExtensionService}.
 */
public class SalesNavigatorSeatDataExtensionServiceTest extends ServiceUnitTest {
  private static final String APPLICATION_TYPE = "salesNavigator";
  private static final SeatUrn SEAT_URN_1 = new SeatUrn(1111L);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(1112L);
  private static final Long CONTRACT_ID = 2222L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final CrmPairing CRM_PAIRING = new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(3333L);
  private static final CrmPairingUrn CRM_PAIRING_URN = new CrmPairingUrn(CONTRACT_URN, CRM_PAIRING.getCrmPairingId());
  private static final EnterpriseSeatUrn ENTERPRISE_SEAT_URN_1 =
      new EnterpriseSeatUrn(APPLICATION_TYPE, CONTRACT_URN.getContractIdEntity(), SEAT_URN_1.getSeatIdEntity());
  private static final EnterpriseSeatUrn ENTERPRISE_SEAT_URN_2 =
      new EnterpriseSeatUrn(APPLICATION_TYPE, CONTRACT_URN.getContractIdEntity(), SEAT_URN_2.getSeatIdEntity());
  private static final EnterpriseSeatUrn ENTERPRISE_SEAT_URN_DIFF_CONTRACT = new EnterpriseSeatUrn(
      APPLICATION_TYPE, new ContractUrn(999L).getContractIdEntity(), SEAT_URN_2.getSeatIdEntity());
  private static final CrmUserMappingKey CRM_USER_MAPPING_KEY_1 =
      new CrmUserMappingKey().setCrmPairing(CRM_PAIRING_URN).setSeat(SEAT_URN_1);
  private static final CrmUserMappingKey CRM_USER_MAPPING_KEY_2 =
      new CrmUserMappingKey().setCrmPairing(CRM_PAIRING_URN).setSeat(SEAT_URN_2);
  private static final CrmUserMapping CRM_USER_MAPPING_1 =
      new CrmUserMapping().setSeat(SEAT_URN_1);
  private static final CrmUserMapping CRM_USER_MAPPING_2 =
      new CrmUserMapping().setSeat(SEAT_URN_2);

  @Mock
  private CrmUserMappingClient _crmUserMappingClient;
  @Mock
  private CrmPairingClient _crmPairingClient;
  private SalesNavigatorSeatDataExtensionService _salesNavigatorSeatDataExtensionService;

  @BeforeTest
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @BeforeMethod
  public void setupMethod() {
    _salesNavigatorSeatDataExtensionService =
        new SalesNavigatorSeatDataExtensionService(_crmUserMappingClient, _crmPairingClient);
  }

  @Test
  public void testGetDataExtensions_happyPath() {
    Set<EnterpriseSeatUrn> seatUrns = ImmutableSet.of(ENTERPRISE_SEAT_URN_1, ENTERPRISE_SEAT_URN_2);

    when(_crmPairingClient.findCrmPairingsByContract(CONTRACT_URN, true, null))
        .thenReturn(Task.value(ImmutableList.of(CRM_PAIRING)));
    when(_crmUserMappingClient.getCrmUserMappings(Lists.newArrayList(CRM_USER_MAPPING_KEY_1, CRM_USER_MAPPING_KEY_2)))
        .thenReturn(Task.value(
            ImmutableMap.of(CRM_USER_MAPPING_KEY_1, CRM_USER_MAPPING_1, CRM_USER_MAPPING_KEY_2, CRM_USER_MAPPING_2)));

    Map<EnterpriseSeatUrn, SalesNavigatorSeatDataExtension> actualDataExtensions =
        await(_salesNavigatorSeatDataExtensionService.getDataExtensions(seatUrns));

    Assert.assertEquals(actualDataExtensions.get(ENTERPRISE_SEAT_URN_1).getCrmSyncStatus(), CrmSyncStatus.CRM_SYNC_ON);
  }

  @Test
  public void testGetDataExtension_happyPath() {
    when(_crmPairingClient.findCrmPairingsByContract(CONTRACT_URN, true, null))
        .thenReturn(Task.value(ImmutableList.of(CRM_PAIRING)));
    when(_crmUserMappingClient.getCrmUserMappings(Lists.newArrayList(CRM_USER_MAPPING_KEY_1)))
        .thenReturn(Task.value(ImmutableMap.of(CRM_USER_MAPPING_KEY_1, CRM_USER_MAPPING_1)));

    SalesNavigatorSeatDataExtension actualDataExtension =
        await(_salesNavigatorSeatDataExtensionService.getDataExtension(ENTERPRISE_SEAT_URN_1));

    Assert.assertEquals(actualDataExtension.getCrmSyncStatus(), CrmSyncStatus.CRM_SYNC_ON);
  }

  @Test
  public void testGetDataExtension_crmPairingNotFound() {
    when(_crmPairingClient.findCrmPairingsByContract(CONTRACT_URN, true, null))
        .thenReturn(Task.value(ImmutableList.of()));

    SalesNavigatorSeatDataExtension actualDataExtension =
        await(_salesNavigatorSeatDataExtensionService.getDataExtension(ENTERPRISE_SEAT_URN_1));

    Assert.assertEquals(actualDataExtension.getCrmSyncStatus(), CrmSyncStatus.CRM_SYNC_NOT_MATCHED);
  }

  @Test(
      expectedExceptions = RestLiServiceException.class,
      expectedExceptionsMessageRegExp = "All seats should be from the same contract .*")
  public void testGetDataExtensions_seatsFromDifferentContracts() {
    Set<EnterpriseSeatUrn> seatUrns = ImmutableSet.of(ENTERPRISE_SEAT_URN_1, ENTERPRISE_SEAT_URN_DIFF_CONTRACT);
    await(_salesNavigatorSeatDataExtensionService.getDataExtensions(seatUrns));
  }

  @Test
  public void testGetDataExtensions_incompleteCrmUserMappingResults() {
    Set<EnterpriseSeatUrn> seatUrns = ImmutableSet.of(ENTERPRISE_SEAT_URN_1, ENTERPRISE_SEAT_URN_2);

    when(_crmPairingClient.findCrmPairingsByContract(CONTRACT_URN, true, null))
        .thenReturn(Task.value(ImmutableList.of(CRM_PAIRING)));
    when(_crmUserMappingClient.getCrmUserMappings(Lists.newArrayList(CRM_USER_MAPPING_KEY_1, CRM_USER_MAPPING_KEY_2)))
        .thenReturn(Task.value(ImmutableMap.of(CRM_USER_MAPPING_KEY_1, CRM_USER_MAPPING_1)));

    Map<EnterpriseSeatUrn, SalesNavigatorSeatDataExtension> actualDataExtensions =
        await(_salesNavigatorSeatDataExtensionService.getDataExtensions(seatUrns));

    Assert.assertEquals(actualDataExtensions.get(ENTERPRISE_SEAT_URN_1).getCrmSyncStatus(), CrmSyncStatus.CRM_SYNC_ON);
    Assert.assertEquals(
        actualDataExtensions.get(ENTERPRISE_SEAT_URN_2).getCrmSyncStatus(), CrmSyncStatus.CRM_SYNC_NOT_MATCHED);
  }
}
