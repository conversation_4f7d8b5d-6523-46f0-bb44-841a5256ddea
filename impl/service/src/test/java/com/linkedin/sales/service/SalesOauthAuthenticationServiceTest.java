package com.linkedin.sales.service;

import com.linkedin.common.urn.Urn;
import com.linkedin.container.ic.handlers.api.MemberIdentityHandler;
import com.linkedin.oauth2.internal.OAuth2AccessTokenData;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.SalesAccessToken;
import com.linkedin.sales.client.externalization.SalesOauthAuthenticationClient;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.security.identitytoken.api.MemberIdentityToken;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScope;
import com.linkedin.thirdpartyaccess.ThirdPartyAuthorizedScopeWithPermissionInfo;
import com.linkedin.util.envinfo.EnvInfoFinder;
import org.mockito.Mockito;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;

public class SalesOauthAuthenticationServiceTest extends BaseEngineParTest {
  private SalesOauthAuthenticationService _salesOauthAuthenticationService;
  private SalesOauthAuthenticationClient _salesOauthAuthenticationClient;
  private MemberIdentityHandler _memberIdentityHandler;
  private MemberIdentityToken _memberIdentityToken;
  private EnvInfoFinder _envInfoFinder;
  private LixService _lixService;

  private static final Long APP_ID = 123L;
  private static final Long MEMBER_ID = 999L;
  private static final String ACCESS_TOKEN = "this-is-my-access-token";
  private static final Long ACCESS_TOKEN_TTL = 100L;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    _salesOauthAuthenticationClient = Mockito.mock(SalesOauthAuthenticationClient.class);
    _memberIdentityHandler = Mockito.mock(MemberIdentityHandler.class);
    _memberIdentityToken = Mockito.mock(MemberIdentityToken.class);
    _lixService = Mockito.mock(LixService.class);
    _salesOauthAuthenticationService = new SalesOauthAuthenticationService(_salesOauthAuthenticationClient, _memberIdentityHandler, _envInfoFinder, _lixService);
    doReturn(Task.value(false)).when(_lixService).isLixEnabled(any(), eq(LixUtils.LSS_SAT_TTL_SHORT), any());
    doReturn(_memberIdentityToken).when(_memberIdentityHandler).getMemberIdentity();
  }

  @Test
  public void testGetSalesAccessToken_validParameters_successful() {
    doReturn(Task.value(
        new ThirdPartyAuthorizedScopeWithPermissionInfo().setScope(new ThirdPartyAuthorizedScope().setId(123)))).when(
        _salesOauthAuthenticationClient).generateThirdPartyAuthorizedScopeForApp(any(), any());

    OAuth2AccessTokenData oAuth2AccessTokenData = new OAuth2AccessTokenData();
    oAuth2AccessTokenData.setAccessToken(ACCESS_TOKEN);
    oAuth2AccessTokenData.setAccessTokenTTLInSec(ACCESS_TOKEN_TTL);

    Response salesAccessTokenResponse = mock(Response.class);

    doReturn(Task.value(oAuth2AccessTokenData))
        .when(_salesOauthAuthenticationClient).generateOauth2SalesAccessToken(anyLong(), anyLong(), anyInt(), anyLong(), any(), any());
    doReturn(oAuth2AccessTokenData).when(salesAccessTokenResponse).getEntity();

    SalesAccessToken salesAccessToken =
        super.runAndWait(_salesOauthAuthenticationService.getSalesAccessToken(APP_ID, MEMBER_ID));

    Assert.assertNotNull(salesAccessToken);
    Assert.assertEquals(salesAccessToken.getToken(), ACCESS_TOKEN);
    Assert.assertNotEquals(salesAccessToken.getExpiryTime(), 0);
  }

  @Test(expectedExceptions = RestLiServiceException.class)
  public void testGetSalesAccessToken_noAuthorizedScope_unsuccessful() {
    doThrow(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN)).when(_salesOauthAuthenticationClient)
        .generateThirdPartyAuthorizedScopeForApp(any(), any());

    super.runAndWait(_salesOauthAuthenticationService.getSalesAccessToken(APP_ID, MEMBER_ID));
  }

  @Test(expectedExceptions = PromiseException.class)
  public void testGetSalesAccessToken_LoginServerError_unsuccessful() {
    doReturn(Task.value(
        new ThirdPartyAuthorizedScopeWithPermissionInfo().setScope(new ThirdPartyAuthorizedScope().setId(123)))).when(
        _salesOauthAuthenticationClient).generateThirdPartyAuthorizedScopeForApp(any(), any());

    doThrow(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN)).when(_salesOauthAuthenticationClient)
        .generateOauth2SalesAccessToken(anyLong(), anyLong(), anyInt(), anyLong(), any(), any());

    super.runAndWait(_salesOauthAuthenticationService.getSalesAccessToken(APP_ID, MEMBER_ID));
  }

  @Test(expectedExceptions = RestLiServiceException.class)
  public void testGetSalesAccessToken_DownstreamNotAvailable_unsuccessful() {
    doThrow(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN))
        .when(_salesOauthAuthenticationClient).generateThirdPartyAuthorizedScopeForApp(any(), any());

    super.runAndWait(_salesOauthAuthenticationService.getSalesAccessToken(APP_ID, MEMBER_ID));
  }

}
