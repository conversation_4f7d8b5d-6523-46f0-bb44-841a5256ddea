package com.linkedin.sales.service;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.sales.ds.db.LssCustomFilterViewDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.PinnedFilters;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.PinnedFiltersUtils;
import com.linkedin.salescustomfilterview.SearchType;
import java.util.List;
import java.util.stream.Collectors;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.*;


public class SalesPinnedFiltersServiceTest extends ServiceUnitTest {

  @Test(description = "Test successfully getting records")
  public void getPinnedFiltersPresent() {
    Mocks mocks = new Mocks();
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE, LEAD_HIGHLIGHTS"));
    List<String> expectedServicePinnedFilters = ImmutableList.of("PAST_TITLE, LEAD_HIGHLIGHTS");
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(dbPinnedFilters));
    com.linkedin.salescustomfilterview.PinnedFilters result = await(mocks._salesPinnedFiltersService.getEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.CONTRACT_URN, Mocks.SEARCH_TYPE));
    assertThat(result.getFilters()).isEqualTo(expectedServicePinnedFilters);
  }

  @Test(description = "Test successfully getting records with Sales Intelligence (SI) lix enabled")
  public void getPinnedFiltersPresentSIEnabled() {
    Mocks mocks = new Mocks();
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER))).thenReturn(Task.value(true));
    when(mocks._lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT))).thenReturn(Task.value(true));
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE", "LEAD_HIGHLIGHTS"));
    PinnedFilters updateDbPinnedFilters = new PinnedFilters();
    updateDbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE" ));
    updateDbPinnedFilters.setContractUrn(Mocks.SERIALIZED_CONTRACT_URN);
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(dbPinnedFilters));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE,
        updateDbPinnedFilters)).thenReturn(Task.value(Boolean.TRUE));
    await(mocks._salesPinnedFiltersService.getEspressoPinnedFilters(Mocks.SEAT_URN, Mocks.CONTRACT_URN, Mocks.SEARCH_TYPE));
    verify(mocks._lssCustomFilterViewDB, times(3)).getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE);
  }

  @Test(description = "Test failing to find records for get")
  public void getPinnedFiltersNotPresent() {
    Mocks mocks = new Mocks();
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE, LEAD_HIGHLIGHTS"));
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "test")));
    com.linkedin.salescustomfilterview.PinnedFilters result = await(mocks._salesPinnedFiltersService.getEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.CONTRACT_URN, Mocks.SEARCH_TYPE));
    assertThat(result.getFilters()).isEqualTo(PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, false));
    verify(mocks._lssCustomFilterViewDB, times(1)).getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE);
  }

  @Test(description = "Test failing to find records for get with sales intelligence")
  public void getPinnedFiltersNotPresentSI() {
    Mocks mocks = new Mocks();
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE, LEAD_HIGHLIGHTS"));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER))).thenReturn(Task.value(true));
    when(mocks._lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT))).thenReturn(Task.value(true));
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "test")));
    com.linkedin.salescustomfilterview.PinnedFilters result = await(mocks._salesPinnedFiltersService.getEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.CONTRACT_URN, Mocks.SEARCH_TYPE));
    assertThat(result.getFilters()).isEqualTo(PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, true));
    verify(mocks._lssCustomFilterViewDB, times(1)).getPinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE);
  }

  @Test(description = "Test successfully appending a new pinned filter")
  public void updatePinnedFiltersPresentAppend() {
    Mocks mocks = new Mocks();
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE"));
    PinnedFilters updateDbPinnedFilters = new PinnedFilters();
    updateDbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE", "CURRENT_TITLE"));
    updateDbPinnedFilters.setContractUrn(Mocks.SERIALIZED_CONTRACT_URN);
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.value(dbPinnedFilters));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        eq(updateDbPinnedFilters))).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getSetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(result).isTrue();
  }

  @Test(description = "Test successfully removing a pinned filter")
  public void updatePinnedFiltersPresentRemove() {
    Mocks mocks = new Mocks();
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE", "CURRENT_TITLE"));
    PinnedFilters updateDbPinnedFilters = new PinnedFilters();
    updateDbPinnedFilters.setPinnedFilters(ImmutableList.of("PAST_TITLE"));
    updateDbPinnedFilters.setContractUrn(Mocks.SERIALIZED_CONTRACT_URN);
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.value(dbPinnedFilters));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        eq(updateDbPinnedFilters))).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(result).isTrue();
  }

  @Test(description = "Test updating on an empty record using the default")
  public void updatePinnedFiltersNotPresent() {
    Mocks mocks = new Mocks();
    List<CharSequence> modifiedDefaultList = PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, false).stream()
        .filter(filter -> !filter.equals("CURRENT_TITLE")).collect(Collectors.toList());
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of());
    ArgumentCaptor<PinnedFilters> pinnedFiltersArgumentCaptor = ArgumentCaptor.forClass(PinnedFilters.class);
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.value(dbPinnedFilters));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        pinnedFiltersArgumentCaptor.capture())).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters().containsAll(modifiedDefaultList)).isTrue();
    assertThat(modifiedDefaultList.containsAll(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters())).isTrue();
    assertThat(result).isTrue();
  }

  @Test(description = "Test updating on an empty record using the default with sales intelligence")
  public void updatePinnedFiltersNotPresentSI() {
    Mocks mocks = new Mocks();
    List<CharSequence> modifiedDefaultList = PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, true).stream()
        .filter(filter -> !filter.equals("CURRENT_TITLE")).collect(Collectors.toList());
    PinnedFilters dbPinnedFilters = new PinnedFilters();
    dbPinnedFilters.setPinnedFilters(ImmutableList.of());
    ArgumentCaptor<PinnedFilters> pinnedFiltersArgumentCaptor = ArgumentCaptor.forClass(PinnedFilters.class);
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER))).thenReturn(Task.value(true));
    when(mocks._lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT))).thenReturn(Task.value(true));
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.value(dbPinnedFilters));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        pinnedFiltersArgumentCaptor.capture())).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters().containsAll(modifiedDefaultList)).isTrue();
    assertThat(modifiedDefaultList.containsAll(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters())).isTrue();
    assertThat(result).isTrue();
  }

  @Test(description = "Test updating on not present record doesn't exception out")
  public void updatePinnedFiltersEmpty() {
    Mocks mocks = new Mocks();
    List<CharSequence> modifiedDefaultList = PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, false).stream()
        .filter(filter -> !filter.equals("CURRENT_TITLE")).collect(Collectors.toList());
    ArgumentCaptor<PinnedFilters> pinnedFiltersArgumentCaptor = ArgumentCaptor.forClass(PinnedFilters.class);
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.failure(new EntityNotFoundException(null, "Test")));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        pinnedFiltersArgumentCaptor.capture())).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters().containsAll(modifiedDefaultList)).isTrue();
    assertThat(modifiedDefaultList.containsAll(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters())).isTrue();
    assertThat(result).isTrue();
  }

  @Test(description = "Test updating on not present record doesn't exception out sales intelligence")
  public void updatePinnedFiltersEmptySI() {
    Mocks mocks = new Mocks();
    List<CharSequence> modifiedDefaultList = PinnedFiltersUtils.getDefaultPinnedFilters(Mocks.SEARCH_TYPE, true).stream()
        .filter(filter -> !filter.equals("CURRENT_TITLE")).collect(Collectors.toList());
    ArgumentCaptor<PinnedFilters> pinnedFiltersArgumentCaptor = ArgumentCaptor.forClass(PinnedFilters.class);
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER))).thenReturn(Task.value(true));
    when(mocks._lixService.isContractBasedLixEnabled(any(),
        eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT))).thenReturn(Task.value(true));
    when(mocks._lssCustomFilterViewDB.getPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE))).thenReturn(
        Task.failure(new EntityNotFoundException(null, "Test")));
    when(mocks._lssCustomFilterViewDB.upsertPinnedFilters(eq(Mocks.SERIALIZED_SEAT_URN), eq(Mocks.STRING_SEARCH_TYPE),
        pinnedFiltersArgumentCaptor.capture())).thenReturn(Task.value(Boolean.TRUE));
    Boolean result = await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    assertThat(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters().containsAll(modifiedDefaultList)).isTrue();
    assertThat(modifiedDefaultList.containsAll(pinnedFiltersArgumentCaptor.getValue().getPinnedFilters())).isTrue();
    assertThat(result).isTrue();
  }

  @Test(description = "Test updating with mobile search type fails")
  public void updatePinnedFiltersFailsWhenMobileSearchTypeIsProvided() {
    Mocks mocks = new Mocks();
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(mocks._salesPinnedFiltersService.updateEspressoPinnedFilters(
          Mocks.SEAT_URN, SearchType.MOBILE_ACCOUNT, getUnsetPatchRequest(), Mocks.CONTRACT_URN));
    }).withMessageContaining("400");
  }

  @Test(description = "Test successfully deleting records")
  public void deletePinnedFiltersPresent() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(HttpStatus.S_200_OK));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(false));
    HttpStatus result = await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
    assertThat(result).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test(description = "Test successfully deleting records lix enabled")
  public void deletePinnedFiltersPresentEnabledLix() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(HttpStatus.S_200_OK));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(true));
    HttpStatus result = await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
    assertThat(result).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test(description = "Test failing to delete records should not exception out")
  public void deletePinnedFiltersException() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(false));
    HttpStatus result = await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
    assertThat(result).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test(description = "Test failing to delete records should not exception out lix enabled")
  public void deletePinnedFiltersExceptionLixEnabled() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenReturn(
        Task.value(HttpStatus.S_500_INTERNAL_SERVER_ERROR));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(true));
    HttpStatus result = await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
    assertThat(result).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test(description = "Test failing to find records to delete should throw exception")
  public void deletePinnedFiltersEntityNotFound() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenAnswer(
        (t) -> Task.failure(new EntityNotFoundException(null, "record not found")));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(false));
    try {
      await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
      fail("Exception not thrown as expected");
    } catch (PromiseException e) {
      assertThat(e.getCause().getClass()).isEqualTo(EntityNotFoundException.class);
    }
  }

  @Test(description = "Test failing to find records to delete should not exception out lix enabled")
  public void deletePinnedFiltersEntityNotFoundLixEnabled() {
    Mocks mocks = new Mocks();
    when(mocks._lssCustomFilterViewDB.deletePinnedFilters(Mocks.SERIALIZED_SEAT_URN, Mocks.STRING_SEARCH_TYPE)).thenAnswer(
        (t) -> Task.failure(new EntityNotFoundException(null, "record not found")));
    when(mocks._lixService.isMemberBasedLixEnabledForSeat(any(), eq(LixUtils.LSS_SEARCH_PINNED_FILTERS_HTTP_STATUS))).thenReturn(
        Task.value(true));
    HttpStatus result = await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters(
        Mocks.SEAT_URN, Mocks.SEARCH_TYPE));
    assertThat(result).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test(description = "Test deleting with mobile search type fails")
  public void deletePinnedFiltersWithMobileSearchType() {
    Mocks mocks = new Mocks();
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(mocks._salesPinnedFiltersService.deleteEspressoPinnedFilters
          (
          Mocks.SEAT_URN, SearchType.MOBILE_ACCOUNT));
    }).withMessageContaining("400");
  }

  private PatchRequest<com.linkedin.salescustomfilterview.PinnedFilters> getSetPatchRequest() {
    DataMap patchData = new DataMap();
    com.linkedin.salescustomfilterview.PinnedFilters setPinnedFilters = new com.linkedin.salescustomfilterview.PinnedFilters();
    setPinnedFilters.setFilters(new StringArray(ImmutableList.of("CURRENT_TITLE")));
    patchData.put("$set", setPinnedFilters.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  private PatchRequest<com.linkedin.salescustomfilterview.PinnedFilters> getUnsetPatchRequest() {
    DataMap patchData = new DataMap();
    com.linkedin.salescustomfilterview.PinnedFilters setPinnedFilters = new com.linkedin.salescustomfilterview.PinnedFilters();
    setPinnedFilters.setFilters(new StringArray(ImmutableList.of("CURRENT_TITLE")));
    patchData.put("$unset", setPinnedFilters.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  static class Mocks {
    @Mock
    private final LssCustomFilterViewDB _lssCustomFilterViewDB;
    @Mock
    private final LixService _lixService;

    private static final String SERIALIZED_SEAT_URN = "urn:li:seat:2000";
    private static final String SERIALIZED_CONTRACT_URN = "urn:li:contract:8000";
    private static final String STRING_SEARCH_TYPE = "LEAD";
    private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
    private static final ContractUrn CONTRACT_URN = new ContractUrn(8000L);
    private static final SearchType SEARCH_TYPE = SearchType.LEAD;

    private final SalesPinnedFiltersService _salesPinnedFiltersService;
    Mocks() {
      _lixService = Mockito.mock(LixService.class);
      _lssCustomFilterViewDB = Mockito.mock(LssCustomFilterViewDB.class);
      when(_lixService.isMemberBasedLixEnabledForSeat(any(),
          eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_MEMBER))).thenReturn(Task.value(false));
      when(_lixService.isContractBasedLixEnabled(any(),
          eq(LixUtils.LSS_SEARCH_SALES_INTELLIGENCE_FILTERS_CONTRACT))).thenReturn(Task.value(false));
      _salesPinnedFiltersService = new SalesPinnedFiltersService(_lssCustomFilterViewDB, _lixService);
    }
  }
}
