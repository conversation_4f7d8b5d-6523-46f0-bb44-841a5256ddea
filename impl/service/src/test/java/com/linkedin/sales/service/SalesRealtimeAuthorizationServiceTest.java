package com.linkedin.sales.service;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.SalesIdentityUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.realtimedispatcher.Authorization;
import com.linkedin.realtimedispatcher.AuthorizationStatus;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.SalesIdentity;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.sales.urn.SalesRelationshipMapChangeLogsTopicUrn;
import com.linkedin.sales.client.common.SalesIdentityClient;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.urn.SalesSharedResourceViewersTopicUrn;
import com.linkedin.salessharing.AccessDecision;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


public class SalesRealtimeAuthorizationServiceTest extends ServiceUnitTest {
  private static final SeatUrn SEAT_URN = new SeatUrn(20L);
  private static final long LIST_ID = 1234L;
  private static final long SALES_IDENTITY_ID = 1000L;
  private static final long SALES_SEAT_ID = 1100L;
  private static final Urn RESOURCE_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, LIST_ID);
  private static final SalesListUrn SALES_LIST_URN = UrnUtils.createSalesListUrn(RESOURCE_URN);
  private static final Urn SHARED_RESOURCE_TOPIC_URN = new SalesSharedResourceViewersTopicUrn(RESOURCE_URN.toString());
  private static final Urn CHANGE_LOG_RESOURCE_TOPIC_URN = new SalesRelationshipMapChangeLogsTopicUrn(SALES_LIST_URN);
  private static final Urn SALES_IDENTITY_URN = new SalesIdentityUrn(SALES_IDENTITY_ID);
  private static final SeatUrn SALES_SEAT_URN = new SeatUrn(SALES_SEAT_ID);

  @Mock
  private AclServiceDispatcher _aclServiceDispatcher;
  @Mock
  private SalesIdentityClient _salesIdentityClient;

  private SalesRealtimeAuthorizationService _salesRealtimeAuthorizationService;

  @BeforeMethod
  public void resetMock() {
    reset(_aclServiceDispatcher);
    reset(_salesIdentityClient);
  }

  @BeforeTest
  public void setup() {
    MockitoAnnotations.openMocks(this);
    _salesRealtimeAuthorizationService =
        new SalesRealtimeAuthorizationService(_aclServiceDispatcher, _salesIdentityClient);
  }

  @Test
  void testNonValidSubscriber() {
    RestLiServiceException expection = runAndWaitException(
        _salesRealtimeAuthorizationService.authorizeForSharedResourceViewerTopic(SHARED_RESOURCE_TOPIC_URN,
            SALES_SEAT_URN), RestLiServiceException.class);
    assertThat(expection.getStatus()).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  void testSubscribeToTopicWithFailedDownstreamCall() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    runAndWaitException(_salesRealtimeAuthorizationService.authorizeForSharedResourceViewerTopics(
        ImmutableSet.of(SHARED_RESOURCE_TOPIC_URN), SALES_IDENTITY_URN), RestLiServiceException.class);
  }

  @Test
  void testSubscribeToSharedResourceTopicsWithInValidTopicUrn() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.value(new SalesIdentity().setOwner(SALES_SEAT_URN)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    BatchResult<Urn, Authorization> res = await(
        _salesRealtimeAuthorizationService.authorizeForSharedResourceViewerTopics(
            ImmutableSet.of(CHANGE_LOG_RESOURCE_TOPIC_URN), SALES_IDENTITY_URN));
    assertThat(res.size()).isEqualTo(0);
    assertThat(res.getErrors().get(CHANGE_LOG_RESOURCE_TOPIC_URN)).isInstanceOf(RestLiServiceException.class);
    assertThat(res.getErrors().get(CHANGE_LOG_RESOURCE_TOPIC_URN).getStatus()).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  void testSubscribeSharedResourceTopicHappyPath() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.value(new SalesIdentity().setId(SALES_IDENTITY_ID).setOwner(SALES_SEAT_URN)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    Authorization res = await(
        _salesRealtimeAuthorizationService.authorizeForSharedResourceViewerTopic(SHARED_RESOURCE_TOPIC_URN,
            SALES_IDENTITY_URN));
    assertThat(res.getStatus() == AuthorizationStatus.APPROVED).isTrue();
  }

  @Test
  void testSubscribeSharedResourceTopicsHappyPath() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.value(new SalesIdentity().setId(SALES_IDENTITY_ID).setOwner(SALES_SEAT_URN)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    BatchResult<Urn, Authorization> res = await(
        _salesRealtimeAuthorizationService.authorizeForSharedResourceViewerTopics(
            ImmutableSet.of(SHARED_RESOURCE_TOPIC_URN), SALES_IDENTITY_URN));
    assertThat(res.size()).isEqualTo(1);
    assertThat(res.get(SHARED_RESOURCE_TOPIC_URN).getStatus()).isEqualTo(AuthorizationStatus.APPROVED);
  }

  @Test
  void testSubscribeChangeLogTopicHappyPath() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.value(new SalesIdentity().setOwner(SALES_SEAT_URN)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    Authorization res = await(
        _salesRealtimeAuthorizationService.authorizeForRelationshipMapChangeLogTopic(CHANGE_LOG_RESOURCE_TOPIC_URN,
            SALES_IDENTITY_URN));
    assertThat(res.getStatus() == AuthorizationStatus.APPROVED).isTrue();
  }

  @Test
  void testSubscribeChangeLogTopicsHappyPath() {
    when(_salesIdentityClient.get(anyLong(), any())).thenReturn(
        Task.value(new SalesIdentity().setId(SALES_IDENTITY_ID).setOwner(SALES_SEAT_URN)));
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    BatchResult<Urn, Authorization> res = await(
        _salesRealtimeAuthorizationService.authorizeForRelationshipMapChangeLogTopics(
            ImmutableSet.of(CHANGE_LOG_RESOURCE_TOPIC_URN), SALES_IDENTITY_URN));
    assertThat(res.size()).isEqualTo(1);
    assertThat(res.get(CHANGE_LOG_RESOURCE_TOPIC_URN).getStatus()).isEqualTo(AuthorizationStatus.APPROVED);
  }
}

