package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssRecentViewsDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.RecentViewEntity;
import com.linkedin.sales.espresso.RecentViews;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesrecentactivities.RecentActivity;
import com.linkedin.salesrecentactivities.RecentActivityType;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SalesRecentActivitiesServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 2000L;
  private static final String SEARIALIZED_SEAT_URN = "urn:li:seat:2000";
  private static final String SERIALIZED_CONTRACT_URN = "urn:li:contract:8000";
  private static final String SERIALIZED_MEMBER_URN = "urn:li:member:1000";
  private static final String SERIALIZED_ORGANIZATION_URN = "urn:li:organization:3000";
  private static final String SERIALIZED_ORGANIZATION_URN2 = "urn:li:organization:4000";
  private static final long SEAT_ID2 = 2001L;

  @Mock
  private LssRecentViewsDB _lssRecentViewsDB;

  private SalesRecentActivitiesService _salesRecentActivitiesService;

  @BeforeMethod
  public void setup() {
    _lssRecentViewsDB = Mockito.mock(LssRecentViewsDB.class);
    _salesRecentActivitiesService = new SalesRecentActivitiesService(_lssRecentViewsDB);
  }

  /**
   * Get Recent Activity
   */
  @Test
  public void getRecentViewsSucceed() {
    SeatUrn seatUrn;
    long timestamp = System.currentTimeMillis();
    try {
      seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);

      RecentViews recentViews = new RecentViews();
      RecentViewEntity recentViewEntity = new RecentViewEntity();
      recentViewEntity.entity = SERIALIZED_ORGANIZATION_URN2;
      recentViewEntity.lastViewedTime = timestamp;
      recentViews.entities = new ArrayList<>();
      recentViews.entities.add(recentViewEntity);

      when(_lssRecentViewsDB.getRecentViews(eq(SEARIALIZED_SEAT_URN),eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(recentViews));

      List<RecentActivity> result = await(_salesRecentActivitiesService.getEspressoRecentViews(seatUrn,
          RecentActivityType.COMPANY));
      assertThat(result.size()).isEqualTo(1);
      assertThat(result.get(0).getEntity().isOrganizationUrn()).isTrue();
      assertThat(result.get(0).getEntity().getOrganizationUrn().toString()).isEqualToIgnoringCase(SERIALIZED_ORGANIZATION_URN2);
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }

  }

  /**
   * Test failing to find recent views should not throw exception
   */
  @Test
  public void getRecentViewsFailed() {
    SeatUrn seatUrn = new SeatUrn(SEAT_ID);
    when(_lssRecentViewsDB.getRecentViews(eq(SEARIALIZED_SEAT_URN),eq(RecentActivityType.COMPANY.name())))
        .thenReturn(Task.failure(new EntityNotFoundException(null, "test")));
    List<RecentActivity> result = await(_salesRecentActivitiesService.getEspressoRecentViews(seatUrn,
        RecentActivityType.COMPANY));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test(description = "Test successfully deleting records")
  public void deleteRecentActivitySucceed() {
    try {
      SeatUrn seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);
      when(_lssRecentViewsDB.deleteRecentViews(eq(SEARIALIZED_SEAT_URN), eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(true));

      Boolean result = await(_salesRecentActivitiesService.deleteEspressoRecentViews(seatUrn, RecentActivityType.COMPANY));
      assertThat(result).isTrue();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

  @Test(description = "Test failing to find records to delete should not exception out")
  public void deleteRecentActivityFailed() {
    try {
      SeatUrn seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);
      when(_lssRecentViewsDB.deleteRecentViews(eq(SEARIALIZED_SEAT_URN), eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(false));

      Boolean result = await(_salesRecentActivitiesService.deleteEspressoRecentViews(seatUrn, RecentActivityType.COMPANY));
      assertThat(result).isFalse();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }
  /**
   * Test Update existing Recent Activity
   */
  @Test
  public void upsertRecentViewsAppendSucceed() {
    SeatUrn seatUrn;
    ContractUrn contractUrn;
    OrganizationUrn organizationUrn;
    RecentActivity recentActivity = new RecentActivity();
    long timestamp = System.currentTimeMillis();
    try {
      seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);
      contractUrn = ContractUrn.deserialize(SERIALIZED_CONTRACT_URN);
      organizationUrn = OrganizationUrn.deserialize(SERIALIZED_ORGANIZATION_URN);
      RecentActivity.Entity entity = RecentActivity.Entity.createWithOrganizationUrn(organizationUrn);
      recentActivity.setEntity(entity);
      recentActivity.setLastActionTime(timestamp);

      RecentViews recentViews = new RecentViews();
      RecentViewEntity recentViewEntity = new RecentViewEntity();
      recentViewEntity.entity = SERIALIZED_ORGANIZATION_URN2;
      recentViewEntity.lastViewedTime = timestamp;
      recentViews.entities = new ArrayList<>();
      recentViews.entities.add(recentViewEntity);

      when(_lssRecentViewsDB.getRecentViews(eq(SEARIALIZED_SEAT_URN),eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(recentViews));

      when(_lssRecentViewsDB.updateRecentViews(eq(SEARIALIZED_SEAT_URN), eq(RecentActivityType.COMPANY.name()), any()))
          .thenReturn(Task.value(true));

      boolean result = await(_salesRecentActivitiesService.upsertRecentViews(seatUrn,
          RecentActivityType.COMPANY, contractUrn, recentActivity));
      assertThat(result).isTrue();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

  /**
   * Create new Recent Activity
   */

  @Test
  public void upsertRecentViewsCreateSucceed() {
    SeatUrn seatUrn;
    ContractUrn contractUrn;
    OrganizationUrn organizationUrn;
    RecentActivity recentActivity = new RecentActivity();
    long timestamp = System.currentTimeMillis();
    try {
      seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);
      contractUrn = ContractUrn.deserialize(SERIALIZED_CONTRACT_URN);
      organizationUrn = OrganizationUrn.deserialize(SERIALIZED_ORGANIZATION_URN);
      RecentActivity.Entity entity = RecentActivity.Entity.createWithOrganizationUrn(organizationUrn);
      recentActivity.setEntity(entity);
      recentActivity.setLastActionTime(timestamp);

      RecentViews recentViews = new RecentViews();
      RecentViewEntity recentViewEntity = new RecentViewEntity();
      recentViewEntity.entity = SERIALIZED_ORGANIZATION_URN2;
      recentViewEntity.lastViewedTime = timestamp;
      recentViews.entities = new ArrayList<>();
      recentViews.entities.add(recentViewEntity);

      when(_lssRecentViewsDB.getRecentViews(eq(SEARIALIZED_SEAT_URN),eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(null));

      when(_lssRecentViewsDB.createRecentView(eq(SEARIALIZED_SEAT_URN), eq(RecentActivityType.COMPANY.name()), any()))
          .thenReturn(Task.value(true));

      boolean result = await(_salesRecentActivitiesService.upsertRecentViews(seatUrn,
          RecentActivityType.COMPANY, contractUrn, recentActivity));
      assertThat(result).isTrue();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

  /**
   * Replace timestamp for existing Recent Activity
   */
  @Test
  public void upsertRecentViewsReplaceSucceed() {
    SeatUrn seatUrn;
    ContractUrn contractUrn;
    OrganizationUrn organizationUrn;
    RecentActivity recentActivity = new RecentActivity();
    long oldTimestamp = 123L;
    long timestamp = 234L;
    long newTimestamp = 456L;
    try {
      seatUrn = SeatUrn.deserialize(SEARIALIZED_SEAT_URN);
      contractUrn = ContractUrn.deserialize(SERIALIZED_CONTRACT_URN);
      organizationUrn = OrganizationUrn.deserialize(SERIALIZED_ORGANIZATION_URN);
      RecentActivity.Entity entity = RecentActivity.Entity.createWithOrganizationUrn(organizationUrn);
      recentActivity.setEntity(entity);
      recentActivity.setLastActionTime(timestamp);

      RecentViews recentViews = new RecentViews();
      RecentViewEntity recentViewEntity = new RecentViewEntity();
      recentViewEntity.entity = SERIALIZED_ORGANIZATION_URN;
      recentViewEntity.lastViewedTime = oldTimestamp;
      recentViews.entities = new ArrayList<>();
      recentViews.entities.add(recentViewEntity);

      RecentViews newRecentViews = new RecentViews();
      RecentViewEntity newRecentViewEntity = new RecentViewEntity();
      newRecentViewEntity.entity = recentViews.entities.get(0).entity;
      newRecentViewEntity.lastViewedTime = timestamp;
      newRecentViews.entities = new ArrayList<>();
      newRecentViews.entities.add(newRecentViewEntity);

      when(_lssRecentViewsDB.getRecentViews(eq(SEARIALIZED_SEAT_URN),eq(RecentActivityType.COMPANY.name())))
          .thenReturn(Task.value(recentViews));

      when(_lssRecentViewsDB.updateRecentViews(eq(SEARIALIZED_SEAT_URN), eq(RecentActivityType.COMPANY.name()), any()))
          .thenReturn(Task.value(true));

      await(_salesRecentActivitiesService.upsertRecentViews(seatUrn, RecentActivityType.COMPANY, contractUrn, recentActivity));

      // verify that the db is being updated with same entity but updated timestamp
      verify(_lssRecentViewsDB, times(1)).updateRecentViews(SEARIALIZED_SEAT_URN,RecentActivityType.COMPANY.name(), newRecentViews);

      recentActivity = new RecentActivity();
      recentActivity.setEntity(entity);
      recentActivity.setLastActionTime(newTimestamp);
      await(_salesRecentActivitiesService.upsertRecentViews(seatUrn, RecentActivityType.COMPANY, contractUrn, recentActivity));

      newRecentViews = new RecentViews();
      recentViewEntity = new RecentViewEntity();
      recentViewEntity.entity = recentViews.entities.get(0).entity;
      recentViewEntity.lastViewedTime = newTimestamp;
      newRecentViews.entities = new ArrayList<>();
      newRecentViews.entities.add(recentViewEntity);

      // verify that the db is being updated with same entity second time but with more updated timestamp
      verify(_lssRecentViewsDB, times(2)).updateRecentViews(SEARIALIZED_SEAT_URN, RecentActivityType.COMPANY.name(), newRecentViews);

    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

}
