package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.RequiredFieldNotPresentException;
import com.linkedin.data.template.SetMode;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lssSearch.SearchQuery;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssRecentViewsDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.ds.db.exception.TooManyRequestsException;
import com.linkedin.sales.espresso.RecentSearchesV2;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesrecentactivities.RecentActivityType;
import com.linkedin.salesrecentactivities.RecentSearch;
import com.linkedin.salesrecentactivities.RecentSearchKey;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * This is the class to test {@link SalesRecentSearchesService}
 * <AUTHOR>
 */
public class SalesRecentSearchesServiceTest extends ServiceUnitTest {
  private static final long RECENT_SEARCH_ID = 3L;
  private static final String SERIALIZED_CONTRACT_URN = "urn:li:contract:8000";
  private static final String SERIALIZED_CONTRACT_URN2 = "urn:li:contract:9000";
  private static final String SERIALIZED_SEARCH_QUERY = "{\"testQuery\":\"hello\"}";
  private static final String SERIALIZED_SEARCH_QUERY_RESULT = "{testQuery=hello}";
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
  private static final SearchQuery searchQuery1 = new SearchQuery().setKeywords("Test Engineer");
  private static final SearchQuery searchQuery2 = new SearchQuery().setKeywords("Sales Test Engineer");
  private static final String PEOPLE_SEARCH_QUERY_SHARE_TYPE = "P_SEARCH_SHARE";
  private static final String PEOPLE_SEARCH_TYPE = "PEOPLE_SEARCH";

  private RecentSearchKey _recentSearchKey;
  private RecentSearch _recentSearch;
  private RecentSearch _peopleSearchShare;

  @Mock
  private LssRecentViewsDB _lssRecentViewsDB;
  private SalesRecentSearchesService _salesRecentSearchesService;

  @BeforeMethod
  public void setup() {
    _lssRecentViewsDB = Mockito.mock(LssRecentViewsDB.class);
    _salesRecentSearchesService = new SalesRecentSearchesService(_lssRecentViewsDB);
    _recentSearchKey = new RecentSearchKey()
        .setSeat(SEAT_URN)
        .setRecentActivityType(RecentActivityType.PEOPLE_SEARCH)
        .setId(RECENT_SEARCH_ID);

    try {
      _recentSearch = new RecentSearch()
          .setRecentActivityType(RecentActivityType.PEOPLE_SEARCH)
          .setContract(ContractUrn.deserialize(SERIALIZED_CONTRACT_URN))
          .setSeat(SEAT_URN)
          .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery1));
    }
    catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }

    try {
      _peopleSearchShare = new RecentSearch()
          .setRecentActivityType(RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE)
          .setContract(ContractUrn.deserialize(SERIALIZED_CONTRACT_URN))
          .setSeat(SEAT_URN)
          .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery1));
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

  /**
   * Helper to create a RecentSearchesV2 record
   */
  private RecentSearchesV2 createRecentSearchesV2Record(long timestamp) {
    RecentSearchesV2 recentSearches = new RecentSearchesV2();
    recentSearches.searchQuery = SERIALIZED_SEARCH_QUERY;
    recentSearches.lastSearchedTime = timestamp;
    recentSearches.contractUrn = SERIALIZED_CONTRACT_URN;
    return recentSearches;
  }

  /**
   * Get Recent Searches List by Seat
   */
  @Test
  public void getRecentSearchesSucceed() {
    long timestamp = System.currentTimeMillis();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);
    Pair<Long, String> espressoKey = new Pair<>(RECENT_SEARCH_ID, RecentActivityType.PEOPLE_SEARCH.name());
    Pair<Pair<Long, String>, RecentSearchesV2> recentSearchPair = new Pair<>(espressoKey, recentSearches);
    List response = new ArrayList<Pair<Long, RecentSearchesV2>>();
    response.add(recentSearchPair);

    when(_lssRecentViewsDB.getRecentSearches(eq(SEAT_URN),eq(PEOPLE_SEARCH_TYPE), eq(0), eq(5)))
        .thenReturn(Task.value(response));

    List<RecentSearch> result = await(_salesRecentSearchesService.getEspressoRecentSearchesBySeat(SEAT_URN,
        RecentActivityType.PEOPLE_SEARCH, 0, 5));
    assertThat(result.size()).isEqualTo(1);
    assertThat(result.get(0).getRecentActivityType()).isEqualTo(RecentActivityType.PEOPLE_SEARCH);
    assertThat(result.get(0).hasSearchQuery()).isTrue();
    assertThat(result.get(0).getSearchQuery().isPeopleSearchQuery()).isTrue();
    assertThat(result.get(0).getSearchQuery().getPeopleSearchQuery().toString()).isEqualToIgnoringCase(SERIALIZED_SEARCH_QUERY_RESULT);
  }

  /**
   * Get Recent Searches List by Seat Shared search type
   */
  @Test
  public void getRecentSharedSearchesSucceed() {
    long timestamp = System.currentTimeMillis();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);
    Pair<Long, String> espressoKey = new Pair<>(RECENT_SEARCH_ID, PEOPLE_SEARCH_QUERY_SHARE_TYPE);
    Pair<Pair<Long, String>, RecentSearchesV2> recentSearchPair = new Pair<>(espressoKey, recentSearches);
    List response = new ArrayList<Pair<Long, RecentSearchesV2>>();
    response.add(recentSearchPair);

    when(_lssRecentViewsDB.getRecentSearches(eq(SEAT_URN),eq(PEOPLE_SEARCH_QUERY_SHARE_TYPE), eq(0), eq(5)))
        .thenReturn(Task.value(response));

    List<RecentSearch> result = await(_salesRecentSearchesService.getEspressoRecentSearchesBySeat(SEAT_URN,
        RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE, 0, 5));
    assertThat(result.size()).isEqualTo(1);
    assertThat(result.get(0).getRecentActivityType()).isEqualTo(RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE);
    assertThat(result.get(0).hasSearchQuery()).isTrue();
    assertThat(result.get(0).getSearchQuery().isPeopleSearchQuery()).isTrue();
    assertThat(result.get(0).getSearchQuery().getPeopleSearchQuery().toString()).isEqualToIgnoringCase(SERIALIZED_SEARCH_QUERY_RESULT);
  }

  /**
   * Get Recent Searches List by Seat invalid type
   */
  @Test
  public void getRecentSearchesInvalidType() {
    long timestamp = System.currentTimeMillis();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);
    Pair<Long, String> espressoKey = new Pair<>(RECENT_SEARCH_ID, "DUMMY_TYPE");
    Pair<Pair<Long, String>, RecentSearchesV2> recentSearchPair = new Pair<>(espressoKey, recentSearches);
    List response = new ArrayList<Pair<Long, RecentSearchesV2>>();
    response.add(recentSearchPair);

    when(_lssRecentViewsDB.getRecentSearches(eq(SEAT_URN),eq(PEOPLE_SEARCH_QUERY_SHARE_TYPE), eq(0), eq(5)))
        .thenReturn(Task.value(response));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
            await(_salesRecentSearchesService.getEspressoRecentSearchesBySeat(SEAT_URN,
                RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE, 0, 5)))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Search type DUMMY_TYPE is not supported"));
  }

  /**
   * Get Recent Searches List by Seat Failed
   */
  @Test
  public void getRecentSearchesFailed() {
    long timestamp = System.currentTimeMillis();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);
    Pair<Long, RecentSearchesV2> recentSearchPair = new Pair<>(RECENT_SEARCH_ID, recentSearches);
    List response = new ArrayList<Pair<Long, RecentSearchesV2>>();
    response.add(recentSearchPair);

    when(_lssRecentViewsDB.getRecentSearches(eq(SEAT_URN),eq(PEOPLE_SEARCH_TYPE), eq(0), eq(5)))
        .thenReturn(Task.failure(new RuntimeException("recent searches failed with exception")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
      await(_salesRecentSearchesService.getEspressoRecentSearchesBySeat(SEAT_URN,
          RecentActivityType.PEOPLE_SEARCH, 0, 5)))
        .withCause(new RuntimeException("recent searches failed with exception"));
  }

  /**
   * Get Recent Searches List by Seat Empty
   */
  @Test
  public void getRecentSearchesSucceedEmpty() {
    List response = new ArrayList<Pair<Long, RecentSearchesV2>>();

    when(_lssRecentViewsDB.getRecentSearches(eq(SEAT_URN), eq(PEOPLE_SEARCH_TYPE), eq(0), eq(5)))
        .thenReturn(Task.value(response));

    List<RecentSearch> result = await(
        _salesRecentSearchesService.getEspressoRecentSearchesBySeat(SEAT_URN, RecentActivityType.PEOPLE_SEARCH, 0, 5));
    assertThat(result.size()).isEqualTo(0);
  }

  /**
   * Get Recent Search By Id
   */
  @Test
  public void getRecentSearchSucceed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    RecentSearch result = await(_salesRecentSearchesService.getEspressoRecentSearch(recentSearchKey,
        RecentActivityType.PEOPLE_SEARCH));
    assertThat(result.hasSearchQuery()).isTrue();
    assertThat(result.getRecentActivityType()).isEqualTo(RecentActivityType.PEOPLE_SEARCH);
    assertThat(result.getSearchQuery().isPeopleSearchQuery()).isTrue();
    assertThat(result.getSearchQuery().getPeopleSearchQuery().toString()).isEqualToIgnoringCase(SERIALIZED_SEARCH_QUERY_RESULT);
  }

  /**
   * Get Recent Shared Search succeeded
   */
  @Test
  public void getRecentSharedSearchSucceeded() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN), eq(PEOPLE_SEARCH_QUERY_SHARE_TYPE)))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(PEOPLE_SEARCH_QUERY_SHARE_TYPE), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    RecentSearch result = await(_salesRecentSearchesService.getEspressoRecentSearch(recentSearchKey,
        RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE));
    assertThat(result.hasSearchQuery()).isTrue();
    assertThat(result.getRecentActivityType()).isEqualTo(RecentActivityType.PEOPLE_SEARCH_QUERY_SHARE);
    assertThat(result.getSearchQuery().isPeopleSearchQuery()).isTrue();
    assertThat(result.getSearchQuery().getPeopleSearchQuery().toString()).isEqualToIgnoringCase(SERIALIZED_SEARCH_QUERY_RESULT);
  }

  /**
   * Get Recent Search By Id Failed
   */
  @Test
  public void getRecentSearchFailed() throws Exception {
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.failure(new EntityNotFoundException(null, "recent search record not found")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.getEspressoRecentSearch(recentSearchKey,
            RecentActivityType.PEOPLE_SEARCH))).withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
        "recent search record not found"));
  }

  /**
   * Create a new Recent Search
   */
  @Test
  public void createRecentSearchesSucceed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearch recentSearch = _recentSearch.clone()
        .setLastSearchedTime(timestamp);

    when(_lssRecentViewsDB.createRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    long resultSearchId = await(_salesRecentSearchesService.createRecentSearches(recentSearch));
    assertThat(resultSearchId).isEqualTo(RECENT_SEARCH_ID);
  }

  /**
   * Create a new Shared People Search
   */
  @Test
  public void createSharedPeopleSearchesSucceed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearch recentSearch = _peopleSearchShare.clone()
        .setLastSearchedTime(timestamp);

    when(_lssRecentViewsDB.createRecentSearch(eq(SEAT_URN), any(), eq(PEOPLE_SEARCH_QUERY_SHARE_TYPE)))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    long resultSearchId = await(_salesRecentSearchesService.createRecentSearches(recentSearch));
    assertThat(resultSearchId).isEqualTo(RECENT_SEARCH_ID);
  }

  /**
   * Create a new Recent Search failed with no seat
   */
  @Test
  public void createRecentSearchesFailedNoSeat() {
    try {
      long timestamp = System.currentTimeMillis();
      RecentSearch recentSearch = _recentSearch.clone()
            .setLastSearchedTime(timestamp)
            .setSeat(null,  SetMode.REMOVE_IF_NULL);

      when(_lssRecentViewsDB.createRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name())))
          .thenReturn(Task.value(RECENT_SEARCH_ID));
      await(_salesRecentSearchesService.createRecentSearches(recentSearch));
    } catch (Exception e) {
      assertThat(e).isInstanceOf(RequiredFieldNotPresentException.class);
    }
  }

  /**
   * Create a new Recent Search failed
   */
  @Test
  public void createRecentSearchesFailed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearch recentSearch = _recentSearch.clone()
        .setLastSearchedTime(timestamp);

    when(_lssRecentViewsDB.createRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.failure(new RuntimeException("create failed for recent search record")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
       await(_salesRecentSearchesService.createRecentSearches(recentSearch)))
        .withCause(new RuntimeException("create failed for recent search record"));
  }

  /**
   * Create a new Recent Search failed with too many requests exception
   */
  @Test
  public void createRecentSearchesFailedWithTooManyRequestsExceptions() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearch recentSearch = _recentSearch.clone()
        .setLastSearchedTime(timestamp);

    when(_lssRecentViewsDB.createRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.failure(new TooManyRequestsException("create failed for recent search record")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.createRecentSearches(recentSearch)))
        .withCause(new RestLiServiceException(HttpStatus.S_429_TOO_MANY_REQUESTS,
            "create failed for recent search record"));
  }

  /**
   * Update an existing Recent Search
   */
  @Test
  public void updateRecentSearchesSucceed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearch recentSearch = _recentSearch.clone()
        .setId(RECENT_SEARCH_ID)
        .setLastSearchedTime(timestamp)
        .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery2));
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    long resultSearchId = await(_salesRecentSearchesService.updateRecentSearches(recentSearchKey, recentSearch));
    assertThat(resultSearchId).isEqualTo(RECENT_SEARCH_ID);
  }

  /**
   * Update an existing Recent Search failed due to incorrect contract
   */
  @Test
  public void updateRecentSearchesFailedContractMismatch() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearch recentSearch = _recentSearch.clone()
        .setId(RECENT_SEARCH_ID)
        .setLastSearchedTime(timestamp)
        .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery2));
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);
    recentSearches.contractUrn = SERIALIZED_CONTRACT_URN2;

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.value(RECENT_SEARCH_ID));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.updateRecentSearches(recentSearchKey, recentSearch))).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
        "Incorrect contract urn sent in the request key"));
  }

  /**
   * Update a Recent Search failed
   */
  @Test
  public void updateRecentSearchesFailed() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearch recentSearch = _recentSearch.clone()
        .setId(RECENT_SEARCH_ID)
        .setLastSearchedTime(timestamp)
        .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery2));
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.failure(new RuntimeException("update failed for recent search record")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.updateRecentSearches(recentSearchKey, recentSearch)))
        .withCause(new RuntimeException("update failed for recent search record"));
  }

  /**
   * Update a Recent Search failed with too many requests exception
   */
  @Test
  public void updateRecentSearchesFailedWithTooManyRequestsException() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearch recentSearch = _recentSearch.clone()
        .setId(RECENT_SEARCH_ID)
        .setLastSearchedTime(timestamp)
        .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery2));
    RecentSearchesV2 recentSearches = createRecentSearchesV2Record(timestamp);

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.value(recentSearches));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.failure(new TooManyRequestsException("update failed for recent search record")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.updateRecentSearches(recentSearchKey, recentSearch)))
        .withCause(new RestLiServiceException(HttpStatus.S_429_TOO_MANY_REQUESTS, "update failed for recent search record"));
  }

  /**
   * Update a Recent Search failed for non existing entity
   */
  @Test
  public void updateRecentSearchesFailedForNonExistingEntity() throws Exception {
    long timestamp = System.currentTimeMillis();
    RecentSearchKey recentSearchKey = _recentSearchKey.clone();
    RecentSearch recentSearch = _recentSearch.clone()
        .setId(RECENT_SEARCH_ID)
        .setLastSearchedTime(timestamp)
        .setSearchQuery(RecentSearch.SearchQuery.createWithPeopleSearchQuery(searchQuery2));

    when(_lssRecentViewsDB.getRecentSearch(any(), eq(SEAT_URN),eq(RecentActivityType.PEOPLE_SEARCH.name())))
        .thenReturn(Task.failure(new EntityNotFoundException(null, "recent search record not found")));
    when(_lssRecentViewsDB.updateRecentSearch(eq(SEAT_URN), any(), eq(RecentActivityType.PEOPLE_SEARCH.name()), eq(RECENT_SEARCH_ID)))
        .thenReturn(Task.failure(new RuntimeException("update failed for recent search record")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesRecentSearchesService.updateRecentSearches(recentSearchKey, recentSearch)))
        .withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
            "recent search record not found"));
  }

  @Test(description = "Test successfully deleting records")
  public void deleteRecentSearchesSucceed() {
    try {
      when(_lssRecentViewsDB.deleteRecentSearches(eq(SEAT_URN.toString()), eq(RecentActivityType.PEOPLE_SEARCH.name())))
          .thenReturn(Task.value(true));

      Boolean result = await(_salesRecentSearchesService.deleteEspressoRecentSearches(SEAT_URN, RecentActivityType.PEOPLE_SEARCH));
      assertThat(result).isTrue();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }

  @Test(description = "Test failing to find records to delete should not exception out")
  public void deleteRecentSearchesFailed() {
    try {
      when(_lssRecentViewsDB.deleteRecentSearches(eq(SEAT_URN.toString()), eq(RecentActivityType.PEOPLE_SEARCH.name())))
          .thenReturn(Task.value(false));

      Boolean result = await(_salesRecentSearchesService.deleteEspressoRecentSearches(SEAT_URN, RecentActivityType.PEOPLE_SEARCH));
      assertThat(result).isFalse();
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(URISyntaxException.class);
    }
  }
}
