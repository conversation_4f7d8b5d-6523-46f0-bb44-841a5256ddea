package com.linkedin.sales.service;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.admin.CreditGrant;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRole;
import com.linkedin.sales.admin.SeatRoleAllocation;
import com.linkedin.sales.admin.SeatRoleAllocationArray;
import com.linkedin.sales.client.common.SalesCreditGrantsClient;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesbenefits.SalesSubscriptionBenefits;
import com.linkedin.talent.decorator.PathSpecSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.SalesSubscriptionBenefitsService.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * This is the class to test {@link SalesSubscriptionBenefitsService}
 * <AUTHOR>
 */
public class SalesSubscriptionBenefitsServiceTest extends ServiceUnitTest {
  private static final MemberUrn member1 = new MemberUrn(1000L);
  private static final ContractUrn proContractUrn = new ContractUrn(2000L);
  private static final List<ContractUrn> proContractUrnList = Collections.singletonList(proContractUrn);
  private static final ContractUrn teamsContractUrn = new ContractUrn(5000L);
  private static final List<ContractUrn> teamsContractUrnList = Collections.singletonList(teamsContractUrn);
  private static final SeatUrn seatUrn = new SeatUrn(3000L);
  private static final SalesSeat salesSeat = new SalesSeat().setId(3000);
  private static final Task<List<SalesSeat>> salesSeatsTask = Task.value(Collections.singletonList(salesSeat));
  private static final Task<List<SalesSeat>> salesSeatsTaskInvalidSeat = Task.value(new ArrayList<>());
  private static final int savedLeadsCount = 10;
  private static final Task<Integer> savedLeadsCountTask = Task.value(savedLeadsCount);
  private static final int savedAccountsCount = 20;
  private static final Task<Integer> savedAccountsCountTask = Task.value(savedAccountsCount);
  private static final int remainingInMailCredit = 250;

  private SalesSubscriptionBenefitsService _salesSubscriptionBenefitsService;

  @Mock
  private SalesSeatClient _salesSeatClient;
  @Mock
  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;
  @Mock
  private SalesCreditGrantsClient _salesCreditGrantsClient;
  @Mock
  private SalesContractService _salesContractService;

  @BeforeTest(alwaysRun = true)
  public void setup() {
    MockitoAnnotations.openMocks(this);
    _salesSubscriptionBenefitsService = new SalesSubscriptionBenefitsService(_salesSeatClient, _lssSavedLeadAccountDB,
        _salesCreditGrantsClient, _salesContractService);
  }

  @Test(description = "invalid seat")
  public void testGetSubscriptionBenefitsForInvalidSeat() {
    when(_salesSeatClient.findByMember(any(), anyCollection(), any(), any(), any()))
        .thenReturn(salesSeatsTaskInvalidSeat);

    PathSpecSet projection = PathSpecSet.of(new HashSet<>(Arrays.asList(
        SalesSubscriptionBenefits.fields().savedLeadsCount(),
        SalesSubscriptionBenefits.fields().savedAccountsCount())));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesSubscriptionBenefitsService
            .getSubscriptionBenefits(member1, proContractUrn, projection)))
        .withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
            "User does not have any assigned seat on Sales Navigator contract"));
  }

  @DataProvider
  public static Object[][] savedLeadsAndAccountsDataProvider() {
    return new Object[][] {
        new Object[] { proContractUrn, (long) savedLeadsCount, (long) savedAccountsCount },
        new Object[] { teamsContractUrn, DEFAULT_SAVED_LEADS_COUNT, DEFAULT_SAVED_ACCOUNTS_COUNT }
    };
  }

  @Test(description = "get subscription benefits for saved leads and accounts",
  dataProvider = "savedLeadsAndAccountsDataProvider")
  public void testGetSubscriptionBenefitsSavedLeadsAndAccounts(ContractUrn contractUrn,
      Long expectedSavedLeadsCount, Long expectedSavedAccountsCount) {
    stubLssSavedLeadAccountDB();
    stubSalesSeatClient();
    stubSalesContractService();

    PathSpecSet projection = PathSpecSet.of(new HashSet<>(Arrays.asList(
        SalesSubscriptionBenefits.fields().savedLeadsCount(),
        SalesSubscriptionBenefits.fields().savedAccountsCount())));

    SalesSubscriptionBenefits salesSubscriptionBenefits = await(
        _salesSubscriptionBenefitsService
            .getSubscriptionBenefits(member1, contractUrn, projection));

    Assert.assertNotNull(salesSubscriptionBenefits.getSavedLeadsCount());
    Assert.assertEquals(salesSubscriptionBenefits.getSavedLeadsCount(), expectedSavedLeadsCount);
    Assert.assertNotNull(salesSubscriptionBenefits.getSavedAccountsCount());
    Assert.assertEquals(salesSubscriptionBenefits.getSavedAccountsCount(), expectedSavedAccountsCount);
    Assert.assertNull(salesSubscriptionBenefits.getUnusedInmailCredits());
    Assert.assertNull(salesSubscriptionBenefits.getMessageThreadsCount());
  }

  @DataProvider
  public static Object[][] unusedInMailCreditsDataProvider() {
    return new Object[][] {
        new Object[] { proContractUrn, (long) remainingInMailCredit },
        new Object[] { teamsContractUrn, DEFAULT_UNUSED_INMAIL_CREDITS }
    };
  }

  @Test(description = "get subscription benefits for unused inmail Credits",
      dataProvider = "unusedInMailCreditsDataProvider")
  public void testGetSubscriptionBenefitsInmailCredits(ContractUrn contractUrn, Long expectedUnusedInmailCredits) {
    stubLssSavedLeadAccountDB();
    stubSalesSeatClient();
    stubSalesContractService();
    when(_salesCreditGrantsClient.getInmailCredits(contractUrn, seatUrn))
        .thenReturn(Task.value(Collections.singletonList(new CreditGrant()
            .setNumCreditsRemaining(remainingInMailCredit))));

    PathSpecSet projection = PathSpecSet.of(new HashSet<>(Collections.singletonList(
        SalesSubscriptionBenefits.fields().unusedInmailCredits())));

    SalesSubscriptionBenefits salesSubscriptionBenefits = await(
        _salesSubscriptionBenefitsService
            .getSubscriptionBenefits(member1, contractUrn, projection));

    Assert.assertNotNull(salesSubscriptionBenefits.getUnusedInmailCredits());
    Assert.assertEquals(salesSubscriptionBenefits.getUnusedInmailCredits(), expectedUnusedInmailCredits);
    Assert.assertNull(salesSubscriptionBenefits.getSavedLeadsCount());
    Assert.assertNull(salesSubscriptionBenefits.getSavedAccountsCount());
    Assert.assertNull(salesSubscriptionBenefits.getMessageThreadsCount());
  }

  @Test(description = "get subscription benefits for unused inmail Credits when downstream doesn't return any credits")
  public void testGetSubscriptionBenefitsInmailCreditsForDefault() {
    stubLssSavedLeadAccountDB();
    stubSalesSeatClient();
    stubSalesContractService();
    when(_salesCreditGrantsClient.getInmailCredits(proContractUrn, seatUrn))
        .thenReturn(Task.value(Collections.emptyList()));

    PathSpecSet projection = PathSpecSet.of(new HashSet<>(Collections.singletonList(
        SalesSubscriptionBenefits.fields().unusedInmailCredits())));

    SalesSubscriptionBenefits salesSubscriptionBenefits = await(
        _salesSubscriptionBenefitsService
            .getSubscriptionBenefits(member1, proContractUrn, projection));

    Assert.assertNotNull(salesSubscriptionBenefits.getUnusedInmailCredits());
    Assert.assertEquals(salesSubscriptionBenefits.getUnusedInmailCredits().longValue(), DEFAULT_UNUSED_INMAIL_CREDITS);
    Assert.assertNull(salesSubscriptionBenefits.getSavedLeadsCount());
    Assert.assertNull(salesSubscriptionBenefits.getSavedAccountsCount());
    Assert.assertNull(salesSubscriptionBenefits.getMessageThreadsCount());
  }

  @Test(description = "get subscription benefits for Messaging history")
  public void testGetSubscriptionBenefitsMessageThreads() {
    stubLssSavedLeadAccountDB();
    stubSalesSeatClient();
    stubSalesContractService();

    PathSpecSet projection = PathSpecSet.of(new HashSet<>(Collections.singletonList(
        SalesSubscriptionBenefits.fields().messageThreadsCount())));

    SalesSubscriptionBenefits salesSubscriptionBenefits = await(
        _salesSubscriptionBenefitsService
            .getSubscriptionBenefits(member1, proContractUrn, projection));

    Assert.assertNotNull(salesSubscriptionBenefits.getMessageThreadsCount());
    Assert.assertEquals(salesSubscriptionBenefits.getMessageThreadsCount().longValue(), DEFAULT_TOTAL_MESSAGE_THREADS);
    Assert.assertNull(salesSubscriptionBenefits.getSavedLeadsCount());
    Assert.assertNull(salesSubscriptionBenefits.getSavedAccountsCount());
    Assert.assertNull(salesSubscriptionBenefits.getUnusedInmailCredits());
  }

  private void stubSalesSeatClient() {
    when(_salesSeatClient.findByMember(eq(member1), eq(proContractUrnList),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any()))
        .thenReturn(salesSeatsTask);
    when(_salesSeatClient.findByMember(eq(member1), eq(teamsContractUrnList),
        any(EnterpriseApplicationUsageUrn.class), isNull(), any()))
        .thenReturn(salesSeatsTask);
  }

  private void stubLssSavedLeadAccountDB() {
    when(_lssSavedLeadAccountDB.getSavedLeadCountForSeat(eq(seatUrn)))
        .thenReturn(savedLeadsCountTask);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(eq(seatUrn)))
        .thenReturn(savedAccountsCountTask);
  }

  private void stubSalesContractService() {
    SalesContract proContract = new SalesContract()
        .setSeatRoleAllocations(new SeatRoleAllocationArray(new SeatRoleAllocation()
            .setRole(SeatRole.SALES_SEAT_TIER1).setAllocation(1)));
    SalesContract teamContract = new SalesContract()
        .setSeatRoleAllocations(new SeatRoleAllocationArray(new SeatRoleAllocation()
            .setRole(SeatRole.SALES_SEAT_TIER2).setAllocation(2)));

    when(_salesContractService.getSalesContractById(proContractUrn.getIdAsLong(), SALES_CONTRACT_PATH_SPEC))
        .thenReturn(Task.value(proContract));
    when(_salesContractService.getSalesContractById(teamsContractUrn.getIdAsLong(), SALES_CONTRACT_PATH_SPEC))
        .thenReturn(Task.value(teamContract));
  }
}
