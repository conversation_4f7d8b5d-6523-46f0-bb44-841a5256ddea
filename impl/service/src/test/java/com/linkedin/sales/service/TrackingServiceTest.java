package com.linkedin.sales.service;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.events.UnifiedActionType;
import com.linkedin.events.federator.SalesActionEvent;
import com.linkedin.events.federator.UnifiedAction;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.tracker.producer.TrackingProducer;
import java.util.Collections;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


public class TrackingServiceTest extends BaseEngineParTest {


  private String _machineName = "machineName";
  private String _envName = "envName";
  private String _appName = "appName";
  private final MemberUrn viewee = new MemberUrn(12345L);
  private final MemberUrn viewer = new MemberUrn(1234666L);
  private final SeatUrn seatUrn = new SeatUrn(1111L);
  private final ContractUrn contractUrn = new ContractUrn(2222L);
  @Mock
  private TrackingProducer _trackingProducer;
  private TrackingService _trackingService;

  @BeforeTest
  public void setup() {
    MockitoAnnotations.initMocks(this);

    _trackingService =
        new TrackingService(_trackingProducer, _machineName, _envName, _appName);
    when(_trackingProducer.sendRecord(eq(ServiceConstants.SALES_ACTION_EVENT_NAME), any())).thenReturn(null);
  }

  @Test
  public void testCreateEvent() {
    SalesActionEvent salesActionEvent = _trackingService.
        createSalesActionEvent(UnifiedAction.SAVE, viewee.toString(), UnifiedActionType.SINGLE, Collections.emptyMap(),
            viewer, seatUrn, contractUrn);
    Assert.assertEquals(salesActionEvent.actionType, UnifiedActionType.SINGLE);
    Assert.assertEquals(salesActionEvent.viewerUrn, Urn.createFromTuple(ServiceConstants.SALES,
        viewer.getMemberIdEntity(), seatUrn.getSeatIdEntity(), contractUrn.getContractIdEntity()).toString());
    Assert.assertEquals(salesActionEvent.accountUrn, Urn.createFromTuple(ServiceConstants.SALES_ACCOUNT, -1L, -1L).toString());
  }

}
