package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.List;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Set;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for AccountAclService
 */
public class AccountMapAclServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID2 = 2001L;
  private static final long SEAT_ID3 = 2002L;
  private static final long SALES_LIST_ID = 1L;
  private static final long MEMBER_ID = 111L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(SEAT_ID2);
  private static final SeatUrn SEAT_URN3 = new SeatUrn(SEAT_ID3);
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final Urn SALES_LIST_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);
  private static final com.linkedin.sales.admin.SeatRoleArray SALES_SEAT_ROLES_TIER_3 =
      new com.linkedin.sales.admin.SeatRoleArray(new com.linkedin.sales.admin.SeatRoleArray(
          Collections.singletonList(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3)));
  private static final SalesSeat TIER_3_SALES_SEAT =
      new SalesSeat().setRoles(SALES_SEAT_ROLES_TIER_3).setContract(CONTRACT_URN);

  private LssSharingDB _lssSharingDB;
  private LssListDB _lssListDB;
  private SalesSeatClient _salesSeatClient;
  private AccountMapAclService _accountMapAclService;

  @BeforeMethod
  public void setup() {
    _lssSharingDB = Mockito.mock(LssSharingDB.class);
    _lssListDB = Mockito.mock(LssListDB.class);
    _salesSeatClient = Mockito.mock(SalesSeatClient.class);
    _accountMapAclService = new AccountMapAclService(_lssSharingDB, _lssListDB, _salesSeatClient);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnershipAndNoRoleExists() {
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID)));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, WRITER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));
    AccessDecision accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDenyWithOwnershipAndRoleExists() {
    java.util.List<Pair<Urn, ShareRole>> pairs = new ArrayList<>();
    pairs.add(new Pair<>(SEAT_URN2, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_URN.getIdAsLong())));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));
    AccessDecision accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
    pairs.add(new Pair<>(SEAT_URN3, ShareRole.WRITER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAdminAccessDecisionAllowWithSharingPermission() {
    java.util.List<Pair<Urn, ShareRole>> pairs = new ArrayList<>();
    pairs.add(new Pair<>(SEAT_URN, ShareRole.WRITER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, WRITER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    //Seat 2 is the owner
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_URN2.getIdAsLong())));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);

    pairs.add(new Pair<>(SEAT_URN2, ShareRole.OWNER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN2, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDeny() {
    java.util.List<Pair<Urn, ShareRole>> pairs = new ArrayList<>();
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, WRITER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));
    //Seat 2 is the owner
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_URN2.getIdAsLong())));
    AccessDecision accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);

    pairs.add(new Pair<>(SEAT_URN2, ShareRole.OWNER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);

    pairs.add(new Pair<>(SEAT_URN2, ShareRole.READER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckReadAccessDecisionForMapEntitiesReaderRole() {
    java.util.List<Pair<Urn, ShareRole>> pairs = new ArrayList<>();
    pairs.add(new Pair<>(SEAT_URN2, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, WRITER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));
    //Seat 2 is the owner
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_URN2.getIdAsLong())));

    AccessDecision accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ, SubResourceType.LIST_ENTITY));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);

    pairs.add(new Pair<>(SEAT_URN, ShareRole.READER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ, SubResourceType.NONE));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);

    // Add a member collaborator
    pairs.add(new Pair<>(MEMBER_URN, ShareRole.READER));
    accessDecision =
        await(_accountMapAclService.checkAccessDecision(MEMBER_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ, SubResourceType.LIST_ENTITY));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionRecoverFromException() {
    Set<ShareRole> permittedRoles = ACCOUNT_MAP_SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.failure(new RuntimeException()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        runAndWait(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  private List createEspressoList(long creatorSeatId) {
    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = creatorSeatId;
    espressoList.listType = ListType.LEAD;
    return espressoList;
  }
}
