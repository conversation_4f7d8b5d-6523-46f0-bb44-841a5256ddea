package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for AclServiceDispatcher
 * <AUTHOR>
 */
public class AclServiceDispatcherTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 2000L;
  private static final long SALES_LIST_ID = 1L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final Urn SALES_LIST_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);

  private ListAclService _listAclService;
  private AccountMapAclService _accountMapAclService;
  private NoteAclService _noteAclService;
  private LsiMetricsReportAclService _lsiMetricsReportAclService;

  private AclServiceDispatcher _aclServiceDispatcher;

  @BeforeMethod
  public void setup() {
    _listAclService = Mockito.mock(ListAclService.class);
    _accountMapAclService = Mockito.mock(AccountMapAclService.class);
    _noteAclService = Mockito.mock(NoteAclService.class);
    _lsiMetricsReportAclService = Mockito.mock(LsiMetricsReportAclService.class);
    _aclServiceDispatcher = new AclServiceDispatcher(_listAclService, _accountMapAclService,
        _noteAclService, _lsiMetricsReportAclService);
  }

  @Test
  public void testDispatchToSalesListSucceed() {
    when(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ))
        .thenReturn(Task.value(AccessDecision.ALLOWED));
    await(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    verify(_listAclService, times(1)).checkAccessDecision(any(), any(), any(), any());
  }

  @Test
  public void testDispatchToAccountMapSucceed() {
    when(_accountMapAclService.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ, SubResourceType.LIST_ENTITY))
        .thenReturn(Task.value(AccessDecision.ALLOWED));
    await(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.READ, SubResourceType.LIST_ENTITY));
    verify(_accountMapAclService, times(1)).checkAccessDecision(any(), any(), any(), any(), any());
  }

  @Test
  public void testDispatchToSalesNoteSucceed() {
    when(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_LIST_URN, AccessAction.READ))
        .thenReturn(Task.value(AccessDecision.ALLOWED));
    await(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_LIST_URN, AccessAction.READ));
    verify(_noteAclService, times(1)).checkAccessDecision(any(), any(), any(), any());
  }

  @Test
  public void testDispatchFailWithInvalidPolicyType() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.$UNKNOWN, SALES_LIST_URN, AccessAction.READ));
    }).withCause(new RuntimeException(String.format("unsupported policy type: %s", PolicyType.$UNKNOWN)));
  }
}
