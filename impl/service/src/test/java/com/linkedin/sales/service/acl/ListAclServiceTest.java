package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.admin.SeatRole;
import com.linkedin.sales.admin.SeatRoleArray;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.List;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.Collections;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;


public class ListAclServiceTest extends BaseEngineParJunitJupiterTest {
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID2 = 2001L;
  private static final long SALES_LIST_ID = 1L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final Urn SALES_LIST_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);
  private static final SeatRoleArray SALES_SEAT_ROLES_TIER_3 =
      new SeatRoleArray(new SeatRoleArray(
          Collections.singletonList(SeatRole.SALES_SEAT_TIER3)));
  private static final SeatRoleArray SALES_SEAT_ROLES_TIER_1 =
      new SeatRoleArray(new SeatRoleArray(
          Collections.singletonList(SeatRole.SALES_SEAT_TIER1)));
  private static final SalesSeat TIER_3_SALES_SEAT =
      new SalesSeat().setRoles(SALES_SEAT_ROLES_TIER_3).setContract(CONTRACT_URN);
  private static final SalesSeat TIER_1_SALES_SEAT =
      new SalesSeat().setRoles(SALES_SEAT_ROLES_TIER_1).setContract(CONTRACT_URN);

  @Mock
  private LssSharingDB _lssSharingDB;
  @Mock
  private LssListDB _lssListDB;
  @Mock
  private SalesSeatClient _salesSeatClient;
  private ListAclService _listAclService;

  @BeforeEach
  public void setUp() {
    _listAclService = new ListAclService(_lssSharingDB, _lssListDB, _salesSeatClient);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnership() {
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID)));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision = runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnershipAndSharingPermission() {
    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(SEAT_URN, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID)));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision = runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithSharingPermission() {
    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(SEAT_URN, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID2)));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision = runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDeny() {
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID2)));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionSelf() {
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID)));

    AccessDecision accessDecision = runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
    verify(_salesSeatClient, never()).getSeat(any(), any(), any(), any());
    verify(_lssSharingDB, never()).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testCheckAccessDecisionRecoverFromException() {
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenThrow(new RuntimeException());
    when(_lssListDB.getList(SALES_LIST_ID)).thenReturn(Task.value(createEspressoList(SEAT_ID2)));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), any(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision = runAndWait(_listAclService.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  private List createEspressoList(long creatorSeatId) {
    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = creatorSeatId;
    espressoList.listType = ListType.LEAD;
    return espressoList;
  }
}
