package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.salesinsights.SalesInsightsMetricsReportClient;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesinsights.MetricsReport;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.Collections;
import java.util.Set;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for LsiMetricsReportAclService
 */
public class LsiMetricsReportAclServiceTest extends ServiceUnitTest {
  private static final long METRICS_REPORT_ID = 100L;
  private static final long METRICS_REPORT_ID2 = 200L;
  private static final long METRICS_REPORT_ID3 = 300L;
  private static final long ENTERPRISE_ACCOUNT_ID = 2000L;
  private static final long ENTERPRISE_APPLICATION_INSTANCE_ID = 2000L;
  private static final long ENTERPRISE_APPLICATION_INSTANCE_ID2 = 3000L;
  private static final long ENTERPRISE_PROFILE_ID = 1L;
  private static final long ENTERPRISE_PROFILE_ID2 = 2L;
  private static final long ENTERPRISE_PROFILE_ID3 = 3L;
  private static final Urn METRICS_REPORT_URN =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID);
  private static final Urn METRICS_REPORT_URN2 =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID2);
  private static final Urn METRICS_REPORT_URN3 =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID3);

  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN2 =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID2);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN3 =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID2, ENTERPRISE_PROFILE_ID3);

  private static final Urn ENTERPRISE_APPLICATION_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseApplicationInstanceUrn.ENTITY_TYPE,
          Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID), ENTERPRISE_APPLICATION_INSTANCE_ID);

  private LssSharingDB _lssSharingDB;
  private SalesSeatClient _salesSeatClient;
  private SalesInsightsMetricsReportClient _salesInsightsMetricsReportClient;
  private LsiMetricsReportAclService _lsiMetricsReportAclService;

  @BeforeMethod
  public void setup() {
    _lssSharingDB = Mockito.mock(LssSharingDB.class);
    _salesSeatClient = Mockito.mock(SalesSeatClient.class);
    _salesInsightsMetricsReportClient = Mockito.mock(SalesInsightsMetricsReportClient.class);
    _lsiMetricsReportAclService = new LsiMetricsReportAclService(_lssSharingDB, _salesSeatClient, _salesInsightsMetricsReportClient);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnership() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    when(_lssSharingDB.getSubjectPolicy(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT.toString(), METRICS_REPORT_URN)).thenReturn(Task.value(subjectPolicy));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
            PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.UPDATE));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnershipAndSharingPermission() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    when(_lssSharingDB.getSubjectPolicy(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT.toString(), METRICS_REPORT_URN)).thenReturn(Task.value(subjectPolicy));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
            PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.UPDATE));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithSharingPermission() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(ENTERPRISE_APPLICATION_INSTANCE_URN, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2,
            PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDeny() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID2);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID2);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN2)))
        .thenReturn(Task.value(report));

    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN2, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN2, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionDeniedCheckMemberAllowedToShare() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    java.util.List<Pair<Urn, ShareRole>> pairs = Collections.singletonList(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.UPDATE);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN, AccessAction.UPDATE));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionWithOwnershipRecoverFromException() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenThrow(RuntimeException.class);

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionRecoverFromException() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenThrow(RuntimeException.class);

    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenThrow(RuntimeException.class);

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithSharingPermissionAcrossAppInstance() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(ENTERPRISE_APPLICATION_INSTANCE_URN, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDenyWithSharingPermissionAcrossAppInstance() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN)))
        .thenReturn(Task.value(report));

    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.UPDATE);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN, AccessAction.UPDATE));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionDenyWithSharingPermissionAcrossDifferentAppInstance() {
    EnterpriseProfileUrn profileUrn = UrnUtils.createEnterpriseProfileUrn(ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID3);
    MetricsReport report = new MetricsReport();
    report.setId(METRICS_REPORT_ID);
    report.setEnterpriseProfile(profileUrn);
    report.setEnterpriseApplicationInstance(UrnUtils.createEnterpriseApplicationInstanceUrn(ENTERPRISE_APPLICATION_INSTANCE_URN));
    when(_salesInsightsMetricsReportClient.get(UrnUtils.createSalesInsightsMetricsReportUrn(METRICS_REPORT_URN3)))
        .thenReturn(Task.value(report));

    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN3, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.UPDATE);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN3, PolicyType.LSI_METRICS_REPORT, permittedRoles, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedPairs));

    AccessDecision accessDecision =
        await(_lsiMetricsReportAclService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2, PolicyType.LSI_METRICS_REPORT,
            METRICS_REPORT_URN3, AccessAction.UPDATE));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }
}
