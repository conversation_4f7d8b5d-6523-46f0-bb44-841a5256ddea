package com.linkedin.sales.service.acl;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssNoteDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.AttributedText;
import com.linkedin.sales.espresso.Note;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.NoteKey;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.Collections;
import java.util.Set;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for NoteAclService
 * <AUTHOR>
 */
public class NoteAclServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID2 = 2001L;
  private static final long SALES_NOTE_ID = 1L;
  private static final long MEMBER_ID = 888L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(SEAT_ID2);
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private AnnotatableEntityUrn _entityUrn = new AnnotatableEntityUrn();
  private static final NoteKey NOTE_KEY = new NoteKey().setNoteId(SALES_NOTE_ID).setSeat(SEAT_URN);
  private static final Urn SALES_NOTE_URN = Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, SEAT_URN, MEMBER_URN,
     SALES_NOTE_ID);
  private static final Urn SALES_NOTE_URN2 = Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, SEAT_URN2, MEMBER_URN,
      SALES_NOTE_ID);
  private static final long SALES_LIST_ID = 1L;
  private static final Urn SALES_LIST_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);
  private static final com.linkedin.sales.admin.SeatRoleArray SALES_SEAT_ROLES_TIER_3 =
      new com.linkedin.sales.admin.SeatRoleArray(new com.linkedin.sales.admin.SeatRoleArray(
          Collections.singletonList(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER3)));
  private static final com.linkedin.sales.admin.SeatRoleArray SALES_SEAT_ROLES_TIER_1 =
      new com.linkedin.sales.admin.SeatRoleArray(new com.linkedin.sales.admin.SeatRoleArray(
          Collections.singletonList(com.linkedin.sales.admin.SeatRole.SALES_SEAT_TIER1)));
  private static final SalesSeat TIER_3_SALES_SEAT =
      new SalesSeat().setRoles(SALES_SEAT_ROLES_TIER_3).setContract(CONTRACT_URN);
  private static final SalesSeat TIER_1_SALES_SEAT =
      new SalesSeat().setRoles(SALES_SEAT_ROLES_TIER_1).setContract(CONTRACT_URN);

  private LssSharingDB _lssSharingDB;
  private LssNoteDB _lssNoteDB;
  private NoteAclService _noteAclService;
  private SalesSeatClient _salesSeatClient;

  @BeforeMethod
  public void setup() {
    _lssSharingDB = Mockito.mock(LssSharingDB.class);
    _lssNoteDB = Mockito.mock(LssNoteDB.class);
    _salesSeatClient = Mockito.mock(SalesSeatClient.class);
    _noteAclService = new NoteAclService(_lssSharingDB, _salesSeatClient);
    _entityUrn.setMemberUrn(MEMBER_URN);
    NOTE_KEY.setEntity(_entityUrn);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnership() {
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithOwnershipAndSharingPermission() {
    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(SEAT_URN, ShareRole.OWNER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithSharingPermission() {
    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(SEAT_URN2, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  @Test
  public void testCheckAccessDecisionDeny() {
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(Collections.emptyList(), 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN2, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN2, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionRecoverFromException() {
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenThrow(
        new RuntimeException());
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID2), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN2, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.DENIED);
  }

  @Test
  public void testCheckAccessDecisionAllowWithSharingPermissionAcrossContract() {
    java.util.List<Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new Pair<>(CONTRACT_URN, ShareRole.READER));
    PaginatedList<Pair<Urn, ShareRole>> paginatedPairs = PaginatedList.createForPage(pairs, 0, 10, 0);
    Set<ShareRole> permittedRoles = ServiceConstants.SHARING_ACCESS_ACTION_TO_ROLES_MAP.get(AccessAction.READ);
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(paginatedPairs));
    when(_lssNoteDB.getNote(NOTE_KEY)).thenReturn(Task.value(createEspressoNote()));
    when(_salesSeatClient.getSeat(eq(SEAT_ID), isNull(), any(EnterpriseApplicationUsageUrn.class), any())).thenReturn(
        Task.value(TIER_3_SALES_SEAT));

    AccessDecision accessDecision =
        await(_noteAclService.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.READ));
    assertThat(accessDecision).isEqualTo(AccessDecision.ALLOWED);
  }

  private Note createEspressoNote() {
    Note espressoNote = new Note();
    espressoNote.migrationId = 12345L;
    espressoNote.contractUrn = CONTRACT_URN.toString();
    espressoNote.createdTime = 1233512L;
    espressoNote.body = new AttributedText();
    return espressoNote;
  }
}

