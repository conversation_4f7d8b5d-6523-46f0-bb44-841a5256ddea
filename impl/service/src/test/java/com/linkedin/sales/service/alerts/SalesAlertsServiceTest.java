package com.linkedin.sales.service.alerts;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssAlertDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesalerts.Alert;
import com.linkedin.salesalerts.AlertEntity;
import com.linkedin.salesalerts.AlertKey;
import com.linkedin.salesalerts.AlertOrdering;
import com.linkedin.salesalerts.AlertType;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;


/**
 * Unit test for alerts service
 */
public class SalesAlertsServiceTest extends ServiceUnitTest {
  @Mock
  private LssAlertDB _lssAlertDB;

  private static SalesAlertsService _salesAlertsService;

  private static final long ALERT_ID = 101L;
  private static final String SERIALIZED_ENTITY_URN = "urn:li:organization:123";
  private static final AlertEntity ALERT_ENTITY;
  static {
    ALERT_ENTITY = new AlertEntity();
    try {
      ALERT_ENTITY.setOrganization(OrganizationUrn.deserialize(SERIALIZED_ENTITY_URN));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesAlertsService = new SalesAlertsService(_lssAlertDB);
  }

  @Test
  public void testDeleteAlertSucceed() {
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(_lssAlertDB).deleteAlert(any(), any());
    UpdateResponse result =
        await(_salesAlertsService.deleteAlert(new AlertKey().setEntity(ALERT_ENTITY).setAlertId(ALERT_ID)));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteAlertEntityTypeFailure() {
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(_lssAlertDB).deleteAlert(any(), any());
    AlertEntity alertEntity = new AlertEntity(); // empty entity without valid type
    assertThatExceptionOfType(IllegalArgumentException.class).isThrownBy(() -> {
      await(_salesAlertsService.deleteAlert(new AlertKey().setEntity(alertEntity).setAlertId(ALERT_ID)));
    });
  }

  @Test
  public void testFindByCriteriaSucceed() {
    com.linkedin.sales.espresso.EntityAlert entityAlert = createEspressoAlert();
    Mockito.doReturn(Task.value(ImmutableList.of(new Pair<>(ALERT_ID, entityAlert))))
        .when(_lssAlertDB).findByCriteria(any(), any(), anyInt(), anyInt());
    BasicCollectionResult<Alert> result = await(_salesAlertsService.findByCriteria(
        ALERT_ENTITY, new AlertType[0], AlertOrdering.CREATION_TIME, 0, 10));
    Assert.assertEquals(result.getTotal().intValue(), 1);
    Alert alert = result.getElements().get(0);
    Assert.assertEquals(alert.getEntity().getOrganization().toString(), SERIALIZED_ENTITY_URN);
    Assert.assertEquals(alert.getAlertId().longValue(), ALERT_ID);
    Assert.assertEquals(alert.getContent().getIdAsLong().longValue(), 1002L);
  }

  @Test
  public void testFindByCriteriaDeserializeFailure(){
    com.linkedin.sales.espresso.EntityAlert entityAlert = createEspressoAlert();
    entityAlert.contentUrn = "urn:li|ingestedContent:1002"; // corrupted urn
    Mockito.doReturn(Task.value(ImmutableList.of(new Pair<>(ALERT_ID, entityAlert))))
        .when(_lssAlertDB).findByCriteria(any(), any(), anyInt(), anyInt());
    BasicCollectionResult<Alert> result = await(_salesAlertsService.findByCriteria(
        ALERT_ENTITY, new AlertType[0], AlertOrdering.CREATION_TIME, 0, 10));
    Assert.assertEquals(result.getElements().size(), 0);
  }

  private com.linkedin.sales.espresso.EntityAlert createEspressoAlert() {
    com.linkedin.sales.espresso.EntityAlert alert = new com.linkedin.sales.espresso.EntityAlert();
    alert.alertType = "ACCOUNT_MENTIONED_IN_THE_NEWS";
    alert.contentUrn = "urn:li:ingestedContent:1002";
    alert.createdTime = System.currentTimeMillis();
    return alert;
  }
}
