package com.linkedin.sales.service.autofinder;

import com.linkedin.common.MemberUrnArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.DataMap;
import com.linkedin.data.transform.patch.PatchConstants;
import com.linkedin.lssSearch.AutoLeadFinderPlayType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.BatchCreateRequest;
import com.linkedin.restli.server.BatchPatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesautofinder.AccountPlay;
import com.linkedin.salesautofinder.AccountPlayKey;
import com.linkedin.util.Pair;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


public class AccountPlayServiceTest extends ServiceUnitTest {
  @Mock
  private LssAutoFinderDB _lssAutoFinderDB;

  private AccountPlayService _accountPlayService;
  private static final SeatUrn SEAT_URN = new SeatUrn(1L);
  private static final OrganizationUrn ORGANIZATION_URN = UrnUtils.createOrganizationUrn(10L);
  private static com.linkedin.sales.espresso.AccountPlay ESPRESSO_SUCCESS_1;
  private static com.linkedin.sales.espresso.AccountPlay ESPRESSO_SUCCESS_2;
  private static com.linkedin.sales.espresso.AccountPlay ESPRESSO_FAIL_1;
  private static com.linkedin.sales.espresso.AccountPlay ESPRESSO_FAIL_2;
  private static AccountPlay RESTLI_SUCCESS_1;
  private static AccountPlay RESTLI_SUCCESS_2;
  private static AccountPlay RESTLI_FAIL_1;
  private static AccountPlay RESTLI_FAIL_2;
  private static final AutoLeadFinderPlayType PLAY_TYPE = AutoLeadFinderPlayType.PAST_CUSTOMER_MOVED_TO_ACCOUNT;
  private static final Integer PERSONA_ID_2 = 11;
  private static final List<Long> LEAD_LIST = Stream.of(44L, 55L).collect(Collectors.toList());
  private static final Integer ID_1 = 1;
  private static final Integer ID_2 = 2;
  private static final Integer ID_3 = 3;
  private static final Integer ID_4 = 4;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _accountPlayService = new AccountPlayService(_lssAutoFinderDB);
    prepareTestData();
  }

  @Test
  public void testBatchCreate() {
    List<CreateResponse> emptyResponses = await(_accountPlayService
        .batchCreate(new BatchCreateRequest<>(Collections.emptyList())));
    assertEquals(emptyResponses.size(), 0);

    MockAccountPlay mockAccountPlay1 = new MockAccountPlay(ESPRESSO_SUCCESS_1);
    MockAccountPlay mockAccountPlay2 = new MockAccountPlay(ESPRESSO_SUCCESS_2);
    when(_lssAutoFinderDB.createAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), argThat(mockAccountPlay1)))
        .thenReturn(Task.value(ID_1));
    when(_lssAutoFinderDB.createAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), argThat(mockAccountPlay2)))
        .thenReturn(Task.value(ID_2));
    when(_lssAutoFinderDB.createAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), argThat(new MockAccountPlay(ESPRESSO_FAIL_1))))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));
    when(_lssAutoFinderDB.createAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), argThat(new MockAccountPlay(ESPRESSO_FAIL_2))))
        .thenReturn(Task.failure(new RuntimeException()));

    List<AccountPlay> inserts = Arrays.asList(RESTLI_SUCCESS_1, RESTLI_SUCCESS_2, RESTLI_FAIL_1, RESTLI_FAIL_2);
    List<CreateResponse> response = await(_accountPlayService.batchCreate(new BatchCreateRequest<>(inserts)));
    AccountPlayKey key1 = new AccountPlayKey().setSeat(SEAT_URN).setOrganization(ORGANIZATION_URN).setId(ID_1);
    AccountPlayKey key2 = new AccountPlayKey().setSeat(SEAT_URN).setOrganization(ORGANIZATION_URN).setId(ID_2);
    assertEquals(response.size(), 4);
    assertEquals(response.get(0).getId(), key1.getId());
    assertEquals(response.get(1).getId(), key2.getId());
    assertNull(response.get(2).getId());
    assertEquals(response.get(2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertNull(response.get(3).getId());
    assertEquals(response.get(3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testFindByAccount() {
    when(_lssAutoFinderDB.getAccountPlays(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(0), eq(10)))
        .thenReturn(Task.value(Arrays.asList(new Pair<>(ID_1, ESPRESSO_SUCCESS_1),
            new Pair<>(ID_2, ESPRESSO_SUCCESS_2))));
    List<AccountPlay> accountPlays = await(_accountPlayService
        .findByAccount(SEAT_URN, ORGANIZATION_URN, 0, 10)).getElements();
    assertEquals(accountPlays.size(), 2);
    AccountPlay expected1 = new AccountPlay();
    expected1.setId(ID_1);
    expected1.setOrganization(ORGANIZATION_URN);
    expected1.setSeat(SEAT_URN);
    expected1.setPlayType(PLAY_TYPE);
    expected1.setPastLeads(new MemberUrnArray());
    expected1.setCurrentLeads(new MemberUrnArray());

    AccountPlay expected2 = new AccountPlay();
    expected2.setId(ID_2);
    expected2.setOrganization(ORGANIZATION_URN);
    expected2.setSeat(SEAT_URN);
    expected2.setPlayType(PLAY_TYPE);
    expected2.setPersonaLocalId(PERSONA_ID_2);
    expected2.setPastLeads(new MemberUrnArray(LEAD_LIST.stream()
        .map(MemberUrn::new).collect(Collectors.toList())));
    expected2.setCurrentLeads(new MemberUrnArray());

    assertEquals(accountPlays.get(0), expected1);
    assertEquals(accountPlays.get(1), expected2);
  }

  @Test
  public void testBatchPartialUpdate() {
    Map<AccountPlayKey, UpdateResponse> result = await(_accountPlayService
        .batchPartialUpdate(new BatchPatchRequest<>(Collections.emptyMap())));
    assertEquals(result.size(), 0);

    Map<ComplexResourceKey<AccountPlayKey, EmptyRecord >, PatchRequest<AccountPlay>> patchMap
        = new HashMap<>();

    AccountPlayKey key1 = new AccountPlayKey().setSeat(SEAT_URN).setOrganization(ORGANIZATION_URN).setId(ID_1);
    AccountPlayKey key3 = new AccountPlayKey().setSeat(SEAT_URN).setOrganization(ORGANIZATION_URN).setId(ID_3);
    AccountPlayKey key4 = new AccountPlayKey().setSeat(SEAT_URN).setOrganization(ORGANIZATION_URN).setId(ID_4);

    PatchRequest<AccountPlay> patchRequest1 = createPatchRequest(RESTLI_SUCCESS_1.setId(ID_1));
    PatchRequest<AccountPlay> invalidPatchRequest = createPatchRequest(RESTLI_FAIL_1.setId(ID_3));
    PatchRequest<AccountPlay> invalidPatchRequest2 = createPatchRequest(RESTLI_FAIL_2.setId(ID_4));

    patchMap.put(new ComplexResourceKey<>(key1, new EmptyRecord()), patchRequest1);
    patchMap.put(new ComplexResourceKey<>(key3, new EmptyRecord()), invalidPatchRequest);
    patchMap.put(new ComplexResourceKey<>(key4, new EmptyRecord()), invalidPatchRequest2);

    ESPRESSO_SUCCESS_1.setCurrentLeads(null);
    ESPRESSO_SUCCESS_1.setPastLeads(null);
    ESPRESSO_FAIL_1.setPastLeads(null);
    ESPRESSO_FAIL_2.setCurrentLeads(null);
    ESPRESSO_FAIL_2.setPastLeads(null);

    when(_lssAutoFinderDB.partialUpdateAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(ID_1),
        argThat(new MockAccountPlay(ESPRESSO_SUCCESS_1))))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssAutoFinderDB.partialUpdateAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(ID_3),
        argThat(new MockAccountPlay(ESPRESSO_FAIL_1))))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));
    when(_lssAutoFinderDB.partialUpdateAccountPlay(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(ID_4),
        argThat(new MockAccountPlay(ESPRESSO_FAIL_2))))
        .thenReturn(Task.failure(new RuntimeException()));
    result = await(_accountPlayService.batchPartialUpdate(new BatchPatchRequest<>(patchMap)));
    assertEquals(result.size(), 3);
    assertEquals(result.get(key1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(key3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(key4).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  private PatchRequest<AccountPlay> createPatchRequest(AccountPlay accountPlay) {
    DataMap patchData = new DataMap();
    patchData.put(PatchConstants.SET_COMMAND, accountPlay.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  private void prepareTestData() {
    RESTLI_SUCCESS_1 = new AccountPlay();
    RESTLI_SUCCESS_2 = new AccountPlay();
    RESTLI_FAIL_1 = new AccountPlay();
    RESTLI_FAIL_2 = new AccountPlay();
    ESPRESSO_SUCCESS_1 = new com.linkedin.sales.espresso.AccountPlay();
    ESPRESSO_SUCCESS_2 = new com.linkedin.sales.espresso.AccountPlay();
    ESPRESSO_FAIL_1 = new com.linkedin.sales.espresso.AccountPlay();
    ESPRESSO_FAIL_2 = new com.linkedin.sales.espresso.AccountPlay();

    RESTLI_SUCCESS_1.setPlayType(PLAY_TYPE);
    RESTLI_SUCCESS_1.setSeat(SEAT_URN);
    RESTLI_SUCCESS_1.setOrganization(ORGANIZATION_URN);
    RESTLI_SUCCESS_2.setPlayType(PLAY_TYPE);
    RESTLI_SUCCESS_2.setSeat(SEAT_URN);
    RESTLI_SUCCESS_2.setOrganization(ORGANIZATION_URN);
    RESTLI_SUCCESS_2.setPersonaLocalId(PERSONA_ID_2);
    MemberUrnArray leadsArray = new MemberUrnArray(LEAD_LIST.stream()
        .map(MemberUrn::new).collect(Collectors.toList()));
    RESTLI_SUCCESS_2.setPastLeads(leadsArray);
    RESTLI_FAIL_1.setPlayType(PLAY_TYPE);
    RESTLI_FAIL_1.setSeat(SEAT_URN);
    RESTLI_FAIL_1.setOrganization(ORGANIZATION_URN);
    RESTLI_FAIL_1.setCurrentLeads(leadsArray);
    RESTLI_FAIL_2.setPlayType(AutoLeadFinderPlayType.$UNKNOWN);
    RESTLI_FAIL_2.setSeat(SEAT_URN);
    RESTLI_FAIL_2.setOrganization(ORGANIZATION_URN);

    ESPRESSO_SUCCESS_1.setPlayType(PLAY_TYPE.name());
    ESPRESSO_SUCCESS_1.setCurrentLeads(Collections.emptyList());
    ESPRESSO_SUCCESS_1.setPastLeads(Collections.emptyList());
    ESPRESSO_SUCCESS_2.setPlayType(PLAY_TYPE.name());
    ESPRESSO_SUCCESS_2.setPersonaLocalId(PERSONA_ID_2);
    ESPRESSO_SUCCESS_2.setPastLeads(LEAD_LIST);
    ESPRESSO_SUCCESS_2.setCurrentLeads(Collections.emptyList());
    ESPRESSO_FAIL_1.setPlayType(PLAY_TYPE.name());
    ESPRESSO_FAIL_1.setCurrentLeads(LEAD_LIST);
    ESPRESSO_FAIL_1.setPastLeads(Collections.emptyList());
    ESPRESSO_FAIL_2.setPlayType(AutoLeadFinderPlayType.$UNKNOWN.name());
    ESPRESSO_FAIL_2.setCurrentLeads(Collections.emptyList());
    ESPRESSO_FAIL_2.setPastLeads(Collections.emptyList());
  }

  class MockAccountPlay implements ArgumentMatcher<com.linkedin.sales.espresso.AccountPlay> {
    public MockAccountPlay(com.linkedin.sales.espresso.AccountPlay accountPlay) {
      playType = accountPlay.getPlayType().toString();
      pastLeads = accountPlay.getPastLeads();
      currentLeads = accountPlay.getCurrentLeads();
      personaLocalId = accountPlay.getPersonaLocalId();
    }
    @Override
    public boolean matches(com.linkedin.sales.espresso.AccountPlay accountPlay) {
      return Objects.equals(accountPlay.getPlayType().toString(), playType)
          && Objects.equals(accountPlay.getPastLeads(), pastLeads)
          && Objects.equals(accountPlay.getCurrentLeads(), currentLeads)
          && Objects.equals(accountPlay.getPersonaLocalId(), personaLocalId);
    }

    String playType;
    List<Long> pastLeads;
    List<Long> currentLeads;
    Integer personaLocalId;
  }
}
