package com.linkedin.sales.service.autofinder;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.MemberUrnArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssAutoFinderDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesautofinder.AccountPlaysMetadata;
import com.linkedin.salesautofinder.AccountPlaysMetadataKey;
import java.util.Collections;
import com.linkedin.util.Pair;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.openjdk.tools.javac.util.List;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


public class AccountPlaysMetadataServiceTest extends ServiceUnitTest {
  @Mock
  private LssAutoFinderDB _lssAutoFinderDB;
  @Mock
  private LixService _lixService;
  private AccountPlaysMetadataService _accountPlaysMetadataService;
  private static final SeatUrn SEAT_URN = new SeatUrn(1L);
  private static final OrganizationUrn ORGANIZATION_URN = UrnUtils.createOrganizationUrn(10L);
  private static final MemberUrn MEMBER_URN_1 = new MemberUrn(100L);
  private static final MemberUrn MEMBER_URN_2 = new MemberUrn(200L);
  private static final MemberUrn MEMBER_URN_3 = new MemberUrn(300L);

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _accountPlaysMetadataService = new AccountPlaysMetadataService(_lssAutoFinderDB, _lixService);
  }

  @BeforeMethod(alwaysRun = true)
  public void testSetup() {
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.FALSE));
  }

  @Test
  public void testCreate() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setSeat(SEAT_URN);
    accountPlaysMetadata.setOrganization(ORGANIZATION_URN);
    accountPlaysMetadata.setLastRunAt(100L);
    accountPlaysMetadata.setDismissedLeads(new MemberUrnArray());

    when(_lssAutoFinderDB.createAccountPlaysMetadata(eq(SEAT_URN), eq(ORGANIZATION_URN), any())).thenReturn(Task.value(
        HttpStatus.S_201_CREATED));

    CreateResponse result = await(_accountPlaysMetadataService.create(accountPlaysMetadata));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateFail() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setSeat(SEAT_URN);
    accountPlaysMetadata.setOrganization(ORGANIZATION_URN);
    accountPlaysMetadata.setLastRunAt(100L);
    accountPlaysMetadata.setDismissedLeads(new MemberUrnArray());
    when(_lssAutoFinderDB.createAccountPlaysMetadata(eq(SEAT_URN), eq(ORGANIZATION_URN), any()))
        .thenReturn(Task.failure(new RuntimeException()));
    runAndWaitException(_accountPlaysMetadataService.create(accountPlaysMetadata), RuntimeException.class);
  }

  @Test (description = "Test create AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled")
  public void testCreateAccountPlaysMetadataV2() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setSeat(SEAT_URN);
    accountPlaysMetadata.setOrganization(ORGANIZATION_URN);
    accountPlaysMetadata.setLastRunAt(100L);
    accountPlaysMetadata.setDismissedLeads(new MemberUrnArray(List.of(MEMBER_URN_1)));

    when(_lssAutoFinderDB.createAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(MEMBER_URN_1))).thenReturn(Task.value(
        HttpStatus.S_201_CREATED));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    CreateResponse result = await(_accountPlaysMetadataService.create(accountPlaysMetadata));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test (description = "Test create AccountPlaysMetadataV2 with multiple member urns and should fail with Bad Request")
  public void testCreateAccountPlaysMetadataV2WithMultipleMemberUrns() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setSeat(SEAT_URN);
    accountPlaysMetadata.setOrganization(ORGANIZATION_URN);
    accountPlaysMetadata.setLastRunAt(100L);
    accountPlaysMetadata.setDismissedLeads(new MemberUrnArray(List.of(MEMBER_URN_1, MEMBER_URN_2)));

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    runAndWaitException(_accountPlaysMetadataService.create(accountPlaysMetadata), RestLiServiceException.class);
  }

  @Test (description = "Test create AccountPlaysMetadataV2 should fail due to espresso failure")
  public void testCreateAccountPlaysMetadataV2Fail() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setSeat(SEAT_URN);
    accountPlaysMetadata.setOrganization(ORGANIZATION_URN);
    accountPlaysMetadata.setLastRunAt(100L);
    accountPlaysMetadata.setDismissedLeads(new MemberUrnArray(List.of(MEMBER_URN_1)));

    when(_lssAutoFinderDB.createAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), any()))
        .thenReturn(Task.failure(new RuntimeException()));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    runAndWaitException(_accountPlaysMetadataService.create(accountPlaysMetadata), RuntimeException.class);
  }

  @Test
  public void testGet() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunTimestamp(100L);
    accountPlaysMetadata.setDismissedLeads(Collections.singletonList(1L));
    accountPlaysMetadata.setCreatedTime(0L);
    accountPlaysMetadata.setLastModifiedTime(0L);

    when(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN)).thenReturn(Task.value(accountPlaysMetadata));
    MemberUrnArray memberUrnArray = new MemberUrnArray();
    memberUrnArray.add(new MemberUrn(1L));

    AccountPlaysMetadata expected = new AccountPlaysMetadata();
    expected.setSeat(SEAT_URN);
    expected.setOrganization(ORGANIZATION_URN);
    expected.setLastRunAt(100L);
    expected.setDismissedLeads(memberUrnArray);

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertEquals(result, expected);
  }

  @Test
  public void testGetWithEntityNotFoundException() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    when(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN)).thenReturn(Task.failure(new EntityNotFoundException(null, "")));
    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test
  public void testGetFail() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    when(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN)).thenReturn(Task.failure(new RuntimeException()));
    runAndWaitException(_accountPlaysMetadataService.get(key), RuntimeException.class);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled," +
          " should return AccountPlaysMetadata with lastRunAt as the largest createdTime and two dismissed leads")
  public void testGetAccountPlaysMetadataV2s() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadataV2 accountPlaysMetadata1 = new com.linkedin.sales.espresso.AccountPlaysMetadataV2();
    accountPlaysMetadata1.setCreatedTime(0L);
    com.linkedin.sales.espresso.AccountPlaysMetadataV2 accountPlaysMetadata2 = new com.linkedin.sales.espresso.AccountPlaysMetadataV2();
    accountPlaysMetadata2.setCreatedTime(10L);
    OrganizationUrn organizationUrn2 = UrnUtils.createOrganizationUrn(20L);

    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(
        ORGANIZATION_URN,
        List.of(Pair.of(MEMBER_URN_1, accountPlaysMetadata1), Pair.of(MEMBER_URN_2, accountPlaysMetadata2)),
        organizationUrn2,
        List.of(Pair.of(MEMBER_URN_1, accountPlaysMetadata1), Pair.of(MEMBER_URN_2, accountPlaysMetadata2)))));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN)).thenReturn(Task.value(
            List.of(Pair.of(MEMBER_URN_1, accountPlaysMetadata1), Pair.of(MEMBER_URN_2, accountPlaysMetadata2))));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, organizationUrn2)).thenReturn(Task.value(
            List.of(Pair.of(MEMBER_URN_1, accountPlaysMetadata1), Pair.of(MEMBER_URN_2, accountPlaysMetadata2))));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    AccountPlaysMetadata expected = new AccountPlaysMetadata();
    expected.setSeat(SEAT_URN);
    expected.setOrganization(ORGANIZATION_URN);
    expected.setLastRunAt(10L); // Use the largest createdTime from the list of AccountPlaysMetadataV2s
    MemberUrnArray memberUrnArray = new MemberUrnArray(List.of(MEMBER_URN_1, MEMBER_URN_2));
    expected.setDismissedLeads(memberUrnArray);

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertEquals(result, expected);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled," +
          " should return null as no dismissed leads found for given organization")
  public void testGetAccountPlaysMetadataV2sWhenNoDismissedLeadForCompany() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadataV2 accountPlaysMetadata1 = new com.linkedin.sales.espresso.AccountPlaysMetadataV2();
    accountPlaysMetadata1.setCreatedTime(0L);
    OrganizationUrn organizationUrn2 = UrnUtils.createOrganizationUrn(20L);

    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(
            organizationUrn2,
            List.of(Pair.of(MEMBER_URN_1, accountPlaysMetadata1)))));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN)).thenReturn(Task.value(Collections.emptyList()));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
            .thenReturn(Task.value(Boolean.TRUE));

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should get from AccountPlaysMetadata table and put to AccountPlaysMetadataV2 table")
  public void testGetAccountPlaysMetadataV2sWithDataMigration() {
    AccountPlaysMetadataKey key1 = new AccountPlaysMetadataKey();
    key1.setSeat(SEAT_URN);
    key1.setOrganization(ORGANIZATION_URN);
    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata1 = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata1.setDismissedLeads(
        List.of(MEMBER_URN_1.getMemberIdEntity(), MEMBER_URN_2.getMemberIdEntity(), MEMBER_URN_3.getMemberIdEntity()));
    accountPlaysMetadata1.setLastRunTimestamp(100L);
    accountPlaysMetadata1.setCreatedTime(10L);
    accountPlaysMetadata1.setLastModifiedTime(100L);

    OrganizationUrn organizationUrn2 = UrnUtils.createOrganizationUrn(20L);
    AccountPlaysMetadataKey key2 = new AccountPlaysMetadataKey();
    key2.setSeat(SEAT_URN);
    key2.setOrganization(organizationUrn2);
    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata2 = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata2.setDismissedLeads(
        List.of(MEMBER_URN_1.getMemberIdEntity(), MEMBER_URN_2.getMemberIdEntity()));
    accountPlaysMetadata2.setLastRunTimestamp(200L);
    accountPlaysMetadata2.setCreatedTime(20L);
    accountPlaysMetadata2.setLastModifiedTime(200L);

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(any(SeatUrn.class))).thenReturn(Task.value(Collections.emptyMap()));
    when(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(
            ORGANIZATION_URN, accountPlaysMetadata1,
            organizationUrn2, accountPlaysMetadata2
            )));
    when(_lssAutoFinderDB.createAccountPlaysMetadataV2s(any(SeatUrn.class), anyMap()))
        .thenReturn(Task.value(ImmutableMap.of(
                ORGANIZATION_URN, ImmutableMap.of(MEMBER_URN_1, HttpStatus.S_201_CREATED, MEMBER_URN_2, HttpStatus.S_201_CREATED, MEMBER_URN_3, HttpStatus.S_201_CREATED),
                organizationUrn2, ImmutableMap.of(MEMBER_URN_1, HttpStatus.S_201_CREATED, MEMBER_URN_2, HttpStatus.S_201_CREATED)
        )));

    // Should only return AccountPlaysMetadata for organizationUrn2
    AccountPlaysMetadata expected = new AccountPlaysMetadata();
    expected.setSeat(SEAT_URN);
    expected.setOrganization(organizationUrn2);
    expected.setLastRunAt(accountPlaysMetadata2.getLastRunTimestamp());
    MemberUrnArray memberUrnArray = new MemberUrnArray(List.of(MEMBER_URN_1, MEMBER_URN_2));
    expected.setDismissedLeads(memberUrnArray);

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key2));
    Assert.assertEquals(result, expected);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should get from AccountPlaysMetadata table and return null as dismissedLeads is empty")
  public void testGetAccountPlaysMetadataV2sWithDataMigrationWhenDismissedLeadsIsEmpty() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata.setDismissedLeads(Collections.emptyList());
    accountPlaysMetadata.setLastRunTimestamp(100L);
    accountPlaysMetadata.setCreatedTime(10L);
    accountPlaysMetadata.setLastModifiedTime(100L);

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.value(Collections.emptyMap()));
    when(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(
        UrnUtils.createOrganizationUrn(0L), accountPlaysMetadata)));

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should return null as entity not found from espresso")
  public void testGetAccountPlaysMetadataV2WithEntityNotFoundException() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.failure(new EntityNotFoundException(null, "")));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should throw exception due to espresso failure")
  public void testGetAccountPlaysMetadataV2Fail() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.failure(new RuntimeException()));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    runAndWaitException(_accountPlaysMetadataService.get(key), RuntimeException.class);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should get from AccountPlaysMetadata table and put to AccountPlaysMetadataV2 table but fail due to espresso failure")
  public void testGetAccountPlaysMetadataV2sWithDataMigrationWithFailure() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata.setDismissedLeads(
        List.of(MEMBER_URN_1.getMemberIdEntity(), MEMBER_URN_2.getMemberIdEntity(), MEMBER_URN_3.getMemberIdEntity()));
    accountPlaysMetadata.setLastRunTimestamp(100L);
    accountPlaysMetadata.setCreatedTime(10L);
    accountPlaysMetadata.setLastModifiedTime(100L);

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN)).thenReturn(Task.value(Collections.emptyMap()));
    when(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(ORGANIZATION_URN, accountPlaysMetadata)));
    when(_lssAutoFinderDB.createAccountPlaysMetadataV2s(any(SeatUrn.class), anyMap()))
        .thenReturn(Task.value(ImmutableMap.of(ORGANIZATION_URN, ImmutableMap.of(
            MEMBER_URN_1, HttpStatus.S_201_CREATED,
            MEMBER_URN_3, HttpStatus.S_400_BAD_REQUEST
        ))));

    runAndWaitException(_accountPlaysMetadataService.get(key), RestLiServiceException.class);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should get from AccountPlaysMetadata table and put to AccountPlaysMetadataV2 table but return null as no metadata found for organization")
  public void testGetAccountPlaysMetadataV2sWithDataMigrationNoMetadataForOrganizationFound() {
    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata.setDismissedLeads(
            List.of(MEMBER_URN_1.getMemberIdEntity(), MEMBER_URN_2.getMemberIdEntity(), MEMBER_URN_3.getMemberIdEntity()));
    accountPlaysMetadata.setLastRunTimestamp(100L);
    accountPlaysMetadata.setCreatedTime(10L);
    accountPlaysMetadata.setLastModifiedTime(100L);

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
            .thenReturn(Task.value(Boolean.TRUE));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(any(SeatUrn.class))).thenReturn(Task.value(Collections.emptyMap()));
    when(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(ORGANIZATION_URN, accountPlaysMetadata)));
    when(_lssAutoFinderDB.createAccountPlaysMetadataV2s(any(SeatUrn.class), anyMap()))
            .thenReturn(Task.value(ImmutableMap.of(ORGANIZATION_URN, ImmutableMap.of(
                    MEMBER_URN_1, HttpStatus.S_201_CREATED,
                    MEMBER_URN_2, HttpStatus.S_201_CREATED,
                    MEMBER_URN_3, HttpStatus.S_201_CREATED
            ))));

    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(UrnUtils.createOrganizationUrn(20L));
    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test (description = "Test get AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should get from AccountPlaysMetadata table but not put to AccountPlaysMetadataV2 table and return null as no dismissedLeads found")
  public void testGetAccountPlaysMetadataV2sWithDataMigrationNoDismissLeadForOrganizationFound() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);

    com.linkedin.sales.espresso.AccountPlaysMetadata accountPlaysMetadata = new com.linkedin.sales.espresso.AccountPlaysMetadata();
    accountPlaysMetadata.setDismissedLeads(Collections.emptyList());
    accountPlaysMetadata.setLastRunTimestamp(100L);
    accountPlaysMetadata.setCreatedTime(10L);
    accountPlaysMetadata.setLastModifiedTime(100L);

    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));
    when(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(any(SeatUrn.class))).thenReturn(Task.value(Collections.emptyMap()));
    when(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN)).thenReturn(Task.value(ImmutableMap.of(ORGANIZATION_URN, accountPlaysMetadata)));

    AccountPlaysMetadata result = await(_accountPlaysMetadataService.get(key));
    Assert.assertNull(result);
  }

  @Test
  public void testPartialUpdate() {
    when(_lssAutoFinderDB.partialUpdateAccountPlaysMetadata(eq(SEAT_URN), eq(ORGANIZATION_URN), any())).thenReturn(Task.value(
        HttpStatus.S_200_OK));
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());
    UpdateResponse result =
        await(_accountPlaysMetadataService.partialUpdate(key, PatchGenerator.diffEmpty(accountPlaysMetadata), false, null));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testPartialUpdateFail() {
    when(_lssAutoFinderDB.partialUpdateAccountPlaysMetadata(eq(SEAT_URN), eq(ORGANIZATION_URN), any())).thenReturn(Task.failure(new RuntimeException()));
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());
    runAndWaitException(_accountPlaysMetadataService.partialUpdate(key, PatchGenerator.diffEmpty(accountPlaysMetadata), false, null), RuntimeException.class);
  }

  @Test (description = "Test partialUpdate AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should dismiss a lead")
  public void testPartialUpdate_shouldCallAccountPlaysMetadataV2Create() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());

    when(_lssAutoFinderDB.createAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(MEMBER_URN_1))).thenReturn(Task.value(
        HttpStatus.S_200_OK));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    UpdateResponse result = await(_accountPlaysMetadataService.partialUpdate(
        key, PatchGenerator.diffEmpty(accountPlaysMetadata), false, MEMBER_URN_1));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test (description = "Test partialUpdate AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should dismiss a lead but fail due to espresso failure")
  public void testPartialUpdate_shouldCallAccountPlaysMetadataV2CreateAndFail() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());

    when(_lssAutoFinderDB.createAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(MEMBER_URN_1)))
        .thenReturn(Task.failure(new RuntimeException()));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    runAndWaitException(_accountPlaysMetadataService.partialUpdate(key, PatchGenerator.diffEmpty(accountPlaysMetadata), false, MEMBER_URN_1),
        RuntimeException.class);
  }

  @Test (description = "Test partialUpdate AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should undo dimiss a lead")
  public void testPartialUpdate_shouldCallAccountPlaysMetadataV2Delete() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());

    when(_lssAutoFinderDB.deleteAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(MEMBER_URN_1)))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    UpdateResponse result = await(_accountPlaysMetadataService.partialUpdate(
        key, PatchGenerator.diffEmpty(accountPlaysMetadata), true, MEMBER_URN_1));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test (description = "Test partialUpdate AccountPlaysMetadataV2 when switch to AccountPlaysMetadataV2 table lix is enabled, " +
          "should undo dismiss a lead but fail due to espresso failure")
  public void testPartialUpdate_shouldCallAccountPlaysMetadataV2DeleteAndFail() {
    AccountPlaysMetadataKey key = new AccountPlaysMetadataKey();
    key.setSeat(SEAT_URN);
    key.setOrganization(ORGANIZATION_URN);
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunAt(System.currentTimeMillis());

    when(_lssAutoFinderDB.deleteAccountPlaysMetadataV2(eq(SEAT_URN), eq(ORGANIZATION_URN), eq(MEMBER_URN_1)))
        .thenReturn(Task.failure(new RuntimeException()));
    when(_lixService.isEEPLixEnabledForSeat(any(SeatUrn.class), eq(LixUtils.LSS_SEARCH_SWITCH_TO_ACCOUNT_PLAYS_METADATA_V2_TABLE)))
        .thenReturn(Task.value(Boolean.TRUE));

    runAndWaitException(_accountPlaysMetadataService.partialUpdate(key, PatchGenerator.diffEmpty(accountPlaysMetadata), true, MEMBER_URN_1),
        RuntimeException.class);
  }
}
