package com.linkedin.sales.service.autoprospecting;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.common.OrganizationUrnArray;
import com.linkedin.common.SalesListUrnArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.sales.service.buyerengagement.SalesSellerIdentityService;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.util.Pair;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSetting;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSettingArray;
import proto.com.linkedin.salesautoprospecting.Campaign;
import proto.com.linkedin.salesautoprospecting.CampaignKey;
import proto.com.linkedin.salesautoprospecting.CreateCampaignResponse;
import proto.com.linkedin.salesautoprospecting.DeleteCampaignResponse;
import proto.com.linkedin.salesautoprospecting.FindCampaignBySeatAndContractResponse;
import proto.com.linkedin.salesautoprospecting.GetCampaignResponse;
import proto.com.linkedin.salesautoprospecting.PartialUpdateCampaignResponse;


public class CampaignServiceTest extends BaseEngineParTest {
  // Constants
  private static final SeatUrn SEAT_URN = new SeatUrn(123L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(456L);
  private static final long CAMPAIGN_ID = 789L;
  private static final String PRODUCT_ID = "product123";
  private static final long CURRENT_TIME = System.currentTimeMillis();
  private static final long LAST_MODIFIED_TIME = CURRENT_TIME - 10000;
  private static final long ONBOARDING_COMPLETED_TIME = CURRENT_TIME - 20000;

  // Mocks
  @Mock
  private LssAutoProspectingDB _lssAutoProspectingDB;

  @Mock
  private SalesSellerIdentityService _salesSellerIdentityService;

  private CampaignService _campaignService;

  // Proto objects for reuse
  private proto.com.linkedin.common.SeatUrn _protoSeatUrn;
  private proto.com.linkedin.common.ContractUrn _protoContractUrn;
  private Campaign _defaultCampaign;
  private CampaignKey _defaultCampaignKey;

  @BeforeMethod
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _campaignService = new CampaignService(_lssAutoProspectingDB, _salesSellerIdentityService);

    // Initialize reusable objects
    _protoSeatUrn = proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build();
    _protoContractUrn =
        proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build();
    _defaultCampaign = createTestCampaign(_protoSeatUrn, _protoContractUrn, PRODUCT_ID);
    _defaultCampaignKey = createTestCampaignKey(_protoSeatUrn, CAMPAIGN_ID);
  }

  //
  // Create Campaign Tests
  //

  @Test
  public void testCreateCampaignSuccess() {
    // Setup
    when(_lssAutoProspectingDB.createCampaign(any(SeatUrn.class),
        any(com.linkedin.sales.espresso.Campaign.class))).thenReturn(Task.value(1L));

    // Execute
    CreateCampaignResponse response = runAndWait(_campaignService.createCampaign(_defaultCampaign));

    // Verify
    assertEquals(_defaultCampaign.getSeatUrn(), response.getKey().getSeatUrn());
    assertEquals(1L, response.getKey().getCampaignId());
    assertEquals(_defaultCampaign, response.getValue());
  }

  @Test
  public void testCreateCampaignWithException() {
    // Setup
    when(_lssAutoProspectingDB.createCampaign(any(SeatUrn.class),
        any(com.linkedin.sales.espresso.Campaign.class))).thenReturn(
        Task.failure(new RuntimeException("Unexpected database error")));

    // Execute & Verify
    RuntimeException exception =
        runAndWaitException(_campaignService.createCampaign(_defaultCampaign), RuntimeException.class);
    assertEquals("Unexpected database error", exception.getMessage());
  }

  //
  // Update Campaign Tests
  //

  @DataProvider(name = "updateCampaignScenarios")
  public Object[][] updateCampaignScenarios() {
    return new Object[][]{
        // scenario name, http status, exception expected
        {"Success", HttpStatus.S_200_OK, false},
        {"Precondition Failed", HttpStatus.S_412_PRECONDITION_FAILED, true}};
  }

  @Test(dataProvider = "updateCampaignScenarios")
  public void testPartialUpdateCampaign(String scenario, HttpStatus status, boolean expectsException) {
    // Setup
    if (expectsException) {
      when(_lssAutoProspectingDB.partialUpdateCampaign(any(SeatUrn.class), anyLong(),
          any(com.linkedin.sales.espresso.Campaign.class))).thenReturn(
          Task.failure(new RestLiServiceException(status)));
    } else {
      when(_lssAutoProspectingDB.partialUpdateCampaign(any(SeatUrn.class), anyLong(),
          any(com.linkedin.sales.espresso.Campaign.class))).thenReturn(Task.value(createTestEspressoCampaign()));
    }

    // Execute
    if (expectsException) {
      RestLiServiceException exception =
          runAndWaitException(_campaignService.partialUpdateCampaign(_defaultCampaignKey, _defaultCampaign),
              RestLiServiceException.class);
      assertEquals(status, exception.getStatus());
    } else {
      PartialUpdateCampaignResponse response =
          runAndWait(_campaignService.partialUpdateCampaign(_defaultCampaignKey, _defaultCampaign));

      assertNotNull(response.getValue());
    }
  }

  //
  // Get Campaign Tests
  //

  @Test
  public void testGetCampaignSuccess() {
    // Setup
    com.linkedin.sales.espresso.Campaign espressoCampaign = createTestEspressoCampaign();
    when(_lssAutoProspectingDB.getCampaign(SEAT_URN, CAMPAIGN_ID)).thenReturn(Task.value(espressoCampaign));

    // Execute
    GetCampaignResponse response = runAndWait(_campaignService.getCampaign(_defaultCampaignKey));

    // Verify
    assertCampaignFields(response.getValue());
    assertCampaignListUrns(response.getValue());
    assertCampaignAccountUrns(response.getValue());
  }

  @Test
  public void testGetCampaignWithException() {
    // Setup
    when(_lssAutoProspectingDB.getCampaign(SEAT_URN, CAMPAIGN_ID)).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    // Execute & Verify
    RestLiServiceException exception =
        runAndWaitException(_campaignService.getCampaign(_defaultCampaignKey), RestLiServiceException.class);
    assertEquals(HttpStatus.S_500_INTERNAL_SERVER_ERROR, exception.getStatus());
  }

  //
  // Delete Campaign Tests
  //

  @Test
  public void testDeleteCampaignSuccess() {
    // Setup
    when(_lssAutoProspectingDB.deleteCampaign(SEAT_URN, CAMPAIGN_ID))
        .thenReturn(Task.value(HttpStatus.S_204_NO_CONTENT));

    // Execute
    DeleteCampaignResponse response = runAndWait(_campaignService.deleteCampaign(_defaultCampaignKey));

    // Verify
    assertNotNull(response);
    verify(_lssAutoProspectingDB).deleteCampaign(SEAT_URN, CAMPAIGN_ID);
  }

  @Test
  public void testDeleteCampaignFailure() {
    // Setup
    when(_lssAutoProspectingDB.deleteCampaign(SEAT_URN, CAMPAIGN_ID))
        .thenReturn(Task.failure(new RuntimeException("Database error")));

    // Execute & Verify
    assertThrows(RuntimeException.class, () -> runAndWait(_campaignService.deleteCampaign(_defaultCampaignKey)));
  }

  @Test
  public void testDeleteCampaignWithNullKey() {
    // Execute & Verify
    assertThrows(NullPointerException.class, () -> runAndWait(_campaignService.deleteCampaign(null)));
  }

  @Test
  public void testDeleteCampaignWithNullSeatUrn() {
    // Setup
    CampaignKey invalidKey = CampaignKey.newBuilder()
        .setCampaignId(CAMPAIGN_ID)
        .build(); // Missing seatUrn

    // Execute & Verify
    assertThrows(NullPointerException.class, () -> runAndWait(_campaignService.deleteCampaign(invalidKey)));
  }

  @Test
  public void testDeleteCampaignWithInvalidCampaignId() {
    // Setup
    CampaignKey invalidKey = CampaignKey.newBuilder()
        .setSeatUrn(_protoSeatUrn)
        .setCampaignId(0L) // Invalid campaign ID
        .build();

    // Execute & Verify
    assertThrows(IllegalArgumentException.class, () -> runAndWait(_campaignService.deleteCampaign(invalidKey)));
  }

  //
  // Find Campaigns Tests
  //

  @Test
  public void testFindCampaignsBySeatAndContractSuccess() {
    // Setup
    List<Pair<Long, com.linkedin.sales.espresso.Campaign>> campaignPairs = createMultipleEspressoCampaignPairs();
    when(_lssAutoProspectingDB.findCampaignsBySeat(SEAT_URN)).thenReturn(Task.value(campaignPairs));

    // Execute
    FindCampaignBySeatAndContractResponse response =
        runAndWait(_campaignService.findCampaignsBySeatAndContract(_protoSeatUrn, _protoContractUrn));

    // Verify
    assertNotNull(response);
    assertEquals(2, response.getValuesCount());

    // Verify first campaign
    Campaign campaign1 = response.getValues(0);
    assertEquals(_protoSeatUrn, campaign1.getSeatUrn());
    assertEquals(1L, campaign1.getCampaignId());
    assertEquals(PRODUCT_ID, campaign1.getProductId());

    // Verify second campaign
    Campaign campaign2 = response.getValues(1);
    assertEquals(_protoSeatUrn, campaign2.getSeatUrn());
    assertEquals(2L, campaign2.getCampaignId());
    assertEquals("product456", campaign2.getProductId());
  }

  @Test
  public void testFindCampaignsWithApSettingsFallback() {
    // Setup - no existing campaigns but AP settings exist
    when(_lssAutoProspectingDB.findCampaignsBySeat(SEAT_URN)).thenReturn(Task.value(Collections.emptyList()));

    Campaign expectedCampaign = Campaign.newBuilder()
        .setSeatUrn(_protoSeatUrn)
        .setContractUrn(_protoContractUrn)
        .setProductId(PRODUCT_ID)
        .setCampaignId(CAMPAIGN_ID)
        .setCreatedTime(ONBOARDING_COMPLETED_TIME)
        .setLastModifiedTime(LAST_MODIFIED_TIME)
        .build();

    CampaignService spyService = spy(_campaignService);
    doReturn(Task.value(Optional.of(expectedCampaign))).when(spyService)
        .maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn);

    // Execute
    FindCampaignBySeatAndContractResponse response =
        runAndWait(spyService.findCampaignsBySeatAndContract(_protoSeatUrn, _protoContractUrn));

    // Verify
    assertEquals(1, response.getValuesCount());
    assertEquals(expectedCampaign, response.getValues(0));
    verify(spyService).maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn);
  }

  @Test
  public void testFindCampaignsNothingFound() {
    // Setup - no campaigns and no AP settings
    when(_lssAutoProspectingDB.findCampaignsBySeat(SEAT_URN)).thenReturn(Task.value(Collections.emptyList()));

    CampaignService spyService = spy(_campaignService);
    doReturn(Task.value(Optional.empty())).when(spyService)
        .maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn);

    // Execute
    FindCampaignBySeatAndContractResponse response =
        runAndWait(spyService.findCampaignsBySeatAndContract(_protoSeatUrn, _protoContractUrn));

    // Verify
    assertEquals(0, response.getValuesCount());
  }

  @Test
  public void testFindCampaignsWithException() {
    // Setup
    when(_lssAutoProspectingDB.findCampaignsBySeat(SEAT_URN)).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    // Execute & Verify
    RestLiServiceException exception =
        runAndWaitException(_campaignService.findCampaignsBySeatAndContract(_protoSeatUrn, _protoContractUrn),
            RestLiServiceException.class);
    assertEquals(HttpStatus.S_500_INTERNAL_SERVER_ERROR, exception.getStatus());
  }

  @Test
  public void testConvertAutoProspectingSettingToCampaignComplete() {
    // Setup
    AutoProspectingSetting apSetting = createCompleteApSetting();

    // Execute
    Campaign campaign = _campaignService.convertAutoProspectingSettingToCampaign(_protoSeatUrn, _protoContractUrn, apSetting);

    // Verify
    assertNotNull(campaign);
    assertEquals(_protoSeatUrn, campaign.getSeatUrn());
    assertEquals(_protoContractUrn, campaign.getContractUrn());
    assertEquals(PRODUCT_ID, campaign.getProductId());
    assertEquals(ONBOARDING_COMPLETED_TIME, campaign.getCreatedTime());
    assertEquals(LAST_MODIFIED_TIME, campaign.getLastModifiedTime());

    // Verify URN conversions
    assertEquals(2, campaign.getAccountListUrnsCount());
    assertEquals(123L, campaign.getAccountListUrns(0).getListId());
    assertEquals(456L, campaign.getAccountListUrns(1).getListId());

    assertEquals(2, campaign.getAccountUrnsCount());
    assertEquals(789L, campaign.getAccountUrns(0).getOrganizationId());
    assertEquals(101112L, campaign.getAccountUrns(1).getOrganizationId());
  }

  @Test
  public void testConvertApSettingWithMissingFields() {
    // Setup
    AutoProspectingSetting minimalSetting = new AutoProspectingSetting();
    minimalSetting.setProductId(PRODUCT_ID);

    // Execute
    Campaign campaign = _campaignService.convertAutoProspectingSettingToCampaign(_protoSeatUrn, _protoContractUrn, minimalSetting);

    // Verify - particularly time fields
    assertEquals(PRODUCT_ID, campaign.getProductId(), "productId should be set");
    assertFalse(campaign.getLastModifiedTime() > 0, "lastModifiedTime should not be set");
    assertFalse(campaign.getCreatedTime() > 0, "createdTime should not be set");
    assertEquals(0, campaign.getAccountListUrnsCount(), "account list urns should be empty");
    assertEquals(0, campaign.getAccountUrnsCount(), "account urns should be empty");
  }

  @Test
  public void testMaybeCloneApSettingsToCampaign_Success() {
    // Setup
    SeatSellerIdentity sellerIdentity = createSellerIdentityWithApSetting();
    when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN, SEAT_URN)).thenReturn(Task.value(sellerIdentity));

    CampaignService spyService = spy(_campaignService);
    AutoProspectingSetting apSetting = sellerIdentity.getAutoProspectingSettings().get(0);
    Campaign convertedCampaign = spyService.convertAutoProspectingSettingToCampaign(_protoSeatUrn, _protoContractUrn, apSetting);

    CreateCampaignResponse createResponse = CreateCampaignResponse.newBuilder()
        .setKey(CampaignKey.newBuilder().setSeatUrn(_protoSeatUrn).setCampaignId(CAMPAIGN_ID).build())
        .setValue(convertedCampaign)
        .build();
    doReturn(Task.value(createResponse)).when(spyService).createCampaign(convertedCampaign);

    // Execute
    Optional<Campaign> campaign = runAndWait(spyService.maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn));

    // Verify
    Campaign clonedCampaign = campaign.get();
    assertEquals(CAMPAIGN_ID, clonedCampaign.getCampaignId());
    assertEquals(apSetting.getOnboardingCompletedTime(), clonedCampaign.getCreatedTime(),
        "inherits onboardin from AP setting");
    assertEquals(apSetting.getLastModifiedTime(), clonedCampaign.getLastModifiedTime(),
        "inherits lastModified from AP setting");
    verify(_salesSellerIdentityService).getSellerIdentity(CONTRACT_URN, SEAT_URN);
  }

  @Test
  public void testMaybeCloneApSettingsToCampaign_EmptySettings() {
    // Setup
    SeatSellerIdentity sellerIdentity = createSellerIdentityWithEmptyApSettings();
    when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN, SEAT_URN)).thenReturn(Task.value(sellerIdentity));

    CampaignService spyService = spy(_campaignService);

    // Execute
    Optional<Campaign> campaigns = runAndWait(spyService.maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn));

    // Verify
    assertFalse(campaigns.isPresent(), "Should return empty campaign when AP settings are empty");
    verify(_salesSellerIdentityService).getSellerIdentity(CONTRACT_URN, SEAT_URN);
  }

  @Test
  public void testMaybeCloneApSettingsToCampaign_ServiceException() {
    // Setup
    when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN, SEAT_URN)).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Service error")));
    CampaignService spyService = spy(_campaignService);

    // Execute
    Optional<Campaign> campaigns = runAndWait(spyService.maybeCloneApSettingsToCampaign(_protoSeatUrn, _protoContractUrn));

    // Verify
    assertFalse(campaigns.isPresent(), "Should return empty campaign when getSellerIdentity throws exception");
    verify(_salesSellerIdentityService).getSellerIdentity(CONTRACT_URN, SEAT_URN);
  }

  //
  // Test Utilities and Factory Methods
  //

  private CampaignKey createTestCampaignKey(proto.com.linkedin.common.SeatUrn seatUrn, long campaignId) {
    return CampaignKey.newBuilder().setSeatUrn(seatUrn).setCampaignId(campaignId).build();
  }

  private Campaign createTestCampaign(proto.com.linkedin.common.SeatUrn seatUrn,
      proto.com.linkedin.common.ContractUrn contractUrn, String productId) {
    proto.com.linkedin.common.SalesListUrn salesListUrn =
        proto.com.linkedin.common.SalesListUrn.newBuilder().setListId(123).build();
    proto.com.linkedin.common.OrganizationUrn accountUrn =
        proto.com.linkedin.common.OrganizationUrn.newBuilder().setOrganizationId(789).build();

    return Campaign.newBuilder()
        .setSeatUrn(seatUrn)
        .setContractUrn(contractUrn)
        .setProductId(productId)
        .addAccountListUrns(salesListUrn)
        .addAccountUrns(accountUrn)
        .build();
  }

  private com.linkedin.sales.espresso.Campaign createTestEspressoCampaign() {
    com.linkedin.sales.espresso.Campaign espressoCampaign = createBaseEspressoCampaign(PRODUCT_ID, 1000L, 2000L);

    // Add account list URNs
    List<CharSequence> accountListUrns = new ArrayList<>();
    accountListUrns.add("urn:li:salesList:123");
    accountListUrns.add("urn:li:salesList:456");
    espressoCampaign.setAccountListUrns(accountListUrns);

    // Add account URNs
    List<CharSequence> accountUrns = new ArrayList<>();
    accountUrns.add("urn:li:organization:789");
    accountUrns.add("urn:li:organization:101112");
    espressoCampaign.setAccountUrns(accountUrns);

    return espressoCampaign;
  }

  private List<Pair<Long, com.linkedin.sales.espresso.Campaign>> createMultipleEspressoCampaignPairs() {
    // Campaign 1
    com.linkedin.sales.espresso.Campaign espressoCampaign1 = createBaseEspressoCampaign(PRODUCT_ID, 1000L, 2000L);
    espressoCampaign1.setAccountListUrns(new ArrayList<>());
    espressoCampaign1.setAccountUrns(new ArrayList<>());

    // Campaign 2
    com.linkedin.sales.espresso.Campaign espressoCampaign2 = createBaseEspressoCampaign("product456", 3000L, 4000L);
    espressoCampaign2.setAccountListUrns(new ArrayList<>());
    espressoCampaign2.setAccountUrns(new ArrayList<>());

    return Arrays.asList(new Pair<>(1L, espressoCampaign1), new Pair<>(2L, espressoCampaign2));
  }

  /**
   * Creates a base Espresso Campaign with common properties set
   *
   * @param productId the product ID to set
   * @param createdTime the creation timestamp to set
   * @param lastModifiedTime the last modified timestamp to set
   * @return an Espresso Campaign with common fields set
   */
  private com.linkedin.sales.espresso.Campaign createBaseEspressoCampaign(String productId, long createdTime,
      long lastModifiedTime) {
    com.linkedin.sales.espresso.Campaign espressoCampaign = new com.linkedin.sales.espresso.Campaign();
    espressoCampaign.setContractUrn(CONTRACT_URN.toString());
    espressoCampaign.setProductId(productId);
    espressoCampaign.setCreatedTime(createdTime);
    espressoCampaign.setLastModifiedTime(lastModifiedTime);
    return espressoCampaign;
  }

  private AutoProspectingSetting createCompleteApSetting() {
    AutoProspectingSetting apSetting = new AutoProspectingSetting();
    apSetting.setProductId(PRODUCT_ID);
    apSetting.setLastModifiedTime(LAST_MODIFIED_TIME);
    apSetting.setOnboardingCompletedTime(ONBOARDING_COMPLETED_TIME);

    // Add account list URNs
    SalesListUrn salesListUrn1 = UrnUtils.createSalesListUrn(123L);
    SalesListUrn salesListUrn2 = UrnUtils.createSalesListUrn(456L);
    SalesListUrnArray accountListUrns = new SalesListUrnArray(Arrays.asList(salesListUrn1, salesListUrn2));
    apSetting.setAccountListUrns(accountListUrns);

    // Add account URNs
    OrganizationUrn orgUrn1 = UrnUtils.createOrganizationUrn(789L);
    OrganizationUrn orgUrn2 = UrnUtils.createOrganizationUrn(101112L);
    OrganizationUrnArray accountUrns = new OrganizationUrnArray(Arrays.asList(orgUrn1, orgUrn2));
    apSetting.setAccountUrns(accountUrns);

    return apSetting;
  }

  private SeatSellerIdentity createSellerIdentityWithApSetting() {
    AutoProspectingSetting apSetting = new AutoProspectingSetting();
    apSetting.setProductId(PRODUCT_ID);
    apSetting.setLastModifiedTime(LAST_MODIFIED_TIME);
    apSetting.setOnboardingCompletedTime(ONBOARDING_COMPLETED_TIME);
    apSetting.setAccountListUrns(new SalesListUrnArray());
    apSetting.setAccountUrns(new OrganizationUrnArray());

    SeatSellerIdentity spySellerIdentity = spy(new SeatSellerIdentity());
    when(spySellerIdentity.hasAutoProspectingSettings()).thenReturn(true);
    when(spySellerIdentity.getAutoProspectingSettings()).thenReturn(
        new AutoProspectingSettingArray(Collections.singletonList(apSetting)));

    return spySellerIdentity;
  }

  private SeatSellerIdentity createSellerIdentityWithEmptyApSettings() {
    SeatSellerIdentity spySellerIdentity = spy(new SeatSellerIdentity());
    when(spySellerIdentity.hasAutoProspectingSettings()).thenReturn(true);
    when(spySellerIdentity.getAutoProspectingSettings()).thenReturn(
        new AutoProspectingSettingArray(Collections.emptyList()));

    return spySellerIdentity;
  }

  private void assertCampaignFields(Campaign campaign) {
    assertNotNull(campaign);
    assertEquals(_defaultCampaignKey.getSeatUrn(), campaign.getSeatUrn());
    assertEquals(_defaultCampaignKey.getCampaignId(), campaign.getCampaignId());
    assertEquals(PRODUCT_ID, campaign.getProductId());
    assertEquals(1000L, campaign.getCreatedTime());
    assertEquals(2000L, campaign.getLastModifiedTime());
  }

  private void assertCampaignListUrns(Campaign campaign) {
    assertEquals(2, campaign.getAccountListUrnsCount());
    assertEquals(123L, campaign.getAccountListUrns(0).getListId());
    assertEquals(456L, campaign.getAccountListUrns(1).getListId());
  }

  private void assertCampaignAccountUrns(Campaign campaign) {
    assertEquals(2, campaign.getAccountUrnsCount());
    assertEquals(789L, campaign.getAccountUrns(0).getOrganizationId());
    assertEquals(101112L, campaign.getAccountUrns(1).getOrganizationId());
  }
}
