package com.linkedin.sales.service.autoprospecting;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import proto.com.linkedin.salesautoprospecting.BatchCreateLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.FindByParamsLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunLeadResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLead;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunLeadStatus;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunLeadResponse;
import si.RequestPagingContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


public class LeadFindingRunLeadServiceTest extends BaseEngineParTest {
  private static final MemberUrn MEMBER_URN_1 = new MemberUrn(123L);
  private static final ContractUrn CONTRACT_URN_1 = new ContractUrn(456L);
  private static final MemberUrn MEMBER_URN_2 = new MemberUrn(789L);
  private static final ContractUrn CONTRACT_URN_2 = new ContractUrn(999L);
  private static final long runId1 = 1L;
  private static final long runId2 = 2L;
  @Mock
  private LssAutoProspectingDB lssAutoProspectingDB;

  private LeadFindingRunLeadService leadFindingRunLeadService;

  @BeforeTest(alwaysRun = true)
  void setUp() {
    MockitoAnnotations.openMocks(this);
    leadFindingRunLeadService = new LeadFindingRunLeadService(lssAutoProspectingDB);
  }

  @org.testng.annotations.Test
  public void testBatchCreateLeadFindingRunLead_EmptyList() {
    Task<BatchCreateLeadFindingRunLeadResponse> response = leadFindingRunLeadService.batchCreateLeadFindingRunLead(
        Collections.emptyList());
    BatchCreateLeadFindingRunLeadResponse result = runAndWait(response); // Ensure the task is executed
    assertTrue(result.getResponsesList().isEmpty());
  }

  @org.testng.annotations.Test
  void testBatchCreateLeadFindingRunLead_Success() {
    when(lssAutoProspectingDB.createLeadFindingRunLead(anyLong(), any(MemberUrn.class), any()))
        .thenReturn(Task.value(HttpStatus.S_201_CREATED));

    LeadFindingRunLead lead1 = LeadFindingRunLead.newBuilder()
        .setRunId(runId1)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder()
            .setMemberId(MEMBER_URN_1.getMemberId()) // Set the required memberId field
            .build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder()
            .setContractId(CONTRACT_URN_1.getContractId()) // Ensure contractUrn is set
            .build())
        .build();

    LeadFindingRunLead lead2 = LeadFindingRunLead.newBuilder()
        .setRunId(runId2)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder()
            .setMemberId(MEMBER_URN_2.getMemberId()) // Set the required memberId field
            .build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder()
            .setContractId(MEMBER_URN_2.getMemberId()) // Ensure contractUrn is set
            .build())
        .build();

    List<LeadFindingRunLead> leads = Arrays.asList(lead1, lead2);

    Task<BatchCreateLeadFindingRunLeadResponse> response = leadFindingRunLeadService.batchCreateLeadFindingRunLead(
        leads);
    BatchCreateLeadFindingRunLeadResponse result = runAndWait(response);

    assertEquals(2, result.getResponsesList().size());
    // Validate that the response contains the expected keys
    assertTrue(result.getResponsesList().stream()
        .anyMatch(responseItem -> responseItem.getKey().getRunId() == 1L));
    assertTrue(result.getResponsesList().stream()
        .anyMatch(responseItem -> responseItem.getKey().getRunId() == 2L));

    verify(lssAutoProspectingDB, times(2)).createLeadFindingRunLead(anyLong(), any(MemberUrn.class), any());

  }

  @org.testng.annotations.Test
  void testGetLeadFindingRunLead_Success() throws URISyntaxException {
    com.linkedin.sales.espresso.LeadFindingRunLead leadFindingRunLead = createLeadFindingRunLeadEspresso();

    when(lssAutoProspectingDB.getLeadByLeadFindingRunIdAndMemberUrn(eq(1L), any(MemberUrn.class)))
        .thenReturn(Task.value(Optional.of(leadFindingRunLead)));

    LeadFindingRunLeadKey leadFindingRunLeadKey = buildLeadFindingRunLeadKey();

    Task<GetLeadFindingRunLeadResponse> responseTask = leadFindingRunLeadService.getLeadFindingRunLead(leadFindingRunLeadKey);
    GetLeadFindingRunLeadResponse response = runAndWait(responseTask);

    assertNotNull(response);
    assertEquals(1L, response.getValue().getRunId());
    assertEquals(1L, response.getValue().getRunId());
    assertEquals("Test Rationale 1", response.getValue().getRationale());
    assertEquals(LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_ACCEPTED_MANUALLY, response.getValue().getStatus());
    assertEquals(123, response.getValue().getCreatedTime());
    assertEquals(456, response.getValue().getModifiedTime());
    assertEquals(Arrays.asList(1L, 2L, 3L), response.getValue().getRelevantPositionIdsList());
    assertEquals(Arrays.asList("Strength1", "Strength2", "Strength3"), response.getValue().getKeyStrengthsList());
  }

  @org.testng.annotations.Test
  void testGetLeadFindingRunLead_NotFound() {
    LeadFindingRunLeadKey leadFindingRunLeadKey = buildLeadFindingRunLeadKey();

    when(lssAutoProspectingDB.getLeadByLeadFindingRunIdAndMemberUrn(eq(1L), any(MemberUrn.class)))
        .thenReturn(Task.value(Optional.empty()));

    Task<GetLeadFindingRunLeadResponse> responseTask = leadFindingRunLeadService.getLeadFindingRunLead(leadFindingRunLeadKey);
    GetLeadFindingRunLeadResponse response = runAndWait(responseTask);

    assertNotNull(response);
    assertEquals(0L, response.getValue().getRunId()); // Default value
    verify(lssAutoProspectingDB, times(1)).getLeadByLeadFindingRunIdAndMemberUrn(eq(1L), any(MemberUrn.class));
  }

  @org.testng.annotations.Test
  public void testPartialUpdate_Success() {
    // Set up
    LeadFindingRunLeadKey leadFindingRunLeadKey = LeadFindingRunLeadKey.newBuilder()
        .setRunId(runId1)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(1234).build())
        .build();

    LeadFindingRunLead leadFindingRunLead = LeadFindingRunLead.newBuilder()
        .setRunId(runId1)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(1234).build())
        .setRationale("Test Rationale 1")
        .setVariant("12")
        .setCreatedTime(123)
        .setModifiedTime(456)
        .setStatus(LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_ACCEPTED_MANUALLY)
        .addAllRelevantPositionIds(Arrays.asList(1L, 2L, 3L))
        .addAllKeyStrengths(Arrays.asList("Strength1", "Strength2", "Strength3"))
        .build();
    com.linkedin.sales.espresso.LeadFindingRunLead leadFindingRunLeadEspresso = createLeadFindingRunLeadEspresso();

    LeadFindingRunLead leadFindingRunLeadExpected = LeadFindingRunLead.newBuilder()
        .setRunId(runId1)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(1234).build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(456L).build())
        .setRationale("Test Rationale 1")
        .setVariant("12")
        .setCreatedTime(123)
        .setModifiedTime(456)
        .setStatus(LeadFindingRunLeadStatus.LeadFindingRunLeadStatus_ACCEPTED_MANUALLY)
        .addAllRelevantPositionIds(Arrays.asList(1L, 2L, 3L))
        .addAllKeyStrengths(Arrays.asList("Strength1", "Strength2", "Strength3"))
        .build();

    when(lssAutoProspectingDB.partialUpdateLeadFindingRunLead(
        eq(1L), any(MemberUrn.class), any(com.linkedin.sales.espresso.LeadFindingRunLead.class)))
        .thenReturn(Task.value(leadFindingRunLeadEspresso));

    // Execute
    Task<PartialUpdateLeadFindingRunLeadResponse> responseTask = leadFindingRunLeadService.partialUpdate(leadFindingRunLeadKey, leadFindingRunLead);
    PartialUpdateLeadFindingRunLeadResponse response = runAndWait(responseTask);

    // Verify
    assertNotNull(response);
    assertEquals(leadFindingRunLeadExpected, response.getValue());
    //verify(lssAutoProspectingDB, times(1)).partialUpdateLeadFindingRunLead(eq(1L), any(MemberUrn.class), any());
  }

  @org.testng.annotations.Test
  public void testPartialUpdate_Failure() {
    // Set up
    LeadFindingRunLeadKey leadFindingRunLeadKey = LeadFindingRunLeadKey.newBuilder()
        .setRunId(runId1)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(1234).build())
        .build();

    LeadFindingRunLead leadFindingRunLead = LeadFindingRunLead.newBuilder()
        .setRunId(runId2)
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(1234).build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(456L).build())
        .setRationale("Updated Rationale")
        .build();

    when(lssAutoProspectingDB.partialUpdateLeadFindingRunLead(
        eq(1L), any(MemberUrn.class), any(com.linkedin.sales.espresso.LeadFindingRunLead.class)))
        .thenReturn(Task.failure(new RuntimeException("Update failed")));

    Task<PartialUpdateLeadFindingRunLeadResponse> responseTask = leadFindingRunLeadService.partialUpdate(leadFindingRunLeadKey, leadFindingRunLead);

    // Verify
    RuntimeException exception = assertThrows(RuntimeException.class, () -> runAndWait(responseTask));
    assertEquals("java.lang.RuntimeException: Update failed", exception.getMessage());
  }

  @org.testng.annotations.Test
  public void testFindByParams_Success() throws URISyntaxException {
    // Set up
    Long runId = 1L;
    RequestPagingContext pagingContext = RequestPagingContext.newBuilder().setStart(0).setCount(10).build();

    com.linkedin.sales.espresso.LeadFindingRunLead leadFindingRunLead = createLeadFindingRunLeadEspresso();

    pegasus.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey leadFindingRunLeadKey =
        new pegasus.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey();
    leadFindingRunLeadKey.setRunId(runId1); // Replace with the appropriate runId
    leadFindingRunLeadKey.setMemberUrn(new MemberUrn(MEMBER_URN_2.getMemberId())); // Replace "23" with the appropriate memberId

    List<Pair<pegasus.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey, com.linkedin.sales.espresso.LeadFindingRunLead>> mockResults = Collections.singletonList(
        Pair.of(leadFindingRunLeadKey, leadFindingRunLead)
    );

    when(lssAutoProspectingDB.findLeadFindingRunLeadByParams(eq(runId), eq(0), eq(10)))
        .thenReturn(Task.value(mockResults));

    // Execute
    Task<FindByParamsLeadFindingRunLeadResponse> responseTask = leadFindingRunLeadService.findByParams(runId, pagingContext);
    FindByParamsLeadFindingRunLeadResponse response = runAndWait(responseTask);

    // Verify
    assertNotNull(response);
    assertFalse(response.getValuesList().isEmpty());
    assertEquals(1, response.getValuesList().size());
    assertEquals("Test Rationale 1", response.getValuesList().get(0).getRationale()); // Ensure mock data matches
  }

  private LeadFindingRunLeadKey buildLeadFindingRunLeadKey() {
    return LeadFindingRunLeadKey.newBuilder()
        .setMemberUrn(proto.com.linkedin.common.MemberUrn.newBuilder().setMemberId(MEMBER_URN_1.getMemberId()))
        .setRunId(1L)
        .build();
  }

  private com.linkedin.sales.espresso.LeadFindingRunLead createLeadFindingRunLeadEspresso() {
    com.linkedin.sales.espresso.LeadFindingRunLead leadFindingRunLead = new com.linkedin.sales.espresso.LeadFindingRunLead();
    leadFindingRunLead.setCreatedTime(123);
    leadFindingRunLead.setRationale("Test Rationale 1");
    leadFindingRunLead.setModifiedTime(456);
    leadFindingRunLead.setStatus("ACCEPTED_MANUALLY");
    leadFindingRunLead.setContractUrn(CONTRACT_URN_1.toString());
    leadFindingRunLead.setKeyStrengths(Arrays.asList("Strength1", "Strength2", "Strength3"));
    leadFindingRunLead.setVariant("12");
    leadFindingRunLead.setScore(0);
    leadFindingRunLead.setRelevantPositionIds(Arrays.asList(1L, 2L, 3L));
    return leadFindingRunLead;
  }
}

