package com.linkedin.sales.service.autoprospecting;

import com.google.common.collect.ImmutableList;
import com.google.protobuf.ByteString;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import com.linkedin.util.IdGenerator;
import com.linkedin.util.Pair;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import proto.com.linkedin.common.TrackingId;
import proto.com.linkedin.salesautoprospecting.CreateLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.FindByQueryLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.GetLeadFindingRunResponse;
import proto.com.linkedin.salesautoprospecting.LeadFindingRun;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunError;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunKey;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunStatus;
import proto.com.linkedin.salesautoprospecting.LeadFindingRunType;
import proto.com.linkedin.salesautoprospecting.PartialUpdateLeadFindingRunResponse;
import si.RequestPagingContext;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


public class LeadFindingRunServiceTest extends BaseEngineParTest {
  private static final Long RUN_ID1 = 123L;
  private static final Long RUN_ID2 = 234L;
  private static final SeatUrn SEAT_URN = new SeatUrn(123L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(456L);
  private static final Long CAMPAIGN_ID = 22222L;
  private static final TrackingId TRACKING_ID =
      TrackingId.newBuilder().setFixedValue(ByteString.copyFrom(IdGenerator.randomUuidBytes())).build();
  private static final Long CURRENT_TIME = System.currentTimeMillis();
  private static final Long END_TIME = CURRENT_TIME + 3600 * 1000;

  @Mock
  private LssAutoProspectingDB _lssAutoProspectingDB;

  private LeadFindingRunService _prospectingAgentService;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _prospectingAgentService = new LeadFindingRunService(_lssAutoProspectingDB);
  }

  @Test
  public void testCreate() {
    when(_lssAutoProspectingDB.createLeadFindingRun(eq(SEAT_URN), eq(CAMPAIGN_ID),
        any(com.linkedin.sales.espresso.LeadFindingRun.class))).thenReturn(Task.value(RUN_ID1));

    LeadFindingRun leadFindingRun = buildLeadFindingRunForCreate();
    CreateLeadFindingRunResponse response = runAndWait(_prospectingAgentService.create(leadFindingRun));

    LeadFindingRunKey expectedKey = LeadFindingRunKey.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setCampaignId(CAMPAIGN_ID)
        .setRunId(RUN_ID1)
        .build();
    assertEquals(response.getKey(), expectedKey);
    assertEquals(response.getValue(), leadFindingRun);
  }

  @Test
  public void testPartialUpdate() {
    long timestamp = System.currentTimeMillis();
    com.linkedin.sales.espresso.LeadFindingRun leadFindingRun = new com.linkedin.sales.espresso.LeadFindingRun();
    leadFindingRun.setContractUrn(CONTRACT_URN.toString());
    leadFindingRun.setStatus("FAILED");
    leadFindingRun.setType("ON_DEMAND");
    leadFindingRun.setError(new com.linkedin.sales.espresso.LeadFindingRunError("429", "Too many requests"));
    leadFindingRun.setLastModifiedTime(timestamp);
    leadFindingRun.setCreatedTime(CURRENT_TIME);
    leadFindingRun.setScheduledStartTime(CURRENT_TIME);
    leadFindingRun.setStartTime(CURRENT_TIME);
    leadFindingRun.setProjectedCompletionTime(END_TIME);
    leadFindingRun.setLeadLimit(25);
    leadFindingRun.setProspectedLeadsCount(10);

    when(_lssAutoProspectingDB.partialUpdateLeadFindingRun(eq(SEAT_URN), eq(CAMPAIGN_ID), eq(RUN_ID1),
        any(com.linkedin.sales.espresso.LeadFindingRun.class))).thenReturn(Task.value(leadFindingRun));

    LeadFindingRunKey leadFindingRunKey = buildLeadFindingRunKey();
    LeadFindingRun toUpdate = LeadFindingRun.newBuilder()
        .setStatus(LeadFindingRunStatus.LeadFindingRunStatus_FAILED)
        .setError(LeadFindingRunError.newBuilder().setCode("429").setMessage("Fuse limit").build())
        .setLeadLimit(25)
        .setProspectedLeadsCount(10)
        .build();

    PartialUpdateLeadFindingRunResponse
        response = runAndWait(_prospectingAgentService.partialUpdate(leadFindingRunKey, toUpdate));
    LeadFindingRun expected = LeadFindingRun.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build())
        .setRunId(RUN_ID1)
        .setCampaignId(CAMPAIGN_ID)
        .setStatus(LeadFindingRunStatus.LeadFindingRunStatus_FAILED)
        .setError(LeadFindingRunError.newBuilder().setCode("429").setMessage("Too many requests").build())
        .setType(LeadFindingRunType.LeadFindingRunType_ON_DEMAND)
        .setLastModifiedTime(timestamp)
        .setCreatedTime(CURRENT_TIME)
        .setScheduledStartTime(CURRENT_TIME)
        .setStartTime(CURRENT_TIME)
        .setProjectedCompletionTime(END_TIME)
        .setLeadLimit(25)
        .setProspectedLeadsCount(10)
        .build();
    assertEquals(response.getValue(), expected);
  }

  @Test
  public void testGet() {
    long timestamp = System.currentTimeMillis();
    com.linkedin.sales.espresso.LeadFindingRun leadFindingRun = buildEspressoLeadFindingRun("IN_PROGRESS", "ON_DEMAND");

    when(_lssAutoProspectingDB.getLeadFindingRun(eq(SEAT_URN), eq(CAMPAIGN_ID), eq(RUN_ID1)))
        .thenReturn(Task.value(leadFindingRun));

    LeadFindingRun expected = LeadFindingRun.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build())
        .setRunId(RUN_ID1)
        .setCampaignId(CAMPAIGN_ID)
        .setStatus(LeadFindingRunStatus.LeadFindingRunStatus_IN_PROGRESS)
        .setType(LeadFindingRunType.LeadFindingRunType_ON_DEMAND)
        .setLastModifiedTime(timestamp)
        .setCreatedTime(timestamp)
        .setStartTime(timestamp)
        .setCompletionTime(timestamp)
        .setTrackingId(TRACKING_ID)
        .build();

    LeadFindingRunKey leadFindingRunKey = buildLeadFindingRunKey();
    GetLeadFindingRunResponse response = runAndWait(_prospectingAgentService.get(leadFindingRunKey));
    assertEquals(expected, response.getValue());
  }

  @Test
  public void testFindByQuery() {
    pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey leadFindingRunKey1 = new pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey();
    leadFindingRunKey1.setSeatUrn(SEAT_URN);
    leadFindingRunKey1.setCampaignId(CAMPAIGN_ID);
    leadFindingRunKey1.setRunId(RUN_ID1);
    com.linkedin.sales.espresso.LeadFindingRun leadFindingRun1 = buildEspressoLeadFindingRun("IN_PROGRESS", "ON_DEMAND");

    pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey leadFindingRunKey2 = new pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey();
    leadFindingRunKey2.setSeatUrn(SEAT_URN);
    leadFindingRunKey2.setCampaignId(CAMPAIGN_ID);
    leadFindingRunKey2.setRunId(RUN_ID2);
    com.linkedin.sales.espresso.LeadFindingRun leadFindingRun2 = buildEspressoLeadFindingRun("COMPLETED", "SCHEDULED");

    when(_lssAutoProspectingDB.findLeadFindingRunByQuery(any(SeatUrn.class), any(), any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(ImmutableList.of(
            Pair.of(leadFindingRunKey1, leadFindingRun1),
            Pair.of(leadFindingRunKey2, leadFindingRun2))));

    RequestPagingContext requestPagingContext = RequestPagingContext.newBuilder().setStart(0).setCount(5).build();
    proto.com.linkedin.common.SeatUrn seatUrn = proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build();
    FindByQueryLeadFindingRunResponse
        response = runAndWait(_prospectingAgentService.findByQuery(seatUrn, null, null, null, requestPagingContext));

    List<LeadFindingRun> leadFindingRuns = response.getValuesList();
    assertEquals(leadFindingRuns.size(), 2);
    assertEquals(leadFindingRuns.get(0).getRunId(), RUN_ID1);
    assertEquals(leadFindingRuns.get(0).getCampaignId(), CAMPAIGN_ID);
    assertEquals(leadFindingRuns.get(1).getRunId(), RUN_ID2);
    assertEquals(leadFindingRuns.get(1).getCampaignId(), CAMPAIGN_ID);
  }

  private LeadFindingRunKey buildLeadFindingRunKey() {
    return LeadFindingRunKey.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setCampaignId(CAMPAIGN_ID)
        .setRunId(RUN_ID1)
        .build();
  }

  private LeadFindingRun buildLeadFindingRunForCreate() {
    return LeadFindingRun.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build())
        .setCampaignId(CAMPAIGN_ID)
        .setStatus(LeadFindingRunStatus.LeadFindingRunStatus_NOT_STARTED)
        .setType(LeadFindingRunType.LeadFindingRunType_ON_DEMAND)
        .setScheduledStartTime(CURRENT_TIME)
        .setStartTime(CURRENT_TIME)
        .setProjectedCompletionTime(END_TIME)
        .setTrackingId(TRACKING_ID)
        .setCompletionTime(CURRENT_TIME)
        .build();
  }

  private com.linkedin.sales.espresso.LeadFindingRun buildEspressoLeadFindingRun(String status, String type) {
    long timestamp = System.currentTimeMillis();
    com.linkedin.sales.espresso.LeadFindingRun leadFindingRun = new com.linkedin.sales.espresso.LeadFindingRun();
    leadFindingRun.setContractUrn(CONTRACT_URN.toString());
    leadFindingRun.setStatus(status);
    leadFindingRun.setType(type);
    leadFindingRun.setLastModifiedTime(timestamp);
    leadFindingRun.setCreatedTime(timestamp);
    leadFindingRun.setStartTime(timestamp);
    leadFindingRun.setCompletionTime(timestamp);
    leadFindingRun.setTrackingId(new com.linkedin.sales.espresso.TrackingId(TRACKING_ID.getFixedValue().toByteArray()));
    return leadFindingRun;
  }
}
