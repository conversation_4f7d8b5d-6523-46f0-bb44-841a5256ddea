package com.linkedin.sales.service.autoprospecting;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssAutoProspectingDB;
import java.util.Optional;

import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import proto.com.linkedin.salesautoprospecting.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SearchCriteriaServiceTest extends BaseEngineParTest {
  private static final SeatUrn SEAT_URN = new SeatUrn(123L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(456L);
  private static final String RAW_CRITERIA = "This is a test criteria";
  private static final String PROMPT_VARIANT = "v1";
  private final static String FACET_SELECTIONS = "facetSelections=[{valuesWithSelections=[{value=105, selected=true}]}]";
  private final static String EBR_SEARCH_QUERIES = "Someone is known as a good buyer";

  @Mock
  private LssAutoProspectingDB _lssAutoProspectingDB;

  private SearchCriteriaService _searchCriteriaService;


  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _searchCriteriaService = new SearchCriteriaService(_lssAutoProspectingDB);
  }

  @Test
  public void testCreateSearchCriteriaHappyPath() {
    // Set up
    when(_lssAutoProspectingDB.createSearchCriteria(any(SeatUrn.class), anyString(), any(com.linkedin.sales.espresso.SearchCriteriaV1.class)))
        .thenReturn(Task.value(HttpStatus.S_201_CREATED));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();
    SearchCriteria searchCriteria = buildSearchCriteria();

    // Verify
    CreateSearchCriteriaResponse response = runAndWait(_searchCriteriaService.createSearchCriteria(searchCriteriaKey, searchCriteria));
    assertEquals(response.getKey(), searchCriteriaKey);
  }

  @Test
  public void testCreateSearchCriteriaWithException() {
    // Set up
    when(_lssAutoProspectingDB.createSearchCriteria(any(SeatUrn.class), anyString(), any(com.linkedin.sales.espresso.SearchCriteriaV1.class)))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED)));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();
    SearchCriteria searchCriteria = buildSearchCriteria();

    // Verify
    RestLiServiceException exception = runAndWaitException(
        _searchCriteriaService.createSearchCriteria(searchCriteriaKey, searchCriteria), RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testPartialUpdateSearchCriteriaHappyPath() {
    // Set up
    when(_lssAutoProspectingDB.partialUpdateSearchCriteria(any(SeatUrn.class), anyString(), any(com.linkedin.sales.espresso.SearchCriteriaV1.class)))
        .thenReturn(Task.value(HttpStatus.S_200_OK));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();
    SearchCriteria searchCriteria = buildSearchCriteria();

    // Verify
    PartialUpdateSearchCriteriaResponse response = runAndWait(_searchCriteriaService.partialUpdateSearchCriteria(searchCriteriaKey, searchCriteria));
    assertEquals(response.getValue(), searchCriteria);
  }

  @Test
  public void testPartialUpdateSearchCriteriaWithException() {
    // Set up
    when(_lssAutoProspectingDB.partialUpdateSearchCriteria(any(SeatUrn.class), anyString(), any(com.linkedin.sales.espresso.SearchCriteriaV1.class)))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED)));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();
    SearchCriteria searchCriteria = buildSearchCriteria();

    // Verify
    RestLiServiceException exception = runAndWaitException(
        _searchCriteriaService.partialUpdateSearchCriteria(searchCriteriaKey, searchCriteria), RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testGetSearchCriteriaHappyPath() {
    // Set up
    com.linkedin.sales.espresso.SearchCriteriaV1 espressoSearchCriteria = new com.linkedin.sales.espresso.SearchCriteriaV1();
    espressoSearchCriteria.setRawCriteria(RAW_CRITERIA);
    espressoSearchCriteria.setFacetSelections(FACET_SELECTIONS);
    espressoSearchCriteria.setEbrSearchQuery(EBR_SEARCH_QUERIES);
    espressoSearchCriteria.setContractUrn(CONTRACT_URN.toString());
    long timestamp = System.currentTimeMillis();
    espressoSearchCriteria.setCreatedTime(timestamp);
    espressoSearchCriteria.setLastModifiedTime(timestamp);

    when(_lssAutoProspectingDB.getSearchCriteria(any(SeatUrn.class), anyString()))
        .thenReturn(Task.value(Optional.of(espressoSearchCriteria)));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();

    // Verify
    SearchCriteria expectedSearchCriteria = SearchCriteria.newBuilder()
            .setFacetSelections(FACET_SELECTIONS)
            .setEbrSearchQuery(EBR_SEARCH_QUERIES)
            .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build())
            .setCreatedTime(timestamp)
            .setLastModifiedTime(timestamp)
            .build();
    GetSearchCriteriaResponse response = runAndWait(_searchCriteriaService.getSearchCriteria(searchCriteriaKey));
    assertEquals(expectedSearchCriteria, response.getValue());
  }

  @Test
  public void testGetSearchCriteriaEntityNotFound() {
    // Set up
    when(_lssAutoProspectingDB.getSearchCriteria(any(SeatUrn.class), anyString()))
            .thenReturn(Task.value(Optional.empty()));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();

    // Verify
    GetSearchCriteriaResponse response = runAndWait(_searchCriteriaService.getSearchCriteria(searchCriteriaKey));
    assertEquals(SearchCriteria.newBuilder().build(), response.getValue());
  }

  @Test
  public void testGetSearchCriteriaWithException() {
    // Set up
    when(_lssAutoProspectingDB.getSearchCriteria(any(SeatUrn.class), anyString()))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    SearchCriteriaKey searchCriteriaKey = buildSearchCriteriaKey();

    // Verify
    RestLiServiceException exception = runAndWaitException(
        _searchCriteriaService.getSearchCriteria(searchCriteriaKey), RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  private SearchCriteriaKey buildSearchCriteriaKey() {
    return SearchCriteriaKey.newBuilder()
        .setSeatUrn(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(SEAT_URN.getSeatIdEntity()).build())
        .setRawCriteria(RAW_CRITERIA)
        .setPromptVariant(PROMPT_VARIANT)
        .build();
  }

  private SearchCriteria buildSearchCriteria() {
    return SearchCriteria.newBuilder()
        .setFacetSelections(FACET_SELECTIONS)
        .setEbrSearchQuery(EBR_SEARCH_QUERIES)
        .setContractUrn(proto.com.linkedin.common.ContractUrn.newBuilder().setContractId(CONTRACT_URN.getContractIdEntity()).build())
        .build();
  }
}
