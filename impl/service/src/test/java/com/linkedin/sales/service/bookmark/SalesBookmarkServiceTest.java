package com.linkedin.sales.service.bookmark;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.NotificationV2Urn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.NoMetadata;
import com.linkedin.sales.ds.db.LssBookmarkDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesbookmark.Bookmark;
import com.linkedin.salesbookmark.BookmarkContent;
import com.linkedin.salesbookmark.BookmarkType;
import com.linkedin.util.Pair;
import java.util.Collections;
import java.net.URISyntaxException;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for {@link SalesBookmarkService}
 * <AUTHOR>
 */
public class SalesBookmarkServiceTest extends ServiceUnitTest {
  private static final String MEMBER_CONTRACT_URN_STRING =
      "urn:li:memberContract:(urn:li:member:123,urn:li:contract:456)";
  private static final long CONTRACT_ID = 100L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final long SEAT_ID = 2000L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final long EXPIRATION_TIME_MS = 10000L;
  private static final long BOOKMARK_ID = 384601L;
  private static final BookmarkType BOOKMARK_TYPE = BookmarkType.ALERT;

  private static NotificationV2Urn NOTIFICATION_V_2_URN;
  static {
    try {
      NOTIFICATION_V_2_URN = NotificationV2Urn.createFromUrn(Urn.createFromTuple(NotificationV2Urn.ENTITY_TYPE,
          MEMBER_CONTRACT_URN_STRING,
          "SALES_INSIGHTS_EXPORT_LIST", MEMBER_CONTRACT_URN_STRING));
    } catch (URISyntaxException e) {
      e.printStackTrace();
    }
  }
  @Mock
  private LssBookmarkDB _lssBookmarkDB;

  private SalesBookmarkService _salesBookmarkService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesBookmarkService = new SalesBookmarkService(_lssBookmarkDB);
  }

  @Test
  public void testCreate_minimumFields() {
    Bookmark bookmark = generateBookmark();
    bookmark.removeCreatedAt();
    when(_lssBookmarkDB.getBySeatAndTypeAndContent(any(), any(), anyString()))
        .thenReturn(Task.value(Collections.emptyList()));
    when(_lssBookmarkDB.createBookmark(any(), anyLong(), any())).thenReturn(Task.value(1L));
    CreateResponse response = await(_salesBookmarkService.createBookmark(bookmark));
    assertThat(response.getId()).isEqualTo(1L);
    assertThat(response.getStatus()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreate_ableToCreateWhenContentIsNotYetBookmarked() {
    when(_lssBookmarkDB.getBySeatAndTypeAndContent(any(), any(), anyString()))
        .thenReturn(Task.value(Collections.emptyList()));
    when(_lssBookmarkDB.createBookmark(any(), anyLong(), any())).thenReturn(Task.value(1L));
    CreateResponse response = await(_salesBookmarkService.createBookmark(generateBookmark()));
    assertThat(response.getId()).isEqualTo(1L);
    assertThat(response.getStatus()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreate_noOpWhenContentIsAlreadyBookmarked() {
    when(_lssBookmarkDB.getBySeatAndTypeAndContent(any(), any(), anyString()))
        .thenReturn(Task.value(Collections.singletonList(Pair.of(3L, generateEspressoBookmark()))));
    when(_lssBookmarkDB.createBookmark(any(), anyLong(), any())).thenReturn(Task.value(1L));
    CreateResponse response = await(_salesBookmarkService.createBookmark(generateBookmark()));
    assertThat(response.getId()).isEqualTo(0L);
    assertThat(response.getStatus()).isEqualTo(HttpStatus.S_409_CONFLICT);
  }

  @Test
  public void testDeleteAlertSucceed() {
    when(_lssBookmarkDB.getBookmark(anyLong())).thenReturn(Task.value(generateEspressoBookmark()));
    when(_lssBookmarkDB.deleteBookmark(anyLong())).thenReturn(Task.value(HttpStatus.S_204_NO_CONTENT));
    HttpStatus status = await(_salesBookmarkService.deleteBookmark(BOOKMARK_ID, SEAT_URN));
    assertThat(status).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testFindBySeatAndType() {
    when(_lssBookmarkDB.getBySeatAndType(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(Collections.singletonList(new Pair(BOOKMARK_ID, generateEspressoBookmark()))));
    BasicCollectionResult<Bookmark> result =
        await(_salesBookmarkService.findBySeatAndType(SEAT_URN, BOOKMARK_TYPE, 0, 10));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getElements()).hasSize(1);
    assertThat(result.getElements().get(0).getId()).isEqualTo(BOOKMARK_ID);
    assertThat(result.getElements().get(0).getContent().getBody().isNotificationV2()).isTrue();
    assertThat(result.getElements().get(0).getContent().getBody().getNotificationV2()).isEqualTo(NOTIFICATION_V_2_URN);
  }

  @Test
  public void testFindBySeatAndType_errorInDao() {
    when(_lssBookmarkDB.getBySeatAndType(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.failure(new Throwable("error")));
    BasicCollectionResult<Bookmark> result =
        await(_salesBookmarkService.findBySeatAndType(SEAT_URN, BOOKMARK_TYPE, 0, 10));
    assertThat(result.getTotal()).isEqualTo(0);
    assertThat(result.getElements()).hasSize(0);
  }

  @Test
  public void testBatchFindByContents() {
    com.linkedin.sales.espresso.Bookmark espressoBookmark = generateEspressoBookmark();
    espressoBookmark.setContractUrn(null);
    when(_lssBookmarkDB.getBySeatAndTypeAndContent(any(), any(), anyString()))
        .thenReturn(Task.value(Collections.singletonList(new Pair(BOOKMARK_ID, espressoBookmark))));
    BookmarkContent.Body body = new BookmarkContent.Body();
    body.setNotificationV2(NOTIFICATION_V_2_URN);
    BookmarkContent bookmarkContent = new BookmarkContent().setBody(body);
    Map<BookmarkContent, CollectionResult<Bookmark, NoMetadata>> result = await(
        _salesBookmarkService.batchFindByContents(SEAT_URN, BOOKMARK_TYPE, new BookmarkContent[]{bookmarkContent}));
    assertThat(result.size()).isEqualTo(1);
    assertThat(result.containsKey(bookmarkContent)).isTrue();
    assertThat(result.get(bookmarkContent).getTotal()).isEqualTo(1);
    assertThat(result.get(bookmarkContent).getElements()).hasSize(1);
    assertThat(result.get(bookmarkContent).getElements().get(0).getContent()).isEqualTo(bookmarkContent);
    assertThat(result.get(bookmarkContent).getElements().get(0).getId()).isEqualTo(BOOKMARK_ID);
    assertThat(result.get(bookmarkContent).getElements().get(0).getContract())
        .isEqualTo(SalesBookmarkService.DUMMY_CONTRACT_URN);
  }

  @Test
  public void testBatchFindByContents_throwOnUnrecognizedContent() {
    BookmarkContent.Body body = new BookmarkContent.Body();
    BookmarkContent bookmarkContent = new BookmarkContent().setBody(body);
    try {
      _salesBookmarkService.batchFindByContents(SEAT_URN, BOOKMARK_TYPE, new BookmarkContent[]{bookmarkContent});
    } catch (IllegalArgumentException e) {
      assertThat(e.getMessage()).contains("Unsupported bookmark content");
    }
  }

  private com.linkedin.sales.espresso.Bookmark generateEspressoBookmark() {
    com.linkedin.sales.espresso.Bookmark bookmark = new com.linkedin.sales.espresso.Bookmark();
    bookmark.contentUrn = NOTIFICATION_V_2_URN.toString();
    bookmark.contractUrn = CONTRACT_URN.toString();
    bookmark.createdTime = EXPIRATION_TIME_MS;
    bookmark.type = com.linkedin.sales.espresso.BookmarkType.ALERT;
    bookmark.seatUrn = SEAT_URN.toString();
    return bookmark;
  }

  private Bookmark generateBookmark() {
    BookmarkContent.Body body = new BookmarkContent.Body();
    body.setNotificationV2(NOTIFICATION_V_2_URN);
    return new Bookmark()
        .setContent(new BookmarkContent().setBody(body))
        .setContract(CONTRACT_URN)
        .setCreatedAt(EXPIRATION_TIME_MS)
        .setExpiredAt(EXPIRATION_TIME_MS)
        .setType(BOOKMARK_TYPE)
        .setOwner(SEAT_URN);
  }
}
