package com.linkedin.sales.service.buyerengagement;

import com.linkedin.analytics.QueryResponse;
import com.linkedin.analytics.Row;
import com.linkedin.analytics.RowArray;
import com.linkedin.buyerengagement.BuyerEngagementDailyActivity;
import com.linkedin.buyerengagement.BuyerSegment;
import com.linkedin.common.Date;
import com.linkedin.common.urn.FunctionUrn;
import com.linkedin.common.urn.GeoUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeniorityUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.ibrik.client.AnalyticsQueryClient;
import com.linkedin.ibrik.client.exception.AnalyticsQueryClientException;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.PinotUtils;
import java.net.URISyntaxException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeoutException;
import org.joda.time.DateTime;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


public class BuyerEngagementServiceTest extends ServiceUnitTest {

  @Mock
  private AnalyticsQueryClient _analyticsQueryClient;

  private final ExecutorService _executorService = Executors.newSingleThreadExecutor();

  private BuyerEngagementService _buyerEngagementService;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    PinotUtils pinotUtils = new PinotUtils(_analyticsQueryClient, _executorService, 5);
    _buyerEngagementService = new BuyerEngagementService(pinotUtils, 100);
  }

  @Test(expectedExceptions = NullPointerException.class)
  public void testFindUniqueSegmentsWithException() {
    // invalid parameter, empty result
    PagingContext pagingContext = new PagingContext(0, 10);
    BasicCollectionResult<BuyerEngagementDailyActivity> invalidParameter =
        await(_buyerEngagementService.findByBuyerOrganization(null, null, 18375, 18377, pagingContext));
    Assert.assertTrue(invalidParameter.getElements().isEmpty());
  }

  @Test
  public void testFindUniqueSegmentsWithFailure()
      throws URISyntaxException, TimeoutException, AnalyticsQueryClientException {
    PagingContext pagingContext = new PagingContext(0, 10);
    OrganizationUrn actorOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 784658));
    OrganizationUrn targetOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 15782));
    when(_analyticsQueryClient.runPinotQueryWithTimeOut(any(), anyString(),anyLong(),any())).thenThrow(AnalyticsQueryClientException.class);
    try {
      BasicCollectionResult<BuyerEngagementDailyActivity> invalidParameter =
          await(_buyerEngagementService.findByBuyerOrganization(actorOrganizationUrn, targetOrganizationUrn, 18375, 18377, pagingContext));
    }catch (Exception e) {
      assertTrue(e.getCause().getCause() instanceof AnalyticsQueryClientException);
    }
  }

  @Test
  public void testFindUniqueSegmentsWithTimeOut()
      throws URISyntaxException, TimeoutException, AnalyticsQueryClientException {
    PagingContext pagingContext = new PagingContext(0, 10);
    OrganizationUrn actorOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 784658));
    OrganizationUrn targetOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 15782));
    when(_analyticsQueryClient.runPinotQueryWithTimeOut(any(), anyString(),anyLong(),any())).thenThrow(TimeoutException.class);
    try {
      BasicCollectionResult<BuyerEngagementDailyActivity> invalidParameter =
          await(_buyerEngagementService.findByBuyerOrganization(actorOrganizationUrn, targetOrganizationUrn, 18375, 18377, pagingContext));
    }catch (Exception e) {
      assertTrue(e.getCause().getCause() instanceof TimeoutException);
    }
  }

  @Test
  public void testFindUniqueSegments() throws URISyntaxException, TimeoutException, AnalyticsQueryClientException {

    PagingContext pagingContext = new PagingContext(0, 10);
    OrganizationUrn actorOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 784658));
    OrganizationUrn targetOrganizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple(OrganizationUrn.ENTITY_TYPE, 15782));

    // valid pinot query empty result
    QueryResponse response = new QueryResponse();
    response.setRows(new RowArray());
    when(_analyticsQueryClient.runPinotQueryWithTimeOut(any(), anyString(),anyLong(),any())).thenReturn(response);
    BasicCollectionResult<BuyerEngagementDailyActivity> emptySegments = await(
        _buyerEngagementService.findByBuyerOrganization(actorOrganizationUrn, targetOrganizationUrn, 18375, 18377,
            pagingContext));
    assertTrue(emptySegments.getElements().isEmpty());

    // positive case: set up input of statistics
    Row.Value actorSeniorityId = Row.Value.createWithInt(3);
    Row.Value actorFunctionId = Row.Value.createWithInt(2);
    Row.Value actorGeoId = Row.Value.createWithLong(100762591L);
    Row.Value daysSinceEpoch = Row.Value.createWithInt(18375);
    Row.Value engagementCount = Row.Value.createWithInt(83);

    Row.ValueArray valueArray =
        new Row.ValueArray(actorSeniorityId, actorFunctionId, actorGeoId, daysSinceEpoch,
            engagementCount);
    Row row = new Row();
    row.setValue(valueArray);
    RowArray rows = new RowArray();
    rows.add(row);
    response.setRows(rows);

    when(_analyticsQueryClient.runPinotQueryWithTimeOut(any(), anyString(),anyLong(),any())).thenReturn(response);
    BasicCollectionResult<BuyerEngagementDailyActivity> uniqueSegments = await(
        _buyerEngagementService.findByBuyerOrganization(actorOrganizationUrn, targetOrganizationUrn, 18375, 18377,
            pagingContext));

    assertEquals(uniqueSegments.getElements().size(), 1);
    for (BuyerEngagementDailyActivity buyerEngagementDailyActivity : uniqueSegments.getElements()) {
      BuyerSegment segment = buyerEngagementDailyActivity.getSegment();
      SeniorityUrn seniorityUrn = segment.getSeniority();
      FunctionUrn functionUrn = segment.getFunction();
      GeoUrn geoUrn = segment.getGeo();
      Date engagedOn = buyerEngagementDailyActivity.getEngagedOn();
      int activityCount = buyerEngagementDailyActivity.getActivityCount();
      assertEquals(seniorityUrn.getId(), "3");
      assertEquals(functionUrn.getId(), "2");
      assertEquals(geoUrn.getId(), "100762591");
      assertEquals(engagedOn.getDay(), new DateTime(18375L * 24 * 3600 * 1000).getDayOfMonth(), 0);
      assertEquals(engagedOn.getMonth(), new DateTime(18375L * 24 * 3600 * 1000).getMonthOfYear(), 0);
      assertEquals(engagedOn.getYear(), new DateTime(18375L * 24 * 3600 * 1000).getYear(), 0);
      assertEquals(activityCount, 83);
    }
  }
}
