package com.linkedin.sales.service.buyerengagement;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.buyerengagement.BuyerIntentLevel;
import com.linkedin.buyerengagement.BuyerIntentTrend;
import com.linkedin.buyerengagement.BuyerIntentTrendKey;
import com.linkedin.buyerengagement.FeatureCategory;
import com.linkedin.buyerengagement.FeatureCategoryArray;
import com.linkedin.buyerengagement.FeatureCategoryName;
import com.linkedin.buyerengagement.FeatureCategoryScoreLevel;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.lss.UrnUtils;
import com.linkedin.lssbuyer.schemas.venice.BuyerIntentTrendVeniceKey;
import com.linkedin.lssbuyer.schemas.venice.BuyerIntentTrendVeniceValue;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.restligateway.util.GatewayCallerIdentity;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.venice.client.store.AvroSpecificStoreClient;
import edu.emory.mathcs.backport.java.util.Collections;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class BuyerIntentServiceTest extends ServiceUnitTest {
  private static final long BUYER_ORG_1 = 1L;
  private static final long BUYER_ORG_2 = 2L;

  private static final long SELLER_ORG_1 = 11L;
  private static final long SELLER_ORG_2 = 22L;

  private static final int LEVEL_SUB = -1;
  private static final int LEVEL_BASE = 0;
  private static final int LEVEL_MED = 1;
  private static final int LEVEL_HI = 2;

  private static final String MODEL_ID = "binaryClassifier";
  private static final String LEVEL_VERSION = "ADVANTAGE_V0";
  private static final String RAW_FEATURE_CATEGORY_NAME_1 = FeatureCategoryName.ADS_ENGAGEMENT.toString();
  private static final String RAW_FEATURE_CATEGORY_NAME_2 = FeatureCategoryName.COMPANY_PAGE_ACTIVITY.toString();
  private static final String RAW_FEATURE_CATEGORY_NAME_3 = FeatureCategoryName.INMAIL_ACTIVITY.toString();
  private static final String RAW_FEATURE_CATEGORY_NAME_4 = FeatureCategoryName.POST_INTERACTION_ACTIVITY.toString();

  private static final ContractUrn sellerContract = new ContractUrn(1234L);
  private static final long MEMBER_ID = 123L;
  private static final MemberUrn memberUrn = new MemberUrn(MEMBER_ID);

  private static final com.linkedin.lssbuyer.schemas.venice.FeatureCategory VENICE_FEATURE_CATEGORY =
      com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder().setName(RAW_FEATURE_CATEGORY_NAME_1).setLevel(1).build();
  private static final FeatureCategory BUYER_INTENT_FEATURE_CATEGORY =
      new FeatureCategory().setName(FeatureCategoryName.ADS_ENGAGEMENT).setLevel(FeatureCategoryScoreLevel.HIGH);


  @Mock
  private AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> _veniceClient;
  @Mock
  private AvroSpecificStoreClient<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> _veniceClientForInteractionScores;
  @Mock
  private LixService _lixService;
  @Mock
  private GatewayCallerFinder _gatewayCallerFinder;

  private BuyerIntentService _buyerIntentService;

  @BeforeClass
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _buyerIntentService = new BuyerIntentService(_veniceClient, _veniceClientForInteractionScores, _lixService, _gatewayCallerFinder);
    when(_gatewayCallerFinder.getCaller()).thenReturn(new GatewayCallerIdentity(MEMBER_ID, null, null));
    _buyerIntentService.abTestEnabled = true;
  }

  @Test(description = "batchGetTrends calls the venice client with the correct parameters and properly decodes the response")
  public void testBatchGetTrends() {
    BuyerIntentTrendVeniceKey veniceKey1 = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceKey veniceKey2 = getBuyerIntentTrendVeniceKey(BUYER_ORG_2, SELLER_ORG_1);
    BuyerIntentTrendVeniceKey veniceKey3 = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_2);
    Set<BuyerIntentTrendVeniceKey> veniceKeys = ImmutableSet.of(veniceKey1, veniceKey2, veniceKey3);

    // This one should be dropped
    com.linkedin.lssbuyer.schemas.venice.FeatureCategory invalidNamedFeatureCategory =
        com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder().setName("foo").setLevel(1).build();
    // This one can still be mapped with default level even though having invalid level
    com.linkedin.lssbuyer.schemas.venice.FeatureCategory invalidLeveledFeatureCategory =
        com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
            .setName(RAW_FEATURE_CATEGORY_NAME_1).setLevel(99).build();
    Map<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> veniceResponse = ImmutableMap.of(
        veniceKey1, getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_BASE, null, null, 1111, Collections.singletonList(invalidNamedFeatureCategory)),
        veniceKey2, getBuyerIntentTrendVeniceValue(0.9f, 1.1f, LEVEL_HI, 0.1f, 0.2f, 1222, Collections.singletonList(VENICE_FEATURE_CATEGORY))
    );

    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST, memberUrn))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(veniceKeys))).thenReturn(CompletableFuture.completedFuture(veniceResponse));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey1 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());
    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey2 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_2, SELLER_ORG_1), new EmptyRecord());
    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey3 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_2), new EmptyRecord());

    Map<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>, BuyerIntentTrend> response = await(
        _buyerIntentService.batchGetTrends(ImmutableSet.of(trendKey1, trendKey2, trendKey3), sellerContract));

    assertThat(response).isEqualTo(ImmutableMap.of(
        trendKey1, getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.BASELINE, null, null, 1111, Collections.emptyList()),
        trendKey2, getBuyerIntentTrend(BUYER_ORG_2, SELLER_ORG_1, 0.9f, 1.1f, BuyerIntentLevel.HIGH, 0.1f, 0.2f, 1222, Collections.singletonList(BUYER_INTENT_FEATURE_CATEGORY))
    ));
  }

  @Test(description = "batchGetTrends calls the venice client for the new store with the correct parameters and properly decodes the response")
  public void testBatchGetTrendsFromNewStore() {
    BuyerIntentTrendVeniceKey veniceKey1 = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceKey veniceKey2 = getBuyerIntentTrendVeniceKey(BUYER_ORG_2, SELLER_ORG_1);
    BuyerIntentTrendVeniceKey veniceKey3 = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_2);
    Set<BuyerIntentTrendVeniceKey> veniceKeys = ImmutableSet.of(veniceKey1, veniceKey2, veniceKey3);

    // This one should be dropped
    com.linkedin.lssbuyer.schemas.venice.FeatureCategory invalidNamedFeatureCategory =
        com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder().setName("foo").setLevel(1).build();
    // This one can still be mapped with default level even though having invalid level
    com.linkedin.lssbuyer.schemas.venice.FeatureCategory invalidLeveledFeatureCategory =
        com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
            .setName(RAW_FEATURE_CATEGORY_NAME_1).setLevel(99).build();
    Map<BuyerIntentTrendVeniceKey, BuyerIntentTrendVeniceValue> veniceResponse = ImmutableMap.of(
        veniceKey1, getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_BASE, null, null, 1111, Collections.singletonList(invalidNamedFeatureCategory)),
        veniceKey2, getBuyerIntentTrendVeniceValue(0.9f, 1.1f, LEVEL_HI, 0.1f, 0.2f, 1222, Collections.singletonList(VENICE_FEATURE_CATEGORY))
    );

    when(_gatewayCallerFinder.getCaller()).thenReturn(null);
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(false));
    when(_veniceClientForInteractionScores.batchGet(eq(veniceKeys))).thenReturn(CompletableFuture.completedFuture(veniceResponse));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey1 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());
    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey2 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_2, SELLER_ORG_1), new EmptyRecord());
    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey3 = new ComplexResourceKey<>(
        getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_2), new EmptyRecord());

    Map<ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord>, BuyerIntentTrend> response = await(
        _buyerIntentService.batchGetTrends(ImmutableSet.of(trendKey1, trendKey2, trendKey3), sellerContract));

    assertThat(response).isEqualTo(ImmutableMap.of(
        trendKey1, getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.BASELINE, null, null, 1111, Collections.emptyList()),
        trendKey2, getBuyerIntentTrend(BUYER_ORG_2, SELLER_ORG_1, 0.9f, 1.1f, BuyerIntentLevel.HIGH, 0.1f, 0.2f, 1222, Collections.singletonList(BUYER_INTENT_FEATURE_CATEGORY))
    ));
  }

  @Test(description = "getTrend calls the venice client with the correct parameters")
  public void testGetTrend() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_HI, null, null, 1111, Collections.singletonList(VENICE_FEATURE_CATEGORY));

    when(_gatewayCallerFinder.getCaller()).thenReturn(new GatewayCallerIdentity(MEMBER_ID, null, null));
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST, memberUrn))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.HIGH, null, null, 1111, Collections.singletonList(BUYER_INTENT_FEATURE_CATEGORY)));
  }

  @Test(description = "getTrend calls the venice client for new store with the correct parameters")
  public void testGetTrendForNewStore() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_HI, null, null, 1111, Collections.singletonList(VENICE_FEATURE_CATEGORY));

    when(_gatewayCallerFinder.getCaller()).thenReturn(null);
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(false));
    when(_veniceClientForInteractionScores.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.HIGH, null, null, 1111, Collections.singletonList(BUYER_INTENT_FEATURE_CATEGORY)));
  }

  @Test(description = "HIGH intent only keep HIGH and VERY_HIGH feature categories")
  public void testGetTrendClearedFeatureCategoriesHighIntent() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_HI, null, null, 1111,
        ImmutableList.of(
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.ADS_ENGAGEMENT.name()).setLevel(-1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.COMPANY_PAGE_ACTIVITY.name()).setLevel(0).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.INMAIL_ACTIVITY.name()).setLevel(1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.CONNECTION_ACTIVITY.name()).setLevel(2).build()));

    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST, memberUrn))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.HIGH, null, null, 1111, ImmutableList.of(
        new FeatureCategory().setName(FeatureCategoryName.INMAIL_ACTIVITY).setLevel(FeatureCategoryScoreLevel.HIGH),
        new FeatureCategory().setName(FeatureCategoryName.CONNECTION_ACTIVITY)
            .setLevel(FeatureCategoryScoreLevel.VERY_HIGH)
    )));
  }

  @Test(description = "MEDIUM intent should only keep HIGH or VERY_HIGH feature categories")
  public void testGetTrendClearedFeatureCategoriesMediumIntent() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_MED, null, null, 1111,
        ImmutableList.of(
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.ADS_ENGAGEMENT.name()).setLevel(-1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.COMPANY_PAGE_ACTIVITY.name()).setLevel(0).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.INMAIL_ACTIVITY.name()).setLevel(1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.CONNECTION_ACTIVITY.name()).setLevel(2).build()));

    when(_gatewayCallerFinder.getCaller()).thenReturn(null);
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.MEDIUM, null, null, 1111, ImmutableList.of(
        new FeatureCategory().setName(FeatureCategoryName.INMAIL_ACTIVITY).setLevel(FeatureCategoryScoreLevel.HIGH),
        new FeatureCategory().setName(FeatureCategoryName.CONNECTION_ACTIVITY)
            .setLevel(FeatureCategoryScoreLevel.VERY_HIGH)
    )));
  }

  @Test(description = "BELOW_BASELINE intent should only keep LOW feature categories")
  public void testGetTrendClearedFeatureCategoriesBelowBaselineIntent() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_SUB, null, null, 1111,
        ImmutableList.of(
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.ADS_ENGAGEMENT.name()).setLevel(-1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.COMPANY_PAGE_ACTIVITY.name()).setLevel(0).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.INMAIL_ACTIVITY.name()).setLevel(1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.CONNECTION_ACTIVITY.name()).setLevel(2).build()));

    when(_gatewayCallerFinder.getCaller()).thenReturn(null);
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.BELOW_BASELINE, null, null, 1111, ImmutableList.of(
        new FeatureCategory().setName(FeatureCategoryName.ADS_ENGAGEMENT).setLevel(FeatureCategoryScoreLevel.LOW)
    )));
  }

  @Test(description = "BASELINE intent should be cleared")
  public void testGetTrendClearedFeatureCategoriesBaselineIntent() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);
    BuyerIntentTrendVeniceValue veniceValue = getBuyerIntentTrendVeniceValue(0.5f, 1.0f, LEVEL_BASE, null, null, 1111,
        ImmutableList.of(
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.ADS_ENGAGEMENT.name()).setLevel(-1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.COMPANY_PAGE_ACTIVITY.name()).setLevel(0).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.INMAIL_ACTIVITY.name()).setLevel(1).build(),
            com.linkedin.lssbuyer.schemas.venice.FeatureCategory.newBuilder()
                .setName(FeatureCategoryName.CONNECTION_ACTIVITY.name()).setLevel(2).build()));

    when(_gatewayCallerFinder.getCaller()).thenReturn(null);
    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of(veniceKey, veniceValue)));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    BuyerIntentTrend response = await(_buyerIntentService.getTrend(trendKey, sellerContract));

    assertThat(response).isEqualTo(getBuyerIntentTrend(BUYER_ORG_1, SELLER_ORG_1, 0.5f, 1.0f, BuyerIntentLevel.BASELINE, null, null, 1111, Collections.emptyList()));
  }

  @Test(description = "getTrend throws when there is no data", expectedExceptions = PromiseException.class)
  public void testGetTrendFailure() {
    BuyerIntentTrendVeniceKey veniceKey = getBuyerIntentTrendVeniceKey(BUYER_ORG_1, SELLER_ORG_1);

    when(_lixService.isContractBasedEEPLixEnabled(sellerContract, LixUtils.LSS_BUYER_INTEREST_DL_MODEL_TEST))
        .thenReturn(Task.value(true));
    when(_veniceClient.batchGet(eq(ImmutableSet.of(veniceKey)))).thenReturn(CompletableFuture.completedFuture(ImmutableMap.of()));

    ComplexResourceKey<BuyerIntentTrendKey, EmptyRecord> trendKey = new ComplexResourceKey<>(getBuyerIntentTrendKey(BUYER_ORG_1, SELLER_ORG_1), new EmptyRecord());

    await(_buyerIntentService.getTrend(trendKey, sellerContract));
  }

  @Test(description = "make sure the intent level deserialization logic is correct")
  public void testIntentLevelFromInt() {
    // we're just considering a "sub" level so lss-mt will be updated once we're certain it's useful. currently should be LOW
    assertThat(_buyerIntentService.intentLevelFromInt(LEVEL_SUB)).isEqualTo(BuyerIntentLevel.BELOW_BASELINE);
    assertThat(_buyerIntentService.intentLevelFromInt(LEVEL_BASE)).isEqualTo(BuyerIntentLevel.BASELINE);
    assertThat(_buyerIntentService.intentLevelFromInt(LEVEL_MED)).isEqualTo(BuyerIntentLevel.MEDIUM);
    assertThat(_buyerIntentService.intentLevelFromInt(LEVEL_HI)).isEqualTo(BuyerIntentLevel.HIGH);
  }



  private BuyerIntentTrend getBuyerIntentTrend(
      long buyerId, long sellerId, float score, float advantageScore, BuyerIntentLevel level, Float change7Day, Float change30Day, long calculatedAt, Collection<FeatureCategory> featureCategories) {
    return new BuyerIntentTrend()
        .setBuyer(UrnUtils.createOrganizationUrn(buyerId))
        .setSeller(UrnUtils.createOrganizationUrn(sellerId))
        .setScore(score)
        .setAdvantageScore(advantageScore)
        .setLevel(level)
        .setLevelVersion(LEVEL_VERSION)
        .setModelId(MODEL_ID)
        .setPercentScoreChange7Day(change7Day, SetMode.REMOVE_OPTIONAL_IF_NULL)
        .setPercentScoreChange30Day(change30Day, SetMode.REMOVE_OPTIONAL_IF_NULL)
        .setCalculatedAt(calculatedAt)
        .setFeatureCategories(new FeatureCategoryArray(featureCategories));
  }

  private BuyerIntentTrendKey getBuyerIntentTrendKey(long buyerId, long sellerId) {
    return new BuyerIntentTrendKey()
        .setBuyer(UrnUtils.createOrganizationUrn(buyerId))
        .setSeller(UrnUtils.createOrganizationUrn(sellerId));
  }

  private BuyerIntentTrendVeniceKey getBuyerIntentTrendVeniceKey(long buyerId, long sellerId) {
    return BuyerIntentTrendVeniceKey.newBuilder()
        .setBuyerId(buyerId)
        .setSellerId(sellerId)
        .build();
  }

  private BuyerIntentTrendVeniceValue getBuyerIntentTrendVeniceValue(
      float score, float advantageScore, int level, Float change7Day, Float change30Day, long calculatedAt,
      List<com.linkedin.lssbuyer.schemas.venice.FeatureCategory> featureCategories) {
    return BuyerIntentTrendVeniceValue.newBuilder()
        .setScore(score)
        .setAdvantageScore(advantageScore)
        .setLevel(level)
        .setLevelVersion(LEVEL_VERSION)
        .setModelId(MODEL_ID)
        .setPercentScoreChange7Day(change7Day)
        .setPercentScoreChange30Day(change30Day)
        .setCalculatedAt(calculatedAt)
        .setFeatureCategories(featureCategories)
        .build();
  }
}
