package com.linkedin.sales.service.buyerengagement;

import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.espresso.Product;
import com.linkedin.sales.service.TrackingService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import edu.emory.mathcs.backport.java.util.Collections;
import java.net.URISyntaxException;
import java.util.Optional;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import pegasus.com.linkedin.buyerengagement.ContractSellerIdentity;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class ContractSellerIdentityServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 123L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final SeatUrn SEAT_URN = new SeatUrn(12345L);
  private static final String PRODUCT_ID = "1234";
  private static final String PRODUCT_NAME = "test_product";
  private static final String PRODUCT_CATEGORY_NAME = "test_product_category";
  private static final String PRODUCT_DESCRIPTION = "test_description";
  private static final String SESSION_ID = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6c9";
  private static final StandardizedProductUrn PRODUCT_URN_1;
  private static final StandardizedProductCategoryUrn PRODUCT_CATEGORY_URN;
  static {
    try {
      PRODUCT_URN_1 = StandardizedProductUrn.deserialize("urn:li:standardizedProduct:12");
      PRODUCT_CATEGORY_URN = StandardizedProductCategoryUrn.deserialize("urn:li:standardizedProductCategory:34");
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @Test()
  public void testGetContractSellerIdentityWhenResourceNotFound() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.empty()));
    ContractSellerIdentity contractSellerIdentity = runAndWait(mocks._contractSellerIdentityService.getContractSellerIdentity(CONTRACT_ID));
    assertThat(contractSellerIdentity).isNull();
  }

  @Test
  public void testGetContractSellerIdentity() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.of(buildEspressoContractSellerIdentity())));
    ContractSellerIdentity actual = runAndWait(mocks._contractSellerIdentityService.getContractSellerIdentity(CONTRACT_ID));
    assertThat(actual).isEqualTo(buildApiContractSellerIdentity());
  }

  @Test
  public void testAddProductActionFailureCase() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN)).thenReturn(Task.value(Optional.empty()));
    when(mocks._lssBuyerDB.createOrUpdateContractSellerIdentity(eq(CONTRACT_URN), any()))
        .thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));
    ActionResult actual = runAndWait(mocks._contractSellerIdentityService.addProduct(CONTRACT_URN, SEAT_URN, new SellerIdentityProduct()
        .setProduct(SellerIdentityProduct.Product.createWithProductName(PRODUCT_NAME)), SESSION_ID));
    assertThat(actual.getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertThat(actual.getValue()).isNull();
    verify(mocks._trackingService).createAndSendOutProductCollectFunnelSaveProductTrackingEvent(eq(SEAT_URN), eq(SESSION_ID), any(), eq(true));
  }

  @DataProvider
  private Object[][] testAddProductHappyCaseDataProvider() {
    return new Object[][] {
        {
            Optional.of(buildEspressoContractSellerIdentityWithStandardizedProduct()),
            "When already other products exist for the contract"
        },
        {
            Optional.empty(),
            "When a first product is being added"
        }
    };
  }

  @Test(dataProvider = "testAddProductHappyCaseDataProvider")
  public void testAddProductHappyCase(Optional<com.linkedin.sales.espresso.ContractSellerIdentity> maybeContractSellerIdentity,
      String testDescription) {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(maybeContractSellerIdentity));
    when(mocks._lssBuyerDB.createOrUpdateContractSellerIdentity(eq(CONTRACT_URN), any()))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    SellerIdentityProduct product = buildSellerIdentityProduct();
    product.removeId();
    ActionResult actual = runAndWait(mocks._contractSellerIdentityService.addProduct(CONTRACT_URN, SEAT_URN, product, SESSION_ID));
    assertThat(actual.getStatus()).as(testDescription).isEqualTo(HttpStatus.S_200_OK);
    assertThat(actual.getValue()).isNotNull();
    verify(mocks._trackingService).createAndSendOutProductCollectFunnelSaveProductTrackingEvent(eq(SEAT_URN), eq(SESSION_ID), any(), eq(false));
  }

  @Test
  public void testUpdateProductWhenNoProductsForContract() {
    Mocks mocks = new Mocks();
    SellerIdentityProduct product = new SellerIdentityProduct()
        .setProduct(SellerIdentityProduct.Product.createWithProductName(PRODUCT_NAME));
    assertThat(runAndWait(mocks._contractSellerIdentityService.updateProduct(CONTRACT_URN, SEAT_URN, product)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
  }

  @Test()
  public void testUpdateProductWhenExistingProductsDoesNotExist() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.of(buildEspressoContractSellerIdentity())));
    SellerIdentityProduct product = new SellerIdentityProduct()
        .setId("123")
        .setProduct(SellerIdentityProduct.Product.createWithProductName(PRODUCT_NAME));
    assertThat(runAndWait(mocks._contractSellerIdentityService.updateProduct(CONTRACT_URN, SEAT_URN, product)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
  }

  @Test
  public void testUpdateProduct() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.of(buildEspressoContractSellerIdentity())));
    SellerIdentityProduct sellerIdentityProduct = buildSellerIdentityProduct()
        .setProduct(SellerIdentityProduct.Product.createWithProductName("New Name"));
    when(mocks._lssBuyerDB.createOrUpdateContractSellerIdentity(eq(CONTRACT_URN), any()))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    assertThat(runAndWait(mocks._contractSellerIdentityService.updateProduct(CONTRACT_URN, SEAT_URN, sellerIdentityProduct)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_200_OK));
  }

  @Test
  public void testRemoveProductWhenContractSellerIdentityIsMissing() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.empty()));
    assertThat(runAndWait(mocks._contractSellerIdentityService.removeProduct(CONTRACT_URN, PRODUCT_ID)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_404_NOT_FOUND));
  }

  @Test
  public void testConvertToEspressoProduct() {
    Mocks mocks = new Mocks();
    assertThat(mocks._contractSellerIdentityService.convertToEspressoProduct(buildSellerIdentityProduct()))
        .isEqualTo(buildEspressoContractSellerIdentity().getProducts().get(0));
  }

  @Test
  public void testRemoveProductWhenProductIsNotFound() {
    Mocks mocks = new Mocks();
    com.linkedin.sales.espresso.ContractSellerIdentity contractSellerIdentity = buildEspressoContractSellerIdentity();
    contractSellerIdentity.getProducts().get(0).setProductId("12345");
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.of(contractSellerIdentity)));
    assertThat(runAndWait(mocks._contractSellerIdentityService.removeProduct(CONTRACT_URN, PRODUCT_ID)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_412_PRECONDITION_FAILED));
  }

  @Test
  public void testRemoveProduct() {
    Mocks mocks = new Mocks();
    when(mocks._lssBuyerDB.getContractSellerIdentity(CONTRACT_URN))
        .thenReturn(Task.value(Optional.of(buildEspressoContractSellerIdentity())));
    com.linkedin.sales.espresso.ContractSellerIdentity updatedContractSellerIdentity = buildEspressoContractSellerIdentity();
    updatedContractSellerIdentity.setProducts(Collections.emptyList());
    when(mocks._lssBuyerDB.createOrUpdateContractSellerIdentity(CONTRACT_URN, updatedContractSellerIdentity))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    assertThat(runAndWait(mocks._contractSellerIdentityService.removeProduct(CONTRACT_URN, PRODUCT_ID)))
        .isEqualTo(new ActionResult<>(HttpStatus.S_200_OK));
  }

  private com.linkedin.sales.espresso.ContractSellerIdentity buildEspressoContractSellerIdentity() {
    com.linkedin.sales.espresso.ContractSellerIdentity sellerIdentity = new com.linkedin.sales.espresso.ContractSellerIdentity();
    Product product = new Product();
    product.setProductName(PRODUCT_NAME);
    product.setProductCategoryName(PRODUCT_CATEGORY_NAME);
    product.setProductId(PRODUCT_ID);
    product.setProductDescription(PRODUCT_DESCRIPTION);
    product.setCreatedBy("urn:li:seat:12");
    product.setLastModifiedBy("urn:li:seat:12");
    product.setCreatedTime(122L);
    product.setLastModifiedTime(1234L);
    sellerIdentity.setProducts(Collections.singletonList(product));
    return sellerIdentity;
  }

  private com.linkedin.sales.espresso.ContractSellerIdentity buildEspressoContractSellerIdentityWithStandardizedProduct() {
    com.linkedin.sales.espresso.ContractSellerIdentity sellerIdentity = new com.linkedin.sales.espresso.ContractSellerIdentity();
    Product product = new Product();
    product.setStandardizedProductUrn(PRODUCT_URN_1.toString());
    product.setStandardizedProductCategoryUrn(PRODUCT_CATEGORY_URN.toString());
    product.setProductId(PRODUCT_ID);
    product.setProductDescription(PRODUCT_DESCRIPTION);
    product.setCreatedBy("urn:li:seat:12");
    product.setLastModifiedBy("urn:li:seat:12");
    product.setCreatedTime(122L);
    product.setLastModifiedTime(1234L);
    sellerIdentity.setProducts(Collections.singletonList(product));
    return sellerIdentity;
  }

  private ContractSellerIdentity buildApiContractSellerIdentity() {
    return new ContractSellerIdentity()
        .setSellerIdentityProducts(new SellerIdentityProductArray(buildSellerIdentityProduct()))
        .setContractUrn(CONTRACT_URN);
  }

  private SellerIdentityProduct buildSellerIdentityProduct() {
    return new SellerIdentityProduct()
        .setId(PRODUCT_ID)
        .setProduct(SellerIdentityProduct.Product.createWithProductName(PRODUCT_NAME))
        .setProductCategory(SellerIdentityProduct.ProductCategory.createWithProductCategoryName(PRODUCT_CATEGORY_NAME))
        .setCreatedBy(new SeatUrn(12L))
        .setDescription(PRODUCT_DESCRIPTION)
        .setLastModifiedBy(new SeatUrn(12L))
        .setCreatedTime(122L)
        .setLastModifiedTime(1234L);
  }

  private static class Mocks {
    @Mock
    private LssBuyerDB _lssBuyerDB;
    @Mock
    private TrackingService _trackingService;
    private ContractSellerIdentityService _contractSellerIdentityService;

    Mocks() {
      MockitoAnnotations.openMocks(this);
      _contractSellerIdentityService = new ContractSellerIdentityService(_lssBuyerDB, _trackingService);
    }
  }
}
