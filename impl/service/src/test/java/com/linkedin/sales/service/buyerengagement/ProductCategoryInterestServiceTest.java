package com.linkedin.sales.service.buyerengagement;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.data.DataMap;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.buyerengagement.ProductCategoryInterest;
import com.linkedin.buyerengagement.ProductCategoryInterestKey;
import com.linkedin.espresso.common.util.Pair;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 * Unit tests for ProductCategoryInterestService
 */
public class ProductCategoryInterestServiceTest extends ServiceUnitTest {
  @Mock
  private LssBuyerDB _lssBuyerDB;

  private ProductCategoryInterestService _productCategoryInterestService;

  private static final ProductCategoryInterest interest1 = new ProductCategoryInterest();
  private static final ProductCategoryInterest interest2 = new ProductCategoryInterest();
  private static final com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest1
      = new com.linkedin.sales.espresso.ProductCategoryInterest();
  private static final com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest2
      = new com.linkedin.sales.espresso.ProductCategoryInterest();
  private static final ProductCategoryInterest invalidInterest = new ProductCategoryInterest();
  private static final com.linkedin.sales.espresso.ProductCategoryInterest invalidEspressoInterest
      = new com.linkedin.sales.espresso.ProductCategoryInterest();
  private static final EmptyRecord emptyRecord = new EmptyRecord();
  private static final Throwable throwable = new Throwable("Purposive failure for test");

  private static final String SEAT_URN_STR = "urn:li:seat:101";
  private static final Long INTEREST_ID_1 = 1001L;
  private static final Long INTEREST_ID_2 = 1002L;
  private static final Long INVALID_INTEREST_ID = 2000L;
  private static final String INVALID_STR = "invalid_string";
  private static final String PRODUCT_NAME_1 = "product_1";
  private static final String PRODUCT_NAME_2 = "product_2";
  private static final String INVALID_PRODUCT_URN_STR = "urn:li:standardizedProduct:-1";
  private static final String PRODUCT_URN_10_STR = "urn:li:standardizedProduct:10";
  private static final String PRODUCT_URL_STR = "product_url";
  private static final String CATEGORY_NAME_1 = "category_1";
  private static final String CATEGORY_NAME_2 = "category_2";
  private static final String INVALID_CATEGORY_URN_STR = "urn:li:standardizedProductCategory:-1";
  private static final String CATEGORY_URN_1_STR = "urn:li:standardizedProductCategory:1";

  private static final SeatUrn SEAT_URN;
  private static final StandardizedProductUrn INVALID_PRODUCT_URN;
  private static final StandardizedProductUrn PRODUCT_URN_10;
  private static final Url PRODUCT_URL = new Url(PRODUCT_URL_STR);
  private static final StandardizedProductCategoryUrn INVALID_CATEGORY_URN;
  private static final StandardizedProductCategoryUrn CATEGORY_URN_1;
  static {
    try {
      SEAT_URN = SeatUrn.deserialize(SEAT_URN_STR);
      INVALID_PRODUCT_URN = StandardizedProductUrn.deserialize(INVALID_PRODUCT_URN_STR);
      PRODUCT_URN_10 = StandardizedProductUrn.deserialize(PRODUCT_URN_10_STR);
      INVALID_CATEGORY_URN = StandardizedProductCategoryUrn.deserialize(INVALID_CATEGORY_URN_STR);
      CATEGORY_URN_1 = StandardizedProductCategoryUrn.deserialize(CATEGORY_URN_1_STR);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    _productCategoryInterestService = new ProductCategoryInterestService(_lssBuyerDB);
  }

  @Test
  public void testBatchCreateInterests() {
    prepareInterestTest();

    // test empty input case
    List<ProductCategoryInterest> emptyList = new ArrayList<>();
    List<CreateResponse> emptyResponses = await(_productCategoryInterestService.batchCreateInterests(emptyList));
    assertEquals(emptyResponses.size(), 0);

    // set up two positive cases and one negative case
    TimeIgnorantInterest timeIgnorantInterest1 = new TimeIgnorantInterest(espressoInterest1);
    TimeIgnorantInterest timeIgnorantInterest2 = new TimeIgnorantInterest(espressoInterest2);
    TimeIgnorantInterest invalidTimeIgnorantInterest = new TimeIgnorantInterest(invalidEspressoInterest);
    // the invalid create request will cause failure
    when(_lssBuyerDB.createProductCategoryInterest(eq(SEAT_URN), argThat(timeIgnorantInterest1)))
        .thenReturn(Task.value(INTEREST_ID_1));
    when(_lssBuyerDB.createProductCategoryInterest(eq(SEAT_URN), argThat(timeIgnorantInterest2)))
        .thenReturn(Task.value(INTEREST_ID_2));
    when(_lssBuyerDB.createProductCategoryInterest(eq(SEAT_URN), argThat(invalidTimeIgnorantInterest)))
        .thenReturn(Task.failure(throwable));

    List<ProductCategoryInterest> interestList = Arrays.asList(interest1, interest2, invalidInterest);
    List<CreateResponse> responses = await(_productCategoryInterestService.batchCreateInterests(interestList));

    // the two valid create request succeed; and the invalid one returns 500 error.
    assertEquals(responses.size(), 3);
    assertEquals(responses.get(0).getId(), INTEREST_ID_1);
    assertEquals(responses.get(0).getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(responses.get(1).getId(), INTEREST_ID_2);
    assertEquals(responses.get(1).getStatus(), HttpStatus.S_201_CREATED);
    assertNull(responses.get(2).getId());
    assertEquals(responses.get(2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testBatchPartialUpdateInterests() {
    prepareInterestTest();

    Map<ComplexResourceKey<ProductCategoryInterestKey, EmptyRecord >, PatchRequest<ProductCategoryInterest>> patchMap
        = new HashMap<>();
    // test empty input case
    Map<CompoundKey, UpdateResponse> emptyResponseMap =
        await(_productCategoryInterestService.batchPartialUpdateInterests(patchMap));
    assertEquals(emptyResponseMap.size(), 0);

    // create the patch map with two valid patches and one invalid patch
    ProductCategoryInterestKey key1 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_1);
    ProductCategoryInterestKey key2 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_2);
    ProductCategoryInterestKey invalidKey = new ProductCategoryInterestKey()
        .setSeat(SEAT_URN).setInterestId(INVALID_INTEREST_ID);
    PatchRequest<ProductCategoryInterest> patch1 = createInterestPatch(interest1);
    PatchRequest<ProductCategoryInterest> patch2 = createInterestPatch(interest2);
    PatchRequest<ProductCategoryInterest> invalidPatch = createInterestPatch(invalidInterest);
    patchMap.put(new ComplexResourceKey<>(key1, emptyRecord), patch1);
    patchMap.put(new ComplexResourceKey<>(key2, emptyRecord), patch2);
    patchMap.put(new ComplexResourceKey<>(invalidKey, emptyRecord), invalidPatch);

    // let the first 2 pass the get() and patch, return 200 and failure.
    // the invalid one cannot be found and return 404
    when(_lssBuyerDB.getProductCategoryInterest(SEAT_URN, INTEREST_ID_1)).thenReturn(Task.value(espressoInterest1));
    when(_lssBuyerDB.getProductCategoryInterest(SEAT_URN, INTEREST_ID_2)).thenReturn(Task.value(espressoInterest2));
    when(_lssBuyerDB.getProductCategoryInterest(SEAT_URN, INVALID_INTEREST_ID))
        .thenReturn(Task.failure(throwable));
    when(_lssBuyerDB.updateProductCategoryInterest(eq(SEAT_URN), eq(INTEREST_ID_1), any()))
        .thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssBuyerDB.updateProductCategoryInterest(eq(SEAT_URN), eq(INTEREST_ID_2), any()))
        .thenReturn(Task.failure(throwable));

    Map<CompoundKey, UpdateResponse> responseMap =
        await(_productCategoryInterestService.batchPartialUpdateInterests(patchMap));

    assertEquals(responseMap.size(), 3);
    UpdateResponse response1 = responseMap.get(_productCategoryInterestService.toCompoundKey(key1));
    assertEquals(response1.getStatus(), HttpStatus.S_200_OK);
    UpdateResponse response2 = responseMap.get(_productCategoryInterestService.toCompoundKey(key2));
    assertEquals(response2.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    UpdateResponse response3 = responseMap.get(_productCategoryInterestService.toCompoundKey(invalidKey));
    assertEquals(response3.getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testBatchDeleteInterests() {
    // test empty input case
    List<ProductCategoryInterestKey> emptyList = new ArrayList<>();
    Map<CompoundKey, UpdateResponse> emptyResponseMap =
        await(_productCategoryInterestService.batchDeleteInterests(emptyList));
    assertEquals(emptyResponseMap.size(), 0);

    // two valid keys and one invalid key
    ProductCategoryInterestKey key1 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_1);
    ProductCategoryInterestKey key2 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_2);
    ProductCategoryInterestKey invalidKey = new ProductCategoryInterestKey()
        .setSeat(SEAT_URN).setInterestId(INVALID_INTEREST_ID);

    // two valid keys return success; the invalid key returns 404 error.
    when(_lssBuyerDB.deleteProductCategoryInterest(SEAT_URN, INTEREST_ID_1))
        .thenReturn(Task.value(HttpStatus.S_204_NO_CONTENT));
    when(_lssBuyerDB.deleteProductCategoryInterest(SEAT_URN, INTEREST_ID_2))
        .thenReturn(Task.failure(throwable));
    when(_lssBuyerDB.deleteProductCategoryInterest(SEAT_URN, INVALID_INTEREST_ID))
        .thenReturn(Task.value(HttpStatus.S_404_NOT_FOUND));

    List<ProductCategoryInterestKey> keyList = Arrays.asList(key1, key2, invalidKey);
    Map<CompoundKey, UpdateResponse> responseMap = await(_productCategoryInterestService.batchDeleteInterests(keyList));

    assertEquals(responseMap.size(), 3);
    UpdateResponse response1 = responseMap.get(_productCategoryInterestService.toCompoundKey(key1));
    assertEquals(response1.getStatus(), HttpStatus.S_204_NO_CONTENT);
    UpdateResponse response2 = responseMap.get(_productCategoryInterestService.toCompoundKey(key2));
    assertEquals(response2.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    UpdateResponse response3 = responseMap.get(_productCategoryInterestService.toCompoundKey(invalidKey));
    assertEquals(response3.getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testFindInterests() {
    prepareInterestTest();

    // if no category name given, return both interests
    // if specify category 1, then only return interest 1
    Pair<Long, com.linkedin.sales.espresso.ProductCategoryInterest> pair1 = new Pair<>(INTEREST_ID_1, espressoInterest1);
    Pair<Long, com.linkedin.sales.espresso.ProductCategoryInterest> pair2 = new Pair<>(INTEREST_ID_2, espressoInterest2);
    when(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[]{}, 0, 10))
        .thenReturn(Task.value(Arrays.asList(pair1, pair2)));
    when(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[]{CATEGORY_NAME_1}, 0, 10))
        .thenReturn(Task.value(Arrays.asList(pair1)));

    // get both interests and sort by interest Id
    List<ProductCategoryInterest> interestList = await(_productCategoryInterestService
        .findInterests(SEAT_URN, new String[]{}, 0, 10));
    assertEquals(interestList.size(), 2);
    interestList.sort(Comparator.comparing(ProductCategoryInterest::getInterestId));
    // the input interest has no Id. to compare we set Id here
    interest1.setInterestId(INTEREST_ID_1);
    assertEquals(interestList.get(0), interest1);
    interest2.setInterestId(INTEREST_ID_2);
    assertEquals(interestList.get(1), interest2);

    // get interests for category 1 only
    interestList = await(_productCategoryInterestService.
        findInterests(SEAT_URN, new String[]{CATEGORY_NAME_1}, 0, 10));
    assertEquals(interestList.size(), 1);
    assertEquals(interestList.get(0), interest1);

    // test failure case
    when(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[]{INVALID_STR}, 0, 10))
        .thenReturn(Task.failure(throwable));
    interestList = await(_productCategoryInterestService.
        findInterests(SEAT_URN, new String[]{INVALID_STR}, 0, 10));
    assertEquals(interestList.size(), 0);
  }

  @Test
  public void testUtils() {
    ProductCategoryInterestKey key1 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_1);
    ProductCategoryInterestKey key2 = new ProductCategoryInterestKey().setSeat(SEAT_URN).setInterestId(INTEREST_ID_2);
    List<ProductCategoryInterestKey> keyList = Arrays.asList(key1, key2);

    CompoundKey compoundKey1 = _productCategoryInterestService.toCompoundKey(key1);
    assertEquals(compoundKey1.getPart("seat"), SEAT_URN);
    assertEquals(compoundKey1.getPart("interestId"), INTEREST_ID_1);
    CompoundKey compoundKey2 = _productCategoryInterestService.toCompoundKey(key2);
    assertEquals(compoundKey2.getPart("seat"), SEAT_URN);
    assertEquals(compoundKey2.getPart("interestId"), INTEREST_ID_2);

    Map<CompoundKey, UpdateResponse> responseMap = _productCategoryInterestService.getUpdateResponseMap(keyList,
        HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(responseMap.size(), 2);
    assertEquals(responseMap.get(compoundKey1).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(responseMap.get(compoundKey2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }


  private void prepareInterestTest() {
    long currentTime = System.currentTimeMillis();

    // set up two interest records
    interest1.setSeat(SEAT_URN);
    interest1.setProductName(PRODUCT_NAME_1);
    interest1.setStandardizedProduct(PRODUCT_URN_10);
    interest1.setProductUrl(PRODUCT_URL);
    interest1.setCategoryName(CATEGORY_NAME_1);
    interest1.setStandardizedProductCategory(CATEGORY_URN_1);
    interest2.setSeat(SEAT_URN);
    interest2.setProductName(PRODUCT_NAME_2);
    interest2.setCategoryName(CATEGORY_NAME_2);

    // set up two Espresso interest records
    espressoInterest1.setProductName(PRODUCT_NAME_1);
    espressoInterest1.setStandardizedProductUrn(PRODUCT_URN_10_STR);
    espressoInterest1.setProductUrl(PRODUCT_URL_STR);
    espressoInterest1.setCategoryName(CATEGORY_NAME_1);
    espressoInterest1.setStandardizedProductCategoryUrn(CATEGORY_URN_1_STR);
    espressoInterest1.setCreatedTime(currentTime);
    espressoInterest1.setLastModifiedTime(currentTime);
    espressoInterest2.setProductName(PRODUCT_NAME_2);
    espressoInterest2.setStandardizedProductUrn(INVALID_PRODUCT_URN_STR);
    espressoInterest2.setCategoryName(CATEGORY_NAME_2);
    espressoInterest2.setStandardizedProductCategoryUrn(INVALID_CATEGORY_URN_STR);
    espressoInterest2.setCreatedTime(currentTime);
    espressoInterest2.setLastModifiedTime(currentTime);

    // set up for invalid interest
    invalidInterest.setSeat(SEAT_URN);
    invalidInterest.setProductName(INVALID_STR);
    invalidInterest.setCategoryName(INVALID_STR);
    invalidEspressoInterest.setProductName(INVALID_STR);
    invalidEspressoInterest.setStandardizedProductUrn(INVALID_PRODUCT_URN_STR);
    invalidEspressoInterest.setCategoryName(INVALID_STR);
    invalidEspressoInterest.setStandardizedProductCategoryUrn(INVALID_CATEGORY_URN_STR);
  }

  private PatchRequest<ProductCategoryInterest> createInterestPatch(ProductCategoryInterest interest) {
    DataMap patchData = new DataMap();
    patchData.put("$set", interest.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  /**
   * This is a argument matcher class used in Mockito powered tests.
   * The espresso.ProductCategoryInterest class has two timestamp fields that are generated in real time.
   * This class is to help the Mockito.when() method to ignore these two fields.
   */
  class TimeIgnorantInterest implements ArgumentMatcher<com.linkedin.sales.espresso.ProductCategoryInterest> {

    public TimeIgnorantInterest(com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest) {
      productName = espressoInterest.getProductName().toString();
      productUrn = espressoInterest.getStandardizedProductUrn().toString();
      categoryName = espressoInterest.getCategoryName().toString();
      categoryUrn = espressoInterest.getStandardizedProductCategoryUrn().toString();
    }

    @Override
    public boolean matches(com.linkedin.sales.espresso.ProductCategoryInterest espressoInterest) {
      return espressoInterest.getProductName().equals(productName)
          && espressoInterest.getStandardizedProductUrn().equals(productUrn)
          && espressoInterest.getCategoryName().equals(categoryName)
          && espressoInterest.getStandardizedProductCategoryUrn().equals(categoryUrn);
    }

    String productName;
    String productUrn;
    String categoryName;
    String categoryUrn;
  }
}
