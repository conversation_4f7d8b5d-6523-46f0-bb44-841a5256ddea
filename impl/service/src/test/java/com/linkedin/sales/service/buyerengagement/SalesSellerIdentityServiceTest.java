package com.linkedin.sales.service.buyerengagement;

import com.google.common.collect.ImmutableList;
import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.buyerengagement.SellerIdentityPosition;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.buyerengagement.SellerIdentityService;
import com.linkedin.buyerengagement.SellerIdentityServiceArray;
import com.linkedin.buyerengagement.SellerIdentityTargetType;
import com.linkedin.common.OrganizationUrnArray;
import com.linkedin.common.SalesListUrnArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.data.DataMap;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.admin.ContractSetting;
import com.linkedin.sales.admin.ContractSettingType;
import com.linkedin.sales.client.common.SalesContractSettingsClient;
import com.linkedin.sales.ds.db.LssBuyerDB;
import com.linkedin.sales.espresso.TargetType;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSetting;
import pegasus.com.linkedin.buyerengagement.AutoProspectingSettingArray;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


public class SalesSellerIdentityServiceTest extends ServiceUnitTest {
  @Mock
  private LssBuyerDB _lssBuyerDB;
  @Mock
  SalesContractSettingsClient _salesContractSettingsClient;
  @Mock
  LixService _lixService;
  private SalesSellerIdentityService _salesSellerIdentityService;
  private static final SeatSellerIdentity identity1 = new SeatSellerIdentity();
  private static final SeatSellerIdentity identity2 = new SeatSellerIdentity();
  private static final SeatSellerIdentity identity3 = new SeatSellerIdentity();
  private static final SeatSellerIdentity identityWithDefaultProductId = new SeatSellerIdentity();
  private static final SeatSellerIdentity identityWithoutCurrentPosition = new SeatSellerIdentity();
  private static final SeatSellerIdentity identityWithEmptyProducts = new SeatSellerIdentity();
  private static final SeatSellerIdentity patchIdentity1 = new SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentity1 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentity2 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentity3 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentityWithoutCurrentPosition = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoIdentityWithDefaultProductId = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoPatchIdentity1 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity espressoExistingIdentity1 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity invalidEspressoIdentity1 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final com.linkedin.sales.espresso.SeatSellerIdentity invalidEspressoIdentity2 = new com.linkedin.sales.espresso.SeatSellerIdentity();
  private static final SeatSellerIdentity.CurrentPosition currentPosition1 = new SeatSellerIdentity.CurrentPosition();
  private static final SeatSellerIdentity.CurrentPosition currentPosition2 = new SeatSellerIdentity.CurrentPosition();
  private static final SellerIdentityPosition position1 = new SellerIdentityPosition();
  private static final com.linkedin.sales.espresso.SellerIdentityPosition espressoPosition1 = new com.linkedin.sales.espresso.SellerIdentityPosition();
  private static final com.linkedin.sales.espresso.SellerIdentityPosition espressoPosition2 = new com.linkedin.sales.espresso.SellerIdentityPosition();
  private static final com.linkedin.sales.espresso.SellerIdentityPosition invalidEspressoPosition1 = new com.linkedin.sales.espresso.SellerIdentityPosition();
  private static final SellerIdentityProduct product1 = new SellerIdentityProduct();
  private static final SellerIdentityProduct product2 = new SellerIdentityProduct();
  private static final com.linkedin.sales.espresso.SellerIdentityProduct espressoProduct1 = new com.linkedin.sales.espresso.SellerIdentityProduct();
  private static final com.linkedin.sales.espresso.SellerIdentityProduct espressoProduct2 = new com.linkedin.sales.espresso.SellerIdentityProduct();
  private static final com.linkedin.sales.espresso.SellerIdentityProduct invalidEspressoProduct1 = new com.linkedin.sales.espresso.SellerIdentityProduct();
  private static final SellerIdentityService service1 = new SellerIdentityService();
  private static final SellerIdentityService service2 = new SellerIdentityService();
  private static final com.linkedin.sales.espresso.SellerIdentityService espressoService1 = new com.linkedin.sales.espresso.SellerIdentityService();
  private static final com.linkedin.sales.espresso.SellerIdentityService espressoService2 = new com.linkedin.sales.espresso.SellerIdentityService();
  private static final SellerIdentityProduct.Product productUnion1 = new SellerIdentityProduct.Product();
  private static final SellerIdentityProduct.Product productUnion2 = new SellerIdentityProduct.Product();
  private static final SellerIdentityProduct.ProductCategory productCategoryUnion1 = new SellerIdentityProduct.ProductCategory();
  private static final AutoProspectingSetting autoProspectingSetting1 = new AutoProspectingSetting();
  private static final AutoProspectingSetting autoProspectingSetting2 = new AutoProspectingSetting();
  private static final com.linkedin.sales.espresso.AutoProspectingSetting espressoAutoProspectingSetting1 =
      new com.linkedin.sales.espresso.AutoProspectingSetting();
  private static final com.linkedin.sales.espresso.AutoProspectingSetting espressoAutoProspectingSetting2 =
      new com.linkedin.sales.espresso.AutoProspectingSetting();
  private static final Throwable throwable = new Throwable("Test failure");
  private static final String CONTRACT_URN_STR_1 = "urn:li:contract:9999";
  private static final String CONTRACT_URN_STR_2 = "urn:li:contract:8888";
  private static final String CONTRACT_URN_STR_3 = "urn:li:contract:7777";
  private static final String SEAT_URN_STR_1 = "urn:li:seat:9999";
  private static final String SEAT_URN_STR_2 = "urn:li:seat:8888";
  private static final String SEAT_URN_STR_3 = "urn:li:seat:7777";
  private static final String SEAT_URN_STR_4 = "urn:li:seat:6666";
  private static final String SEAT_URN_STR_5 = "urn:li:seat:5555";
  private static final String INVALID_STR = "INVALID";
  private static final Long POSITION_ID = 1234L;
  private static final String PRODUCT_NAME_1 = "The Best Product";
  private static final String ORG_URN_STR_1 = "urn:li:organization:1111";
  private static final String COMPANY_NAME_1 = "The Best Company";
  private static final String TITLE_1 = "CEO";
  private static final String PRODUCT_URN_STR_1 = "urn:li:standardizedProduct:2222";
  private static final String PRODUCT_URL_STR_1 = "https://thebestcompany.com/thebestproduct";
  private static final String PRODUCT_CATEGORY_1 = "The Best Category";
  private static final String SERVICE_NAME_1 = "The Best Service";
  private static final String SERVICE_NAME_2 = "The Second Best Service";
  private static final String SERVICE_URL_STR_1 = "https://thebestcompany.com/thebestservice";
  private static final Long PERSONA_ID_1 = 1234L;
  private static final String PRODUCT_ID_1 = "40cb9d2a-b2ac-4a5e-a8f6-07476f92d6c3";
  private static final String SALES_LIST_URN_STR_1 = "urn:li:salesList:168";
  private static final Long LAST_MODIFIED_TIME_1 = 1733270193000L;
  private static final Long ONBOARDING_COMPLETED_TIME_1 = 1733270193001L;
  private static final CharSequence DEFAULT_PRODUCT_ID = "defaultProductId";
  private static final Long CREATED_TIME = 100L;
  private static final Long LAST_MODIFIED_TIME = 200L;
  private static final TargetType ESPRESSO_ACCOUNT_TYPE = TargetType.ACCOUNT;
  private static final TargetType ESPRESSO_LEAD_TYPE = TargetType.LEAD;
  private static final SellerIdentityTargetType ACCOUNT_TYPE = SellerIdentityTargetType.ACCOUNT;
  private static final SellerIdentityTargetType LEAD_TYPE = SellerIdentityTargetType.LEAD;
  private static final ContractUrn CONTRACT_URN_1;
  private static final ContractUrn CONTRACT_URN_2;
  private static final ContractUrn CONTRACT_URN_3;
  private static final SeatUrn SEAT_URN_1;
  private static final SeatUrn SEAT_URN_2;
  private static final SeatUrn SEAT_URN_3;
  private static final SeatUrn SEAT_URN_4;
  private static final SeatUrn SEAT_URN_5;
  private static final OrganizationUrn ORG_URN_1;
  private static final StandardizedProductUrn PRODUCT_URN_1;
  private static final SalesListUrn SALES_LIST_URN_1;
  static {
    try {
      CONTRACT_URN_1 = ContractUrn.deserialize(CONTRACT_URN_STR_1);
      CONTRACT_URN_2 = ContractUrn.deserialize(CONTRACT_URN_STR_2);
      CONTRACT_URN_3 = ContractUrn.deserialize(CONTRACT_URN_STR_3);
      SEAT_URN_1 = SeatUrn.deserialize(SEAT_URN_STR_1);
      SEAT_URN_2 = SeatUrn.deserialize(SEAT_URN_STR_2);
      SEAT_URN_3 = SeatUrn.deserialize(SEAT_URN_STR_3);
      SEAT_URN_4 = SeatUrn.deserialize(SEAT_URN_STR_4);
      SEAT_URN_5 = SeatUrn.deserialize(SEAT_URN_STR_5);
      ORG_URN_1 = OrganizationUrn.deserialize(ORG_URN_STR_1);
      PRODUCT_URN_1 = StandardizedProductUrn.deserialize(PRODUCT_URN_STR_1);
      SALES_LIST_URN_1 = SalesListUrn.deserialize(SALES_LIST_URN_STR_1);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    when(_lixService.isContractBasedLixEnabled(any(ContractUrn.class), eq(LixUtils.LSS_SEAT_MANAGED_PRODUCTS_ENABLED))).thenReturn(Task.value(Boolean.FALSE));
    _salesSellerIdentityService = new SalesSellerIdentityService(_lssBuyerDB, _salesContractSettingsClient, _lixService);
    prepareDefaultIdentityForTest();
  }

  @Test
  public void testCreateSellerIdentity() {
    PatchRequest<SeatSellerIdentity> patch = createIdentityPatch(identity1);
    PatchRequest<SeatSellerIdentity> patch2 = createIdentityPatch(identity3);

    //success -> provide all fields
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(espressoIdentity1));
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, espressoIdentity1)).thenReturn(Task.value(HttpStatus.S_201_CREATED));
    UpdateResponse response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, patch));
    assertEquals(response.getStatus(), HttpStatus.S_201_CREATED);

    //success -> minimal required field
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_2, SEAT_URN_3)).thenReturn(Task.value(espressoIdentity3));
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_2, SEAT_URN_3, espressoIdentity3)).thenReturn(Task.value(HttpStatus.S_201_CREATED));
    response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_2, SEAT_URN_3, patch2));
    assertEquals(response.getStatus(), HttpStatus.S_201_CREATED);

    //fail -> record cannot be created
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_3, SEAT_URN_4)).thenReturn(Task.value(null));
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_3, SEAT_URN_4, espressoIdentity3)).thenReturn(Task.failure(throwable));
    response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_3, SEAT_URN_4, patch2));
    assertEquals(response.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testPartialUpdateSellerIdentity() {
    PatchRequest<SeatSellerIdentity> patch1 = createIdentityPatch(patchIdentity1);

    //success convert full opposing record
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(espressoExistingIdentity1));
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, espressoPatchIdentity1)).thenReturn(Task.value(HttpStatus.S_200_OK));
    UpdateResponse response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, patch1));
    assertEquals(response.getStatus(), HttpStatus.S_200_OK);

    //fail -> can be found but cannot be updated
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_2)).thenReturn(Task.value(espressoExistingIdentity1));
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_1, SEAT_URN_2, espressoPatchIdentity1)).thenReturn(Task.failure(throwable));
    response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_2, patch1));
    assertEquals(response.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);

    //fail -> cannot find
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_2, SEAT_URN_3)).thenReturn(Task.failure(throwable));
    response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_2, SEAT_URN_3, patch1));
    assertEquals(response.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testUpdateSellerIdentity() {
    //success update full opposing record
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, espressoIdentity1)).thenReturn(Task.value(HttpStatus.S_201_CREATED));
    UpdateResponse response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, identity1));
    assertEquals(response.getStatus(), HttpStatus.S_201_CREATED);

    //success update
    when(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN_1, SEAT_URN_2, espressoIdentity2)).thenReturn(Task.value(HttpStatus.S_200_OK));
    response = await(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_2, identity2));
    assertEquals(response.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testGetSellerIdentity() {
    //Success case 1 -> return full record -> populate every field
    //with current position -> position id
    //with SellerIdentityProduct 1 -> product name, SellerIdentityProduct 2 -> StandardizedProductUrn
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(espressoIdentity1));
    SeatSellerIdentity sellerIdentity = await(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1));
    assertEquals(sellerIdentity.getTargetType(), identity1.getTargetType());
    assertEquals(sellerIdentity.getCurrentPosition(), identity1.getCurrentPosition());
    assertEquals(sellerIdentity.getProducts(), identity1.getProducts());
    assertEquals(sellerIdentity.getServices(), identity1.getServices());

    //Success case 2 -> return partial record -> partial optional field empty
    //with current position -> SellerIdentityPosition
    //with SellerIdentityProduct -> StandardizedProductUrn
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_2)).thenReturn(Task.value(espressoIdentity2));
    sellerIdentity = await(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_2));
    assertEquals(sellerIdentity.getTargetType(), identity2.getTargetType());
    assertEquals(sellerIdentity.getCurrentPosition(), identity2.getCurrentPosition());
    assertEquals(sellerIdentity.getProducts(), identity2.getProducts());
    assertEquals(sellerIdentity.getServices(), identity2.getServices());

    //Success case 3 -> return minimal partial record -> all optional fields empty (P&S array empty)
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_2, SEAT_URN_3)).thenReturn(Task.value(espressoIdentity3));
    sellerIdentity = await(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_2, SEAT_URN_3));
    assertEquals(sellerIdentity.getTargetType(), identity3.getTargetType());
    assertEquals(sellerIdentity.getCurrentPosition(), identity3.getCurrentPosition());
    assertEquals(sellerIdentity.getProducts(), identity3.getProducts());
    assertEquals(sellerIdentity.getServices(), identity3.getServices());

    //Fail -> with CurrentPositionUnion -> position (SellerIdentityPosition) -> organization deserialize failed case
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_3, SEAT_URN_4)).thenReturn(Task.value(invalidEspressoIdentity1));
    RestLiServiceException exception = runAndWaitException(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_3, SEAT_URN_4),
        RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);

    //Fail -> with SellerIdentityProduct -> StandardizedProductUrn deserialize fail
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_3, SEAT_URN_5)).thenReturn(Task.value(invalidEspressoIdentity2));
    exception = runAndWaitException(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_3, SEAT_URN_5), RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);

    //Fail -> DB failure
    when(_lssBuyerDB.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_5)).thenReturn(Task.failure(throwable));
    exception = runAndWaitException(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_5), RestLiServiceException.class);
    assertEquals(exception.getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  private void testFindSellerIdentities() {
    when(_lssBuyerDB.findSellerIdentities(CONTRACT_URN_1, null, 0, 10)).thenReturn(Task.value(
        ImmutableList.of(
            new Pair<>(SEAT_URN_1, espressoIdentity1),
            new Pair<>(SEAT_URN_2, espressoIdentity2)
        )));
    List<SeatSellerIdentity> identities = await(_salesSellerIdentityService.findSellerIdentities(CONTRACT_URN_1, 0, 10));
    assertEquals(identities.get(0).getTargetType(), identity1.getTargetType());
    assertEquals(identities.get(0).getCurrentPosition(), identity1.getCurrentPosition());
    assertEquals(identities.get(0).getProducts(), identity1.getProducts());
    assertEquals(identities.get(0).getServices(), identity1.getServices());
    assertEquals(identities.get(1).getTargetType(), identity2.getTargetType());
    assertEquals(identities.get(1).getCurrentPosition(), identity2.getCurrentPosition());
    assertEquals(identities.get(1).getProducts(), identity2.getProducts());
    assertEquals(identities.get(1).getServices(), identity2.getServices());

    //fail recover with empty list
    when(_lssBuyerDB.findSellerIdentities(CONTRACT_URN_3, null,0, 10)).thenReturn(Task.failure(throwable));
    identities = await(_salesSellerIdentityService.findSellerIdentities(CONTRACT_URN_3, 0, 10));
    assertEquals(identities.size(), 0);
  }

  @DataProvider
  public static Object[][] testSeatManagedProductsDataProvider() {
    return new Object[][] {
        new Object[] { true, true, true},
        new Object[] { true, false, false},
        new Object[] { false, true, true},
        new Object[] { false, false, true}
    };
  }

  @Test(dataProvider = "testSeatManagedProductsDataProvider")
  public void testSeatManagedProducts(boolean isSeatManagedProductsLixEnabled, boolean isSeatManagedProductSettingEnabled, boolean isSeatManagedProductExpected) {
    when(_lixService.isContractBasedLixEnabled(CONTRACT_URN_1, LixUtils.LSS_SEAT_MANAGED_PRODUCTS_ENABLED)).thenReturn(Task.value(isSeatManagedProductsLixEnabled));
    when(_salesContractSettingsClient.get(CONTRACT_URN_1.getContractId(),
        ContractSettingType.SEAT_MANAGE_PRODUCTS_ENABLED)).thenReturn(Task.value(
        new ContractSetting().setType(ContractSettingType.SEAT_MANAGE_PRODUCTS_ENABLED)
            .setValue(ContractSetting.Value.createWithBoolean(isSeatManagedProductSettingEnabled))));
    Boolean isSeatManagedProduct = await(_salesSellerIdentityService.isSeatManagedProductsEnabled(CONTRACT_URN_1));
    assertEquals(isSeatManagedProduct, isSeatManagedProductExpected);
  }

  @DataProvider
  public static Object[][] testConvertToApiSellerIdentityDataProvider() {

    return new Object[][] {
        new Object[] { espressoIdentityWithDefaultProductId, true, identityWithDefaultProductId, "complete seller identity"},
        new Object[] { espressoIdentity1, true, identity1, "seller identity without default product id"},
        new Object[] { espressoIdentityWithoutCurrentPosition, true, identityWithoutCurrentPosition, "seller identity without current porsition"},
        new Object[] { espressoIdentityWithoutCurrentPosition, false, identityWithEmptyProducts, "seller identity with empty products and shouldSetSeatManagedProducts false"},
    };
  }

  @Test(dataProvider = "testConvertToApiSellerIdentityDataProvider")
  public void testConvertToApiSellerIdentity(com.linkedin.sales.espresso.SeatSellerIdentity espressoSellerIdentity,
      Boolean shouldSetSeatManagedProducts, SeatSellerIdentity expectedSellerIdentity, String description) {
    expectedSellerIdentity.setContract(CONTRACT_URN_1);
    expectedSellerIdentity.setSeat(SEAT_URN_1);
    SeatSellerIdentity sellerIdentity =
        _salesSellerIdentityService.convertToApiSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, espressoSellerIdentity,
            shouldSetSeatManagedProducts);
    assertEquals(sellerIdentity, expectedSellerIdentity, description);
  }

  private void prepareDefaultIdentityForTest() {
    espressoPosition1.setPositionId(POSITION_ID);
    espressoPosition2.setOrganizationUrn(ORG_URN_STR_1);
    espressoPosition2.setCompanyName(COMPANY_NAME_1);
    espressoPosition2.setTitle(TITLE_1);

    espressoProduct1.setProductName(PRODUCT_NAME_1);
    espressoProduct1.setProductUrl(PRODUCT_URL_STR_1);
    espressoProduct1.setProductCategoryName(PRODUCT_CATEGORY_1);
    espressoProduct1.setCreatedTime(CREATED_TIME);
    espressoProduct1.setLastModifiedTime(LAST_MODIFIED_TIME);
    espressoProduct2.setStandardizedProductUrn(PRODUCT_URN_STR_1);
    espressoProduct2.setCreatedTime(CREATED_TIME);
    espressoProduct2.setLastModifiedTime(LAST_MODIFIED_TIME);

    espressoService1.setServiceName(SERVICE_NAME_1);
    espressoService1.setServiceUrl(SERVICE_URL_STR_1);
    espressoService2.setServiceName(SERVICE_NAME_2);

    espressoAutoProspectingSetting1.setPersonaId(PERSONA_ID_1);
    espressoAutoProspectingSetting1.setProductId(PRODUCT_ID_1);
    espressoAutoProspectingSetting1.setAccountListUrns(ImmutableList.of(SALES_LIST_URN_STR_1));
    espressoAutoProspectingSetting1.setAccountUrns(ImmutableList.of(ORG_URN_STR_1));
    espressoAutoProspectingSetting1.setLastModifiedTime(LAST_MODIFIED_TIME_1);
    espressoAutoProspectingSetting1.setOnboardingCompletedTime(ONBOARDING_COMPLETED_TIME_1);

    espressoAutoProspectingSetting2.setPersonaId(PERSONA_ID_1);
    espressoAutoProspectingSetting2.setProductId(PRODUCT_ID_1);
    espressoAutoProspectingSetting2.setAccountListUrns(ImmutableList.of(SALES_LIST_URN_STR_1));
    espressoAutoProspectingSetting2.setAccountUrns(ImmutableList.of(ORG_URN_STR_1));
    espressoAutoProspectingSetting2.setLastModifiedTime(LAST_MODIFIED_TIME_1);

    position1.setCompanyUrn(ORG_URN_1);
    position1.setCompanyName(COMPANY_NAME_1);
    position1.setTitle(TITLE_1);
    currentPosition1.setPositionId(POSITION_ID);
    currentPosition2.setPosition(position1);

    productUnion1.setProductName(PRODUCT_NAME_1);
    product1.setProduct(productUnion1);
    product1.setProductUrl(new Url(PRODUCT_URL_STR_1));
    productCategoryUnion1.setProductCategoryName(PRODUCT_CATEGORY_1);
    product1.setProductCategory(productCategoryUnion1);
    product1.setCreatedBy(SEAT_URN_1);
    product1.setLastModifiedBy(SEAT_URN_1);
    product1.setCreatedTime(CREATED_TIME);
    product1.setLastModifiedTime(LAST_MODIFIED_TIME);
    productUnion2.setStandardizedProduct(PRODUCT_URN_1);
    product2.setProduct(productUnion2);
    product2.setCreatedBy(SEAT_URN_1);
    product2.setLastModifiedBy(SEAT_URN_1);
    product2.setCreatedTime(CREATED_TIME);
    product2.setLastModifiedTime(LAST_MODIFIED_TIME);

    service1.setServiceName(SERVICE_NAME_1);
    service1.setServiceUrl(new Url(SERVICE_URL_STR_1));
    service2.setServiceName(SERVICE_NAME_2);

    autoProspectingSetting1.setPersonaId(PERSONA_ID_1);
    autoProspectingSetting1.setProductId(PRODUCT_ID_1);
    autoProspectingSetting1.setAccountUrns(new OrganizationUrnArray(ORG_URN_1));
    autoProspectingSetting1.setAccountListUrns(new SalesListUrnArray(SALES_LIST_URN_1));
    autoProspectingSetting1.setLastModifiedTime(LAST_MODIFIED_TIME_1);
    autoProspectingSetting1.setOnboardingCompletedTime(ONBOARDING_COMPLETED_TIME_1);

    autoProspectingSetting2.setPersonaId(PERSONA_ID_1);
    autoProspectingSetting2.setProductId(PRODUCT_ID_1);
    autoProspectingSetting2.setAccountUrns(new OrganizationUrnArray(ORG_URN_1));
    autoProspectingSetting2.setAccountListUrns(new SalesListUrnArray(SALES_LIST_URN_1));
    autoProspectingSetting2.setLastModifiedTime(LAST_MODIFIED_TIME_1);

    //full espresso record
    espressoIdentity1.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    espressoIdentity1.setCurrentPosition(espressoPosition1);
    espressoIdentity1.setProducts(ImmutableList.of(espressoProduct1, espressoProduct2));
    espressoIdentity1.setServices(ImmutableList.of(espressoService1));
    espressoIdentity1.setAutoProspectingSettings(ImmutableList.of(espressoAutoProspectingSetting1));

    //partial espresso record with position
    espressoIdentity2.setTargetType(ESPRESSO_LEAD_TYPE);
    espressoIdentity2.setCurrentPosition(espressoPosition2);
    espressoIdentity2.setProducts(Collections.emptyList());
    espressoIdentity2.setServices(Collections.emptyList());

    //partial espresso record with required field only
    espressoIdentity3.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    espressoIdentity3.setProducts(Collections.emptyList());
    espressoIdentity3.setServices(Collections.emptyList());

    //complete espresso record with defaultproductid and full AP fields
    espressoIdentityWithDefaultProductId.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    espressoIdentityWithDefaultProductId.setCurrentPosition(espressoPosition1);
    espressoIdentityWithDefaultProductId.setProducts(ImmutableList.of(espressoProduct1, espressoProduct2));
    espressoIdentityWithDefaultProductId.setServices(ImmutableList.of(espressoService1));
    espressoIdentityWithDefaultProductId.setDefaultProductId(DEFAULT_PRODUCT_ID);
    espressoIdentityWithDefaultProductId.setAutoProspectingSettings(ImmutableList.of(espressoAutoProspectingSetting2));

    // espresso record without current position
    espressoIdentityWithoutCurrentPosition.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    espressoIdentityWithoutCurrentPosition.setProducts(ImmutableList.of(espressoProduct1, espressoProduct2));
    espressoIdentityWithoutCurrentPosition.setServices(ImmutableList.of(espressoService1));

    //full api record
    identity1.setTargetType(ACCOUNT_TYPE);
    identity1.setCurrentPosition(currentPosition1);
    identity1.setProducts(new SellerIdentityProductArray(product1, product2));
    identity1.setServices(new SellerIdentityServiceArray(service1));
    identity1.setAutoProspectingSettings(new AutoProspectingSettingArray(autoProspectingSetting1));

    //partial api record with position
    identity2.setTargetType(LEAD_TYPE);
    identity2.setCurrentPosition(currentPosition2);

    //partial api record with required field only
    identity3.setTargetType(ACCOUNT_TYPE);

    //complete api record with default product id
    identityWithDefaultProductId.setTargetType(ACCOUNT_TYPE);
    identityWithDefaultProductId.setCurrentPosition(currentPosition1);
    identityWithDefaultProductId.setProducts(new SellerIdentityProductArray(product1, product2));
    identityWithDefaultProductId.setServices(new SellerIdentityServiceArray(service1));
    identityWithDefaultProductId.setDefaultProductId(DEFAULT_PRODUCT_ID.toString());
    identityWithDefaultProductId.setDefaultProductId(DEFAULT_PRODUCT_ID.toString());
    identityWithDefaultProductId.setAutoProspectingSettings(new AutoProspectingSettingArray(autoProspectingSetting2));

    // api record without current position
    identityWithoutCurrentPosition.setTargetType(ACCOUNT_TYPE);
    identityWithoutCurrentPosition.setProducts(new SellerIdentityProductArray(product1, product2));
    identityWithoutCurrentPosition.setServices(new SellerIdentityServiceArray(service1));

    // api record with empty products
    identityWithEmptyProducts.setTargetType(ACCOUNT_TYPE);
    identityWithEmptyProducts.setProducts(new SellerIdentityProductArray());
    identityWithEmptyProducts.setServices(new SellerIdentityServiceArray(service1));

    //before patch espresso identity
    espressoExistingIdentity1.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    espressoExistingIdentity1.setCurrentPosition(espressoPosition1);
    espressoExistingIdentity1.setProducts(ImmutableList.of(espressoProduct1));
    espressoExistingIdentity1.setServices(ImmutableList.of(espressoService1));

    //after patch espresso identity
    espressoPatchIdentity1.setTargetType(ESPRESSO_LEAD_TYPE);
    espressoPatchIdentity1.setCurrentPosition(espressoPosition2);
    espressoPatchIdentity1.setProducts(ImmutableList.of(espressoProduct2));
    espressoPatchIdentity1.setServices(ImmutableList.of(espressoService2));

    //patch api record
    patchIdentity1.setTargetType(LEAD_TYPE);
    patchIdentity1.setCurrentPosition(currentPosition2);
    patchIdentity1.setProducts(new SellerIdentityProductArray(product2));
    patchIdentity1.setServices(new SellerIdentityServiceArray(service2));

    //invalid espresso identity 1
    invalidEspressoPosition1.setOrganizationUrn(INVALID_STR);
    invalidEspressoIdentity1.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    invalidEspressoIdentity1.setCurrentPosition(invalidEspressoPosition1);
    invalidEspressoIdentity1.setProducts(Collections.emptyList());
    invalidEspressoIdentity1.setServices(Collections.emptyList());

    //invalid espresso identity 2
    invalidEspressoProduct1.setProductUrn(INVALID_STR);
    invalidEspressoIdentity2.setTargetType(ESPRESSO_ACCOUNT_TYPE);
    invalidEspressoIdentity2.setProducts(ImmutableList.of(invalidEspressoProduct1));
    invalidEspressoIdentity2.setServices(Collections.emptyList());
  }

  private PatchRequest<SeatSellerIdentity> createIdentityPatch(SeatSellerIdentity identity) {
    DataMap patchData = new DataMap();
    patchData.put("$set", identity.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }
}
