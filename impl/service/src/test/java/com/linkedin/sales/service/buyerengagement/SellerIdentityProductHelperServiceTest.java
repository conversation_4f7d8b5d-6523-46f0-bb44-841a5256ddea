package com.linkedin.sales.service.buyerengagement;

import com.linkedin.buyerengagement.SeatSellerIdentity;
import com.linkedin.buyerengagement.SellerIdentityPosition;
import com.linkedin.buyerengagement.SellerIdentityProduct;
import com.linkedin.buyerengagement.SellerIdentityProductArray;
import com.linkedin.buyerengagement.SellerIdentityTargetType;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.TrackingService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import java.net.URISyntaxException;
import java.util.UUID;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import pegasus.com.linkedin.buyerengagement.ContractSellerIdentity;

import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

public class SellerIdentityProductHelperServiceTest extends ServiceUnitTest {
  @Mock
  private SalesSellerIdentityService _salesSellerIdentityService;
  @Mock
  private TrackingService _trackingService;
  @Mock
  private LixService _lixService;
  @Mock
  private ContractSellerIdentityService _contractSellerIdentityService;
  private SellerIdentityProductHelperService _sellerIdentityProductHelperService;

  private static final SeatSellerIdentity identity1 = new SeatSellerIdentity();
  private static final ContractSellerIdentity contractSellerIdentity = new ContractSellerIdentity();
  private static final SeatSellerIdentity.CurrentPosition currentPosition1 = new SeatSellerIdentity.CurrentPosition();
  private static final SeatSellerIdentity.CurrentPosition currentPosition2 = new SeatSellerIdentity.CurrentPosition();
  private static final SellerIdentityPosition position1 = new SellerIdentityPosition();
  private static final String PRODUCT_ID_1 = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6b7";
  private static final String PRODUCT_ID_2 = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6b8";
  private static final String PRODUCT_ID_3 = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6b9";
  private static final SellerIdentityProduct product1 = new SellerIdentityProduct();
  private static final SellerIdentityProduct product2 = new SellerIdentityProduct();
  private static final SellerIdentityProduct product3 = new SellerIdentityProduct();
  private static final SellerIdentityProduct.Product productUnion1 = new SellerIdentityProduct.Product();
  private static final SellerIdentityProduct.Product productUnion2 = new SellerIdentityProduct.Product();
  private static final SellerIdentityProduct.Product productUnion3 = new SellerIdentityProduct.Product();
  private static final SellerIdentityProduct.ProductCategory productCategoryUnion1 = new SellerIdentityProduct.ProductCategory();
  private static final String SESSION_ID = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6c9";
  private static final String CONTRACT_URN_STR_1 = "urn:li:contract:9999";
  private static final String SEAT_URN_STR_1 = "urn:li:seat:9999";
  private static final Long POSITION_ID = 1234L;
  private static final String PRODUCT_NAME_1 = "The Best Product";
  private static final String ORG_URN_STR_1 = "urn:li:organization:1111";
  private static final String COMPANY_NAME_1 = "The Best Company";
  private static final String TITLE_1 = "CEO";
  private static final String PRODUCT_URN_STR_1 = "urn:li:standardizedProduct:2222";
  private static final String PRODUCT_URL_STR_1 = "https://thebestcompany.com/thebestproduct";
  private static final String PRODUCT_DESCRIPTION = "The Best Description";
  private static final String PRODUCT_CATEGORY_1 = "The Best Category";
  private static final SellerIdentityTargetType ACCOUNT_TYPE = SellerIdentityTargetType.ACCOUNT;
  private static final ContractUrn CONTRACT_URN_1;
  private static final SeatUrn SEAT_URN_1;
  private static final OrganizationUrn ORG_URN_1;
  private static final StandardizedProductUrn PRODUCT_URN_1;
  static {
    try {
      CONTRACT_URN_1 = ContractUrn.deserialize(CONTRACT_URN_STR_1);
      SEAT_URN_1 = SeatUrn.deserialize(SEAT_URN_STR_1);
      ORG_URN_1 = OrganizationUrn.deserialize(ORG_URN_STR_1);
      PRODUCT_URN_1 = StandardizedProductUrn.deserialize(PRODUCT_URN_STR_1);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    when(_salesSellerIdentityService.isSeatManagedProductsEnabled(CONTRACT_URN_1)).thenReturn(Task.value(Boolean.TRUE));
    when(_lixService.isContractBasedEEPLixEnabled(CONTRACT_URN_1, LixUtils.LSS_ADMIN_PRODUCT_COLLECTION)).thenReturn(Task.value(Boolean.TRUE));
    _sellerIdentityProductHelperService = new SellerIdentityProductHelperService(_salesSellerIdentityService, _trackingService,
        _lixService, _contractSellerIdentityService);
    prepareDefaultIdentityForTest();
  }

  @Test
  public void testProductCRUD() {
    UUID uuid1 = UUID.fromString(PRODUCT_ID_1);
    UUID uuid2 = UUID.fromString(PRODUCT_ID_2);

    try (MockedStatic<UUID> mockedUUID = Mockito.mockStatic(UUID.class)) {
      when(_contractSellerIdentityService.getContractSellerIdentity(CONTRACT_URN_1.getIdAsLong()))
          .thenReturn(Task.value(null));
      //Add Product1
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(identity1));
      mockedUUID.when(UUID::randomUUID).thenReturn(uuid1);
      SeatSellerIdentity updatedIdentity1 = new SeatSellerIdentity(identity1.data());
      SellerIdentityProduct product1WithId = new SellerIdentityProduct(product1.data()).setId(PRODUCT_ID_1);
      updatedIdentity1.getProducts().add(product1WithId);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity1)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_201_CREATED)));
      ActionResult<String> response =
          await(_sellerIdentityProductHelperService.addProduct(CONTRACT_URN_1, SEAT_URN_1, product1, SESSION_ID));
      assertEquals(response.getStatus(), HttpStatus.S_201_CREATED);
      assertNotNull(response.getValue());
      assertEquals(product1.getCreatedBy(), SEAT_URN_1);
      assertEquals(product1.getLastModifiedBy(), SEAT_URN_1);

      //Add Product2
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity1));
      SeatSellerIdentity updatedIdentity2 = new SeatSellerIdentity(updatedIdentity1.data());
      when(UUID.randomUUID()).thenReturn(uuid2);
      SellerIdentityProduct product2WithId = new SellerIdentityProduct(product2.data()).setId(PRODUCT_ID_2);
      updatedIdentity2.getProducts().add(product2WithId);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity2)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_201_CREATED)));
      ActionResult<String> response2 =
          await(_sellerIdentityProductHelperService.addProduct(CONTRACT_URN_1, SEAT_URN_1, product2, SESSION_ID));
      assertEquals(response2.getStatus(), HttpStatus.S_201_CREATED);
      assertNotNull(response2.getValue());

      //Update Product1
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity2));
      SellerIdentityProduct updatedProduct1 =
          new SellerIdentityProduct(product1WithId.data()).setDescription(PRODUCT_DESCRIPTION);
      SeatSellerIdentity updatedIdentity3 = new SeatSellerIdentity(updatedIdentity2.data());
      updatedIdentity3.getProducts().set(0, updatedProduct1);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity3)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> response3 =
          await(_sellerIdentityProductHelperService.updateProduct(CONTRACT_URN_1, SEAT_URN_1, updatedProduct1));
      assertEquals(response3.getStatus(), HttpStatus.S_200_OK);
      assertEquals(product1.getCreatedBy(), SEAT_URN_1);
      assertEquals(product1.getLastModifiedBy(), SEAT_URN_1);

      //Update Product1 as default product
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity3));
      SeatSellerIdentity updatedIdentity4 =
          new SeatSellerIdentity(updatedIdentity3.data()).setDefaultProductId(response.getValue());
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity4)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> response4 = await(
          _sellerIdentityProductHelperService.updateDefaultProduct(CONTRACT_URN_1, SEAT_URN_1, response.getValue()));
      assertEquals(response4.getStatus(), HttpStatus.S_200_OK);

      //Remove Product2
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity4));
      SeatSellerIdentity updatedIdentity5 = new SeatSellerIdentity(updatedIdentity4.data());
      updatedIdentity5.getProducts().remove(1);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity5)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> response5 =
          await(_sellerIdentityProductHelperService.removeProduct(CONTRACT_URN_1, SEAT_URN_1, response2.getValue()));
      assertEquals(response5.getStatus(), HttpStatus.S_200_OK);

      //Update admin product as default product
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1))
          .thenReturn(Task.value(updatedIdentity5));
      when(_contractSellerIdentityService.getContractSellerIdentity(CONTRACT_URN_1.getIdAsLong()))
          .thenReturn(Task.value(contractSellerIdentity));
      SeatSellerIdentity updatedIdentity6 =
          new SeatSellerIdentity(updatedIdentity5.data()).setDefaultProductId(PRODUCT_ID_3);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity6))
          .thenReturn(Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> response6 = await(_sellerIdentityProductHelperService.updateDefaultProduct(CONTRACT_URN_1, SEAT_URN_1, PRODUCT_ID_3));
      assertEquals(response6.getStatus(), HttpStatus.S_200_OK);
    }
  }

  @Test
  public void testUpdateDefaultOnlyAdminProducts() {
    SeatSellerIdentity sellerIdentity = new SeatSellerIdentity().setSeat(SEAT_URN_1)
        .setContract(CONTRACT_URN_1)
        .setTargetType(SellerIdentityTargetType.ACCOUNT)
        .setProducts(new SellerIdentityProductArray())
        .setDefaultProductId(PRODUCT_ID_3);
    when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(null));
    when(_contractSellerIdentityService.getContractSellerIdentity(CONTRACT_URN_1.getIdAsLong()))
        .thenReturn(Task.value(contractSellerIdentity));
    when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, sellerIdentity))
        .thenReturn(Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
    ActionResult<Void> response = await(_sellerIdentityProductHelperService.updateDefaultProduct(CONTRACT_URN_1, SEAT_URN_1, PRODUCT_ID_1));
    assertEquals(response.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testUpdateDefaultAdminProductWhenLixDisabled() {
    when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(identity1));
    when(_lixService.isContractBasedEEPLixEnabled(CONTRACT_URN_1, LixUtils.LSS_ADMIN_PRODUCT_COLLECTION)).thenReturn(Task.value(Boolean.FALSE));
    ActionResult<Void> response = await(_sellerIdentityProductHelperService.updateDefaultProduct(CONTRACT_URN_1, SEAT_URN_1, PRODUCT_ID_1));
    assertEquals(response.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
    verify(_contractSellerIdentityService, never()).getContractSellerIdentity(CONTRACT_URN_1.getIdAsLong());
  }

  @Test
  public void testProductCRUDWithSeatManagedProductsDisabled() {

    UUID uuid1 = UUID.fromString(PRODUCT_ID_1);
    UUID uuid2 = UUID.fromString(PRODUCT_ID_2);
    when(_salesSellerIdentityService.isSeatManagedProductsEnabled(CONTRACT_URN_1)).thenReturn(Task.value(Boolean.FALSE));
    try (MockedStatic<UUID> mockedUUID = Mockito.mockStatic(UUID.class)) {
      //Add Product1
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(identity1));
      mockedUUID.when(UUID::randomUUID).thenReturn(uuid1);
      SeatSellerIdentity updatedIdentity1 = new SeatSellerIdentity(identity1.data());
      SellerIdentityProduct product1WithId = new SellerIdentityProduct(product1.data()).setId(PRODUCT_ID_1);
      updatedIdentity1.getProducts().add(product1WithId);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity1)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_201_CREATED)));
      ActionResult<String> addResponse =
          await(_sellerIdentityProductHelperService.addProduct(CONTRACT_URN_1, SEAT_URN_1, product1, SESSION_ID));

      assertEquals(addResponse.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
      assertNull(addResponse.getValue());
      verify(_salesSellerIdentityService, never()).getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1);
      verify(_salesSellerIdentityService, never()).updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, identity1);

      // Update Product1
      SeatSellerIdentity updatedIdentity2 = new SeatSellerIdentity(updatedIdentity1.data());
      when(UUID.randomUUID()).thenReturn(uuid2);
      SellerIdentityProduct product2WithId = new SellerIdentityProduct(product2.data()).setId(PRODUCT_ID_2);
      updatedIdentity2.getProducts().add(product2WithId);
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity2));
      SellerIdentityProduct updatedProduct1 =
          new SellerIdentityProduct(product1WithId.data()).setDescription(PRODUCT_DESCRIPTION);
      SeatSellerIdentity updatedIdentity3 = new SeatSellerIdentity(updatedIdentity2.data());
      updatedIdentity3.getProducts().set(0, updatedProduct1);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity3)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> updateResponse =
          await(_sellerIdentityProductHelperService.updateProduct(CONTRACT_URN_1, SEAT_URN_1, updatedProduct1));
      assertEquals(updateResponse.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
      verify(_salesSellerIdentityService, never()).getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1);
      verify(_salesSellerIdentityService, never()).updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity2);

      //Remove Product1
      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(
          Task.value(updatedIdentity3));
      SeatSellerIdentity updatedIdentity5 = new SeatSellerIdentity(updatedIdentity3.data());
      updatedIdentity5.getProducts().remove(1);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity5)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_200_OK)));
      ActionResult<Void> removeResponse =
          await(_sellerIdentityProductHelperService.removeProduct(CONTRACT_URN_1, SEAT_URN_1, PRODUCT_ID_2));
      assertEquals(removeResponse.getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
      verify(_salesSellerIdentityService, never()).getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1);
      verify(_salesSellerIdentityService, never()).updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, updatedIdentity3);

    }
  }

  @Test
  public void testAddProductWithEmptyIdentity() {
    try (MockedStatic<UUID> mockedUUID = Mockito.mockStatic(UUID.class)){
      UUID uuid1 = UUID.fromString(PRODUCT_ID_1);
      mockedUUID.when(UUID::randomUUID).thenReturn(uuid1);
      //Add Product1

      when(_salesSellerIdentityService.getSellerIdentity(CONTRACT_URN_1, SEAT_URN_1)).thenReturn(Task.value(null));
      SellerIdentityProduct product1WithId = new SellerIdentityProduct(product1.data()).setId(PRODUCT_ID_1);
      SellerIdentityProductArray products = new SellerIdentityProductArray();
      products.add(product1WithId);
      SeatSellerIdentity newIdentity = new SeatSellerIdentity().setSeat(SEAT_URN_1).setContract(CONTRACT_URN_1)
          .setTargetType(SellerIdentityTargetType.ACCOUNT).setProducts(products);
      when(_salesSellerIdentityService.updateSellerIdentity(CONTRACT_URN_1, SEAT_URN_1, newIdentity)).thenReturn(
          Task.value(new UpdateResponse(HttpStatus.S_201_CREATED)));
      ActionResult<String> response = await(_sellerIdentityProductHelperService.addProduct(CONTRACT_URN_1, SEAT_URN_1, product1, SESSION_ID));
      assertEquals(response.getStatus(), HttpStatus.S_201_CREATED);
      assertNotNull(response.getValue());
    }
  }

  private void prepareDefaultIdentityForTest() {
    position1.setCompanyUrn(ORG_URN_1);
    position1.setCompanyName(COMPANY_NAME_1);
    position1.setTitle(TITLE_1);
    currentPosition1.setPositionId(POSITION_ID);
    currentPosition2.setPosition(position1);

    productUnion1.setProductName(PRODUCT_NAME_1);
    product1.setProduct(productUnion1);
    product1.setProductUrl(new Url(PRODUCT_URL_STR_1));
    productCategoryUnion1.setProductCategoryName(PRODUCT_CATEGORY_1);
    product1.setProductCategory(productCategoryUnion1);

    productUnion2.setStandardizedProduct(PRODUCT_URN_1);
    product2.setProduct(productUnion2);

    //full api record
    identity1.setTargetType(ACCOUNT_TYPE);
    identity1.setCurrentPosition(currentPosition1);
    identity1.setProducts(new SellerIdentityProductArray());

    product3.setProduct(productUnion3).setId(PRODUCT_ID_3);
    contractSellerIdentity.setSellerIdentityProducts(new SellerIdentityProductArray(product3));
  }
}
