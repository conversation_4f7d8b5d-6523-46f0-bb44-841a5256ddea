package com.linkedin.sales.service.coach;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssCoachDB;
import com.linkedin.sales.espresso.AuthorType;
import com.linkedin.sales.espresso.CoachConversationHistory;
import com.linkedin.sales.espresso.Metadata;
import com.linkedin.sales.espresso.UseCaseType;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salescoach.ChatHistoryMetadata;
import com.linkedin.salescoach.ChatMessage;
import com.linkedin.salescoach.ChatMessageArray;
import com.linkedin.salescoach.ChatMessageAuthorType;
import com.linkedin.salescoach.SalesAIChatHistory;
import com.linkedin.salescoach.SalesAIChatUseCase;
import java.util.Arrays;
import java.util.Optional;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


/**
 * Unit test for SalesCoachConversationService
 */
public class SalesCoachConversationServiceTest extends ServiceUnitTest {

  private static final SeatUrn SEAT_URN1 = new SeatUrn(2001L);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(2002L);
  private static final String SESSION_ID_1 = "2001";
  private static final String SESSION_ID_2 = "2002";

  private static final String SUMMARIZED_CONTENT_1 = "SUMMARIZED_CONTENT_1";
  private static final String SUMMARIZED_CONTENT_2 = "SUMMARIZED_CONTENT_2";

  private static final String SUMMARIZED_INTENT_1 = "SUMMARIZED_INTENT_1";
  private static final String SUMMARIZED_INTENT_2 = "SUMMARIZED_INTENT_2";

  private static final String CHAT_MESSAGE_1 = "Hi, find me CEOs in SF?";
  private static final String CHAT_MESSAGE_2 = "Sure, here are 300 CEOs in SF.";

  private static final Long CREATED_TIME_1 = 1000L;
  private static final Long CREATED_TIME_2 = 2000L;

  private static final Long UPDATED_TIME_1 = 1001L;
  private static final Long UPDATED_TIME_2 = 2001L;

  private SalesCoachConversationService _salesCoachConversationService;

  @Mock
  private LssCoachDB _lssCoachDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesCoachConversationService = new SalesCoachConversationService(_lssCoachDB);
  }

  @Test
  public void testCreateCoachConversationHistorySucceed() {
    doReturn(Task.value(Optional.empty())).when(_lssCoachDB).getCoachConversationHistory(any(), any());
    doReturn(Task.value(Boolean.TRUE)).when(_lssCoachDB).upsertCoachConversationHistory(any(), any(), any());
    ChatMessage chatMessage1 = createChatMessage(CHAT_MESSAGE_1, ChatMessageAuthorType.MEMBER, CREATED_TIME_1);
    ChatMessage chatMessage2 = createChatMessage(CHAT_MESSAGE_2, ChatMessageAuthorType.SYSTEM, CREATED_TIME_2);
    ChatMessage[] chatMessages = createChatHistories(chatMessage1, chatMessage2);
    ChatHistoryMetadata chatHistoryMetadata = createChatHistoryMetadata(SalesAIChatUseCase.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1);

    Boolean isSuccessful = await(_salesCoachConversationService.addSalesAIChatMessages(SEAT_URN1, SESSION_ID_1, chatMessages, chatHistoryMetadata));
    Assert.assertTrue(isSuccessful);
  }

  @Test
  public void testCreateCoachConversationHistoryFailed() {
    doReturn(Task.value(Optional.empty())).when(_lssCoachDB).getCoachConversationHistory(any(), any());
    doReturn(Task.value(Boolean.FALSE)).when(_lssCoachDB).upsertCoachConversationHistory(any(), any(), any());
    ChatMessage chatMessage1 = createChatMessage(CHAT_MESSAGE_1, ChatMessageAuthorType.MEMBER, CREATED_TIME_1);
    ChatMessage chatMessage2 = createChatMessage(CHAT_MESSAGE_2, ChatMessageAuthorType.SYSTEM, CREATED_TIME_2);
    ChatMessage[] chatMessages = createChatHistories(chatMessage1, chatMessage2);
    ChatHistoryMetadata chatHistoryMetadata = createChatHistoryMetadata(SalesAIChatUseCase.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1);

    Boolean isSuccessful = await(_salesCoachConversationService.addSalesAIChatMessages(SEAT_URN1, SESSION_ID_1, chatMessages, chatHistoryMetadata));
    Assert.assertFalse(isSuccessful);
  }

  @Test
  public void testGetCoachConversationHistory() {

    CoachConversationHistory coachConversationHistory = new CoachConversationHistory();
    com.linkedin.sales.espresso.ChatMessage espressoChatMessage1 = createEspressoChatMessage(CHAT_MESSAGE_1, AuthorType.MEMBER, CREATED_TIME_1);
    com.linkedin.sales.espresso.ChatMessage espressoChatMessage2 = createEspressoChatMessage(CHAT_MESSAGE_2, AuthorType.SYSTEM, CREATED_TIME_2);

    coachConversationHistory.setChatContent(Arrays.asList(espressoChatMessage1, espressoChatMessage2));
    coachConversationHistory.setMetadata(createEspressoChatHistoryMetadata(UseCaseType.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1));
    coachConversationHistory.setUpdatedTime(UPDATED_TIME_1);

    doReturn(Task.value(Optional.of(coachConversationHistory))).when(_lssCoachDB).getCoachConversationHistory(any(), any());

    ChatMessage chatMessage1 = createChatMessage(CHAT_MESSAGE_1, ChatMessageAuthorType.MEMBER, CREATED_TIME_1);
    ChatMessage chatMessage2 = createChatMessage(CHAT_MESSAGE_2, ChatMessageAuthorType.SYSTEM, CREATED_TIME_2);
    ChatMessage[] chatMessages = createChatHistories(chatMessage1, chatMessage2);
    ChatHistoryMetadata chatHistoryMetadata = createChatHistoryMetadata(SalesAIChatUseCase.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1);

    SalesAIChatHistory result = await(_salesCoachConversationService.get(SEAT_URN1, SESSION_ID_1));
    Assert.assertEquals(result.getChatContent(), new ChatMessageArray(Arrays.asList(chatMessages)));
    Assert.assertEquals(result.getMetaData(), chatHistoryMetadata);
    Assert.assertEquals(result.getUpdatedTime(), UPDATED_TIME_1);
  }

  @Test
  public void testGetKCoachConversationHistory() {

    CoachConversationHistory coachConversationHistory = new CoachConversationHistory();
    com.linkedin.sales.espresso.ChatMessage espressoChatMessage1 = createEspressoChatMessage(CHAT_MESSAGE_1, AuthorType.MEMBER, CREATED_TIME_1);
    com.linkedin.sales.espresso.ChatMessage espressoChatMessage2 = createEspressoChatMessage(CHAT_MESSAGE_2, AuthorType.SYSTEM, CREATED_TIME_2);

    coachConversationHistory.setChatContent(Arrays.asList(espressoChatMessage1, espressoChatMessage2));
    coachConversationHistory.setMetadata(createEspressoChatHistoryMetadata(UseCaseType.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1));
    coachConversationHistory.setUpdatedTime(UPDATED_TIME_2);

    doReturn(Task.value(Optional.of(coachConversationHistory))).when(_lssCoachDB).getCoachConversationHistory(any(), any());

    ChatMessage chatMessage2 = createChatMessage(CHAT_MESSAGE_2, ChatMessageAuthorType.SYSTEM, CREATED_TIME_2);
    ChatHistoryMetadata chatHistoryMetadata = createChatHistoryMetadata(SalesAIChatUseCase.ACCOUNT_SEARCH, SUMMARIZED_INTENT_1, SUMMARIZED_CONTENT_1);

    SalesAIChatHistory result = await(_salesCoachConversationService.getLastKMessages(SEAT_URN2, SESSION_ID_2, 1));
    Assert.assertEquals(result.getChatContent().size(), 1);
    Assert.assertEquals(result.getChatContent().get(0), chatMessage2);
    Assert.assertEquals(result.getMetaData(), chatHistoryMetadata);
    Assert.assertEquals(result.getUpdatedTime(), UPDATED_TIME_2);
  }

  private Metadata createEspressoChatHistoryMetadata(UseCaseType useCaseType, String summarizedIntent, String summarizedContent) {
    Metadata metadata = new Metadata();
    metadata.setUseCase(useCaseType);
    metadata.setSummarizedIntent(summarizedIntent);
    metadata.setSummarizedContent(summarizedContent);
    return metadata;
  }

  private com.linkedin.sales.espresso.ChatMessage createEspressoChatMessage(String chatMessage, AuthorType member, Long createdTime) {
    com.linkedin.sales.espresso.ChatMessage chatMessageResult = new com.linkedin.sales.espresso.ChatMessage();
    chatMessageResult.setAuthor(member);
    chatMessageResult.setCreatedTime(createdTime);
    chatMessageResult.setMessage(chatMessage);
    return chatMessageResult;
  }

  private ChatHistoryMetadata createChatHistoryMetadata(SalesAIChatUseCase useCase, String summarizedIntent, String summarizedContent) {
    return new ChatHistoryMetadata()
        .setUseCase(useCase)
        .setSummarizedIntent(summarizedIntent)
        .setSummarizedContent(summarizedContent);
  }

  private ChatMessage[] createChatHistories(ChatMessage chatMessage1, ChatMessage chatMessage2) {
    return new ChatMessage[] { chatMessage1, chatMessage2 };
  }

  private ChatMessage createChatMessage(String message, ChatMessageAuthorType authorType, Long createTime) {
    return new ChatMessage()
        .setMessage(message)
        .setAuthor(authorType)
        .setCreatedTime(createTime);
  }

}
