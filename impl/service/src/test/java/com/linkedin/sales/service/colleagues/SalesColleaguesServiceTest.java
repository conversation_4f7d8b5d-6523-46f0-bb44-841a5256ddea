package com.linkedin.sales.service.colleagues;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.sales.ds.db.LssColleaguesDB;
import com.linkedin.sales.service.SalesColleaguesService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salescolleagues.ColleagueRelationship;
import com.linkedin.salescolleagues.RelationshipType;
import com.linkedin.salescolleagues.State;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * Created by aewong
 * Test class for {@link SalesColleaguesService}
 */
public class SalesColleaguesServiceTest extends ServiceUnitTest {

  private static final long FROM_MEMBER_ID = 1001L;
  private static final long FROM_MEMBER_ID_2 = 1002L;
  private static final long TO_MEMBER_ID = 1011L;
  private static final long TO_MEMBER_ID_2 = 1012L;
  private static final long CREATOR_SEAT_ID = 1200L;
  private static final long CONTRACT_ID = 1100L;
  private static final SeatUrn CREATOR_SEAT_URN = new SeatUrn(CREATOR_SEAT_ID);

  private static final int START = 0;
  private static final int COUNT_ONE = 1;
  private static final int COUNT_TEN = 10;

  private LssColleaguesDB _lssColleaguesDB;

  private SalesColleaguesService _salesColleaguesService;

  @BeforeMethod
  public void setup() {
    _lssColleaguesDB = Mockito.mock(LssColleaguesDB.class);
    _salesColleaguesService = new SalesColleaguesService(_lssColleaguesDB);
  }

  @Test
  public void testAddManagerSucceed() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(null));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(true);
  }

  @Test
  public void testAddManagerFailWhenSameToMemberId() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(TO_MEMBER_ID));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(false);
  }

  @Test
  public void testAddManagerFailWhenDifferentToMemberId() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(TO_MEMBER_ID_2));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(false);
  }

  @Test
  public void testRemoveManagerFailWhenNothingAdded() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(null));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.removeManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(false);
  }

  @Test
  public void testRemoveManagerSucceedWhenSameToMemberId() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(TO_MEMBER_ID));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.removeManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(true);
  }

  @Test
  public void testRemoveManagerFailWhenDifferentToMemberId() {
    when(_lssColleaguesDB.loadToMemberIdForLatestAdded(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()))
        .thenReturn(Task.value(TO_MEMBER_ID_2));
    when(_lssColleaguesDB.addColleagueRelationshipEntry(eq(FROM_MEMBER_ID), eq(CONTRACT_ID), eq(TO_MEMBER_ID),
        eq(RelationshipType.REPORTS_TO.name()), any())).thenReturn(Task.value(null));

    Boolean result = await(_salesColleaguesService.removeManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID, CREATOR_SEAT_URN));
    assertThat(result).isEqualTo(false);
  }

  @Test
  public void testFindByFromMemberAdded() {
    com.linkedin.sales.espresso.ColleagueRelationship colleagueRelationship = new com.linkedin.sales.espresso.ColleagueRelationship();
    colleagueRelationship.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationship.state = com.linkedin.sales.espresso.State.ADDED;
    colleagueRelationship.updatedTime = System.currentTimeMillis();
    when(_lssColleaguesDB.getColleagueRelationships(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), START, COUNT_ONE))
        .thenReturn(Task.value(Collections.singletonList(
            new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID), colleagueRelationship))));

    ColleagueRelationship result =
        await(_salesColleaguesService.findByFromMember(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO, START, COUNT_ONE)).get(0);

    assertThat(result.getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID);
    assertThat(result.getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.getState()).isEqualTo(State.ADDED);
  }

  @Test
  public void testFindByFromMemberRemoved() {
    com.linkedin.sales.espresso.ColleagueRelationship colleagueRelationship = new com.linkedin.sales.espresso.ColleagueRelationship();
    colleagueRelationship.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationship.state = com.linkedin.sales.espresso.State.REMOVED;
    colleagueRelationship.updatedTime = System.currentTimeMillis();
    when(_lssColleaguesDB.getColleagueRelationships(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), START, COUNT_ONE))
        .thenReturn(Task.value(Collections.singletonList(
            new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID), colleagueRelationship))));

    ColleagueRelationship result =
        await(_salesColleaguesService.findByFromMember(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO, START, COUNT_ONE)).get(0);

    assertThat(result.getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID);
    assertThat(result.getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.getState()).isEqualTo(State.REMOVED);
  }

  @Test
  public void testFindHistoryByFromMemberAdded() {
    com.linkedin.sales.espresso.ColleagueRelationshipHistory colleagueRelationshipHistory1 = new com.linkedin.sales.espresso.ColleagueRelationshipHistory();
    com.linkedin.sales.espresso.ColleagueRelationshipHistory colleagueRelationshipHistory2 = new com.linkedin.sales.espresso.ColleagueRelationshipHistory();
    com.linkedin.sales.espresso.ColleagueRelationshipHistory colleagueRelationshipHistory3 = new com.linkedin.sales.espresso.ColleagueRelationshipHistory();
    com.linkedin.sales.espresso.ColleagueRelationshipHistory colleagueRelationshipHistory4 = new com.linkedin.sales.espresso.ColleagueRelationshipHistory();

    Long updatedTime = System.currentTimeMillis();

    colleagueRelationshipHistory1.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationshipHistory1.state = com.linkedin.sales.espresso.State.REMOVED;
    colleagueRelationshipHistory1.updatedTime = updatedTime;

    colleagueRelationshipHistory2.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationshipHistory2.state = com.linkedin.sales.espresso.State.ADDED;
    colleagueRelationshipHistory2.updatedTime = updatedTime - 100000000L;

    colleagueRelationshipHistory3.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationshipHistory3.state = com.linkedin.sales.espresso.State.REMOVED;
    colleagueRelationshipHistory3.updatedTime = updatedTime - 200000000L;

    colleagueRelationshipHistory4.createdBySeatUrn = CREATOR_SEAT_URN.toString();
    colleagueRelationshipHistory4.state = com.linkedin.sales.espresso.State.ADDED;
    colleagueRelationshipHistory4.updatedTime = updatedTime - 300000000L;

    List<Pair<Pair<String, Long>, com.linkedin.sales.espresso.ColleagueRelationshipHistory>> list = new ArrayList<>();
    list.add(new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID), colleagueRelationshipHistory1));
    list.add(new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID), colleagueRelationshipHistory2));
    list.add(new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID_2), colleagueRelationshipHistory3));
    list.add(new Pair<>(new Pair<>(RelationshipType.REPORTS_TO.name(), TO_MEMBER_ID_2), colleagueRelationshipHistory4));
    when(_lssColleaguesDB.getColleagueRelationshipHistory(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), START, COUNT_ONE))
        .thenReturn(Task.value(list));

    List<ColleagueRelationship> result =
        await(_salesColleaguesService.findHistoryByFromMember(FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO, START, COUNT_ONE));

    assertThat(result.get(0).getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.get(0).getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID);
    assertThat(result.get(0).getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.get(0).getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.get(0).getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.get(0).getState()).isEqualTo(State.REMOVED);

    assertThat(result.get(1).getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.get(1).getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID);
    assertThat(result.get(1).getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.get(1).getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.get(1).getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.get(1).getState()).isEqualTo(State.ADDED);

    assertThat(result.get(2).getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.get(2).getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID_2);
    assertThat(result.get(2).getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.get(2).getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.get(2).getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.get(2).getState()).isEqualTo(State.REMOVED);

    assertThat(result.get(3).getFromMember().getMemberIdEntity()).isEqualTo(FROM_MEMBER_ID);
    assertThat(result.get(3).getToMember().getMemberIdEntity()).isEqualTo(TO_MEMBER_ID_2);
    assertThat(result.get(3).getCreator().getSeatIdEntity()).isEqualTo(CREATOR_SEAT_ID);
    assertThat(result.get(3).getContract().getContractIdEntity()).isEqualTo(CONTRACT_ID);
    assertThat(result.get(3).getRelationshipType()).isEqualTo(RelationshipType.REPORTS_TO);
    assertThat(result.get(3).getState()).isEqualTo(State.ADDED);
  }

}
