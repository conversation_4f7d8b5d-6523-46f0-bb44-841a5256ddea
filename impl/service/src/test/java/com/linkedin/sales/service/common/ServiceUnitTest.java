package com.linkedin.sales.service.common;

import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;

/**
 * Created by hacao at 6/13/2018
 * Service Unit test is to help provide await for parseqEngine
 */
public abstract class ServiceUnitTest extends BaseEngineParTest {

  protected <T> T await(Task<T> task) {
    getEngine().run(task);
    try {
      task.await();
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }
    return task.get();
  }
}