package com.linkedin.sales.service.entitymappings;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssEntityMappingDB;
import com.linkedin.sales.espresso.ManualEmailToMemberMapping;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesentitymappings.EmailMatchingKey;
import com.linkedin.salesentitymappings.MatchedMemberEntity;
import com.linkedin.security.crypto.SHA256Hasher;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


/**
 * Unit tests for ManualEmailToMemberMappingService
 */
public class ManualEmailToMemberMappingServiceTest extends ServiceUnitTest {

  private static final MemberUrn MEMBER_URN1 = new MemberUrn(301L);
  private static final MemberUrn MEMBER_URN2 = new MemberUrn(301L);
  private static final SeatUrn SEAT_URN1 = new SeatUrn(2001L);
  private static final String ORG_URN_STRING = "urn:li:organization:1234";
  private static final ContractUrn CONTRACT_URN1 = new ContractUrn(101L);
  private static final long CREATED_AT = 1L;
  private static final String EMAIL1 = "<EMAIL>";
  private static final String EMAIL2 = "<EMAIL>";
  private static final String HASHED_EMAIL1 = SHA256Hasher.hash(EMAIL1);
  private static final String HASHED_EMAIL2 = SHA256Hasher.hash(EMAIL2);

  private ManualEmailToMemberMappingService _manualEmailToMemberMappingService;

  @Mock
  private LssEntityMappingDB _lssEntityMappingDB;

  @BeforeMethod
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    _manualEmailToMemberMappingService = new ManualEmailToMemberMappingService(_lssEntityMappingDB);
  }

  @Test
  public void testBatchGet() {
    // Set Keys
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> complexKey1 = constructComplexKey(EMAIL1);
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> complexKey2 = constructComplexKey(EMAIL2);
    Set<ComplexResourceKey<EmailMatchingKey, EmptyRecord>> keys = new HashSet<>(Arrays.asList(complexKey1, complexKey2));

    // Set Response
    List<Pair<String, ContractUrn>> expectedEspressoKeys = Arrays.asList(
        new Pair<>(HASHED_EMAIL1, CONTRACT_URN1),
        new Pair<>(HASHED_EMAIL2, CONTRACT_URN1)
    );

    List<Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping>> expectedResponse = Arrays.asList(
        new Pair<>(expectedEspressoKeys.get(0), constructMemberMapping(MEMBER_URN1)),
        new Pair<>(expectedEspressoKeys.get(1), constructMemberMapping(MEMBER_URN2))
    );

    Mockito.doReturn(Task.value(expectedResponse)).when(_lssEntityMappingDB).batchGetManualEmailMappings(eq(expectedEspressoKeys));

    Map<ComplexResourceKey<EmailMatchingKey, EmptyRecord>, MatchedMemberEntity> result =
        await(_manualEmailToMemberMappingService.batchGet(keys));

    // Verify contents of the result corresponding to the non-hashed email
    Assert.assertEquals(result.size(), 2);
    Assert.assertEquals(result.get(complexKey1), constructMatchedMemberEntity(MEMBER_URN1));
    Assert.assertEquals(result.get(complexKey2), constructMatchedMemberEntity(MEMBER_URN2));

    // Verify that the email was hashed before the DB call
    verify(_lssEntityMappingDB).batchGetManualEmailMappings(eq(expectedEspressoKeys));
  }

  @Test
  public void testBatchGetWithInvalidResponse() {
    // Set Keys
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> complexKey1 = constructComplexKey(EMAIL1);
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> complexKey2 = constructComplexKey(EMAIL2);
    Set<ComplexResourceKey<EmailMatchingKey, EmptyRecord>> keys = new HashSet<>(Arrays.asList(complexKey1, complexKey2));

    // Set Response
    List<Pair<String, ContractUrn>> expectedEspressoKeys = Arrays.asList(
        new Pair<>(HASHED_EMAIL1, CONTRACT_URN1),
        new Pair<>(HASHED_EMAIL2, CONTRACT_URN1)
    );

    ManualEmailToMemberMapping validResponse = constructMemberMapping(MEMBER_URN1);
    ManualEmailToMemberMapping invalidResponse = constructMemberMapping(MEMBER_URN2);
    invalidResponse.memberUrn = "invalidUrn";


    List<Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping>> expectedResponse = Arrays.asList(
        new Pair<>(expectedEspressoKeys.get(0), validResponse),
        new Pair<>(expectedEspressoKeys.get(1), invalidResponse)
    );

    Mockito.doReturn(Task.value(expectedResponse)).when(_lssEntityMappingDB).batchGetManualEmailMappings(eq(expectedEspressoKeys));

    Map<ComplexResourceKey<EmailMatchingKey, EmptyRecord>, MatchedMemberEntity> result =
        await(_manualEmailToMemberMappingService.batchGet(keys));

    // Only 1 valid response should be returned
    Assert.assertEquals(result.size(), 1);
    Assert.assertEquals(result.get(complexKey1), constructMatchedMemberEntity(MEMBER_URN1));

    // Verify that the email was hashed before the DB call
    verify(_lssEntityMappingDB).batchGetManualEmailMappings(eq(expectedEspressoKeys));
  }

  @Test
  public void testDelete() {
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> key = constructComplexKey(EMAIL1);
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(_lssEntityMappingDB)
        .deleteManualEmailMapping(eq(HASHED_EMAIL1), eq(CONTRACT_URN1));

    UpdateResponse result = await(_manualEmailToMemberMappingService.delete(key));

    Assert.assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);

    // Verify that the email was hashed before the DB call
    verify(_lssEntityMappingDB).deleteManualEmailMapping(eq(HASHED_EMAIL1), eq(CONTRACT_URN1));
  }

  @Test
  public void testUpsert() {
    ComplexResourceKey<EmailMatchingKey, EmptyRecord> key = constructComplexKey(EMAIL1);
    MatchedMemberEntity matchedMemberEntity = constructMatchedMemberEntity(MEMBER_URN1);

    Mockito.doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssEntityMappingDB)
        .upsertManualEmailToMemberMapping(eq(HASHED_EMAIL1), eq(CONTRACT_URN1), any());

    UpdateResponse result = await(_manualEmailToMemberMappingService.upsert(key, matchedMemberEntity));

    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @DataProvider
  public Object[][] createMatchedMemberEntityDataProvider() throws URISyntaxException {
    ManualEmailToMemberMapping input = constructMemberMapping(MEMBER_URN1);
    ManualEmailToMemberMapping inputWithOrgUrn = constructMemberMapping(MEMBER_URN1);
    inputWithOrgUrn.associatedOrgUrn = ORG_URN_STRING;
    OrganizationUrn OrgUrn = OrganizationUrn.deserialize(ORG_URN_STRING);

    return new Object[][] {
        { input, constructMatchedMemberEntity(MEMBER_URN1) },
        { inputWithOrgUrn,  constructMatchedMemberEntity(MEMBER_URN1).setAssociatedOrganization(OrgUrn) }
    };
  }

  @Test(dataProvider = "createMatchedMemberEntityDataProvider")
  public void testCreateMatchedMemberEntity(ManualEmailToMemberMapping input, MatchedMemberEntity expected) throws URISyntaxException {
    MatchedMemberEntity actualMemberEntity = _manualEmailToMemberMappingService.createMatchedMemberEntity(input);
    Assert.assertEquals(actualMemberEntity, expected);
  }

  @Test
  public void testCreateManualEmailToMemberMapping() {
    MatchedMemberEntity matchedMemberEntity = constructMatchedMemberEntity(MEMBER_URN1);
    ManualEmailToMemberMapping manualEmailToMemberMapping = _manualEmailToMemberMappingService.createManualEmailToMemberMapping(matchedMemberEntity);

    Assert.assertEquals(manualEmailToMemberMapping.memberUrn, MEMBER_URN1.toString());
    Assert.assertEquals(manualEmailToMemberMapping.seatUrn, SEAT_URN1.toString());
  }

  @Test
  public void testCreateManualEmailToMemberMappingWithOrgUrn() throws URISyntaxException {
    MatchedMemberEntity matchedMemberEntity = constructMatchedMemberEntity(MEMBER_URN1)
        .setAssociatedOrganization(OrganizationUrn.deserialize(ORG_URN_STRING));
    ManualEmailToMemberMapping manualEmailToMemberMapping = _manualEmailToMemberMappingService.createManualEmailToMemberMapping(matchedMemberEntity);

    Assert.assertEquals(manualEmailToMemberMapping.memberUrn, MEMBER_URN1.toString());
    Assert.assertEquals(manualEmailToMemberMapping.seatUrn, SEAT_URN1.toString());
    Assert.assertEquals(manualEmailToMemberMapping.associatedOrgUrn, ORG_URN_STRING);
  }

  private ManualEmailToMemberMapping constructMemberMapping(MemberUrn memberUrn) {
    ManualEmailToMemberMapping memberMapping = new ManualEmailToMemberMapping();

    memberMapping.memberUrn = memberUrn.toString();
    memberMapping.seatUrn = SEAT_URN1.toString();
    memberMapping.createdAt = CREATED_AT;
    memberMapping.lastModifiedAt = CREATED_AT;

    return memberMapping;
  }

  private ComplexResourceKey<EmailMatchingKey, EmptyRecord> constructComplexKey(String email) {
    EmailMatchingKey key1 = new EmailMatchingKey().setEmail(email).setContract(CONTRACT_URN1);
    return new ComplexResourceKey<>(key1, new EmptyRecord());
  }

  private MatchedMemberEntity constructMatchedMemberEntity(MemberUrn memberUrn) {
    MatchedMemberEntity matchedMemberEntity = new MatchedMemberEntity();

    matchedMemberEntity.setMember(memberUrn);
    matchedMemberEntity.setActorSeat(SEAT_URN1);
    matchedMemberEntity.setCreated(CREATED_AT);
    matchedMemberEntity.setLastModified(CREATED_AT);

    return matchedMemberEntity;
  }
}
