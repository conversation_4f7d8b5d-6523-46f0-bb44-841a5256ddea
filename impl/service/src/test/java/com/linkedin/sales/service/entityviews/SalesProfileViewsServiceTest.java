package com.linkedin.sales.service.entityviews;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssEntityViewDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.ProfileView;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesentityview.SalesProfileView;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


public class SalesProfileViewsServiceTest extends ServiceUnitTest {
  private static final SeatUrn SEAT_URN = new SeatUrn(111L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final Long MEMBER_ID = 1111L;
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final Long lastViewedTime = 1644271043194L;

  private SalesProfileViewsService _salesProfileViewsService;

  @Mock
  private LssEntityViewDB _lssEntityViewDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesProfileViewsService = new SalesProfileViewsService(_lssEntityViewDB);
  }

  @BeforeMethod
  public void resetMock() {
    reset(_lssEntityViewDB);
  }

  @Test
  public void testUpsertProfileView() {
    SalesProfileView salesProfileView = createSalesProfileView();
    ProfileView profileView = createProfileView();
    doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssEntityViewDB)
        .upsertProfileView(eq(SEAT_URN), eq(MEMBER_URN), eq(profileView));
    UpdateResponse result =
        await(_salesProfileViewsService.upsertSalesProfileView(buildCompoundKey(salesProfileView), salesProfileView));
    assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testUpsertProfileViewWithExceptionFromDB() {
    SalesProfileView salesProfileView = createSalesProfileView();
    ProfileView profileView = createProfileView();
    doReturn(Task.failure(new RuntimeException(
        String.format("Unexpected response code for call to upsert the profile view for seat: %s, member: %s", SEAT_URN,
            MEMBER_URN)))).when(_lssEntityViewDB).upsertProfileView(eq(SEAT_URN), eq(MEMBER_URN), eq(profileView));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> await(
        _salesProfileViewsService.upsertSalesProfileView(buildCompoundKey(salesProfileView), salesProfileView)))
        .withCause(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR,
            "Failed to upsert salesProfileView by key: " + buildCompoundKey()));
  }

  @Test
  public void testGetProfileViewFromDB() {
    SalesProfileView expectedResult = createSalesProfileView();
    doReturn(Task.value(createProfileView())).when(_lssEntityViewDB).getProfileView(eq(SEAT_URN), eq(MEMBER_URN));
    SalesProfileView result = await(_salesProfileViewsService.getSalesProfileView(buildCompoundKey(expectedResult)));
    assertEquals(result.getViewerSeat(), expectedResult.getViewerSeat());
    assertEquals(result.getViewedMember(), expectedResult.getViewedMember());
    assertEquals(result.getViewerContract(), expectedResult.getViewerContract());
    assertEquals(result.getLastViewedAt(), expectedResult.getLastViewedAt());
  }

  @Test
  public void testGetProfileViewWithNoEntityFoundExceptionFromDB() {
    doReturn(Task.failure(new EntityNotFoundException(null,
        String.format("can not find profileView for seat: %s, member: %s", SEAT_URN, MEMBER_URN)))).when(
        _lssEntityViewDB).getProfileView(eq(SEAT_URN), eq(MEMBER_URN));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesProfileViewsService.getSalesProfileView(buildCompoundKey())))
        .withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
  }

  private SalesProfileView createSalesProfileView() {
    return new SalesProfileView().setViewerSeat(SEAT_URN)
        .setViewedMember(MEMBER_URN)
        .setViewerContract(CONTRACT_URN)
        .setLastViewedAt(lastViewedTime);
  }

  private ProfileView createProfileView() {
    ProfileView profileView = new ProfileView();
    profileView.lastViewedTime = lastViewedTime;
    profileView.contractUrn = CONTRACT_URN.toString();

    return profileView;
  }

  private CompoundKey buildCompoundKey(SalesProfileView salesProfileView) {
    return new CompoundKey().append(VIEWER_SEAT_COMPOUND_KEY, salesProfileView.getViewerSeat())
        .append(VIEWED_MEMBER_COMPOUND_KEY, salesProfileView.getViewedMember());
  }

  private CompoundKey buildCompoundKey() {
    return new CompoundKey().append(VIEWER_SEAT_COMPOUND_KEY, SEAT_URN).append(VIEWED_MEMBER_COMPOUND_KEY, MEMBER_URN);
  }
}
