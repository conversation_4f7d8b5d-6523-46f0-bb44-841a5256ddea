package com.linkedin.sales.service.flagship;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.DecoSpecification;
import com.linkedin.comms.NotificationCard;
import com.linkedin.notifications.NotificationTriggerSet;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.parseq.BaseEngineParTest;
import org.testng.Assert;
import org.testng.annotations.Test;


public class LeadPositionChangeServiceTest extends BaseEngineParTest {

  @Test
  public void testGenerateDecorator() throws Exception {
    MemberUrn memberUrn = MemberUrn.deserialize("urn:li:member:135");

    LeadPositionChangeService service = new LeadPositionChangeService();

    CommunicationDecorator result = runAndWait(service.generateDecorator(memberUrn));

    Assert.assertTrue(result.hasCachable());
    Assert.assertTrue(result.isCachable());
    DecoSpecification decoSpecification = result.getDetail().getDecoSpecification();
    Assert.assertFalse(decoSpecification.hasGroupByValue());
    Assert.assertFalse(decoSpecification.hasRecipientValue());
    Assert.assertEquals(decoSpecification.getItemValue(), "~position:isbMini(companyName,title,localizedFirstName)");
  }

  @Test
  public void testFormatNotificationWithWrongType() throws Exception {
    NotificationsV2.NotificationTriggerSet triggerSet = new NotificationsV2.NotificationTriggerSet();
    triggerSet.setNotificationTriggerSet(new NotificationTriggerSet().setNotificationType("INVALID_VALUE"));
    NotificationsV2 notification = new NotificationsV2().setNotificationTriggerSet(triggerSet);
    NotificationsV2[] notifications = new NotificationsV2[] { notification };

    LeadPositionChangeService service = new LeadPositionChangeService();

    NotificationCard[] notificationCards = service.formatNotifications(notifications);

    Assert.assertEquals(notificationCards.length, 0);
  }

}
