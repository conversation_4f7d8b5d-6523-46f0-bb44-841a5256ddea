package com.linkedin.sales.service.flagship;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.mnybe.shared.identity.Entitlement;
import com.linkedin.mnybe.shared.identity.EntityType;
import com.linkedin.mnybe.shared.identity.Party;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.sales.client.flagship.PremiumEntitlementsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import java.util.Set;
import org.mockito.Mock;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.flagship.PremiumEntitlementsService.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.*;


public class PremiumEntitlementsServiceTest extends BaseEngineParTest {
  private static final MemberUrn MEMBER_URN = new MemberUrn(123L);
  private static final Party MEMBER_PARTY = new Party(EntityType.Member, 123L);
  @Mock
  private LixService _lixService;
  @Mock
  private PremiumEntitlementsClient _premiumEntitlementsClient;
  private PremiumEntitlementsService _premiumEntitlementsService;

  @BeforeMethod
  public void setup() {
    initMocks(this);
    _premiumEntitlementsService = new PremiumEntitlementsService(_lixService, _premiumEntitlementsClient);
  }

  @Test(description = "Test that normal flow works correctly")
  public void testGetEntitlements() throws Exception {
    Set<Entitlement> requestedEntitlements = ImmutableSet.of(Entitlement.CAN_ACCESS_CAP, Entitlement.CAN_ACCESS_SALES);
    Set<Entitlement> expectedEntitlements = ImmutableSet.of(Entitlement.CAN_ACCESS_SALES);
    when(_lixService.getLixTreatment(MEMBER_URN, LixUtils.PREMIUM_ENTITLEMENTS_TOGGLE_LIX, null)).thenReturn(
        Task.value("control"));
    when(_premiumEntitlementsClient.getEntitlements(MEMBER_PARTY, requestedEntitlements)).thenReturn(
        Task.value(expectedEntitlements));

    Set<Entitlement> actualEntitlements =
        runAndWait(_premiumEntitlementsService.getEntitlements(MEMBER_URN, requestedEntitlements));
    Assert.assertEquals(actualEntitlements, expectedEntitlements);
  }

  @Test(description = "Test that the lix toggle uses a different party")
  public void testGetEntitlementsLixToggle() throws Exception {
    Set<Entitlement> requestedEntitlements = ImmutableSet.of(Entitlement.CAN_ACCESS_CAP, Entitlement.CAN_ACCESS_SALES);
    Set<Entitlement> expectedEntitlements = ImmutableSet.of(Entitlement.CAN_ACCESS_SALES);
    when(_lixService.getLixTreatment(MEMBER_URN, LixUtils.PREMIUM_ENTITLEMENTS_TOGGLE_LIX, null)).thenReturn(
        Task.value("enabled"));
    when(_premiumEntitlementsClient.getEntitlements(BASIC_ENTITLEMENTS_PARTY, requestedEntitlements)).thenReturn(
        Task.value(expectedEntitlements));

    Set<Entitlement> actualEntitlements =
        runAndWait(_premiumEntitlementsService.getEntitlements(MEMBER_URN, requestedEntitlements));
    Assert.assertEquals(actualEntitlements, expectedEntitlements);
  }
}
