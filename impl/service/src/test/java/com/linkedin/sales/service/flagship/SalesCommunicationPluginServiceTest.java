package com.linkedin.sales.service.flagship;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.AttributedText;
import com.linkedin.common.urn.Urn;
import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.DecoSpecification;
import com.linkedin.comms.NotificationCard;
import com.linkedin.comms.TextProperty;
import com.linkedin.notifications.Element;
import com.linkedin.notifications.ElementArray;
import com.linkedin.notifications.NotificationTriggerSet;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.notifications.NotificationsV2Key;
import com.linkedin.notifications.UrnList;
import com.linkedin.notifications.UrnListArray;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.flagship.enums.SalesCommunicationCampaignName;
import com.linkedin.sales.service.utils.UrnUtils;
import java.util.Arrays;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SalesCommunicationPluginServiceTest extends BaseEngineParTest {
  private static final String ACTOR_DECO_SPEC =
      "~member:isbMini;viewerId=1!prune=0(localizedFirstName,localizedLastName,profilePicture(displayImage),positionsOrder,positions*(localizedCompanyName,localizedTitle))";
  private static final String ITEM_DECO_SPEC =
      "~share:ugcPost!prune=0(visibility,specificContent(com.linkedin.ugc.ShareContent(shareCommentary(text))))~ugcPost!prune=0(visibility,specificContent(com.linkedin.ugc.ShareContent(shareCommentary(text))))";

  @Test
  public void testGenerateDecorator() {

    // set up mocks
    Mocks mocks = new Mocks();
    CommunicationContext communicationContext = mocks.getEmptyCommunicationContext();
    DecoSpecification expectedDecoSpec =
        new DecoSpecification().setItemValue(ITEM_DECO_SPEC).setActorValue(ACTOR_DECO_SPEC);

    CommunicationDecorator communicationDecorator =
        new CommunicationDecorator().setDetail(CommunicationDecorator.Detail.create(expectedDecoSpec));
    when(mocks._salesLeadSharedUpdateRenderer.generateDecorator(eq(communicationContext))).thenReturn(
        Task.value(communicationDecorator));

    // execute
    CommunicationDecorator decorator = runAndWait(mocks.getService()
        .generateDecorator(SalesCommunicationCampaignName.FS_LEAD_SHARED_UPDATE.toString(), communicationContext));

    // assert
    assertThat(decorator.getDetail().getDecoSpecification()).isEqualTo(expectedDecoSpec);
  }

  @Test
  public void testFormatNotificationsAllSuccess() {
    // set up mocks
    Mocks mocks = new Mocks();
    CommunicationContext communicationContext = mocks.getEmptyCommunicationContext();
    NotificationsV2 notification1 = mocks.getNotificationV2(UrnUtils.toMemberUrn(1));
    NotificationsV2 notification2 = mocks.getNotificationV2(UrnUtils.toMemberUrn(2));
    NotificationCard notificationCard =
        new NotificationCard().setHeadline(new TextProperty().setText(new AttributedText().setText("Jingtao Wang")));
    when(mocks._salesLeadSharedUpdateRenderer.formatNotification(any(), eq(communicationContext))).thenReturn(
        Task.value(notificationCard));

    // execute
    List<NotificationCard> actual = Arrays.asList(runAndWait(mocks.getService()
        .formatNotifications(new NotificationsV2[]{notification1, notification2}, communicationContext)));

    // assertion
    assertThat(actual.size()).isEqualTo(2);
    assertThat(actual.get(0)).isEqualTo(notificationCard);
    assertThat(actual.get(1)).isEqualTo(notificationCard);
  }

  @Test
  public void testFormatNotificationsWithOneNullCard() {
    // set up mocks
    Mocks mocks = new Mocks();
    CommunicationContext communicationContext = mocks.getEmptyCommunicationContext();
    NotificationsV2 notification1 = mocks.getNotificationV2(UrnUtils.toMemberUrn(1));
    NotificationsV2 notification2 = mocks.getNotificationV2(UrnUtils.toMemberUrn(2));
    NotificationCard notificationCard =
        new NotificationCard().setHeadline(new TextProperty().setText(new AttributedText().setText("Jingtao Wang")));
    when(mocks._salesLeadSharedUpdateRenderer.formatNotification(eq(notification1),
        eq(communicationContext))).thenReturn(Task.value(notificationCard));
    when(mocks._salesLeadSharedUpdateRenderer.formatNotification(eq(notification2),
        eq(communicationContext))).thenReturn(Task.value(null));
    // execute
    List<NotificationCard> actual = Arrays.asList(runAndWait(mocks.getService()
        .formatNotifications(new NotificationsV2[]{notification1, notification2}, communicationContext)));

    // assertion
    assertThat(actual.size()).isEqualTo(1);
    assertThat(actual.get(0)).isEqualTo(notificationCard);
  }

  @Test
  public void testFormatNotificationsWithOneFailure() {
    // set up mocks
    Mocks mocks = new Mocks();
    CommunicationContext communicationContext = mocks.getEmptyCommunicationContext();
    NotificationsV2 notification1 = mocks.getNotificationV2(UrnUtils.toMemberUrn(1));
    NotificationsV2 notification2 = mocks.getNotificationV2(UrnUtils.toMemberUrn(2));
    NotificationCard notificationCard =
        new NotificationCard().setHeadline(new TextProperty().setText(new AttributedText().setText("Jingtao Wang")));
    when(mocks._salesLeadSharedUpdateRenderer.formatNotification(eq(notification1),
        eq(communicationContext))).thenReturn(Task.value(notificationCard));
    when(
        mocks._salesLeadSharedUpdateRenderer.formatNotification(eq(notification2), eq(communicationContext))).thenThrow(
        new RuntimeException("Failed to format notification"));

    assertThatExceptionOfType(Exception.class).isThrownBy(() -> {
      runAndWait(mocks.getService()
          .formatNotifications(new NotificationsV2[]{notification1, notification2}, communicationContext));
    });
  }

  private static class Mocks {
    @Mock
    private SalesLeadSharedUpdateRenderer _salesLeadSharedUpdateRenderer;

    Mocks() {
      MockitoAnnotations.openMocks(this);
    }

    SalesCommunicationPluginService getService() {
      return new SalesCommunicationPluginService(
          ImmutableMap.of(SalesCommunicationCampaignName.FS_LEAD_SHARED_UPDATE, _salesLeadSharedUpdateRenderer));
    }

    CommunicationContext getEmptyCommunicationContext() {
      return new CommunicationContext();
    }

    NotificationsV2 getNotificationV2(Urn urn) {
      NotificationsV2.NotificationTriggerSet triggerSet = new NotificationsV2.NotificationTriggerSet();
      triggerSet.setNotificationTriggerSet(new NotificationTriggerSet().setNotificationType(
              SalesCommunicationCampaignName.FS_LEAD_SHARED_UPDATE.toString())
          .setUrnLists(new UrnListArray(
              new UrnList().setElements(new ElementArray(new Element().setElement(urn))).setNumberOfItems(1))));
      return new NotificationsV2().setNotificationId(
              new NotificationsV2Key().setNotificationType(SalesCommunicationCampaignName.FS_LEAD_SHARED_UPDATE.toString()))
          .setNotificationTriggerSet(triggerSet);
    }
  }
}

