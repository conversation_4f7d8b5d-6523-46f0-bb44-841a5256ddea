package com.linkedin.sales.service.flagship;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.common.Attribute;
import com.linkedin.common.AttributeArray;
import com.linkedin.common.AttributedText;
import com.linkedin.common.BoldAttributedEntity;
import com.linkedin.common.Locale;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.DigitalmediaAssetUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.comms.CommunicationCategory;
import com.linkedin.comms.CommunicationContext;
import com.linkedin.comms.CommunicationDecorator;
import com.linkedin.comms.DecoSpecification;
import com.linkedin.comms.ImageAttribute;
import com.linkedin.comms.MemberImage;
import com.linkedin.comms.NotificationCard;
import com.linkedin.comms.TextInsightVariant;
import com.linkedin.comms.helpers.deco.MockedDecoratedNotificationBuilder;
import com.linkedin.comms.helpers.internal.AttributedTextParser;
import com.linkedin.comms.helpers.internal.CommsLocalizationHandlerImpl;
import com.linkedin.comms.helpers.internal.ResourceBundleHandler;
import com.linkedin.comms.helpers.internal.jsoup.JsoupAttributedTextParser;
import com.linkedin.data.template.LongArray;
import com.linkedin.identity.Position;
import com.linkedin.identity.PositionMap;
import com.linkedin.identity.Profile;
import com.linkedin.identity.ProfilePicture;
import com.linkedin.notifications.NotificationsV2;
import com.linkedin.notifications.NotificationsV2Key;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.ugc.ShareContent;
import com.linkedin.ugc.UserGeneratedContent;
import java.net.URISyntaxException;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.Test;

import static com.linkedin.comms.SystemImageName.*;
import static com.linkedin.sales.service.flagship.enums.SalesCommunicationCampaignName.*;
import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class SalesLeadSharedUpdateRendererTest extends BaseEngineParTest {
  private static final MemberUrn MEMBER_URN = UrnUtils.toMemberUrn(1L);
  private static final Locale DEFAULT_LOCALE = new Locale().setLanguage("en").setCountry("US");
  private static final String DEFAULT_TIME_ZONE = "America/Los_Angeles";
  private static final String ACTOR_DECO_SPEC =
      "~member:isbMini!prune=0(localizedFirstName,localizedLastName,positionsOrder,positions*(localizedCompanyName,localizedTitle))";
  private static final String ITEM_DECO_SPEC =
      "~share:ugcPost!prune=0(visibility,specificContent(com.linkedin.ugc.ShareContent(shareCommentary(text))))~ugcPost!prune=0(visibility,specificContent(com.linkedin.ugc.ShareContent(shareCommentary(text))))";
  private static final String VOYAGER_WEB_MP_NAME = "voyager-web";
  private static final String VOYAGER_IOS_MP_NAME = "voyager-ios";
  private static final String VOYAGER_ANDROID_MP_NAME = "voyager-android";
  private static final String VOYAGER_WEB_MP_VERSION = "1.13.16734";
  private static final String VOYAGER_IOS_MP_VERSION = "9.29.7124";
  private static final String VOYAGER_ANDROID_MP_VERSION = "1.22.110";

  @Test
  public void testGenerateDecorator() {
    Mocks mocks = new Mocks();
    Task<CommunicationDecorator> task = mocks.getRenderer()
        .generateDecorator(mocks.getCommunicationContext(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION));
    CommunicationDecorator communicationDecorator = runAndWait(task);
    DecoSpecification decoSpecification = communicationDecorator.getDetail().getDecoSpecification();
    String itemDecoSpec = decoSpecification.getItemValue();
    String actorDecoSpec = decoSpecification.getActorValue();
    assertFalse(decoSpecification.hasGroupByValue());
    assertFalse(decoSpecification.hasRecipientValue());
    assertThat(actorDecoSpec).isEqualTo(ACTOR_DECO_SPEC);
    assertThat(itemDecoSpec).isEqualTo(ITEM_DECO_SPEC);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerWebWithInsightOfImageWithPlainText() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerIOSWithInsightOfImageWithPlainTextWhenMpVersionEqualsToMinimal()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_IOS_MP_NAME, VOYAGER_IOS_MP_VERSION, null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerAndroidWithInsightOfImageWithPlainTextWhenMpVersionEqualsToMinimal()
      throws URISyntaxException {
    NotificationCard actual =
        getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, VOYAGER_ANDROID_MP_VERSION, null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerIOSWithInsightOfImageWithPlainTextWhenMpVersionLargerThanMinimal()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_IOS_MP_NAME, "9.29.7125", null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerAndroidWithInsightOfImageWithPlainTextWhenMpVersionLargerThanMinimal()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, "1.23.216", null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testShouldFormatNotificationForVoyagerIOSWithInsightOfPlainTextOnlyWhenMpVersionSmallerThanMinimal()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_IOS_MP_NAME, "9.29.7123", null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
  }

  @Test
  public void testShouldFormatNotificationForVoyagerAndroidWithInsightOfPlainTextOnlyWhenMpVersionSmallerThanMinimal()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, "1.18.216", null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
  }

  @Test
  public void testShouldFormatNotificationWithInsightOfPlainTextOnlyWhenMpNameIsNotPresent()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(null, "1.18.216", null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
  }

  @Test
  public void testShouldFormatNotificationForVoyagerAndroidWithInsightOfPlainTextOnlyWhenMpVersionIsNotPresent()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, null, null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
  }

  @Test
  public void testShouldFormatNotificationForVoyagerWebWithInsightOfImageWithPlainTextWhenMpVersionIsNotPresent()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, null, null);

    assertForHappyPath(actual, null);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
  }

  @Test
  public void testFormatNotificationShouldHaveHeadlineForSavedLeadWhenGroupByUrnHasTrueForSaved()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION,
        "urn:li:save:(urn:li:member:1,true)");

    assertForHappyPath(actual, true);
    assertThat(actual.getHeadline().getText().getText()).isEqualTo(
        "Your saved lead John Doe at Microsoft posted: hello");
  }

  @Test
  public void testFormatNotificationShouldHaveHeadlineForPotentialLeadWhenGroupByUrnHasFalseForSaved()
      throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION,
        "urn:li:save:(urn:li:member:1,false)");

    assertForHappyPath(actual, false);
    assertThat(actual.getHeadline().getText().getText()).isEqualTo(
        "Your potential lead John Doe at Microsoft posted: hello");
  }

  private NotificationCard getNotificationCardForHappyPath(String mpName, String mpVersion, String groupByUrnStr)
      throws URISyntaxException {
    Mocks mocks = new Mocks();
    CommunicationContext context = mocks.getCommunicationContext(mpName, mpVersion);
    SalesLeadSharedUpdateRenderer renderer = mocks.getRenderer();

    CommunicationDecorator decorator = runAndWait(renderer.generateDecorator(context));
    NotificationsV2 notification =
        mocks.getNotificationV2WithAllRequiredFields(decorator, Urn.createFromString("urn:li:share:123"),
            Urn.createFromString("urn:li:member:1"), groupByUrnStr);

    return runAndWait(renderer.formatNotification(notification, context));
  }

  private void assertForHappyPath(NotificationCard actual, Boolean isSavedLead) {
    assertThat(actual.getHeadline().getText().getText()).isEqualTo(
        String.format("Your %s lead John Doe at Microsoft posted: hello",
            isSavedLead == null || isSavedLead ? "saved" : "potential"));
    assertThat(actual.getHeadline().getText().getAttributes()).isEqualTo(new AttributeArray(new Attribute().setStart(
            String.format("Your %s lead ", isSavedLead == null || isSavedLead ? "saved" : "potential").length())
        .setLength("John Doe".length())
        .setValue(Attribute.Value.create(new BoldAttributedEntity()))));
    assertThat(actual.getHeadline().getAccessibilityText()).isEqualTo("Details about the lead shared update");
    assertNotNull(actual.getInsight());
    ImageAttribute expectedAttribute =
        new ImageAttribute().setAttribute(ImageAttribute.Attribute.create(new MemberImage().setSource(MEMBER_URN)));
    assertThat(actual.getHeaderImage().getAttributes().get(0)).isEqualTo(expectedAttribute);
    NotificationsV2Key v2Key = new NotificationsV2Key().setRecipient(MEMBER_URN)
        .setNotificationType(FS_LEAD_SHARED_UPDATE.name())
        .setGroupBy(
            isSavedLead == null ? "urn:li:none" : String.format("urn:li:save:(urn:li:member:1,%s)", isSavedLead));
    assertThat(actual.getNotificationKey().getRecipient()).isEqualTo(v2Key.getRecipient());
    assertThat(actual.getNotificationKey().getNotificationType()).isEqualTo(v2Key.getNotificationType());
    assertThat(actual.getNotificationKey().getGroupBy()).contains(v2Key.getGroupBy());
    assertNotNull(actual.getCardAction());
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  private void assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(NotificationCard actual, boolean isSavedLead, boolean isUserSalesNavigatorMember) {
    assertThat(actual.getHeadline().getText().getText()).isEqualTo(
        String.format("Your %s lead John Doe at Microsoft posted: hello",
            isSavedLead ? "saved" : "potential"));
    assertThat(actual.getHeadline().getText().getAttributes()).isEqualTo(new AttributeArray(new Attribute().setStart(
            String.format("Your %s lead ", isSavedLead ? "saved" : "potential").length())
        .setLength("John Doe".length())
        .setValue(Attribute.Value.create(new BoldAttributedEntity()))));
    assertThat(actual.getHeadline().getAccessibilityText()).isEqualTo("Details about the lead shared update");
    assertNotNull(actual.getInsight());
    ImageAttribute expectedAttribute =
        new ImageAttribute().setAttribute(ImageAttribute.Attribute.create(new MemberImage().setSource(MEMBER_URN)));
    assertThat(actual.getHeaderImage().getAttributes().get(0)).isEqualTo(expectedAttribute);
    NotificationsV2Key v2Key = new NotificationsV2Key().setRecipient(MEMBER_URN)
        .setNotificationType(FS_LEAD_SHARED_UPDATE.name())
        .setGroupBy(String.format("urn:li:save:(urn:li:member:1,%s,%s)", isSavedLead, isUserSalesNavigatorMember));
    assertThat(actual.getNotificationKey().getRecipient()).isEqualTo(v2Key.getRecipient());
    assertThat(actual.getNotificationKey().getNotificationType()).isEqualTo(v2Key.getNotificationType());
    assertThat(actual.getNotificationKey().getGroupBy()).contains(v2Key.getGroupBy());
    assertNotNull(actual.getCardAction());
  }

  @Test
  public void testFormatNotificationShouldGetNullResultWhenThereIsMissingFieldInNotificationV2()
      throws URISyntaxException {
    // assert
    Mocks mocks = new Mocks();
    CommunicationContext context = mocks.getCommunicationContext(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION);
    SalesLeadSharedUpdateRenderer renderer = mocks.getRenderer();

    CommunicationDecorator decorator = runAndWait(renderer.generateDecorator(context));
    NotificationsV2 notification =
        mocks.getNotificationV2WithMissingRequiredFields(decorator, Urn.createFromString("urn:li:share:123"),
            Urn.createFromString("urn:li:member:456"));

    NotificationCard notificationCard = runAndWait(renderer.formatNotification(notification, context));

    // then
    assertNull(notificationCard);
  }


  @Test
  public void testShouldFormatNotificationWithPremiumChipWhenLeadIsSavedAndUserIsSalesNavigatorMember() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:save:(urn:li:member:1,true,true)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, true, true);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipAndLinkWhenLeadIsSavedAndUserIsNotSalesNavigatorMember() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:save:(urn:li:member:1,true,false)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, true, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_PREMIUM_CHIP_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123?type=FS_LEAD_SHARED_UPDATE"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipWhenLeadIsNotSavedAndUserIsSalesNavigatorMember() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:save:(urn:li:member:1,false,true)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, true);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipAndLinkWhenLeadIsNotSavedAndUserIsNotSalesNavigatorMember() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:save:(urn:li:member:1,false,false)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_PREMIUM_CHIP_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123?type=FS_LEAD_SHARED_UPDATE"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipAndLinkWhenLeadIsNotSavedAndUserIsNotSalesNavigatorMemberWhenIOSVersionIsSmallerThanMinimalForBlueCrossIcon() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_IOS_MP_NAME, "9.29.7123", "urn:li:save:(urn:li:member:1,false,false)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_PREMIUM_CHIP_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123?type=FS_LEAD_SHARED_UPDATE"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipAndLinkWhenLeadIsNotSavedAndUserIsNotSalesNavigatorMemberWhenAndroidVersionIsSmallerThanMinimalForBlueCrossIcon() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, "1.18.216", "urn:li:save:(urn:li:member:1,false,false)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_PREMIUM_CHIP_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123?type=FS_LEAD_SHARED_UPDATE"));
  }

  @Test
  public void testShouldFormatNotificationWithPlainTextInsightWhenUserIsSalesNavigatorMemberAndIOSVersionIsSmallerThanMinimalForBlueCrossIcon() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_IOS_MP_NAME, "9.29.7123", "urn:li:save:(urn:li:member:1,false,true)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, true);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  @Test
  public void testShouldFormatNotificationWithPlainTextWhenLeadIsNotSavedAndUserIsSalesNavigatorMemberWhenAndroidVersionIsSmallerThanMinimalForBlueCrossIcon() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_ANDROID_MP_NAME, "1.18.216", "urn:li:save:(urn:li:member:1,true,true)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, true, true);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(TextInsightVariant.DEFAULT);
    assertNull(actual.getInsight().getDetail().getTextInsight().getSystemImageName());
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  @Test
  public void testShouldFormatNotificationWithPremiumChipAndLinkWhenLeadIsNotSavedAndUserIsNotSalesNavigatorMemberWithGroupByUrnOfUniqueSuffix() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:uniqueSuffix:(urn:li:save:(urn:li:member:1,false,false),5gcDjuC0Q3CpVdENVArBdw)");

    assertForHappyPathWithIsSavedLeadAndIsUserSalesNavigatorMemberInfo(actual, false, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_PREMIUM_CHIP_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123?type=FS_LEAD_SHARED_UPDATE"));
  }

  @Test
  public void testShouldFormatNotificationWithBlueCrossIconWhenLeadIsNotSavedAndUserIsSalesNavigatorMemberWithGroupByUrnOfUniqueSuffix() throws URISyntaxException {
    NotificationCard actual = getNotificationCardForHappyPath(VOYAGER_WEB_MP_NAME, VOYAGER_WEB_MP_VERSION, "urn:li:uniqueSuffix:(urn:li:save:(urn:li:member:1,false),5gcDjuC0Q3CpVdENVArBdw)");

    assertForHappyPath(actual, false);
    assertThat(actual.getInsight().getDetail().getTextInsight().getVariant()).isEqualTo(
        TextInsightVariant.IMAGE_WITH_PLAIN_TEXT);
    assertThat(actual.getInsight().getDetail().getTextInsight().getSystemImageName()).isEqualTo(
        SYS_ICN_APP_SALES_NAVIGATOR_COLOR_SMALL);
    assertThat(actual.getCardAction().getTargetUrlData().getValue().getUrl()).isEqualTo(
        new Url("/feed/update/urn%3Ali%3Ashare%3A123"));
  }

  private static class Mocks {
    @Mock
    private ResourceBundleHandler resourceBundleHandler;

    public Mocks() {
      MockitoAnnotations.openMocks(this);
    }

    public SalesLeadSharedUpdateRenderer getRenderer() {
      Stubbing.stubDynamicResourceBundleManager(this);

      CommsLocalizationHandlerImpl commsLocalizationHandler =
          new CommsLocalizationHandlerImpl(this.resourceBundleHandler, MockData.attributedTextParser, null, null, null);

      return new SalesLeadSharedUpdateRenderer(commsLocalizationHandler);
    }

    public NotificationsV2 getNotificationV2WithAllRequiredFields(CommunicationDecorator decorator, Urn entityUrn,
        Urn actorUrn, String groupByUrnStr) {
      UserGeneratedContent userGeneratedContent = new UserGeneratedContent();
      UserGeneratedContent.SpecificContent specificContent = new UserGeneratedContent.SpecificContent();
      specificContent.setShareContent(new ShareContent().setShareCommentary(new AttributedText().setText("hello")));

      userGeneratedContent.setSpecificContent(specificContent);

      return MockedDecoratedNotificationBuilder.buildWith(decorator, MEMBER_URN, FS_LEAD_SHARED_UPDATE.name(),
              UrnUtils.createUrnFromString(groupByUrnStr == null ? "urn:li:none" : groupByUrnStr))
          .addItemUrnWithActor(entityUrn, actorUrn)
          .addRecord(entityUrn, "ugcPost", userGeneratedContent)
          .addRecord(actorUrn, "isbMini", new Profile().setLocalizedFirstName("John")
              .setLocalizedLastName("Doe")
              .setProfilePicture(new ProfilePicture().setDisplayImage(new DigitalmediaAssetUrn("C5503AQHcc3HxiojpoA")))
              .setPositionsOrder(new LongArray(ImmutableList.of(1L, 2L)))
              .setPositions(new PositionMap(ImmutableMap.of("1",
                  new Position().setLocalizedCompanyName("Microsoft").setLocalizedTitle("Sr. Software Engineer")))))
          .build();
    }

    public NotificationsV2 getNotificationV2WithMissingRequiredFields(CommunicationDecorator decorator, Urn entityUrn,
        Urn actorUrn) {
      UserGeneratedContent userGeneratedContent = new UserGeneratedContent();
      UserGeneratedContent.SpecificContent specificContent = new UserGeneratedContent.SpecificContent();
      specificContent.setShareContent(new ShareContent().setShareCommentary(new AttributedText().setText("hello")));

      userGeneratedContent.setSpecificContent(specificContent);

      return MockedDecoratedNotificationBuilder.buildWith(decorator, MEMBER_URN, FS_LEAD_SHARED_UPDATE.name())
          .addItemUrnWithActor(entityUrn, actorUrn)
          .addRecord(entityUrn, "ugcPost", userGeneratedContent)
          .addRecord(actorUrn, "isbMini", new Profile().setLocalizedFirstName("John")
              .setLocalizedLastName("Doe")
              .setPositionsOrder(new LongArray(ImmutableList.of(1L, 2L)))
              .setPositions(
                  new PositionMap(ImmutableMap.of("1", new Position().setLocalizedTitle("Sr. Software Engineer")))))
          .build();
    }

    public CommunicationContext getCommunicationContext(String appId, String appVersion) {
      CommunicationContext communicationContext =
          new CommunicationContext().setCommunicationCategory(CommunicationCategory.IN_APP)
              .setLocale(DEFAULT_LOCALE)
              .setTargetEntity(MEMBER_URN)
              .setViewerPreferredTimeZone(DEFAULT_TIME_ZONE);
      if (appId != null) {
        communicationContext.setAppId(appId);
      }
      if (appVersion != null) {
        communicationContext.setAppVersion(appVersion);
      }
      return communicationContext;
    }
  }

  /**
   * Responsible to maintain data required for the tests.
   */
  private static final class MockData {
    final static AttributedTextParser attributedTextParser = new JsoupAttributedTextParser();
  }

  /**
   * Responsible to manage common stubbing variants for mock objects associated with the tests.
   */
  private static final class Stubbing {
    public static void stubDynamicResourceBundleManager(Mocks mocks) {
      // respond with the resource bundle stub instance.
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headerimage_a11y_text_v2"))).thenReturn(
          "Lead profile image");
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_v2_a11y_text"))).thenReturn(
          "Details about the lead shared update");
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_card_action_a11y_text"))).thenReturn(
          "Link to the shared update");
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_branding_insight_text"))).thenReturn(
          "Powered by Sales Navigator");
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_for_saved_lead"))).thenReturn(
          "Your saved lead <b>{:user,name,full}</b> at {:company} posted: {:comment}");
      when(mocks.resourceBundleHandler.getTemplate(any(),
          eq("com.linkedin.sales.notification.communicationplugin.fs_lead_shared_update_headline_for_potential_lead"))).thenReturn(
          "Your potential lead <b>{:user,name,full}</b> at {:company} posted: {:comment}");
    }
  }
}
