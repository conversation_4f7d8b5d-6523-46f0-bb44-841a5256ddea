package com.linkedin.sales.service.flagship;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.ScoredEntity;
import com.linkedin.common.ScoredEntityArray;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.container.ic.api.ICFinder;
import com.linkedin.container.ic.impl.InvocationContextImpl;
import com.linkedin.lix.dsl.v2.api.LixDslFactory;
import com.linkedin.lix.executor.IEvaluationContext;
import com.linkedin.lss.clients.SimpleSettingsClient;
import com.linkedin.mnybe.shared.identity.Entitlement;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.sales.client.common.MemberRestrictionClient;
import com.linkedin.sales.client.pymk.SalesPymkVeniceClient;
import com.linkedin.sales.monitoring.flagship.SalesPymkRecommendationsMetrics;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.PYMKUtils;
import com.linkedin.salespymk.GroupReason;
import com.linkedin.salespymk.PymkRecommendationsGroup;
import com.linkedin.settings.SettingValue;
import com.linkedin.settingsmt.enums.UspSettingType;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.flagship.SalesPYMKRecommendationsService.*;
import static org.mockito.Mockito.*;


public class SalesPYMKRecommendationServiceTest extends BaseEngineParTest {
  private static final Long TEST_MEMBER_ID = 135L;
  private static final Long VIEWEE_ID = 246L;
  private static final Long VIEWEE_ID_2 = 456L;
  private static final Long HIDDEN_VIEWEE_ID_3 = 468L;
  private static final Long HIDDEN_VIEWEE_ID_4 = 859L;
  private static final Long HIDDEN_VIEWEE_ID_5 = 948L;
  private static final Long HIDDEN_VIEWEE_ID_6 = 656L;
  private static final MemberUrn TEST_MEMBER_URN = new MemberUrn(TEST_MEMBER_ID);
  private static final MemberUrn VIEWEE_URN = new MemberUrn(VIEWEE_ID);
  private static final MemberUrn VIEWEE_URN2 = new MemberUrn(VIEWEE_ID_2);
  private static final MemberUrn HIDDEN_VIEWEE_URN_3 = new MemberUrn(HIDDEN_VIEWEE_ID_3);
  private static final MemberUrn HIDDEN_VIEWEE_URN_4 = new MemberUrn(HIDDEN_VIEWEE_ID_4);
  private static final MemberUrn HIDDEN_VIEWEE_URN_5 = new MemberUrn(HIDDEN_VIEWEE_ID_5);
  private static final MemberUrn HIDDEN_VIEWEE_URN_6 = new MemberUrn(HIDDEN_VIEWEE_ID_6);

  private static final Set<Long> MEMBER_ID_SET =
      ImmutableSet.of(VIEWEE_ID, VIEWEE_ID_2, HIDDEN_VIEWEE_ID_3, HIDDEN_VIEWEE_ID_4, HIDDEN_VIEWEE_ID_5,
          HIDDEN_VIEWEE_ID_6);
  private static final Map<Long, Boolean> RESTRICTION_MAP = ImmutableMap.of(
      VIEWEE_ID, Boolean.FALSE,
      VIEWEE_ID_2, Boolean.FALSE,
      HIDDEN_VIEWEE_ID_3, Boolean.FALSE,
      HIDDEN_VIEWEE_ID_4, Boolean.TRUE
  );
  private static final float SCORE = 0.9F;
  private static final ImmutableMap<MemberUrn, com.linkedin.common.TrackingId> MEMBER_URN_TRACKING_ID_MAP =
      ImmutableMap.of(VIEWEE_URN, PYMKUtils.createTrackingId(), VIEWEE_URN2, PYMKUtils.createTrackingId(),
          HIDDEN_VIEWEE_URN_3, PYMKUtils.createTrackingId(), HIDDEN_VIEWEE_URN_4, PYMKUtils.createTrackingId(),
          HIDDEN_VIEWEE_URN_5, PYMKUtils.createTrackingId(), HIDDEN_VIEWEE_URN_6, PYMKUtils.createTrackingId());
  private static final Map<Long, Map<UspSettingType, SettingValue>> SETTINGS_MAP = ImmutableMap.of(
      VIEWEE_ID, ImmutableMap.of(UspSettingType.allowShownInMeetTheTeam, SettingValue.createWithBoolean(Boolean.TRUE)),
      VIEWEE_ID_2, ImmutableMap.of(UspSettingType.allowShownInMeetTheTeam, SettingValue.createWithBoolean(Boolean.TRUE)),
      HIDDEN_VIEWEE_ID_4,
      ImmutableMap.of(UspSettingType.allowShownInMeetTheTeam, SettingValue.createWithBoolean(Boolean.TRUE)),
      HIDDEN_VIEWEE_ID_5,
      ImmutableMap.of(UspSettingType.blockUnwantedReconnectInvitations, SettingValue.createWithBoolean(Boolean.TRUE)),
      HIDDEN_VIEWEE_ID_6,
      ImmutableMap.of(UspSettingType.blockUnwantedReconnectInvitations, SettingValue.createWithBoolean(Boolean.TRUE),
          UspSettingType.blockUnwantedInvitations, SettingValue.createWithBoolean(Boolean.TRUE))
  );
  private static final String CLIENT_INFO_IC_KEY = "xLiTrack";
  private static final String WEB_X_LI_TRACK_JSON =
      "{\"clientVersion\":\"1.0.*\",\"osName\":\"web\",\"timezoneOffset\":-8,\"deviceFormFactor\":\"DESKTOP\",\"mpVersion\":\"9.99.9999\"}";

  @Mock private LixService _lixService;
  @Mock private ICFinder _icFinder;
  @Mock private MemberRestrictionClient _memberRestrictionClient;
  @Mock private PremiumEntitlementsService _premiumEntitlementsService;
  @Mock private SalesPymkRecommendationsMetrics _salesPymkRecommendationsMetrics;
  @Mock private SalesPymkVeniceClient _salesPymkVeniceClient;
  @Mock private SimpleSettingsClient _simpleSettingsClient;

  private SalesPYMKRecommendationsService _pymkService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);

    // Set a default IC for all tests
    InvocationContextImpl ic = new InvocationContextImpl();
    ic.setValue(CLIENT_INFO_IC_KEY, WEB_X_LI_TRACK_JSON);
    when(_icFinder.findCurrent()).thenReturn(ic);

    _pymkService = new SalesPYMKRecommendationsService(_lixService, _icFinder, _memberRestrictionClient,
        _premiumEntitlementsService, _salesPymkRecommendationsMetrics, _salesPymkVeniceClient, _simpleSettingsClient);
  }

  @Test(description = "Test that it returns nothing if the gating lix is control")
  public void testFindRecommendationsMockWithoutLix() {
    when(_lixService.getLixTreatment(TEST_MEMBER_URN, LixUtils.LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION, null))
        .thenReturn(Task.value("control"));
    BasicCollectionResult<PymkRecommendationsGroup> result = runAndWait(_pymkService.findRecommendations(TEST_MEMBER_URN,
        GroupReason.DECISION_MAKERS));
    Assert.assertEquals(result, new BasicCollectionResult<>(Collections.emptyList()));
  }

  @Test(description = "Test that it returns mock data if gating lix is mockData")
  public void testFindRecommendationsWithLixMockData() {
    when(_lixService.getLixTreatment(TEST_MEMBER_URN, LixUtils.LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION, null))
        .thenReturn(Task.value("mockData"));

    BasicCollectionResult<PymkRecommendationsGroup> result = runAndWait(_pymkService.findRecommendations(TEST_MEMBER_URN,
        GroupReason.DECISION_MAKERS));
    Assert.assertEquals(result.getElements().get(0).getRecommendations().size(), 11);
  }

  @Test(description = "Test that it returns recommendations, all lixes enabled, filtered by invitation privacy settings")
  public void TestFindRecommendationsWithLixEnabled() {
    when(_lixService.getLixTreatment(TEST_MEMBER_URN, LixUtils.LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION, null))
        .thenReturn(Task.value("enabled"));
    when(_salesPymkVeniceClient.getFreemiumRecommendations(TEST_MEMBER_URN, GroupReason.DECISION_MAKERS))
        .thenReturn(Task.value(
            new PymkRecommendationsGroup()
                .setRecommendations(new ScoredEntityArray(MEMBER_URN_TRACKING_ID_MAP.entrySet().stream().map(
                    entry -> new ScoredEntity()
                    .setEntity(entry.getKey())
                    .setRecommendationTrackingId(entry.getValue())
                    .setScore(SCORE))
                    .collect(Collectors.toList())))
                .setGroupReason(GroupReason.DECISION_MAKERS)
        ));
    when(_simpleSettingsClient.batchGet(MEMBER_ID_SET, INVITATION_SETTINGS)).thenReturn(Task.value(SETTINGS_MAP));
    when(_memberRestrictionClient.areMembersRestricted(ArgumentMatchers.anyCollection(), eq(TEST_MEMBER_ID))).thenReturn(
        RESTRICTION_MAP);
    when(_premiumEntitlementsService.getEntitlements(TEST_MEMBER_URN, ENTITLEMENTS_TO_CHECK)).thenReturn(
        Task.value(Collections.emptySet()));

    BasicCollectionResult<PymkRecommendationsGroup> result = runAndWait(_pymkService.findRecommendations(TEST_MEMBER_URN,
        GroupReason.DECISION_MAKERS));

    // Check that only viewee 1 and 2 are returned, since 3 is hidden due to visibility settings
    // and 4 is hidden due to M2M restrictions
    Set<Long> midResults = result.getElements().get(0).getRecommendations().stream().map(
        scoredEntity -> scoredEntity.getEntity().getIdAsLong()).collect(Collectors.toSet());
    Assert.assertEquals(midResults, ImmutableSet.of(VIEWEE_ID, VIEWEE_ID_2));
  }

  @Test(description = "Test that it returns no recommendations for premium members")
  public void TestFindRecommendationsWithPremium() {
    when(_lixService.getLixTreatment(TEST_MEMBER_URN, LixUtils.LSS_PEOPLE_YOU_MAY_KNOW_RECOMMENDATION, null))
        .thenReturn(Task.value("enabled"));
    when(_premiumEntitlementsService.getEntitlements(TEST_MEMBER_URN, ENTITLEMENTS_TO_CHECK)).thenReturn(
        Task.value(ImmutableSet.of(Entitlement.CAN_ACCESS_SALES)));

    BasicCollectionResult<PymkRecommendationsGroup> result = runAndWait(_pymkService.findRecommendations(TEST_MEMBER_URN,
        GroupReason.DECISION_MAKERS));

    // Check that it returns no results for premium members
    Assert.assertEquals(result.getElements().size(), 0);
  }
}
