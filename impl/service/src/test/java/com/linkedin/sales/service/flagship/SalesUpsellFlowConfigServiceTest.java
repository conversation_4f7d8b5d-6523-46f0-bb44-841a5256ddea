package com.linkedin.sales.service.flagship;

import proto.com.linkedin.common.MemberUrn;
import proto.com.linkedin.comms.Image;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import proto.com.linkedin.premium.PremiumFlowContext;
import proto.com.linkedin.premium.PremiumUpsellButton;
import proto.com.linkedin.premium.PremiumUpsellCardData;
import proto.com.linkedin.premium.PremiumUpsellFlowContext;
import proto.com.linkedin.premium.PremiumUpsellSlot;
import proto.com.linkedin.premium.PremiumUtype;
import com.linkedin.sales.client.flagship.PremiumUpsellFlowsClient;
import java.util.List;
import java.util.Objects;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.Assert;
import proto.com.linkedin.salesupsell.UpsellFlowConfig;

import static org.mockito.Mockito.*;


public class SalesUpsellFlowConfigServiceTest extends BaseEngineParTest {
  private static final MemberUrn TEST_MEMBER_URN = MemberUrn.newBuilder().setMemberId(135L).build();
  private static final PremiumFlowContext TEST_PREMIUM_FLOW_CONTEXT = PremiumFlowContext.newBuilder().build();
  private static final PremiumUpsellFlowContext TEST_PREMIUM_UPSELL_FLOW_CONTEXT = PremiumUpsellFlowContext.newBuilder()
      .setMember(TEST_MEMBER_URN)
      .setPremiumFlowContext(TEST_PREMIUM_FLOW_CONTEXT).build();
  private static final PremiumUpsellButton TEST_PREMIUM_PRIMARY_CTA = PremiumUpsellButton.newBuilder()
      .setActionUrl("www.linkedin.com")
      .setControlName("test_upsell_order_origin_click")
      .setCtaText("ctaText").build();
  private static final PremiumUpsellCardData TEST_PREMIUM_UPSELL_CARD = PremiumUpsellCardData.newBuilder()
      .setUpsellOrderOrigin("test_upsell_order_origin")
      .setUtype(PremiumUtype.PremiumUtype_SALES)
      .setTitle("title")
      .setSubtitle("subtitle")
      .setFooterText("footer")
      .setPrimaryCta(TEST_PREMIUM_PRIMARY_CTA)
      .setImage(Image.newBuilder().build())
      .build();

  private static final PremiumUpsellSlot TEST_UPSELL_SLOT_ID =
      PremiumUpsellSlot.PremiumUpsellSlot_COMPANY_SALES_NAVIGATOR_ACCOUNT_IQ_DISCOVERY_UPSELL;

  @Mock
  private PremiumUpsellFlowsClient _premiumUpsellFlowsClient;
  private SalesUpsellFlowConfigService _service;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.openMocks(this);
    _service = new SalesUpsellFlowConfigService(_premiumUpsellFlowsClient);
  }

  @Test
  public void testGetUpsellConfig() {
    when(_premiumUpsellFlowsClient.getPremiumUpsellCardData(TEST_MEMBER_URN, TEST_UPSELL_SLOT_ID, TEST_PREMIUM_UPSELL_FLOW_CONTEXT))
        .thenReturn(Task.value(TEST_PREMIUM_UPSELL_CARD));
    List<UpsellFlowConfig> actual =
        runAndWait(_service.getUpsellConfig(TEST_MEMBER_URN, TEST_UPSELL_SLOT_ID, TEST_PREMIUM_FLOW_CONTEXT));
    Assert.assertEquals(actual.size(), 1);
    UpsellFlowConfig config = actual.get(0);
    Assert.assertEquals(config.getControlName(), "test_upsell_order_origin_click");
    Assert.assertEquals(config.getUpsellSlot(), TEST_UPSELL_SLOT_ID.toString());
    Assert.assertEquals(config.getMember(), TEST_MEMBER_URN);
    Assert.assertEquals(config.getTitle(), "title");
    Assert.assertEquals(config.getSubtitle(), "subtitle");
    Assert.assertEquals(config.getFooterText(), "footer");
    Assert.assertTrue(config.hasUpsellCTA());
    Assert.assertEquals(Objects.requireNonNull(config.getUpsellCTA()).getCtaText(), "ctaText");
  }

  @Test
  public void testGetUpsellError() {
    when(_premiumUpsellFlowsClient.getPremiumUpsellCardData(any(), any(), any())).thenReturn(Task.failure(new RuntimeException()));
    Assert.assertTrue(runAndWait(_service.getUpsellConfig(TEST_MEMBER_URN, TEST_UPSELL_SLOT_ID, TEST_PREMIUM_FLOW_CONTEXT)).isEmpty());
  }
}
