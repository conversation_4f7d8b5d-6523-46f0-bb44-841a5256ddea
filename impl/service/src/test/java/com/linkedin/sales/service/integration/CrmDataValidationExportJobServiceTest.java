package com.linkedin.sales.service.integration;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.UrlArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.SalesConnectedCrmSetting;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CollectionResponse;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.CrmDataValidationExportJob;
import com.linkedin.sales.CrmDataValidationExportJobStatus;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.client.integration.CrmSettingClient;
import com.linkedin.sales.ds.db.CrmDataValidationExportJobDB;
import com.linkedin.sales.model.CrmDataValidationServiceErrorCode;
import com.linkedin.sales.monitoring.CounterMetricsSensor;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus.*;
import static com.linkedin.sales.service.integration.CrmDataValidationExportJobService.*;
import static org.mockito.Mockito.*;


/**
 * Created by jiawang on 8/16/2018
 * This is the class to test {@link CrmDataValidationExportJobService}
 */
public class CrmDataValidationExportJobServiceTest extends ServiceUnitTest {
  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final Long CRM_PAIRING_ID = 2L;
  private static final CrmPairingUrn CRM_PAIRING_URN = new CrmPairingUrn(CONTRACT_URN, CRM_PAIRING_ID);

  private CrmInstanceUrn _crmInstanceUrn;
  private String _crmInstanceId;
  private CrmDataValidationExportJobService _crmDataValidationExportJobService;
  @Mock
  private CrmDataValidationExportJobDB _crmDataValidationExportJobDB;
  @Mock
  private CounterMetricsSensor _counterMetricsSensor;
  @Mock
  private CrmPairingClient _crmPairingClient;
  @Mock
  private CrmSettingClient _crmSettingClient;
  @Mock
  private LixService _lixService;
  @Mock
  private SalesExternalizationService _salesExternalizationService;

  @BeforeMethod(alwaysRun = true)
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    _crmDataValidationExportJobService =
        new CrmDataValidationExportJobService(_crmPairingClient, _crmDataValidationExportJobDB,
            _crmSettingClient, _salesExternalizationService, _lixService, _counterMetricsSensor,
            Collections.emptySet());
    _crmInstanceId = "00D6g000004ZTklEAG";
    _crmInstanceUrn = CrmInstanceUrn.deserialize(String.format("urn:li:crmInstance:(SFDC,%s)", _crmInstanceId));
    when(_lixService.isCrmInstanceBasedLixEnabled(any(), eq(LixUtils.LSS_CRM_DV_FILTER_FILES_FOR_LAST_ONE_DAY))).thenReturn(
        Task.value(false));
  }

  @Test
  public void testCreateJobWithBulkExportEnabled() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(
        Collections.singletonList(new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(CRM_PAIRING_ID)));
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    long jobId = await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    Assert.assertEquals(jobId, 10000L);
  }

  @Test
  public void testCreateJobWithRequestNotTrusted() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(false));

    try {
      await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_403_FORBIDDEN);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.REQUEST_NOT_TRUSTED.getCode());
    }
  }

  @Test
  public void testCreateJobWithNotConnectedToSN() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(Collections.emptyList());
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    try {
      await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_403_FORBIDDEN);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.NOT_SYNCED_TO_SN.getCode());
    }
  }

  @Test
  public void testCreateJobWithFeatureNotEnabled() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(
        Collections.singletonList(new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(CRM_PAIRING_ID)));
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(false))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    try {
      await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_403_FORBIDDEN);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.FEATURE_NOT_ENABLED.getCode());
    }
  }

  @Test
  public void testCreateJobWithFeatureNotEnabledNullPref() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(collectionResponse.getElements()).thenReturn(
        Collections.singletonList(new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(CRM_PAIRING_ID)));
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting()));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    try {
      await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_403_FORBIDDEN);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.FEATURE_NOT_ENABLED.getCode());
    }
  }

  @Test
  public void testCreateJobWithMultipleContractsAndOneOfWhichEnabled() throws Exception {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(1L).setExportEndAt(2L);

    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    ContractUrn contractUrn2 = new ContractUrn(11L);
    long crmPairingId2 = 222L;
    when(collectionResponse.getElements()).thenReturn(
        Arrays.asList(new CrmPairing().setContract(contractUrn2).setCrmPairingId(crmPairingId2),
            new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(CRM_PAIRING_ID)));
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    when(_crmSettingClient.get(new CrmPairingUrn(contractUrn2, crmPairingId2),
        SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(false))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    long jobId = await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    Assert.assertEquals(jobId, 10000L);
  }

  @Test
  public void testCreateJobWithInvalidExportDate() {
    CrmDataValidationExportJob pegasusExportJob =
        new CrmDataValidationExportJob().setExportStartAt(5L).setExportEndAt(2L);
    CollectionResponse<CrmPairing> collectionResponse = mock(CollectionResponse.class);
    when(_crmPairingClient.findByCrmInstance(_crmInstanceUrn, true)).thenReturn(Task.value(collectionResponse));
    when(collectionResponse.getElements()).thenReturn(
        Collections.singletonList(new CrmPairing().setContract(CONTRACT_URN).setCrmPairingId(CRM_PAIRING_ID)));
    when(_crmSettingClient.get(CRM_PAIRING_URN, SalesConnectedCrmSettingType.DATA_VALIDATION_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    try {
      await(_crmDataValidationExportJobService.createJob(pegasusExportJob, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_400_BAD_REQUEST);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.INVALID_EXPORT_DATE.getCode());
    }
  }

  @Test
  public void testGetJobCompletedWithBulkExport() {
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob1 =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob1.setExecutionId(5L);
    avroExportJob1.setExportStartTime(1L);
    avroExportJob1.setExportEndTime(2L);
    avroExportJob1.setCreatedTime(new Date().getTime());
    avroExportJob1.setAmbryBlobId("ambryBlobId1");
    avroExportJob1.setStatus(COMPLETED_WITH_BULK_EXPORT);
    avroExportJob1.setNextStartTime(200L);
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob2 =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob2.setExecutionId(5L);
    avroExportJob2.setExportStartTime(1L);
    avroExportJob2.setExportEndTime(2L);
    avroExportJob2.setCreatedTime(new Date().getTime() + 1000L);
    avroExportJob2.setAmbryBlobId("ambryBlobId2");
    avroExportJob2.setStatus(COMPLETED_WITH_BULK_EXPORT);
    avroExportJob2.setNextStartTime(200L);
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(
        ImmutableList.of(avroExportJob1, avroExportJob2)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    when(_salesExternalizationService.createSignedAmbryUrls(new String[]{"ambryBlobId2"}, null,
        DOWNLOAD_URL_TTL)).thenReturn(new UrlArray(Collections.singleton(new Url("url"))));
    CrmDataValidationExportJob actual = await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    Assert.assertEquals(actual.getJobId().longValue(), 10L);
    Assert.assertEquals(actual.getExportStartAt().longValue(), 1L);
    Assert.assertEquals(actual.getExportEndAt().longValue(), 2L);
    Assert.assertEquals(actual.getStatus(), CrmDataValidationExportJobStatus.COMPLETED);
    Assert.assertTrue(actual.hasNextExportStartAt());
    Assert.assertEquals(actual.getDownloadUrls().get(0), new Url("url"));
    Assert.assertTrue(actual.hasExpireAt());
  }

  @Test
  public void testGetJobNoRecordWithBulkExport() {
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    when(_salesExternalizationService.putBlob(any(), any())).thenReturn("ambryBlobId");
    when(_salesExternalizationService.createSignedAmbryUrls(new String[]{"ambryBlobId"}, null,
        DOWNLOAD_URL_TTL)).thenReturn(new UrlArray(Collections.singleton(new Url("url"))));
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(
        Collections.emptyList()));
    CrmDataValidationExportJob actual = await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    Assert.assertEquals(actual.getJobId().longValue(), 10L);
    Assert.assertEquals(actual.getStatus(), CrmDataValidationExportJobStatus.COMPLETED);
    Assert.assertEquals(actual.getDownloadUrls().get(0), new Url("url"));
    Assert.assertTrue(actual.hasExpireAt());
    Assert.assertTrue(actual.hasNextExportStartAt());
  }

  @Test
  public void testGetJobEmptyAmbryFilesWithBulkExport() {
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob.setExecutionId(5L);
    avroExportJob.setExportStartTime(1L);
    avroExportJob.setExportEndTime(2L);
    avroExportJob.setCreatedTime(new Date().getTime());
    avroExportJob.setStatus(COMPLETED_WITH_BULK_EXPORT);
    avroExportJob.setNextStartTime(200L);
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(
        ImmutableList.of(avroExportJob)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    try {
      await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.BULK_EXPORT_MISSING_AMBRY_BLOB_ID.getCode());
    }
  }

  @Test(description = "Verify that an empty Ambry file is returned if most recent was generated 24 hours ago")
  public void testGetJobCompletedWithBulkExport_NoAmbryFilesInLast24hours() {
    long timeTwoDayAgo = System.currentTimeMillis() - (2 * 24 * 60 * 60 * 1000);
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob1 =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob1.setExecutionId(5L);
    avroExportJob1.setExportStartTime(1L);
    avroExportJob1.setExportEndTime(2L);
    avroExportJob1.setCreatedTime(timeTwoDayAgo);
    avroExportJob1.setAmbryBlobId("ambryBlobId1");
    avroExportJob1.setStatus(COMPLETED_WITH_BULK_EXPORT);
    avroExportJob1.setNextStartTime(200L);
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(
        ImmutableList.of(avroExportJob1)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    when(_salesExternalizationService.putBlob(any(), any())).thenReturn("EmptyAmbryBlobId");
    when(_salesExternalizationService.createSignedAmbryUrls(new String[]{"EmptyAmbryBlobId"}, null,
        DOWNLOAD_URL_TTL)).thenReturn(new UrlArray(Collections.singleton(new Url("UrlForEmptyAmbryBlobId"))));
    when(_lixService.isCrmInstanceBasedLixEnabled(any(), eq(LixUtils.LSS_CRM_DV_FILTER_FILES_FOR_LAST_ONE_DAY))).thenReturn(
        Task.value(true));


    CrmDataValidationExportJob actual = await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    // verify using putBlob call that empty blob has been created which wouldn't be created otherwise.
    verify(_salesExternalizationService, times(1)).putBlob(any(), any());
    // verify that the url in the result is for empty ambry blob
    Assert.assertEquals(actual.getDownloadUrls().get(0), new Url("UrlForEmptyAmbryBlobId"));
  }

  @Test
  public void testGetJobWithRequestNotTrusted() {
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(false));
    try {
      await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_403_FORBIDDEN);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.REQUEST_NOT_TRUSTED.getCode());
    }
  }

  @Test
  public void testGetJobWithAmbryError() {
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob.executionId = 5L;
    avroExportJob.exportStartTime = 1L;
    avroExportJob.exportEndTime = 2L;
    avroExportJob.createdTime = new Date().getTime();
    avroExportJob.ambryBlobId = "ambryBlobId";
    avroExportJob.status = COMPLETED_WITH_BULK_EXPORT;
    avroExportJob.nextStartTime = 200L;
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(Collections.singletonList(avroExportJob)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    when(_salesExternalizationService.createSignedAmbryUrls(new String[]{"ambryBlobId"}, null,
        DOWNLOAD_URL_TTL)).thenThrow(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR));

    try {
      await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getServiceErrorCode().intValue(),
          CrmDataValidationServiceErrorCode.AMBRY_URL_CREATION_FAILURE.getCode());
    }
  }

  @Test
  public void testGetJobCompletedNoAmbryId() {
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob.executionId = 5L;
    avroExportJob.exportStartTime = 1L;
    avroExportJob.exportEndTime = 2L;
    avroExportJob.createdTime = 1L;
    avroExportJob.status = COMPLETED_WITH_BULK_EXPORT;
    avroExportJob.nextStartTime = 200L;
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenReturn(Task.value(Collections.singletonList(avroExportJob)));
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));

    try {
      await(_crmDataValidationExportJobService.getJob(10L, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
  }

  @Test
  public void testGetJobNotFound() {
    when(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)).thenReturn(Task.value(true));
    when(_crmDataValidationExportJobDB.find(_crmInstanceId)).thenThrow(
        new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));

    try {
      await(_crmDataValidationExportJobService.getJob(11L, _crmInstanceUrn));
    } catch (Exception e) {
      Assert.assertEquals(e.getCause().getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e.getCause()).getStatus(), HttpStatus.S_404_NOT_FOUND);
    }
  }

  @Test
  public void testConvertFromAvro() {
    com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob avroExportJob =
        new com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob();
    avroExportJob.executionId = 5L;
    avroExportJob.exportStartTime = 1L;
    avroExportJob.exportEndTime = 2L;
    avroExportJob.createdTime = 1L;
    avroExportJob.status = COMPLETED_WITH_BULK_EXPORT;
    CrmDataValidationExportJob pegasusJob = new CrmDataValidationExportJob().setJobId(100L)
        .setExportStartAt(1L)
        .setExportEndAt(2L)
        .setStatus(CrmDataValidationExportJobStatus.COMPLETED);

    Assert.assertEquals(runAndWait(_crmDataValidationExportJobService.convertFromAvro(avroExportJob, 100L, "crm")),
        pegasusJob);
  }
}
