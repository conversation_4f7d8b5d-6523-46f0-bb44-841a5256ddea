package com.linkedin.sales.service.integration;

import com.linkedin.ambry.client.AmbryBlobPropertiesBuilder;
import com.linkedin.ambry.client.AmbryClient;
import com.linkedin.ambry.client.AmbryClientException;
import com.linkedin.ambry.client.AmbryErrorCode;
import com.linkedin.common.UrlArray;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.CrmInstanceUrn;
import com.linkedin.common.urn.DeveloperApplicationUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.crm.CrmSource;
import com.linkedin.parseq.Task;
import com.linkedin.platform.ProvisionedApplication;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restligateway.util.GatewayCallerFinder;
import com.linkedin.restligateway.util.GatewayCallerIdentity;
import com.linkedin.sales.client.externalization.ProvisionedApplicationClient;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.UrnUtils;
import edu.umd.cs.findbugs.annotations.NonNull;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.io.ByteArrayInputStream;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


/**
 * Unit test for {@link SalesExternalizationService}
 * <AUTHOR>
 */
public class SalesExternalizationServiceTest extends ServiceUnitTest {
  private final static Duration DOWNLOAD_URL_TTL = Duration.of(1, ChronoUnit.MINUTES);
  private CrmInstanceUrn _crmInstanceUrn;
  private String _crmInstanceId;
  private String _crmSource;
  private int _applicationId;
  private DeveloperApplicationUrn _developerApplicationUrn;
  private SalesExternalizationService _salesExternalizationService;

  @Mock
  private GatewayCallerFinder _gatewayCallerFinder;
  @Mock
  private ProvisionedApplicationClient _provisionedApplicationClient;
  @Mock
  private AmbryClient _ambryClient;

  @BeforeMethod(alwaysRun = true)
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    _salesExternalizationService =
        new SalesExternalizationService(_gatewayCallerFinder, _provisionedApplicationClient, _ambryClient);
    _crmInstanceId = "xxxxxxx";
    _crmSource = CrmSource.SFDC.toString();
    _crmInstanceUrn = CrmInstanceUrn.deserialize(String.format("urn:li:crmInstance:(SFDC,%s)", _crmInstanceId));
    _applicationId = 1;
    _developerApplicationUrn = UrnUtils.createDeveloperApplicationUrn(_applicationId);
    when(_gatewayCallerFinder.getCaller()).thenReturn(new GatewayCallerIdentity(0L, _applicationId, 2));
  }

  @Test
  public void testIsRequestTrustedForApplication() {
    when(_provisionedApplicationClient.get(_developerApplicationUrn)).thenReturn(
        Task.value(new ProvisionedApplication().setUniqueForeignId("xyz")));

    Assert.assertFalse(runAndWait(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)));
  }

  @Test
  public void testIsRequestTrustedForApplicationWithException() {
    when(_provisionedApplicationClient.get(_developerApplicationUrn)).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));

    Assert.assertFalse(runAndWait(_salesExternalizationService.isRequestTrustedForApplication(_crmInstanceUrn)));
  }

  @Test
  public void testCreateSignedAmbryUrls() throws Exception {
    when(_ambryClient.getSignedUrl(any())).thenReturn("url");
    Assert.assertEquals(
        _salesExternalizationService.createSignedAmbryUrls(new String[]{"blobId"}, null, DOWNLOAD_URL_TTL),
        new UrlArray(Collections.singleton(new Url("url"))));
  }

  @Test
  public void testCreateSignedAmbryUrlsForEmptyData() {
    SalesExternalizationService underTest =
        new SalesExternalizationService(_gatewayCallerFinder, _provisionedApplicationClient, _ambryClient) {
          @NonNull
          Url createSignedAmbryUrl(@NonNull DeveloperApplicationUrn developerApplicationUrn,
              @NonNull String ambryBlobId, @Nullable MemberUrn memberUrn, @NonNull Duration urlTtl)
              throws RestLiServiceException {
            throw new RuntimeException(); // Test to make sure this method is not called
          }
        };

    Assert.assertTrue(underTest.createSignedAmbryUrls(new String[]{""}, null, DOWNLOAD_URL_TTL).isEmpty());
  }

  @Test
  public void testCreateSignedAmbryUrl() throws Exception {
    when(_ambryClient.getSignedUrl(any())).thenReturn("url");
    Assert.assertEquals(_salesExternalizationService.createSignedAmbryUrl("blobId", null, DOWNLOAD_URL_TTL),
        new Url("url"));
  }

  @Test
  public void testCreateSignedAmbryUrlWithException() throws Exception {
    when(_ambryClient.getSignedUrl(any())).thenThrow(
        new AmbryClientException("Error", AmbryErrorCode.Internal_Server_Error));
    try {
      _salesExternalizationService.createSignedAmbryUrl("blobId", null, DOWNLOAD_URL_TTL);
    } catch (Exception e) {
      Assert.assertEquals(e.getClass(), RestLiServiceException.class);
      Assert.assertEquals(((RestLiServiceException) e).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    }
  }

  @Test
  public void testPutBlob() throws Exception {
    when(_ambryClient.putBlob(any(), any())).thenReturn("blobId");
    Assert.assertEquals(_salesExternalizationService.putBlob(new AmbryBlobPropertiesBuilder("text/csv",
            "LssCrmDataValidation", _crmInstanceUrn).build(), new ByteArrayInputStream(new byte[]{})),
        "blobId");
  }
}
