package com.linkedin.sales.service.lbep;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.url.Url;
import com.linkedin.common.urn.CsUserUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.EnterpriseLicenseType;
import com.linkedin.enterprise.account.ApplicationInstanceType;
import com.linkedin.enterprise.acl.EisRole;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignment;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentArray;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentKey;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentState;
import com.linkedin.enterprise.license.LicenseAssignmentStatus;
import com.linkedin.enterprise.license.LicenseAssignmentStatusEnum;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.platform.email.EnterpriseOnboardingEmailCustomData;
import com.linkedin.platform.email.EnterpriseOnboardingEmailTriggerContext;
import com.linkedin.platform.email.LicenseAssignment;
import com.linkedin.platform.email.LicenseAssignmentArray;
import com.linkedin.platform.email.RenderingProduct;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.SalesNavigatorEmailService;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.lbep.EnterpriseOnboardingEmailTriggerContextBuilder.*;
import static org.mockito.Mockito.*;


public class EnterpriseEmailPluginsServiceTest extends BaseEngineParTest {

  private static final Urn DEFAULT_VIEWER = new CsUserUrn(1L);

  private static final LicenseAssignmentArray CORE_LICENSE_ASSIGNMENT = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER1.getUrn())
  ));
  private static final LicenseAssignmentArray TLE_LICENSE_ASSIGNMENT = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TEAMLINK_EXTEND.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED))
  ));
  private static final LicenseAssignmentArray LICENSE_ASSIGNMENTS = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER1.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED)),
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER2.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED))
  ));
  private static final EnterpriseRoleAssignmentArray ADMIN_ROLE_ASSIGNMENT = new EnterpriseRoleAssignmentArray(
      ImmutableSet.of(
          new EnterpriseRoleAssignment().setKey(new EnterpriseRoleAssignmentKey().setRole(
              EisRole.SALES_NAVIGATOR_TEAMSKU_PRODUCT_ADMIN.getUrn()))
      ));
  private static final EnterpriseRoleAssignmentArray UR_ROLE_ASSIGNMENT = new EnterpriseRoleAssignmentArray(
      ImmutableSet.of(
          new EnterpriseRoleAssignment().setKey(new EnterpriseRoleAssignmentKey().setRole(
              EisRole.SALES_NAVIGATOR_REPORTING_ADMIN.getUrn()))
      ));
  private static final EnterpriseRoleAssignmentArray REVOKED_ROLE_ASSIGNMENT = new EnterpriseRoleAssignmentArray(
      ImmutableSet.of(
          new EnterpriseRoleAssignment().setKey(new EnterpriseRoleAssignmentKey().setRole(
              EisRole.SALES_NAVIGATOR_REPORTING_ADMIN.getUrn())).setState(EnterpriseRoleAssignmentState.REVOKED),
          new EnterpriseRoleAssignment().setKey(new EnterpriseRoleAssignmentKey().setRole(
              EisRole.SALES_NAVIGATOR_TEAMSKU_PRODUCT_ADMIN.getUrn())).setState(EnterpriseRoleAssignmentState.APPROVED)
      ));
  private static final EnterpriseRoleAssignmentArray LEARNING_ROLE_ASSIGNMENT = new EnterpriseRoleAssignmentArray(
      ImmutableSet.of(
          new EnterpriseRoleAssignment().setKey(new EnterpriseRoleAssignmentKey().setRole(
              EisRole.LEARNING_ASSIGNMENTS_ADMIN.getUrn()))
      ));

  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_T2_LICENSE = new EnterpriseOnboardingEmailTriggerContextBuilder().build();
  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_T4_LICENSE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setCurrentLicenseAssignments(TIER_4_LICENSE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_T2_LICENSE_ADMIN_ROLE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setCurrentAclRoleAssignments(ADMIN_ROLE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_T4_LICENSE_ADMIN_ROLE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setCurrentLicenseAssignments(TIER_4_LICENSE_ASSIGNMENT)
      .setCurrentAclRoleAssignments(ADMIN_ROLE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_TLE_LICENSE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setCurrentLicenseAssignments(TLE_LICENSE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext VALID_CONTEXT_WITH_ONLY_ADMIN_ROLE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(null)
          .setCurrentAclRoleAssignments(ADMIN_ROLE_ASSIGNMENT).build();

  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NOT_SN_APP = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setApplication(EnterpriseApplication.LEARNING.getUrn()).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NOT_SN_APP_TYPE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setApplicationInstanceType(ApplicationInstanceType.LEARNING_DEFAULT).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NULL_CONTRACT = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setContract(null).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NULL_APP_INST = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setApplicationInstance(null).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NULL_BUDGET_GRP = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setBudgetGroup(null).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_NULL_PROFILE = new EnterpriseOnboardingEmailTriggerContextBuilder()
      .setProfile(null).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_LEARNING_CURRENT_LICENSE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(LEARNING_LICENSE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_NO_CURRENT_LICENSE_OR_ROLE =
      new EnterpriseOnboardingEmailTriggerContextBuilder().setCurrentLicenseAssignments(null).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_MORE_THAN_ONE_CURR_LICENSE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(LICENSE_ASSIGNMENTS).build();
  private static final EnterpriseOnboardingEmailTriggerContext CONTEXT_WITH_ONLY_UR_ROLE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(null)
          .setCurrentAclRoleAssignments(UR_ROLE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext CONTEXT_WITH_REVOKED_AND_APPROVED_ROLE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(null)
          .setCurrentAclRoleAssignments(REVOKED_ROLE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_NON_LSS_ROLE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(null)
          .setCurrentAclRoleAssignments(LEARNING_ROLE_ASSIGNMENT).build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_ACTIVATED_STATUS_T2_LICENSE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(new LicenseAssignmentArray(ADVANCED_LICENSE_ASSIGNMENT_ACTIVATED))
          .build();
  private static final EnterpriseOnboardingEmailTriggerContext INVALID_CONTEXT_WITH_DECLINE_STATUS_TLE_LICENSE =
      new EnterpriseOnboardingEmailTriggerContextBuilder()
          .setCurrentLicenseAssignments(new LicenseAssignmentArray(TLE_LICENSE_ASSIGNMENT_DECLINED))
          .build();

  private static final Url REGULAR_REDIRECT_URL =
      new Url("https://www.linkedin-ei.com/sales/activate?applicationInstance=urn%3Ali%3AenterpriseApplicationInstance%3A%28urn%3Ali%3AenterpriseAccount%3A14226404%2C17312596%29&enterpriseProfile=urn%3Ali%3AenterpriseProfile%3A%28urn%3Ali%3AenterpriseAccount%3A14226404%2C25862467%29&isTLE=false");
  private static final Url TLE_REDIRECT_URL =
      new Url("https://www.linkedin-ei.com/sales/activate?applicationInstance=urn%3Ali%3AenterpriseApplicationInstance%3A%28urn%3Ali%3AenterpriseAccount%3A14226404%2C17312596%29&enterpriseProfile=urn%3Ali%3AenterpriseProfile%3A%28urn%3Ali%3AenterpriseAccount%3A14226404%2C25862467%29&isTLE=true");

  @Mock
  SalesNavigatorEmailService _emailService;
  @Mock
  EnterpriseEmailPluginsServiceMetrics _metrics;
  @Mock
  LixService _lixService;

  private EnterpriseEmailPluginsServiceImpl _enterpriseEmailPluginsService;


  @BeforeMethod
  public void setUp() throws Exception {
    MockitoAnnotations.initMocks(this);
    _enterpriseEmailPluginsService = new EnterpriseEmailPluginsServiceImpl(_emailService, _metrics, _lixService);
  }

  @DataProvider
  private static Object[][] dataForTestShouldSendOnboardingEmail() {
    return new Object[][]{
        {VALID_CONTEXT_WITH_T2_LICENSE, true},
        {VALID_CONTEXT_WITH_T4_LICENSE, true},
        {VALID_CONTEXT_WITH_T2_LICENSE_ADMIN_ROLE, true},
        {VALID_CONTEXT_WITH_T4_LICENSE_ADMIN_ROLE, true},
        {VALID_CONTEXT_WITH_TLE_LICENSE, true},
        {null, false},
        {INVALID_CONTEXT_NULL_APP_INST, false},
        {INVALID_CONTEXT_NULL_BUDGET_GRP, false},
        {INVALID_CONTEXT_NULL_PROFILE, false},
        {INVALID_CONTEXT_NULL_CONTRACT, false},
        {INVALID_CONTEXT_WITH_NO_CURRENT_LICENSE_OR_ROLE, false},
        {INVALID_CONTEXT_WITH_MORE_THAN_ONE_CURR_LICENSE, false},
        {CONTEXT_WITH_ONLY_UR_ROLE, true},
        {INVALID_CONTEXT_WITH_LEARNING_CURRENT_LICENSE, false},
        {INVALID_CONTEXT_WITH_NON_LSS_ROLE, false},
        {INVALID_CONTEXT_WITH_ACTIVATED_STATUS_T2_LICENSE, false},
        {INVALID_CONTEXT_WITH_DECLINE_STATUS_TLE_LICENSE, false},
        {CONTEXT_WITH_REVOKED_AND_APPROVED_ROLE, true},
        {CONTEXT_WITH_REVOKED_AND_APPROVED_ROLE, true}
    };
  }
  @Test(dataProvider = "dataForTestShouldSendOnboardingEmail")
  public void testShouldSendOnboardingEmail(EnterpriseOnboardingEmailTriggerContext context, Boolean expected) {
    Boolean actual = runAndWait(_enterpriseEmailPluginsService.shouldSendOnboardingEmail(context, DEFAULT_VIEWER));
    Assert.assertEquals(expected, actual);
  }

  @DataProvider
  private static Object[][] dataForTestShouldSendOnboardingEmailThrowsException() {
    return new Object[][]{
        {INVALID_CONTEXT_NOT_SN_APP},
        {INVALID_CONTEXT_NOT_SN_APP_TYPE}
    };
  }

  @Test(dataProvider = "dataForTestShouldSendOnboardingEmailThrowsException")
  public void testShouldSendOnboardingEmailExceptionCases(EnterpriseOnboardingEmailTriggerContext context) {
    Assert.assertThrows(() ->
        _enterpriseEmailPluginsService.shouldSendOnboardingEmail(context, DEFAULT_VIEWER)
    );
  }

  @DataProvider
  private static Object[][] dataForTestFetchOnboardingEmailData() {
    return new Object[][]{
        {VALID_CONTEXT_WITH_T2_LICENSE, RenderingProduct.DEFAULT_SALES_NAVIGATOR, REGULAR_REDIRECT_URL},
        {VALID_CONTEXT_WITH_T2_LICENSE_ADMIN_ROLE, RenderingProduct.DEFAULT_SALES_NAVIGATOR, REGULAR_REDIRECT_URL},
        {VALID_CONTEXT_WITH_ONLY_ADMIN_ROLE, RenderingProduct.SALES_NAVIGATOR_ADMIN, REGULAR_REDIRECT_URL},
        {CONTEXT_WITH_ONLY_UR_ROLE, RenderingProduct.SALES_NAVIGATOR_ADMIN, REGULAR_REDIRECT_URL},
        {VALID_CONTEXT_WITH_TLE_LICENSE, RenderingProduct.SALES_NAVIGATOR_TEAMLINK_EXTEND, TLE_REDIRECT_URL},
        {CONTEXT_WITH_REVOKED_AND_APPROVED_ROLE, RenderingProduct.SALES_NAVIGATOR_ADMIN, REGULAR_REDIRECT_URL}
    };
  }

  @Test(dataProvider = "dataForTestFetchOnboardingEmailData")
  public void testFetchOnboardingEmailDataHappyCases(EnterpriseOnboardingEmailTriggerContext context,
      RenderingProduct product, Url url) {
    when(_emailService.buildLighthouseWebRedirectUrl(any(), any(), eq(true))).thenReturn(TLE_REDIRECT_URL);
    when(_emailService.buildLighthouseWebRedirectUrl( any(), any(), eq(false))).thenReturn(REGULAR_REDIRECT_URL);
    EnterpriseOnboardingEmailCustomData actualCustomData = runAndWait(_enterpriseEmailPluginsService
        .fetchOnboardingEmailData(context, DEFAULT_VIEWER));
    Assert.assertEquals(product, actualCustomData.getProduct());
    Assert.assertEquals(url, actualCustomData.getRedirectUrl());
  }

  @Test
  public void testFetchOnboardingEmailDataExceptionCases() {
    when(_emailService.buildLighthouseWebRedirectUrl( any(), any(), anyBoolean())).thenThrow(RuntimeException.class);
    Assert.assertThrows(() ->
      _enterpriseEmailPluginsService.fetchOnboardingEmailData(VALID_CONTEXT_WITH_T2_LICENSE, DEFAULT_VIEWER)
    );
  }
}
