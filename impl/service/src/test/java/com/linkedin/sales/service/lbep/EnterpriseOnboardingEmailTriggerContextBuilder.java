package com.linkedin.sales.service.lbep;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseBudgetGroupUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.data.template.SetMode;
import com.linkedin.enterprise.EnterpriseApplication;
import com.linkedin.enterprise.EnterpriseLicenseType;
import com.linkedin.enterprise.account.ApplicationInstanceType;
import com.linkedin.enterprise.acl.EnterpriseRoleAssignmentArray;
import com.linkedin.enterprise.license.LicenseAssignmentStatus;
import com.linkedin.enterprise.license.LicenseAssignmentStatusEnum;
import com.linkedin.platform.email.EnterpriseOnboardingEmailTriggerContext;
import com.linkedin.platform.email.LicenseAssignment;
import com.linkedin.platform.email.LicenseAssignmentArray;


public class EnterpriseOnboardingEmailTriggerContextBuilder {
  private EnterpriseProfileUrn _profileUrn;
  private EnterpriseApplicationUrn _applicationUrn;
  private ApplicationInstanceType _applicationInstanceType;
  private EnterpriseApplicationInstanceUrn _appInstanceUrn;
  private EnterpriseBudgetGroupUrn _budgetGroupUrn;
  private ContractUrn _contractUrn;
  private LicenseAssignmentArray _currentLicenseAssignments;
  private LicenseAssignmentArray _previousLicenseAssignments;
  private EnterpriseRoleAssignmentArray _currentRoleAssignments;

  protected static final Long ACCOUNT_ID = 12345L;
  protected static final Long PROFILE_ID = 11122L;
  protected static final Long APPLICATION_INSTANCE_ID = 67890L;
  protected static final Long BUDGET_GROUP_ID = 123L;
  protected static final Long CONTRACT_ID = 1234567L;
  protected static final EnterpriseAccountUrn ENTERPRISE_ACCOUNT_URN = new EnterpriseAccountUrn(ACCOUNT_ID);
  protected static final EnterpriseProfileUrn ENTERPRISE_PROFILE_URN =
      new EnterpriseProfileUrn(ENTERPRISE_ACCOUNT_URN,PROFILE_ID);
  protected static final EnterpriseApplicationInstanceUrn ENTERPRISE_APPLICATION_INSTANCE_URN =
      new EnterpriseApplicationInstanceUrn(ENTERPRISE_ACCOUNT_URN, APPLICATION_INSTANCE_ID);
  protected static final EnterpriseBudgetGroupUrn ENTERPRISE_BUDGET_GROUP_URN =
      new EnterpriseBudgetGroupUrn(ENTERPRISE_ACCOUNT_URN, BUDGET_GROUP_ID);
  protected static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  protected static final LicenseAssignmentArray ADVANCED_LICENSE_ASSIGNMENT = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER2.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED))
  ));
  protected static final LicenseAssignmentArray TIER_4_LICENSE_ASSIGNMENT = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER4.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED))
  ));
  protected static final LicenseAssignmentArray ADVANCED_LICENSE_ASSIGNMENT_ACTIVATED = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TIER2.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.ACTIVATED))
  ));
  protected static final LicenseAssignmentArray TLE_LICENSE_ASSIGNMENT_DECLINED = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.SALES_NAVIGATOR_TEAMLINK_EXTEND.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.DECLINED))
  ));
  protected static final LicenseAssignmentArray LEARNING_LICENSE_ASSIGNMENT = new LicenseAssignmentArray(ImmutableSet.of(
      new LicenseAssignment().setLicenseType(EnterpriseLicenseType.LEARNING_PRO_FRENCH.getUrn())
          .setStatus(new LicenseAssignmentStatus().setStatus(LicenseAssignmentStatusEnum.INVITED))
  ));

  public EnterpriseOnboardingEmailTriggerContextBuilder() {
    _profileUrn = ENTERPRISE_PROFILE_URN;
    _applicationUrn = EnterpriseApplication.SALES_NAVIGATOR.getUrn();
    _applicationInstanceType = ApplicationInstanceType.SALES_DEFAULT;
    _appInstanceUrn = ENTERPRISE_APPLICATION_INSTANCE_URN;
    _budgetGroupUrn = ENTERPRISE_BUDGET_GROUP_URN;
    _contractUrn = CONTRACT_URN;
    _currentLicenseAssignments = new LicenseAssignmentArray(ADVANCED_LICENSE_ASSIGNMENT);
    _previousLicenseAssignments = new LicenseAssignmentArray();
    _currentRoleAssignments = new EnterpriseRoleAssignmentArray();
  }

  public EnterpriseOnboardingEmailTriggerContext build() {
    return new EnterpriseOnboardingEmailTriggerContext()
        .setProfile(_profileUrn, SetMode.IGNORE_NULL)
        .setApplication(_applicationUrn, SetMode.IGNORE_NULL)
        .setApplicationInstanceType(_applicationInstanceType, SetMode.IGNORE_NULL)
        .setApplicationInstance(_appInstanceUrn, SetMode.IGNORE_NULL)
        .setBudgetGroup(_budgetGroupUrn, SetMode.IGNORE_NULL)
        .setContract(_contractUrn, SetMode.IGNORE_NULL)
        .setCurrentLicenseAssignments(_currentLicenseAssignments, SetMode.IGNORE_NULL)
        .setPreviousLicenseAssignments(_previousLicenseAssignments, SetMode.IGNORE_NULL)
        .setCurrentAclRoles(_currentRoleAssignments, SetMode.IGNORE_NULL);
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setProfile(EnterpriseProfileUrn profileUrn) {
    _profileUrn = profileUrn;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setApplication(EnterpriseApplicationUrn applicationUrn) {
    _applicationUrn = applicationUrn;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setApplicationInstanceType(ApplicationInstanceType applicationInstanceType) {
    _applicationInstanceType = applicationInstanceType;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setApplicationInstance(EnterpriseApplicationInstanceUrn appInstanceUrn) {
    _appInstanceUrn = appInstanceUrn;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setBudgetGroup(EnterpriseBudgetGroupUrn budgetGroupUrn) {
    _budgetGroupUrn = budgetGroupUrn;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setContract(ContractUrn contractUrn) {
    _contractUrn = contractUrn;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setCurrentLicenseAssignments(LicenseAssignmentArray currentLicenseAssignments) {
    _currentLicenseAssignments = currentLicenseAssignments;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setPreviousLicenseAssignments(LicenseAssignmentArray previousLicenseAssignments) {
    _previousLicenseAssignments = previousLicenseAssignments;
    return this;
  }

  public EnterpriseOnboardingEmailTriggerContextBuilder setCurrentAclRoleAssignments(EnterpriseRoleAssignmentArray currentRoleAssignments) {
    _currentRoleAssignments = currentRoleAssignments;
    return this;
  }
}
