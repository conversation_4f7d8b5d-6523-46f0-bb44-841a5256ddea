package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.DataMap;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.SavedAccount;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salesleadaccount.AccountDataSource;
import com.linkedin.salesleadaccount.LeadAccountStarredInfo;
import com.linkedin.salesleadaccount.SalesAccount;
import com.linkedin.salesleadaccount.SalesAccountFilter;
import com.linkedin.util.collections.list.PaginatedList;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.LixUtils.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 * This is the class to test {@link SalesAccountsService}
 */
public class SalesAccountsServiceTest extends ServiceUnitTest {

  private static final SeatUrn SEAT_URN = new SeatUrn(111L);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(222L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final SeatUrn ANOTHER_SEAT_URN = new SeatUrn(222L);
  private static final Long ORGANIZATION_ID1 = 1234L;
  private static final Long ORGANIZATION_ID2 = 2345L;
  private static final Long ORGANIZATION_ID3 = 3456L;
  private static final Long ORGANIZATION_ID4 = 4567L;
  private static final Long ORGANIZATION_ID5 = 5678L;
  private static final OrganizationUrn ORGANIZATION_URN_1;
  private static final OrganizationUrn ORGANIZATION_URN_2;
  private static final OrganizationUrn ORGANIZATION_URN_3;
  private static final OrganizationUrn ORGANIZATION_URN_4;
  private static final OrganizationUrn ORGANIZATION_URN_5;
  private static final Integer _maxSavedAccountsLimitAllTiers = 10000;

  private static String EXCEED_LIMIT_ERROR_MESSAGE = "Exceeded saved account limit for seat: {}" + SEAT_URN;

  static {
    try {
      ORGANIZATION_URN_1 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", ORGANIZATION_ID1));
      ORGANIZATION_URN_2 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", ORGANIZATION_ID2));
      ORGANIZATION_URN_3 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", ORGANIZATION_ID3));
      ORGANIZATION_URN_4 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", ORGANIZATION_ID4));
      ORGANIZATION_URN_5 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", ORGANIZATION_ID5));
    } catch (URISyntaxException e) {
      throw new RuntimeException();
    }
  }

  private SalesAccountsService _salesAccountsService;

  @Mock
  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  @Mock
  private LixService _lixService;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesAccountsService = new SalesAccountsService(_lssSavedLeadAccountDB, _lixService, _maxSavedAccountsLimitAllTiers);
    doReturn(Task.value("control")).when(_lixService)
        .getLixTreatment(SEAT_URN, LIX_SALES_ENTITIES_DELETION_BATCH_SIZE, null);
    doReturn(Task.value(5)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());
  }

  @BeforeMethod
  public void resetMock() {
    reset(_lssSavedLeadAccountDB);
  }

  @Test
  public void testCreateSavedAccount_Succeed() {
    SalesAccount salesAccount = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1), any());
    Pair<CompoundKey, CreateResponse> result = await(_salesAccountsService.createSavedAccount(salesAccount));
    assertEquals(result.getSecond().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateSavedAccount_SucceedWithDuplication() {
    SalesAccount salesAccount = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));
    doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSavedLeadAccountDB).createSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1), any());
    Pair<CompoundKey, CreateResponse> result = await(_salesAccountsService.createSavedAccount(salesAccount));
    assertEquals(result.getSecond().getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testCreateSavedAccount_FailedWithExceptionFromDB() {
    SalesAccount salesAccount = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));

    doReturn(Task.failure(new RuntimeException("Failed to create savedAccount"))).when(_lssSavedLeadAccountDB)
        .createSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1), any());
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesAccountsService.createSavedAccount(salesAccount));
    }).withCause(new RuntimeException("Failed to create savedAccount"));
  }

  @Test
  public void testCreateSavedAccount_FailedWhenLimitExceeded() {
    SalesAccount salesAccount = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(10000));

    doReturn(Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, EXCEED_LIMIT_ERROR_MESSAGE)))
        .when(_lssSavedLeadAccountDB)
        .createSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1), any());
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesAccountsService.createSavedAccount(salesAccount));
    }).withCause(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, EXCEED_LIMIT_ERROR_MESSAGE));
  }

  @Test
  public void testBatchCreateSavedAccounts_Succeed() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, AccountDataSource.USER_GENERATED);
    SalesAccount salesAccount2 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, AccountDataSource.CRM_SYNC);
    SalesAccount salesAccount3 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN));

    List<Pair<OrganizationUrn, SavedAccount>> pairs = Collections.singletonList(pair1);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_201_CREATED);
    responseMap.put(ORGANIZATION_URN_3, HttpStatus.S_201_CREATED);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(ImmutableList.of(salesAccount1, salesAccount2, salesAccount3)));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount2.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount2.getOrganization());
    CompoundKey compoundKey3 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount3.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount3.getOrganization());
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testBatchCreateSavedAccounts_FailedWithExceptionFromCreateAccounts() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, AccountDataSource.USER_GENERATED);
    SalesAccount salesAccount2 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, AccountDataSource.CRM_SYNC);
    SalesAccount salesAccount3 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN));

    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));
    List<Pair<OrganizationUrn, SavedAccount>> pairs = Collections.singletonList(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));

    doReturn(Task.failure(new RuntimeException("Failed to create savedAccounts"))).when(_lssSavedLeadAccountDB)
        .createSavedAccounts(eq(SEAT_URN), anyMap());

    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(ImmutableList.of(salesAccount1, salesAccount2, salesAccount3)));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount2.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount2.getOrganization());
    CompoundKey compoundKey3 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount3.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount3.getOrganization());
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  public void testBatchCreateSavedAccounts_FailedWhenLimitExceeded() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, AccountDataSource.USER_GENERATED);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN));
    ImmutableList<SalesAccount> savedAccounts = ImmutableList.of(salesAccount1);

    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(9999));
    List<Pair<OrganizationUrn, SavedAccount>> pairs = Collections.singletonList(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, EXCEED_LIMIT_ERROR_MESSAGE)))
        .when(_lssSavedLeadAccountDB)
        .createSavedAccounts(eq(SEAT_URN), anyMap());

    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(savedAccounts));
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());

    assertEquals(result.size(), 1);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testBatchCreateSavedAccounts_OwnerNotIdentical() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN);
    SalesAccount salesAccount2 = createSalesAccount(ANOTHER_SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN);
    SalesAccount salesAccount3 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(0));
    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(ImmutableList.of(salesAccount1, salesAccount2, salesAccount3)));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount2.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount2.getOrganization());
    CompoundKey compoundKey3 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount3.getOwner()).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount3.getOrganization());
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    verifyNoInteractions(_lssSavedLeadAccountDB);
  }

  @Test
  public void testBatchCreateSavedAccounts_LimitNotExceededWithSavedAndUnsavedAccountsInBatch() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, AccountDataSource.USER_GENERATED);
    SalesAccount salesAccount2 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, AccountDataSource.CRM_SYNC);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN));

    List<Pair<OrganizationUrn, SavedAccount>> pairs = Collections.singletonList(pair1);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(9999));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_201_CREATED);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(1)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(ImmutableList.of(salesAccount1, salesAccount2)));
    assertEquals(result.size(), 2);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner())
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount2.getOwner())
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount2.getOrganization());
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testBatchCreateSavedAccounts_LimitExceededWithSavedAndUnsavedAccountsInBatch() {
    SalesAccount salesAccount1 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, AccountDataSource.USER_GENERATED);
    SalesAccount salesAccount2 = createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, AccountDataSource.CRM_SYNC);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN));
    ImmutableList<SalesAccount> savedAccounts = ImmutableList.of(salesAccount1, salesAccount2);

    List<Pair<OrganizationUrn, SavedAccount>> pairs = Collections.singletonList(pair1);
    when(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(SEAT_URN)).thenReturn(Task.value(10000));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));

    doReturn(Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, EXCEED_LIMIT_ERROR_MESSAGE)))
        .when(_lssSavedLeadAccountDB)
        .createSavedAccounts(eq(SEAT_URN), anyMap());
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, salesAccount1.getOwner())
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, salesAccount1.getOrganization());

    Map<CompoundKey, CreateResponse> result = await(_salesAccountsService.batchCreateSavedAccounts(savedAccounts));
    assertEquals(result.size(), 2);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testDeleteSavedAccount_Succeed() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);

    doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(_lssSavedLeadAccountDB).deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1));
    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(compoundKey)));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteSavedAccount_SucceedWithEntityNotFound() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);

    doReturn(Task.value(HttpStatus.S_404_NOT_FOUND)).when(_lssSavedLeadAccountDB).deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1));
    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(compoundKey)));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testDeleteSavedAccount_FailedWithExceptionFromDB() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);

    doReturn(Task.failure(new RuntimeException("Failed to create savedAccount"))).when(_lssSavedLeadAccountDB).deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1));
    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(compoundKey)));
    assertEquals(result.size(), 1);
  }

  @Test
  public void testDeleteSavedAccount_FailedWithInvalidCompoundKey() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN);

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(compoundKey)));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_400_BAD_REQUEST);
    verifyNoInteractions(_lssSavedLeadAccountDB);
  }

  @Test
  public void testDeleteSavedAccount_FailedWithWrongKeyType() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN).append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, CONTRACT_URN);

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(Collections.singleton(compoundKey)));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_400_BAD_REQUEST);
    verifyNoInteractions(_lssSavedLeadAccountDB);
  }

  @Test
  public void testBatchDeleteSavedAccount() {
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_2);
    CompoundKey compoundKey3 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_3);
    CompoundKey compoundKey4 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN);
    CompoundKey compoundKey5 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, CONTRACT_URN);

    doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(_lssSavedLeadAccountDB).deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_1));
    doReturn(Task.value(HttpStatus.S_404_NOT_FOUND)).when(_lssSavedLeadAccountDB).deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_2));
    doReturn(Task.failure(new RuntimeException("Failed to create savedAccount"))).when(_lssSavedLeadAccountDB)
        .deleteSavedAccount(eq(SEAT_URN), eq(ORGANIZATION_URN_3));
    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchDeleteSavedAccounts(
        ImmutableSet.of(compoundKey1, compoundKey2, compoundKey3, compoundKey4, compoundKey5)));
    assertEquals(result.size(), 5);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(compoundKey4).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(result.get(compoundKey5).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    verify(_lssSavedLeadAccountDB, times(3)).deleteSavedAccount(any(), any());
  }

  @Test
  public void testGetSalesAccountsForGivenOwner_Succeed() {
    SavedAccount savedAccount1 = createSavedAccount(CONTRACT_URN);
    SavedAccount savedAccount2 = createSavedAccount(CONTRACT_URN);
    SavedAccount savedAccount3 = createSavedAccount(CONTRACT_URN);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, savedAccount1);
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, savedAccount2);
    Pair<OrganizationUrn, SavedAccount> pair3 = new Pair<>(ORGANIZATION_URN_3, savedAccount3);
    List<Pair<OrganizationUrn, SavedAccount>> pairs = new ArrayList<>();
    pairs.add(pair1);
    pairs.add(pair2);
    pairs.add(pair3);

    doReturn(Task.value(3)).when(_lssSavedLeadAccountDB).getSavedAccountCountForSeat(eq(SEAT_URN));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(null), eq(0), eq(3), eq(null), eq(null), eq(null));
    PaginatedList<SalesAccount> result = await(_salesAccountsService.getSalesAccountsForGivenOwner(SEAT_URN, 0, 3, null, null, null));
    assertEquals(result.getTotal(), 3);
    List<SalesAccount> resultList = result.getResult();
    assertEquals(resultList.size(), 3);
    assertEquals(resultList.get(0).getOrganization(), ORGANIZATION_URN_1);
    assertEquals(resultList.get(0).getOwner(), SEAT_URN);
    assertEquals(resultList.get(0).getContract(), CONTRACT_URN);
    assertEquals(resultList.get(1).getOrganization(), ORGANIZATION_URN_2);
    assertEquals(resultList.get(2).getOrganization(), ORGANIZATION_URN_3);

    PaginatedList<SalesAccount> countOnlyResult = await(_salesAccountsService.getSalesAccountsForGivenOwner(SEAT_URN, 0, 0, null, null, null));
    assertEquals(countOnlyResult.getTotal(), 3);
    List<SalesAccount> countOnlyResultList = countOnlyResult.getResult();
    assertEquals(countOnlyResultList.size(), 0);
  }

  @Test
  public void testGetSalesAccountsForGivenOwnerWithFilter_Succeed() {
    SavedAccount savedAccount1 = createSavedAccount(CONTRACT_URN, true);
    SavedAccount savedAccount2 = createSavedAccount(CONTRACT_URN, true);
    SavedAccount savedAccount3 = createSavedAccount(CONTRACT_URN, false);

    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, savedAccount1);
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, savedAccount2);
    Pair<OrganizationUrn, SavedAccount> pair3 = new Pair<>(ORGANIZATION_URN_3, savedAccount3);

    List<Pair<OrganizationUrn, SavedAccount>> pairs = new ArrayList<>();
    pairs.add(pair1);
    pairs.add(pair2);

    doReturn(Task.value(2)).when(_lssSavedLeadAccountDB).getSavedAccountCountForSeat(eq(SEAT_URN));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB).getSavedAccounts(eq(SEAT_URN), eq(null), eq(0), eq(2), eq(SalesAccountFilter.STAR_ONLY), eq(null), eq(null));
    PaginatedList<SalesAccount> result = await(_salesAccountsService.getSalesAccountsForGivenOwner(SEAT_URN, 0, 2, SalesAccountFilter.STAR_ONLY, null, null));
    assertEquals(result.getTotal(), 2);
    List<SalesAccount> resultList = result.getResult();
    assertEquals(resultList.size(), 2);
    assertEquals(resultList.get(0).getOrganization(), ORGANIZATION_URN_1);
    assertEquals(resultList.get(0).getOwner(), SEAT_URN);
    assertEquals(resultList.get(0).getContract(), CONTRACT_URN);
    assertEquals(resultList.get(0).getStarredInfo().isStarred(), Boolean.TRUE);
    assertEquals(resultList.get(1).getOrganization(), ORGANIZATION_URN_2);
    assertEquals(resultList.get(1).getStarredInfo().isStarred(), Boolean.TRUE);

    pairs.clear();
    pairs.add(pair3);

    doReturn(Task.value(1)).when(_lssSavedLeadAccountDB).getSavedAccountCountForSeat(eq(SEAT_URN));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB).getSavedAccounts(eq(SEAT_URN), eq(null), eq(0), eq(1), eq(SalesAccountFilter.STAR_ONLY), eq(null), eq(null));
    result = await(_salesAccountsService.getSalesAccountsForGivenOwner(SEAT_URN, 0, 1, SalesAccountFilter.STAR_ONLY, null, null));
    assertEquals(result.getTotal(), 1);
    resultList = result.getResult();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getOrganization(), ORGANIZATION_URN_3);
    assertEquals(resultList.get(0).getOwner(), SEAT_URN);
    assertEquals(resultList.get(0).getContract(), CONTRACT_URN);
    assertEquals(resultList.get(0).getStarredInfo().isStarred(), Boolean.FALSE);
  }

  @Test
  public void testGetSalesAccountsForGivenOwner_WithExceptionFromDB() {
    doReturn(Task.value(3)).when(_lssSavedLeadAccountDB).getSavedAccountCountForSeat(eq(SEAT_URN));
    doReturn(Task.failure(new RuntimeException("Failed to get savedAccount"))).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(null), eq(0), eq(3), eq(null), eq(null), eq(null));
    PaginatedList<SalesAccount> result = await(_salesAccountsService.getSalesAccountsForGivenOwner(SEAT_URN, 0, 3, null, null, null));
    assertEquals(result.getTotal(), 0);
    List<SalesAccount> resultList = result.getResult();
    assertEquals(resultList.size(), 0);
  }

  @Test
  public void testGetSalesAccountForGivenCompoundKey_Succeed() {
    SavedAccount savedAccount = createSavedAccount(CONTRACT_URN);
    Pair<OrganizationUrn, SavedAccount> pair = new Pair<>(ORGANIZATION_URN_1, savedAccount);
    List<Pair<OrganizationUrn, SavedAccount>> pairs = new ArrayList<>();
    pairs.add(pair);
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);

    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB).getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    SalesAccount result = await(_salesAccountsService.getSalesAccountForGivenCompoundKey(compoundKey));
    assertEquals(result.getOrganization(), ORGANIZATION_URN_1);
    assertEquals(result.getOwner(), SEAT_URN);
    assertEquals(result.getContract(), CONTRACT_URN);
  }

  @Test
  public void testGetSalesAccountForGivenCompoundKey_FailedWithExceptionFromDB() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);
    doReturn(Task.failure(new RuntimeException("Failed to get savedAccount"))).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesAccountsService.getSalesAccountForGivenCompoundKey(compoundKey)))
        .withCause(new RuntimeException("Failed to get savedAccount"));
  }

  @Test
  public void testGetSalesAccountForGivenCompoundKey_FailedWithEntityNotFound() {
    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);

    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesAccountsService.getSalesAccountForGivenCompoundKey(compoundKey)))
        .withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
  }

  @Test
  public void testBatchGetSalesAccounts(){
    SavedAccount savedAccount1 = createSavedAccount(CONTRACT_URN, com.linkedin.sales.espresso.AccountDataSource.USER_GENERATED);
    SavedAccount savedAccount2 = createSavedAccount(CONTRACT_URN, com.linkedin.sales.espresso.AccountDataSource.CRM_SYNC);
    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, savedAccount1);
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, savedAccount2);
    List<Pair<OrganizationUrn, SavedAccount>> pairs1 = new ArrayList<>();
    List<Pair<OrganizationUrn, SavedAccount>> pairs2 = new ArrayList<>();
    pairs1.add(pair1);
    pairs2.add(pair2);

    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_2);
    CompoundKey compoundKey3 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_3);
    CompoundKey compoundKey4= new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, ORGANIZATION_URN_4);
    CompoundKey compoundKey5 = new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, SEAT_URN)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, CONTRACT_URN);

    doReturn(Task.value(pairs1)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.failure(new RuntimeException("Failed to get savedAccount"))).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.failure(new EntityNotFoundException(null, "Cannot find savedAccount"))).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(pairs2)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_4), eq(0), eq(10), eq(null), eq(null), eq(null));
    Map<CompoundKey, SalesAccount> resultMap = await(_salesAccountsService.batchGetSalesAccounts(
        ImmutableSet.of(compoundKey1, compoundKey2, compoundKey3, compoundKey4, compoundKey5)));
    assertEquals(resultMap.size(), 2);
    assertTrue(resultMap.containsKey(compoundKey1));
    assertEquals(resultMap.get(compoundKey1).getOrganization(), ORGANIZATION_URN_1);
    assertEquals(resultMap.get(compoundKey1).getOwner(), SEAT_URN);
    assertEquals(resultMap.get(compoundKey1).getContract(), CONTRACT_URN);
    assertEquals(resultMap.get(compoundKey1).getDataSource(), AccountDataSource.USER_GENERATED);
    verify(_lssSavedLeadAccountDB, times(4)).getSavedAccounts(eq(SEAT_URN), any(), eq(0), eq(10), eq(null), eq(null), eq(null));
  }

  @Test(description = "Test batchPartialUpdate where the owner keys in the patch do not mach the specified owner key")
  public void testBatchPartialUpdate_ownerKeyMismatch() {

    Map<CompoundKey, PatchRequest<SalesAccount>> patchRequestMap = new HashMap<>();
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, true));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN_2, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN_2, ORGANIZATION_URN_1, CONTRACT_URN, true));

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchPartialUpdateSalesAccounts(patchRequestMap));
    assertEquals(result.size(), 2);
    CompoundKey compoundKey1 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = createCompoundKey(SEAT_URN_2, ORGANIZATION_URN_1);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  @Test(description = "Test batchPartialUpdate succeeded")
  public void testBatchPartialUpdate_succeeded() {

    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN, false));
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, createSavedAccount(CONTRACT_URN, true));
    Pair<OrganizationUrn, SavedAccount> pair3 = new Pair<>(ORGANIZATION_URN_3, createSavedAccount(CONTRACT_URN, true));

    List<Pair<OrganizationUrn, SavedAccount>> pairs1 = ImmutableList.of(pair1);
    List<Pair<OrganizationUrn, SavedAccount>> pairs2 = ImmutableList.of(pair2);
    List<Pair<OrganizationUrn, SavedAccount>> pairs3 = ImmutableList.of(pair3);

    doReturn(Task.value(pairs1)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(pairs2)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(pairs3)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_1, HttpStatus.S_200_OK);
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_200_OK);
    responseMap.put(ORGANIZATION_URN_3, HttpStatus.S_200_OK);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, PatchRequest<SalesAccount>> patchRequestMap = new HashMap<>();
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, true));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_2, createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, false));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_3, createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN, false));

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchPartialUpdateSalesAccounts(patchRequestMap));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_2);
    CompoundKey compoundKey3 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_3);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_200_OK);
  }

  @Test(description = "Test batchPartialUpdate succeeded, only update existing saved accounts")
  public void testBatchPartialUpdate_succeededWithExistingSavedAccounts() {

    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN, false));
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, createSavedAccount(CONTRACT_URN, true));

    List<Pair<OrganizationUrn, SavedAccount>> pairs1 = ImmutableList.of(pair1);
    List<Pair<OrganizationUrn, SavedAccount>> pairs2 = ImmutableList.of(pair2);

    doReturn(Task.value(pairs1)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(pairs2)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_1, HttpStatus.S_200_OK);
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_200_OK);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, PatchRequest<SalesAccount>> patchRequestMap = new HashMap<>();
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, true));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_2, createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, false));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_3, createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN, false));

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchPartialUpdateSalesAccounts(patchRequestMap));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_2);
    CompoundKey compoundKey3 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_3);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_200_OK);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_404_NOT_FOUND);
  }




  @Test(description = "Test batchPartialUpdate failed with expresso update exception")
  public void testBatchPartialUpdate_failedWithEspressoUpdateError() {

    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN, false));
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, createSavedAccount(CONTRACT_URN, true));

    List<Pair<OrganizationUrn, SavedAccount>> pairs1 = ImmutableList.of(pair1);
    List<Pair<OrganizationUrn, SavedAccount>> pairs2 = ImmutableList.of(pair2);

    doReturn(Task.value(pairs1)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(pairs2)).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_2), eq(0), eq(10), eq(null), eq(null), eq(null));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_3), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_1, HttpStatus.S_200_OK);
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_200_OK);
    doReturn(Task.failure(new RuntimeException("Failed to create savedAccounts"))).when(_lssSavedLeadAccountDB)
        .createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, PatchRequest<SalesAccount>> patchRequestMap = new HashMap<>();
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, true));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_2, createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, false));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_3, createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN, false));

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchPartialUpdateSalesAccounts(patchRequestMap));
    assertEquals(result.size(), 3);
    CompoundKey compoundKey1 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_2);
    CompoundKey compoundKey3 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_3);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(compoundKey3).getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  public void testBatchPartialUpdate_failedWithEspressGetException() {

    Pair<OrganizationUrn, SavedAccount> pair1 = new Pair<>(ORGANIZATION_URN_1, createSavedAccount(CONTRACT_URN, false));
    Pair<OrganizationUrn, SavedAccount> pair2 = new Pair<>(ORGANIZATION_URN_2, createSavedAccount(CONTRACT_URN, true));

    List<Pair<OrganizationUrn, SavedAccount>> pairs1 = ImmutableList.of(pair1);
    List<Pair<OrganizationUrn, SavedAccount>> pairs2 = ImmutableList.of(pair2);

    doReturn(Task.failure(new RuntimeException("Failed to create savedAccounts"))).when(_lssSavedLeadAccountDB)
        .getSavedAccounts(eq(SEAT_URN), eq(ORGANIZATION_URN_1), eq(0), eq(10), eq(null), eq(null), eq(null));

    Map<OrganizationUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(ORGANIZATION_URN_1, HttpStatus.S_200_OK);
    responseMap.put(ORGANIZATION_URN_2, HttpStatus.S_200_OK);
    doReturn(Task.failure(new RuntimeException("Failed to create savedAccounts"))).when(_lssSavedLeadAccountDB)
        .createSavedAccounts(eq(SEAT_URN), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, PatchRequest<SalesAccount>> patchRequestMap = new HashMap<>();
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_1, createSalesAccount(SEAT_URN, ORGANIZATION_URN_1, CONTRACT_URN, true));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_2, createSalesAccount(SEAT_URN, ORGANIZATION_URN_2, CONTRACT_URN, false));
    addCompoundKeyPathRequestToMap(patchRequestMap, SEAT_URN, ORGANIZATION_URN_3, createSalesAccount(SEAT_URN, ORGANIZATION_URN_3, CONTRACT_URN, false));

    Map<CompoundKey, UpdateResponse> result = await(_salesAccountsService.batchPartialUpdateSalesAccounts(patchRequestMap));
    assertEquals(result.size(), 2);
    CompoundKey compoundKey1 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_1);
    CompoundKey compoundKey2 = createCompoundKey(SEAT_URN, ORGANIZATION_URN_2);
    assertEquals(result.get(compoundKey1).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertEquals(result.get(compoundKey2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  private SalesAccount createSalesAccount(SeatUrn seat, OrganizationUrn organization, ContractUrn contract){
    SalesAccount salesAccount = new SalesAccount();
    salesAccount.setOwner(seat);
    salesAccount.setOrganization(organization);
    salesAccount.setContract(contract);

    return salesAccount;
  }

  private SalesAccount createSalesAccount(SeatUrn seat, OrganizationUrn organization, ContractUrn contract, AccountDataSource accountDataSource){
    SalesAccount salesAccount = new SalesAccount();
    salesAccount.setOwner(seat);
    salesAccount.setOrganization(organization);
    salesAccount.setContract(contract);
    salesAccount.setDataSource(accountDataSource);

    return salesAccount;
  }

  private SalesAccount createSalesAccount(SeatUrn seat, OrganizationUrn organization, ContractUrn contract, boolean isStarred){
    SalesAccount salesAccount = new SalesAccount();
    salesAccount.setOwner(seat);
    salesAccount.setOrganization(organization);
    salesAccount.setContract(contract);
    salesAccount.setStarredInfo(new LeadAccountStarredInfo().setLastModifiedAt(System.currentTimeMillis()).setStarred(isStarred));

    return salesAccount;
  }

  private SavedAccount createSavedAccount(ContractUrn contract){
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = contract.toString();
    savedAccount.createdTime = System.currentTimeMillis();

    return savedAccount;
  }

  private SavedAccount createSavedAccount(ContractUrn contract, com.linkedin.sales.espresso.AccountDataSource accountDataSource){
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = contract.toString();
    savedAccount.createdTime = System.currentTimeMillis();
    savedAccount.dataSource = accountDataSource;

    return savedAccount;
  }

  private SavedAccount createSavedAccount(ContractUrn contract, boolean isStarred){
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = contract.toString();
    savedAccount.createdTime = System.currentTimeMillis();
    savedAccount.starred = isStarred;
    savedAccount.starLastModifiedTime = System.currentTimeMillis();

    return savedAccount;
  }

  private static CompoundKey createCompoundKey(SeatUrn seatUrn, OrganizationUrn organizationUrn) {
    return new CompoundKey().append(ServiceConstants.OWNER_COMPOUND_KEY, seatUrn)
        .append(ServiceConstants.ORGANIZATION_COMPOUND_KEY, organizationUrn);
  }

  private static void addCompoundKeyPathRequestToMap(
      Map<CompoundKey, PatchRequest<SalesAccount>> requestMap, SeatUrn seatUrn, OrganizationUrn organizationUrn,
      SalesAccount salesAccount) {
    DataMap patchData = new DataMap();
    patchData.put("$set", salesAccount.data());
    requestMap.put(createCompoundKey(seatUrn, organizationUrn), PatchRequest.createFromPatchDocument(patchData));
  }
}
