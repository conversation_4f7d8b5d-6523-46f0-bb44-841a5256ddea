package com.linkedin.sales.service.leadaccount;

import com.linkedin.common.AuditStamp;
import com.linkedin.common.ChangeAuditStamps;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.sales.ds.db.model.LeadAccountAssociationKey;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.AccountToLeadAssociationView;
import com.linkedin.sales.service.LixService;
import com.linkedin.util.collections.list.PaginatedList;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.espresso.LeadToAccountAssociation;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.leadaccount.SalesLeadAccountAssociationService.*;
import static com.linkedin.sales.service.utils.LixUtils.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static java.lang.Boolean.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 *  * This is the class to test {@link SalesLeadAccountAssociationService}
 */
public class SalesLeadAccountAssociationServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
  private static final SeatUrn SEAT_URN_TEST = new SeatUrn(1000L);

  private SalesLeadAccountAssociationService _salesLeadAccountAssociationService;

  @Mock
  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  @Mock
  private LixService _lixService;

  @BeforeTest(alwaysRun = true)
  public void setUp() {

    MockitoAnnotations.initMocks(this);
    _salesLeadAccountAssociationService = new SalesLeadAccountAssociationService(_lssSavedLeadAccountDB, _lixService);
    doReturn(Task.value("control")).when(_lixService)
        .getLixTreatment(SEAT_URN, LIX_SALES_ENTITIES_DELETION_BATCH_SIZE, null);
    doReturn(Task.value(5)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());
  }

  @Test
  public void testCreateLeadAccountAssociation() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    doReturn(Task.value(TRUE)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), any(), any(), any());
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());
    Pair<CompoundKey, CreateResponse> result = await(
        _salesLeadAccountAssociationService.createLeadAccountAssociation(leadAccountAssociation));
    Assert.assertEquals(result.getSecond().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateLeadAccountAssociationWithInvalidKey() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    doReturn(Task.value(TRUE)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), any(), any(), any());

    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());
    Pair<CompoundKey, CreateResponse> result = await(
        _salesLeadAccountAssociationService.createLeadAccountAssociation(leadAccountAssociation));
    Assert.assertEquals(result.getSecond().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateLeadAccountAssociationFailedDueToDeletion() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    doReturn(Task.failure(new RuntimeException())).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), any(), any(), any());

    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());

    Map<CompoundKey, CreateResponse> result = await(
        _salesLeadAccountAssociationService.batchCreateLeadAccountAssociations(Collections.singletonList(leadAccountAssociation)));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testCreateLeadAccountAssociationDuplicate() throws URISyntaxException {
    doReturn(Task.value(TRUE)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(HttpStatus.S_412_PRECONDITION_FAILED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), any(), any(), any());
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());
    Pair<CompoundKey, CreateResponse> result = await(
        _salesLeadAccountAssociationService.createLeadAccountAssociation(leadAccountAssociation));
    Assert.assertEquals(result.getSecond().getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testBatchCreateLeadAccountAssociationCreatorNotIdentical() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), any(), any(), any());

    List<LeadAccountAssociation> leadAccountAssociations = new ArrayList<>();

    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());

    LeadAccountAssociation leadAccountAssociation1 = new LeadAccountAssociation();
    leadAccountAssociation1.setLead(new MemberUrn(2L))
        .setCreator(SEAT_URN_TEST)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());

    leadAccountAssociations.add(leadAccountAssociation1);
    leadAccountAssociations.add(leadAccountAssociation);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesLeadAccountAssociationService.batchCreateLeadAccountAssociations(leadAccountAssociations));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = getCompoundKey(leadAccountAssociation1);
    CompoundKey compoundKey2 = getCompoundKey(leadAccountAssociation);
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }
  @Test
  public void testBatchCreateSavedAccounts_FailedWithExceptionFromCreateAccounts() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));

    doReturn(Task.value(TRUE)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(HttpStatus.S_201_CREATED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), eq(new MemberUrn(1L)), any(), any());
    doReturn(Task.value(HttpStatus.S_412_PRECONDITION_FAILED)).when(_lssSavedLeadAccountDB).createLeadAccountAssociation(any(), eq(new MemberUrn(2L)), any(), any());
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());

    LeadAccountAssociation leadAccountAssociation1 = new LeadAccountAssociation();
    leadAccountAssociation1.setLead(new MemberUrn(2L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());
    List<LeadAccountAssociation> leadAccountAssociations = new ArrayList<>();
    Pair<MemberUrn, OrganizationUrn> pair1 = new Pair<>(new MemberUrn(1L), organizationUrn);
    Pair<MemberUrn, OrganizationUrn> pair2 = new Pair<>(new MemberUrn(2L), organizationUrn);
    Map<Pair<MemberUrn, OrganizationUrn>, LeadToAccountAssociation> map = new HashMap<>();
    map.put(pair1, createLeadToAccountAssociation());
    map.put(pair2, createLeadToAccountAssociation());
    Map<Pair<MemberUrn, OrganizationUrn>, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(pair1, HttpStatus.S_201_CREATED);
    responseMap.put(pair2, HttpStatus.S_201_CREATED);
    doReturn(Task.failure(new RuntimeException("Failed to create savedLeadaccountassociation")))
        .when(_lssSavedLeadAccountDB).createLeadAccountAssociations(any(), eq(SEAT_URN));
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());
    leadAccountAssociations.add(leadAccountAssociation1);
    leadAccountAssociations.add(leadAccountAssociation);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesLeadAccountAssociationService.batchCreateLeadAccountAssociations(leadAccountAssociations));
    Assert.assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = getCompoundKey(leadAccountAssociation);
    CompoundKey compoundKey2 = getCompoundKey(leadAccountAssociation1);
    Assert.assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    Assert.assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);

  }


  @Test
  public void testBatchCreateLeadAccountAssociation() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    LeadAccountAssociation leadAccountAssociation = new LeadAccountAssociation();
    leadAccountAssociation.setLead(new MemberUrn(1L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());

    LeadAccountAssociation leadAccountAssociation1 = new LeadAccountAssociation();
    leadAccountAssociation1.setLead(new MemberUrn(2L))
        .setCreator(SEAT_URN)
        .setAccount(organizationUrn)
        .setContract(CONTRACT_URN)
        .setChangeAuditStamps(createChangeAuditStamps());
    List<LeadAccountAssociation> leadAccountAssociations = new ArrayList<>();
    Pair<MemberUrn, OrganizationUrn> pair1 = new Pair<>(new MemberUrn(1L), organizationUrn);
    Pair<MemberUrn, OrganizationUrn> pair2 = new Pair<>(new MemberUrn(2L), organizationUrn);
    Map<Pair<MemberUrn, OrganizationUrn>, LeadToAccountAssociation> map = new HashMap<>();
    map.put(pair1, createLeadToAccountAssociation());
    map.put(pair2, createLeadToAccountAssociation());
    Map<Pair<MemberUrn, OrganizationUrn>, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(pair1, HttpStatus.S_201_CREATED);
    responseMap.put(pair2, HttpStatus.S_201_CREATED);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createLeadAccountAssociations(any(), eq(SEAT_URN));
    doReturn(Task.value(TRUE)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());
    leadAccountAssociations.add(leadAccountAssociation1);
    leadAccountAssociations.add(leadAccountAssociation);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesLeadAccountAssociationService.batchCreateLeadAccountAssociations(leadAccountAssociations));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = getCompoundKey(leadAccountAssociation);
    CompoundKey compoundKey2 = getCompoundKey(leadAccountAssociation1);
    Assert.assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_201_CREATED);
    Assert.assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testDeleteLeadAccountAssociation() throws URISyntaxException {
    doReturn(Task.value(true)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    CompoundKey compoundKey = createCompoundKey();
    UpdateResponse result = await(_salesLeadAccountAssociationService.deleteLeadAccountAssociation(compoundKey));
    assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteLeadAccountAssociationWithInvalidCompoundKey() throws URISyntaxException {
    doReturn(Task.value(true)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), any(), any());
    CompoundKey compoundKey = createCompoundKey();
    compoundKey.append(CREATOR_COMPOUND_KEY, new MemberUrn(1L));
    UpdateResponse result = await(_salesLeadAccountAssociationService.deleteLeadAccountAssociation(compoundKey));
    assertEquals(result.getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }


  @Test
  public void testBatchDeleteLeadAccountAssociation() throws URISyntaxException {
    doReturn(Task.value(true)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), eq(new MemberUrn(1L)), any());
    doReturn(Task.value(true)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), eq(new MemberUrn(2L)), any());

    CompoundKey compoundKey1 = createCompoundKey();
    CompoundKey compoundKey2 = createCompoundKey();
    compoundKey2.append(LEAD_COMPOUND_KEY, new MemberUrn(2L));
    Set<CompoundKey> compoundKeySet = new HashSet<>();
    compoundKeySet.add(compoundKey1);
    compoundKeySet.add(compoundKey2);

    Map<CompoundKey, UpdateResponse> resultMap =
        await(_salesLeadAccountAssociationService.batchDeleteLeadAccountAssociations(compoundKeySet));
    assertEquals(resultMap.size(), 2);
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testBatchDeleteLeadAccountAssociationNoFound() throws URISyntaxException {
    doReturn(Task.value(true)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), eq(new MemberUrn(1L)), any());
    doReturn(Task.value(false)).when(_lssSavedLeadAccountDB).deleteLeadAccountAssociation(any(), eq(new MemberUrn(2L)), any());

    CompoundKey compoundKey1 = createCompoundKey();
    CompoundKey compoundKey2 = createCompoundKey();
    compoundKey2.append(LEAD_COMPOUND_KEY, new MemberUrn(2L));
    Set<CompoundKey> compoundKeySet = new HashSet<>();
    compoundKeySet.add(compoundKey1);
    compoundKeySet.add(compoundKey2);

    Map<CompoundKey, UpdateResponse> resultMap =
        await(_salesLeadAccountAssociationService.batchDeleteLeadAccountAssociations(compoundKeySet));
    assertEquals(resultMap.size(), 2);
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testGetLeadAccountAssociationBySeatAndAccount() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    MemberUrn member = new MemberUrn(1L);
    AccountToLeadAssociationView accountToLeadAssociationView = new AccountToLeadAssociationView();
    accountToLeadAssociationView.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView.lastModifiedTime = System.currentTimeMillis();
    Pair<MemberUrn, AccountToLeadAssociationView> pair = new Pair<>(member, accountToLeadAssociationView);

    MemberUrn member1 = new MemberUrn(2L);
    AccountToLeadAssociationView accountToLeadAssociationView1 = new AccountToLeadAssociationView();
    accountToLeadAssociationView1.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView1.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView1.lastModifiedTime = System.currentTimeMillis();
    Pair<MemberUrn, AccountToLeadAssociationView> pair1 = new Pair<>(member1, accountToLeadAssociationView1);

    MemberUrn member2 = new MemberUrn(3L);
    AccountToLeadAssociationView accountToLeadAssociationView2 = new AccountToLeadAssociationView();
    accountToLeadAssociationView2.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView2.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView2.lastModifiedTime = System.currentTimeMillis();

    Pair<MemberUrn, AccountToLeadAssociationView> pair2 = new Pair<>(member2, accountToLeadAssociationView2);

    List pairs = new ArrayList<Pair<MemberUrn, AccountToLeadAssociationView>>();
    pairs.add(pair);
    pairs.add(pair1);
    pairs.add(pair2);

     Map<OrganizationUrn, Integer> map = new HashMap<>();
     map.put(organizationUrn, pairs.size());

    doReturn(Task.value(map)).when(_lssSavedLeadAccountDB).getAssociatedLeadCounts(any(), any());

    doReturn(Task.value(pairs))
        .when(_lssSavedLeadAccountDB)
        .getLeadsAssociatedWithGivenAccount(eq(organizationUrn), eq(SEAT_URN), eq(0), eq(BATCH_SIZE));

    PaginatedList<LeadAccountAssociation> resultPair = await(
        _salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(Collections.singletonList(organizationUrn), SEAT_URN, 0, 3));


    assertEquals(resultPair.getTotal(), 3);
    List<LeadAccountAssociation> resultList = resultPair.getResult();
    assertEquals(resultList.size(), 3);
    assertEquals(resultList.get(0).getLead(), new MemberUrn(1L));
  }

  @Test
  public void testGetAllLeadAccountAssociationBySeatAndAccount() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    MemberUrn member = new MemberUrn(1L);
    AccountToLeadAssociationView accountToLeadAssociationView = new AccountToLeadAssociationView();
    accountToLeadAssociationView.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView.lastModifiedTime = System.currentTimeMillis();
    Pair<MemberUrn, AccountToLeadAssociationView> pair = new Pair<>(member, accountToLeadAssociationView);

    MemberUrn member1 = new MemberUrn(2L);
    AccountToLeadAssociationView accountToLeadAssociationView1 = new AccountToLeadAssociationView();
    accountToLeadAssociationView1.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView1.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView1.lastModifiedTime = System.currentTimeMillis();
    Pair<MemberUrn, AccountToLeadAssociationView> pair1 = new Pair<>(member1, accountToLeadAssociationView1);

    MemberUrn member2 = new MemberUrn(3L);
    AccountToLeadAssociationView accountToLeadAssociationView2 = new AccountToLeadAssociationView();
    accountToLeadAssociationView2.contractUrn = CONTRACT_URN.toString();
    accountToLeadAssociationView2.createdTime = System.currentTimeMillis();
    accountToLeadAssociationView2.lastModifiedTime = System.currentTimeMillis();

    Pair<MemberUrn, AccountToLeadAssociationView> pair2 = new Pair<>(member2, accountToLeadAssociationView2);

    List pairs = new ArrayList<Pair<MemberUrn, AccountToLeadAssociationView>>();
    pairs.add(pair);
    pairs.add(pair1);
    pairs.add(pair2);

    Map<OrganizationUrn, Integer> map = new HashMap<>();
    map.put(organizationUrn, pairs.size());

    doReturn(Task.value(map)).when(_lssSavedLeadAccountDB).getAssociatedLeadCounts(any(), any());

    doReturn(Task.value(pairs))
        .when(_lssSavedLeadAccountDB)
        .getLeadsAssociatedWithGivenAccount(eq(organizationUrn), eq(SEAT_URN), eq(0), eq(BATCH_SIZE));

    PaginatedList<LeadAccountAssociation> resultPair = await(
        _salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(Collections.singletonList(organizationUrn), SEAT_URN, 0, -1));


    assertEquals(resultPair.getTotal(), 3);
    List<LeadAccountAssociation> resultList = resultPair.getResult();
    assertEquals(resultList.size(), 3);
    assertEquals(resultList.get(0).getLead(), new MemberUrn(1L));
  }

  @Test
  public void testGetLeadAccountAssociationBySeatAndAccountNotFound() throws URISyntaxException {
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));

    Map<OrganizationUrn, Integer> map = new HashMap<>();
    map.put(organizationUrn, 1);

    doReturn(Task.value(map)).when(_lssSavedLeadAccountDB).getAssociatedLeadCounts(any(), any());

    doReturn(Task.failure(new EntityNotFoundException(null, "not found")))
        .when(_lssSavedLeadAccountDB)
        .getLeadsAssociatedWithGivenAccount(eq(organizationUrn), eq(SEAT_URN), eq(0), eq(BATCH_SIZE));

    PaginatedList<LeadAccountAssociation> resultPair = await(
        _salesLeadAccountAssociationService.getLeadAccountAssociationForAccounts(Collections.singletonList(organizationUrn), SEAT_URN, 0, 3));

    assertEquals(resultPair.getTotal(), 0);
    List<LeadAccountAssociation> resultList = resultPair.getResult();
    assertEquals(resultList.size(), 0);
  }

  @Test
  public void testGetLeadAccountAssociationBySeatAndLead() throws URISyntaxException {
    MemberUrn member = new MemberUrn(1L);
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********0"));
    LeadToAccountAssociation leadToAccountAssociation = new LeadToAccountAssociation();
    leadToAccountAssociation.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation.createdTime = 1L;
    leadToAccountAssociation.lastModifiedTime = 1L;
    Pair<OrganizationUrn, LeadToAccountAssociation> pair = new Pair<>(organizationUrn, leadToAccountAssociation);

    OrganizationUrn organizationUrn1 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:*********"));
    LeadToAccountAssociation leadToAccountAssociation1 = new LeadToAccountAssociation();
    leadToAccountAssociation1.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation1.createdTime = 2L;
    leadToAccountAssociation1.lastModifiedTime = 2L;
    Pair<OrganizationUrn, LeadToAccountAssociation> pair1 = new Pair<>(organizationUrn1, leadToAccountAssociation1);

    OrganizationUrn organizationUrn2 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********2"));
    LeadToAccountAssociation leadToAccountAssociation2 = new LeadToAccountAssociation();
    leadToAccountAssociation2.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation2.createdTime = 3L;
    leadToAccountAssociation2.lastModifiedTime = 3L;
    Pair<OrganizationUrn, LeadToAccountAssociation> pair2 = new Pair<>(organizationUrn2, leadToAccountAssociation2);
    List pairs = new ArrayList<Pair<OrganizationUrn, LeadToAccountAssociation>>();
    pairs.add(pair);
    pairs.add(pair1);
    pairs.add(pair2);
    doReturn(Task.value(pairs))
        .when(_lssSavedLeadAccountDB)
        .getAccountsAssociatedWithGivenLead(eq(member), eq(SEAT_URN), eq(0), eq(DEFAULT_COUNT));

    List<LeadAccountAssociation> result = await(
        _salesLeadAccountAssociationService.getLeadAccountAssociationForLeads(Collections.singletonList(member), SEAT_URN));
    assertEquals(result.size(), 1);
    assertEquals(result.get(0).getAccount(), OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********2")));
  }

  @Test
  public void testGetLeadAccountAssociationBySeat() throws URISyntaxException {
    OrganizationUrn orgUrn1 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********0"));
    MemberUrn memberUrn1 = new MemberUrn(123L);
    LeadToAccountAssociation leadToAccountAssociation1 = new LeadToAccountAssociation();
    leadToAccountAssociation1.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation1.createdTime = 1L;
    leadToAccountAssociation1.lastModifiedTime = 1L;
    LeadAccountAssociationKey key1 = new LeadAccountAssociationKey(SEAT_URN, memberUrn1, orgUrn1);
    Pair<LeadAccountAssociationKey, LeadToAccountAssociation> pair1 = new Pair<>(key1, leadToAccountAssociation1);

    OrganizationUrn orgUrn2 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:*********"));
    MemberUrn memberUrn2 = new MemberUrn(456L);
    LeadToAccountAssociation leadToAccountAssociation2 = new LeadToAccountAssociation();
    leadToAccountAssociation2.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation2.createdTime = 2L;
    leadToAccountAssociation2.lastModifiedTime = 2L;
    LeadAccountAssociationKey key2 = new LeadAccountAssociationKey(SEAT_URN, memberUrn2, orgUrn2);
    Pair<LeadAccountAssociationKey, LeadToAccountAssociation> pair2 = new Pair<>(key2, leadToAccountAssociation2);

    List<Pair<LeadAccountAssociationKey, LeadToAccountAssociation>> pairs = Arrays.asList(pair1, pair2);
    doReturn(Task.value(pairs))
        .when(_lssSavedLeadAccountDB)
        .getLeadAccountAssociations(eq(SEAT_URN), eq(0), eq(-1));

    List<LeadAccountAssociation> result = await(
        _salesLeadAccountAssociationService.getLeadAccountAssociations(SEAT_URN, 0, -1));
    assertEquals(result.size(), 2);
  }

  private LeadToAccountAssociation createLeadToAccountAssociation() {
    LeadToAccountAssociation leadToAccountAssociation = new LeadToAccountAssociation();
    leadToAccountAssociation.contractUrn = CONTRACT_URN.toString();
    leadToAccountAssociation.createdTime = 2L;
    leadToAccountAssociation.lastModifiedTime = 2L;
    return leadToAccountAssociation;
  }
  private ChangeAuditStamps createChangeAuditStamps() {
    ChangeAuditStamps changeAuditStamps = new ChangeAuditStamps();
    AuditStamp timeStamp = new AuditStamp();
    timeStamp.setActor(SEAT_URN);
    timeStamp.setTime(System.currentTimeMillis());
    changeAuditStamps.setCreated(timeStamp);
    changeAuditStamps.setLastModified(timeStamp);
    return changeAuditStamps;
  }
  private CompoundKey getCompoundKey(@NonNull LeadAccountAssociation leadAccountAssociation) {
    return new CompoundKey().append(CREATOR_COMPOUND_KEY, leadAccountAssociation.getCreator())
        .append(LEAD_COMPOUND_KEY, leadAccountAssociation.getLead())
        .append(ACCOUNT_COMPOUND_KEY, leadAccountAssociation.getAccount());
  }

  private CompoundKey createCompoundKey() throws URISyntaxException{
    OrganizationUrn organizationUrn = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:********"));
    return new CompoundKey().append(CREATOR_COMPOUND_KEY, SEAT_URN)
        .append(LEAD_COMPOUND_KEY, new MemberUrn(1L))
        .append(ACCOUNT_COMPOUND_KEY, organizationUrn);
  }
}
