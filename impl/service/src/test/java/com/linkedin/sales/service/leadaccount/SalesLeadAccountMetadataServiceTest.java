package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesleadaccount.LeadAccountMetadata;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 * This is the class to test {@link SalesLeadAccountMetadataService}
 */
public class SalesLeadAccountMetadataServiceTest extends ServiceUnitTest {
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);

  private SalesLeadAccountMetadataService _salesLeadAccountMetadataService;

  @Mock
  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesLeadAccountMetadataService = new SalesLeadAccountMetadataService(_lssSavedLeadAccountDB);
  }

  @Test
  public void testBatchGetAssociatedLeadCounts_Empty() {
    Map<OrganizationUrn, LeadAccountMetadata> leadCounts = await(
        _salesLeadAccountMetadataService.batchGetAssociatedLeadCounts(
            Collections.emptySet(), SEAT_URN));
    assertEquals(leadCounts.size(), 0);
    verifyNoInteractions(_lssSavedLeadAccountDB);
  }

  @Test
  public void testBatchGetAssociatedLeadCounts_Succeed() throws URISyntaxException {
    OrganizationUrn organizationUrn1 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:1234"));
    OrganizationUrn organizationUrn2 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:1111"));
    Map<Urn, Integer> resultMap = ImmutableMap.of(organizationUrn1, 10, organizationUrn2, 0);
    doReturn(Task.value(resultMap)).when(_lssSavedLeadAccountDB).getAssociatedLeadCounts(anySet(), any());

    Map<OrganizationUrn, LeadAccountMetadata> leadCounts = await(
        _salesLeadAccountMetadataService.batchGetAssociatedLeadCounts(
            ImmutableSet.of(organizationUrn1, organizationUrn2), SEAT_URN));
    assertEquals(leadCounts.size(), 2);
    assertTrue(leadCounts.containsKey(organizationUrn1));
    assertEquals(leadCounts.get(organizationUrn1).getTotalCount(), resultMap.get(organizationUrn1));
    assertTrue(leadCounts.containsKey(organizationUrn2));
    assertEquals(leadCounts.get(organizationUrn2).getTotalCount(), resultMap.get(organizationUrn2));
  }

  @Test
  public void testBatchGetAssociatedLeadCounts_FailWithDBException() throws URISyntaxException {
    OrganizationUrn organizationUrn1 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:1234"));
    OrganizationUrn organizationUrn2 = OrganizationUrn.createFromUrn(new Urn("urn:li:organization:1111"));
    doReturn(Task.failure(new RuntimeException("get lead counts fail"))).when(_lssSavedLeadAccountDB).getAssociatedLeadCounts(anySet(), any());

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesLeadAccountMetadataService.batchGetAssociatedLeadCounts(ImmutableSet.of(organizationUrn1, organizationUrn2), SEAT_URN));
    }).withCause(new RuntimeException("get lead counts fail"));
  }
}


