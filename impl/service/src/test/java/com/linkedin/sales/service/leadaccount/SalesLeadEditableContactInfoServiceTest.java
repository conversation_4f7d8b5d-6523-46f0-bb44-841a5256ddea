package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.AddressArray;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.PhoneNumber;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.Address;
import com.linkedin.sales.espresso.Email;
import com.linkedin.sales.espresso.LeadEditableContactInfo;
import com.linkedin.sales.espresso.TypedPhoneNumber;
import com.linkedin.sales.espresso.PhoneNumberType;
import com.linkedin.sales.espresso.SocialHandle;
import com.linkedin.sales.espresso.SocialHandleProvider;
import com.linkedin.sales.espresso.Website;
import com.linkedin.sales.espresso.WebsiteCategory;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfo;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfoKey;
import com.linkedin.salesleadaccount.SocialHandleArray;
import com.linkedin.salesleadaccount.TypedPhoneNumberArray;
import com.linkedin.salesleadaccount.WebsiteArray;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


public class SalesLeadEditableContactInfoServiceTest extends ServiceUnitTest {

  private static final SeatUrn SEAT_URN = new SeatUrn(111L);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(222L);
  private static final SeatUrn DEFAULT_SEAT_URN = new SeatUrn(0L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final Long MEMBER_ID = 1111L;
  private static final MemberUrn MEMBER_URN;
  private static final String FULL_ADDRESS = "full address";
  private static final String FULL_ADDRESS1 = "full address1";
  private static final String USER_NAME = "test test";
  private static final String PHONE_NUMBER = "88888776";
  private static final long CREATED_TIME = 11111L;
  private static final String EMAIL  = "<EMAIL>";

  static {
    try {
      MEMBER_URN =
          MemberUrn.createFromUrn(Urn.createFromTuple("member", MEMBER_ID));
    } catch (URISyntaxException e) {
      throw new RuntimeException();
    }
  }

  private SalesLeadEditableContactInfoService _salesLeadEditableContactInfoService;

  @Mock
  private LssLeadExtendedInfoDB _lssLeadExtendedInfoDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesLeadEditableContactInfoService = new SalesLeadEditableContactInfoService(_lssLeadExtendedInfoDB);
  }

  @BeforeMethod
  public void resetMock() {
    reset(_lssLeadExtendedInfoDB);
  }

  @Test
  public void testGetLeadEditableContactInfoWithNoEntityFoundExceptionFromDB() {
      SalesLeadEditableContactInfoKey key =
          new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
      doReturn(Task.failure(
          new EntityNotFoundException(null, String.format("can not find leadEditableContactInfo:%s", key)))).when(
          _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
      SalesLeadEditableContactInfo result = await(_salesLeadEditableContactInfoService.getLeadEditableContactInfo(key));
      assertEquals(result.getCreated().getActor(), new SeatUrn(0L));
      assertEquals(result.getLastModified().getActor(), new SeatUrn(0L));
  }

  @Test
  public void testGetLeadEditableContactInfoFromDB() {
    SalesLeadEditableContactInfo expectedRes = createSalesLeadEditableContactInfo();
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    doReturn(Task.value(createLeadEditableContactInfo())).when(
        _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
    SalesLeadEditableContactInfo result = await(_salesLeadEditableContactInfoService.getLeadEditableContactInfo(key));
    assertEquals(result.getCreated(), expectedRes.getCreated());
    assertEquals(result.getLastModified(), expectedRes.getLastModified());
    assertEquals(result.getAddresses(), expectedRes.getAddresses());
    assertEquals(result.getEmails(), expectedRes.getEmails());
    assertEquals(result.getPhoneNumbers(), expectedRes.getPhoneNumbers());
    assertEquals(result.getWebsites(), expectedRes.getWebsites());
  }

  @Test
  public void testUpsertLeadEditableContactInfo() {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = createSalesLeadEditableContactInfo();
    LeadEditableContactInfo leadEditableContactInfo = createLeadEditableContactInfo();
    LeadEditableContactInfo leadEditableContactInfo1 = createLeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN_2.toString();
    leadEditableContactInfo1.creatorSeatUrn = SEAT_URN_2.toString();
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    doReturn(Task.value(leadEditableContactInfo1)).when(
        _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
    doReturn(Task.value(HttpStatus.S_200_OK)).when(
        _lssLeadExtendedInfoDB).upsertLeadEditableContactInfo(eq(key), eq(leadEditableContactInfo));
    UpdateResponse
        result = await(_salesLeadEditableContactInfoService.upsertLeadEditableInfo(key, salesLeadEditableContactInfo));
    assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testUpsertLeadEditableContactInfoWithOutCreateBefore() {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = createSalesLeadEditableContactInfo();
    LeadEditableContactInfo leadEditableContactInfo = createLeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = DEFAULT_SEAT_URN.toString();
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    LeadEditableContactInfo leadEditableContactInfo1 = createLeadEditableContactInfo();
    leadEditableContactInfo1.creatorSeatUrn = SEAT_URN.toString();
    doReturn(Task.failure(new EntityNotFoundException(null, "test"))).when(
        _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
    doReturn(Task.value(HttpStatus.S_200_OK)).when(
        _lssLeadExtendedInfoDB).upsertLeadEditableContactInfo(eq(key), eq(leadEditableContactInfo1));
    UpdateResponse
        result = await(_salesLeadEditableContactInfoService.upsertLeadEditableInfo(key, salesLeadEditableContactInfo));
    assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testEmptyUpsertLeadEditableContactInfo() {
    SalesLeadEditableContactInfo leadEditableContactInfo = new SalesLeadEditableContactInfo();
    leadEditableContactInfo.setContract(CONTRACT_URN);
    leadEditableContactInfo.setMember(MEMBER_URN);
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    doReturn(Task.value(HttpStatus.S_204_NO_CONTENT)).when(
        _lssLeadExtendedInfoDB).deleteEditableContactInfo(eq(key));
    UpdateResponse
        result = await(_salesLeadEditableContactInfoService.upsertLeadEditableInfo(key, leadEditableContactInfo));
    assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testLeadEditableContactInfoWithoutExistingRecord() {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = createSalesLeadEditableContactInfo();
    LeadEditableContactInfo leadEditableContactInfo = createLeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN.toString();
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    doReturn(Task.failure(
        new EntityNotFoundException(null, String.format("can not find leadEditableContactInfo:%s", key)))).when(
        _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
    doReturn(Task.value(HttpStatus.S_200_OK)).when(
        _lssLeadExtendedInfoDB).upsertLeadEditableContactInfo(eq(key), any());
    UpdateResponse
        result = await(_salesLeadEditableContactInfoService.upsertLeadEditableInfo(key, salesLeadEditableContactInfo));
    assertEquals(result.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testLeadEditableContactInfoWithException() {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = createSalesLeadEditableContactInfo();
    LeadEditableContactInfo leadEditableContactInfo = createLeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN.toString();
    SalesLeadEditableContactInfoKey key =
        new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
    doReturn(Task.failure(
        new RuntimeException(String.format("can not find leadEditableContactInfo:%s", key)))).when(
        _lssLeadExtendedInfoDB).getLeadEditableContactInfo(eq(key));
    doReturn(Task.value(HttpStatus.S_200_OK)).when(
        _lssLeadExtendedInfoDB).upsertLeadEditableContactInfo(eq(key), eq(leadEditableContactInfo));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesLeadEditableContactInfoService.upsertLeadEditableInfo(key, salesLeadEditableContactInfo));
    }).withCause(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR, "Failed to upsert the leadEditableContactInfo by key: {member=urn:li:member:1111, contract=urn:li:contract:123}"));
  }

  private LeadEditableContactInfo createLeadEditableContactInfo () {
    LeadEditableContactInfo leadEditableContactInfo = new LeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN.toString();
    Address address = new Address();
    address.fullAddress = FULL_ADDRESS;
    Address address1 = new Address();
    address1.fullAddress = FULL_ADDRESS1;
    SocialHandle socialHandle = new SocialHandle();
    socialHandle.username = USER_NAME;
    socialHandle.provider = SocialHandleProvider.QQ;
    TypedPhoneNumber phoneNumber = new TypedPhoneNumber();
    phoneNumber.type = PhoneNumberType.FAX;
    phoneNumber.number = PHONE_NUMBER;
    Website website1 = new Website();
    Website website2 = new Website();
    website1.url = "www.test.com";
    website1.category = WebsiteCategory.BLOG;
    website2.url = "www.test.com";
    website2.category = WebsiteCategory.COMPANY;
    Email email = new Email();
    email.email = EMAIL;
    leadEditableContactInfo.addresses = ImmutableList.of(address1, address);
    leadEditableContactInfo.socialHandles = Collections.singletonList(socialHandle);
    leadEditableContactInfo.lastModifiedBySeatUrn = SEAT_URN.toString();
    leadEditableContactInfo.typedPhoneNumbers = Collections.singletonList(phoneNumber);
    leadEditableContactInfo.websites = ImmutableList.of(website1, website2);
    leadEditableContactInfo.emails = Collections.singletonList(email);
    leadEditableContactInfo.lastModifiedTime = 11111L;
    leadEditableContactInfo.createdTime = 11111L;
    return leadEditableContactInfo;
  }

  private SalesLeadEditableContactInfo createSalesLeadEditableContactInfo () {
    SalesLeadEditableContactInfo salesLeadEditableContactInfo = new SalesLeadEditableContactInfo();
    AuditStamp created = new AuditStamp().setActor(SEAT_URN).setTime(CREATED_TIME);
    AuditStamp lastModified = created;
    salesLeadEditableContactInfo.setLastModified(lastModified);
    salesLeadEditableContactInfo.setCreated(created);
    com.linkedin.salesleadaccount.TypedPhoneNumber phoneNumber =
        new com.linkedin.salesleadaccount.TypedPhoneNumber().setNumber(new PhoneNumber().setNumber(PHONE_NUMBER)).setType(
            com.linkedin.common.PhoneNumberType.FAX);
    salesLeadEditableContactInfo.setPhoneNumbers(new TypedPhoneNumberArray(phoneNumber));
    salesLeadEditableContactInfo.setContract(CONTRACT_URN);
    salesLeadEditableContactInfo.setMember(MEMBER_URN);
    com.linkedin.common.Address address = new com.linkedin.common.Address().setLine1(FULL_ADDRESS);
    com.linkedin.common.Address address1 = new com.linkedin.common.Address().setLine1(FULL_ADDRESS1);
    salesLeadEditableContactInfo.setAddresses(new AddressArray(ImmutableList.of(address1, address)));
    com.linkedin.salesleadaccount.Website website1 = new com.linkedin.salesleadaccount.Website().setCategory(
        com.linkedin.salesleadaccount.WebsiteCategory.BLOG).setUrl("www.test.com");
    com.linkedin.salesleadaccount.Website website2 = new com.linkedin.salesleadaccount.Website().setCategory(
        com.linkedin.salesleadaccount.WebsiteCategory.COMPANY).setUrl("www.test.com");
    salesLeadEditableContactInfo.setWebsites(new WebsiteArray(ImmutableList.of(website1, website2)));
    com.linkedin.salesleadaccount.SocialHandle socialHandle = new com.linkedin.salesleadaccount.SocialHandle();
    socialHandle.setProvider(com.linkedin.salesleadaccount.SocialHandleProvider.QQ).setUsername(USER_NAME);
    salesLeadEditableContactInfo.setSocialHandles(new SocialHandleArray(socialHandle));

    salesLeadEditableContactInfo.setEmails(new StringArray(EMAIL));
    return salesLeadEditableContactInfo;
  }
}

