package com.linkedin.sales.service.leadaccount;

import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.sales.ds.db.LssLeadExtendedInfoDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.LeadProfileUnlockInfo;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesleadaccount.SalesLeadProfileUnlockInfo;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Map;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;

public class SalesLeadProfileUnlockInfoServiceTest extends ServiceUnitTest {

  private static final SeatUrn SEAT_URN = new SeatUrn(111L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final Long MEMBER_ID = 1111L;
  private static final MemberUrn MEMBER_URN;
  private static final long CREATED_TIME = 11111L;

  static {
    try {
      MEMBER_URN =
          MemberUrn.createFromUrn(Urn.createFromTuple("member", MEMBER_ID));
    } catch (URISyntaxException e) {
      throw new RuntimeException();
    }
  }

  private SalesLeadProfileUnlockInfoService _salesLeadProfileUnlockInfoService;

  @Mock
  private LssLeadExtendedInfoDB _lssLeadExtendedInfoDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesLeadProfileUnlockInfoService = new SalesLeadProfileUnlockInfoService(_lssLeadExtendedInfoDB);
  }

  @BeforeMethod
  public void resetMock() {
    reset(_lssLeadExtendedInfoDB);
  }

  @Test
  public void testGetLeadProfileUnlockInfoFromDBHappyCase() {
    LeadProfileUnlockInfo leadProfileUnlockInfo = new LeadProfileUnlockInfo();
    leadProfileUnlockInfo.unlockedBySeatUrn = SEAT_URN.toString();
    leadProfileUnlockInfo.unlockedTime = CREATED_TIME;
    doReturn(Task.value(leadProfileUnlockInfo)).when(
        _lssLeadExtendedInfoDB).getLeadProfileUnlockInfo(eq(MEMBER_URN), eq(CONTRACT_URN));
    CompoundKey key = createCompoundKey();
    Map<CompoundKey, SalesLeadProfileUnlockInfo> result =
        await(_salesLeadProfileUnlockInfoService.batchGetLeadProfileUnlockInfo(Collections.singleton(key)));
    assertEquals(result.values().size(), 1);
    assertEquals(result.values().iterator().next().getUnlockedAt().getActor(), SEAT_URN);
  }

  @Test
  public void testGetLeadProfileUnlockInfoFromDBWithNoEntityFindException() {
    LeadProfileUnlockInfo leadProfileUnlockInfo = new LeadProfileUnlockInfo();
    leadProfileUnlockInfo.unlockedBySeatUrn = SEAT_URN.toString();
    leadProfileUnlockInfo.unlockedTime = CREATED_TIME;
    doReturn(Task.failure(
        new EntityNotFoundException(null, String.format("can not find leadProfileUnlockInfo")))).when(
        _lssLeadExtendedInfoDB).getLeadProfileUnlockInfo(eq(MEMBER_URN), eq(CONTRACT_URN));
    CompoundKey key = createCompoundKey();
    Map<CompoundKey, SalesLeadProfileUnlockInfo> result =
        await(_salesLeadProfileUnlockInfoService.batchGetLeadProfileUnlockInfo(Collections.singleton(key)));
    assertEquals(result.isEmpty(), true);
  }

  @Test
  public void testCreateProfileUnlock() {
    SalesLeadProfileUnlockInfo salesLeadProfileUnlockInfo = new SalesLeadProfileUnlockInfo();
    salesLeadProfileUnlockInfo.setContract(CONTRACT_URN);
    AuditStamp created =  new AuditStamp().setTime(CREATED_TIME).setActor(SEAT_URN);
    salesLeadProfileUnlockInfo.setUnlockedAt(created);
    salesLeadProfileUnlockInfo.setMember(MEMBER_URN);
    LeadProfileUnlockInfo leadProfileUnlockInfo  =  new LeadProfileUnlockInfo();
    leadProfileUnlockInfo.unlockedTime =  System.currentTimeMillis();
    leadProfileUnlockInfo.unlockedBySeatUrn = SEAT_URN.toString();
    doReturn(Task.value(HttpStatus.S_200_OK)).when(
        _lssLeadExtendedInfoDB).createLeadProfileUnlockInfo(eq(MEMBER_URN), eq(CONTRACT_URN), any());
    Pair<CompoundKey, CreateResponse> res =  await(_salesLeadProfileUnlockInfoService.createLeadProfileUnlockInfo(salesLeadProfileUnlockInfo));
    CompoundKey key = createCompoundKey();
    assertEquals(res.getFirst(), key);
    assertEquals(res.getSecond().getStatus(), HttpStatus.S_200_OK);
  }

   private CompoundKey createCompoundKey() {
    CompoundKey key  =  new CompoundKey().append(MEMBER_COMPOUND_KEY, MEMBER_URN)
         .append(CONTRACT_COMPOUND_KEY, CONTRACT_URN);
    return key;
   }
}

