package com.linkedin.sales.service.leadaccount;

import com.google.common.collect.ImmutableMap;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssSavedLeadAccountDB;
import com.linkedin.sales.espresso.SavedLead;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesleadaccount.LeadDataSource;
import com.linkedin.salesleadaccount.SalesLead;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.LixUtils.*;
import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 * This is the class to test {@link SavedLeadService}
 */
public class SavedLeadServiceTest extends ServiceUnitTest {
  private static final ContractUrn CONTRACT_UNDER_TEST = new ContractUrn(100L);
  private static final SeatUrn SEAT_UNDER_TEST = new SeatUrn(2000L);
  private static final Map<String, Integer> _lssLeadLimitByTier =
      ImmutableMap.of("SALES_SEAT_TIER0", 1, "SALES_SEAT_TIER1", 5, "SALES_SEAT_TIER2", 10, "SALES_SEAT_TIER3", 100);

  private SavedLeadService _savedLeadService;

  @Mock
  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;

  @Mock
  private LixService _lixService;

  @BeforeMethod
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _savedLeadService =
        new SavedLeadService(_lssSavedLeadAccountDB,
            _lixService, 100);
    doReturn(Task.value("control")).when(_lixService)
        .getLixTreatment(SEAT_UNDER_TEST, LIX_SALES_ENTITIES_DELETION_BATCH_SIZE, null);
    doReturn(Task.value(5)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());
  }

  @Test
  public void testCreateSavedLead() {
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));
    doReturn(Task.value(HttpStatus.S_201_CREATED))
        .when(_lssSavedLeadAccountDB)
        .createSavedLead(any(), any(), any());

    doReturn(Task.value(0)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    SalesLead salesLead = new SalesLead();
    salesLead.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(123L));

    CreateResponse createResponse = await(_savedLeadService.createSavedLead(salesLead));
    Assert.assertEquals(createResponse.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateSavedLeadLeadLimitExceeded() {
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));
    doReturn(Task.value(101)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    SalesLead salesLead = new SalesLead();
    salesLead.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(123L));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_savedLeadService.createSavedLead(salesLead)))
        .withCause(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded lead limit"));
  }

  @Test
  public void testCreateSavedLeadLeadLimitExceededForContractTierAgnosticLimit() {
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));

    doReturn(Task.value(100)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    SalesLead salesLead = new SalesLead();
    salesLead.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(123L));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_savedLeadService.createSavedLead(salesLead)))
        .withCause(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED, "Exceeded lead limit"));
  }

  @Test
  public void testCreateSavedLeadLeadSavedAlready() {
    SavedLead savedLead = createSavedLead();
    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(new MemberUrn(123L), savedLead);
    List<Pair<MemberUrn, SavedLead>> pairs = new ArrayList<>();
    pairs.add(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));
    doReturn(Task.value(10)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    SalesLead salesLead = new SalesLead();
    salesLead.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(123L));

    CreateResponse createResponse = await(_savedLeadService.createSavedLead(salesLead));
    Assert.assertEquals(createResponse.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testBatchCreateSavedLeadSucceed() {
    Set<SalesLead> salesLeads = new HashSet<>();

    SalesLead salesLead1 = new SalesLead();
    SalesLead salesLead2 = new SalesLead();
    SalesLead salesLead3 = new SalesLead();
    salesLead1.setContract(CONTRACT_UNDER_TEST)
        .setOwner(SEAT_UNDER_TEST)
        .setMember(new MemberUrn(123L))
        .setDataSource(LeadDataSource.CRM_SYNC);
    salesLead2.setContract(CONTRACT_UNDER_TEST)
        .setOwner(SEAT_UNDER_TEST)
        .setMember(new MemberUrn(234L))
        .setDataSource(LeadDataSource.USER_GENERATED);
    salesLead3.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(345L));

    salesLeads.add(salesLead1);
    salesLeads.add(salesLead2);
    salesLeads.add(salesLead3);

    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(new MemberUrn(123L), createSavedLead());
    List<Pair<MemberUrn, SavedLead>> pairs = Collections.singletonList(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(234L)), eq(0), eq(10));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(345L)), eq(0), eq(10));

    doReturn(Task.value(0)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    Map<MemberUrn, HttpStatus> responseMap = new HashMap<>();
    responseMap.put(new MemberUrn(234L), HttpStatus.S_201_CREATED);
    responseMap.put(new MemberUrn(345L), HttpStatus.S_201_CREATED);
    doReturn(Task.value(responseMap)).when(_lssSavedLeadAccountDB).createSavedLeads(eq(SEAT_UNDER_TEST), anyMap());
    doReturn(Task.value(2)).when(_lixService).getEntityBatchCreateConcurrencyLevel(any(), any());

    Map<CompoundKey, CreateResponse> resultMap = await(_savedLeadService.batchCreateSavedLeads(salesLeads));
    Assert.assertEquals(resultMap.size(), 3);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead1)).getStatus(), HttpStatus.S_200_OK);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead2)).getStatus(), HttpStatus.S_201_CREATED);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead3)).getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testBatchCreateSavedLeadFailedWithExceptionFromCreateLeads() {
    Set<SalesLead> salesLeads = new HashSet<>();

    SalesLead salesLead1 = new SalesLead();
    SalesLead salesLead2 = new SalesLead();
    SalesLead salesLead3 = new SalesLead();
    salesLead1.setContract(CONTRACT_UNDER_TEST)
        .setOwner(SEAT_UNDER_TEST)
        .setMember(new MemberUrn(123L))
        .setDataSource(LeadDataSource.CRM_SYNC);
    salesLead2.setContract(CONTRACT_UNDER_TEST)
        .setOwner(SEAT_UNDER_TEST)
        .setMember(new MemberUrn(234L))
        .setDataSource(LeadDataSource.USER_GENERATED);
    salesLead3.setContract(CONTRACT_UNDER_TEST).setOwner(SEAT_UNDER_TEST).setMember(new MemberUrn(345L));

    salesLeads.add(salesLead1);
    salesLeads.add(salesLead2);
    salesLeads.add(salesLead3);

    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(new MemberUrn(123L), createSavedLead());
    List<Pair<MemberUrn, SavedLead>> pairs = Collections.singletonList(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(123L)), eq(0), eq(10));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(234L)), eq(0), eq(10));
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(new MemberUrn(345L)), eq(0), eq(10));
    doReturn(Task.value(0)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));

    doReturn(Task.failure(new RuntimeException("Failed to create savedLeads"))).when(_lssSavedLeadAccountDB)
        .createSavedLeads(eq(SEAT_UNDER_TEST), anyMap());

    Map<CompoundKey, CreateResponse> resultMap = await(_savedLeadService.batchCreateSavedLeads(salesLeads));
    Assert.assertEquals(resultMap.size(), 3);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead1)).getStatus(), HttpStatus.S_200_OK);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead2)).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    Assert.assertEquals(resultMap.get(buildSavedLeadCompoundKey(salesLead3)).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testDeleteSavedLead() {
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), any());

    UpdateResponse updateResponse =
        await(_savedLeadService.deleteSavedLead(buildSavedLeadCompoundKey(SEAT_UNDER_TEST, new MemberUrn(123L))));
    Assert.assertEquals(updateResponse.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteSavedLeadNonExistence() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSavedLeadAccountDB).deleteSavedLead(any(), any());

    UpdateResponse updateResponse =
        await(_savedLeadService.deleteSavedLead(buildSavedLeadCompoundKey(SEAT_UNDER_TEST, new MemberUrn(123L))));
    Assert.assertEquals(updateResponse.getStatus(), HttpStatus.S_200_OK);
  }

  @Test
  public void testBatchDeleteSavedLead() {
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(123L)));
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(234L)));
    Mockito.doReturn(Task.value(HttpStatus.S_500_INTERNAL_SERVER_ERROR))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(345L)));

    Set<CompoundKey> compoundKeys = new HashSet<>();
    CompoundKey compoundKey1 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, new MemberUrn(123L));
    CompoundKey compoundKey2 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, new MemberUrn(234L));
    CompoundKey compoundKey3 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, new MemberUrn(345L));
    CompoundKey compoundKey4 =
        new CompoundKey().append(OWNER_COMPOUND_KEY, new MemberUrn(100L)).append(MEMBER_COMPOUND_KEY, SEAT_UNDER_TEST);
    compoundKeys.add(compoundKey1);
    compoundKeys.add(compoundKey2);
    compoundKeys.add(compoundKey3);
    compoundKeys.add(compoundKey4);

    Map<CompoundKey, UpdateResponse> resultMap = await(_savedLeadService.batchDeleteSavedLeads(compoundKeys));
    Assert.assertEquals(resultMap.size(), 4);
    Assert.assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    Assert.assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_200_OK);
    Assert.assertEquals(resultMap.get(compoundKey3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    Assert.assertEquals(resultMap.get(compoundKey4).getStatus(), HttpStatus.S_400_BAD_REQUEST);

    doReturn(Task.value("invalid_value")).when(_lixService).getLixTreatment(SEAT_UNDER_TEST, LIX_SALES_ENTITIES_DELETION_BATCH_SIZE, null);
    resultMap = await(_savedLeadService.batchDeleteSavedLeads(compoundKeys));
    Assert.assertEquals(resultMap.size(), 4);
    Assert.assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    Assert.assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_200_OK);
    Assert.assertEquals(resultMap.get(compoundKey3).getStatus(), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    Assert.assertEquals(resultMap.get(compoundKey4).getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  @Test(expectedExceptions = RestLiServiceException.class)
  public void batchDeleteSavedLeadThrowsWhenSeatUrnNotFound() {
    Mockito.doReturn(Task.value(HttpStatus.S_204_NO_CONTENT))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(123L)));
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(234L)));
    Mockito.doReturn(Task.value(HttpStatus.S_500_INTERNAL_SERVER_ERROR))
        .when(_lssSavedLeadAccountDB)
        .deleteSavedLead(any(), eq(new MemberUrn(345L)));

    Set<CompoundKey> compoundKeys = new HashSet<>();
    CompoundKey compoundKey =
        new CompoundKey().append(OWNER_COMPOUND_KEY, new MemberUrn(100L)).append(MEMBER_COMPOUND_KEY, SEAT_UNDER_TEST);
    compoundKeys.add(compoundKey);

    await(_savedLeadService.batchDeleteSavedLeads(compoundKeys));
  }

  @Test
  public void testGetSalesLeadsForGivenOwnerSucceed() {
    MemberUrn memberUrn1 = new MemberUrn(123L);
    MemberUrn memberUrn2 = new MemberUrn(234L);
    MemberUrn memberUrn3 = new MemberUrn(345L);
    SavedLead savedLead = createSavedLead();
    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(memberUrn1, savedLead);
    Pair<MemberUrn, SavedLead> pair2 = new Pair<>(memberUrn2, savedLead);
    Pair<MemberUrn, SavedLead> pair3 = new Pair<>(memberUrn3, savedLead);
    List<Pair<MemberUrn, SavedLead>> pairs = new ArrayList<>();
    pairs.add(pair1);
    pairs.add(pair2);
    pairs.add(pair3);

    doReturn(Task.value(3)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(null), eq(0), eq(10));
    PaginatedList<SalesLead> result = await(_savedLeadService.getSalesLeadsForGivenOwner(SEAT_UNDER_TEST, 0, 10));
    assertEquals(result.getTotal(), 3);
    List<SalesLead> resultList = result.getResult();
    assertEquals(resultList.size(), 3);
    assertEquals(resultList.get(0).getMember(), memberUrn1);
    assertEquals(resultList.get(0).getOwner(), SEAT_UNDER_TEST);
    assertEquals(resultList.get(0).getContract(), CONTRACT_UNDER_TEST);
    assertEquals(resultList.get(1).getMember(), memberUrn2);
    assertEquals(resultList.get(2).getMember(), memberUrn3);

    PaginatedList<SalesLead> countOnlyResult =
        await(_savedLeadService.getSalesLeadsForGivenOwner(SEAT_UNDER_TEST, 0, 0));
    assertEquals(countOnlyResult.getTotal(), 3);
    assertEquals(countOnlyResult.getResult().size(), 0);
  }

  @Test
  public void testGetSalesLeadsForGivenOwnerFailure() {
    doReturn(Task.value(3)).when(_lssSavedLeadAccountDB).getSavedLeadCountForSeat(eq(SEAT_UNDER_TEST));
    doReturn(Task.failure(new RuntimeException("Failed to get savedAccount"))).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(null), eq(0), eq(3));
    PaginatedList<SalesLead> result = await(_savedLeadService.getSalesLeadsForGivenOwner(SEAT_UNDER_TEST, 0, 3));
    assertEquals(result.getTotal(), 0);
    assertEquals(result.getResult().size(), 0);
  }

  @Test
  public void testGetSalesLeadSuccess() {
    MemberUrn memberUrn1 = new MemberUrn(123L);
    SavedLead savedLead = createSavedLead();
    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(memberUrn1, savedLead);
    List<Pair<MemberUrn, SavedLead>> pairs = new ArrayList<>();
    pairs.add(pair1);
    doReturn(Task.value(pairs)).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(memberUrn1), eq(0), eq(10));
    SalesLead result = await(_savedLeadService.getSalesLead(buildSavedLeadCompoundKey(SEAT_UNDER_TEST, memberUrn1)));
    assertEquals(result.getMember(), memberUrn1);
    assertEquals(result.getOwner(), SEAT_UNDER_TEST);
  }

  @Test
  public void testGetSalesLeadNotFound() {
    MemberUrn memberUrn1 = new MemberUrn(123L);
    doReturn(Task.value(Collections.emptyList())).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(memberUrn1), eq(0), eq(10));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_savedLeadService.getSalesLead(buildSavedLeadCompoundKey(SEAT_UNDER_TEST, memberUrn1))))
        .withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND));
  }

  @Test
  public void testGetSalesLeadBadRequest() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_savedLeadService.getSalesLead(new CompoundKey().append("owner", "val1").append("bcd", "val2"))))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST));
  }

  @Test
  public void testBatchGetSalesLeads() {
    MemberUrn memberUrn1 = new MemberUrn(123L);
    MemberUrn memberUrn2 = new MemberUrn(234L);
    MemberUrn memberUrn3 = new MemberUrn(345L);
    SavedLead savedLead = createSavedLead();
    Pair<MemberUrn, SavedLead> pair1 = new Pair<>(memberUrn1, savedLead);
    Pair<MemberUrn, SavedLead> pair2 = new Pair<>(memberUrn2, savedLead);
    Pair<MemberUrn, SavedLead> pair3 = new Pair<>(memberUrn3, savedLead);

    doReturn(Task.value(Collections.singletonList(pair1))).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(memberUrn1), eq(0), eq(10));
    doReturn(Task.value(Collections.singletonList(pair2))).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(memberUrn2), eq(0), eq(10));
    doReturn(Task.value(Collections.singletonList(pair3))).when(_lssSavedLeadAccountDB)
        .getSavedLeads(eq(SEAT_UNDER_TEST), eq(memberUrn3), eq(0), eq(10));

    Set<CompoundKey> compoundKeys = new HashSet<>();
    CompoundKey compoundKey1 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, memberUrn1);
    CompoundKey compoundKey2 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, memberUrn2);
    CompoundKey compoundKey3 = buildSavedLeadCompoundKey(SEAT_UNDER_TEST, memberUrn3);
    compoundKeys.add(compoundKey1);
    compoundKeys.add(compoundKey2);
    compoundKeys.add(compoundKey3);

    Map<CompoundKey, SalesLead> result = await(_savedLeadService.batchGetSalesLeads(compoundKeys));
    assertEquals(result.size(), 3);
    assertEquals(result.get(compoundKey1).getMember(), memberUrn1);
    assertEquals(result.get(compoundKey1).getOwner(), SEAT_UNDER_TEST);
    assertEquals(result.get(compoundKey1).getContract(), CONTRACT_UNDER_TEST);
    assertEquals(result.get(compoundKey1).getDataSource(), LeadDataSource.USER_GENERATED);
    assertEquals(result.get(compoundKey2).getMember(), memberUrn2);
    assertEquals(result.get(compoundKey3).getMember(), memberUrn3);
    assertEquals(result.get(compoundKey3).getDataSource(), LeadDataSource.USER_GENERATED);
  }

  private CompoundKey buildSavedLeadCompoundKey(SalesLead salesLead) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, salesLead.getOwner())
        .append(MEMBER_COMPOUND_KEY, salesLead.getMember());
  }

  private CompoundKey buildSavedLeadCompoundKey(SeatUrn ownerSeatUrn, MemberUrn leadMemberUrn) {
    return new CompoundKey().append(OWNER_COMPOUND_KEY, ownerSeatUrn).append(MEMBER_COMPOUND_KEY, leadMemberUrn);
  }

  private SavedLead createSavedLead(){
    SavedLead savedLead = new SavedLead();
    savedLead.contractUrn = CONTRACT_UNDER_TEST.toString();
    savedLead.createdTime = System.currentTimeMillis();
    savedLead.dataSource = com.linkedin.sales.espresso.LeadDataSource.USER_GENERATED;

    return savedLead;
  }
}
