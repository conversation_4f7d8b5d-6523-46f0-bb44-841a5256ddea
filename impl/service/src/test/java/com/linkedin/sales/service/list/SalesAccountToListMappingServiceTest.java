package com.linkedin.sales.service.list;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.saleslist.AccountToListMapping;
import com.linkedin.util.collections.list.PaginatedList;
import java.util.Collections;
import java.net.URISyntaxException;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for SalesAccountToListMappingService
 */
public class SalesAccountToListMappingServiceTest extends ServiceUnitTest {
  private static final long SEAT_ID = 2000L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final long ORGANIZATION_ID = 3000L;
  private static final long LIST_ID = 1L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);

  @Mock
  private LssListDB _lssListDB;

  private SalesAccountToListMappingService _salesAccountToListMappingService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesAccountToListMappingService = new SalesAccountToListMappingService(_lssListDB);
  }

  //------------- tests for create accountToListMapping -------------//

  @Test
  public void testCreateAccountToListMappingSucceed() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    com.linkedin.sales.espresso.AccountToListMapping espressoAccountToListMapping =
        _salesAccountToListMappingService.createEspressoAccountToListMappingFromService(accountToListMapping);
    when(_lssListDB.createAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID), eq(espressoAccountToListMapping))).thenReturn(
        Task.value(HttpStatus.S_201_CREATED));

    CreateResponse result =
        await(_salesAccountToListMappingService.createAccountToListMapping(accountToListMapping));
    assertThat(((CompoundKey)(result.getId())).getPart(OWNER_COMPOUND_KEY)).isEqualTo(
        compoundKey.getPart(OWNER_COMPOUND_KEY));
    assertThat(((CompoundKey)(result.getId())).getPart(ACCOUNT_COMPOUND_KEY)).isEqualTo(
        compoundKey.getPart(ACCOUNT_COMPOUND_KEY));
    assertThat(((CompoundKey)(result.getId())).getPart(LIST_COMPOUND_KEY)).isEqualTo(compoundKey.getPart(LIST_COMPOUND_KEY));
    assertThat(result.getStatus()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateAccountToListMappingSucceedWithAlreadyCreated() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    com.linkedin.sales.espresso.AccountToListMapping espressoAccountToListMapping =
        _salesAccountToListMappingService.createEspressoAccountToListMappingFromService(accountToListMapping);
    when(_lssListDB.createAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID),
        eq(espressoAccountToListMapping))).thenReturn(
        Task.value(HttpStatus.S_412_PRECONDITION_FAILED));

    CreateResponse result =
        await(_salesAccountToListMappingService.createAccountToListMapping(accountToListMapping));
    assertThat(((CompoundKey)(result.getId())).getPart(OWNER_COMPOUND_KEY)).isEqualTo(
        compoundKey.getPart(OWNER_COMPOUND_KEY));
    assertThat(((CompoundKey)(result.getId())).getPart(ACCOUNT_COMPOUND_KEY)).isEqualTo(
        compoundKey.getPart(ACCOUNT_COMPOUND_KEY));
    assertThat(((CompoundKey)(result.getId())).getPart(LIST_COMPOUND_KEY)).isEqualTo(compoundKey.getPart(LIST_COMPOUND_KEY));
    assertThat(result.getStatus()).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testCreateAccountToListMappingFailed() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    com.linkedin.sales.espresso.AccountToListMapping espressoAccountToListMapping =
        _salesAccountToListMappingService.createEspressoAccountToListMappingFromService(accountToListMapping);
    when(_lssListDB.createAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID),
        eq(espressoAccountToListMapping))).thenReturn(
        Task.failure(new RuntimeException("create accountToListMapping failed")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesAccountToListMappingService.createAccountToListMapping(accountToListMapping));
    }).withCause(new RuntimeException("create accountToListMapping failed"));
  }

  //------------- tests for delete accountToListMapping -------------//

  @Test
  public void testDeleteAccountToListMappingSucceed() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    when(_lssListDB.deleteAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID))).thenReturn(
        Task.value(Boolean.TRUE));

    UpdateResponse updateResponse = await(_salesAccountToListMappingService.deleteAccountToListMapping(compoundKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteAccountToListMappingSucceedWithRecordNotFound() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    when(_lssListDB.deleteAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID))).thenReturn(
        Task.value(Boolean.FALSE));

    UpdateResponse updateResponse = await(_salesAccountToListMappingService.deleteAccountToListMapping(compoundKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteAccountToListMappingFailedWithDBError() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    when(_lssListDB.deleteAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID))).thenReturn(
        Task.failure(new RuntimeException("delete accountToListMapping failed")));

    UpdateResponse updateResponse = await(_salesAccountToListMappingService.deleteAccountToListMapping(compoundKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testDeleteAccountToListMappingFailedWithInvalidCompoundKey() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = new CompoundKey();
    compoundKey.append(OWNER_COMPOUND_KEY, SEAT_URN);
    compoundKey.append(ACCOUNT_COMPOUND_KEY, organizationUrn);

    when(_lssListDB.deleteAccountToListMapping(eq(SEAT_URN), eq(organizationUrn), eq(LIST_ID))).thenReturn(
        Task.failure(new RuntimeException("delete accountToListMapping failed")));

    UpdateResponse updateResponse = await(_salesAccountToListMappingService.deleteAccountToListMapping(compoundKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  //-------------- test for get accountToListMapping ---------------//

  @Test
  public void testGetListSucceed() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    com.linkedin.sales.espresso.AccountToListMapping espressoAccountToListMapping =
        _salesAccountToListMappingService.createEspressoAccountToListMappingFromService(accountToListMapping);

    when(_lssListDB.getAccountMapListIdsForGivenSeatAndAccount(eq(SEAT_URN), eq(organizationUrn), anyInt(),
        anyInt())).thenReturn(Task.value(Collections.singletonList(new Pair<>((LIST_ID), espressoAccountToListMapping))));

    PaginatedList<AccountToListMapping> resultList =
        await(_salesAccountToListMappingService.getAccountToListMappingForAccount(SEAT_URN, organizationUrn, 0, 10));
    assertThat(resultList.getTotal()).isEqualTo(1);
    assertThat(resultList.getResult().get(0)).isEqualTo(accountToListMapping);
  }

  @Test
  public void testGetListDBFailed() throws URISyntaxException {
    OrganizationUrn organizationUrn = buildOrganizationUrn(ORGANIZATION_ID);
    AccountToListMapping accountToListMapping =
        _salesAccountToListMappingService.createAccountToListMapping(SEAT_URN, organizationUrn, LIST_ID, CONTRACT_URN);
    CompoundKey compoundKey = _salesAccountToListMappingService.getCompoundKey(accountToListMapping);

    when(_lssListDB.getAccountMapListIdsForGivenSeatAndAccount(eq(SEAT_URN), eq(organizationUrn), anyInt(),
        anyInt())).thenReturn(Task.failure(new RuntimeException("failed to get accountToListMapping")));

    PaginatedList<AccountToListMapping> resultList =
        await(_salesAccountToListMappingService.getAccountToListMappingForAccount(SEAT_URN, organizationUrn, 0, 10));
    assertThat(resultList.getTotal()).isEqualTo(0);
  }

  private static OrganizationUrn buildOrganizationUrn(long organizationId) throws URISyntaxException {
    return OrganizationUrn.createFromUrn(new Urn("urn:li:organization:" + organizationId));
  }
}
