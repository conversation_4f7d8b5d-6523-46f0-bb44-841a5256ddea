package com.linkedin.sales.service.list;

import com.github.ambry.utils.ByteBufferInputStream;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.linkedin.ambry.client.AmbryConstants;
import com.linkedin.ambry.client.StreamReader;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.omni.utils.ambry.AmbryDocumentService;
import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesEntitlementArray;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesinsights.TaxonomyColumnMappingArray;
import com.linkedin.saleslist.ListCsvImport;
import com.linkedin.saleslist.ListCsvImportState;
import io.grpc.StatusRuntimeException;
import java.io.ByteArrayInputStream;
import java.net.URISyntaxException;
import java.nio.ByteBuffer;
import java.util.Collections;
import java.util.List;
import java.util.stream.IntStream;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import proto.com.linkedin.common.AmbryBlobUrn;
import proto.com.linkedin.common.ContractUrn;
import proto.com.linkedin.common.SalesListUrn;
import proto.com.linkedin.salesinsights.TaxonomyColumnMapping;
import proto.com.linkedin.salesinsights.TaxonomyColumnName;
import proto.com.linkedin.saleslist.CsvImportListSource;
import proto.com.linkedin.saleslist.ListCsvImportStartRequest;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

public class SalesAdminCsvImportServiceTest extends BaseEngineParTest {

  private static final long CONTRACT_ID = 123L;
  private static final long ADMIN_SEAT_ID = 456L;
  private static final long SELLER_SEAT_ID_1 = 789L;
  private static final long SELLER_SEAT_ID_2 = 912L;
  private static final String LIST_NAME = "list_name";
  private static final int TOTAL_LINE_COUNT = 2;
  private static final String FILE_HEADER = "Name, SeatEmail\n";
  private static final String ADMIN_CSV_BLOB_ID = "BlobId";
  private static final String SELLER_CSV_BLOB_ID = "Seller_BlobId";
  private static final long LIST_CSV_IMPORT_ID = 1L;
  private static final long CREATED_TIME = 3L;
  private static final long LAST_MODIFIED_TIME = 4L;


  @Mock
  private AmbryDocumentService ambryDocumentService;
  @Mock
  private SalesListCsvImportService salesListCsvImportService;
  @Mock
  private LixService lixService;
  @Mock
  private SalesSeatClient salesSeatClient;
  private SalesAdminCsvImportService _salesAdminCsvImportService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.openMocks(this);
    _salesAdminCsvImportService = new SalesAdminCsvImportService(ambryDocumentService, salesListCsvImportService, lixService,
        salesSeatClient);
  }

  @Test(expectedExceptions = PromiseException.class,
      expectedExceptionsMessageRegExp = ".*Admin Upload is not allowed.*")
  public void testCreateAndStartImportWhenLixIsNotEnabled() {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(false));
    runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));
  }

  @Test(expectedExceptions = StatusRuntimeException.class,
      expectedExceptionsMessageRegExp = ".*Uploading to same list is not supported.*")
  public void testCreateAndStartImportWhenImportingToSameList() {
    runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(true)));
  }

  @Test(expectedExceptions = PromiseException.class,
      expectedExceptionsMessageRegExp = ".*More than 50 Distinct Seats available in CSV.*")
  public void testCreateAndStartImportWhenMoreThanAllowedDistinctSeats() {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(true));
    StringBuilder csvStringBuilder = new StringBuilder();

    IntStream.range(0, 55).forEach(index -> csvStringBuilder.append("Account_Name,")
        .append("email")
        .append(index)
        .append("@mail.com\n"));
    StreamReader streamReader = mockStreamReader(csvStringBuilder.toString());
    when(ambryDocumentService.downloadBlob(ADMIN_CSV_BLOB_ID)).thenReturn(Task.value(streamReader));
    runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));
  }

  @Test(expectedExceptions = PromiseException.class,
      expectedExceptionsMessageRegExp = ".*More than 1000 accounts are not allowed for the Seat.*")
  public void testCreateAndStartImportWhenMoreThanAllowedAccountsForASeat() {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(true));
    StringBuilder csvStringBuilder = new StringBuilder();

    IntStream.range(0, 1010).forEach(index -> csvStringBuilder.append("Account_Name_")
        .append(index)
        .append(",<EMAIL>\n"));
    StreamReader streamReader = mockStreamReader(csvStringBuilder.toString());
    when(ambryDocumentService.downloadBlob(ADMIN_CSV_BLOB_ID)).thenReturn(Task.value(streamReader));
    runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));
  }

  @Test(expectedExceptions = PromiseException.class,
      expectedExceptionsMessageRegExp = ".*Failed to find seat for the email.*")
  public void testCreateAndStartImportForInvalidSeatId() {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(true));
    StringBuilder csvStringBuilder = new StringBuilder().append(FILE_HEADER);
    IntStream.range(0, 1).forEach(index -> csvStringBuilder.append("Account_Name,")
        .append("email")
        .append(index)
        .append("@mail.com\n"));
    StreamReader streamReader = mockStreamReader(csvStringBuilder.toString());
    when(ambryDocumentService.downloadBlob(ADMIN_CSV_BLOB_ID)).thenReturn(Task.value(streamReader));
    when(salesSeatClient.findBySeatEmail("<EMAIL>", new com.linkedin.common.urn.ContractUrn(CONTRACT_ID),
        new SeatUrn(ADMIN_SEAT_ID), SalesAdminCsvImportService.SEAT_FIELDS))
        .thenReturn(Task.value(Collections.emptyList()));
   runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));
  }

  @Test(expectedExceptions = PromiseException.class,
      expectedExceptionsMessageRegExp = ".*does not have upload entitlement.*")
  public void testCreateAndStartImportWhenNoPermissionForSeat() {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(true));
    StringBuilder csvStringBuilder = new StringBuilder().append(FILE_HEADER);
    IntStream.range(0, 1).forEach(index -> csvStringBuilder.append("Account_Name,")
        .append("email")
        .append(index)
        .append("@mail.com\n"));
    StreamReader streamReader = mockStreamReader(csvStringBuilder.toString());
    when(ambryDocumentService.downloadBlob(ADMIN_CSV_BLOB_ID)).thenReturn(Task.value(streamReader));
    when(salesSeatClient.findBySeatEmail("<EMAIL>", new com.linkedin.common.urn.ContractUrn(CONTRACT_ID),
        new SeatUrn(ADMIN_SEAT_ID), SalesAdminCsvImportService.SEAT_FIELDS))
        .thenReturn(Task.value(Collections.singletonList(new SalesSeat().setId(SELLER_SEAT_ID_2)
            .setEntitlements(new SalesEntitlementArray(Collections.singletonList(SalesEntitlement.ACCOUNT_IQ))))));
    runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));
  }

  @Test()
  public void testCreateAndStartImportHappyPath() throws URISyntaxException {
    when(lixService.isContractBasedLixWithAdminEnabled(any(com.linkedin.common.urn.ContractUrn.class),
        any(SeatUrn.class), eq(LixUtils.LSS_PAGES_ENABLE_BOOK_OF_BUSINESS_LIST_UPLOAD_BY_ADMIN)))
        .thenReturn(Task.value(true));
    StringBuilder csvStringBuilder = new StringBuilder().append(FILE_HEADER);
    IntStream.range(0, 2).forEach(index -> csvStringBuilder.append("Account_Name,")
        .append("email")
        .append(index)
        .append("@mail.com\n"));
    StreamReader streamReader = mockStreamReader(csvStringBuilder.toString());
    when(ambryDocumentService.downloadBlob(ADMIN_CSV_BLOB_ID)).thenReturn(Task.value(streamReader));
    when(salesSeatClient.findBySeatEmail("<EMAIL>", new com.linkedin.common.urn.ContractUrn(CONTRACT_ID),
        new SeatUrn(ADMIN_SEAT_ID), SalesAdminCsvImportService.SEAT_FIELDS))
        .thenReturn(Task.value(Collections.singletonList(new SalesSeat().setId(SELLER_SEAT_ID_1)
            .setEntitlements(new SalesEntitlementArray(Collections.singletonList(SalesEntitlement.LIST_CSV_UPLOAD))))));
    when(salesSeatClient.findBySeatEmail("<EMAIL>", new com.linkedin.common.urn.ContractUrn(CONTRACT_ID),
        new SeatUrn(ADMIN_SEAT_ID), SalesAdminCsvImportService.SEAT_FIELDS))
        .thenReturn(Task.value(Collections.singletonList(new SalesSeat().setId(SELLER_SEAT_ID_2)
            .setEntitlements(new SalesEntitlementArray(Collections.singletonList(SalesEntitlement.LIST_CSV_UPLOAD))))));
    when(ambryDocumentService.uploadBlob(eq(ImmutableMap.of(AmbryConstants.FileMetadataKeys.FILENAME, LIST_NAME)),
        any(ByteArrayInputStream.class), any(SeatUrn.class)))
        .thenReturn(Task.value(SELLER_CSV_BLOB_ID));
    when(salesListCsvImportService.createAndStart(getCsvImportStartRequestForSeller(SELLER_SEAT_ID_1)))
        .thenReturn(Task.value(getPegasusListCsvImport(SELLER_SEAT_ID_1, 101L, 202L)));
    when(salesListCsvImportService.createAndStart(getCsvImportStartRequestForSeller(SELLER_SEAT_ID_2)))
        .thenReturn(Task.value(getPegasusListCsvImport(SELLER_SEAT_ID_2, 201L, 302L)));
    List<proto.com.linkedin.saleslist.ListCsvImport> csvImports =
        runAndWait(_salesAdminCsvImportService.createAndStartUploadByAdmin(getCsvImportStartRequest(false)));

    assertThat(csvImports).hasSize(2);
    assertThat(csvImports).containsExactlyInAnyOrderElementsOf(ImmutableList.of(getProtoListCsvImport(SELLER_SEAT_ID_1, 101L, 202L),
        getProtoListCsvImport(SELLER_SEAT_ID_2, 201L, 302L))) ;
  }

  private ListCsvImportStartRequest getCsvImportStartRequest(boolean shouldIncludeList) {
    List<TaxonomyColumnMapping> mappings = ImmutableList.of(
        TaxonomyColumnMapping.newBuilder().setColumnName(TaxonomyColumnName.TaxonomyColumnName_NAME).setColumnIndex(1).build(),
        TaxonomyColumnMapping.newBuilder().setColumnName(TaxonomyColumnName.TaxonomyColumnName_CITY).setColumnIndex(2).build());
    ListCsvImportStartRequest.Builder builder = ListCsvImportStartRequest.newBuilder()
        .setContract(ContractUrn.newBuilder().setContractId(CONTRACT_ID).build())
        .setCreator(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(ADMIN_SEAT_ID).build())
        .setCsvImportListSource(CsvImportListSource.CsvImportListSource_CSV_IMPORT)
        .setDefaultListUponImport(true)
        .setListName(LIST_NAME)
        .setTotalLineCount(TOTAL_LINE_COUNT)
        .setRawInputFileHeader(FILE_HEADER)
        .setRawInputFile(AmbryBlobUrn.newBuilder().setBlobId(ADMIN_CSV_BLOB_ID).build())
        .setTaxonomyMapping(ListCsvImportStartRequest.TaxonomyMappingWrapper.newBuilder().addAllItems(mappings).build());

    if (shouldIncludeList) {
      builder.setList(SalesListUrn.newBuilder().setListId(23L).build());
    }
    return builder.build();
  }

  private StreamReader mockStreamReader(String text) {
    ByteBuffer byteBuffer = ByteBuffer.wrap(text.getBytes());
    ByteBufferInputStream inputStream = new ByteBufferInputStream(byteBuffer);
    StreamReader streamReader = mock(StreamReader.class);
    when(streamReader.getInputStream()).thenReturn(inputStream);
    return streamReader;
  }

  private com.linkedin.saleslist.ListCsvImportStartRequest getCsvImportStartRequestForSeller(long seatId)
      throws URISyntaxException {
    return new com.linkedin.saleslist.ListCsvImportStartRequest()
        .setCsvImportListSource(com.linkedin.saleslist.CsvImportListSource.BOOK_OF_BUSINESS)
        .setContract(new com.linkedin.common.urn.ContractUrn(CONTRACT_ID))
        .setCreator(new SeatUrn(seatId))
        .setDefaultListUponImport(true)
        .setListName(LIST_NAME)
        .setRawInputFile(com.linkedin.common.urn.AmbryBlobUrn.deserialize("urn:li:ambryBlob:" + SELLER_CSV_BLOB_ID))
        .setRawInputFileHeader(FILE_HEADER)
        .setTotalLineCount(1)
        .setTaxonomyMapping(new TaxonomyColumnMappingArray(ImmutableList.of(
            new com.linkedin.salesinsights.TaxonomyColumnMapping()
                .setColumnName(com.linkedin.salesinsights.TaxonomyColumnName.NAME).setColumnIndex(1),
            new com.linkedin.salesinsights.TaxonomyColumnMapping()
                .setColumnName(com.linkedin.salesinsights.TaxonomyColumnName.CITY).setColumnIndex(2))));
  }

  private ListCsvImport getPegasusListCsvImport(long sellerSeatId, long listId, long csvImportTaskId) {

    return new ListCsvImport()
        .setId(LIST_CSV_IMPORT_ID)
        .setCreated(CREATED_TIME)
        .setLastModified(LAST_MODIFIED_TIME)
        .setState(ListCsvImportState.IN_PROGRESS)
        .setCreator(UrnUtils.createSeatUrn(new SeatUrn(sellerSeatId)))
        .setContract(UrnUtils.createContractUrn(new com.linkedin.common.urn.ContractUrn(CONTRACT_ID)))
        .setCsvImportTask(UrnUtils.createCsvImportTaskUrn(new CsvImportTaskUrn(csvImportTaskId)))
        .setList(UrnUtils.createSalesListUrn(listId))
        .setListName(LIST_NAME);
  }

  private proto.com.linkedin.saleslist.ListCsvImport getProtoListCsvImport(long sellerSeatId, long listId, long csvImportTaskId) {
    return proto.com.linkedin.saleslist.ListCsvImport.newBuilder()
        .setId(LIST_CSV_IMPORT_ID)
        .setCreated(CREATED_TIME)
        .setLastModified(LAST_MODIFIED_TIME)
        .setState(proto.com.linkedin.saleslist.ListCsvImportState.ListCsvImportState_IN_PROGRESS)
        .setContract(ContractUrn.newBuilder().setContractId(CONTRACT_ID).build())
        .setCreator(proto.com.linkedin.common.SeatUrn.newBuilder().setSeatId(sellerSeatId).build())
        .setCsvImportTask(proto.com.linkedin.common.CsvImportTaskUrn.newBuilder().setCsvImportTaskId(csvImportTaskId).build())
        .setList(SalesListUrn.newBuilder().setListId(listId).build())
        .setDefaultListUponImport(false)
        .setImportingToExistingList(false)
        .setCsvImportListSource(CsvImportListSource.CsvImportListSource_CSV_IMPORT)
        .setListName(LIST_NAME)
        .build();
  }
}
