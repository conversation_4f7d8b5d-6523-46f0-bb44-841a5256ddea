package com.linkedin.sales.service.list;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.client.RestLiResponseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.DefaultList;
import com.linkedin.saleslist.DefaultListKey;
import com.linkedin.saleslist.ListType;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for SalesAccountToListMappingService
 */
public class SalesDefaultListServiceTest extends ServiceUnitTest {
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID_2 = 2002L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(SEAT_ID_2);
  private static final long LIST_ID = 1L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final SalesListUrn LIST_URN = UrnUtils.createSalesListUrn(LIST_ID);

  @Mock
  private LssListDB _lssListDB;

  private SalesDefaultListService _salesDefaultListService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesDefaultListService = new SalesDefaultListService(_lssListDB);
  }

  //------------- tests for upsert default -------------//

  @Test(description = "Upsert a new default list, i.e. a true create.")
  public void testUpsertDefaultList_newDefaultListSucceeded() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.DefaultList espressoDefaultList = _salesDefaultListService.createEspressoDefaultListFromService(defaultList);
    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.upsertDefaultList(eq(SEAT_URN), eq(espressoListType), eq(espressoDefaultList))).thenReturn(
        Task.value(HttpStatus.S_201_CREATED));

    UpdateResponse result =
        await(_salesDefaultListService.upsertDefaultList(defaultListKey, defaultList));
    assertThat(result.getStatus()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test(description = "Upsert an existing default list, i.e. an update.")
  public void testUpsertDefaultList_existingDefaultListSucceeded() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.DefaultList espressoDefaultList = _salesDefaultListService.createEspressoDefaultListFromService(defaultList);
    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.upsertDefaultList(eq(SEAT_URN), eq(espressoListType), eq(espressoDefaultList))).thenReturn(
        Task.value(HttpStatus.S_200_OK));

    UpdateResponse result =
        await(_salesDefaultListService.upsertDefaultList(defaultListKey, defaultList));
    assertThat(result.getStatus()).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test(description = "Upsert a default list failed because of db error.")
  public void testUpsertDefaultList_dbError()  {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.DefaultList espressoDefaultList = _salesDefaultListService.createEspressoDefaultListFromService(defaultList);
    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.upsertDefaultList(eq(SEAT_URN), eq(espressoListType), eq(espressoDefaultList))).thenReturn(
        Task.failure(new RuntimeException("upsert default list failed")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesDefaultListService.upsertDefaultList(defaultListKey, defaultList));
    }).withCause(new RuntimeException("upsert default list failed"));
  }

  @Test(description = "Setting a default list for an unsupported list type")
  public void testUpsertDefaultList_mismatchedSeatUrn() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.LEAD);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.LEAD, CONTRACT_URN, LIST_URN);

    RestLiServiceException exception =
        runAndWaitException(_salesDefaultListService.upsertDefaultList(defaultListKey, defaultList),
            RestLiServiceException.class);
    Assert.assertEquals(exception.getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  //------------- tests for delete default list -------------//

  @Test(description = "Delete an existing default list.")
  public void testDeleteAccountToListMapping_defaultListExited_Succeed() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.deleteDefaultList(eq(SEAT_URN), eq(espressoListType))).thenReturn(
        Task.value(Boolean.TRUE));

    UpdateResponse updateResponse = await(_salesDefaultListService.deleteDefaultList(defaultListKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test(description = "Delete a non-existing default list.")
  public void testDeleteAccountToListMapping_defaultListNotExited_Succeed() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.deleteDefaultList(eq(SEAT_URN), eq(espressoListType))).thenReturn(
        Task.value(Boolean.FALSE));

    UpdateResponse updateResponse = await(_salesDefaultListService.deleteDefaultList(defaultListKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test(description = "Delete a default list failed because of a db error.")
  public void testDeleteAccountToListMappingFailedWithDBError() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.deleteDefaultList(eq(SEAT_URN), eq(espressoListType))).thenReturn(
        Task.failure(new RuntimeException("delete default list failed")));

    UpdateResponse updateResponse = await(_salesDefaultListService.deleteDefaultList(defaultListKey));
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  //-------------- test for get default list ---------------//

  @Test(description = "Get a default list.")
  public void testGetDefaultList_succeeded() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.DefaultList espressoDefaultList = _salesDefaultListService.createEspressoDefaultListFromService(defaultList);
    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.getDefaultList(eq(SEAT_URN), eq(espressoListType))).thenReturn(Task.value(espressoDefaultList));

    DefaultList resultDefaultList =
        await(_salesDefaultListService.getDefaultList(defaultListKey));
    assertThat(resultDefaultList.getSeat()).isEqualTo(defaultList.getSeat());
    assertThat(resultDefaultList.getListType()).isEqualTo(defaultList.getListType());
    assertThat(resultDefaultList.getContract()).isEqualTo(defaultList.getContract());
    assertThat(resultDefaultList.getList()).isEqualTo(defaultList.getList());
  }

  @Test(description = "Get a default list failed because of a db error.")
  public void testGetDefaultList_dbError() {
    DefaultListKey defaultListKey = buildDefaultListKey(SEAT_URN, ListType.ACCOUNT);
    DefaultList defaultList =
        _salesDefaultListService.createServiceDefaultList(SEAT_URN, ListType.ACCOUNT, CONTRACT_URN, LIST_URN);

    com.linkedin.sales.espresso.ListType espressoListType = ServiceConstants.LIST_TYPE_SERVICE_TO_ESPRESSO_MAPPING.get(ListType.ACCOUNT);

    when(_lssListDB.getDefaultList(eq(SEAT_URN), eq(espressoListType))).thenReturn(
        Task.failure(new RuntimeException("get default list failed")));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesDefaultListService.getDefaultList(defaultListKey));
    }).withCause(new RuntimeException("get default list failed"));
  }

  private static DefaultListKey buildDefaultListKey(SeatUrn seatUrn, ListType listType) {
    return new DefaultListKey().setSeat(seatUrn).setListType(listType);
  }
}
