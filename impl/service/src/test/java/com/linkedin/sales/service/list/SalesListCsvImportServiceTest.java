package com.linkedin.sales.service.list;

import com.linkedin.common.urn.AmbryBlobUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.SetMode;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.PagingContext;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.salesinsights.CsvImportTaskClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.CreatorToListCsvImportView;
import com.linkedin.sales.espresso.ImportTaskToListCsvImportView;
import com.linkedin.sales.espresso.ListToListCsvImportView;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.salesinsights.CsvImportTask;
import com.linkedin.salesinsights.CsvImportTaskState;
import com.linkedin.salesinsights.TaxonomyColumnMappingArray;
import com.linkedin.saleslist.ListCsvImport;
import com.linkedin.saleslist.ListCsvImportStartRequest;
import com.linkedin.saleslist.ListCsvImportState;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.mockito.Mockito.*;


/**
 * Tests for {@link SalesListCsvImportService}
 */
public class SalesListCsvImportServiceTest extends ServiceUnitTest {

  private static final long LIST_ID = 1;
  private static final long CSV_IMPORT_TASK_ID = 2;
  private static final long LIST_CSV_IMPORT_ID = 3;
  private static final long LIST_CSV_IMPORT_ID_2 = 4;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
  private static final AmbryBlobUrn AMBRY_BLOB_URN = UrnUtils.createAmbryBlobUrn("abcd1234");
  private static final CsvImportTaskUrn CSV_IMPORT_TASK_URN = UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID);
  private static final SalesListUrn SALES_LIST_URN = UrnUtils.createSalesListUrn(LIST_ID);

  private SalesListCsvImportService _salesListCsvImportService;

  @Mock
  private LssListDB _lssListDB;

  @Mock
  private CsvImportTaskClient _csvImportTaskClient;

  @Mock
  private SalesListIdService _salesListIdService;
  @Mock
  private SalesListService _salesListService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesListCsvImportService =
        new SalesListCsvImportService(_lssListDB, _csvImportTaskClient, _salesListIdService, _salesListService);
  }

  @Test(description = "Create and start csv import to a new list.")
  public void testCreateAndStartCsvImportToNewListHappyPath() {
    ListCsvImportStartRequest startRequest = constructStartRequest();
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, startRequest);
    ListToListCsvImportView listToListCsvImportView = constructListToListCsvImportView(espressoListCsvImport);

    when(_salesListIdService.generateNextId()).thenReturn(Task.value(LIST_ID));
    when(_csvImportTaskClient.create(any())).thenReturn(Task.value(CSV_IMPORT_TASK_ID));
    when(_csvImportTaskClient.start(CSV_IMPORT_TASK_ID)).thenReturn(Task.value(null));
    when(_lssListDB.createListCsvImport(any())).thenReturn(Task.value(LIST_CSV_IMPORT_ID));
    when(_lssListDB.getListCsvImportsBySalesList(LIST_ID)).thenReturn(Task.value(listToListCsvImportView));
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(espressoListCsvImport));

    ListCsvImport result = await(_salesListCsvImportService.createAndStart(startRequest));

    assertListCsvImport(result, espressoListCsvImport);

    verify(_salesListIdService, times(1)).generateNextId();
    verify(_csvImportTaskClient, times(1)).create(any());
    verify(_csvImportTaskClient, times(1)).start(CSV_IMPORT_TASK_ID);
    verify(_lssListDB, times(1)).createListCsvImport(any());
    verify(_lssListDB, times(1)).getListCsvImport(LIST_CSV_IMPORT_ID);
    verify(_lssListDB, times(0)).getListCsvImportsBySalesList(LIST_ID);
  }

  @Test(description = "Create and start csv import to an existing list.")
  public void testCreateAndStartCsvImportToExistingListHappyPath() {
    ListCsvImportStartRequest startRequest = constructStartRequest();
    startRequest.setList(SALES_LIST_URN);
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, startRequest);

    when(_csvImportTaskClient.create(any())).thenReturn(Task.value(CSV_IMPORT_TASK_ID));
    when(_csvImportTaskClient.start(CSV_IMPORT_TASK_ID)).thenReturn(Task.value(null));
    when(_lssListDB.createListCsvImport(any())).thenReturn(Task.value(LIST_CSV_IMPORT_ID));
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(espressoListCsvImport));
    when(_lssListDB.getListCsvImportsBySalesList(LIST_ID)).thenReturn(Task.failure(new EntityNotFoundException(null, "not found")));

    ListCsvImport result = await(_salesListCsvImportService.createAndStart(startRequest));

    assertListCsvImport(result, espressoListCsvImport);

    verify(_salesListIdService, times(0)).generateNextId();
    verify(_csvImportTaskClient, times(1)).create(any());
    verify(_csvImportTaskClient, times(1)).start(CSV_IMPORT_TASK_ID);
    verify(_lssListDB, times(1)).createListCsvImport(any());
    verify(_lssListDB, times(1)).getListCsvImport(LIST_CSV_IMPORT_ID);
    verify(_lssListDB, times(1)).getListCsvImportsBySalesList(LIST_ID);
  }

  @Test(description = "Create and start csv import to an existing list - existing listCsvImport Record found.")
  public void testCreateAndStartCsvImportToExistingList_existingImportFound() {
    ListCsvImportStartRequest startRequest = constructStartRequest();
    startRequest.setList(SALES_LIST_URN);
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, startRequest);
    ListToListCsvImportView listToListCsvImportView = constructListToListCsvImportView(espressoListCsvImport);

    when(_lssListDB.getListCsvImportsBySalesList(LIST_ID)).thenReturn(Task.value(listToListCsvImportView));

    RestLiServiceException exception =
        runAndWaitException(_salesListCsvImportService.createAndStart(startRequest), RestLiServiceException.class);
    Assert.assertEquals(exception.getStatus(), HttpStatus.S_409_CONFLICT);

    verify(_salesListIdService, times(0)).generateNextId();
    verify(_csvImportTaskClient, times(0)).create(any());
    verify(_csvImportTaskClient, times(0)).start(CSV_IMPORT_TASK_ID);
    verify(_lssListDB, times(0)).createListCsvImport(any());
    verify(_lssListDB, times(0)).getListCsvImport(LIST_CSV_IMPORT_ID);
    verify(_lssListDB, times(1)).getListCsvImportsBySalesList(LIST_ID);
  }

  @Test
  public void testGet() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(espressoListCsvImport));

    ListCsvImport result = await(_salesListCsvImportService.get(LIST_CSV_IMPORT_ID));

    assertListCsvImport(result, espressoListCsvImport);
  }

  @Test
  public void testBatchGet() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    List<Long> idList = Arrays.asList(LIST_CSV_IMPORT_ID, LIST_CSV_IMPORT_ID_2);
    when(_lssListDB.getListCsvImport(eq(LIST_CSV_IMPORT_ID)))
        .thenReturn(Task.value(espressoListCsvImport));
    when(_lssListDB.getListCsvImport(eq(LIST_CSV_IMPORT_ID_2)))
        .thenReturn(Task.failure(new EntityNotFoundException(LIST_CSV_IMPORT_ID_2, "not found")));

    BatchResult<Long, ListCsvImport> result = await(_salesListCsvImportService.batchGet(new HashSet<>(idList)));

    Assert.assertEquals(result.size(), 1);
    Assert.assertEquals(result.getErrors().size(), 1);
    assertListCsvImport(result.get(LIST_CSV_IMPORT_ID), espressoListCsvImport);
    Assert.assertEquals(result.getErrors().get(LIST_CSV_IMPORT_ID_2).getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testFindByCreator() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    CreatorToListCsvImportView creatorToListCsvImportView = constructEspressoCreatorToListCsvImportView(espressoListCsvImport);
    Pair<Integer, java.util.List<Pair<Long, CreatorToListCsvImportView>>> daoResult =
        new Pair<>(1, Collections.singletonList(new Pair<>(LIST_CSV_IMPORT_ID, creatorToListCsvImportView)));
    when(_lssListDB.getListCsvImportsByCreator(any(), anyInt(), anyInt(), any())).thenReturn(Task.value(daoResult));

    BasicCollectionResult<ListCsvImport> result =
        await(_salesListCsvImportService.findByCreator(new PagingContext(0, 1), SEAT_URN, new ListCsvImportState[]{}));

    Assert.assertEquals(result.getElements().size(), 1);
    Assert.assertEquals(result.getTotal().intValue(), 1);
    assertListCsvImport(result.getElements().get(0), espressoListCsvImport);
  }

  @Test
  public void testFindByCreatorEmptyResponse() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    CreatorToListCsvImportView creatorToListCsvImportView = constructEspressoCreatorToListCsvImportView(espressoListCsvImport);
    Pair<Integer, java.util.List<Pair<Long, CreatorToListCsvImportView>>> daoResult =
        new Pair<>(1, Collections.singletonList(new Pair<>(LIST_CSV_IMPORT_ID, creatorToListCsvImportView)));
    when(_lssListDB.getListCsvImportsByCreator(any(), anyInt(), anyInt(), any())).thenReturn(
        Task.failure(new EntityNotFoundException(null, "not found")));

    BasicCollectionResult<ListCsvImport> result =
        await(_salesListCsvImportService.findByCreator(new PagingContext(0, 1), SEAT_URN, new ListCsvImportState[]{}));

    Assert.assertEquals(result.getElements().size(), 0);
    Assert.assertEquals(result.getTotal().intValue(), 0);
  }

  @Test
  public void testFindByCsvImportTask() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ImportTaskToListCsvImportView importTaskToListCsvImportView = constructEspressoImportTaskToListCsvImportView(espressoListCsvImport);

    when(_lssListDB.getListCsvImportsByCsvImportTask(CSV_IMPORT_TASK_URN)).thenReturn(Task.value(importTaskToListCsvImportView));

    BasicCollectionResult<ListCsvImport> result =
        await(_salesListCsvImportService.findByCsvImportTask(CSV_IMPORT_TASK_URN));

    Assert.assertEquals(result.getElements().size(), 1);
    assertListCsvImport(result.getElements().get(0), espressoListCsvImport);
  }

  @Test
  public void testFindByCsvImportTaskNotFound() {
    when(_lssListDB.getListCsvImportsByCsvImportTask(CSV_IMPORT_TASK_URN)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "not found")));

    RestLiServiceException result =
        runAndWaitException(_salesListCsvImportService.findByCsvImportTask(CSV_IMPORT_TASK_URN), RestLiServiceException.class);

    Assert.assertEquals(result.getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testFindByList() {
    com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListToListCsvImportView listToListCsvImportView = constructListToListCsvImportView(espressoListCsvImport);
    when(_lssListDB.getListCsvImportsBySalesList(LIST_ID)).thenReturn(Task.value(listToListCsvImportView));

    BasicCollectionResult<ListCsvImport> result =
        await(_salesListCsvImportService.findByList(SALES_LIST_URN));

    Assert.assertEquals(result.getElements().size(), 1);
    assertListCsvImport(result.getElements().get(0), espressoListCsvImport);
  }

  @Test
  public void testFindByListNotFound() {
    when(_lssListDB.getListCsvImportsBySalesList(LIST_ID)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "not found")));

    RestLiServiceException result =
        runAndWaitException(_salesListCsvImportService.findByList(SALES_LIST_URN), RestLiServiceException.class);

    Assert.assertEquals(result.getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test(description = "test partial update when importing to a new list happy case")
  public void testPartialUpdateImportingToNewList() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.SUCCEEDED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(true));
    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));
    Assert.assertTrue(result);
    verify(_salesListService, times(0)).getDeleteListEntitiesTask(anyLong(), any(), any());
    verify(_lssListDB, times(0)).deleteList(anyLong());
  }

  @Test(description = "test partial update when importing to a new list and import is not found - no cleanup")
  public void testPartialUpdateImportingToNewList_ImportNotFound() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.SUCCEEDED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(false));
    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));
    Assert.assertFalse(result);
    verify(_salesListService, times(0)).getDeleteListEntitiesTask(anyLong(), any(), any());
    verify(_lssListDB, times(0)).deleteList(anyLong());
  }

  @Test(description = "test partial update to FAILED when importing to a new list - performs cleanup")
  public void testPartialUpdateImportingToNewList_UpdateToFailed() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(true));
    when(_csvImportTaskClient.get(CSV_IMPORT_TASK_ID)).thenReturn(
        Task.value(constructCsvImportTask(CSV_IMPORT_TASK_ID, CsvImportTaskState.COMPLETE)));
    when(_salesListService.getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN)).thenReturn(Task.value(true));
    when(_lssListDB.deleteList(original.listId)).thenReturn(Task.value(true));

    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));

    Assert.assertTrue(result);
    verify(_salesListService, times(1)).getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN);
    verify(_lssListDB, times(1)).deleteList(original.listId);
  }

  @Test(description = "test partial update to FAILED when importing to a new list - performs cleanup, also mark CSVImportTask as SYSTEM_CANCELED")
  public void testPartialUpdateImportingToNewList_UpdateToFailed_CancelCSVImportTask() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_csvImportTaskClient.get(CSV_IMPORT_TASK_ID)).thenReturn(
        Task.value(constructCsvImportTask(CSV_IMPORT_TASK_ID, CsvImportTaskState.IN_PROGRESS)));
    when(_csvImportTaskClient.update(anyLong(), any())).thenReturn(Task.value(true));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(true));
    when(_salesListService.getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN)).thenReturn(Task.value(true));
    when(_lssListDB.deleteList(original.listId)).thenReturn(Task.value(true));

    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));

    Assert.assertTrue(result);
    verify(_salesListService, times(1)).getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN);
    verify(_lssListDB, times(1)).deleteList(original.listId);
  }

  @Test(description = "test partial update to FAILED when importing to a new list and update call failed - performs cleanup")
  public void testPartialUpdateImportingToNewList_UpdateToFailedCallFailed() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.failure(new IOException("failed to update listCsvImport")));
    when(_salesListService.getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN)).thenReturn(Task.value(true));
    when(_lssListDB.deleteList(original.listId)).thenReturn(Task.value(true));

    Exception exception =
        runAndWaitException(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch), IOException.class);

    Assert.assertEquals(exception.getMessage(), "failed to update listCsvImport");
    verify(_salesListService, times(1)).getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN);
    verify(_lssListDB, times(1)).deleteList(original.listId);
  }

  @Test(description = "test partial update when importing to an existing list happy case - no cleanup")
  public void testPartialUpdateImportingToExistingList() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    original.setIsImportingToExistingList(true);
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(true));
    when(_csvImportTaskClient.get(CSV_IMPORT_TASK_ID)).thenReturn(
        Task.value(constructCsvImportTask(CSV_IMPORT_TASK_ID, CsvImportTaskState.COMPLETE)));
    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));
    Assert.assertTrue(result);
    verify(_salesListService, times(0)).getDeleteListEntitiesTask(anyLong(), any(), any());
    verify(_lssListDB, times(0)).deleteList(anyLong());
  }

  @Test(description = "test partial update when importing to an existing list and import not found - no cleanup")
  public void testPartialUpdateImportingToExistingList_ImportNotFound() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    original.setIsImportingToExistingList(true);
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(false));
    boolean result = await(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch));
    Assert.assertFalse(result);
    verify(_salesListService, times(0)).getDeleteListEntitiesTask(anyLong(), any(), any());
    verify(_lssListDB, times(0)).deleteList(anyLong());
  }

  @Test(description = "test partial update to FAILED when importing to an existing list and update call failed - no cleanup")
  public void testPartialUpdateImportingToExistingList_UpdateToFailedCallFailed() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    original.setIsImportingToExistingList(true);
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.failure(new IOException("failed to update listCsvImport")));
    when(_salesListService.getDeleteListEntitiesTask(original.listId, SEAT_URN, CONTRACT_URN)).thenReturn(Task.value(true));
    when(_lssListDB.deleteList(original.listId)).thenReturn(Task.value(true));

    Exception exception =
        runAndWaitException(_salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch), IOException.class);

    Assert.assertEquals(exception.getMessage(), "failed to update listCsvImport");
    verify(_salesListService, times(0)).getDeleteListEntitiesTask(anyLong(), any(), any());
    verify(_lssListDB, times(0)).deleteList(anyLong());
  }

  @Test(description = "test partial update when importing to an existing list illegal state")
  public void testPartialUpdateIllegalStateUpdate() {
    com.linkedin.sales.espresso.ListCsvImport original =
        constructEspressoListCsvImport(LIST_ID, CSV_IMPORT_TASK_ID, constructStartRequest());
    original.state = com.linkedin.sales.espresso.ListCsvImportState.SUCCEEDED;
    ListCsvImport revised = new ListCsvImport().setState(ListCsvImportState.FAILED);
    PatchRequest<ListCsvImport> patch = PatchGenerator.diffEmpty(revised);
    when(_lssListDB.getListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(original));
    when(_lssListDB.updateListCsvImport(anyLong(), any())).thenReturn(Task.value(true));
    RestLiServiceException result = runAndWaitException(
        _salesListCsvImportService.partialUpdate(LIST_CSV_IMPORT_ID, patch), RestLiServiceException.class);
    Assert.assertEquals(result.getStatus(), HttpStatus.S_400_BAD_REQUEST);
    Assert.assertEquals(result.getMessage(), "Can't move an import from state SUCCEEDED to state FAILED");
  }

  @Test
  public void testDelete() {
    when(_lssListDB.deleteListCsvImport(LIST_CSV_IMPORT_ID)).thenReturn(Task.value(true));
    boolean result = await(_salesListCsvImportService.delete(LIST_CSV_IMPORT_ID));
    Assert.assertTrue(result);
  }

  private ListCsvImportStartRequest constructStartRequest() {
    return new ListCsvImportStartRequest()
        .setContract(CONTRACT_URN)
        .setCreator(SEAT_URN)
        .setListDescription("a list description")
        .setListName("a list name")
        .setRawInputFile(AMBRY_BLOB_URN)
        .setRawInputFileHeader("company,address")
        .setTaxonomyMapping(new TaxonomyColumnMappingArray())
        .setTotalLineCount(2);
  }

  private ListCsvImport constructListCsvImport(Long listCsvImportId, com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {

    String listDescription = Optional.ofNullable(espressoListCsvImport.listDescription)
        .map(CharSequence::toString)
        .orElse(null);

    return new ListCsvImport()
        .setId(listCsvImportId)
        .setCreated(espressoListCsvImport.createdTime)
        .setLastModified(espressoListCsvImport.lastModifiedTime)
        .setState(ListCsvImportState.IN_PROGRESS)
        .setCreator(UrnUtils.createSeatUrn(espressoListCsvImport.creatorSeatUrn))
        .setContract(UrnUtils.createContractUrn(espressoListCsvImport.contractUrn))
        .setCsvImportTask(UrnUtils.createCsvImportTaskUrn(espressoListCsvImport.csvImportTaskUrn))
        .setList(UrnUtils.createSalesListUrn(espressoListCsvImport.listId))
        .setListName(espressoListCsvImport.listName.toString())
        .setListDescription(listDescription, SetMode.IGNORE_NULL);
  }

  private CreatorToListCsvImportView constructEspressoCreatorToListCsvImportView(
      com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {

    CreatorToListCsvImportView creatorToListCsvImportView = new CreatorToListCsvImportView();

    creatorToListCsvImportView.state = espressoListCsvImport.state;
    creatorToListCsvImportView.contractUrn = espressoListCsvImport.contractUrn;
    creatorToListCsvImportView.csvImportTaskUrn = espressoListCsvImport.csvImportTaskUrn;
    creatorToListCsvImportView.listId = espressoListCsvImport.listId;
    creatorToListCsvImportView.listName = espressoListCsvImport.listName;
    creatorToListCsvImportView.listDescription = espressoListCsvImport.listDescription;

    return creatorToListCsvImportView;
  }

  private ImportTaskToListCsvImportView constructEspressoImportTaskToListCsvImportView(
      com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {

    ImportTaskToListCsvImportView importTaskToListCsvImportView = new ImportTaskToListCsvImportView();

    importTaskToListCsvImportView.state = espressoListCsvImport.state;
    importTaskToListCsvImportView.contractUrn = espressoListCsvImport.contractUrn;
    importTaskToListCsvImportView.listId = espressoListCsvImport.listId;
    importTaskToListCsvImportView.listName = espressoListCsvImport.listName;
    importTaskToListCsvImportView.listDescription = espressoListCsvImport.listDescription;
    importTaskToListCsvImportView.creatorSeatUrn = espressoListCsvImport.creatorSeatUrn;

    return importTaskToListCsvImportView;
  }

  private ListToListCsvImportView constructListToListCsvImportView(
      com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {

    ListToListCsvImportView listToListCsvImportView = new ListToListCsvImportView();

    listToListCsvImportView.state = espressoListCsvImport.state;
    listToListCsvImportView.contractUrn = espressoListCsvImport.contractUrn;
    listToListCsvImportView.listName = espressoListCsvImport.listName;
    listToListCsvImportView.listDescription = espressoListCsvImport.listDescription;
    listToListCsvImportView.creatorSeatUrn = espressoListCsvImport.creatorSeatUrn;
    listToListCsvImportView.csvImportTaskUrn = espressoListCsvImport.csvImportTaskUrn;

    return listToListCsvImportView;
  }

  private com.linkedin.sales.espresso.ListCsvImport constructEspressoListCsvImport(
      Long salesListId, Long csvImportTaskId, ListCsvImportStartRequest startRequest) {

    long now = System.currentTimeMillis();
    com.linkedin.sales.espresso.ListCsvImport listCsvImport = new com.linkedin.sales.espresso.ListCsvImport();

    listCsvImport.createdTime = now;
    listCsvImport.lastModifiedTime = now;
    listCsvImport.state = com.linkedin.sales.espresso.ListCsvImportState.IN_PROGRESS;
    listCsvImport.creatorSeatUrn = startRequest.getCreator().toString();
    listCsvImport.contractUrn = startRequest.getContract().toString();
    listCsvImport.csvImportTaskUrn = UrnUtils.createCsvImportTaskUrn(csvImportTaskId).toString();
    listCsvImport.listId = salesListId;
    listCsvImport.listName = startRequest.getListName();
    listCsvImport.listDescription = startRequest.getListDescription(GetMode.NULL);
    listCsvImport.isImportingToExistingList = startRequest.hasList();
    listCsvImport.isDefaultListUponImport = startRequest.isDefaultListUponImport();

    return listCsvImport;
  }

  private CsvImportTask constructCsvImportTask(Long csvImportTaskId, CsvImportTaskState csvImportTaskState) {
    return new CsvImportTask().setState(csvImportTaskState).setId(csvImportTaskId);
  }

  private void assertListCsvImport(ListCsvImport result, com.linkedin.sales.espresso.ListCsvImport espressoListCsvImport) {
    Assert.assertEquals(result.getContract().toString(), espressoListCsvImport.contractUrn.toString());
    Assert.assertEquals(result.getCreator().toString(), espressoListCsvImport.creatorSeatUrn.toString());
    Assert.assertEquals(result.getListDescription(), espressoListCsvImport.listDescription);
    Assert.assertEquals(result.getListName(), espressoListCsvImport.listName);
    Assert.assertEquals(result.getCsvImportTask().getIdAsLong().longValue(), CSV_IMPORT_TASK_ID);
    Assert.assertEquals(result.getState().toString(), espressoListCsvImport.state.toString());
  }
}
