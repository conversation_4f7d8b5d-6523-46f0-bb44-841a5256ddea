package com.linkedin.sales.service.list;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.CompanyUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.data.DataMap;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.ActionResult;
import com.linkedin.restli.server.BatchResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.ListEntity;
import com.linkedin.sales.espresso.ListSource;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.SearchIndexUpdatePriorityType;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.saleslist.AccountMapTier;
import com.linkedin.saleslist.LeadManager;
import com.linkedin.saleslist.LeadManagerEntityUrn;
import com.linkedin.saleslist.LeadOwner;
import com.linkedin.saleslist.LeadRelationshipStrength;
import com.linkedin.saleslist.LeadRole;
import com.linkedin.saleslist.LeadText;
import com.linkedin.saleslist.ListEntityPriorityInfo;
import com.linkedin.saleslist.ListEntityPriorityType;
import com.linkedin.saleslist.RelationshipStrengthType;
import com.linkedin.saleslist.RoleType;
import com.linkedin.salessharing.AccessDecision;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;
import org.testng.collections.Maps;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.testng.Assert.*;


/**
 * Created by hacao at 6/13/2018
 * This is the class to test {@link SalesListEntityService}
 */
public class SalesListEntityServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
  private static final SeatUrn ANOTHER_SEAT_URN = new SeatUrn(2001L);
  private static final int MAX_TIER_POSITION = 9;

  private SalesListEntityService _salesListEntityService;

  @Mock
  private LssListDB _lssListDB;

  @Mock
  private AclServiceDispatcher _aclServiceDispatcher;

  @Mock
  private LixService _lixService;

  @Mock
  private SalesListEntityPlaceholderUrnService _salesListEntityPlaceholderUrnService;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesListEntityService = new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService);

    Mockito.doReturn(Task.value("batch_size_1")).when(_lixService).
        getLixTreatment(any(), eq(LixUtils.LIX_SALES_ENTITIES_DELETION_BATCH_SIZE), any());

    Mockito.doReturn(Task.value("batch_size_1")).when(_lixService).
        getLixTreatment(any(), eq(LixUtils.LIX_SALES_ENTITIES_CREATION_BATCH_SIZE), any());

    Mockito.doReturn(Task.value(1)).when(_lixService).
        getEntityBatchCreateConcurrencyLevel(any(), any());
  }

  @Test
  public void testCreateListEntity() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateListEntityWithLowSearchIndexPriority() throws URISyntaxException {
    reset(_lssListDB);
    mockPermissionAndTypeAndSource(ListType.LEAD, ListSource.LEADS_TO_FOLLOW_UP, AccessDecision.ALLOWED);

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(1L);
    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(urn, testSalesListUrn);
    Map<Urn, ListEntity> testReturnMap = Maps.newHashMap();
    ListEntity espressoListEntityPair = listEntityPair.getSecond();
    espressoListEntityPair.setSearchIndexUpdatePriority(SearchIndexUpdatePriorityType.LOW);
    testReturnMap.put(urn, espressoListEntityPair);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(testSalesListUrn, testReturnMap, false);
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntityPair.getFirst()), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test(description = "Create list entity with value assigned to RM fields")
  public void testCreateListEntityWithRMFields() throws URISyntaxException {
    reset(_lssListDB);
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, true);
    Urn urn = new Urn("urn:li:member:1");
    MemberUrn managerMemberUrn = MemberUrn.deserialize("urn:li:member:101");
    SeatUrn ownerSeatUrn = SeatUrn.deserialize("urn:li:seat:301");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);
    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(1L);
    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(urn, testSalesListUrn);

    com.linkedin.saleslist.ListEntity listEntity = listEntityPair.getFirst();
    listEntity.setLeadManager(new LeadManager().setEntityUrn(LeadManagerEntityUrn.createWithMember(managerMemberUrn)));
    listEntity.setLeadRole(new LeadRole().setRoleType(RoleType.CHAMPION));
    listEntity.setLeadRelationshipStrength(
        new LeadRelationshipStrength().setRelationshipStrength(RelationshipStrengthType.STRONG));
    listEntity.setLeadOwner(new LeadOwner().setSeatUrn(ownerSeatUrn));
    listEntity.setPositionInLevel(4);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));

    verify(_lssListDB).upsertListEntities(any(), argThat(argument -> {
      //Prelim conditions.
      if (argument.size() != 1 || argument.get(urn).getLeadManager() == null || argument.get(urn).getLeadOwner() == null
          || argument.get(urn).getLeadRole() == null || argument.get(urn).getLeadRelationshipStrength() == null) {
        return false;
      }
      return checkRMFields(argument.get(urn), false);
    }), anyBoolean());
  }

  @Test(description = "Failure in converting restli listEntity to espresso listEntity")
  public void testFailureInConvertingListEntityFromRestliToEspresso() throws URISyntaxException {
    reset(_lssListDB);
    Urn entityUrn = new Urn("urn:li:member:1");
    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(1L);
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, false);
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(entityUrn, HttpStatus.S_201_CREATED);
    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());
    com.linkedin.saleslist.ListEntity salesListEntity = new com.linkedin.saleslist.ListEntity();
    salesListEntity.setEntity(entityUrn)
        .setList(testSalesListUrn)
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis())
        .setLeadRelationshipStrength(
            new LeadRelationshipStrength().setRelationshipStrength(RelationshipStrengthType.$UNKNOWN));

    Map<CompoundKey, CreateResponse> result = await(
        _salesListEntityService.batchCreateListEntities(Collections.singletonList(salesListEntity), CONTRACT_URN));
    assertEquals(result.size(), 1);
    assertEquals(result.get(createcCompoundKey(1L, MemberUrn.createFromUrn(entityUrn))).getStatus(),
        HttpStatus.S_400_BAD_REQUEST);
  }

  @Test(description = "Create placeholder entity, also test out the input params")
  public void testCreatePlaceholderListEntity() {
    reset(_lssListDB);
    SalesListUrn salesListUrn = UrnUtils.createSalesListUrn(1L);
    SalesListEntityPlaceholderUrn salesListEntityPlaceholderUrn = new SalesListEntityPlaceholderUrn(salesListUrn, 1000L);
    when(_salesListEntityPlaceholderUrnService.generateUrn(any())).thenReturn(Task.value(new SalesListEntityPlaceholderUrn(salesListUrn, 1000L)));
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED);
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(salesListEntityPlaceholderUrn, HttpStatus.S_201_CREATED);
    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), anyMap(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());
    ActionResult<SalesListEntityPlaceholderUrn> res =
        await(_salesListEntityService.createPlaceholderEntity(salesListUrn, CONTRACT_URN, SEAT_URN, 1001001L));
    assertEquals(res.getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(res.getValue(), salesListEntityPlaceholderUrn);
  }

  @Test(description = "Update placeholder entity with LeadText field")
  public void testUpdatePlaceholderEntityWithPlaceholderManagerAndLeadText() throws URISyntaxException {
    reset(_lssListDB);
    SalesListUrn salesListUrn = UrnUtils.createSalesListUrn(1L);
    SalesListEntityPlaceholderUrn salesListEntityPlaceholderUrn = new SalesListEntityPlaceholderUrn(salesListUrn, 1000L);
    com.linkedin.saleslist.ListEntity listEntityUpdate = new com.linkedin.saleslist.ListEntity().setLeadText(new LeadText().setTextBody("Text Body")).setLeadManager(new LeadManager().setEntityUrn(
        LeadManagerEntityUrn.createWithSalesListEntityPlaceholder(salesListEntityPlaceholderUrn)));
    CompoundKey compoundKey = createcCompoundKey(1L, salesListEntityPlaceholderUrn);
    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntityUpdate, compoundKey);
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, true);
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(salesListEntityPlaceholderUrn, HttpStatus.S_200_OK);
    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    Pair<SalesListUrn, Urn> key = Pair.make(salesListUrn, salesListEntityPlaceholderUrn);
    List<Pair<SalesListUrn, Urn>> keyList = Collections.singletonList(key);
    Map<Pair<SalesListUrn, Urn>, ListEntity> stubbedRes = Maps.newHashMap();
    stubbedRes.put(key, new ListEntity());
    Mockito.doReturn(Task.value(stubbedRes)).when(_lssListDB).batchGetListEntities(keyList);

    Map<CompoundKey, UpdateResponse> res =
        await(_salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));
    assertEquals(res.size(), 1);
    assertEquals(res.get(compoundKey).getStatus(), HttpStatus.S_200_OK);
  }

  @Test(description = "Set RM fields value to null for a list entity with fields already set.")
  public void testRemoveAllRMFieldsInListEntity() throws URISyntaxException {
    reset(_lssListDB);
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, true);
    Urn urn = new Urn("urn:li:member:1");
    MemberUrn managerMemberUrn = MemberUrn.deserialize("urn:li:member:101");
    SeatUrn ownerSeatUrn = SeatUrn.deserialize("urn:li:seat:301");

    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_200_OK);

    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(1L);
    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(urn, testSalesListUrn);


    com.linkedin.saleslist.ListEntity existingListEntity = listEntityPair.getFirst();
    existingListEntity.setLeadManager(new LeadManager().setMemberUrn(managerMemberUrn).setEntityUrn(
        LeadManagerEntityUrn.createWithMember(managerMemberUrn)));
    existingListEntity.setLeadRole(new LeadRole().setRoleType(RoleType.CHAMPION));
    existingListEntity.setLeadRelationshipStrength(
        new LeadRelationshipStrength().setRelationshipStrength(RelationshipStrengthType.STRONG));
    existingListEntity.setLeadOwner(new LeadOwner().setSeatUrn(ownerSeatUrn));

    com.linkedin.saleslist.ListEntity listEntityUpdate = new com.linkedin.saleslist.ListEntity()
        .setLeadManager(new LeadManager())
        .setLeadRole(new LeadRole())
        .setLeadRelationshipStrength(
            new LeadRelationshipStrength())
        .setLeadOwner(new LeadOwner())
        .setPositionInLevel(4);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    Map<CompoundKey, com.linkedin.saleslist.ListEntity> entityMap = Maps.newHashMap();
    entityMap.put(createcCompoundKey(1L, new MemberUrn(1L)), existingListEntity);
    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> batchGetListEntityResult = new BatchResult<>(entityMap, Collections.emptyMap());
    doReturn(Task.value(batchGetListEntityResult)).when(salesListEntityService).batchGetListEntities(anyLong(), any());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntityUpdate);

    await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));

    verify(_lssListDB).upsertListEntities(any(), argThat(argument -> {
      //Prelim conditions.
      if (argument.size() != 1 || argument.get(urn).getLeadManager() == null || argument.get(urn).getLeadOwner() == null
          || argument.get(urn).getLeadRole() == null || argument.get(urn).getLeadRelationshipStrength() == null) {
        return false;
      }
      return checkRMFields(argument.get(urn), true);
    }), anyBoolean());
  }

  @Test(description = "Fetch entity that already has RM fields set. Check for expected returned value.")
  public void testGetAllRMFields() throws URISyntaxException {

    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, true);
    long listId = 1L;
    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(listId);
    Urn entityUrn = new Urn("urn:li:member:1");
    String managerMemberUrnStr = "urn:li:member:101";
    MemberUrn managerMemberUrn = MemberUrn.deserialize(managerMemberUrnStr);
    String ownerSeatUrnStr = "urn:li:seat:301";
    SeatUrn ownerSeatUrn = SeatUrn.deserialize(ownerSeatUrnStr);
    String lastModifiedSeatUrnStr = "urn:li:seat:50";
    long lastModifiedTime = 10023L;
    Integer positionInLevel = 4;
    Urn lastModifiedSeatUrn = new Urn(lastModifiedSeatUrnStr);
    AuditStamp auditStamp = new AuditStamp().setActor(lastModifiedSeatUrn).setTime(lastModifiedTime);
    Set<CompoundKey> getEntitySet = new HashSet<>();

    CompoundKey entityKey = createcCompoundKey(listId, entityUrn);
    getEntitySet.add(entityKey);

    Map<Pair<SalesListUrn, Urn>, ListEntity> entityMap = Maps.newHashMap();
    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(entityUrn, testSalesListUrn);
    ListEntity espressoEntity = listEntityPair.getSecond();

    com.linkedin.sales.espresso.LeadRole leadRole = new com.linkedin.sales.espresso.LeadRole();
    leadRole.setRoleType(com.linkedin.sales.espresso.RoleType.DECISION_MAKER);
    leadRole.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadRole.setLastModifiedTime(lastModifiedTime);
    LeadRole expectedLeadRole =
        new LeadRole().setRoleType(RoleType.DECISION_MAKER).setCreated(auditStamp).setLastModified(auditStamp);

    com.linkedin.sales.espresso.LeadManager leadManager = new com.linkedin.sales.espresso.LeadManager();
    leadManager.setMemberUrn(managerMemberUrnStr);
    leadManager.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadManager.setLastModifiedTime(lastModifiedTime);
    LeadManager expectedLeadManager = new LeadManager().setMemberUrn(managerMemberUrn)
        .setEntityUrn(LeadManagerEntityUrn.createWithMember(managerMemberUrn))
        .setCreated(auditStamp)
        .setLastModified(auditStamp);

    com.linkedin.sales.espresso.LeadOwner leadOwner = new com.linkedin.sales.espresso.LeadOwner();
    leadOwner.setSeatUrn(ownerSeatUrnStr);
    leadOwner.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadOwner.setLastModifiedTime(lastModifiedTime);
    LeadOwner expectedLeadOwner =
        new LeadOwner().setSeatUrn(ownerSeatUrn).setCreated(auditStamp).setLastModified(auditStamp);

    com.linkedin.sales.espresso.LeadRelationshipStrength leadRelationshipStrength =
        new com.linkedin.sales.espresso.LeadRelationshipStrength();
    leadRelationshipStrength.setRelationshipStrengthType(com.linkedin.sales.espresso.RelationshipStrengthType.STRONG);
    leadRelationshipStrength.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadRelationshipStrength.setLastModifiedTime(lastModifiedTime);
    LeadRelationshipStrength expectedLeadRelationshipStrength =
        new LeadRelationshipStrength().setRelationshipStrength(RelationshipStrengthType.STRONG)
            .setCreated(auditStamp)
            .setLastModified(auditStamp);

    com.linkedin.sales.espresso.LeadText leadText = new com.linkedin.sales.espresso.LeadText();
    leadText.setTextBody("Sample");
    leadText.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadText.setLastModifiedTime(lastModifiedTime);
    LeadText expectedLeadText = new LeadText().setTextBody("Sample").setCreated(auditStamp).setLastModified(auditStamp);


    espressoEntity.setLeadRole(leadRole);
    espressoEntity.setLeadManager(leadManager);
    espressoEntity.setLeadOwner(leadOwner);
    espressoEntity.setLeadRelationshipStrength(leadRelationshipStrength);
    espressoEntity.setPositionInLevel(positionInLevel);
    espressoEntity.setLeadText(leadText);

    entityMap.put(Pair.make(testSalesListUrn, entityUrn), espressoEntity);

    Mockito.doReturn(Task.value(entityMap))
        .when(_lssListDB)
        .batchGetListEntities(any());

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> entityMapRes =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), getEntitySet));
    assertEquals(entityMapRes.size(), 1);
    com.linkedin.saleslist.ListEntity returnedEntity = entityMapRes.get(entityKey);
    assertEquals(returnedEntity.getLeadRole(), expectedLeadRole);
    assertEquals(returnedEntity.getLeadManager(), expectedLeadManager);
    assertEquals(returnedEntity.getLeadOwner(), expectedLeadOwner);
    assertEquals(returnedEntity.getLeadRelationshipStrength(), expectedLeadRelationshipStrength);
    assertEquals(returnedEntity.getPositionInLevel(), positionInLevel);
    assertEquals(returnedEntity.getLeadText(), expectedLeadText);
  }

  @Test(description = "Get placeholder entity and test out espresso to restli conversion for placeholder as a manager.")
  public void testGetPlaceholderEntityWithPlaceholderManager() throws URISyntaxException {
    reset(_lssListDB);
    mockPermissionAndTypeAndSource(ListType.ACCOUNT_MAP, ListSource.MANUAL, AccessDecision.ALLOWED, null, true);

    SalesListUrn salesListUrn = UrnUtils.createSalesListUrn(1L);
    SalesListEntityPlaceholderUrn salesListEntityPlaceholderUrn = new SalesListEntityPlaceholderUrn(salesListUrn, 1000L);
    CompoundKey compoundKey = createcCompoundKey(1L, salesListEntityPlaceholderUrn);
    String lastModifiedSeatUrnStr = "urn:li:seat:50";
    long lastModifiedTime = 10023L;
    Urn lastModifiedSeatUrn = new Urn(lastModifiedSeatUrnStr);
    AuditStamp auditStamp = new AuditStamp().setActor(lastModifiedSeatUrn).setTime(lastModifiedTime);
    Map<Pair<SalesListUrn, Urn>, ListEntity> entityMap = Maps.newHashMap();

    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(salesListEntityPlaceholderUrn, salesListUrn);
    ListEntity espressoEntity = listEntityPair.getSecond();

    com.linkedin.sales.espresso.LeadManager leadManager = new com.linkedin.sales.espresso.LeadManager();
    leadManager.setSalesListEntityPlaceholderUrn(salesListEntityPlaceholderUrn.toString());
    leadManager.setLastModifiedBySeatUrn(lastModifiedSeatUrnStr);
    leadManager.setLastModifiedTime(lastModifiedTime);
    LeadManager expectedLeadManager = new LeadManager()
        .setEntityUrn(LeadManagerEntityUrn.createWithSalesListEntityPlaceholder(salesListEntityPlaceholderUrn))
        .setCreated(auditStamp)
        .setLastModified(auditStamp);
    espressoEntity.setLeadManager(leadManager);
    Set<CompoundKey> getEntitySet = new HashSet<>();
    getEntitySet.add(compoundKey);
    Mockito.doReturn(Task.value(entityMap))
        .when(_lssListDB)
        .batchGetListEntities(any());

    entityMap.put(Pair.make(salesListUrn, salesListEntityPlaceholderUrn), espressoEntity);

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> entityMapRes =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), getEntitySet));
    assertEquals(entityMapRes.get(compoundKey).getLeadManager(), expectedLeadManager);
  }


  private boolean checkRMFields(ListEntity listEntity, boolean isRemoveOperation) {
    com.linkedin.sales.espresso.LeadManager leadManager = listEntity.getLeadManager();
    if ((isRemoveOperation ^ leadManager.getMemberUrn() == null) || leadManager.getLastModifiedTime() == 0L
        || leadManager.getLastModifiedBySeatUrn() == null) {
      return false;
    }

    com.linkedin.sales.espresso.LeadOwner leadOwner = listEntity.getLeadOwner();
    if ((isRemoveOperation ^ leadOwner.getSeatUrn() == null) || leadOwner.getLastModifiedTime() == 0L
        || leadOwner.getLastModifiedBySeatUrn() == null) {
      return false;
    }

    com.linkedin.sales.espresso.LeadRole leadRole = listEntity.getLeadRole();
    if (( isRemoveOperation ^ leadRole.getRoleType() == null) || leadRole.getLastModifiedTime() == 0L
        || leadRole.getLastModifiedBySeatUrn() == null) {
      return false;
    }

    if (listEntity.getPositionInLevel() == null) {
      return false;
    }

    com.linkedin.sales.espresso.LeadRelationshipStrength leadRelationshipStrength =
        listEntity.getLeadRelationshipStrength();
    return (isRemoveOperation == (leadRelationshipStrength.getRelationshipStrengthType() == null))
        && leadRelationshipStrength.getLastModifiedTime() != 0L
        && leadRelationshipStrength.getLastModifiedBySeatUrn() != null;
  }

  @Test
  public void testCreateListEntityWithNoSearchIndexPrioritySetForManualListType() throws URISyntaxException {
    reset(_lssListDB);
    mockPermissionAndTypeAndSource(ListType.LEAD, ListSource.MANUAL, AccessDecision.ALLOWED);

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    SalesListUrn testSalesListUrn = UrnUtils.createSalesListUrn(1L);
    Pair<com.linkedin.saleslist.ListEntity, ListEntity> listEntityPair =
        createSalesAndEspressoListEntity(urn, testSalesListUrn);
    ListEntity espressoListEntity = listEntityPair.getSecond();
    Map<Urn, ListEntity> testReturnMap = Maps.newHashMap();
    testReturnMap.put(urn, espressoListEntity);


    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(testSalesListUrn, testReturnMap, false);
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntityPair.getFirst()), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);
  }
  @Test
  public void testCreateEntityFailureWithNoCorrectListType() throws URISyntaxException {
    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());
    Mockito.doReturn(Task.value(Maps.newHashMap())).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(),
        HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testCreateEntityFailureWithNoPermission() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.DENIED);

    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());
    Mockito.doReturn(Task.value(Maps.newHashMap())).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(),
        HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testCreateListEntityDuplicate() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_200_OK);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_409_CONFLICT);
  }

  @Test
  public void testCreateListEntityExceedLimit() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(1000L)).when(_lssListDB).getListEntityCount(anyLong());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(),
        HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testCreateListEntityLimitExtended() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(250L)).when(_lssListDB).getListEntityCount(anyLong());


    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateListEntityExceedExtendedLimit() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(250L)).when(_lssListDB).getListEntityCount(anyLong());

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(1000L)).when(_lssListDB).getListEntityCount(anyLong());

    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(),
        HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testCreateListEntityWithListNotFound() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getListCsvImportsBySalesList(anyLong());
    Mockito.doReturn(Task.value(250L)).when(_lssListDB).getListEntityCount(anyLong());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    Map<CompoundKey, CreateResponse> result = await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testCreateListEntityWithTier() throws URISyntaxException {
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Urn urn = new Urn("urn:li:member:1");
    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    respMap.put(urn, HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis())
        .setTier(AccountMapTier.TIER_1);
    Map<CompoundKey, CreateResponse> result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);

    listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis())
        .setTier(AccountMapTier.TIER_1)
        .setPositionInTier(0);
    result =
        await(_salesListEntityService.batchCreateListEntities(Collections.singletonList(listEntity), CONTRACT_URN));
    assertEquals(result.entrySet().stream().findFirst().get().getValue().getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testBatchCreateListEntities() throws URISyntaxException {
    prepareMocks();

    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    com.linkedin.saleslist.ListEntity listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(2L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);

    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));

    assertEquals(resultMap.size(), 2);

    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());

    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_409_CONFLICT);
  }

  @Test(description = "Test creating entities for a list that doesn't exist yet, because its being created as part of the ListCsvImport workflow")
  public void testBatchCreateListEntitiesForListCsvImport() throws URISyntaxException {
    Urn urn = new Urn("urn:li:organization:1");

    Map<Urn, HttpStatus> respMap1 = Maps.newHashMap();
    respMap1.put(urn, HttpStatus.S_201_CREATED);

    Mockito.doReturn(Task.value(respMap1)).when(_lssListDB).upsertListEntities(eq(UrnUtils.createSalesListUrn(1L)), any(), anyBoolean());

    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());

    com.linkedin.sales.espresso.ListToListCsvImportView listCsvImport = new com.linkedin.sales.espresso.ListToListCsvImportView();
    listCsvImport.creatorSeatUrn = SEAT_URN.toString();

    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.value(listCsvImport)).when(_lssListDB).getListCsvImportsBySalesList(anyLong());

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(OrganizationUrn.createFromUrn(urn))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity);

    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));

    assertEquals(resultMap.size(), 1);

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity.getEntity());

    assertEquals(resultMap.get(compoundKey).getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test(description = "Test creating entities for a list that doesn't exist yet, because its being created as part of "
      + "the ListCsvImport workflow, but the ListCsvImport isn't found")
  public void testBatchCreateListEntitiesForListCsvImportNotFound() throws URISyntaxException {
    Urn urn = new Urn("urn:li:organization:1");

    Mockito.doReturn(Task.value(1L)).when(_lssListDB).getListEntityCount(anyLong());
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getListCsvImportsBySalesList(anyLong());

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity = new com.linkedin.saleslist.ListEntity();
    listEntity.setEntity(OrganizationUrn.createFromUrn(urn))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity);

    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));

    assertEquals(resultMap.size(), 1);

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity.getEntity());

    assertEquals(resultMap.get(compoundKey).getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testBatchCreateListEntitiesExceedUpperLimitation() throws URISyntaxException {
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(999L)).when(_lssListDB).getListEntityCount(anyLong());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    com.linkedin.saleslist.ListEntity listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testBatchCreateListEntitiesLimitExtended() throws URISyntaxException {
    prepareMocks();
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(250L)).when(_lssListDB).getListEntityCount(anyLong());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    com.linkedin.saleslist.ListEntity listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(2L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_409_CONFLICT);
  }

  @Test
  public void testBatchCreateListEntitiesExceedExtendedUpperLimitation() throws URISyntaxException {
    prepareMocks();
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(249L)).when(_lssListDB).getListEntityCount(anyLong());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    com.linkedin.saleslist.ListEntity listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(2L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_201_CREATED);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_409_CONFLICT);

    Mockito.doReturn(Task.value(999L)).when(_lssListDB).getListEntityCount(anyLong());

    listEntities = new ArrayList<>();

    listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

     listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);

    resultMap = await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));
    assertEquals(resultMap.size(), 2);
    compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testBatchCreateListEntitiesCreatorNotIdentical() throws URISyntaxException {
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());
    Mockito.doReturn(Task.value(249L)).when(_lssListDB).getListEntityCount(anyLong());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    com.linkedin.saleslist.ListEntity listEntity2 = new com.linkedin.saleslist.ListEntity();
    listEntity2.setEntity(new MemberUrn(2L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(ANOTHER_SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    listEntities.add(listEntity1);
    listEntities.add(listEntity2);
    Map<CompoundKey, CreateResponse> resultMap =
        await(_salesListEntityService.batchCreateListEntities(listEntities, CONTRACT_URN));
    assertEquals(resultMap.size(), 2);
    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity1.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity1.getEntity());
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listEntity2.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, listEntity2.getEntity());
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_400_BAD_REQUEST);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void batchCreateListEntitiesUpdatesListModificationTime() throws URISyntaxException {
    CreateResponse createResponse = new CreateResponse(HttpStatus.S_201_CREATED);
    CompoundKey compoundKey =
        new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, UrnUtils.createSalesListUrn(1L))
            .append(ServiceConstants.ENTITY_COMPOUND_KEY, new MemberUrn(2L));
    LssListDB lssListDB = mock(LssListDB.class);
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    List<com.linkedin.saleslist.ListEntity> listEntities = new ArrayList<>();

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());
    listEntities.add(listEntity1);

    doReturn(Task.value(true)).when(lssListDB).updateList(anyLong(), any());
    Map<Urn, HttpStatus> respMap1 = Maps.newHashMap();
    Urn urn1 = new Urn("urn:li:member:1");

    respMap1.put(urn1, HttpStatus.S_201_CREATED);
    Mockito.doReturn(Task.value(respMap1)).when(lssListDB).upsertListEntities(any(), any(), anyBoolean());
    await(salesListEntityService.batchCreateListEntities(123L, listEntities, CONTRACT_URN, 1234L, Maps.newHashMap(), false));

    verify(lssListDB, times((1))).updateList(anyLong(), any());
  }

  @Test
  public void testDeleteListEntity() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(anyLong(), any(), eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    UpdateResponse result = await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteListEntityWithDBFailure() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Mockito.doReturn(Task.failure(new RuntimeException())).when(_lssListDB).deleteListEntity(anyLong(), any(), eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    try {
      await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RuntimeException.class);
    }
  }

  @Test
  public void testDeleteListEntityWithListNotFound() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);

    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getListCsvImportsBySalesList(anyLong());

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    try {
      await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RuntimeException.class);
    }
  }

  @Test(description = "Test deleting entities for a list that doesn't exist yet, because its being created as part of the ListCsvImport workflow")
  public void testDeleteListEntityForListCsvImport() {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);

    com.linkedin.sales.espresso.ListToListCsvImportView listCsvImport = new com.linkedin.sales.espresso.ListToListCsvImportView();
    listCsvImport.creatorSeatUrn = SEAT_URN.toString();

    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.value(listCsvImport)).when(_lssListDB).getListCsvImportsBySalesList(anyLong());

    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(anyLong(), any(), eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    UpdateResponse result = await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteEntityFailureWithNoPermission() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);
    mockPermissionAndType(ListType.LEAD, AccessDecision.DENIED);

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    UpdateResponse result = await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    assertEquals(result.getStatus(), HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testDeleteEntityFailureWithWrongEntityType() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new CompanyUrn(100);
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    try {
      await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RestLiServiceException.class);
    }
  }

  @Test
  public void testBatchDeleteListEntities() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn1 = new MemberUrn(100L);
    Urn entityUrn2 = new MemberUrn(200L);

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(eq(1L), eq(entityUrn1), eq(false));
    Mockito.doReturn(Task.value(false)).when(_lssListDB).deleteListEntity(eq(1L), eq(entityUrn2),eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn1);
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn2);

    Set<CompoundKey> compoundKeySet = new HashSet<>();
    compoundKeySet.add(compoundKey1);
    compoundKeySet.add(compoundKey2);

    Map<CompoundKey, UpdateResponse> resultMap =
        await(_salesListEntityService.batchDeleteListEntities(compoundKeySet, SEAT_URN, false));
    assertEquals(resultMap.size(), 2);
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testBatchDeleteListEntitiesWhenListNotFound() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn1 = new MemberUrn(100L);
    Urn entityUrn2 = new MemberUrn(200L);

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(eq(1L), eq(entityUrn1), eq(true));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(eq(1L), eq(entityUrn2), eq(true));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getListCsvImportsBySalesList(anyLong());

    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn1);
    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn2);

    Set<CompoundKey> compoundKeySet = new HashSet<>();
    compoundKeySet.add(compoundKey1);
    compoundKeySet.add(compoundKey2);

    Map<CompoundKey, UpdateResponse> resultMap =
        await(_salesListEntityService.batchDeleteListEntities(compoundKeySet, SEAT_URN, true));

    assertEquals(resultMap.size(), 2);
    assertEquals(resultMap.get(compoundKey1).getStatus(), HttpStatus.S_204_NO_CONTENT);
    assertEquals(resultMap.get(compoundKey2).getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteListEntityNotFound() throws URISyntaxException {
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(1L);
    Urn entityUrn = new MemberUrn(100L);

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    Mockito.doReturn(Task.value(false)).when(_lssListDB).deleteListEntity(eq(1L), any(), eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).deleteListEntity(eq(2L), any(), eq(false));
    Mockito.doReturn(Task.value(true)).when(_lssListDB).updateList(anyLong(), any());

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, listUrn)
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, entityUrn);
    UpdateResponse result = await(_salesListEntityService.deleteListEntity(compoundKey, SEAT_URN));
    assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testGetListEntities() {
    Urn testUrn = new MemberUrn(1L);
    ListEntity listEntity = new ListEntity();
    listEntity.sortOrder = 1;
    listEntity.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity.createdTime = 1;
    listEntity.contractId = CONTRACT_URN.getContractIdEntity();
    Pair<Urn, ListEntity> listEntityPair = new Pair<>(testUrn, listEntity);
    Pair<Integer, List> pairListEntities = new Pair<>(10, Collections.singletonList(listEntityPair));
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair =
        await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));

    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getEntity(), testUrn);
  }

  @Test
  public void testGetListEntitiesSortByLastUpdatedTime() {
    Urn testUrn1 = new MemberUrn(1L);
    ListEntity listEntity1 = new ListEntity();
    listEntity1.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity1.createdTime = 1000;
    listEntity1.lastModifiedTime = 7000L;
    listEntity1.sortOrder = listEntity1.lastModifiedTime;
    listEntity1.contractId = CONTRACT_URN.getContractIdEntity();
    Pair<Urn, ListEntity> listEntityPair1 = new Pair<>(testUrn1, listEntity1);

    Urn testUrn2 = new MemberUrn(2L);
    ListEntity listEntity2 = new ListEntity();
    listEntity2.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity2.createdTime = 3000;
    listEntity2.lastModifiedTime = 8000L;
    listEntity2.sortOrder = listEntity2.lastModifiedTime;
    listEntity2.contractId = CONTRACT_URN.getContractIdEntity();
    Pair<Urn, ListEntity> listEntityPair2 = new Pair<>(testUrn2, listEntity2);

    Urn testUrn3 = new MemberUrn(3L);
    ListEntity listEntity3 = new ListEntity();
    listEntity3.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity3.createdTime = 2000;
    listEntity3.lastModifiedTime = 9000L;
    listEntity3.sortOrder = listEntity3.lastModifiedTime;
    listEntity3.contractId = CONTRACT_URN.getContractIdEntity();
    Pair<Urn, ListEntity> listEntityPair3 = new Pair<>(testUrn3, listEntity3);

    List listPairUrnListEntity = new ArrayList<Pair<Urn, ListEntity>>();
    listPairUrnListEntity.add(listEntityPair1);
    listPairUrnListEntity.add(listEntityPair2);
    listPairUrnListEntity.add(listEntityPair3);

    Pair<Integer, List> pairListEntities = new Pair<>(10, listPairUrnListEntity);
    // DB will return the sorted list entities by 'sortOrder' field with input sort order.
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    // test get listEntities with sorting by last updated time with ascending order
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair = await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.ASCENDING));
    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 3);
    assertEquals(resultList.get(0).getEntity(), testUrn1); // lastModifiedTime is 7000
    assertEquals(resultList.get(1).getEntity(), testUrn2); // lastModifiedTime is 8000
    assertEquals(resultList.get(2).getEntity(), testUrn3); // lastModifiedTime is 9000
  }

  @Test
  public void testGetListEntitiesWithTier() {
    Urn testUrn = new MemberUrn(1L);
    ListEntity listEntity = new ListEntity();
    listEntity.sortOrder = 1;
    listEntity.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity.createdTime = 1;
    listEntity.contractId = CONTRACT_URN.getContractIdEntity();
    listEntity.tier = 1;
    Pair<Urn, ListEntity> listEntityPair = new Pair<>(testUrn, listEntity);
    Pair<Integer, List> pairListEntities = new Pair<>(10, Collections.singletonList(listEntityPair));
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair =
        await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));

    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getEntity(), testUrn);
    assertEquals(resultList.get(0).getTier(), AccountMapTier.TIER_1);
  }

  @Test
  public void testGetListEntitiesWithValidTierPosition() {
    Urn testUrn = new MemberUrn(1L);
    ListEntity listEntity = new ListEntity();
    listEntity.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity.createdTime = 1;
    listEntity.contractId = CONTRACT_URN.getContractIdEntity();
    listEntity.tier = 1;
    listEntity.sortOrder = 0;
    Pair<Urn, ListEntity> listEntityPair = new Pair<>(testUrn, listEntity);
    Pair<Integer, List> pairListEntities = new Pair<>(10, Collections.singletonList(listEntityPair));
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair =
        await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));

    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getEntity(), testUrn);
    assertEquals(resultList.get(0).getTier(), AccountMapTier.TIER_1);
    assertEquals(resultList.get(0).getPositionInTier(), Integer.valueOf(0));
  }

  @Test
  public void testGetListEntitiesWithInvalidTierPosition() {
    Urn testUrn = new MemberUrn(1L);
    ListEntity listEntity = new ListEntity();
    listEntity.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity.createdTime = 1;
    listEntity.contractId = CONTRACT_URN.getContractIdEntity();
    listEntity.tier = 1;
    listEntity.sortOrder = 12345;
    Pair<Urn, ListEntity> listEntityPair = new Pair<>(testUrn, listEntity);
    Pair<Integer, List> pairListEntities = new Pair<>(10, Collections.singletonList(listEntityPair));
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair =
        await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));

    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getEntity(), testUrn);
    assertEquals(resultList.get(0).getTier(), AccountMapTier.TIER_1);
    assertEquals(resultList.get(0).getPositionInTier(), Integer.valueOf(MAX_TIER_POSITION));
  }

  @Test
  public void testGetListEntitiesListNotFound() {
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());

    try {
      await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RestLiServiceException.class);
    }
  }

  @Test
  public void testGetListEntitiesListWithFailure() {
    Mockito.doReturn(Task.failure(new RuntimeException("failure"))).when(_lssListDB).getList(anyLong());

    try {
      await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RestLiServiceException.class);
    }
  }

  @Test
  public void testGetListEntitiesFailWithUnsupportedViewerType() throws URISyntaxException {
    RestLiServiceException exception = runAndWaitException(
        _salesListEntityService.getListEntities(CONTRACT_URN, 1L, 0, 10, SortOrder.DESCENDING), RestLiServiceException.class);
    assertEquals(exception.getStatus(),HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testBatchGetListEntitiesByUrns() throws URISyntaxException {
    Urn testUrn1 = new MemberUrn(1L);
    ListEntity listEntity1 = new ListEntity();
    listEntity1.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity1.createdTime = 1000;
    listEntity1.lastModifiedTime = 7000L;
    listEntity1.sortOrder = listEntity1.lastModifiedTime;
    listEntity1.contractId = CONTRACT_URN.getContractIdEntity();

    Urn testUrn2 = new MemberUrn(2L);
    ListEntity listEntity2 = new ListEntity();
    listEntity2.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity2.createdTime = 3000;
    listEntity2.lastModifiedTime = 8000L;
    listEntity2.sortOrder = listEntity2.lastModifiedTime;
    listEntity2.contractId = CONTRACT_URN.getContractIdEntity();

    Urn testUrn3 = new MemberUrn(3L);
    ListEntity listEntity3 = new ListEntity();
    listEntity3.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity3.createdTime = 2000;
    listEntity3.lastModifiedTime = 9000L;
    listEntity3.sortOrder = listEntity3.lastModifiedTime;
    listEntity3.contractId = CONTRACT_URN.getContractIdEntity();

    long listId = 1L;
    SalesListUrn salesListUrn = UrnUtils.createSalesListUrn(listId);
    CompoundKey compoundKey1 = createcCompoundKey(listId, testUrn1);
    CompoundKey compoundKey2 = createcCompoundKey(listId, testUrn2);
    CompoundKey compoundKey3 = createcCompoundKey(listId, testUrn3);

    Set<CompoundKey> ids = ImmutableSet.of(compoundKey1, compoundKey2, compoundKey3);

    Map<Pair<SalesListUrn, Urn>, ListEntity> entityMap =
        ImmutableMap.of(new Pair<>(salesListUrn, testUrn1), listEntity1, new Pair<>(salesListUrn, testUrn2), listEntity2,
            new Pair<>(salesListUrn, testUrn3), listEntity3);

    List<Pair<SalesListUrn, Urn>> keyList =
        ImmutableList.of(new Pair<>(salesListUrn, testUrn1), new Pair<>(salesListUrn, testUrn2),
            new Pair<>(salesListUrn, testUrn3));

    Mockito.doReturn(Task.value(entityMap))
        .when(_lssListDB)
        .batchGetListEntities(keyList);
    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> resultMap =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), ids));
    assertEquals(resultMap.size(), 3);
    for (CompoundKey key : ids) {
      Assert.assertTrue(resultMap.containsKey(key));
    }
  }

  @Test
  public void testBatchGetListEntitiesByUrnsNoPermissionOnOneList() throws URISyntaxException {
    Urn testUrn1 = new MemberUrn(1L);
    ListEntity listEntity1 = new ListEntity();
    listEntity1.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity1.createdTime = 1000;
    listEntity1.lastModifiedTime = 7000L;
    listEntity1.sortOrder = listEntity1.lastModifiedTime;
    listEntity1.contractId = CONTRACT_URN.getContractIdEntity();

    Urn testUrn2 = new MemberUrn(2L);

    long listId1 = 1L;
    long listId2 = 2L;
    SalesListUrn salesListUrn1 = UrnUtils.createSalesListUrn(listId1);
    CompoundKey compoundKey1 = createcCompoundKey(listId1, testUrn1);
    CompoundKey compoundKey2 = createcCompoundKey(listId2, testUrn2);

    Set<CompoundKey> ids = ImmutableSet.of(compoundKey1, compoundKey2);

    Map<Pair<SalesListUrn, Urn>, ListEntity> entityMap = Collections.singletonMap(new Pair<>(salesListUrn1, testUrn1), listEntity1);
    List<Pair<SalesListUrn, Urn>> keyList = Collections.singletonList(new Pair<>(salesListUrn1, testUrn1));

    Mockito.doReturn(Task.value(entityMap))
        .when(_lssListDB)
        .batchGetListEntities(keyList);

    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.ALLOWED, listId1);
    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.DENIED, listId2);

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> resultMap =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), ids));

    assertEquals(resultMap.size(), 1);
    assertEquals(resultMap.getErrors().size(), 1);
    assertTrue(resultMap.containsKey(compoundKey1));
    assertEquals(resultMap.getErrors().get(compoundKey2).getStatus(), HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testBatchGetListEntitiesByUrnsWithEmptyKeys() throws URISyntaxException {
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> resultMap =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), Collections.emptySet()));

    Assert.assertTrue(resultMap.isEmpty());
  }

  @Test
  public void testBatchGetListEntitiesByUrnsWithListNotFound() throws URISyntaxException {
    Mockito.doReturn(Task.failure(new EntityNotFoundException(1L, "not found"))).when(_lssListDB).getList(anyLong());

    CompoundKey ck = createcCompoundKey(1L, new MemberUrn(1L));
    Set<CompoundKey> ids = ImmutableSet.of(ck);

    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> res =
        await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), ids));

    assertTrue(res.isEmpty());
    assertEquals(res.getErrors().get(ck).getStatus(), HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testBatchGetListEntitiesByUrnsWithDBFailure() throws URISyntaxException {
    Mockito.doReturn(Task.failure(new RuntimeException("failure"))).when(_lssListDB).getList(anyLong());

    Set<CompoundKey> ids = ImmutableSet.of(createcCompoundKey(1L, new MemberUrn(1L)));

    try {
      await(_salesListEntityService.batchGetListEntities(SEAT_URN.getSeatIdEntity(), ids));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RestLiServiceException.class);
    }
  }

  @Test
  public void testGetListEntityUrnsByListIds() {
    Urn testUrn1 = new MemberUrn(1L);
    Urn testUrn2 = new MemberUrn(3L);
    List<Urn> listOfEntityUrns = new ArrayList<>();
    listOfEntityUrns.add(testUrn1);
    listOfEntityUrns.add(testUrn2);

    long[] lists = {1L, 3L};
    Mockito.doReturn(Task.value(listOfEntityUrns))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());
    List<Urn> results =
        await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));

    try {
      await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RestLiServiceException.class);
    }

    assertEquals(results.size(), 2);
    assertEquals(results.get(0), testUrn1);
    assertEquals(results.get(1), testUrn2);
  }

  @Test
  public void testGetListEntityUrnsByListIdsNoPermissionOnOneList() {
    long listId1 = 1L;
    long listId2 = 3L;
    Urn testUrn1 = new MemberUrn(1L);
    List<Urn> listOfEntityUrns = Collections.singletonList(testUrn1);

    long[] lists = {listId1, listId2};
    Mockito.doReturn(Task.value(listOfEntityUrns))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(eq(Collections.singleton(listId1)), anyInt(), anyInt());

    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.ALLOWED, listId1);
    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.DENIED, listId2);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());

    List<Urn> results =
        await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));

    assertEquals(results.size(), 1);
    assertEquals(results.get(0), testUrn1);
  }

  @Test
  public void testGetListEntityUrnsByListIdsNotFound() {
    List<Urn> listOfEntityUrns = new ArrayList<>();
    long[] lists = {1L, 3L};

    Mockito.doReturn(Task.value(listOfEntityUrns))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());
    List<Urn> results =
        await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));

    assertEquals(results.size(), 0);
  }

  @Test
  public void testGetListEntityUrnsByListIdsNoPermissionForAllLists() {
    List<Urn> listOfEntityUrns = new ArrayList<>();
    long[] lists = {1L, 3L};

    Mockito.doReturn(Task.value(listOfEntityUrns))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    mockPermissionAndType(ListType.LEAD, AccessDecision.DENIED);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());
    List<Urn> results =
        runAndWait(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));

    assertTrue(results.isEmpty());
  }

  @Test
  public void testGetListEntityUrnsByListIdsNoPermissionForOneList() {
    Urn testUrn1 = new MemberUrn(1L);
    List<Urn> listOfEntityUrns = new ArrayList<>();
    listOfEntityUrns.add(testUrn1);

    long[] lists = {1L, 3L};
    Mockito.doReturn(Task.value(listOfEntityUrns))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(eq(Collections.singleton(1L)), anyInt(), anyInt());

    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.ALLOWED, 1L);
    mockPermissionAndTypeAndSource(ListType.LEAD, null, AccessDecision.DENIED, 3L);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());
    List<Urn> results =
        await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));

    assertEquals(results.size(), 1);
    assertEquals(results.get(0), testUrn1);
  }

  @Test
  public void testGetListEntityUrnsByListIdsHitsException() {
    long[] lists = {1L, 3L};

    Mockito.doReturn(Task.failure(new RuntimeException("failure")))
        .when(_lssListDB)
        .getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Set<Long> listIds = Arrays.stream(lists).boxed().collect(Collectors.toSet());
    try {
      await(_salesListEntityService.getListEntityUrnsByListIds(SEAT_URN.getSeatIdEntity(), listIds, 0, 10));
    } catch (Exception e) {
      assertEquals(e.getCause().getClass(), RuntimeException.class);
    }
  }

  @Test
  public void batchUpdateListEntitiesReturnForbiddenWhenAttemptingToModifyUnOwnedOrUnsharedLists()
      throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntity1);

    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.DENIED);

    Map<CompoundKey, UpdateResponse> res = await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));

    res.forEach((k, v) -> assertEquals(v.getStatus(), HttpStatus.S_403_FORBIDDEN));
  }

  private Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> getCompoundKeyPatchRequestMap(
      com.linkedin.saleslist.ListEntity listEntity1, CompoundKey compoundKey) throws URISyntaxException {
    DataMap patchData = new DataMap();
    patchData.put("$set", listEntity1.data());
    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = Maps.newHashMap();
    map.put(compoundKey, PatchRequest.createFromPatchDocument(patchData));
    return map;
  }

  private Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> getCompoundKeyPatchRequestMap(
      com.linkedin.saleslist.ListEntity listEntity1) throws URISyntaxException {
    return getCompoundKeyPatchRequestMap(listEntity1, createcCompoundKey(1L, new MemberUrn(1L)));
  }

  @Test
  public void batchUpdateListEntitiesReturnForbiddenWhenListEntityTypeMisMatch() throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setCreatedAt(System.currentTimeMillis());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntity1);

    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.ALLOWED);

    Map<CompoundKey, com.linkedin.saleslist.ListEntity> entityMap = Maps.newHashMap();
    entityMap.put(createcCompoundKey(1L, new MemberUrn(1L)), listEntity1);
    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> batchGetListEntityResult = new BatchResult<>(entityMap, Collections.emptyMap());
    doReturn(Task.value(batchGetListEntityResult)).when(salesListEntityService).batchGetListEntities(anyLong(), any());

    Map<CompoundKey, UpdateResponse> res =
        await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));
    res.forEach((key, value) -> assertEquals(HttpStatus.S_403_FORBIDDEN, value.getStatus()));
  }

  @Test
  public void batchUpdateListEntitiesReturnForbiddenWhenModifierSeatIdMisMatch() throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    ListEntityPriorityInfo priorityInfo = new ListEntityPriorityInfo();
    AuditStamp auditStamp = new AuditStamp();
    auditStamp.setActor(new SeatUrn(999L));
    priorityInfo.setLastModified(auditStamp);

    listEntity1.setEntity(new MemberUrn(1L))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setPriorityInfo(priorityInfo)
        .setCreatedAt(System.currentTimeMillis());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntity1);

    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.ALLOWED);

    Map<CompoundKey, com.linkedin.saleslist.ListEntity> entityMap = Maps.newHashMap();
    entityMap.put(createcCompoundKey(1L, new MemberUrn(1L)), listEntity1);
    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> batchGetListEntityResult = new BatchResult<>(entityMap, Collections.emptyMap());
    doReturn(Task.value(batchGetListEntityResult)).when(salesListEntityService).batchGetListEntities(anyLong(), any());

    Map<CompoundKey, UpdateResponse> res =
        await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));

    res.forEach((key, value) -> assertEquals(HttpStatus.S_403_FORBIDDEN, value.getStatus()));
  }

  @Test
  public void batchUpdateReturnsSuccessWhenEspressoReturnsSuccessAfterUpdation() throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    Urn urn1 = new Urn("urn:li:organization:1");

    respMap.put(urn1, HttpStatus.S_200_OK);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    ListEntityPriorityInfo priorityInfo = new ListEntityPriorityInfo();
    priorityInfo.setPriority(ListEntityPriorityType.HIGH);

    listEntity1.setEntity(OrganizationUrn.deserialize("urn:li:organization:1"))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setPriorityInfo(priorityInfo)
        .setCreatedAt(System.currentTimeMillis());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntity1);

    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.ALLOWED);

    Map<CompoundKey, com.linkedin.saleslist.ListEntity> entityMap = Maps.newHashMap();
    entityMap.put(createcCompoundKey(1L, new MemberUrn(1L)), listEntity1);
    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> batchGetListEntityResult = new BatchResult<>(entityMap, Collections.emptyMap());
    doReturn(Task.value(batchGetListEntityResult)).when(salesListEntityService).batchGetListEntities(anyLong(), any());

    Map<CompoundKey, UpdateResponse> res =
        await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));

    res.forEach((key, value) -> assertEquals(value.getStatus(), HttpStatus.S_200_OK));
  }

  @Test
  public void batchUpdateReturnsInternalServerErrorWhenEspressoReturnsFailureAfterUpdation() throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));

    Map<Urn, HttpStatus> respMap = Maps.newHashMap();
    Urn urn1 = new Urn("urn:li:organization:1");

    respMap.put(urn1, HttpStatus.S_500_INTERNAL_SERVER_ERROR);

    Mockito.doReturn(Task.value(respMap)).when(_lssListDB).upsertListEntities(any(), any(), anyBoolean());

    com.linkedin.saleslist.ListEntity listEntity1 = new com.linkedin.saleslist.ListEntity();
    ListEntityPriorityInfo priorityInfo = new ListEntityPriorityInfo();
    AuditStamp auditStamp = new AuditStamp();
    auditStamp.setActor(SEAT_URN);
    auditStamp.setTime(System.currentTimeMillis());

    priorityInfo.setLastModified(auditStamp);
    priorityInfo.setPriority(ListEntityPriorityType.HIGH);

    listEntity1.setEntity(OrganizationUrn.deserialize("urn:li:organization:1"))
        .setList(UrnUtils.createSalesListUrn(1L))
        .setCreator(SEAT_URN)
        .setPriorityInfo(priorityInfo)
        .setCreatedAt(System.currentTimeMillis());

    Map<CompoundKey, PatchRequest<com.linkedin.saleslist.ListEntity>> map = getCompoundKeyPatchRequestMap(listEntity1);

    mockPermissionAndType(ListType.ACCOUNT, AccessDecision.ALLOWED);

    Map<CompoundKey, com.linkedin.saleslist.ListEntity> entityMap = Maps.newHashMap();
    entityMap.put(createcCompoundKey(1L, new MemberUrn(1L)), listEntity1);
    BatchResult<CompoundKey, com.linkedin.saleslist.ListEntity> batchGetListEntityResult = new BatchResult<>(entityMap, Collections.emptyMap());
    doReturn(Task.value(batchGetListEntityResult)).when(salesListEntityService).batchGetListEntities(anyLong(), any());

    Map<CompoundKey, UpdateResponse> res =
        await(salesListEntityService.batchUpdateListEntities(SEAT_URN, CONTRACT_URN, map));

    res.forEach((key, value) -> assertEquals(HttpStatus.S_500_INTERNAL_SERVER_ERROR, value.getStatus()));
  }

  @Test
  public void batchGetListEntitiesWhenLastModifiedTimeIsBeforeTimeCreated() throws URISyntaxException {
    SalesListEntityService salesListEntityService =
        spy(new SalesListEntityService(_lssListDB, _aclServiceDispatcher, _lixService, _salesListEntityPlaceholderUrnService));
    Urn testUrn = new MemberUrn(1L);
    ListEntity listEntity = new ListEntity();
    listEntity.sortOrder = 2L;
    listEntity.ownerSeatId = SEAT_URN.getSeatIdEntity();
    listEntity.createdTime = 2L;
    listEntity.lastModifiedTime = 1L;
    listEntity.contractId = CONTRACT_URN.getContractIdEntity();
    Pair<Urn, ListEntity> listEntityPair = new Pair<>(testUrn, listEntity);
    Pair<Integer, List> pairListEntities = new Pair<>(10, Collections.singletonList(listEntityPair));
    Mockito.doReturn(Task.value(pairListEntities))
        .when(_lssListDB)
        .getListEntities(anyLong(), anyInt(), anyInt(), any());

    mockPermissionAndType(ListType.LEAD, AccessDecision.ALLOWED);
    Pair<Integer, List<com.linkedin.saleslist.ListEntity>> resultPair =
        await(_salesListEntityService.getListEntities(SEAT_URN, 1L, 0, 10, SortOrder.DESCENDING));

    assertEquals(resultPair.getFirst().intValue(), 10);
    List<com.linkedin.saleslist.ListEntity> resultList = resultPair.getSecond();
    assertEquals(resultList.size(), 1);
    assertEquals(resultList.get(0).getEntity(), testUrn);
    assertEquals(resultList.get(0).getLastModifiedAt(), 2L);
  }

  private Pair<com.linkedin.saleslist.ListEntity, ListEntity> createSalesAndEspressoListEntity(Urn entityUrn, SalesListUrn salesListUrn){
    long listCreatedTime = System.currentTimeMillis();

    ListEntity espressoListEntity = new ListEntity();
    espressoListEntity.setCreatedTime(listCreatedTime);
    espressoListEntity.setOwnerSeatId(SEAT_URN.getSeatIdEntity());
    espressoListEntity.setContractId(CONTRACT_URN.getContractIdEntity());
    espressoListEntity.setSortOrder(listCreatedTime);
    espressoListEntity.setLastModifiedTime(listCreatedTime);

    com.linkedin.saleslist.ListEntity salesListEntity = new com.linkedin.saleslist.ListEntity();
    salesListEntity.setEntity(entityUrn)
        .setList(salesListUrn)
        .setCreator(SEAT_URN)
        .setCreatedAt(listCreatedTime);
    return Pair.make(salesListEntity, espressoListEntity);
  }

  private void mockPermissionAndType(ListType listType, AccessDecision accessDecision) {
    mockPermissionAndTypeAndSource(listType, null, accessDecision);
  }

  private void mockPermissionAndTypeAndSource(ListType listType, ListSource listSource, AccessDecision accessDecision) {
    mockPermissionAndTypeAndSource(listType, listSource, accessDecision, null);
  }

  private void mockPermissionAndTypeAndSource(ListType listType, ListSource listSource, AccessDecision accessDecision, Long listId) {
    mockPermissionAndTypeAndSource(listType, listSource, accessDecision, listId, false);
  }

  private void mockPermissionAndTypeAndSource(ListType listType, ListSource listSource, AccessDecision accessDecision, Long listId, Boolean isRMLixEnabled) {
    com.linkedin.sales.espresso.List list = new com.linkedin.sales.espresso.List();
    list.contractId = CONTRACT_URN.getContractIdEntity();
    list.createdTime = System.currentTimeMillis();
    list.creatorSeatId = SEAT_URN.getSeatIdEntity();
    list.lastModifiedTime = System.currentTimeMillis();
    list.listType = listType;
    list.name = "test list";
    if(Objects.nonNull(listSource)){
      list.listSource = listSource;
    }

    if (listType.equals(ListType.ACCOUNT_MAP)) {
      Mockito.doReturn(Task.value(ImmutableMap.of(LixUtils.LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_LIST_VIEW,  isRMLixEnabled,
              LixUtils.LSS_PAGES_ACCOUNT_PAGE_RELATIONSHIP_MAP_MAP_VIEW, isRMLixEnabled)))
          .when(_lixService)
          .batchGetIsEEPLixEnabledForSeat(any(), anySet());
    }

    Mockito.doReturn(Task.value(list)).when(_lssListDB).getList(listId == null ? anyLong() : eq(listId));
    Mockito.doReturn(Task.value(accessDecision))
        .when(_aclServiceDispatcher)
        .checkAccessDecision(any(), any(), listId == null ? any() : eq(UrnUtils.createSalesListUrn(listId)), any());
    Mockito.doReturn(Task.value(accessDecision))
        .when(_aclServiceDispatcher)
        .checkAccessDecision(any(), any(), listId == null ? any() : eq(UrnUtils.createSalesListUrn(listId)), any(), any());
  }

  private void prepareMocks() throws URISyntaxException {
    Urn urn1 = new Urn("urn:li:member:1");
    Urn urn2 = new Urn("urn:li:member:2");

    Map<Urn, HttpStatus> respMap1 = Maps.newHashMap();
    respMap1.put(urn1, HttpStatus.S_201_CREATED);

    Map<Urn, HttpStatus> respMap2 = Maps.newHashMap();
    respMap2.put(urn2, HttpStatus.S_200_OK);

    Mockito.doReturn(Task.value(respMap1)).when(_lssListDB).upsertListEntities(eq(UrnUtils.createSalesListUrn(1L)), any(), anyBoolean());
    Mockito.doReturn(Task.value(respMap2)).when(_lssListDB).upsertListEntities(eq(UrnUtils.createSalesListUrn(2L)), any(), anyBoolean());
  }

  private CompoundKey createcCompoundKey(long listId, Urn memberUrn) throws URISyntaxException {
    return new CompoundKey().append(ServiceConstants.LIST_COMPOUND_KEY, UrnUtils.createSalesListUrn(listId))
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, memberUrn);
  }
}
