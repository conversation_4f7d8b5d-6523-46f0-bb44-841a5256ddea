package com.linkedin.sales.service.list;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Sets;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.workflow.constants.Constants;
import com.linkedin.parseq.Task;
import com.linkedin.sales.admin.SalesEntitlement;
import com.linkedin.sales.admin.SalesEntitlementArray;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.saleslist.List;
import com.linkedin.saleslist.ListEntitySummary;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListSource;
import com.linkedin.saleslist.ListType;
import com.linkedin.talent.decorator.PathSpecSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * Created by hacao at 6/25/18
 * This is the class to test {@link SalesListEntitySummaryService}
 */
public class SalesListEntitySummaryServiceTest extends ServiceUnitTest {
  private static final ContractUrn CONTRACT_URN = new ContractUrn(100L);
  private static final ContractUrn CONTRACT_URN_1 = new ContractUrn(200L);
  private static final SeatUrn SEAT_URN = new SeatUrn(2000L);
  private static final PathSpecSet LIST_PATH_SPEC =
      PathSpecSet.of(com.linkedin.saleslist.List.fields().id(), com.linkedin.saleslist.List.fields().creatorContract());

  private SalesListEntitySummaryService _salesListEntitySummaryService;

  @Mock
  private LssListDB _lssListDB;

  @Mock
  private SalesListService _salesListService;

  @Mock
  private SalesSeatClient _salesSeatClient;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.openMocks(this);
    mock(LixService.class);
    mock(SalesListService.class);
    _salesListEntitySummaryService = new SalesListEntitySummaryService(_lssListDB, _salesListService, _salesSeatClient);
  }

  @Test
  public void testBatchGetEntityCounts() {
    Urn entityUrn1 = new MemberUrn(1L);
    Urn entityUrn2 = new MemberUrn(2L);
    Urn entityUrn3 = new MemberUrn(3L);
    Urn entityUrn4 = new MemberUrn(4L);
    List list1 = new List();
    List list2 = new List();
    List list3 = new List();
    list1.setId(100L).setCreatorContract(CONTRACT_URN);
    list2.setId(101L).setCreatorContract(CONTRACT_URN_1);
    list3.setId(200L).setCreatorContract(CONTRACT_URN);;
    java.util.List<List> totalList = new ArrayList<>();
    totalList.add(list1);
    totalList.add(list2);
    Pair<Integer, java.util.List<List>> listPair = new Pair<>(totalList.size(), totalList);
    java.util.List<Long> listIdsForEntity1 = new ArrayList<>();
    java.util.List<Long> listIdsForEntity2 = new ArrayList<>();
    listIdsForEntity1.add(list1.getId());
    listIdsForEntity2.add(list2.getId());
    listIdsForEntity2.add(list3.getId());

    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), eq(CONTRACT_URN), any(), any())).thenReturn(Task.value(new SalesSeat().setEntitlements(
        new SalesEntitlementArray(SalesEntitlement.CRM_BASICS))));
    when(_salesListService.getListsForSeat(SEAT_URN.getSeatIdEntity(), ListType.LEAD, ListOrdering.LAST_MODIFIED,
        SortOrder.DESCENDING, null, Constants.VISIBLE_LIST_SOURCES, null, ServiceConstants.DEFAULT_START,
        ServiceConstants.GET_ALL_COUNT, LIST_PATH_SPEC)).thenReturn(Task.value(listPair));
    when(_lssListDB.getListIdsForEntity(entityUrn1, CONTRACT_URN.getContractIdEntity(), null)).thenReturn(
        Task.value(listIdsForEntity1));
    when(_lssListDB.getListIdsForEntity(entityUrn1, CONTRACT_URN_1.getContractIdEntity(), null)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "")));
    when(_lssListDB.getListIdsForEntity(entityUrn2, CONTRACT_URN_1.getContractIdEntity(), null)).thenReturn(
        Task.value(listIdsForEntity2));
    when(_lssListDB.getListIdsForEntity(entityUrn2, CONTRACT_URN.getContractIdEntity(), null)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "")));
    when(_lssListDB.getListIdsForEntity(entityUrn3, CONTRACT_URN.getContractIdEntity(), null)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "")));
    when(_lssListDB.getListIdsForEntity(entityUrn3, CONTRACT_URN_1.getContractIdEntity(), null)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "")));
    when(_lssListDB.getListIdsForEntity(entityUrn4, CONTRACT_URN.getContractIdEntity(), null)).thenReturn(
        Task.failure(new RuntimeException("get listIds fail")));
    when(_lssListDB.getListIdsForEntity(entityUrn4, CONTRACT_URN_1.getContractIdEntity(), null)).thenReturn(
        Task.failure(new RuntimeException("get listIds fail")));
    Map<Urn, ListEntitySummary> entityCounts = await(
        _salesListEntitySummaryService.batchGetEntityCounts(ImmutableSet.of(entityUrn1, entityUrn2, entityUrn3, entityUrn4),
            CONTRACT_URN, SEAT_URN));
    Assert.assertEquals(entityCounts.size(), 4);
    Assert.assertTrue(entityCounts.containsKey(entityUrn1));
    Assert.assertEquals(entityCounts.get(entityUrn1).getListCount().intValue(), 1);
    Assert.assertTrue(entityCounts.containsKey(entityUrn2));
    Assert.assertEquals(entityCounts.get(entityUrn2).getListCount().intValue(), 1);
    Assert.assertTrue(entityCounts.containsKey(entityUrn3));
    Assert.assertEquals(entityCounts.get(entityUrn3).getListCount().intValue(), 0);
    Assert.assertTrue(entityCounts.containsKey(entityUrn4));
    Assert.assertEquals(entityCounts.get(entityUrn3).getListCount().intValue(), 0);
  }

  @Test
  public void testBatchGetEntityCountsWithEmptyEntityUrns() {
    Map<Urn, ListEntitySummary> entityCountsWithEmptyEntityUrns =
        await(_salesListEntitySummaryService.batchGetEntityCounts(Collections.emptySet(), CONTRACT_URN, SEAT_URN));
    Assert.assertEquals(entityCountsWithEmptyEntityUrns.size(), 0);
  }

  @Test
  public void testBatchGetEntityCounts_NoCrmEntitlements() {
    Urn entityUrn1 = new MemberUrn(1L);
    List list1 = new List().setId(100L).setCreatorContract(CONTRACT_URN);;
    List list2 = new List().setId(150L).setCreatorContract(CONTRACT_URN);;
    List list3 = new List().setId(200L).setCreatorContract(CONTRACT_URN);;
    java.util.List<List> totalList = Arrays.asList(list1, list2, list3);
    Pair<Integer, java.util.List<List>> listPair = new Pair<>(totalList.size(), totalList);
    java.util.List<Long> listIdsForEntity1 = new ArrayList<>();
    listIdsForEntity1.add(list1.getId());
    listIdsForEntity1.add(list3.getId());

    when(_salesSeatClient.getSeat(eq(SEAT_URN.getSeatIdEntity()), eq(CONTRACT_URN), any(), any())).thenReturn(Task.value(new SalesSeat().setEntitlements(
        new SalesEntitlementArray())));
    Set<ListSource> sourcesExceptCrm = Sets.filter(Constants.VISIBLE_LIST_SOURCES, source ->
        ListSource.CRM_SYNC != source && ListSource.CRM_PERSON_ACCOUNT != source && ListSource.CRM_BLUEBIRD != source && ListSource.CRM_AT_RISK_OPPORTUNITY != source);
    when(_salesListService.getListsForSeat(SEAT_URN.getSeatIdEntity(), ListType.LEAD, ListOrdering.LAST_MODIFIED,
        SortOrder.DESCENDING, null, sourcesExceptCrm, null, ServiceConstants.DEFAULT_START,
        ServiceConstants.GET_ALL_COUNT, LIST_PATH_SPEC)).thenReturn(Task.value(listPair));
    when(_lssListDB.getListIdsForEntity(entityUrn1, CONTRACT_URN.getContractIdEntity(), null)).thenReturn(
        Task.value(listIdsForEntity1));
    Map<Urn, ListEntitySummary> entityCounts = await(
        _salesListEntitySummaryService.batchGetEntityCounts(Collections.singleton(entityUrn1),
            CONTRACT_URN, SEAT_URN));
    assertThat(entityCounts).hasSize(1);
    assertThat(entityCounts.get(entityUrn1).getListCount()).isEqualTo(2);
  }
}
