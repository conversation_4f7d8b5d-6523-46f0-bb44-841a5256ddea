package com.linkedin.sales.service.list;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CrmPairingUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.CrmPairing;
import com.linkedin.crm.CrmPairingDatasetStatus;
import com.linkedin.crm.CrmSource;
import com.linkedin.crm.SalesConnectedCrmSetting;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.crm.common.util.CrmUrnUtils;
import com.linkedin.data.template.GetMode;
import com.linkedin.data.template.LongArray;
import com.linkedin.data.template.SetMode;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.lss.salesleadaccount.clients.TrackingClient;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.integration.CrmPairingClient;
import com.linkedin.sales.client.integration.CrmSettingClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.List;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.SeatToListView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.LocalizationHelper;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.AccountToListMapping;
import com.linkedin.saleslist.ListOrdering;
import com.linkedin.saleslist.ListOwnership;
import com.linkedin.saleslist.ListSource;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.talent.decorator.PathSpecSet;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.Nullable;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for SalesListService
 * <AUTHOR>
 */
public class SalesListServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID_2 = 3000L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(SEAT_ID_2);
  private static final long MEMBER_ID = 111L;
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final long LIST_ID = 1L;
  private static final SalesListUrn LIST_URN = UrnUtils.createSalesListUrn(LIST_ID);
  private static final long LIST_ID2 = 2L;
  private static final SalesListUrn LIST_URN2 = UrnUtils.createSalesListUrn(LIST_ID2);
  private static final long LIST_ENTITY_COUNT = 2L;
  private static final String LIST_NAME = "test";
  private static final String LIST_DESCRIPTION = "test description";
  private static final long CREATED_TIME = 11111111L;
  private static final OrganizationUrn ORG_URN = UrnUtils.createOrganizationUrn(123L);
  private static final OrganizationUrn ORG_URN2 = UrnUtils.createOrganizationUrn(456L);
  private static final Set<com.linkedin.saleslist.ListType> DEFAULT_LIST_TYPES =
      Sets.newHashSet(com.linkedin.saleslist.ListType.LEAD,
          com.linkedin.saleslist.ListType.ACCOUNT);
  private static final Set<com.linkedin.saleslist.ListType> ALL_LIST_TYPES =
      Sets.newHashSet(
          Arrays.stream(com.linkedin.saleslist.ListType.values())
              .filter(type -> type != com.linkedin.saleslist.ListType.$UNKNOWN)
              .collect(Collectors.toList()));
  private static final Long CRM_PAIRING_ID = 2L;
  @Mock
  private LssListDB _lssListDB;

  @Mock
  private SalesListIdService _salesListIdService;

  @Mock
  private LssSharingDB _lssSharingDB;

  @Mock
  private LixService _lixService;

  @Mock
  private AclServiceDispatcher _aclServiceDispatcher;

  @Mock
  private CrmPairingClient _crmPairingClient;

  @Mock
  private LocalizationHelper _localizationHelper;

  @Mock
  private SalesSeatClient _salesSeatClient;

  @Mock
  private TrackingClient _trackingClient;

  @Mock
  private SalesAccountToListMappingService _salesAccountToListMappingService;
  @Mock
  private CrmSettingClient _crmSettingClient;

  private SalesListService _salesListService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesListService =
        new SalesListService(_lssListDB, _salesListIdService, _lssSharingDB, _lixService, _aclServiceDispatcher,
            _crmPairingClient, _localizationHelper, _salesSeatClient, _trackingClient, _salesAccountToListMappingService, _crmSettingClient);
    when(_lixService.isEEPLixEnabledForSeat(any(), any())).thenReturn(Task.value(Boolean.FALSE));
  }

  //------------- test for create list -------------//
  ////////////////////////////////////////////////////
  @Test
  public void testCreateListSucceed() {

    when(_salesListIdService.generateNextId()).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));
    com.linkedin.saleslist.List list = createMockList(LIST_NAME);

    Long listId = await(_salesListService.createList(list, CONTRACT_ID));
    assertThat(listId).isEqualTo(LIST_ID);
  }

  @Test
  public void testCreateListFailWhenGenerateId() {

    when(_salesListIdService.generateNextId()).thenReturn(Task.failure(new RuntimeException("generate Id fail")));
    com.linkedin.saleslist.List list = createMockList(LIST_NAME);

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.createList(list, CONTRACT_ID));
    }).withCause(new RuntimeException("generate Id fail"));
  }

  @Test
  public void testCreateListFailWhenCreateListInDB() {
    when(_salesListIdService.generateNextId()).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID), any()))
        .thenReturn(Task.failure(new RuntimeException("create list fail")));
    com.linkedin.saleslist.List list = createMockList(LIST_NAME);

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.createList(list, CONTRACT_ID));
    }).withCause(new RuntimeException("create list fail"));
  }

  //------------- test for batch create list -------------//
  //////////////////////////////////////////////////////////
  @Test
  public void testBatchCreateListsSucceed() {
    String listName1 = "test1";
    String listName2 = "test2";

    LongArray listIds = new LongArray(Arrays.asList(LIST_ID, LIST_ID2));
    when(_salesListIdService.generateNextIds(2)).thenReturn(Task.value(listIds));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID2), any())).thenReturn(Task.value(LIST_ID));
    com.linkedin.saleslist.List list1 = createMockList(listName1);
    com.linkedin.saleslist.List list2 = createMockList(listName2);

    BatchCreateResult<Long, com.linkedin.saleslist.List> batchCreateResult =
        await(_salesListService.batchCreateLists(Arrays.asList(list1, list2), CONTRACT_ID));
    assertThat(batchCreateResult.getResults().size()).isEqualTo(2);
    for(CreateResponse createResponse: batchCreateResult.getResults()) {
      assertThat(createResponse.getStatus()).isEqualByComparingTo(HttpStatus.S_201_CREATED);
    }
  }

  @Test
  public void testBatchCreateListsPartialFailure() {
    String listName1 = "test1";
    String listName2 = "test2";

    LongArray listIds = new LongArray(Arrays.asList(LIST_ID, LIST_ID2));
    when(_salesListIdService.generateNextIds(2)).thenReturn(Task.value(listIds));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID2), any())).thenReturn(Task.failure(new Throwable()));
    com.linkedin.saleslist.List list1 = createMockList(listName1);
    com.linkedin.saleslist.List list2 = createMockList(listName2);
    list1.setId(LIST_ID);
    list2.setId(LIST_ID2);
    BatchCreateResult<Long, com.linkedin.saleslist.List> batchCreateResult =
        await(_salesListService.batchCreateLists(Arrays.asList(list1, list2), CONTRACT_ID));
    assertThat(batchCreateResult.getResults().size()).isEqualTo(2);
    assertThat(batchCreateResult.getResults().get(0).getStatus()).isEqualByComparingTo(HttpStatus.S_201_CREATED);
    assertThat(batchCreateResult.getResults().get(1).getStatus()).isEqualByComparingTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }


  @Test
  public void testBatchCreateListsPartialFailureWithListSourceEnabled() {
    String listName1 = "test1";
    String listName2 = "test2";

    LongArray listIds = new LongArray(Arrays.asList(LIST_ID, LIST_ID2));
    when(_salesListIdService.generateNextIds(2)).thenReturn(Task.value(listIds));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID2), any())).thenReturn(Task.failure(new Throwable()));
    com.linkedin.saleslist.List list1 = createMockList(listName1);
    com.linkedin.saleslist.List list2 = createMockList(listName2);
    list1.setId(LIST_ID);
    list2.setId(LIST_ID2);
    BatchCreateResult<Long, com.linkedin.saleslist.List> batchCreateResult =
        await(_salesListService.batchCreateLists(Arrays.asList(list1, list2), CONTRACT_ID));
    assertThat(batchCreateResult.getResults().size()).isEqualTo(2);
    assertThat(batchCreateResult.getResults().get(0).getStatus()).isEqualByComparingTo(HttpStatus.S_201_CREATED);
    assertThat(batchCreateResult.getResults().get(1).getStatus()).isEqualByComparingTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
  }

  @Test
  public void testBatchCreateListsTorrentFailure() {
    String listName1 = "test1";
    String listName2 = "test2";
    when(_salesListIdService.generateNextIds(2)).thenReturn(Task.failure(new Throwable("error")));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));
    when(_lssListDB.createList(eq(LIST_ID2), any())).thenReturn(Task.value(LIST_ID));
    com.linkedin.saleslist.List list1 = createMockList(listName1);
    com.linkedin.saleslist.List list2 = createMockList(listName2);

    try {
      await(_salesListService.batchCreateLists(Arrays.asList(list1, list2), CONTRACT_ID));
    } catch (Exception e) {
      assertThat(e.getCause()).isInstanceOf(RestLiServiceException.class);
    }
  }

  @Test
  public void testBatchCreateListsWithEmptyRequest() {
    BatchCreateResult<Long, com.linkedin.saleslist.List> result =
        await(_salesListService.batchCreateLists(Collections.emptyList(), CONTRACT_ID));
    assertThat(result.getResults().size()).isEqualTo(0);
  }

  //------------- test for delete list -------------//
  ////////////////////////////////////////////////////
  @Test
  public void testDeleteListSucceedWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.ACCOUNT_MAP;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.ACCOUNT_MAP;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.value(true));
    when(_lssListDB.deleteAllListEntities(LIST_ID)).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(any())).thenReturn(Task.value(true));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));
    when(_lssListDB.deleteAllRelationshipMapChangeLogsForListId(LIST_ID)).thenReturn(Task.value(true));

    when(_lssSharingDB.getPoliciesByResource(listUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(
        PaginatedList.createForPage(Collections.singletonList(new Pair<>(new SeatUrn(SEAT_ID), ShareRole.OWNER)), 0, 1,
            1)));

    Boolean isSuccessful = await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    verify(_lssListDB).deleteAllRelationshipMapChangeLogsForListId(LIST_ID);
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteListFailNoPermissionWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.value(true));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.DENIED));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    }).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
        String.format("seat:%d does not have permission to delete list:%d", SEAT_ID, LIST_ID)));
  }

  @Test
  public void testDeleteListFailWhenGetListInDBException() {

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.failure(new RuntimeException("get list fail")));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    }).withCause(new RuntimeException("get list fail"));
  }

  @Test
  public void testDeleteListFailWhenGetListInDBNotFound() {
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.failure(new EntityNotFoundException(null, "")));
    Boolean isSuccessful = await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testDeleteListFailWhenDeleteListEntityInDBExceptionWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.value(true));
    when(_lssListDB.deleteAllListEntities(LIST_ID)).thenReturn(Task.failure(new RuntimeException("delete list entities fail")));
    when(_lssSharingDB.deleteSubjectPolicy(any())).thenReturn(Task.value(true));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getPoliciesByResource(listUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(
        PaginatedList.createForPage(Collections.singletonList(new Pair<>(new SeatUrn(SEAT_ID), ShareRole.OWNER)), 0, 1,
            1)));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    }).withCause(new RuntimeException("delete list entities fail"));
  }

  @Test
  public void testDeleteListFailWhenGetListEntityTotalCountInDBExceptionWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.value(true));
    when(_lssListDB.deleteAllListEntities(LIST_ID)).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(any())).thenReturn(Task.value(true));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.failure(new RuntimeException("get list entities count fail")));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getPoliciesByResource(listUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(
        PaginatedList.createForPage(Collections.singletonList(new Pair<>(new SeatUrn(SEAT_ID), ShareRole.OWNER)), 0, 1,
            1)));

    SalesSeat seat = new SalesSeat();
    seat.setMember(new MemberUrn(1L));

    doReturn(Task.value(seat)).when(_salesSeatClient).getSeat(any(), any(), any(EnterpriseApplicationUsageUrn.class), any());
    doReturn(Task.value(Lists.newArrayList())).when(_lssListDB).
        getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    Boolean res = await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    assertThat(res).isTrue();
  }

  @Test
  public void testDeleteListFailWhenDeleteListInDBExceptionWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.failure(new RuntimeException("delete list fail")));
    when(_lssListDB.deleteAllListEntities(LIST_ID)).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(any())).thenReturn(Task.value(true));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getPoliciesByResource(listUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(
        PaginatedList.createForPage(Collections.singletonList(new Pair<>(new SeatUrn(SEAT_ID), ShareRole.OWNER)), 0, 1,
            1)));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    }).withCause(new RuntimeException("delete list fail"));
  }

  @Test
  public void testDeleteListFailWhenDeleteListInDBNotFoundWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    List list = new List();
    list.listType = listType;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.ADMIN;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);
    when(_lssSharingDB.deleteSubjectPolicy(any())).thenReturn(Task.value(true));

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(list));
    when(_lssListDB.deleteList(LIST_ID)).thenReturn(Task.value(false));
    when(_lssListDB.deleteAllListEntities(LIST_ID)).thenReturn(Task.value(true));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getPoliciesByResource(listUrn, policyType, null, 0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(
        PaginatedList.createForPage(Collections.singletonList(new Pair<>(new SeatUrn(SEAT_ID), ShareRole.OWNER)), 0, 1,
            1)));

    Boolean isSuccessful = await(_salesListService.deleteList(LIST_ID, SEAT_URN, CONTRACT_URN));
    assertThat(isSuccessful).isFalse();
  }

  //-------------- test for get list ---------------//
  ////////////////////////////////////////////////////

  @Test
  public void testGetListAsMemberSucceedWithSharingEnabled() throws URISyntaxException {
    PolicyType policyType = PolicyType.ACCOUNT_MAP;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = createEspressoList(LIST_NAME);
    espressoList.listType = ListType.ACCOUNT_MAP;

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.setRole(ShareRole.READER);
    subjectPolicy.setCreatorSeatUrn(SEAT_URN_2.toString());

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(2));
    when(_aclServiceDispatcher.checkAccessDecision(MEMBER_URN, policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getSubjectPolicy(MEMBER_URN, policyType.name(), LIST_URN)).thenReturn(
        Task.value(subjectPolicy));

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, MEMBER_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(LIST_NAME);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.ACCOUNT_MAP);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(list.getCreatedTime()).isEqualTo(CREATED_TIME);
    assertThat(list.getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(list.isShared()).isTrue();
    assertThat(list.getCollaboratorCount()).isEqualTo(1);
    assertThat(list.isSubscribed(GetMode.NULL)).isNull();
    assertThat(list.isAccepted()).isFalse();
    assertThat(list.getSharedBy()).isEqualTo(SEAT_URN_2);
  }

  @Test
  public void testGetListSucceedWithSharingEnabled() throws URISyntaxException {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = createEspressoList(LIST_NAME);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(2));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(LIST_NAME);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(list.getCreatedTime()).isEqualTo(CREATED_TIME);
    assertThat(list.getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(list.isShared()).isTrue();
    assertThat(list.getCollaboratorCount()).isEqualTo(1);
    assertThat(list.isSubscribed(GetMode.NULL)).isNull();
  }

  @Test
  public void testGetListWithSubscribeEnabled(){
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = createEspressoList(LIST_NAME);
    espressoList.setListSource(com.linkedin.sales.espresso.ListSource.CRM_SYNC);
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(2));
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(new SeatUrn(SEAT_ID), policyType.name(), listUrn)).thenReturn(Task.value(subscribePolicy));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(LIST_NAME);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(list.getCreatedTime()).isEqualTo(CREATED_TIME);
    assertThat(list.getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(list.isShared()).isTrue();
    assertThat(list.isSubscribed(GetMode.NULL)).isTrue();
  }

  @Test
  public void testGetListWithSubscribedNotPopulatedByUser(){
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = createEspressoList(LIST_NAME);
    espressoList.setListSource(com.linkedin.sales.espresso.ListSource.CRM_SYNC);
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(2));
    //Subject policy exist but its not set by user, so null is returned.
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    when(_lssSharingDB.getSubjectPolicy(new SeatUrn(SEAT_ID), policyType.name(), listUrn)).thenReturn(Task.value(subscribePolicy));
    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(LIST_NAME);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(list.getCreatedTime()).isEqualTo(CREATED_TIME);
    assertThat(list.getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(list.isShared()).isTrue();
    assertThat(list.isSubscribed(GetMode.NULL)).isNull();
  }

  @Test
  public void testGetListWithCrmListEnabled() throws URISyntaxException {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List crmList = new List();
    crmList.name = "CRM_LEAD_456";
    crmList.listType = ListType.LEAD;
    crmList.creatorSeatId = SEAT_ID;
    crmList.listSource = com.linkedin.sales.espresso.ListSource.CRM_SYNC;

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(crmList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(2));
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(new SeatUrn(SEAT_ID), policyType.name(), listUrn)).thenReturn(Task.value(subscribePolicy));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Leads and Contacts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Lead and Contacts");

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo("CRM_LEAD_456");
    assertThat(list.getLocalizedName()).isEqualTo("My CRM Leads and Contacts");
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(!list.hasDescription());
    assertThat(list.getLocalizedDescription()).isEqualTo("My CRM Lead and Contacts");
    assertThat(list.isShared()).isTrue();
    assertThat(list.isSubscribed()).isTrue();
  }

  @Test
  public void testGetListWithBuyerInterestListEnabled() {
    PolicyType policyType = PolicyType.ACCOUNT_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List buyerInterestList = new List();
    buyerInterestList.name = "BUYER_INTEREST_456";
    buyerInterestList.listType = ListType.ACCOUNT;
    buyerInterestList.creatorSeatId = SEAT_ID;
    buyerInterestList.listSource = com.linkedin.sales.espresso.ListSource.BUYER_INTEREST;

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(buyerInterestList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.BUYER_INTEREST), any(), any())).thenReturn("Top Priority Accounts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.BUYER_INTEREST), any(), any())).thenReturn("Generated by Sales Navigator");

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo("BUYER_INTEREST_456");
    assertThat(list.getLocalizedName()).isEqualTo("Top Priority Accounts");
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.ACCOUNT);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(!list.hasDescription());
    assertThat(list.getLocalizedDescription()).isEqualTo("Generated by Sales Navigator");
    assertThat(list.isShared()).isFalse();
  }

  @Test
  public void testGetListWithRecommendationListEnabled() {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List leadRecommendationList = new List();
    leadRecommendationList.setListSource(com.linkedin.sales.espresso.ListSource.RECOMMENDATION);
    leadRecommendationList.setName("RECOMMENDED_LIST_WEEKLY");
    leadRecommendationList.setListType(ListType.LEAD);
    leadRecommendationList.setCreatorSeatId(SEAT_ID);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(leadRecommendationList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.RECOMMENDATION), eq(com.linkedin.saleslist.ListType.LEAD),
        any())).thenReturn("Recommended Leads Weekly");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.RECOMMENDATION),
        eq(com.linkedin.saleslist.ListType.LEAD), any())).thenReturn(
        "List of recommended leads based on your sales preferences, search history, and past lead interactions to help you identify new prospects");

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo("RECOMMENDED_LIST_WEEKLY");
    assertThat(list.getLocalizedName()).isEqualTo("Recommended Leads Weekly");
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(!list.hasDescription());
    assertThat(list.getLocalizedDescription()).isEqualTo(
        "List of recommended leads based on your sales preferences, search history, and past lead interactions to help you identify new prospects");
    assertThat(list.isShared()).isFalse();
  }

  @Test
  public void testGetListFailWhenGetListInDBException() {
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.failure(new RuntimeException("get list fail")));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    }).withCause(new RuntimeException("get list fail"));
  }

  @Test
  public void testGetListFailWithUnsupportedViewerType() throws URISyntaxException {
    RestLiServiceException exception = runAndWaitException(
        _salesListService.getList(LIST_ID, CONTRACT_URN, null), RestLiServiceException.class);
    assertThat(exception.getStatus()).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testGetListFailWhenNoPermissionWithSharingEnabled() throws URISyntaxException {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = createEspressoList(LIST_NAME);

    when(_lssListDB.getList(eq(LIST_ID))).thenReturn(Task.value(espressoList));
    when(_lssListDB.getListEntityCount(eq(LIST_ID))).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.DENIED));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    }).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN,
        String.format("viewer:%s does not have permission to view list:%d", SEAT_URN, LIST_ID)));
  }

  private List createEspressoList(String name) {
    List espressoList = new List();
    espressoList.name = name;
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = ListType.LEAD;
    espressoList.description = LIST_DESCRIPTION;
    espressoList.createdTime = CREATED_TIME;
    espressoList.contractId = CONTRACT_ID;
    return espressoList;
  }

  //-------------- test for batch get lists ---------------//
  ///////////////////////////////////////////////////////////

  @Test
  public void testBatchGetListsSucceedWithSharingEnabled() throws URISyntaxException {
    String listName2 = "test2";
    long listId2 = 2L;
    long listEntityCount2 = 10L;

    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn1 = UrnUtils.createSalesListUrn(LIST_ID);
    SalesListUrn listUrn2 = UrnUtils.createSalesListUrn(listId2);

    List espressoList1 = createEspressoList(LIST_NAME);
    List espressoList2 = createEspressoList(listName2);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList1));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));

    when(_lssListDB.getList(listId2)).thenReturn(Task.value(espressoList2));
    when(_lssListDB.getListEntityCount(listId2)).thenReturn(Task.value(listEntityCount2));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn1, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn2, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));
    Map<Long, com.linkedin.saleslist.List> listMap =
        await(_salesListService.batchGetLists(Sets.newHashSet(LIST_ID, listId2), SEAT_ID, null));
    assertThat(listMap.size()).isEqualTo(2);
    assertThat(listMap.get(LIST_ID).getId()).isEqualTo(LIST_ID);
    assertThat(listMap.get(LIST_ID).getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(listMap.get(LIST_ID).getName()).isEqualTo(LIST_NAME);
    assertThat(listMap.get(LIST_ID).getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(listMap.get(LIST_ID).getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(listMap.get(LIST_ID).getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(listMap.get(LIST_ID).isShared()).isEqualTo(true);
    assertThat(listMap.get(listId2).getId()).isEqualTo(listId2);
    assertThat(listMap.get(LIST_ID).getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(listMap.get(listId2).getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(listMap.get(listId2).getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(listMap.get(listId2).getName()).isEqualTo(listName2);
    assertThat(listMap.get(listId2).getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(listMap.get(listId2).getEntityCount().longValue()).isEqualTo(listEntityCount2);
    assertThat(listMap.get(listId2).isShared()).isEqualTo(true);
  }

  @Test
  public void testBatchGetListsPartialFailureListWithSharingEnabled() throws URISyntaxException {
    String listName2 = "test2";
    long listId2 = 2L;
    long listEntityCount2 = 10L;

    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn1 = UrnUtils.createSalesListUrn(LIST_ID);
    SalesListUrn listUrn2 = UrnUtils.createSalesListUrn(listId2);

    List espressoList1 = createEspressoList(LIST_NAME);
    List espressoList2 = createEspressoList(listName2);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList1));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));

    when(_lssListDB.getList(listId2)).thenReturn(Task.value(espressoList2));
    when(_lssListDB.getListEntityCount(listId2)).thenReturn(Task.value(listEntityCount2));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn1, accessAction))
            .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn2, accessAction))
            .thenReturn(Task.value(AccessDecision.DENIED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));
    Map<Long, com.linkedin.saleslist.List> listMap =
            await(_salesListService.batchGetLists(Sets.newHashSet(LIST_ID, listId2), SEAT_ID, null));
    assertThat(listMap.size()).isEqualTo(1);
    assertThat(listMap.get(LIST_ID).getId()).isEqualTo(LIST_ID);
    assertThat(listMap.get(LIST_ID).getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(listMap.get(LIST_ID).getName()).isEqualTo(LIST_NAME);
    assertThat(listMap.get(LIST_ID).getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(listMap.get(LIST_ID).getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(listMap.get(LIST_ID).getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(listMap.get(LIST_ID).isShared()).isEqualTo(true);
  }

  @Test
  public void testBatchGetListWithSharingDenied() throws URISyntaxException {
    SalesListUrn listUrn1 = UrnUtils.createSalesListUrn(LIST_ID);
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    List espressoList1 = createEspressoList(LIST_NAME);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList1));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn1, accessAction))
        .thenReturn(Task.value(AccessDecision.DENIED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    Map<Long, com.linkedin.saleslist.List> listMap =
        await(_salesListService.batchGetLists(Sets.newHashSet(LIST_ID), SEAT_ID, null));
    assertThat(listMap.size()).isEqualTo(0);
  }

  @Test
  public void testBatchGetListsSucceedWithPartialSubscribeEnabled() throws URISyntaxException {
    String listName2 = "test2";
    long listId2 = 2L;
    long listEntityCount2 = 10L;

    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn1 = UrnUtils.createSalesListUrn(LIST_ID);
    SalesListUrn listUrn2 = UrnUtils.createSalesListUrn(listId2);

    List espressoList1 = createEspressoList(LIST_NAME);
    List espressoList2 = createEspressoList(listName2);

    espressoList1.setListSource(com.linkedin.sales.espresso.ListSource.CRM_SYNC);
    espressoList2.setListSource(com.linkedin.sales.espresso.ListSource.MANUAL);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList1));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));

    when(_lssListDB.getList(listId2)).thenReturn(Task.value(espressoList2));
    when(_lssListDB.getListEntityCount(listId2)).thenReturn(Task.value(listEntityCount2));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn1, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn2, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(), any(), any())).thenReturn(Task.value(subscribePolicy));
    Map<Long, com.linkedin.saleslist.List> listMap =
        await(_salesListService.batchGetLists(Sets.newHashSet(LIST_ID, listId2), SEAT_ID, null));
    assertThat(listMap.size()).isEqualTo(2);
    assertThat(listMap.get(LIST_ID).getId()).isEqualTo(LIST_ID);
    assertThat(listMap.get(LIST_ID).getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(listMap.get(LIST_ID).getName()).isEqualTo(LIST_NAME);
    assertThat(listMap.get(LIST_ID).getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(listMap.get(LIST_ID).getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(listMap.get(LIST_ID).getDescription()).isEqualToIgnoringCase(LIST_DESCRIPTION);
    assertThat(listMap.get(LIST_ID).isShared()).isEqualTo(false);
    assertThat(listMap.get(LIST_ID).isSubscribed()).isEqualTo(true);
    assertThat(listMap.get(listId2).getId()).isEqualTo(listId2);
    assertThat(listMap.get(LIST_ID).getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(listMap.get(listId2).getCreatorContract()).isEqualTo(CONTRACT_URN);
    assertThat(listMap.get(listId2).getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(listMap.get(listId2).getName()).isEqualTo(listName2);
    assertThat(listMap.get(listId2).getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(listMap.get(listId2).getEntityCount().longValue()).isEqualTo(listEntityCount2);
    assertThat(listMap.get(listId2).isShared()).isEqualTo(false);
    assertThat(listMap.get(listId2).isSubscribed(GetMode.NULL)).isNull();
  }
  //-------------- test for get lists for seat ---------------//
  ////////////////////////////////////////////////////////////

  @Test
  public void testGetListForSeatAllListTypeSanityCheckEmptyResult() {
    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPairs = createSeatToListView(0);

    when(_lssListDB.getListsForSeatToListView(anyLong(), any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(new Pair<>(0, ownedCrmSeatListPairs)));
    when(_lssSharingDB.getPoliciesBySubject(any(), anyString(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(new ArrayList<>(), 0, 0, 0)));
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(anySet(), any())).thenReturn(Task.value(Collections.emptyMap()));

    for (com.linkedin.saleslist.ListType type : ALL_LIST_TYPES) {
      Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = runAndWait(
          _salesListService.getListsForSeat(SEAT_ID, type, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING,
              ListOwnership.OWNED_BY_VIEWER, ImmutableSet.of(ListSource.MANUAL), Locale.ENGLISH, 0, 10));
      assertThat(result.getFirst()).isEqualTo(0);
      assertThat(result.getSecond()).isEmpty();
    }
  }

  @Test
  public void testGetAllListsForSeatSucceed() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));
    // Fetch all the visible lists
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, null,
        Collections.singleton(ListSource.MANUAL), null,0, 10));

    assertThat(result.getFirst()).isEqualTo(5);
    assertThat(result.getSecond().size()).isEqualTo(5);
    for (int i = 0; i < 5; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      assertThat(list.getId()).isEqualTo(4 - i);
      if (list.getId() == 1 || list.getId() == 2) {
        assertThat(list.isShared()).isFalse();
      } else {
        assertThat(list.isShared()).isTrue();
      }
    }
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(5)).getListEntityCount(anyLong());
  }

  @Test(description = "Verify no downstream call is made for field's not in projection")
  public void testGetAllListsForSeatWithPathSpecSucceed() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    PathSpecSet listTestPathSpecSet = PathSpecSet.of(com.linkedin.saleslist.List.fields().id(),
        com.linkedin.saleslist.List.fields().creatorContract());
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));
    // Fetch all the visible lists
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, null,
        Collections.singleton(ListSource.MANUAL), null,0, 10, listTestPathSpecSet));

    assertThat(result.getFirst()).isEqualTo(5);
    assertThat(result.getSecond().size()).isEqualTo(5);
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(0)).getListEntityCount(anyLong());
    verify(_lssSharingDB, times(0)).getResourceSharingPolicyTotal(any(), any());
  }

  @Test(description = "Test get lists for seat sorted by list name")
  public void testGetAllListsForSeatSortByNameSucceed() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(), any(), any())).thenReturn(Task.value(subscribePolicy));
    java.util.List<CrmPairing> crmPairingList = Arrays.asList(
        new CrmPairing().setStatus(CrmPairingDatasetStatus.ACTIVE)
            .setCrmPairingId(CRM_PAIRING_ID)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00Di0000000iWS1EAM")),
        new CrmPairing().setStatus(CrmPairingDatasetStatus.INACTIVE)
            .setCrmPairingId(CRM_PAIRING_ID)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00D300000001XriEAE")));
    when(_crmPairingClient.findCrmPairingsBySeat(CONTRACT_URN, SEAT_URN, false, null)).thenReturn(
        Task.value(crmPairingList));

    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPairs = createSeatToListView(100, 1);
    ownedCrmSeatListPairs.get(0).getSecond().name = "CRM_LEAD_00Di0000000iWS1EAM";
    ownedCrmSeatListPairs.get(0).getSecond().createdTime = 100L;
    ownedCrmSeatListPairs.get(0).getSecond().lastViewedTime = 100L;
    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPersonAccountPairs = createSeatToListView(100, 1);
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_SYNC, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(1, ownedCrmSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(1, ownedCrmSeatListPersonAccountPairs)));

    when(_lssListDB.getListEntityCount(100)).thenReturn(Task.value((long) 2));
    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Leads and Contacts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Lead and Contacts");
    CrmPairingUrn crmPairingUrn = new CrmPairingUrn(CONTRACT_URN, CRM_PAIRING_ID);
    when(_crmSettingClient.get(crmPairingUrn, SalesConnectedCrmSettingType.PERSON_ACCOUNT_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(false))));

    // Fetch all the visible lists
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.NAME, SortOrder.ASCENDING, null,
        Sets.newHashSet(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.CRM_PERSON_ACCOUNT), null,0, 10));

    assertThat(result.getFirst()).isEqualTo(6);
    assertThat(result.getSecond().size()).isEqualTo(6);
    for (int i = 0; i < 6; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      if (i == 5) {
        assertThat(list.getId()).isEqualTo(100L);
        assertThat(list.getName()).isEqualTo("CRM_LEAD_00Di0000000iWS1EAM");
        assertThat(list.getLocalizedName()).isEqualTo("My CRM Leads and Contacts");
        assertThat(list.hasLocalizedDescription()).isTrue();
        assertThat(list.getLocalizedDescription()).isEqualTo("My CRM Lead and Contacts");
        //For CRM List
        assertThat(list.isSubscribed()).isTrue();
      } else {
        assertThat(list.getId()).isEqualTo(i);
      }
      if (list.getId() == 1 || list.getId() == 2 || list.getId() == 100) {
        assertThat(list.isShared()).isFalse();
      } else {
        assertThat(list.isShared()).isTrue();
      }
    }
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(6)).getListEntityCount(anyLong());
  }

  @Test
  public void testGetOwnedListsForSeatSucceed() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);

    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(
        _salesListService.getListsForSeat(SEAT_ID, com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, ListOwnership.OWNED_BY_VIEWER, Sets.newHashSet(ListSource.MANUAL, ListSource.CRM_SYNC),
            null, 0, 10));

    assertThat(result.getFirst()).isEqualTo(3);
    assertThat(result.getSecond().size()).isEqualTo(3);
    for (int i = 0; i < 3; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      assertThat(list.getId()).isEqualTo(2 - i);
      if (list.getId() == 0) {
        assertThat(list.isShared()).isTrue();
      } else {
        assertThat(list.isShared()).isFalse();
      }
    }
    verify(_lssListDB, times(1)).getListsForSeatToListView(anyLong(), eq(ListType.LEAD),
        eq(com.linkedin.sales.espresso.ListSource.MANUAL), anyInt(), anyInt());
    verify(_lssListDB, times(3)).getListEntityCount(anyLong());
    verify(_lssListDB, times(0)).getList(anyLong());
  }

  @Test
  public void testGetOwnedListsSucceedWithSharingEnabledCrmListEnabled() {
    java.util.List<CrmPairing> crmPairingList = Arrays.asList(
        new CrmPairing().setStatus(CrmPairingDatasetStatus.ACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00Di0000000iWS1EAM"))
            .setCrmPairingId(CRM_PAIRING_ID),
        new CrmPairing().setStatus(CrmPairingDatasetStatus.INACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00D300000001XriEAE"))
            .setCrmPairingId(CRM_PAIRING_ID),
        new CrmPairing().setStatus(CrmPairingDatasetStatus.INACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00D6s0000008aROEAY"))
            .setCrmPairingId(CRM_PAIRING_ID));
    when(_crmPairingClient.findCrmPairingsBySeat(CONTRACT_URN, SEAT_URN, false, null)).thenReturn(
        Task.value(crmPairingList));

    java.util.List<Pair<Long, SeatToListView>> ownedManualSeatListPairs = createSeatToListView(2);
    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPairs = createSeatToListView(2);
    ownedCrmSeatListPairs.get(0).getSecond().name = "CRM_LEAD_00D300000001XriEAE";
    ownedCrmSeatListPairs.get(0).getSecond().createdTime = 2L;
    ownedCrmSeatListPairs.get(0).getSecond().lastViewedTime = 2L;
    ownedCrmSeatListPairs.get(1).getSecond().name = "CRM_LEAD_00Di0000000iWS1EAM";
    ownedCrmSeatListPairs.get(1).getSecond().createdTime = 3L;
    ownedCrmSeatListPairs.get(1).getSecond().lastViewedTime = 3L;
    java.util.List<Pair<Long, SeatToListView>> ownedSeatListPersonAccountPairs = createSeatToListView(2);
    ownedCrmSeatListPairs = Arrays.asList(new Pair<>(2L, ownedCrmSeatListPairs.get(0).getSecond()), new Pair<>(3L, ownedCrmSeatListPairs.get(1).getSecond()));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.MANUAL, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(2, ownedManualSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_SYNC, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(3, ownedCrmSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(3, ownedSeatListPersonAccountPairs)));
    mockListDBForGetListHelper(2, 0, 0, null, null);
    // Configure CRM List
    List crmList1 = new List();
    crmList1.name = "CRM_LEAD_00D300000001XriEAE";
    crmList1.listType = ListType.LEAD;
    crmList1.creatorSeatId = SEAT_ID;
    crmList1.listSource = com.linkedin.sales.espresso.ListSource.CRM_SYNC;

    List crmList2 = new List();
    crmList2.name = "CRM_LEAD_00Di0000000iWS1EAM";
    crmList2.listType = ListType.LEAD;
    crmList2.creatorSeatId = SEAT_ID;
    crmList2.listSource = com.linkedin.sales.espresso.ListSource.CRM_SYNC;

    when(_lssListDB.getList(2)).thenReturn(Task.value(crmList1));
    when(_lssListDB.getListEntityCount(2)).thenReturn(Task.value((long) 2));
    when(_lssListDB.getList(3)).thenReturn(Task.value(crmList2));
    when(_lssListDB.getListEntityCount(3)).thenReturn(Task.value((long) 3));
    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Leads and Contacts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Lead and Contacts");
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(), any(), any())).thenReturn(Task.value(subscribePolicy));
    CrmPairingUrn crmPairingUrn = new CrmPairingUrn(CONTRACT_URN, CRM_PAIRING_ID);
    when(_crmSettingClient.get(crmPairingUrn, SalesConnectedCrmSettingType.PERSON_ACCOUNT_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(false))));
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_VIEWED, SortOrder.DESCENDING, ListOwnership.OWNED_BY_VIEWER,
        new HashSet<>(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.CRM_PERSON_ACCOUNT)), null,0, 10));

    assertThat(result.getFirst()).isEqualTo(3);
    assertThat(result.getSecond().size()).isEqualTo(3);
    for (int i = 0; i < 3; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      //CRM List with name "CRM_LEAD_123" will not be returned
      assertThat(list.getId()).isNotEqualTo(2L);
      assertThat(list.isShared()).isFalse();
      if(list.getListSource().equals(ListSource.CRM_SYNC)) {
        assertThat(list.isSubscribed(GetMode.NULL)).isTrue();
      }
      else {
        assertThat(list.isSubscribed(GetMode.NULL)).isNull();
      }
      if (list.getId() == 3) {
        assertThat(list.getName().equals("CRM_LEAD_00Di0000000iWS1EAM"));
        assertThat(list.getLocalizedName().equals("My CRM Leads and Contacts"));
        assertThat(!list.hasDescription());
        assertThat(list.getLocalizedDescription().equals("My CRM Lead and Contacts"));
      }
    }
    verify(_lssListDB, times(1)).getListsForSeatToListView(anyLong(), eq(ListType.LEAD),
        eq(com.linkedin.sales.espresso.ListSource.MANUAL), anyInt(), anyInt());
    verify(_lssListDB, times(3)).getListEntityCount(anyLong());
    verify(_lssListDB, times(0)).getList(anyLong());
  }

  @Test
  public void testGetOwnedListsSucceedWithPersonAccount() {
    java.util.List<CrmPairing> crmPairingList = Arrays.asList(
        new CrmPairing().setStatus(CrmPairingDatasetStatus.ACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00Di0000000iWS1EAM"))
            .setCrmPairingId(CRM_PAIRING_ID),
        new CrmPairing().setStatus(CrmPairingDatasetStatus.INACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00D300000001XriEAE"))
            .setCrmPairingId(CRM_PAIRING_ID),
        new CrmPairing().setStatus(CrmPairingDatasetStatus.INACTIVE)
            .setContract(CONTRACT_URN)
            .setCrmInstanceUrn(CrmUrnUtils.createCrmInstanceUrn(CrmSource.SFDC, "00D6s0000008aROEAY"))
            .setCrmPairingId(CRM_PAIRING_ID));
    when(_crmPairingClient.findCrmPairingsBySeat(CONTRACT_URN, SEAT_URN, false, null)).thenReturn(
        Task.value(crmPairingList));

    java.util.List<Pair<Long, SeatToListView>> ownedManualSeatListPairs = createSeatToListView(2);
    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPairs = createSeatToListView(2);
    ownedCrmSeatListPairs.get(0).getSecond().name = "CRM_LEAD_00D300000001XriEAE";
    ownedCrmSeatListPairs.get(0).getSecond().createdTime = 2L;
    ownedCrmSeatListPairs.get(0).getSecond().lastViewedTime = 2L;
    ownedCrmSeatListPairs.get(1).getSecond().name = "CRM_LEAD_00Di0000000iWS1EAM";
    ownedCrmSeatListPairs.get(1).getSecond().createdTime = 3L;
    ownedCrmSeatListPairs.get(1).getSecond().lastViewedTime = 3L;
    ownedCrmSeatListPairs = Arrays.asList(new Pair<>(2L, ownedCrmSeatListPairs.get(0).getSecond()), new Pair<>(3L, ownedCrmSeatListPairs.get(1).getSecond()));
    java.util.List<Pair<Long, SeatToListView>> ownedSeatListPersonAccountPairs = createSeatToListView(2);
    ownedSeatListPersonAccountPairs.get(0).getSecond().name = "CRM_PERSON_ACCOUNT_00D300000001XriEAE";
    ownedSeatListPersonAccountPairs.get(0).getSecond().createdTime = 4L;
    ownedSeatListPersonAccountPairs.get(0).getSecond().lastViewedTime = 4L;
    ownedSeatListPersonAccountPairs.get(1).getSecond().name = "CRM_PERSON_ACCOUNT_00Di0000000iWS1EAM";
    ownedSeatListPersonAccountPairs.get(1).getSecond().createdTime = 5L;
    ownedSeatListPersonAccountPairs.get(1).getSecond().lastViewedTime = 5L;
    ownedSeatListPersonAccountPairs = Arrays.asList(new Pair<>(4L, ownedSeatListPersonAccountPairs.get(0).getSecond()), new Pair<>(5L, ownedSeatListPersonAccountPairs.get(1).getSecond()));

    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.MANUAL, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(2, ownedManualSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_SYNC, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(3, ownedCrmSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT, 0, GET_COUNT_LIMIT))
        .thenReturn(Task.value(new Pair<>(3, ownedSeatListPersonAccountPairs)));
    mockListDBForGetListHelper(2, 0, 0, null, null);
    // Configure CRM List
    List crmList1 = new List();
    crmList1.name = "CRM_LEAD_00D300000001XriEAE";
    crmList1.listType = ListType.LEAD;
    crmList1.creatorSeatId = SEAT_ID;
    crmList1.listSource = com.linkedin.sales.espresso.ListSource.CRM_SYNC;

    List crmList2 = new List();
    crmList2.name = "CRM_LEAD_00Di0000000iWS1EAM";
    crmList2.listType = ListType.LEAD;
    crmList2.creatorSeatId = SEAT_ID;
    crmList2.listSource = com.linkedin.sales.espresso.ListSource.CRM_SYNC;

    List crmListPersonAccount1 = new List();
    crmList1.name = "CRM_PERSON_ACCOUNT_00D300000001XriEAE";
    crmList1.listType = ListType.LEAD;
    crmList1.creatorSeatId = SEAT_ID;
    crmList1.listSource = com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT;

    List crmListPersonAccount2 = new List();
    crmList2.name = "CRM_PERSON_ACCOUNT_00Di0000000iWS1EAM";
    crmList2.listType = ListType.LEAD;
    crmList2.creatorSeatId = SEAT_ID;
    crmList2.listSource = com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT;

    when(_lssListDB.getList(2)).thenReturn(Task.value(crmList1));
    when(_lssListDB.getListEntityCount(2)).thenReturn(Task.value((long) 2));
    when(_lssListDB.getList(3)).thenReturn(Task.value(crmList2));
    when(_lssListDB.getListEntityCount(3)).thenReturn(Task.value((long) 3));
    when(_lssListDB.getList(4)).thenReturn(Task.value(crmListPersonAccount1));
    when(_lssListDB.getListEntityCount(4)).thenReturn(Task.value((long) 2));
    when(_lssListDB.getList(5)).thenReturn(Task.value(crmListPersonAccount2));
    when(_lssListDB.getListEntityCount(5)).thenReturn(Task.value((long) 3));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Leads and Contacts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_SYNC), any(), any())).thenReturn("My CRM Lead and Contacts");
    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_PERSON_ACCOUNT), any(), any())).thenReturn("My CRM Person Accounts");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_PERSON_ACCOUNT), any(), any())).thenReturn("My CRM Person Accounts");
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(), any(), any())).thenReturn(Task.value(subscribePolicy));
    CrmPairingUrn crmPairingUrn = new CrmPairingUrn(CONTRACT_URN, CRM_PAIRING_ID);
    when(_crmSettingClient.get(crmPairingUrn, SalesConnectedCrmSettingType.PERSON_ACCOUNT_ENABLED)).thenReturn(
        Task.value(new SalesConnectedCrmSetting().setValue(SalesConnectedCrmSetting.Value.create(true))));
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_VIEWED, SortOrder.DESCENDING, ListOwnership.OWNED_BY_VIEWER,
        new HashSet<>(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.CRM_PERSON_ACCOUNT)), null,0, 10));

    assertThat(result.getFirst()).isEqualTo(4);
    assertThat(result.getSecond().size()).isEqualTo(4);
    for (int i = 0; i < 4; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      //CRM List with name "CRM_LEAD_123" will not be returned
      assertThat(list.getId()).isNotEqualTo(2L);
      assertThat(list.getId()).isNotEqualTo(4L);
      assertThat(list.isShared()).isFalse();
      if(list.getListSource().equals(ListSource.CRM_SYNC) || list.getListSource().equals(ListSource.CRM_PERSON_ACCOUNT)) {
        assertThat(list.isSubscribed(GetMode.NULL)).isTrue();
      }
      else {
        assertThat(list.isSubscribed(GetMode.NULL)).isNull();
      }
      if (list.getId() == 3) {
        assertThat(list.getName().equals("CRM_LEAD_00Di0000000iWS1EAM"));
        assertThat(list.getLocalizedName().equals("My CRM Leads and Contacts"));
        assertThat(!list.hasDescription());
        assertThat(list.getLocalizedDescription().equals("My CRM Lead and Contacts"));
      } else if (list.getId() == 5) {
        assertThat(list.getName().equals("CRM_PERSON_ACCOUNT_00Di0000000iWS1EAM"));
        assertThat(list.getLocalizedName().equals("My CRM Person Accounts"));
        assertThat(!list.hasDescription());
        assertThat(list.getLocalizedDescription().equals("My CRM Person Accounts"));
      }
    }
    verify(_lssListDB, times(1)).getListsForSeatToListView(anyLong(), eq(ListType.LEAD),
        eq(com.linkedin.sales.espresso.ListSource.MANUAL), anyInt(), anyInt());
    verify(_lssListDB, times(1)).getListsForSeatToListView(anyLong(), eq(ListType.LEAD),
        eq(com.linkedin.sales.espresso.ListSource.CRM_SYNC), anyInt(), anyInt());
    verify(_lssListDB, times(1)).getListsForSeatToListView(anyLong(), eq(ListType.LEAD),
        eq(com.linkedin.sales.espresso.ListSource.CRM_PERSON_ACCOUNT), anyInt(), anyInt());
    verify(_lssListDB, times(4)).getListEntityCount(anyLong());
    verify(_lssListDB, times(0)).getList(anyLong());
  }

  @Test
  public void testGetSharedWithListsForSeatSucceedWithSharingEnabled() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);

    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));

    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(
        _salesListService.getListsForSeat(SEAT_ID, com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, ListOwnership.SHARED_WITH_VIEWER, Collections.singleton(ListSource.MANUAL), null,0,
            10));

    assertThat(result.getFirst()).isEqualTo(2);
    assertThat(result.getSecond().size()).isEqualTo(2);
    for (int i = 0; i < 2; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      assertThat(list.getId()).isEqualTo(4 - i);
      assertThat(list.getLastModifiedAt()).isEqualTo(4 - i);
      assertThat(list.isShared()).isTrue();
    }
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(2)).getListEntityCount(anyLong());
  }
  @Test
  public void testGetSharedWithListsForSeatFailedWithSharingEnabled() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);
    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesBySubject(new SeatUrn(SEAT_ID), PolicyType.ACCOUNT_LIST.toString(),
        Sets.newHashSet(ShareRole.WRITER),
        0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.failure(new RuntimeException()));
    when(_lssSharingDB.getPoliciesBySubject(new SeatUrn(SEAT_ID), PolicyType.ACCOUNT_LIST.toString(),
        Sets.newHashSet(ShareRole.OWNER),
        0, SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.failure(new RuntimeException()));
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(
        _salesListService.getListsForSeat(SEAT_ID, com.linkedin.saleslist.ListType.ACCOUNT, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, ListOwnership.EDITABLE_BY_VIEWER, Collections.singleton(ListSource.MANUAL), null, 0,
            10));
    assertThat(result.getFirst()).isEqualTo(3);
    verify(_lssListDB, times(3)).getListEntityCount(anyLong());
  }

  @Test
  public void testGetEditableByListsForSeatSucceedWithSharingEnabled() {
    int ownedListCount = 3;
    int sharedWithListCount = 3;
    int sharedByListCount = 1;
    int editableByListCount = ownedListCount + sharedWithListCount - 1;
    Set<ListSource> listSourceSet = new HashSet(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.LINKEDIN_SALES_INSIGHTS,
        ListSource.SYSTEM));
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, editableByListCount, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));

    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, ListOwnership.EDITABLE_BY_VIEWER,
        listSourceSet, null, 0, 10));

    assertThat(result.getFirst()).isEqualTo(editableByListCount);
    assertThat(result.getSecond().size()).isEqualTo(editableByListCount);
    for (int i = 0; i < editableByListCount; i++) {
      com.linkedin.saleslist.List list = result.getSecond().get(i);
      assertThat(list.getId()).isEqualTo(editableByListCount - 1 - i);
      assertThat(list.getLastModifiedAt()).isEqualTo(editableByListCount - 1 - i);
      if(i < editableByListCount - ownedListCount) {
        assertThat(list.isShared()).isTrue();
      }
    }
    verify(_lssListDB, times(editableByListCount - ownedListCount)).getList(anyLong());
    verify(_lssListDB, times(editableByListCount)).getListEntityCount(anyLong());
  }

  @Test
  public void testGetAllListCountForSeatSucceedWithSharingEnabled() {
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    int pagingCount = 2;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    // Fetch all the visible lists
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, null, Collections.singleton(ListSource.MANUAL),
        null, 0, pagingCount));

    assertThat(result.getFirst()).isEqualTo(5);
    assertThat(result.getSecond().size()).isEqualTo(pagingCount);
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(pagingCount)).getListEntityCount(anyLong());
  }

  @Test
  public void testGetAllListCountForSeatSucceedWithDuplicatedList() {
    java.util.List<Pair<Long, SeatToListView>> ownedCrmSeatListPairs = createSeatToListView(2);

    when(_lssListDB.getListsForSeatToListView(anyLong(), any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(new Pair<>(2, ownedCrmSeatListPairs)));
    long duplicatedListId = ownedCrmSeatListPairs.get(0).getFirst();
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    List dummyList = new List();
    dummyList.name = "dummyList";
    dummyList.listType = ListType.LEAD;
    dummyList.creatorSeatId = SEAT_ID;
    dummyList.listSource = com.linkedin.sales.espresso.ListSource.MANUAL;

    when(_lssListDB.getList(anyLong())).thenReturn(Task.value(dummyList));

    java.util.List<Pair<Urn, SubjectPolicy>> sharedListUrnPairs = new ArrayList<>();
    sharedListUrnPairs.add(new Pair<>(UrnUtils.createSalesListUrn(duplicatedListId), new SubjectPolicy()));
    sharedListUrnPairs.add(new Pair<>(UrnUtils.createSalesListUrn(duplicatedListId+3), new SubjectPolicy()));
    when(_lssSharingDB.getPoliciesBySubject(any(), anyString(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(sharedListUrnPairs, 0, 0, 2)));

    Map<Urn, Integer> sharingTotalMap = new HashMap<>();
    sharingTotalMap.put(UrnUtils.createSalesListUrn(duplicatedListId), 2);
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(anySet(), any())).thenReturn(Task.value(sharingTotalMap));
    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(2));
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value((long) 2));
    Pair<Integer, java.util.List<com.linkedin.saleslist.List>> result = await(_salesListService.getListsForSeat(SEAT_ID,
        com.linkedin.saleslist.ListType.LEAD, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, null, Collections.singleton(ListSource.MANUAL),
        null, 0, 10));

    //2 owned lists and 2 share lists but there is one duplicated list from both sides so total should be 3
    assertThat(result.getFirst()).isEqualTo(3);
    assertThat(result.getSecond().size()).isEqualTo(3);
    for (com.linkedin.saleslist.List list : result.getSecond()) {
      if (list.getId() == duplicatedListId) {
        assertThat(list.isShared()).isTrue();
      }
    }
  }

  @Test
  public void testGetBluebirdLists() {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List bluebirdList = new List();
    bluebirdList.name = "Bluebird List";
    bluebirdList.listType = ListType.LEAD;
    bluebirdList.creatorSeatId = SEAT_ID;
    bluebirdList.listSource = com.linkedin.sales.espresso.ListSource.CRM_BLUEBIRD;

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(bluebirdList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.CRM_BLUEBIRD), eq(com.linkedin.saleslist.ListType.LEAD), any()))
        .thenReturn("Customer Advocates");
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.CRM_BLUEBIRD), eq(com.linkedin.saleslist.ListType.LEAD), any()))
        .thenReturn("Customer Advocates Description");

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(bluebirdList.name);
    assertThat(list.getLocalizedName()).isEqualTo("Customer Advocates");
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.hasDescription()).isFalse();
    assertThat(list.getLocalizedDescription()).isEqualTo("Customer Advocates Description");
    assertThat(list.isShared()).isFalse();
  }

  @Test
  public void testGetMyCurrentAccountsListWithLocalization() {
    PolicyType policyType = PolicyType.ACCOUNT_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List myCurrentAccountsList = new List();
    myCurrentAccountsList.name = "BOOK_OF_BUSINESS";
    myCurrentAccountsList.listType = ListType.ACCOUNT;
    myCurrentAccountsList.creatorSeatId = SEAT_ID;
    myCurrentAccountsList.listSource = com.linkedin.sales.espresso.ListSource.BOOK_OF_BUSINESS;

    String localizedListName = "Localized My Current Accounts list name";
    String localizedListDescription = "Localized My Current Accounts list description";

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(myCurrentAccountsList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.BOOK_OF_BUSINESS), eq(com.linkedin.saleslist.ListType.ACCOUNT), any()))
        .thenReturn(localizedListName);
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.BOOK_OF_BUSINESS), eq(com.linkedin.saleslist.ListType.ACCOUNT), any()))
        .thenReturn(localizedListDescription);

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(myCurrentAccountsList.name);
    assertThat(list.getLocalizedName()).isEqualTo(localizedListName);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.ACCOUNT);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualTo(myCurrentAccountsList.description);
    assertThat(list.getLocalizedDescription()).isEqualTo(localizedListDescription);
    assertThat(list.isShared()).isFalse();
  }

  @Test
  public void testGetMyCurrentAccountsList() {
    PolicyType policyType = PolicyType.ACCOUNT_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List myCurrentAccountsList = new List();
    myCurrentAccountsList.name = "user defined list name";
    myCurrentAccountsList.description = "user defined list description";
    myCurrentAccountsList.listType = ListType.ACCOUNT;
    myCurrentAccountsList.creatorSeatId = SEAT_ID;
    myCurrentAccountsList.listSource = com.linkedin.sales.espresso.ListSource.BOOK_OF_BUSINESS;

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(myCurrentAccountsList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    verify(_localizationHelper, times(0))
        .getLocalizedListName(eq(ListSource.BOOK_OF_BUSINESS), eq(com.linkedin.saleslist.ListType.ACCOUNT), any());
    verify(_localizationHelper, times(0))
        .getLocalizedListDescription(eq(ListSource.BOOK_OF_BUSINESS), eq(com.linkedin.saleslist.ListType.ACCOUNT), any());

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(myCurrentAccountsList.name);
    assertThat(list.hasLocalizedName()).isFalse();
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.ACCOUNT);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.getDescription()).isEqualTo(myCurrentAccountsList.description);
    assertThat(list.hasLocalizedDescription()).isFalse();
    assertThat(list.isShared()).isFalse();
  }

  @Test
  public void testGetAutoProspectorRecommendationLists() {
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.READ;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List autoProspectorList = new List();
    autoProspectorList.name = "AUTO_PROSPECTOR_REC";
    autoProspectorList.listType = ListType.LEAD;
    autoProspectorList.creatorSeatId = SEAT_ID;
    autoProspectorList.listSource = com.linkedin.sales.espresso.ListSource.AUTO_PROSPECTOR_REC;

    String localizedListName = "Localized Auto Prospector list name";
    String localizedListDescription = "Localized Auto Prospector list description";

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(autoProspectorList));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(LIST_ENTITY_COUNT));
    when(_lssSharingDB.getResourceSharingPolicyTotal(listUrn, policyType)).thenReturn(Task.value(0));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction)).thenReturn(
        Task.value(AccessDecision.ALLOWED));

    when(_localizationHelper.getLocalizedListName(eq(ListSource.AUTO_PROSPECTOR_REC), eq(com.linkedin.saleslist.ListType.LEAD), any()))
        .thenReturn(localizedListName);
    when(_localizationHelper.getLocalizedListDescription(eq(ListSource.AUTO_PROSPECTOR_REC), eq(com.linkedin.saleslist.ListType.LEAD), any()))
        .thenReturn(localizedListDescription);

    com.linkedin.saleslist.List list = await(_salesListService.getList(LIST_ID, SEAT_URN, null));
    assertThat(list.getId()).isEqualTo(LIST_ID);
    assertThat(list.getCreator().getSeatIdEntity()).isEqualTo(SEAT_ID);
    assertThat(list.getName()).isEqualTo(autoProspectorList.name);
    assertThat(list.getLocalizedName()).isEqualTo(localizedListName);
    assertThat(list.getListType()).isEqualTo(com.linkedin.saleslist.ListType.LEAD);
    assertThat(list.getEntityCount().longValue()).isEqualTo(LIST_ENTITY_COUNT);
    assertThat(list.hasDescription()).isFalse();
    assertThat(list.getLocalizedDescription()).isEqualTo(localizedListDescription);
    assertThat(list.isShared()).isFalse();
  }

  //-------------- test for get lists for entity ---------------//
  ////////////////////////////////////////////////////////////////
  @Test
  public void testGetListsForEntitySucceedWhenGetListIdsInDBNotFound() {
    Urn entityUrn = new MemberUrn(1L);

    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    Set<ListSource> listSourceSet = new HashSet(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.LINKEDIN_SALES_INSIGHTS,
        ListSource.SYSTEM));
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount,
        null, null);
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssListDB.getListIdsForEntity(entityUrn, CONTRACT_ID, null)).thenReturn(
        Task.failure(new EntityNotFoundException(null, "")));
    when(_lssListDB.getListIdsForEntity(entityUrn, 0L, null))
        .thenReturn(Task.value(Collections.emptyList()));
    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    java.util.List<com.linkedin.saleslist.List> result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, DEFAULT_LIST_TYPES));

    assertThat(result.size()).isEqualTo(0);

    // includeAccountMap

    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(),any(), any())).thenReturn(Task.value(subscribePolicy));
     result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, ALL_LIST_TYPES));

    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void testGetListsForEntityFailWhenGetListIdsInDBException() {
    Urn entityUrn = new MemberUrn(1L);
    Set<ListSource> listSourceSet = new HashSet(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.LINKEDIN_SALES_INSIGHTS,
        ListSource.SYSTEM));
    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount,
        sharedByListCount, null, null);
    when(_lssListDB.getListEntityCount(anyLong())).thenReturn(Task.value(10L));

    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssListDB.getListIdsForEntity(entityUrn, CONTRACT_ID, null))
        .thenReturn(Task.failure(new RuntimeException("get listIds fail")));
   when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, listSourceSet, null, DEFAULT_LIST_TYPES));
    }).withCause(new RuntimeException("fail to get lists for Entity: urn:li:member:1"));

    // includeAccountMap
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING, listSourceSet, null, ALL_LIST_TYPES));
    }).withCause(new RuntimeException("fail to get lists for Entity: urn:li:member:1"));
  }

  @Test
  public void testGetListsForEntitySuccess() {
    Urn entityUrn = new MemberUrn(1L);

    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount,
        sharedByListCount, null, null);
    java.util.List<Long> listIds = new ArrayList<>();
    listIds.add((long)ownedListCount - 1);
    listIds.add((long)ownedListCount + sharedWithListCount - 1);
    listIds.add((long)ownedListCount + sharedWithListCount);

    Set<ListSource> listSourceSet = new HashSet(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.LINKEDIN_SALES_INSIGHTS,
        ListSource.SYSTEM));
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(), any(), any())).thenReturn(Task.value(subscribePolicy));

    when(_lssListDB.getListIdsForEntity(entityUrn, CONTRACT_ID, null))
        .thenReturn(Task.value(listIds));
    when(_lssListDB.getListIdsForEntity(entityUrn, 0, null))
        .thenReturn(Task.value(listIds));

    java.util.List<com.linkedin.saleslist.List> result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, DEFAULT_LIST_TYPES));
    assertThat(result.size()).isEqualTo(2);

    // includeAccountMap
    when(_lssSharingDB.getSubjectPolicy(any(),any(), any())).thenReturn(Task.value(subscribePolicy));
    result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, ALL_LIST_TYPES));
    assertThat(result.size()).isEqualTo(3);
    result.stream().map(list -> assertThat(list.isSubscribed()).isTrue());
  }

  @Test
  public void testGetListsForEntitySuccessForAcrossContractsResults() {
    Urn entityUrn = new MemberUrn(1L);

    int ownedListCount = 3;
    int sharedWithListCount = 2;
    int sharedByListCount = 1;
    mockListDBForGetListFromSeatForListView(ownedListCount, sharedWithListCount, sharedByListCount, null, true);
    java.util.List<Long> listIds = new ArrayList<>();
    listIds.add((long)ownedListCount - 1);
    listIds.add((long)ownedListCount + sharedWithListCount - 1);
    listIds.add((long)ownedListCount + sharedWithListCount);
    Set<ListSource> listSourceSet = new HashSet(Arrays.asList(ListSource.MANUAL, ListSource.CRM_SYNC, ListSource.LINKEDIN_SALES_INSIGHTS,
        ListSource.SYSTEM));
    when(_aclServiceDispatcher.checkAccessDecision(eq(new SeatUrn(SEAT_ID)), eq(PolicyType.LEAD_LIST), any(),
        eq(AccessAction.READ))).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.getResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(1));

    when(_lssListDB.getListIdsForEntity(entityUrn, CONTRACT_ID, null))
        .thenReturn(Task.value(listIds));
    when(_lssListDB.getListIdsForEntity(entityUrn, 0, null))
        .thenReturn(Task.value(listIds));
    java.util.List<com.linkedin.saleslist.List> result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, DEFAULT_LIST_TYPES));
    assertThat(result.size()).isEqualTo(2);

    // includeAccountMap
    SubjectPolicy subscribePolicy = new SubjectPolicy();
    subscribePolicy.setIsSubscribed(true);
    when(_lssSharingDB.getSubjectPolicy(any(),any(), any())).thenReturn(Task.value(subscribePolicy));
    result = await(
        _salesListService.getListsForEntity(entityUrn, CONTRACT_ID, SEAT_ID, null, ListOrdering.LAST_MODIFIED,
            SortOrder.DESCENDING, listSourceSet, null, ALL_LIST_TYPES));
    assertThat(result.size()).isEqualTo(3);
    result.stream().map(list -> assertThat(list.isSubscribed()).isTrue());
  }


  //-------------- test for update list ---------------//
  ////////////////////////////////////////////////////

  @Test
  public void testUpdateListSucceedWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.UPDATE;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = listType;

    String updateListName = "updateName";
    long currentTime = System.currentTimeMillis();

    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setName(updateListName);
    list.setLastViewedAt(currentTime);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.updateList(eq(LIST_ID), any())).thenReturn(Task.value(true));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    Boolean isSuccessful = await(_salesListService.updateList(LIST_ID, list, SEAT_ID));
    assertThat(isSuccessful).isTrue();

    verify(_lssListDB, times(1)).updateList(eq(LIST_ID), any());
  }

  // update list name
  @Test
  public void testUpdateListSucceedOnlyUpdateListNameWithSharingEnabled() throws URISyntaxException {
    ListType listType = ListType.LEAD;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.UPDATE;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = listType;

    String updateListName = "updateName";

    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setName(updateListName);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.updateList(eq(LIST_ID), any())).thenReturn(Task.value(true));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    Boolean isSuccessful = await(_salesListService.updateList(LIST_ID, list, SEAT_ID));
    assertThat(isSuccessful).isTrue();

    verify(_lssListDB, times(1)).updateList(eq(LIST_ID), any());
  }

  @Test
  public void testUpdateListFailed() throws URISyntaxException {
    String updateListName = "updateName";

    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setName(updateListName);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.failure(new RestLiServiceException(HttpStatus.S_500_INTERNAL_SERVER_ERROR)));


    assertThatExceptionOfType(PromiseException.class)
        .isThrownBy(() -> await(_salesListService.updateList(LIST_ID, list, SEAT_ID)));
    verify(_lssListDB, times(0)).updateList(eq(LIST_ID), any());
  }

  //-------------- tests for upserting list ---------------//
  ////////////////////////////////////////////////////

  @Test
  public void testUpsertUpdatesAnExistingList() {
    ListType listType = ListType.LEAD;
    PolicyType policyType = PolicyType.LEAD_LIST;
    AccessAction accessAction = AccessAction.UPDATE;
    SalesListUrn listUrn = UrnUtils.createSalesListUrn(LIST_ID);

    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = listType;

    String updateListName = "updateName";
    long currentTime = System.currentTimeMillis();

    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setName(updateListName);
    list.setLastViewedAt(currentTime);
    list.setCreator(SEAT_URN);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(espressoList));
    when(_lssListDB.updateList(eq(LIST_ID), any())).thenReturn(Task.value(true));

    when(_aclServiceDispatcher.checkAccessDecision(new SeatUrn(SEAT_ID), policyType, listUrn, accessAction))
        .thenReturn(Task.value(AccessDecision.ALLOWED));

    UpdateResponse response = await(_salesListService.upsertList(LIST_ID, list, SEAT_ID));
    assertThat(response.getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);

    verify(_lssListDB, times(1)).updateList(eq(LIST_ID), any());
  }

  @Test
  public void testUpsertCreatesANewList() {
    ListType listType = ListType.LEAD;

    List espressoList = new List();
    espressoList.name = "test";
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = listType;

    String updateListName = "updateName";
    long currentTime = System.currentTimeMillis();

    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setName(updateListName);
    list.setLastViewedAt(currentTime);
    list.setCreator(SEAT_URN);
    list.setListType(com.linkedin.saleslist.ListType.ACCOUNT);

    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.failure(new EntityNotFoundException(LIST_ID, "not found")));
    when(_lssListDB.createList(eq(LIST_ID), any())).thenReturn(Task.value(LIST_ID));

    UpdateResponse response = await(_salesListService.upsertList(LIST_ID, list, SEAT_ID));
    assertThat(response.getStatus()).isEqualTo(HttpStatus.S_201_CREATED);

    verify(_lssListDB, times(1)).createList(eq(LIST_ID), any());
  }

  @Test
  public void getDeleteListEntitiesTaskDoesNotProduceKafkaMessageWhenListSizeIsLessThanThreshold() {
    doReturn(Task.value(LIST_ENTITY_ONLINE_PROCESSING_LIMIT)).when(_lssListDB).getListEntityCount(anyLong());
    doReturn(Task.value(Boolean.TRUE)).when(_lssListDB).deleteAllListEntities(anyLong());

    await(_salesListService.getDeleteListEntitiesTask(LIST_ID, SEAT_URN, CONTRACT_URN));

    verify(_trackingClient, never()).send(any(), any());
  }


  @Test
  public void getDeleteListEntitiesTaskProducesKafkaMessageWhenListSizeIsGreaterThanThreshold() {
    doReturn(Task.value(LIST_ENTITY_ONLINE_PROCESSING_LIMIT + 1L)).when(_lssListDB).getListEntityCount(anyLong());
    doReturn(Task.value(Boolean.TRUE)).when(_lssListDB).deleteAllListEntities(anyLong());
    doReturn(Task.value(Lists.newArrayList())).when(_lssListDB).
        getEntityUrnsForListEntitiesForMultipleLists(any(), anyInt(), anyInt());

    SalesSeat seat = new SalesSeat();
    seat.setMember(new MemberUrn(1L));

    doReturn(Task.value(seat)).when(_salesSeatClient)
        .getSeat(eq(SEAT_URN.getSeatIdEntity()), any(), any(EnterpriseApplicationUsageUrn.class), any());
    await(_salesListService.getDeleteListEntitiesTask(LIST_ID, SEAT_URN, CONTRACT_URN));

    verify(_trackingClient, times(1)).send(any(), any());
  }

  @Test
  public void testFindAccountMapsByOrg_NoMaps() {
    when(_salesAccountToListMappingService.getAccountToListMappingForAccount(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));
    SubjectPolicy policy = new SubjectPolicy();
    policy.setResourceContext(ORG_URN2.toString());
    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(any(), any(), any(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));
    BasicCollectionResult<com.linkedin.saleslist.List> list =
        await(_salesListService.findAccountMapsByOrg(SEAT_URN, ORG_URN));

    assertThat(list.getElements()).isEmpty();
    verify(_lssSharingDB, times(0)).batchGetResourceSharingPolicyTotal(any(), any());
    verify(_lssSharingDB, times(0)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
    verifyNoInteractions(_lssListDB);
  }

  @Test
  public void testFindAccountMapsByOrg_happyPath() {
    AccountToListMapping accountToListMapping = new AccountToListMapping().setList(LIST_URN);
    when(_salesAccountToListMappingService.getAccountToListMappingForAccount(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(accountToListMapping), 0, 0, 0)));

    SubjectPolicy policy1 = new SubjectPolicy();
    policy1.setResourceContext(ORG_URN.toString());
    policy1.setRole(ShareRole.OWNER);
    policy1.setLastViewedTime(null);
    Pair<Urn, SubjectPolicy> policyPair1 = Pair.make(LIST_URN, policy1);

    SubjectPolicy policy2 = new SubjectPolicy();
    policy2.setResourceContext(ORG_URN.toString());
    policy2.setRole(ShareRole.WRITER);
    policy2.setLastViewedTime(null);
    Pair<Urn, SubjectPolicy> policyPair2 = Pair.make(LIST_URN2, policy2);

    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(any(), any(), any(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Arrays.asList(policyPair1, policyPair2), 0, 0, 0)));
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(createMockEspressoList(123L)));
    when(_lssListDB.getList(LIST_ID2)).thenReturn(Task.value(createMockEspressoList(null)));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(1L));
    when(_lssListDB.getListEntityCount(LIST_ID2)).thenReturn(Task.value(2L));
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(any(), any()))
        .thenReturn(Task.value(ImmutableMap.of(LIST_URN, 0, LIST_URN2, 2)));
    BasicCollectionResult<com.linkedin.saleslist.List> list =
        await(_salesListService.findAccountMapsByOrg(SEAT_URN, ORG_URN));

    assertThat(list.getElements()).hasSize(2);
    assertThat(list.getElements().get(0).getId()).isEqualTo(LIST_ID);
    assertThat(list.getElements().get(1).getId()).isEqualTo(LIST_ID2);
    assertThat(list.getElements().get(0).getEntityCount()).isEqualTo(1);
    assertThat(list.getElements().get(1).getEntityCount()).isEqualTo(2);
    assertThat(list.getElements().get(0).isShared()).isFalse();
    assertThat(list.getElements().get(1).isShared()).isTrue();
    assertThat(list.getElements().get(1).isAccepted()).isTrue();
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(2)).getListEntityCount(anyLong());
    verify(_lssSharingDB, times(0)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testFindAccountMapsByOrg_ownedMapWithoutPolicy_hasAccess() {
    AccountToListMapping accountToListMapping = new AccountToListMapping().setList(LIST_URN);
    when(_salesAccountToListMappingService.getAccountToListMappingForAccount(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(accountToListMapping), 0, 0, 0)));

    SubjectPolicy policy2 = new SubjectPolicy();
    policy2.setResourceContext(ORG_URN.toString());
    policy2.setRole(ShareRole.WRITER);
    policy2.setLastViewedTime(null);
    Pair<Urn, SubjectPolicy> policyPair2 = Pair.make(LIST_URN2, policy2);

    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(any(), any(), any(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(policyPair2), 0, 0, 0)));
    // the owned map without a policy does not have any other collaborators, thus the seat should have access to this map
    when(_lssSharingDB.getPoliciesByResource(eq(LIST_URN), any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));
    when(_lssListDB.getList(LIST_ID)).thenReturn(Task.value(createMockEspressoList(123L)));
    when(_lssListDB.getList(LIST_ID2)).thenReturn(Task.value(createMockEspressoList(null)));
    when(_lssListDB.getListEntityCount(LIST_ID)).thenReturn(Task.value(1L));
    when(_lssListDB.getListEntityCount(LIST_ID2)).thenReturn(Task.value(2L));
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(any(), any()))
        .thenReturn(Task.value(ImmutableMap.of(LIST_URN, 0, LIST_URN2, 2)));
    BasicCollectionResult<com.linkedin.saleslist.List> list =
        await(_salesListService.findAccountMapsByOrg(SEAT_URN, ORG_URN));

    assertThat(list.getElements()).hasSize(2);
    assertThat(list.getElements().get(0).getId()).isEqualTo(LIST_ID);
    assertThat(list.getElements().get(1).getId()).isEqualTo(LIST_ID2);
    assertThat(list.getElements().get(0).getEntityCount()).isEqualTo(1);
    assertThat(list.getElements().get(1).getEntityCount()).isEqualTo(2);
    assertThat(list.getElements().get(0).isShared()).isFalse();
    assertThat(list.getElements().get(1).isShared()).isTrue();
    assertThat(list.getElements().get(1).isAccepted()).isTrue();
    verify(_lssListDB, times(2)).getList(anyLong());
    verify(_lssListDB, times(2)).getListEntityCount(anyLong());
    verify(_lssSharingDB, times(1)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testFindAccountMapsByOrg_ownedMapWithoutPolicy_noAccess() {
    AccountToListMapping accountToListMapping = new AccountToListMapping().setList(LIST_URN);
    when(_salesAccountToListMappingService.getAccountToListMappingForAccount(any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(accountToListMapping), 0, 0, 0)));

    SubjectPolicy policy2 = new SubjectPolicy();
    policy2.setResourceContext(ORG_URN.toString());
    policy2.setRole(ShareRole.WRITER);
    policy2.setLastViewedTime(null);
    Pair<Urn, SubjectPolicy> policyPair2 = Pair.make(LIST_URN2, policy2);

    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(any(), any(), any(), anySet(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(policyPair2), 0, 0, 0)));
    // the owned map without a policy has a collaborator who is considered the owner of this map now,
    // thus the seat shouldn't have access to this map.
    when(_lssSharingDB.getPoliciesByResource(eq(LIST_URN), any(), any(), anyInt(), anyInt()))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(Pair.make(SEAT_URN_2, ShareRole.WRITER)), 0, 0, 1)));
    when(_lssListDB.getList(LIST_ID2)).thenReturn(Task.value(createMockEspressoList(null)));
    when(_lssListDB.getListEntityCount(LIST_ID2)).thenReturn(Task.value(2L));
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(any(), any()))
        .thenReturn(Task.value(ImmutableMap.of(LIST_URN2, 2)));
    BasicCollectionResult<com.linkedin.saleslist.List> list =
        await(_salesListService.findAccountMapsByOrg(SEAT_URN, ORG_URN));

    assertThat(list.getElements()).hasSize(1);
    assertThat(list.getElements().get(0).getId()).isEqualTo(LIST_ID2);
    assertThat(list.getElements().get(0).getEntityCount()).isEqualTo(2);
    assertThat(list.getElements().get(0).isShared()).isTrue();
    assertThat(list.getElements().get(0).isAccepted()).isTrue();
    verify(_lssListDB, times(1)).getList(anyLong());
    verify(_lssListDB, times(1)).getListEntityCount(anyLong());
    verify(_lssSharingDB, times(1)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testSortList() {
    com.linkedin.saleslist.List list1 = createMockList("aList", 2L, 33L);
    com.linkedin.saleslist.List list2 = createMockList("bList", 1L, 22L);
    com.linkedin.saleslist.List list3 = createMockList("cList", 3L, 11L);
    // test that list without listViewedAt field is ordered last when sorting by LAST_VIEWED
    com.linkedin.saleslist.List list4 = createMockList("dList", 4L, null);
    // test that list without listModifiedAt field is ordered last when sorting by LAST_MODIFIED
    com.linkedin.saleslist.List list5 = createMockList("eList", null, 44L);
    java.util.List<com.linkedin.saleslist.List> lists = Arrays.asList(list1, list2, list3, list4, list5);

    java.util.List<com.linkedin.saleslist.List> sortedListByNameAscending =
        _salesListService.sortLists(lists, ListOrdering.NAME, SortOrder.ASCENDING);
    java.util.List<com.linkedin.saleslist.List> sortedListByNameDescending =
        _salesListService.sortLists(lists, ListOrdering.NAME, SortOrder.DESCENDING);
    java.util.List<com.linkedin.saleslist.List> sortedListByLastViewedAscending =
        _salesListService.sortLists(lists, ListOrdering.LAST_VIEWED, SortOrder.ASCENDING);
    java.util.List<com.linkedin.saleslist.List> sortedListByLastViewedDescending =
        _salesListService.sortLists(lists, ListOrdering.LAST_VIEWED, SortOrder.DESCENDING);
    java.util.List<com.linkedin.saleslist.List> sortedListByLastModifiedAscending =
        _salesListService.sortLists(lists, ListOrdering.LAST_MODIFIED, SortOrder.ASCENDING);
    java.util.List<com.linkedin.saleslist.List> sortedListByLastModifiedDescending =
        _salesListService.sortLists(lists, ListOrdering.LAST_MODIFIED, SortOrder.DESCENDING);

    assertThat(sortedListByNameAscending).containsExactly(list1, list2, list3, list4, list5);
    assertThat(sortedListByNameDescending).containsExactly(list5, list4, list3, list2, list1);
    assertThat(sortedListByLastViewedAscending).containsExactly(list3, list2, list1, list5, list4);
    assertThat(sortedListByLastViewedDescending).containsExactly(list5, list1, list2, list3, list4);
    assertThat(sortedListByLastModifiedAscending).containsExactly(list2, list1, list3, list4, list5);
    assertThat(sortedListByLastModifiedDescending).containsExactly(list4, list3, list1, list2, list5);
  }

  private com.linkedin.saleslist.List createMockList(String listName, Long lastModifiedAt, Long lastViewedAt) {
    return createMockList(listName)
        .setLastModifiedAt(lastModifiedAt, SetMode.IGNORE_NULL)
        .setLastViewedAt(lastViewedAt, SetMode.IGNORE_NULL);
  }

  private com.linkedin.saleslist.List createMockList(String listName) {
    com.linkedin.saleslist.List list = new com.linkedin.saleslist.List();
    list.setCreator(new SeatUrn(SEAT_ID))
        .setListType(com.linkedin.saleslist.ListType.LEAD)
        .setListSource(ListSource.MANUAL)
        .setName(listName);
    return list;
  }

  private List createMockEspressoList(Long lastViewedTime) {
    List list = new List();
    list.setListType(ListType.ACCOUNT_MAP);
    list.setCreatorSeatId(SEAT_ID);
    list.setContractId(CONTRACT_ID);
    list.setName("ACCOUNT MAP");
    list.setLastModifiedTime(123L);
    list.setLastViewedTime(lastViewedTime);
    return list;
  }

  private java.util.List<Pair<Long, SeatToListView>> createSeatToListView(int num) {
    return createSeatToListView(0, num);
  }

  private java.util.List<Pair<Long, SeatToListView>> createSeatToListView(int offset, int count) {
    java.util.List<Pair<Long, SeatToListView>> result = new ArrayList<>();
    for (int i=offset; i<offset+count; i++) {
      SeatToListView seatToListView = new SeatToListView();
      seatToListView.lastViewedTime = (long)i;
      seatToListView.createdTime = i;
      seatToListView.lastModifiedTime = i;
      seatToListView.contractId = CONTRACT_ID;
      seatToListView.name = i + "";
      result.add(new Pair<>((long) i, seatToListView));
    }
    return result;
  }

  private java.util.List<Long> createListIds(int num) {
    java.util.List<Long> result = new ArrayList<>();
    for (int i=0; i<num; i++) {
      result.add((long)i);
    }
    return result;
  }

  /**
   * Build mock data and mock the service calls needed for getListBySeat method
   * @param ownedListCount the lists index from 0 to ownedListCount-1 are lists owned by the requester
   * @param sharedWithListCount the lists index from ownedListCount to ownedListCount+sharedWithListCount are the lists shared with the requester
   * @param sharedByListCount the lists index from 0 to sharedByListCount-1 are lists shared by the requester
   * @param editableByListCount the lists from 0 to editableByListCount-1 are lists editable (owned + shared with writer permission) by the requester
   */
  private void mockListDBForGetListFromSeatForListView(int ownedListCount, int sharedWithListCount,
      int sharedByListCount, @Nullable Integer editableByListCount, @Nullable Boolean sharingAcrossContract) {

    java.util.List<Pair<Long, SeatToListView>> ownedSeatListPairs = createSeatToListView(ownedListCount);
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.MANUAL, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(ownedListCount, ownedSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.ACCOUNT, com.linkedin.sales.espresso.ListSource.MANUAL, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(ownedListCount, ownedSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.ACCOUNT_MAP, com.linkedin.sales.espresso.ListSource.MANUAL, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(ownedListCount, ownedSeatListPairs)));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.CRM_SYNC, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(0, Collections.emptyList())));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD,
        com.linkedin.sales.espresso.ListSource.LINKEDIN_SALES_INSIGHTS, 0, GET_COUNT_LIMIT)).thenReturn(
        Task.value(new Pair<>(0, Collections.emptyList())));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, com.linkedin.sales.espresso.ListSource.SYSTEM, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(0, Collections.emptyList())));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.ACCOUNT_MAP, com.linkedin.sales.espresso.ListSource.CRM_SYNC, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(0, Collections.emptyList())));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.ACCOUNT_MAP,
        com.linkedin.sales.espresso.ListSource.LINKEDIN_SALES_INSIGHTS, 0, GET_COUNT_LIMIT)).thenReturn(
        Task.value(new Pair<>(0, Collections.emptyList())));
    when(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.ACCOUNT_MAP, com.linkedin.sales.espresso.ListSource.SYSTEM, 0,
        GET_COUNT_LIMIT)).thenReturn(Task.value(new Pair<>(0, Collections.emptyList())));
    mockListDBForGetListHelper(ownedListCount, sharedWithListCount, sharedByListCount, editableByListCount,
        sharingAcrossContract);
  }

  private void mockListDBForGetListHelper(int ownedListCount, int sharedWithListCount, int sharedByListCount,
      @Nullable Integer editableByListCount, @Nullable Boolean sharedAcrossContract) {
    if (editableByListCount == null) {
      editableByListCount = ownedListCount + sharedWithListCount;
    }
    java.util.List<Pair<Urn, SubjectPolicy>> sharedWithListUrnWithPolicyPairs = new ArrayList<>();
    java.util.List<Pair<Urn, SubjectPolicy>> editableByListUrnWithPolicyPairs = new ArrayList<>();
    Map<Urn, Integer> resourceIdToIsSavedMap = new HashMap<>();

    for (int i = ownedListCount; i < ownedListCount + sharedWithListCount; i++) {
      sharedWithListUrnWithPolicyPairs.add(
          new Pair<>(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, i), new SubjectPolicy()));
      if (i < editableByListCount) {
        editableByListUrnWithPolicyPairs.add(
            new Pair<>(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, i), new SubjectPolicy()));
      }
    }
    for (int i = 0; i < ownedListCount; i++) {
      if (i < sharedByListCount) {
        resourceIdToIsSavedMap.put(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, i), 2);
      } else {
        resourceIdToIsSavedMap.put(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, i), 0);
      }
    }
    when(_lssSharingDB.batchGetResourceSharingPolicyTotal(any(), any())).thenReturn(Task.value(resourceIdToIsSavedMap));
    when(_lssSharingDB.getPoliciesBySubject(eq(new SeatUrn(SEAT_ID)), any(),
        eq(Sets.newHashSet(ShareRole.READER, ShareRole.WRITER)), eq(0),
        eq(SHARING_POLICY_GET_ALL_COUNT))).thenReturn(
        Task.value(PaginatedList.createForPage(sharedWithListUrnWithPolicyPairs, 0, GET_COUNT_LIMIT, sharedWithListCount)));
    when(_lssSharingDB.getPoliciesBySubject(eq(new SeatUrn(SEAT_ID)), any(),
        eq(Sets.newHashSet(ShareRole.WRITER)), eq(0), eq(SHARING_POLICY_GET_ALL_COUNT))).thenReturn(Task.value(
        PaginatedList.createForPage(editableByListUrnWithPolicyPairs, 0, SHARING_POLICY_GET_ALL_COUNT, editableByListCount - ownedListCount)));
    for (int i = 0; i < ownedListCount; i++) {
      List list = new List();
      list.name = String.valueOf(i);
      list.listType = ListType.LEAD;
      list.creatorSeatId = SEAT_ID;
      list.lastModifiedTime = i;
      list.listSource = com.linkedin.sales.espresso.ListSource.MANUAL;
      when(_lssListDB.getList(i)).thenReturn(Task.value(list));
      when(_lssListDB.getListEntityCount(i)).thenReturn(Task.value((long) i));
    }

    for (int i = ownedListCount; i < ownedListCount + sharedWithListCount; i++) {
      List list = new List();
      list.name = String.valueOf(i);
      list.listType = ListType.LEAD;
      list.creatorSeatId = SEAT_ID;
      list.lastModifiedTime = i;
      list.listSource = com.linkedin.sales.espresso.ListSource.MANUAL;
      when(_lssListDB.getList(i)).thenReturn(Task.value(list));
      when(_lssListDB.getListEntityCount(i)).thenReturn(Task.value((long) i));
    }
  }
}
