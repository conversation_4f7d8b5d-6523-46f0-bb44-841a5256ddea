package com.linkedin.sales.service.list;

import com.linkedin.common.SeatUrnArray;
import com.linkedin.common.TimeRange;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.urn.SalesListEntityPlaceholderUrn;
import com.linkedin.sales.client.realtime.RealtimeDispatcherClient;
import com.linkedin.sales.ds.db.LssListDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.espresso.ChangeTypes;
import com.linkedin.sales.espresso.ResourcePolicyView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.saleslist.CollaboratorChangeValue;
import com.linkedin.saleslist.LeadBodyTextChangeValue;
import com.linkedin.saleslist.LeadChangeValue;
import com.linkedin.saleslist.LeadManagerEntityUrn;
import com.linkedin.saleslist.LeadPositionInMapChangeValue;
import com.linkedin.saleslist.ManagerChangeValue;
import com.linkedin.saleslist.MapNameChangeValue;
import com.linkedin.saleslist.OwnerChangeValue;
import com.linkedin.saleslist.RelationshipMapChangeLog;
import com.linkedin.saleslist.RelationshipMapChangeLogFieldType;
import com.linkedin.saleslist.RelationshipMapChangeLogOperationType;
import com.linkedin.saleslist.RelationshipMapChangeLogValue;
import com.linkedin.saleslist.RelationshipMapEntity;
import com.linkedin.saleslist.RelationshipStrengthChangeValue;
import com.linkedin.saleslist.RelationshipStrengthType;
import com.linkedin.saleslist.RoleType;
import com.linkedin.saleslist.RoleTypeChangeValue;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.util.collections.list.PaginatedList;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SalesRelationshipMapChangeLogServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final long LIST_ID = 1L;
  private static final long CHANGE_LOG_ID = 1001L;
  private static final SalesListUrn LIST_URN = UrnUtils.createSalesListUrn(LIST_ID);
  private static final long SEAT_ID = 1000L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(2000L);
  private static final SeatUrn SEAT_URN_3 = new SeatUrn(3000L);
  private static final SeatUrn SEAT_URN_4 = new SeatUrn(4000L);
  private static final SeatUrn SEAT_URN_5 = new SeatUrn(5000L);
  private static final MemberUrn DUMMY_MEMBER_URN = new MemberUrn(0L);
  private static final MemberUrn MEMBER_URN_1 = new MemberUrn(10L);
  private static final MemberUrn MEMBER_URN_2 = new MemberUrn(20L);
  private static final MemberUrn MEMBER_URN_3 = new MemberUrn(30L);
  private static final SalesListEntityPlaceholderUrn SALES_LIST_ENTITY_PLACEHOLDER_URN_1 =
      new SalesListEntityPlaceholderUrn(LIST_URN, 11L);
  private static final SalesListEntityPlaceholderUrn SALES_LIST_ENTITY_PLACEHOLDER_URN_2 =
      new SalesListEntityPlaceholderUrn(LIST_URN, 21L);

  private static final long TIME_15_DAYS_BEFORE = Instant.now().minus(15, ChronoUnit.DAYS).toEpochMilli();
  private static final long TIME_30_DAYS_BEFORE = Instant.now().minus(45, ChronoUnit.DAYS).toEpochMilli();
  private static final long TIME_45_DAYS_BEFORE = Instant.now().minus(45, ChronoUnit.DAYS).toEpochMilli();
  private static final long TIME_5_DAYS_BEFORE = Instant.now().minus(5, ChronoUnit.DAYS).toEpochMilli();

  @Mock
  private LssListDB _lssListDB;
  @Mock
  private LssSharingDB _lssSharingDB;
  @Mock
  private AclServiceDispatcher _aclServiceDispatcher;
  @Mock
  private RealtimeDispatcherClient _realtimeDispatcherClient;
  @Mock
  private LixService _lixService;

  private SalesRelationshipMapChangeLogService _salesRelationshipMapChangeLogService;

  @BeforeMethod
  public void setup() {
    MockitoAnnotations.openMocks(this);
    _salesRelationshipMapChangeLogService =
        new SalesRelationshipMapChangeLogService(_lssListDB, _lssSharingDB, _aclServiceDispatcher,
            _realtimeDispatcherClient, _lixService);
    when(_realtimeDispatcherClient.publishEvent(any(), any(), eq(null))).thenReturn(
        Task.value(HttpStatus.S_201_CREATED));
    //Change Access Decision.
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.ALLOWED));
    when(_lixService.isEEPLixEnabledForSeat(any(), any())).thenReturn(Task.value(Boolean.TRUE));
  }

  @Test(dataProvider = "createEspressoAndRestliValidChangeLogs")
  public void testCreateChangeLogSuccess(Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog> espressoChangeLog,
      RelationshipMapChangeLog restliChangeLog) {
    when(_lssListDB.createRelationshipMapChangeLog(anyLong(), eq(espressoChangeLog.getSecond()))).thenReturn(
        Task.value(espressoChangeLog.getFirst()));
    when(_realtimeDispatcherClient.publishEvent(any(), any(), eq(null))).thenReturn(Task.value(HttpStatus.S_202_ACCEPTED));
    //Id is read only field.
    restliChangeLog.removeId();
    assertThat(await(_salesRelationshipMapChangeLogService.createChangeLog(restliChangeLog, CONTRACT_URN))).isEqualTo(
        HttpStatus.S_201_CREATED);
    verify(_realtimeDispatcherClient, times(1)).publishEvent(any(), any(), eq(null));
  }

  @Test
  public void testCreateChangeLogSuccessWithFilteringPlaceholderCardForPublish() {
    RelationshipMapChangeLog restliChangeLog = new RelationshipMapChangeLog();
    restliChangeLog.setPerformedAt(100L)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
        .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
        .setActorSeat(SEAT_URN)
        .setRelationshipMap(LIST_URN)
        .setChangeValue(RelationshipMapChangeLogValue.createWithLead(new LeadChangeValue().setPreviousValueEntity(
            RelationshipMapEntity.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))));
    when(_lssListDB.createRelationshipMapChangeLog(anyLong(), any())).thenReturn(
        Task.value(CHANGE_LOG_ID));
    when(_lixService.isEEPLixEnabledForSeat(any(), any())).thenReturn(Task.value(Boolean.FALSE));
    assertThat(await(_salesRelationshipMapChangeLogService.createChangeLog(restliChangeLog, CONTRACT_URN))).isEqualTo(
        HttpStatus.S_201_CREATED);
    verify(_realtimeDispatcherClient, times(0)).publishEvent(any(), any(), eq(null));
  }
  @Test(dataProvider = "createEspressoAndRestliValidChangeLogs")
  public void testFindChangeLogSuccess(Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog> espressoChangeLog,
      RelationshipMapChangeLog restliChangeLog) {
    TimeRange timeRange = new TimeRange();

    int total = 1;
    Pair<Integer, List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>>> mockEspressoRes =
        Pair.make(total, Collections.singletonList(espressoChangeLog));
    //Change espresso data.
    when(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), anyLong(), anyLong(), anyInt(),
        anyInt())).thenReturn(Task.value(mockEspressoRes));
    ResourcePolicyView testResourcePolicyView1 = new ResourcePolicyView();
    List<Pair<Urn, ResourcePolicyView>> mockPolicyList = new ArrayList<>();
    mockPolicyList.add(Pair.make(SEAT_URN, testResourcePolicyView1));
    mockPolicyList.add(Pair.make(SEAT_URN_2, testResourcePolicyView1));
    //Change sharing policies.
    when(_lssSharingDB.getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt())).thenReturn(
        Task.value(PaginatedList.createForPage(mockPolicyList, 0, 10, 10)));
    BasicCollectionResult<RelationshipMapChangeLog> res =
        await(_salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, timeRange));
    assertThat(res.getElements().size()).isEqualTo(1);
    assertThat(res.getElements().get(0)).isEqualTo(restliChangeLog);
    verify(_aclServiceDispatcher, times(1)).checkAccessDecision(any(), any(), any(), any(), any());
    verify(_lssSharingDB, times(1)).getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testFindChangeLogSuccess_FilterPlaceholderCard() {
    TimeRange timeRange = new TimeRange();
    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoChangeLog =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString(), null, null);
    Pair<Integer, List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>>> mockEspressoRes =
        Pair.make(1, Collections.singletonList(Pair.make(1L,espressoChangeLog)));
    //Change espresso data.
    when(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), anyLong(), anyLong(), anyInt(),
        anyInt())).thenReturn(Task.value(mockEspressoRes));
    ResourcePolicyView testResourcePolicyView1 = new ResourcePolicyView();
    List<Pair<Urn, ResourcePolicyView>> mockPolicyList = new ArrayList<>();
    mockPolicyList.add(Pair.make(SEAT_URN, testResourcePolicyView1));
    mockPolicyList.add(Pair.make(SEAT_URN_2, testResourcePolicyView1));
    //Change sharing policies.
    when(_lssSharingDB.getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt())).thenReturn(
        Task.value(PaginatedList.createForPage(mockPolicyList, 0, 10, 10)));
    when(_lixService.isEEPLixEnabledForSeat(any(), any())).thenReturn(Task.value(Boolean.FALSE));
    BasicCollectionResult<RelationshipMapChangeLog> res =
        await(_salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, timeRange));
    assertThat(res.getElements().size()).isEqualTo(0);
  }

  @Test
  public void testFindChangeLogFailedWithAclAccess() {
    when(_aclServiceDispatcher.checkAccessDecision(any(), any(), any(), any(), any())).thenReturn(
        Task.value(AccessDecision.DENIED));
    assertThatThrownBy(() -> await(
        _salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, new TimeRange())));
  }

  @Test(dataProvider = "createDataForAclTimeCheck")
  public void testFindChangeLogForSharingPolicy(List<Pair<Urn, ResourcePolicyView>> mockPolicyList,
      long expectedStartTime, long expectedEndTime) {
    //Set expectedStartTime as -1 when default start time is expected.

    List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>> mockReturnList = new ArrayList<>();
    mockReturnList.add(Pair.make(1L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
        null, MEMBER_URN_1.toString(), null)));
    int total = mockReturnList.size();
    Pair<Integer, List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>>> mockEspressoRes =
        Pair.make(total, mockReturnList);

    when(_lssSharingDB.getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt())).thenReturn(
        Task.value(PaginatedList.createForPage(mockPolicyList, 0, 10, 10)));

    when(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), anyLong(), anyLong(),
        anyInt(), anyInt())).thenReturn(Task.value(mockEspressoRes));

    await(_salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, new TimeRange()));

    verify(_lssListDB, times(1)).findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), longThat(argument -> {
      if (expectedStartTime > 0) {
        return argument.equals(expectedStartTime);
      } else {
        return argument > Instant.now().minus(31, ChronoUnit.DAYS).toEpochMilli() && argument < Instant.now()
            .minus(29, ChronoUnit.DAYS)
            .toEpochMilli();
      }
    }), anyLong(), anyInt(), anyInt());
  }

  @Test(description = "Test findChangeLog failed with less then required sharing policies.")
  public void testFindChangeLogForSharingPolicyFailed(){
    int total = 0;
    Pair<Integer, List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>>> mockEspressoRes =
        Pair.make(total, Collections.emptyList());

    List<Pair<Urn, ResourcePolicyView>> mockPolicyList = new ArrayList<>();

    when(_lssSharingDB.getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt())).thenReturn(
        Task.value(PaginatedList.createForPage(mockPolicyList, 0, 10, 10)));

    when(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), eq(Long.MAX_VALUE), anyLong(),
        anyInt(), anyInt())).thenReturn(Task.value(mockEspressoRes));
    BasicCollectionResult<RelationshipMapChangeLog> res =
        await(_salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, new TimeRange()));
    assertThat(res.getElements().size()).isEqualTo(0);
  }

  @Test(description = "Where espresso record fails to decorate restli record.", dataProvider = "createInvalidEspressoRecords")
  public void testFindChangeLogFailedInEspressoDeco(
      Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog> espressoChangeLog) {
    TimeRange timeRange = new TimeRange();

    List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>> mockReturnList = new ArrayList<>();
    mockReturnList.add(espressoChangeLog);
    int total = mockReturnList.size();
    Pair<Integer, List<Pair<Long, com.linkedin.sales.espresso.RelationshipMapChangeLog>>> mockEspressoRes =
        Pair.make(total, mockReturnList);
    //Change espresso data.
    when(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(anyLong(), anyLong(), anyLong(), anyInt(),
        anyInt())).thenReturn(Task.value(mockEspressoRes));
    ResourcePolicyView testResourcePolicyView1 = new ResourcePolicyView();
    List<Pair<Urn, ResourcePolicyView>> mockPolicyList = new ArrayList<>();
    mockPolicyList.add(Pair.make(SEAT_URN, testResourcePolicyView1));
    mockPolicyList.add(Pair.make(SEAT_URN_2, testResourcePolicyView1));
    //Change sharing policies.
    when(_lssSharingDB.getPolicyViewsByResource(any(), any(), any(), anyInt(), anyInt())).thenReturn(
        Task.value(PaginatedList.createForPage(mockPolicyList, 0, 10, 10)));
    BasicCollectionResult<RelationshipMapChangeLog> res =
        await(_salesRelationshipMapChangeLogService.findByRelationshipMap(LIST_URN, SEAT_URN, 0, 10, timeRange));
    assertThat(res.getElements().size()).isEqualTo(0);
  }

  @Test
  public void testCreateChangLogDBFailure() {
    RelationshipMapChangeLog changeLog = new RelationshipMapChangeLog();
    changeLog.setPerformedAt(100L)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
        .setOperationType(RelationshipMapChangeLogOperationType.ADD)
        .setActorSeat(SEAT_URN)
        .setRelationshipMap(LIST_URN)
        .setChangeValue(
            RelationshipMapChangeLogValue.createWithLead(new LeadChangeValue().setCurrentValue(new MemberUrn(10L))));
    when(_lssListDB.createRelationshipMapChangeLog(anyLong(), any())).thenReturn(
        Task.failure(new RestLiServiceException(HttpStatus.S_412_PRECONDITION_FAILED)));
    assertThatThrownBy(
        () -> await(_salesRelationshipMapChangeLogService.createChangeLog(changeLog, CONTRACT_URN)));
    verify(_realtimeDispatcherClient, times(0)).publishEvent(any(), any(), eq(null));
  }

  @Test(dataProvider = "createChangeLogObjectWithInvalidChangeFieldTypeDataProvider")
  public void testInvalidChangeLogFormatForFieldType(RelationshipMapChangeLog changeLog) {
    when(_lssListDB.createRelationshipMapChangeLog(anyLong(), any())).thenReturn(Task.value(CHANGE_LOG_ID));
    assertThatExceptionOfType(RestLiServiceException.class).isThrownBy(
        () -> await(_salesRelationshipMapChangeLogService.createChangeLog(changeLog, CONTRACT_URN)));
  }

  @Test(dataProvider = "createChangeLogObjectWithInvalidOperationTypeDataProvider")
  public void testInvalidChangeLogFormatForOperationType(RelationshipMapChangeLog changeLog) {
    when(_lssListDB.createRelationshipMapChangeLog(anyLong(), any())).thenReturn(Task.value(CHANGE_LOG_ID));
    assertThatExceptionOfType(RestLiServiceException.class).isThrownBy(
        () -> await(_salesRelationshipMapChangeLogService.createChangeLog(changeLog, CONTRACT_URN)));
  }

  @DataProvider
  public static Object[][] createDataForAclTimeCheck() {
    ResourcePolicyView testResourceOwnerPolicyView = new ResourcePolicyView();
    testResourceOwnerPolicyView.role = ShareRole.OWNER;

    ResourcePolicyView testResourceWriterPolicyViewWithValidTime = new ResourcePolicyView();
    testResourceWriterPolicyViewWithValidTime.role = ShareRole.WRITER;
    testResourceWriterPolicyViewWithValidTime.acceptedTime = TIME_15_DAYS_BEFORE;

    ResourcePolicyView testResourceWriterPolicyViewWithValidTime2 = new ResourcePolicyView();
    testResourceWriterPolicyViewWithValidTime2.role = ShareRole.WRITER;
    testResourceWriterPolicyViewWithValidTime2.acceptedTime = TIME_5_DAYS_BEFORE;

    ResourcePolicyView testResourceWriterPolicyViewWithNoTime = new ResourcePolicyView();
    testResourceWriterPolicyViewWithNoTime.role = ShareRole.WRITER;

    ResourcePolicyView testResourceWriterPolicyViewWithInValidTime = new ResourcePolicyView();
    testResourceWriterPolicyViewWithInValidTime.role = ShareRole.WRITER;
    testResourceWriterPolicyViewWithInValidTime.acceptedTime = TIME_45_DAYS_BEFORE;

    ArrayList<Pair<Urn, ResourcePolicyView>> userIsOwnerWithNoTime = new ArrayList<>(
        Arrays.asList(Pair.make(SEAT_URN, testResourceOwnerPolicyView),
            Pair.make(SEAT_URN_2, testResourceWriterPolicyViewWithValidTime)));

    ArrayList<Pair<Urn, ResourcePolicyView>> userIsWriterWithNoTime = new ArrayList<>(
        Arrays.asList(Pair.make(SEAT_URN_2, testResourceOwnerPolicyView),
            Pair.make(SEAT_URN, testResourceWriterPolicyViewWithNoTime)));

    ArrayList<Pair<Urn, ResourcePolicyView>> userIsWriterWithValidTime = new ArrayList<>(
        Arrays.asList(Pair.make(SEAT_URN_2, testResourceOwnerPolicyView),
            Pair.make(SEAT_URN, testResourceWriterPolicyViewWithValidTime)));

    ArrayList<Pair<Urn, ResourcePolicyView>> userIsWriterWithMultipleWriters = new ArrayList<>(
        Arrays.asList(Pair.make(SEAT_URN_2, testResourceOwnerPolicyView),
            Pair.make(SEAT_URN_3, testResourceWriterPolicyViewWithValidTime2),
            Pair.make(SEAT_URN, testResourceWriterPolicyViewWithValidTime)));

    ArrayList<Pair<Urn, ResourcePolicyView>> userIsWriterWithMultipleWritersNoTime = new ArrayList<>(
        Arrays.asList(Pair.make(SEAT_URN_2, testResourceOwnerPolicyView),
            Pair.make(SEAT_URN_3, testResourceWriterPolicyViewWithNoTime),
            Pair.make(SEAT_URN, testResourceWriterPolicyViewWithValidTime)));

    return new Object[][]{new Object[]{userIsOwnerWithNoTime, TIME_15_DAYS_BEFORE, Long.MAX_VALUE},
        new Object[]{userIsWriterWithNoTime, -1, Long.MAX_VALUE},
        new Object[]{userIsWriterWithValidTime, TIME_15_DAYS_BEFORE, Long.MAX_VALUE},
        new Object[]{userIsWriterWithValidTime, TIME_15_DAYS_BEFORE, Long.MAX_VALUE},
        new Object[]{userIsWriterWithMultipleWriters, TIME_15_DAYS_BEFORE, Long.MAX_VALUE},
        new Object[]{userIsWriterWithMultipleWritersNoTime, TIME_15_DAYS_BEFORE, Long.MAX_VALUE}};
  }

  @DataProvider
  public static Object[][] createInvalidEspressoRecords() {
    return new Object[][]{new Object[]{
        Pair.make(1L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            null, null, null))}, new Object[]{
        Pair.make(2L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_ROLE, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            null, null, MEMBER_URN_3.toString()))}, new Object[]{
        Pair.make(3L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_ROLE, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            RoleType.CHAMPION.toString(), null, null))}, new Object[]{
        Pair.make(4L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, null, MEMBER_URN_3.toString()))}, new Object[]{
        Pair.make(5L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), RelationshipStrengthType.STRONG.toString(), null, null))}, new Object[]{
        Pair.make(6L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_OWNER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, null, MEMBER_URN_3.toString()))}, new Object[]{
        Pair.make(7L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_OWNER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), SEAT_URN_2.toString(), null, null))}, new Object[]{
        Pair.make(8L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, null, MEMBER_URN_3.toString()))}, new Object[]{
        Pair.make(9L, createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), MEMBER_URN_3.toString(), null, null))}};
  }

  @DataProvider
  public static Object[][] createEspressoAndRestliValidChangeLogs() {
    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddLead =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            MEMBER_URN_1.toString(), null, null);

    RelationshipMapChangeLog restliValidChangeLogAddLead = new RelationshipMapChangeLog();
    restliValidChangeLogAddLead.setPerformedAt(TIME_15_DAYS_BEFORE)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
        .setOperationType(RelationshipMapChangeLogOperationType.ADD)
        .setActorSeat(SEAT_URN)
        .setRelationshipMap(LIST_URN)
        .setChangeValue(
            RelationshipMapChangeLogValue.createWithLead(new LeadChangeValue().setCurrentValue(MEMBER_URN_1).setCurrentValueEntity(
                RelationshipMapEntity.createWithMember(MEMBER_URN_1))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddPlaceholderCardLead =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString(), null, null);

    RelationshipMapChangeLog restliValidChangeLogAddPlaceholderCardLead = new RelationshipMapChangeLog()
        .setPerformedAt(TIME_15_DAYS_BEFORE)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
        .setOperationType(RelationshipMapChangeLogOperationType.ADD)
        .setActorSeat(SEAT_URN)
        .setRelationshipMap(LIST_URN)
        .setChangeValue(
            RelationshipMapChangeLogValue.createWithLead(new LeadChangeValue().setCurrentValue(DUMMY_MEMBER_URN).setCurrentValueEntity(
                RelationshipMapEntity.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveLead =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            null, MEMBER_URN_1.toString(), null);

    RelationshipMapChangeLog restliValidChangeLogRemoveLead =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLead(
                new LeadChangeValue().setPreviousValue(MEMBER_URN_1)
                    .setPreviousValueEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_1))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceLead =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.EDIT_LEAD, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            MEMBER_URN_2.toString(), MEMBER_URN_1.toString(), null);

    RelationshipMapChangeLog restliValidChangeLogReplaceLead =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLead(
                new LeadChangeValue().setCurrentValue(MEMBER_URN_2)
                    .setCurrentValueEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_2))
                    .setPreviousValue(MEMBER_URN_1)
                    .setPreviousValueEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_1))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddManager =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), MEMBER_URN_2.toString(), null, MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogAddManager =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithManager(
                new ManagerChangeValue().setCurrentValue(MEMBER_URN_2)
                    .setCurrentValueEntity(LeadManagerEntityUrn.createWithMember(MEMBER_URN_2))
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddPlaceholderCardManager =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString(), null,
            SALES_LIST_ENTITY_PLACEHOLDER_URN_2.toString());

    RelationshipMapChangeLog restliValidChangeLogAddPlaceholderCardManager =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithManager(
                new ManagerChangeValue().setCurrentValue(DUMMY_MEMBER_URN)
                    .setCurrentValueEntity(
                        LeadManagerEntityUrn.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))
                    .setTargetLead(DUMMY_MEMBER_URN)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithSalesListEntityPlaceholder(
                        SALES_LIST_ENTITY_PLACEHOLDER_URN_2))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceManager =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), MEMBER_URN_2.toString(), MEMBER_URN_1.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogReplaceManager =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithManager(
                new ManagerChangeValue().setCurrentValue(MEMBER_URN_2)
                    .setCurrentValueEntity(LeadManagerEntityUrn.createWithMember(MEMBER_URN_2))
                    .setPreviousValue(MEMBER_URN_1)
                    .setPreviousValueEntity(LeadManagerEntityUrn.createWithMember(MEMBER_URN_1))
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveManager =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MANAGER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, MEMBER_URN_1.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogRemoveManager =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithManager(
                new ManagerChangeValue()
                    .setPreviousValue(MEMBER_URN_1)
                    .setPreviousValueEntity(LeadManagerEntityUrn.createWithMember(MEMBER_URN_1))
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddOwner =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_OWNER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), SEAT_URN_2.toString(), null, MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogAddOwner =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithOwner(
                new OwnerChangeValue()
                    .setCurrentValue(SEAT_URN_2)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveOwner =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_OWNER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, SEAT_URN_3.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogRemoveOwner =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithOwner(
                new OwnerChangeValue().setPreviousValue(SEAT_URN_3)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceOwner =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_OWNER, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), SEAT_URN_2.toString(), SEAT_URN_3.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogReplaceOwner =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithOwner(
                new OwnerChangeValue().setCurrentValue(SEAT_URN_2)
                    .setPreviousValue(SEAT_URN_3)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddRole =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_ROLE, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            RoleType.CHAMPION.toString(), null, MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogAddRole =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRoleType(
                new RoleTypeChangeValue().setCurrentValue(RoleType.CHAMPION)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveRole =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_ROLE, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            null, RoleType.DECISION_MAKER.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogRemoveRole =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRoleType(
                new RoleTypeChangeValue().setPreviousValue(RoleType.DECISION_MAKER)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceRole =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_ROLE, TIME_15_DAYS_BEFORE, CONTRACT_URN.toString(),
            RoleType.CHAMPION.toString(), RoleType.DECISION_MAKER.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogReplaceRole =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRoleType(
                new RoleTypeChangeValue().setCurrentValue(RoleType.CHAMPION)
                    .setPreviousValue(RoleType.DECISION_MAKER)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddStrength =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), RelationshipStrengthType.STRONG.toString(), null, MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogAddStrength =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRelationshipStrength(
                new RelationshipStrengthChangeValue().setCurrentValue(RelationshipStrengthType.STRONG)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveStrength =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, RelationshipStrengthType.MEDIUM.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogRemoveStrength =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRelationshipStrength(
                new RelationshipStrengthChangeValue().setPreviousValue(RelationshipStrengthType.MEDIUM)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceStrength =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_RELATIONSHIP_STRENGTH, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), RelationshipStrengthType.STRONG.toString(),
            RelationshipStrengthType.MEDIUM.toString(), MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogReplaceStrength =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithRelationshipStrength(
                new RelationshipStrengthChangeValue().setCurrentValue(RelationshipStrengthType.STRONG)
                    .setPreviousValue(RelationshipStrengthType.MEDIUM)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRenameMap =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MAP_NAME, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), "NEW_NAME",
            "OLD_NAME", null);

    RelationshipMapChangeLog restliValidChangeLogRenameMap =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_NAME)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithMapName(
                new MapNameChangeValue().setCurrentValue("NEW_NAME").setPreviousValue("OLD_NAME")));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogLeadPositionInMapAdd =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_LEAD_POSITION_IN_MAP, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), "1",
            null, MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogLeadPositionInMapAdd =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_POSITION_IN_MAP)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLeadPositionInMap(
                new LeadPositionInMapChangeValue().setCurrentValue(1)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogLeadPositionInMapReplace =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_LEAD_POSITION_IN_MAP, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), "2",
            "1", MEMBER_URN_3.toString());

    RelationshipMapChangeLog restliValidChangeLogLeadPositionInMapReplace =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_POSITION_IN_MAP)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLeadPositionInMap(
                new LeadPositionInMapChangeValue().setCurrentValue(2).setPreviousValue(1)
                    .setTargetLead(MEMBER_URN_3)
                    .setTargetLeadEntity(RelationshipMapEntity.createWithMember(MEMBER_URN_3))
            ));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogCollaboratorAdd =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MAP_COLLABORATORS, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null,
            null, null, Arrays.asList(SEAT_URN.toString(), SEAT_URN_2.toString()), null);

    RelationshipMapChangeLog restliValidChangeLogCollaboratorAdd =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_COLLABORATORS)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithMapCollaborators(
                new CollaboratorChangeValue().setCurrentValuesAdded(new SeatUrnArray(Arrays.asList(SEAT_URN, SEAT_URN_2)))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogCollaboratorRemove =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MAP_COLLABORATORS, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null,
            null, null, null, Arrays.asList(SEAT_URN_3.toString(), SEAT_URN_4.toString()));

    RelationshipMapChangeLog restliValidChangeLogCollaboratorRemove =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_COLLABORATORS)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithMapCollaborators(
                new CollaboratorChangeValue().setPreviousValuesRemoved(new SeatUrnArray(Arrays.asList(SEAT_URN_3, SEAT_URN_4)))));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogCollaboratorReplace =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_MAP_COLLABORATORS, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null,
            null, null, Arrays.asList(SEAT_URN.toString(), SEAT_URN_2.toString()),  Arrays.asList(SEAT_URN_3.toString(), SEAT_URN_4.toString()));

    RelationshipMapChangeLog restliValidChangeLogCollaboratorReplace =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.MAP_COLLABORATORS)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithMapCollaborators(
                new CollaboratorChangeValue().setCurrentValuesAdded(
                        new SeatUrnArray(Arrays.asList(SEAT_URN, SEAT_URN_2)))
                    .setPreviousValuesRemoved(new SeatUrnArray(Arrays.asList(SEAT_URN_3, SEAT_URN_4)))));

    String leadBodyTextCurrentVal = "New text";
    String leadBodyTextPreviousVal = "Old text";
    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogAddLeadText =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_LEAD_TEXT, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), leadBodyTextCurrentVal, null, SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString());

    RelationshipMapChangeLog restliValidChangeLogAddLeadText =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_BODY_TEXT)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLeadBodyText(
                new LeadBodyTextChangeValue().setTargetLeadEntity(
                        RelationshipMapEntity.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))
                    .setCurrentValue(leadBodyTextCurrentVal)));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogReplaceLeadText =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_LEAD_TEXT, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), leadBodyTextCurrentVal, leadBodyTextPreviousVal,
            SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString());

    RelationshipMapChangeLog restliValidChangeLogReplaceLeadText =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_BODY_TEXT)
            .setOperationType(RelationshipMapChangeLogOperationType.REPLACE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLeadBodyText(
                new LeadBodyTextChangeValue().setTargetLeadEntity(
                        RelationshipMapEntity.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))
                    .setCurrentValue(leadBodyTextCurrentVal)
                    .setPreviousValue(leadBodyTextPreviousVal)));

    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoValidChangeLogRemoveLeadText =
        createEspressoRecord(SEAT_URN.toString(), ChangeTypes.UPDATE_LEAD_TEXT, TIME_15_DAYS_BEFORE,
            CONTRACT_URN.toString(), null, leadBodyTextPreviousVal, SALES_LIST_ENTITY_PLACEHOLDER_URN_1.toString());

    RelationshipMapChangeLog restliValidChangeLogRemoveLeadText =
        new RelationshipMapChangeLog().setPerformedAt(TIME_15_DAYS_BEFORE)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD_BODY_TEXT)
            .setOperationType(RelationshipMapChangeLogOperationType.REMOVE)
            .setActorSeat(SEAT_URN)
            .setRelationshipMap(LIST_URN)
            .setChangeValue(RelationshipMapChangeLogValue.createWithLeadBodyText(
                new LeadBodyTextChangeValue().setTargetLeadEntity(
                        RelationshipMapEntity.createWithSalesListEntityPlaceholder(SALES_LIST_ENTITY_PLACEHOLDER_URN_1))
                    .setPreviousValue(leadBodyTextPreviousVal)));

    return new Object[][]{new Object[]{Pair.make(1L,espressoValidChangeLogAddLead), restliValidChangeLogAddLead.setId(1L)},
        new Object[]{Pair.make(2L, espressoValidChangeLogRemoveLead), restliValidChangeLogRemoveLead.setId(2L)},
        new Object[]{Pair.make(3L, espressoValidChangeLogReplaceLead), restliValidChangeLogReplaceLead.setId(3L)},
        new Object[]{Pair.make(4L, espressoValidChangeLogAddManager), restliValidChangeLogAddManager.setId(4L)},
        new Object[]{Pair.make(5L, espressoValidChangeLogRemoveManager), restliValidChangeLogRemoveManager.setId(5L)},
        new Object[]{Pair.make(6L, espressoValidChangeLogReplaceManager), restliValidChangeLogReplaceManager.setId(6L)},
        new Object[]{Pair.make(7L, espressoValidChangeLogAddOwner), restliValidChangeLogAddOwner.setId(7L)},
        new Object[]{Pair.make(8L, espressoValidChangeLogRemoveOwner), restliValidChangeLogRemoveOwner.setId(8L)},
        new Object[]{Pair.make(9L, espressoValidChangeLogReplaceOwner), restliValidChangeLogReplaceOwner.setId(9L)},
        new Object[]{Pair.make(10L, espressoValidChangeLogAddRole), restliValidChangeLogAddRole.setId(10L)},
        new Object[]{Pair.make(11L, espressoValidChangeLogRemoveRole), restliValidChangeLogRemoveRole.setId(11L)},
        new Object[]{Pair.make(12L, espressoValidChangeLogReplaceRole), restliValidChangeLogReplaceRole.setId(12L)},
        new Object[]{Pair.make(13L, espressoValidChangeLogAddStrength), restliValidChangeLogAddStrength.setId(13L)},
        new Object[]{Pair.make(14L, espressoValidChangeLogRemoveStrength), restliValidChangeLogRemoveStrength.setId(14L)},
        new Object[]{Pair.make(15L, espressoValidChangeLogReplaceStrength), restliValidChangeLogReplaceStrength.setId(15L)},
        new Object[]{Pair.make(16L, espressoValidChangeLogRenameMap), restliValidChangeLogRenameMap.setId(16L)},
        new Object[]{Pair.make(17L, espressoValidChangeLogLeadPositionInMapAdd), restliValidChangeLogLeadPositionInMapAdd.setId(17L)},
        new Object[]{Pair.make(18L, espressoValidChangeLogLeadPositionInMapReplace), restliValidChangeLogLeadPositionInMapReplace.setId(18L)},
        new Object[]{Pair.make(19L, espressoValidChangeLogCollaboratorAdd), restliValidChangeLogCollaboratorAdd.setId(19L)},
        new Object[]{Pair.make(20L, espressoValidChangeLogCollaboratorRemove), restliValidChangeLogCollaboratorRemove.setId(20L)},
        new Object[]{Pair.make(21L, espressoValidChangeLogCollaboratorReplace), restliValidChangeLogCollaboratorReplace.setId(21L)},
        new Object[]{Pair.make(22L, espressoValidChangeLogAddPlaceholderCardLead), restliValidChangeLogAddPlaceholderCardLead.setId(22L)},
        new Object[]{Pair.make(23L, espressoValidChangeLogAddPlaceholderCardManager), restliValidChangeLogAddPlaceholderCardManager.setId(23L)},
        new Object[]{Pair.make(24L, espressoValidChangeLogAddLeadText), restliValidChangeLogAddLeadText.setId(24L)},
        new Object[]{Pair.make(25L, espressoValidChangeLogRemoveLeadText), restliValidChangeLogRemoveLeadText.setId(25L)},
        new Object[]{Pair.make(26L, espressoValidChangeLogReplaceLeadText), restliValidChangeLogReplaceLeadText.setId(26L)}
    };
  }

  @DataProvider
  public static Object[][] createChangeLogObjectWithInvalidChangeFieldTypeDataProvider() {
    LeadChangeValue leadChangeValue = new LeadChangeValue();

    ManagerChangeValue managerChangeValue = new ManagerChangeValue();

    return new Object[][]{new Object[]{new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
        .setActorSeat(SEAT_URN)
        .setOperationType(RelationshipMapChangeLogOperationType.ADD)
        .setPerformedAt(100L)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER).setChangeValue(
        RelationshipMapChangeLogValue.createWithLead(leadChangeValue))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD).setChangeValue(
            RelationshipMapChangeLogValue.createWithManager(managerChangeValue))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH).setChangeValue(
            RelationshipMapChangeLogValue.createWithManager(managerChangeValue))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE).setChangeValue(
            RelationshipMapChangeLogValue.createWithManager(managerChangeValue))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER).setChangeValue(
            RelationshipMapChangeLogValue.createWithManager(managerChangeValue))}};
  }

  @DataProvider
  public static Object[][] createChangeLogObjectWithInvalidOperationTypeDataProvider() {
    return new Object[][]{new Object[]{new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
        .setActorSeat(SEAT_URN)
        .setOperationType(RelationshipMapChangeLogOperationType.ADD)
        .setPerformedAt(100L)
        .setChangeFieldType(RelationshipMapChangeLogFieldType.MANAGER).setChangeValue(
        RelationshipMapChangeLogValue.createWithManager(new ManagerChangeValue()))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.LEAD).setChangeValue(
            RelationshipMapChangeLogValue.createWithLead(new LeadChangeValue()))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.OWNER).setChangeValue(
            RelationshipMapChangeLogValue.createWithOwner(new OwnerChangeValue()))}, new Object[]{
        new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.RELATIONSHIP_STRENGTH).setChangeValue(
            RelationshipMapChangeLogValue.createWithRelationshipStrength(new RelationshipStrengthChangeValue()))},
        new Object[]{new RelationshipMapChangeLog().setRelationshipMap(LIST_URN)
            .setActorSeat(SEAT_URN)
            .setOperationType(RelationshipMapChangeLogOperationType.ADD)
            .setPerformedAt(100L)
            .setChangeFieldType(RelationshipMapChangeLogFieldType.ROLE).setChangeValue(
            RelationshipMapChangeLogValue.createWithRoleType(new RoleTypeChangeValue()))}};
  }

  private static com.linkedin.sales.espresso.RelationshipMapChangeLog createEspressoRecord(String actorSeat,
      ChangeTypes changeTypes, long time, String contractUrn, String changeValue, String previousChangeValue,
      String lead) {
    return createEspressoRecord(actorSeat, changeTypes, time, contractUrn, changeValue, previousChangeValue, lead, null, null);
  }

  private static com.linkedin.sales.espresso.RelationshipMapChangeLog createEspressoRecord(String actorSeat,
      ChangeTypes changeTypes, long time, String contractUrn, String changeValue, String previousChangeValue,
      String lead, List<String> addedValues, List<String> removedValues) {
    com.linkedin.sales.espresso.RelationshipMapChangeLog espressoRecord =
        new com.linkedin.sales.espresso.RelationshipMapChangeLog();

    espressoRecord.setActorSeatUrn(actorSeat);
    espressoRecord.setChangeType(changeTypes);
    espressoRecord.setEventTime(time);
    espressoRecord.setActorContractUrn(contractUrn);
    if (changeValue != null) {
      espressoRecord.setChangeValue(changeValue);
    }
    if (previousChangeValue != null) {
      espressoRecord.setPreviousChangeValue(previousChangeValue);
    }
    if (lead != null) {
      Urn leadUrn = UrnUtils.createUrnFromString(lead);
      if (SalesListEntityPlaceholderUrn.ENTITY_TYPE.equals(leadUrn.getEntityType())) {
        espressoRecord.setTargetSalesListEntityPlaceholderUrn(lead);
      } else {
        espressoRecord.setTargetMemberUrn(lead);
      }
    }
    if (addedValues != null) {
      espressoRecord.setAddedValues(new ArrayList<>(addedValues));
    }
    if (removedValues != null) {
      espressoRecord.setRemovedValues(new ArrayList<>(removedValues));
    }
    return espressoRecord;
  }
}
