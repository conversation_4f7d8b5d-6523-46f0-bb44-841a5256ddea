package com.linkedin.sales.service.note;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.AttributeArray;
import com.linkedin.common.AttributedText;
import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesSharedSearchUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BasicCollectionResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssNoteDB;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.Note;
import com.linkedin.salesnote.NoteKey;
import com.linkedin.util.Pair;
import com.linkedin.util.collections.list.PaginatedList;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;


/**
 * Unit test for note service
 */
public class SalesNoteServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN1 = new ContractUrn(101L);
  private static final ContractUrn CONTRACT_URN2 = new ContractUrn(102L);
  private static final SeatUrn SEAT_URN1 = new SeatUrn(2001L);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(2002L);
  private AttributedText ATTRIBUTED_TEXT = new AttributedText();
  private static final Long NOTE_ID1 = 1L;
  private static final Long NOTE_ID2 = 2L;
  private static final Long RANDOM_ID = 3000000001L;
  private static final MemberUrn MEMBER_URN = new MemberUrn(301L);
  private static final String SERIALIZED_ORGANIZATION_URN = "urn:li:organization:3000";
  private Urn ORGANIZATION_URN;
  private Urn SALES_SHARED_SEARCH_URN;
  private static final String SHARE_SEARCH = "salesSharedSearch";
  private static final Long EXPIRE_AT = 9999L;
  private static final Long CREATED_TIME = System.currentTimeMillis();

  private AnnotatableEntityUrn ENTITY_URN = new AnnotatableEntityUrn();

  private SalesNoteService _salesNoteService;

  @Mock
  private LssNoteDB _lssNoteDB;
  @Mock
  private LssSharingDB _lssSharingDB;

  @BeforeTest(alwaysRun = true)
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    _salesNoteService = new SalesNoteService(_lssNoteDB, _lssSharingDB);
    ATTRIBUTED_TEXT.setText("Default text in note").setAttributes(new AttributeArray());
    ENTITY_URN.setMemberUrn(MEMBER_URN);
    try {
      ORGANIZATION_URN = OrganizationUrn.deserialize(SERIALIZED_ORGANIZATION_URN);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);// Should never happen
    }
    try {
      SALES_SHARED_SEARCH_URN = SalesSharedSearchUrn.createFromUrn(Urn.createFromTuple(SHARE_SEARCH,
          SEAT_URN1, "PEOPLE_SHARE_SEARCH", 601L));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);// Should never happen
    }
  }

  @Test
  public void testCreateNoteSucceed() {
    Mockito.doReturn(Task.value(NOTE_ID1)).when(_lssNoteDB).createNote(any(), any(), any(), any());
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT)
        .setExpireAt(EXPIRE_AT);
    CreateResponse result = await(_salesNoteService.createNote(note));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  /**
   * The test is similar to testCreateNoteSucceed, with no migration ID and ExpireAt.
   */
  public void testCreateNoteSucceedWithNoOptionalFields() {
    Mockito.doReturn(Task.value(NOTE_ID1)).when(_lssNoteDB).createNote(any(), any(), any(), any());
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT);
    CreateResponse result = await(_salesNoteService.createNote(note));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  /**
   * Test verifies that without a required value an exception is thrown. In the test below note does not have any body.
   */
  public void testCreateNoteFailureEmptyFields() {
    Mockito.doReturn(Task.value(NOTE_ID1)).when(_lssNoteDB).createNote(any(), any(), any(), any());
    //Note is created without a body which should trigger a failure
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN);
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesNoteService.createNote(note));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Not all the required fields are present"));
  }

  @Test
  /**
   * Test verifies a note is created successfully when expireAt is set to null
   */
  public void testCreateNoteSucceedExpireAtNull() {
    Mockito.doReturn(Task.value(NOTE_ID1)).when(_lssNoteDB).createNote(any(), any(), any(), any());
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT);
    CreateResponse result = await(_salesNoteService.createNote(note));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testDeleteNoteSuccessNoSharingPolicies() {
    NoteKey noteKey = new NoteKey().
        setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).deleteNote(any(), any(), any());
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(
        _lssSharingDB).deleteSubjectPolicy(any());
    UpdateResponse result = await(_salesNoteService.deleteNote(noteKey, SEAT_URN1, CONTRACT_URN1));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteNoteSuccessWithSharingPolicies() {
    java.util.List<com.linkedin.espresso.common.util.Pair<Urn, ShareRole>> pairs =
        Collections.singletonList(new com.linkedin.espresso.common.util.Pair<>((Urn)SEAT_URN1, ShareRole.OWNER));
    PaginatedList<com.linkedin.espresso.common.util.Pair<Urn, ShareRole>> paginatedPairs =
        PaginatedList.createForPage(pairs, 0, 10, 0);
    NoteKey noteKey = new NoteKey().
        setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).deleteNote(any(), any(), any());
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssSharingDB).deleteSubjectPolicy(any());
    UpdateResponse result = await(_salesNoteService.deleteNote(noteKey, SEAT_URN1, CONTRACT_URN1));
    Assert.assertEquals(result.getStatus(), HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  /**
   * Test verifies that seat 2 cannot delete note created by seat 1
   */
  public void testDeleteNoteFailureAccessDeniedToSeat() throws URISyntaxException {
    NoteKey noteKey = new NoteKey().
        setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesNoteService.deleteNote(noteKey, SEAT_URN2, CONTRACT_URN1));
    }).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, "Access denied when requesting delete note"));
  }

  @Test
  public void testFindBySeatAndEntitySuccess(){
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    NoteKey noteKey = new NoteKey().setNoteId(NOTE_ID1).setSeat(SEAT_URN1).setEntity(ENTITY_URN);
    List<Pair<NoteKey, com.linkedin.sales.espresso.Note>> notes = Collections.singletonList(Pair.of(noteKey, note));
    Mockito.doReturn(Task.value(notes)).when(_lssNoteDB).findBySeatAndEntity(any(), any(), anyInt(), anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeatAndEntity(SEAT_URN1, MEMBER_URN, CONTRACT_URN1, 0, 10));

    Assert.assertEquals(result.getElements().size(), 1);
    Note expected = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN)
        .setNoteId(NOTE_ID1).setBody(ATTRIBUTED_TEXT).setExpireAt(EXPIRE_AT)
        .setLastModified(new AuditStamp().setTime(CREATED_TIME).setActor(SEAT_URN1))
        .setCreated(new AuditStamp().setTime(CREATED_TIME).setActor(SEAT_URN1));
    Assert.assertEquals(result.getElements().get(0), expected);
  }

  @Test
  /*
   * Deserialize failure caused because the contract urn field doesn't have the correct urn type, a member urn is set to
   * this field.
   */
  public void testFindBySeatAndEntityDeserializeFailure(){
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    note.contractUrn = MEMBER_URN.toString();
    Pair<Long, com.linkedin.sales.espresso.Note> pair = new Pair<>(1L, note);
    List<Pair<Long, com.linkedin.sales.espresso.Note>> notes = Collections.singletonList(pair);
    Mockito.doReturn(Task.value(PaginatedList.createForPage(notes, 0, 1, 1)))
        .when(_lssNoteDB)
        .findBySeatAndEntity(any(), any(), anyInt(), anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeatAndEntity(SEAT_URN1, MEMBER_URN, CONTRACT_URN1,
            ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));
    Assert.assertEquals(result.getElements().size(), 0);
  }

  @Test
  /**
   * The test verifies that no notes are returned when the DP returns an exception
   */
  public void testFindBySeatAndEntityWithNoNotesFound() {
    Mockito.doReturn(Task.value(new RuntimeException())).when(_lssNoteDB).findBySeatAndEntity(any(), any(), anyInt(),
        anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeatAndEntity(SEAT_URN1, ORGANIZATION_URN,  CONTRACT_URN1,
            ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));
    Assert.assertEquals(result.getElements().size(), 0);
  }

  @Test
  /**
   * The test is similar to the test testFindBySeatAndEnitySuccess. The only change being seat2 with contract2
   * tries to access note created by seat1 in a different contract, ie. Contract1
   */
  public void testFindBySeatAndEntityAccessDeniedFailure() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    Pair<Long, com.linkedin.sales.espresso.Note> pair = new Pair<>(1L, note);
    List<Pair<Long, com.linkedin.sales.espresso.Note>> notes = Collections.singletonList(pair);
    Mockito.doReturn(Task.value(PaginatedList.createForPage(notes, 0, 1, 1)))
        .when(_lssNoteDB)
        .findBySeatAndEntity(any(), any(), anyInt(), anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeatAndEntity(SEAT_URN1, SALES_SHARED_SEARCH_URN, CONTRACT_URN2,
            ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));
    Assert.assertEquals(result.getElements().size(), 0);
  }

  @Test
  /**
   * The test shows failure when the entity type is not supported. Here the entity type is set as SeatUrn
   */
  public void testFindBySeatAndEntityEntityTypeNotSupported() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesNoteService.findBySeatAndEntity(SEAT_URN1, new SeatUrn(999L), CONTRACT_URN2,
          ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Entity type not supported by "
        + "AnnotatableEntityUrn"));
  }

  @Test
  public void testFindBySeatSuccess(){
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    NoteKey noteKey = new NoteKey().setNoteId(RANDOM_ID).setSeat(SEAT_URN1).setEntity(ENTITY_URN);

    Pair<NoteKey, com.linkedin.sales.espresso.Note> entry = new Pair<>(noteKey, note);
    List<Pair<NoteKey, com.linkedin.sales.espresso.Note>> notes = Collections.singletonList(entry);
    Mockito.doReturn(Task.value(notes)).when(_lssNoteDB).findBySeat(any(), anyInt(), anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeat(SEAT_URN1, ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));

    Assert.assertEquals(result.getElements().size(), 1);
    Note expected = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN)
        .setNoteId(RANDOM_ID).setBody(ATTRIBUTED_TEXT).setExpireAt(EXPIRE_AT)
        .setLastModified(new AuditStamp().setTime(CREATED_TIME).setActor(SEAT_URN1))
        .setCreated(new AuditStamp().setTime(CREATED_TIME).setActor(SEAT_URN1));
    Assert.assertEquals(result.getElements().get(0), expected);
  }


  @Test
  /*
   * Deserialize failure caused because the contract urn field doesn't have the correct urn type, a member urn is set to
   * this field.
   */
  public void testFindBySeatDeserializeFailure(){
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    note.contractUrn = MEMBER_URN.toString();
    NoteKey noteKey = new NoteKey().setNoteId(RANDOM_ID).setSeat(SEAT_URN1).setEntity(ENTITY_URN);

    Pair<NoteKey, com.linkedin.sales.espresso.Note> entry = new Pair<>(noteKey, note);
    List<Pair<NoteKey, com.linkedin.sales.espresso.Note>> notes = Collections.singletonList(entry);
    Mockito.doReturn(Task.value(notes)).when(_lssNoteDB).findBySeat(any(), anyInt(), anyInt());
    BasicCollectionResult<Note> result =
        await(_salesNoteService.findBySeat(SEAT_URN1, ServiceConstants.DEFAULT_START, ServiceConstants.DEFAULT_COUNT));

    Assert.assertEquals(result.getElements().size(), 0);
  }

  @Test
  public void testUpdateNoteSuccess() {
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT)
        .setNoteId(NOTE_ID1).setEntity(ENTITY_URN).setSeat(SEAT_URN1).setExpireAt(EXPIRE_AT);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).updateNote(any(), any(), any(), any());

    Boolean result = await(_salesNoteService.updateNote(note, SEAT_URN1, CONTRACT_URN1));
    Assert.assertTrue(result);
  }

  @Test
  public void testUpdateNoteSuccessWithNoContent() {
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setNoteId(NOTE_ID1).
        setEntity(ENTITY_URN).setSeat(SEAT_URN1).setExpireAt(EXPIRE_AT);
    Boolean result = await(_salesNoteService.updateNote(note, SEAT_URN1, CONTRACT_URN1));
    Assert.assertTrue(result);
  }

  @Test
  /**
   *The test is similar to testCreateNoteSucceed, with no migration ID and ExpireAt.
   */
  public void testUpdateNoteSuccessWithNoOptionalFields() {
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT)
        .setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).updateNote(any(), any(), any(), any());

    Boolean result = await(_salesNoteService.updateNote(note, SEAT_URN1, CONTRACT_URN1));
    Assert.assertTrue(result);
  }

  @Test
  /**
   * Test checks if a failure happens when one of the required variable is not set. Here entity Urn in not set.
   */
  public void testUpdateNoteFailureNotAllRequiredFields() {
    Note note = new Note().setSeat(SEAT_URN1).setBody(ATTRIBUTED_TEXT)
        .setNoteId(NOTE_ID1).setExpireAt(EXPIRE_AT).setContract(CONTRACT_URN1);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).updateNote(any(), any(), any(), any());

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesNoteService.updateNote(note, SEAT_URN1, CONTRACT_URN1));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "Not all required fields are present"));
  }

  @Test
  /**
   * Test checks if a failure happens when a seat2 tries to access seat1's notes
   */
  public void testUpdateNoteFailureAccessDenied() {
    Note note = new Note().setContract(CONTRACT_URN1).setSeat(SEAT_URN1).setEntity(ENTITY_URN).setBody(ATTRIBUTED_TEXT)
        .setNoteId(NOTE_ID1).setExpireAt(EXPIRE_AT);
    Mockito.doReturn(Task.value(Boolean.TRUE)).when(_lssNoteDB).updateNote(any(), any(), any(), any());

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesNoteService.updateNote(note, SEAT_URN2, CONTRACT_URN1));
    }).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, "Access denied when requesting update Note"));
  }

  @Test
  public void testGetNoteSuccess() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();

    NoteKey noteKey = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.value(note)).when(_lssNoteDB).getNote(any());

    Note result = await(_salesNoteService.getNote(noteKey));
    Assert.assertEquals(result.getSeat(), SEAT_URN1);
    Assert.assertEquals(result.getEntity(), ENTITY_URN);
    Assert.assertEquals(result.getBody().getText(), ATTRIBUTED_TEXT.getText());
    Assert.assertEquals(result.getNoteId(), NOTE_ID1);
  }

  @Test
  public void testGetNoteNotFound() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    NoteKey noteKey = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.failure(new EntityNotFoundException(null, "cannot find note for " + noteKey))).when(_lssNoteDB).getNote(any());
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_salesNoteService.getNote(noteKey)))
        .withCause(new RestLiServiceException(HttpStatus.S_404_NOT_FOUND,
            "cannot find note for " + noteKey));
  }

  @Test
  /*
   * Deserialize failure caused because the contract urn field doesn't have the correct urn type, a member urn is set to
   * this field.
   */
  public void testGetNoteDeserializeFailure() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    note.contractUrn = MEMBER_URN.toString();
    NoteKey noteKey = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    Mockito.doReturn(Task.value(note)).when(_lssNoteDB).getNote(any());

    Note result = await(_salesNoteService.getNote(noteKey));
    Assert.assertNull(result);
  }

  @Test
  public void testBatchGetNotesSucceed() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    NoteKey noteKey1 = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    NoteKey noteKey2 = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID2);
    List<Pair<NoteKey, com.linkedin.sales.espresso.Note>> noteKeyToNote = new ArrayList<>();
    noteKeyToNote.add(Pair.of(noteKey1, note));
    noteKeyToNote.add(Pair.of(noteKey2, note));
    Mockito.doReturn(Task.value(noteKeyToNote)).when(_lssNoteDB).batchGetNotes(any());

    List<Pair<NoteKey, Note>> pairs = await(_salesNoteService.batchGet(ImmutableList.of(noteKey1, noteKey2)));
    Note result = pairs.get(0).getSecond();
    Assert.assertEquals(result.getSeat(), SEAT_URN1);
    Assert.assertEquals(result.getEntity(), ENTITY_URN);
    Assert.assertEquals(result.getBody().getText(), ATTRIBUTED_TEXT.getText());
    Assert.assertEquals(result.getNoteId(), NOTE_ID1);
    Note result2 = pairs.get(1).getSecond();
    Assert.assertEquals(result2.getSeat(), SEAT_URN1);
    Assert.assertEquals(result2.getEntity(), ENTITY_URN);
    Assert.assertEquals(result2.getBody().getText(), ATTRIBUTED_TEXT.getText());
    Assert.assertEquals(result2.getNoteId(), NOTE_ID2);
  }

  @Test
  public void testBatchGetNotesPartialFailure() {
    com.linkedin.sales.espresso.Note note = createEspressoNote();
    NoteKey noteKey1 = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID1);
    NoteKey noteKey2 = new NoteKey().setEntity(ENTITY_URN).setSeat(SEAT_URN1).setNoteId(NOTE_ID2);
    List<Pair<NoteKey, com.linkedin.sales.espresso.Note>> noteKeyToNote = new ArrayList<>();
    // Only add noteKey1 to the mapping. noteKey2 is not added
    noteKeyToNote.add(Pair.of(noteKey1, note));
    Mockito.doReturn(Task.value(noteKeyToNote)).when(_lssNoteDB).batchGetNotes(any());

    List<Pair<NoteKey, Note>> pairs = await(_salesNoteService.batchGet(ImmutableList.of(noteKey1, noteKey2)));
    assertThat(pairs.size()).isEqualTo(1);

    Note result = pairs.get(0).getSecond();
    Assert.assertEquals(result.getSeat(), SEAT_URN1);
    Assert.assertEquals(result.getEntity(), ENTITY_URN);
    Assert.assertEquals(result.getBody().getText(), ATTRIBUTED_TEXT.getText());
    Assert.assertEquals(result.getNoteId(), NOTE_ID1);
  }

  private com.linkedin.sales.espresso.Note createEspressoNote() {
    com.linkedin.sales.espresso.Note note = new com.linkedin.sales.espresso.Note();
    com.linkedin.sales.espresso.AttributedText attributedText = new com.linkedin.sales.espresso.AttributedText();
    attributedText.text = ATTRIBUTED_TEXT.getText();
    note.body = attributedText;
    note.expireTime = EXPIRE_AT;
    note.createdTime = CREATED_TIME;
    note.lastModifiedTime = CREATED_TIME;
    note.contractUrn = CONTRACT_URN1.toString();
    return note;
  }
}
