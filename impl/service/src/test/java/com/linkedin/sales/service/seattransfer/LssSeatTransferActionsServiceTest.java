package com.linkedin.sales.service.seattransfer;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.ownershiptransfer.OwnershipTransferType;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferRequestsClient;
import com.linkedin.sales.service.seattransfer.LssSeatTransferActionsService;
import com.linkedin.sales.service.seattransfer.salescustomlists.SalesCustomListsTransferService;
import com.linkedin.sales.service.seattransfer.salesentitynotes.SalesEntityNotesTransferService;
import com.linkedin.sales.service.seattransfer.salesleads.SalesLeadsTransferService;
import com.linkedin.sales.service.seattransfer.salesleadaccountassociations.SalesLeadAccountAssociationsTransferService;
import com.linkedin.sales.service.seattransfer.salesaccounts.SalesAccountsTransferService;
import com.linkedin.sales.service.utils.LixUtils;
import com.linkedin.sales.urn.SalesSeatTransferRequestUrn;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.net.URISyntaxException;
import java.util.Optional;
import java.util.stream.Stream;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.linkedin.ownershiptransfer.OwnershipTransferEntityType.SALES_LEADS;
import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.params.provider.Arguments.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class LssSeatTransferActionsServiceTest extends BaseEngineParJunitJupiterTest {

  @Mock
  private SalesSeatTransferRequestsClient _salesSeatTransferRequestsClient;
  @Mock
  private SalesLeadsTransferService _salesLeadsTransferService;
  @Mock
  private SalesCustomListsTransferService _salesCustomListsTransferService;
  @Mock
  private SalesEntityNotesTransferService _salesEntityNotesTransferService;
  @Mock
  private SalesLeadAccountAssociationsTransferService _salesLeadAccountAssociationsTransferService;
  @Mock
  private SalesAccountsTransferService _salesAccountsTransferService;
  private LssSeatTransferActionsService _lssSeatTransferActionsService;

  @BeforeEach
  public void setUp() {
    // Call all services
    _lssSeatTransferActionsService =
        new LssSeatTransferActionsService(_salesSeatTransferRequestsClient, _salesLeadsTransferService,
            _salesEntityNotesTransferService, _salesLeadAccountAssociationsTransferService,
            _salesAccountsTransferService, _salesCustomListsTransferService);
  }

  @Test
  public void testParseBatchSizeAndDelayFromLixTreatmentWhenControl() {
    int[] batchSizeAndDelay = LssSeatTransferActionsService.parseBatchSizeAndDelayFromLixTreatment(LixUtils.CONTROL);
    assertThat(batchSizeAndDelay).hasSize(2);
    assertThat(batchSizeAndDelay[0]).isEqualTo(12);
    assertThat(batchSizeAndDelay[1]).isEqualTo(750);
  }

  @Test
  public void testParseBatchSizeAndDelayFromLixTreatmentWhenNotControl() {
    int[] batchSizeAndDelay = LssSeatTransferActionsService.parseBatchSizeAndDelayFromLixTreatment("batch_100_delay_850");
    assertThat(batchSizeAndDelay).hasSize(2);
    assertThat(batchSizeAndDelay[0]).isEqualTo(100);
    assertThat(batchSizeAndDelay[1]).isEqualTo(850);
  }

  @ParameterizedTest
  @MethodSource("executeSeatTransferRequestDataProvider")
  public void testExecuteSeatTransferRequest(OwnershipTransferRequest request) throws URISyntaxException {
    when(_salesSeatTransferRequestsClient.get(any())).thenReturn(Task.value(Optional.of(request)));
    if (OwnershipTransferType.$UNKNOWN != request.getOwnershipTransferType()) {
          when(_salesLeadsTransferService.transfer(request, ACTOR)).thenReturn(Task.value(null));
    }
    SalesSeatTransferRequestUrn seatTransferUrn = SalesSeatTransferRequestUrn.createFromUrn(
        Urn.createFromTuple(SALES_LEADS.toString(), new ContractUrn(1L), 2L));
    if (OwnershipTransferType.$UNKNOWN == request.getOwnershipTransferType()) {
      runAndWaitException(_lssSeatTransferActionsService.executeSeatTransferRequest(
          seatTransferUrn, SALES_LEADS, ACTOR), UnsupportedOperationException.class);
      verify(_salesLeadsTransferService, times(0)).transfer(request, ACTOR);
    } else {
      runAndWait(_lssSeatTransferActionsService.executeSeatTransferRequest(
          seatTransferUrn, SALES_LEADS, ACTOR));
      verify(_salesLeadsTransferService, times(1)).transfer(request, ACTOR);
      verify(_salesAccountsTransferService, times(0)).transfer(request, ACTOR);
    }
  }

  @SuppressFBWarnings("PRMC_POSSIBLY_REDUNDANT_METHOD_CALLS")
  static Stream<Arguments> executeSeatTransferRequestDataProvider() {
    return Stream.of(
        arguments(getOwnershipTransferRequest().setOwnershipTransferType(OwnershipTransferType.TRANSFER)),
        arguments(getOwnershipTransferRequest().setOwnershipTransferType(OwnershipTransferType.$UNKNOWN)));
  }
}
