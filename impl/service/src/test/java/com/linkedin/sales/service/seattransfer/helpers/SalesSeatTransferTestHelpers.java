package com.linkedin.sales.service.seattransfer.helpers;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.ownershiptransfer.OwnershipTransferKey;
import com.linkedin.ownershiptransfer.OwnershipTransferRequest;
import com.linkedin.sales.urn.SalesListV2Urn;
import com.linkedin.sales.urn.SalesSavedAccountUrn;
import com.linkedin.salesnote.AnnotatableEntityUrn;


public final class SalesSeatTransferTestHelpers {

  public static final Long OWNERSHIP_TRANSFER_ID = 123L;
  public static final SeatUrn ACTOR = new SeatUrn(0L);
  public static final SeatUrn SOURCE_SEAT = new SeatUrn(1L);
  public static final SeatUrn TARGET_SEAT = new SeatUrn(2L);
  public static final MemberUrn TEST_MEMBER = new MemberUrn(1L);
  public static final MemberUrn TEST_MEMBER_2 = new MemberUrn(2L);
  public static final ContractUrn SOURCE_CONTRACT = new ContractUrn(3L);
  public static final ContractUrn TARGET_CONTRACT = new ContractUrn(4L);
  public static final Long TEST_NOTE_ID = 5L;

  private SalesSeatTransferTestHelpers() {

  }

  public static OwnershipTransferRequest getOwnershipTransferRequest() {
    return new OwnershipTransferRequest()
        .setId(OWNERSHIP_TRANSFER_ID)
        .setSourceSeat(SOURCE_SEAT)
        .setTargetSeat(TARGET_SEAT)
        .setSourceContract(SOURCE_CONTRACT)
        .setTargetContract(TARGET_CONTRACT);
  }

  public static OwnershipTransferKey getOwnershipTransferKey() {
    return new OwnershipTransferKey()
        .setId(OWNERSHIP_TRANSFER_ID)
        .setContractUrn(SOURCE_CONTRACT);
  }

  public static OrganizationUrn getTestOrganizationUrn() {
    return (OrganizationUrn) Urn.createFromTuple(
        OrganizationUrn.ENTITY_TYPE,
        2L
    );
  }

  public static SalesNoteUrn getSalesNoteUrn(AnnotatableEntityUrn urn, long id) {
    return (SalesNoteUrn) Urn.createFromTuple(
        SalesNoteUrn.ENTITY_TYPE,
        SOURCE_SEAT,
        urn,
        id
    );
  }

  public static SalesListV2Urn getSalesListV2UrnForAccountLists() {
    return (SalesListV2Urn) Urn.createFromTuple(
        SalesListV2Urn.ENTITY_TYPE,
        SOURCE_SEAT,
        1L
    );
  }

  public static SalesListV2Urn getSalesListV2UrnForLeadLists() {
    return (SalesListV2Urn) Urn.createFromTuple(
        SalesListV2Urn.ENTITY_TYPE,
        SOURCE_SEAT,
        2L
    );
  }

  public static SalesListUrn getSalesListUrnForAccountListEntity() {
    return (SalesListUrn) Urn.createFromTuple(
        SalesListUrn.ENTITY_TYPE,
        1L
    );
  }

  public static SalesListUrn getSalesListUrnForLeadListEntity() {
    return (SalesListUrn) Urn.createFromTuple(
        SalesListUrn.ENTITY_TYPE,
        2L
    );
  }
}
