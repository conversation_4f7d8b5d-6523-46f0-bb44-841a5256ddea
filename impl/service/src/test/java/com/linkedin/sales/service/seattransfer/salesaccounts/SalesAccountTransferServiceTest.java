package com.linkedin.sales.service.seattransfer.salesaccounts;

import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.sales.service.LixService;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.lss.salesleadaccount.SalesAccountActionResult;
import com.linkedin.lss.salesleadaccount.SalesAccountActionResultArray;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineJUnitJupiterTest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.urn.SalesSavedAccountUrn;
import com.linkedin.salesleadaccount.SalesAccount;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static org.mockito.Mockito.*;

public class SalesAccountTransferServiceTest extends BaseEngineJUnitJupiterTest {
  @Mock
  private SalesSeatTransferCopyAssociationsClient _salesSeatTransferCopyAssociationsClient;
  @Mock
  private SalesLeadAccountCommonService _salesLeadAccountCommonService;
  @Mock
  private LixService _lixService;
  private static final OrganizationUrn TEST_ORGANIZATION = getTestOrganizationUrn();
  private SalesAccountsTransferService _salesAccountsTransferService;


  @BeforeEach
  public void setUp() {
    _salesAccountsTransferService = new SalesAccountsTransferService(_salesLeadAccountCommonService,
        _salesSeatTransferCopyAssociationsClient, 10000, _lixService);
  }

  @Test
  public void transferWhenTargetSeatExceedsLimitForSavedAccounts() {
    when(_salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(any())).thenReturn(Task.value(10000));
    runAndWait(_salesAccountsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedAccountCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedAccountsForSeatUpToLimit(any(), any(), any(), any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenSourceSeatDoesNotHaveSavedAccounts() {
    when(_salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(any())).thenReturn(Task.value(0));

    // Create empty SalesAccount list to simulate sourceSeat having no saved accounts
    List<SalesAccount> emptySavedAccountList = new ArrayList<>();
    when(_salesLeadAccountCommonService.findAllSavedAccountsForSeatUpToLimit(SOURCE_SEAT, 10000, null, null, SortOrder.DESCENDING)).thenReturn(Task.value(emptySavedAccountList));
    runAndWait(_salesAccountsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedAccountCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedAccountsForSeatUpToLimit(any(), any(), any(), any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSourceSeatSavedAccountsAreAlreadyTransferred() {
    when(_salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(any())).thenReturn(Task.value(1));
    // Create SalesAccount list with one SalesAccount
    List<SalesAccount> savedAccountList = new ArrayList<>(1);
    savedAccountList.add(new SalesAccount().setOrganization(TEST_ORGANIZATION));

    when(_salesLeadAccountCommonService.findAllSavedAccountsForSeatUpToLimit(SOURCE_SEAT, 10000, null, null, SortOrder.DESCENDING)).thenReturn(Task.value(savedAccountList));

    // Create OwnershipTransferCopyAssociation list with SalesAccount already having a transfer record
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(1);
    ownershipTransferCopyAssociations.add(
        new OwnershipTransferCopyAssociation()
            .setSourceEntity(
                createSalesSavedAccountUrn(SOURCE_SEAT, TEST_ORGANIZATION.getIdAsLong())));

    List<Urn> salesSavedAccountUrnList = createSalesSavedAccountUrnList(savedAccountList, SOURCE_SEAT);
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(salesSavedAccountUrnList, TARGET_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    runAndWait(_salesAccountsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedAccountCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedAccountsForSeatUpToLimit(any(), any(), any(), any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSavedAccountsAreNotSuccessfullyTransferred() {
    when(_salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(any())).thenReturn(Task.value(0));

    // Create SalesAccount list with one SalesAccount
    List<SalesAccount> savedAccountList = new ArrayList<>(1);
    savedAccountList.add(new SalesAccount().setOrganization(TEST_ORGANIZATION));

    when(_salesLeadAccountCommonService.findAllSavedAccountsForSeatUpToLimit(SOURCE_SEAT, 10000, null, null, SortOrder.DESCENDING)).thenReturn(Task.value(savedAccountList));

    // Create empty OwnershipTransferCopyAssociation list because nothing has been transferred previously
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(0);
    List<Urn> savedAccountUrnList = new ArrayList<>();

    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(savedAccountUrnList, TARGET_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    // Create SalesAccountActionResultArray with a result that has a failed status to indicate saved account was not created
    SalesAccountActionResultArray salesAccountActionResultArray = new SalesAccountActionResultArray();
    salesAccountActionResultArray.add(
        new SalesAccountActionResult()
            .setStatus(ActionStatus.INTERNAL_SERVER_ERROR)
            .setOrganizationId(TEST_ORGANIZATION.getIdAsLong()));

    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(new ArrayList<>()));

    runAndWaitException(_salesAccountsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT), RestLiServiceException.class);

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedAccountCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedAccountsForSeatUpToLimit(any(), any(), any(), any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSavedAccountsAreTransferredSuccessfully() {
    when(_salesLeadAccountCommonService.getTotalSavedAccountCountForSeat(any())).thenReturn(Task.value(0));

    // Create SalesAccount list with one SalesAccount
    List<SalesAccount> savedAccountList = new ArrayList<>(1);
    savedAccountList.add(new SalesAccount().setOrganization(TEST_ORGANIZATION));

    when(_salesLeadAccountCommonService.findAllSavedAccountsForSeatUpToLimit(SOURCE_SEAT, 10000, null, null, SortOrder.DESCENDING)).thenReturn(Task.value(savedAccountList));

    // Create empty OwnershipTransferCopyAssociation list because nothing has been transferred previously
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(0);
    List<Urn> savedAccountUrnList = new ArrayList<>();

    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(savedAccountUrnList, TARGET_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    // Create SalesAccountActionResultArray with a result that has a success status to indicate saved account was created
    SalesAccountActionResultArray salesAccountActionResultArray = new SalesAccountActionResultArray();
    salesAccountActionResultArray.add(
        new SalesAccountActionResult()
            .setStatus(ActionStatus.SUCCESS)
            .setOrganizationId(TEST_ORGANIZATION.getIdAsLong()));


    // Create OwnershipTransferCopyAssociations with copyAssociationsClient
    List<Long> newTransferRecordIds = new ArrayList<>();
    newTransferRecordIds.add(1L);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(newTransferRecordIds));

    runAndWait(_salesAccountsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedAccountCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedAccountsForSeatUpToLimit(any(), any(), any(), any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }

  public SalesSavedAccountUrn createSalesSavedAccountUrn(SeatUrn owner, long organizationId) {
    return (SalesSavedAccountUrn) Urn.createFromTuple(
        SalesSavedAccountUrn.ENTITY_TYPE,
        owner,
        organizationId
    );
  }

  public List<Urn> createSalesSavedAccountUrnList(List<SalesAccount> savedAccounts, SeatUrn owner) {
    return savedAccounts.stream().map(
        savedAccount -> (SalesSavedAccountUrn) Urn.createFromTuple(
            SalesSavedAccountUrn.ENTITY_TYPE,
            owner,
            savedAccount.getOrganization().getIdAsLong())
    ).collect(Collectors.toList());
  }
}