package com.linkedin.sales.service.seattransfer.salescustomlists;

import com.linkedin.espresso.common.util.Pair;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.BatchCreateResult;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.list.SalesListEntityService;
import com.linkedin.sales.service.list.SalesListService;
import com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.saleslist.List;
import com.linkedin.saleslist.ListEntity;
import com.linkedin.saleslist.ListType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static org.mockito.Mockito.*;


public class SalesCustomListsTransferServiceTest extends BaseEngineParJunitJupiterTest {
  @Mock
  private SalesSeatTransferCopyAssociationsClient _copyAssociationsClient;
  @Mock
  private SalesListService _salesListService;
  @Mock
  private SalesListEntityService _salesListEntityService;
  private SalesCustomListsTransferService _salesCustomListsTransferService;

  @BeforeEach
  public void setUp() {
    _salesCustomListsTransferService = new SalesCustomListsTransferService(
        _copyAssociationsClient,
        _salesListService,
        _salesListEntityService
    );
  }

  @Test
  public void transferWhenSourceSeatHasNoCustomLists() {
    java.util.List<List> accountList = new ArrayList<>();
    Pair<Integer, java.util.List<List>> accountLists = new Pair<>(0, accountList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.ACCOUNT), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(accountLists));
    java.util.List<List> leadList = new ArrayList<>();
    Pair<Integer, java.util.List<List>> leadLists = new Pair<>(0, leadList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.LEAD), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(leadLists));

    runAndWait(_salesCustomListsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesListService, times(2)).getListsForSeat(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_copyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
    verify(_salesListService, times(0)).batchCreateLists(any(), any());
    verify(_salesListEntityService, times(0)).getListEntities(any(), any(), any(), any(), any());
    verify(_salesListEntityService, times(0)).batchCreateListEntities(any(), any(), any());
    verify(_salesListService, times(0)).deleteList(any(), any(), any());
    verify(_copyAssociationsClient, times(0)).createCopyAssociations(any());
  }

  @Test
  public void transferWhenSourceSeatHasAlreadyTransferredCustomLists() {
    java.util.List<List> accountList = new ArrayList<>();
    accountList.add(new List().setListType(ListType.ACCOUNT).setId(1L));
    Pair<Integer, java.util.List<List>> accountLists = new Pair<>(0, accountList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.ACCOUNT), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(accountLists));
    java.util.List<List> leadList = new ArrayList<>();
    leadList.add(new List().setListType(ListType.LEAD).setId(2L));
    leadList.add(new List().setListType(ListType.LEAD).setId(3L));
    Pair<Integer, java.util.List<List>> leadLists = new Pair<>(0, leadList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.LEAD), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(leadLists));

    java.util.List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociationList = new ArrayList<>();
    ownershipTransferCopyAssociationList.add(new OwnershipTransferCopyAssociation().setSourceEntity(
        SalesSeatTransferTestHelpers.getSalesListV2UrnForAccountLists()));
    ownershipTransferCopyAssociationList.add(new OwnershipTransferCopyAssociation().setSourceEntity(
        SalesSeatTransferTestHelpers.getSalesListV2UrnForLeadLists()));
    when(_copyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(ownershipTransferCopyAssociationList));

    runAndWait(_salesCustomListsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesListService, times(2)).getListsForSeat(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_copyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesListService, times(0)).batchCreateLists(any(), any());
    verify(_salesListEntityService, times(0)).getListEntities(any(), any(), any(), any(), any());
    verify(_salesListEntityService, times(0)).batchCreateListEntities(any(), any(), any());
    verify(_salesListService, times(0)).deleteList(any(), any(), any());
    verify(_copyAssociationsClient, times(0)).createCopyAssociations(any());
  }

  @Test
  public void transferWhenAllCustomListsArentSuccessfullyCreated() {
    java.util.List<List> accountList = new ArrayList<>();
    accountList.add(new List().setListType(ListType.ACCOUNT).setId(1L));
    Pair<Integer, java.util.List<List>> accountLists = new Pair<>(0, accountList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.ACCOUNT), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(accountLists));
    java.util.List<List> leadList = new ArrayList<>();
    leadList.add(new List().setListType(ListType.LEAD).setId(2L));
    Pair<Integer, java.util.List<List>> leadLists = new Pair<>(0, leadList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.LEAD), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(leadLists));

    when(_copyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(new ArrayList<>()));

    java.util.List<ListEntity> accountListEntities = new ArrayList<>();
    ListEntity accountListEntity = new ListEntity()
        .setList(SalesSeatTransferTestHelpers.getSalesListUrnForAccountListEntity())
        .setEntity(SalesSeatTransferTestHelpers.getTestOrganizationUrn());
    accountListEntities.add(accountListEntity);
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesAccountListReturn = new Pair<>(1, accountListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(1L), any(), any(), any())).thenReturn(Task.value(getListEntitiesAccountListReturn));

    java.util.List<ListEntity> leadListEntities = new ArrayList<>();
    ListEntity leadListEntity = new ListEntity()
        .setList(SalesSeatTransferTestHelpers.getSalesListUrnForLeadListEntity())
        .setEntity(SalesSeatTransferTestHelpers.TEST_MEMBER);
    leadListEntities.add(leadListEntity);
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesLeadListReturn = new Pair<>(1, leadListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(2L), any(), any(), any())).thenReturn(Task.value(getListEntitiesLeadListReturn));

    java.util.List<CreateResponse> createResponseList = new ArrayList<>();
    CreateResponse accountListCreateResponse = new CreateResponse(1L, HttpStatus.S_201_CREATED);
    createResponseList.add(accountListCreateResponse);
    CreateResponse leadListCreateResponse = new CreateResponse(2L, HttpStatus.S_204_NO_CONTENT);
    createResponseList.add(leadListCreateResponse);
    BatchCreateResult<Long, List> result = new BatchCreateResult<>(createResponseList);
    when(_salesListService.batchCreateLists(any(), any())).thenReturn(Task.value(result));

    Map<CompoundKey, CreateResponse> createListEntitiesInTargetSeat = new HashMap<>();
    createListEntitiesInTargetSeat.put(new CompoundKey()
        .append(ServiceConstants.LIST_COMPOUND_KEY, accountListEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, accountListEntity.getEntity()), accountListCreateResponse);
    createListEntitiesInTargetSeat.put(new CompoundKey()
        .append(ServiceConstants.LIST_COMPOUND_KEY, leadListEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, leadListEntity.getEntity()), leadListCreateResponse);
    when(_salesListEntityService.batchCreateListEntities(any(), any()))
        .thenReturn(Task.value(createListEntitiesInTargetSeat));

    when(_salesListService.deleteList(any(), any(), any())).thenReturn(Task.value(Boolean.TRUE));

    java.util.List<Long> batchCreateIds = new ArrayList<>();
    batchCreateIds.add(1L);
    when(_copyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(batchCreateIds));

    runAndWaitException(_salesCustomListsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT), RestLiServiceException.class);

    verify(_salesListService, times(2)).getListsForSeat(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_copyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesListService, times(1)).batchCreateLists(any(), any());
    verify(_salesListEntityService, times(1)).getListEntities(any(), any(), any(), any(), any());
    verify(_salesListEntityService, times(1)).batchCreateListEntities(any(), any(), any());
    verify(_salesListService, times(1)).deleteList(any(), any(), any());
    verify(_copyAssociationsClient, times(1)).createCopyAssociations(any());
  }

  @Test
  public void transferWhenSourceSeatHasCustomListsWithNoListEntities() {
    java.util.List<List> accountList = new ArrayList<>();
    accountList.add(new List().setListType(ListType.ACCOUNT).setId(1L));
    Pair<Integer, java.util.List<List>> accountLists = new Pair<>(0, accountList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.ACCOUNT), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(accountLists));
    java.util.List<List> leadList = new ArrayList<>();
    leadList.add(new List().setListType(ListType.LEAD).setId(2L));
    Pair<Integer, java.util.List<List>> leadLists = new Pair<>(0, leadList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.LEAD), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(leadLists));

    when(_copyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(new ArrayList<>()));

    java.util.List<ListEntity> accountListEntities = new ArrayList<>();
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesAccountListReturn = new Pair<>(0, accountListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(1L), any(), any(), any())).thenReturn(Task.value(getListEntitiesAccountListReturn));

    java.util.List<ListEntity> leadListEntities = new ArrayList<>();
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesLeadListReturn = new Pair<>(0, leadListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(2L), any(), any(), any())).thenReturn(Task.value(getListEntitiesLeadListReturn));

    java.util.List<CreateResponse> createResponseList = new ArrayList<>();
    CreateResponse accountListCreateResponse = new CreateResponse(1L, HttpStatus.S_201_CREATED);
    createResponseList.add(accountListCreateResponse);
    CreateResponse leadListCreateResponse = new CreateResponse(2L, HttpStatus.S_201_CREATED);
    createResponseList.add(leadListCreateResponse);
    BatchCreateResult<Long, List> result = new BatchCreateResult<>(createResponseList);
    when(_salesListService.batchCreateLists(any(), any())).thenReturn(Task.value(result));


    java.util.List<Long> batchCreateIds = new ArrayList<>();
    batchCreateIds.add(1L);
    batchCreateIds.add(2L);
    when(_copyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(batchCreateIds));

    runAndWait(_salesCustomListsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesListService, times(2)).getListsForSeat(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_copyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesListService, times(1)).batchCreateLists(any(), any());
    verify(_salesListEntityService, times(1)).getListEntities(any(), any(), any(), any(), any());
    verify(_salesListEntityService, times(0)).batchCreateListEntities(any(), any(), any());
    verify(_salesListService, times(0)).deleteList(any(), any(), any());
    verify(_copyAssociationsClient, times(1)).createCopyAssociations(any());
  }

  @Test
  public void transferWhenSourceSeatSuccessfullyTransfersAllCustomLists() {
    java.util.List<List> accountList = new ArrayList<>();
    accountList.add(new List().setListType(ListType.ACCOUNT).setId(1L));
    Pair<Integer, java.util.List<List>> accountLists = new Pair<>(0, accountList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.ACCOUNT), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(accountLists));
    java.util.List<List> leadList = new ArrayList<>();
    leadList.add(new List().setListType(ListType.LEAD).setId(2L));
    Pair<Integer, java.util.List<List>> leadLists = new Pair<>(0, leadList);
    when(_salesListService.getListsForSeat(any(), eq(ListType.LEAD), any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(Task.value(leadLists));

    when(_copyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(new ArrayList<>()));

    java.util.List<ListEntity> accountListEntities = new ArrayList<>();
    ListEntity accountListEntity = new ListEntity()
        .setList(SalesSeatTransferTestHelpers.getSalesListUrnForAccountListEntity())
        .setEntity(SalesSeatTransferTestHelpers.getTestOrganizationUrn());
    accountListEntities.add(accountListEntity);
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesAccountListReturn = new Pair<>(1, accountListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(1L), any(), any(), any())).thenReturn(Task.value(getListEntitiesAccountListReturn));

    java.util.List<ListEntity> leadListEntities = new ArrayList<>();
    ListEntity leadListEntity = new ListEntity()
        .setList(SalesSeatTransferTestHelpers.getSalesListUrnForLeadListEntity())
        .setEntity(SalesSeatTransferTestHelpers.TEST_MEMBER);
    leadListEntities.add(leadListEntity);
    Pair<Integer, java.util.List<ListEntity>> getListEntitiesLeadListReturn = new Pair<>(1, leadListEntities);
    when(_salesListEntityService.getListEntities(any(), eq(2L), any(), any(), any())).thenReturn(Task.value(getListEntitiesLeadListReturn));

    java.util.List<CreateResponse> createResponseList = new ArrayList<>();
    CreateResponse accountListCreateResponse = new CreateResponse(1L, HttpStatus.S_201_CREATED);
    createResponseList.add(accountListCreateResponse);
    CreateResponse leadListCreateResponse = new CreateResponse(2L, HttpStatus.S_201_CREATED);
    createResponseList.add(leadListCreateResponse);
    BatchCreateResult<Long, List> result = new BatchCreateResult<>(createResponseList);
    when(_salesListService.batchCreateLists(any(), any())).thenReturn(Task.value(result));

    Map<CompoundKey, CreateResponse> createListEntitiesInTargetSeat = new HashMap<>();
    createListEntitiesInTargetSeat.put(new CompoundKey()
        .append(ServiceConstants.LIST_COMPOUND_KEY, accountListEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, accountListEntity.getEntity()), accountListCreateResponse);
    createListEntitiesInTargetSeat.put(new CompoundKey()
        .append(ServiceConstants.LIST_COMPOUND_KEY, leadListEntity.getList())
        .append(ServiceConstants.ENTITY_COMPOUND_KEY, leadListEntity.getEntity()), leadListCreateResponse);
    when(_salesListEntityService.batchCreateListEntities(any(), any()))
        .thenReturn(Task.value(createListEntitiesInTargetSeat));


    java.util.List<Long> batchCreateIds = new ArrayList<>();
    batchCreateIds.add(1L);
    batchCreateIds.add(2L);
    when(_copyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(batchCreateIds));

    runAndWait(_salesCustomListsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesListService, times(2)).getListsForSeat(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_copyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesListService, times(1)).batchCreateLists(any(), any());
    verify(_salesListEntityService, times(1)).getListEntities(any(), any(), any(), any(), any());
    verify(_salesListEntityService, times(1)).batchCreateListEntities(any(), any(), any());
    verify(_salesListService, times(0)).deleteList(any(), any(), any());
    verify(_copyAssociationsClient, times(1)).createCopyAssociations(any());
  }
}
