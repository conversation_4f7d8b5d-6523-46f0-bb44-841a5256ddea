package com.linkedin.sales.service.seattransfer.salesentitynotes;

import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.restli.common.ComplexResourceKey;
import com.linkedin.restli.common.EmptyRecord;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.CreateResponse;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.note.SalesNoteService;
import com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.Note;
import com.linkedin.salesnote.NoteKey;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.ACTOR;
import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.SOURCE_SEAT;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


public class SalesEntityNotesTransferServiceTest extends BaseEngineParJunitJupiterTest {
  @Mock
  private SalesSeatTransferCopyAssociationsClient _salesSeatTransferCopyAssociationsClient;
  @Mock
  private SalesNoteService _salesNoteService;
  private SalesEntityNotesTransferService _salesEntityNotesTransferService;

  @BeforeEach
  public void setUp() {
    _salesEntityNotesTransferService = new SalesEntityNotesTransferService(
        _salesNoteService, _salesSeatTransferCopyAssociationsClient, 200);
  }

  @Test
  public void testTransferWhenSourceSeatHasNoSavedNotes() {
    List<Note> emptySourceSeatNotes = new ArrayList<>();
    when(_salesNoteService.findAllSavedEntityNotesBySeat(SOURCE_SEAT, 10000)).thenReturn(Task.value(emptySourceSeatNotes));
    runAndWait(_salesEntityNotesTransferService.transfer(getOwnershipTransferRequest(), ACTOR));
    verify(_salesNoteService, times(1)).findAllSavedEntityNotesBySeat(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
  }

  @Test
  public void testTransferWhenSourceSeatHasTransferredAllSavedNotes() {
    AnnotatableEntityUrn newEntity = new AnnotatableEntityUrn();
    newEntity.setMemberUrn(TEST_MEMBER);

    List<Note> sourceSeatNotes = new ArrayList<>();
    sourceSeatNotes.add(new Note().setNoteId(TEST_NOTE_ID).setSeat(SOURCE_SEAT).setEntity(newEntity));
    when(_salesNoteService.findAllSavedEntityNotesBySeat(SOURCE_SEAT, 10000)).thenReturn(Task.value(sourceSeatNotes));

    List<OwnershipTransferCopyAssociation> alreadyTransferredEntityNotes = new ArrayList<>();
    alreadyTransferredEntityNotes.add(new OwnershipTransferCopyAssociation().setSourceEntity(
        SalesSeatTransferTestHelpers.getSalesNoteUrn(newEntity, TEST_NOTE_ID)));
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any()))
        .thenReturn(Task.value(alreadyTransferredEntityNotes));

    runAndWait(_salesEntityNotesTransferService.transfer(getOwnershipTransferRequest(), ACTOR));
    verify(_salesNoteService, times(1)).findAllSavedEntityNotesBySeat(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
  }

  @Test
  public void testTransferWhenSourceSeatTransfersAllSavedNotesSuccessfully() {
    AnnotatableEntityUrn newEntity = new AnnotatableEntityUrn();
    newEntity.setMemberUrn(TEST_MEMBER);

    List<Note> sourceSeatNotes = new ArrayList<>();
    sourceSeatNotes.add(new Note().setNoteId(TEST_NOTE_ID).setSeat(SOURCE_SEAT).setEntity(newEntity));
    when(_salesNoteService.findAllSavedEntityNotesBySeat(SOURCE_SEAT, 10000)).thenReturn(Task.value(sourceSeatNotes));

    List<OwnershipTransferCopyAssociation> emptyCopyAssociationsList = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any()))
        .thenReturn(Task.value(emptyCopyAssociationsList));

    NoteKey noteKey = new NoteKey().setNoteId(TEST_NOTE_ID).setSeat(TARGET_SEAT).setEntity(newEntity);
    CreateResponse newResponse = new CreateResponse(new ComplexResourceKey<>(noteKey, new EmptyRecord()), HttpStatus.S_201_CREATED);
    when(_salesNoteService.createNote(any())).thenReturn(Task.value(newResponse));

    List<Long> newlyCreatedTransferIds = new ArrayList<>();
    newlyCreatedTransferIds.add(10L);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(newlyCreatedTransferIds));


    runAndWait(_salesEntityNotesTransferService.transfer(getOwnershipTransferRequest(), ACTOR));
    verify(_salesNoteService, times(1)).findAllSavedEntityNotesBySeat(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesNoteService, times(1)).createNote(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
  }

  @Test
  public void testTransferWhenSourceSeatDoesNotTransferAllSavedNotesSuccessfully() {
    AnnotatableEntityUrn newEntity = new AnnotatableEntityUrn();
    newEntity.setMemberUrn(TEST_MEMBER);

    List<Note> sourceSeatNotes = new ArrayList<>();
    sourceSeatNotes.add(new Note().setNoteId(TEST_NOTE_ID).setSeat(SOURCE_SEAT).setEntity(newEntity));
    when(_salesNoteService.findAllSavedEntityNotesBySeat(SOURCE_SEAT, 10000)).thenReturn(Task.value(sourceSeatNotes));

    List<OwnershipTransferCopyAssociation> emptyCopyAssociationsList = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any()))
        .thenReturn(Task.value(emptyCopyAssociationsList));

    NoteKey noteKey = new NoteKey().setNoteId(TEST_NOTE_ID).setSeat(TARGET_SEAT).setEntity(newEntity);
    CreateResponse newResponse = new CreateResponse(new ComplexResourceKey<>(noteKey, new EmptyRecord()), HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    when(_salesNoteService.createNote(any())).thenReturn(Task.value(newResponse));

    List<Long> newlyCreatedTransferIds = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(newlyCreatedTransferIds));


    runAndWaitException(_salesEntityNotesTransferService.transfer(getOwnershipTransferRequest(), ACTOR), RestLiServiceException.class);
    verify(_salesNoteService, times(1)).findAllSavedEntityNotesBySeat(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesNoteService, times(1)).createNote(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
  }
}
