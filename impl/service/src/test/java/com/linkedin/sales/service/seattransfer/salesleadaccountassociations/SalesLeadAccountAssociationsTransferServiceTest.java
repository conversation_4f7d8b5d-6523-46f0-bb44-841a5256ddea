package com.linkedin.sales.service.seattransfer.salesleadaccountassociations;

import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.lss.salesleadaccount.SalesLeadAccountAssociationResult;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineParJunitJupiterTest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.urn.SalesSavedLeadAccountAssociationUrn;
import com.linkedin.salesleadaccount.LeadAccountAssociation;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static org.mockito.Mockito.*;


public class SalesLeadAccountAssociationsTransferServiceTest extends BaseEngineParJunitJupiterTest {
  @Mock
  private SalesLeadAccountCommonService _salesLeadAccountCommonService;
  @Mock
  private SalesSeatTransferCopyAssociationsClient _salesSeatTransferCopyAssociationsClient;
  @Mock
  private LixService _lixService;

  private SalesLeadAccountAssociationsTransferService _salesLeadAccountAssociationsTransferService;

  @BeforeEach
  public void setUp() {
    _salesLeadAccountAssociationsTransferService = new SalesLeadAccountAssociationsTransferService(
        _salesLeadAccountCommonService,
        _salesSeatTransferCopyAssociationsClient,
        _lixService);
  }

  @Test
  public void transferWhenSourceSeatHasNoLeadAccountAssociations() {
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(SOURCE_SEAT)).thenReturn(Task.value(new ArrayList<>()));
    runAndWait(_salesLeadAccountAssociationsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadAccountAssociationsBySeat(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateLeadAccountAssociations(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenSourceSeatHasTransferredAllLeadAccountAssociations() {
    List<LeadAccountAssociation> sourceSeatLeadAccountAssociations = new ArrayList<>();
    sourceSeatLeadAccountAssociations.add(new LeadAccountAssociation()
        .setLead(TEST_MEMBER).setAccount(getTestOrganizationUrn()));

    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(SOURCE_SEAT))
        .thenReturn(Task.value(sourceSeatLeadAccountAssociations));

    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>();
    ownershipTransferCopyAssociations.add(new OwnershipTransferCopyAssociation()
        .setSourceEntity(Urn.createFromTuple(
            SalesSavedLeadAccountAssociationUrn.ENTITY_TYPE,
            SOURCE_SEAT,
            2L,
            TEST_MEMBER.getMemberIdEntity())));
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(ownershipTransferCopyAssociations));

    runAndWait(_salesLeadAccountAssociationsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadAccountAssociationsBySeat(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateLeadAccountAssociations(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenNoLeadAccountAssociationsCanBeTransferred() {
    List<LeadAccountAssociation> sourceSeatLeadAccountAssociations = new ArrayList<>();
    sourceSeatLeadAccountAssociations.add(new LeadAccountAssociation()
        .setLead(TEST_MEMBER).setAccount(getTestOrganizationUrn()));
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(SOURCE_SEAT))
        .thenReturn(Task.value(sourceSeatLeadAccountAssociations));

    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(ownershipTransferCopyAssociations));

    List<LeadAccountAssociation> targetSeatLeadAccountAssociations = new ArrayList<>();
    targetSeatLeadAccountAssociations.add(new LeadAccountAssociation()
        .setLead(TEST_MEMBER_2).setAccount(getTestOrganizationUrn()));
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(TARGET_SEAT))
        .thenReturn(Task.value(targetSeatLeadAccountAssociations));

    runAndWait(_salesLeadAccountAssociationsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));
    verify(_salesLeadAccountCommonService, times(2)).findAllSavedLeadAccountAssociationsBySeat(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateLeadAccountAssociations(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllLeadAccountAssociationsAreSuccessfullyTransferred() {
    List<LeadAccountAssociation> sourceSeatLeadAccountAssociations = new ArrayList<>();
    sourceSeatLeadAccountAssociations.add(new LeadAccountAssociation()
        .setLead(TEST_MEMBER).setAccount(getTestOrganizationUrn()));
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(SOURCE_SEAT))
        .thenReturn(Task.value(sourceSeatLeadAccountAssociations));

    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(ownershipTransferCopyAssociations));

    List<LeadAccountAssociation> targetSeatLeadAccountAssociations = new ArrayList<>();
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(TARGET_SEAT))
        .thenReturn(Task.value(targetSeatLeadAccountAssociations));

    List<LeadAccountAssociation> newlyCreatedTargetSeatAssociations = new ArrayList<>();
    newlyCreatedTargetSeatAssociations.add(new LeadAccountAssociation()
        .setAccount(getTestOrganizationUrn())
        .setLead(TEST_MEMBER)
        .setCreator(TARGET_SEAT)
        .setContract(TARGET_CONTRACT));
    List<SalesLeadAccountAssociationResult> newTargetSeatLeadAccountAssociations = new ArrayList<>();
    newTargetSeatLeadAccountAssociations.add(new SalesLeadAccountAssociationResult().setAccountActionStatus(
        ActionStatus.SUCCESS));
    when(_salesLeadAccountCommonService.batchCreateLeadAccountAssociations(newlyCreatedTargetSeatAssociations, 12))
        .thenReturn(newTargetSeatLeadAccountAssociations);

    List<Long> successfullyCreatedCopyAssociations = new ArrayList<>(1);
    successfullyCreatedCopyAssociations.add(1L);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(successfullyCreatedCopyAssociations));

    runAndWait(_salesLeadAccountAssociationsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));
    verify(_salesLeadAccountCommonService, times(2)).findAllSavedLeadAccountAssociationsBySeat(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(1)).batchCreateLeadAccountAssociations(any(), any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllLeadAccountAssociationsAreNotSuccessfullyTransferred() {
    List<LeadAccountAssociation> sourceSeatLeadAccountAssociations = new ArrayList<>();
    sourceSeatLeadAccountAssociations.add(new LeadAccountAssociation()
        .setLead(TEST_MEMBER).setAccount(getTestOrganizationUrn()));
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(SOURCE_SEAT))
        .thenReturn(Task.value(sourceSeatLeadAccountAssociations));

    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>();
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(any(), any())).thenReturn(Task.value(ownershipTransferCopyAssociations));

    List<LeadAccountAssociation> targetSeatLeadAccountAssociations = new ArrayList<>();
    when(_salesLeadAccountCommonService.findAllSavedLeadAccountAssociationsBySeat(TARGET_SEAT))
        .thenReturn(Task.value(targetSeatLeadAccountAssociations));

    List<LeadAccountAssociation> newlyCreatedTargetSeatAssociations = new ArrayList<>();
    newlyCreatedTargetSeatAssociations.add(new LeadAccountAssociation()
        .setAccount(getTestOrganizationUrn())
        .setLead(TEST_MEMBER)
        .setCreator(TARGET_SEAT)
        .setContract(TARGET_CONTRACT));
    List<SalesLeadAccountAssociationResult> newTargetSeatLeadAccountAssociations = new ArrayList<>();
    newTargetSeatLeadAccountAssociations.add(new SalesLeadAccountAssociationResult().setAccountActionStatus(
        ActionStatus.INTERNAL_SERVER_ERROR));
    when(_salesLeadAccountCommonService.batchCreateLeadAccountAssociations(newlyCreatedTargetSeatAssociations, 12))
        .thenReturn(newTargetSeatLeadAccountAssociations);

    List<Long> successfullyCreatedCopyAssociations = new ArrayList<>(1);
    successfullyCreatedCopyAssociations.add(1L);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(successfullyCreatedCopyAssociations));

    runAndWaitException(_salesLeadAccountAssociationsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT), RestLiServiceException.class);
    verify(_salesLeadAccountCommonService, times(2)).findAllSavedLeadAccountAssociationsBySeat(any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(1)).batchCreateLeadAccountAssociations(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }
}
