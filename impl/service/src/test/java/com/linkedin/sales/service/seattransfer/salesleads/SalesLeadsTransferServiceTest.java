package com.linkedin.sales.service.seattransfer.salesleads;

import com.linkedin.common.urn.Urn;
import com.linkedin.lss.salesleadaccount.ActionStatus;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.lss.salesleadaccount.SalesLeadActionResult;
import com.linkedin.lss.salesleadaccount.SalesLeadActionResultArray;
import com.linkedin.lss.salesleadaccount.services.common.SalesLeadAccountCommonService;
import com.linkedin.ownershiptransfer.OwnershipTransferCopyAssociation;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.junitjupiter.BaseEngineJUnitJupiterTest;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.client.seattransfer.SalesSeatTransferCopyAssociationsClient;
import com.linkedin.sales.service.LixService;
import com.linkedin.sales.service.leadaccount.SavedLeadService;
import com.linkedin.sales.urn.SalesSavedLeadUrn;
import com.linkedin.salesleadaccount.SalesLead;
import java.util.ArrayList;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.util.List;
import org.mockito.Mock;

import static com.linkedin.sales.service.seattransfer.helpers.SalesSeatTransferTestHelpers.*;
import static org.mockito.Mockito.*;


public class SalesLeadsTransferServiceTest extends BaseEngineJUnitJupiterTest {

  @Mock
  private SalesSeatTransferCopyAssociationsClient _salesSeatTransferCopyAssociationsClient;
  @Mock
  private SalesLeadAccountCommonService _salesLeadAccountCommonService;
  @Mock
  private SavedLeadService _savedLeadService;
  @Mock
  private LixService _lixService;
  private SalesLeadsTransferService _salesLeadsTransferService;

  @BeforeEach
  public void setUp() {
    _salesLeadsTransferService = new SalesLeadsTransferService(_salesSeatTransferCopyAssociationsClient,
        _salesLeadAccountCommonService, _savedLeadService, _lixService);
  }

  @Test
  public void transferWhenTargetSeatExceedsLimitForSavedLeads() {
    when(_salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(TARGET_SEAT)).thenReturn(Task.value(10001));

    runAndWait(_salesLeadsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedLeadCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(0)).findAllSavedLeadsForSeatUpToLimit(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateSavedLeads(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenSourceSeatDoesNotHaveSavedLeads() {
    when(_salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(TARGET_SEAT)).thenReturn(Task.value(1));

    // Create empty SalesLead list to simulate sourceSeat having no saved leads
    List<SalesLead> emptySavedLeadList = new ArrayList<>();
    when(_salesLeadAccountCommonService.findAllSavedLeadsForSeatUpToLimit(SOURCE_SEAT, 10000))
        .thenReturn(Task.value(emptySavedLeadList));

    runAndWait(_salesLeadsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedLeadCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadsForSeatUpToLimit(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateSavedLeads(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSourceSeatSavedLeadsAreAlreadyTransferred() {
    when(_salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(TARGET_SEAT)).thenReturn(Task.value(1));

    // Create SalesLead list with one SalesLead
    List<SalesLead> savedLeadList = new ArrayList<>(1);
    savedLeadList.add(new SalesLead().setMember(TEST_MEMBER));

    when(_salesLeadAccountCommonService.findAllSavedLeadsForSeatUpToLimit(SOURCE_SEAT, 10000))
        .thenReturn(Task.value(savedLeadList));

    // Create OwnershipTransferCopyAssociation list with SalesLead already having a transfer record
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(1);
    ownershipTransferCopyAssociations.add(
        new OwnershipTransferCopyAssociation()
            .setSourceEntity(
                createSalesSavedLeadUrn(SOURCE_SEAT, TEST_MEMBER.getMemberIdEntity())));

    List<Urn> salesSavedLeadUrnList = createSalesSavedLeadUrnList(savedLeadList, SOURCE_SEAT);
    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(salesSavedLeadUrnList, TARGET_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    runAndWait(_salesLeadsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedLeadCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadsForSeatUpToLimit(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(0)).batchCreateSavedLeads(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(0)).createCopyAssociations(any());
    verify(_lixService, times(0)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSavedLeadsAreNotSuccessfullyTransferred() {
    when(_salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(TARGET_SEAT)).thenReturn(Task.value(0));

    // Create SalesLead list with one SalesLead
    List<SalesLead> savedLeadList = new ArrayList<>(1);
    savedLeadList.add(new SalesLead().setMember(TEST_MEMBER));

    when(_salesLeadAccountCommonService.findAllSavedLeadsForSeatUpToLimit(SOURCE_SEAT, 10000))
        .thenReturn(Task.value(savedLeadList));

    // Create empty OwnershipTransferCopyAssociation list because nothing has been transferred previously
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(0);
    List<Urn> savedLeadUrnList = new ArrayList<>();

    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(savedLeadUrnList, SOURCE_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    // Create SalesLeadActionResultArray with a result that has a failed status to indicate saved lead was not created
    SalesLeadActionResultArray salesLeadActionResultArray = new SalesLeadActionResultArray();
    salesLeadActionResultArray.add(
        new SalesLeadActionResult()
            .setStatus(ActionStatus.INTERNAL_SERVER_ERROR)
            .setMemberId(TEST_MEMBER.getMemberIdEntity()));

    when(_salesLeadAccountCommonService.batchCreateSavedLeads(any(), any())).thenReturn(salesLeadActionResultArray);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(new ArrayList<>()));

    runAndWaitException(_salesLeadsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT), RestLiServiceException.class);
    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedLeadCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadsForSeatUpToLimit(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(1)).batchCreateSavedLeads(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }

  @Test
  public void transferWhenAllSavedLeadsAreTransferredSuccessfully() {
    when(_salesLeadAccountCommonService.getTotalSavedLeadCountForSeat(TARGET_SEAT)).thenReturn(Task.value(0));

    // Create SalesLead list with one SalesLead
    List<SalesLead> savedLeadList = new ArrayList<>(1);
    savedLeadList.add(new SalesLead().setMember(TEST_MEMBER));

    when(_salesLeadAccountCommonService.findAllSavedLeadsForSeatUpToLimit(SOURCE_SEAT, 10000))
        .thenReturn(Task.value(savedLeadList));

    // Create empty OwnershipTransferCopyAssociation list because nothing has been transferred previously
    List<OwnershipTransferCopyAssociation> ownershipTransferCopyAssociations = new ArrayList<>(0);
    List<Urn> savedLeadUrnList = new ArrayList<>();

    when(_salesSeatTransferCopyAssociationsClient.findPreviousTransfers(savedLeadUrnList, SOURCE_CONTRACT))
        .thenReturn(Task.value(ownershipTransferCopyAssociations));

    // Create SalesLeadActionResultArray with a result that has a success status to indicate saved lead was created
    SalesLeadActionResultArray salesLeadActionResultArray = new SalesLeadActionResultArray();
    salesLeadActionResultArray.add(
        new SalesLeadActionResult()
            .setStatus(ActionStatus.SUCCESS)
            .setMemberId(TEST_MEMBER.getMemberIdEntity()));

    when(_salesLeadAccountCommonService.batchCreateSavedLeads(any(), any())).thenReturn(salesLeadActionResultArray);

    // Create OwnershipTransferCopyAssociations with copyAssociationsClient
    List<Long> newTransferRecordIds = new ArrayList<>();
    newTransferRecordIds.add(1L);
    when(_salesSeatTransferCopyAssociationsClient.createCopyAssociations(any())).thenReturn(Task.value(newTransferRecordIds));

    runAndWait(_salesLeadsTransferService.transfer(getOwnershipTransferRequest(), SOURCE_SEAT));

    verify(_salesLeadAccountCommonService, times(1)).getTotalSavedLeadCountForSeat(any());
    verify(_salesLeadAccountCommonService, times(1)).findAllSavedLeadsForSeatUpToLimit(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).findPreviousTransfers(any(), any());
    verify(_salesLeadAccountCommonService, times(1)).batchCreateSavedLeads(any(), any());
    verify(_salesSeatTransferCopyAssociationsClient, times(1)).createCopyAssociations(any());
    verify(_lixService, times(1)).getLixTreatment(any(), any(), any());
  }

  public SalesSavedLeadUrn createSalesSavedLeadUrn(SeatUrn owner, long memberId) {
    return (SalesSavedLeadUrn) Urn.createFromTuple(
        SalesSavedLeadUrn.ENTITY_TYPE,
        owner,
        memberId
    );
  }

  public List<Urn> createSalesSavedLeadUrnList(List<SalesLead> savedLeads, SeatUrn owner) {
    return savedLeads.stream().map(
        savedLead -> (SalesSavedLeadUrn) Urn.createFromTuple(
            SalesSavedLeadUrn.ENTITY_TYPE,
            owner,
            savedLead.getMember().getMemberIdEntity())
    ).collect(Collectors.toList());
  }
}
