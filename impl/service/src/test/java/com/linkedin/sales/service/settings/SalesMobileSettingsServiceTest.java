package com.linkedin.sales.service.settings;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.data.template.StringArray;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.MobileSettings;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesseatpreference.CalendarSyncSettings;
import com.linkedin.salesseatpreference.SalesMobileSettings;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static java.lang.Boolean.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SalesMobileSettingsServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN = new ContractUrn(101L);
  private static final ContractUrn DUMMY_CONTRACT_URN = new ContractUrn(0L);
  private static final SeatUrn SEAT_URN = new SeatUrn(2001L);
  private static final String CALENDAR1 = "cal1";
  private static final String CALENDAR2 = "cal2";
  private final Long LAST_RATE_TIME = 1024L;
  private final SalesMobileSettings SALES_MOBILE_SETTINGS = createSalesMobileSettings();
  private final MobileSettings MOBILE_SETTINGS = createMobileSettings();

  private SalesMobileSettingsService _salesMobileSettingsService;

  @Mock
  private LssSeatSettingDB _lssSeatSettingDB;

  @BeforeTest(alwaysRun = true)
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesMobileSettingsService = new SalesMobileSettingsService(_lssSeatSettingDB);
  }

  @Test
  public void testCreateSalesSeatSettingSuccess() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateMobileSetting(any(), any());
    PatchRequest<SalesMobileSettings> patchRequest = PatchGenerator.diffEmpty(SALES_MOBILE_SETTINGS);
    UpdateResponse result = await(_salesMobileSettingsService.updateMobileSetting(SEAT_URN, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(result.getStatus());
  }

  @Test
  public void testCreateSalesSeatSettingSuccess1() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateMobileSetting(any(), any());
    // Partial update by setting only one value in SalesMobileSettings
    SalesMobileSettings SalesMobileSettings = new SalesMobileSettings().setCallLoggingEnabled(FALSE);
    PatchRequest<SalesMobileSettings> patchRequest = PatchGenerator.diffEmpty(SalesMobileSettings);
    UpdateResponse response = await(_salesMobileSettingsService.updateMobileSetting(SEAT_URN, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(response.getStatus());
  }


  @Test
  public void testUpdateSalesMobileSettingsSuccess() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateMobileSetting(any(), any());
    // Partial update by setting only one value in SalesMobileSettings
    SalesMobileSettings SalesMobileSettings = new SalesMobileSettings().setCallLoggingEnabled(FALSE);
    PatchRequest<SalesMobileSettings> patchRequest = PatchGenerator.diffEmpty(SalesMobileSettings);
    UpdateResponse response = await(_salesMobileSettingsService.updateMobileSetting(SEAT_URN, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(response.getStatus());
  }

  @Test
  public void testGetSalesMobileSettingsSuccess() {
    Mockito.doReturn(Task.value(MOBILE_SETTINGS)).when(_lssSeatSettingDB).getMobileSetting(SEAT_URN);
    SalesMobileSettings result = await(_salesMobileSettingsService.getMobileSetting(SEAT_URN));
    assertThat(result).isEqualTo(SALES_MOBILE_SETTINGS);
  }

  @Test
  public void testGetSalesMobileSettingsNotFound() {
    Mockito.doReturn(Task.failure(new EntityNotFoundException(null, "test")))
        .when(_lssSeatSettingDB).getMobileSetting(SEAT_URN);
    SalesMobileSettings result = await(_salesMobileSettingsService.getMobileSetting(SEAT_URN));
    assertThat(result).isEqualTo(new SalesMobileSettings().setSeat(SEAT_URN).setContract(DUMMY_CONTRACT_URN));
  }

  @Test
  public void testGetSalesMobileSettingsFail() {
    Mockito.doReturn(Task.failure(new RuntimeException("test"))).when(_lssSeatSettingDB).getMobileSetting(SEAT_URN);
    assertThatExceptionOfType(PromiseException.class)
        .isThrownBy(() -> {await(_salesMobileSettingsService.getMobileSetting(SEAT_URN));})
        .withCauseInstanceOf(RuntimeException.class);
  }

  @Test
  public void testConvertToEspressoModel() {
    MobileSettings mobileSettings = _salesMobileSettingsService.convertToEspressoModel(SALES_MOBILE_SETTINGS);
    assertThat(mobileSettings).isEqualTo(MOBILE_SETTINGS);
  }

  @Test
  public void testConvertToSalesMobileSettings() {
    SalesMobileSettings SalesMobileSettings = _salesMobileSettingsService.convertToSalesMobileSettings(MOBILE_SETTINGS, SEAT_URN);
    assertThat(SalesMobileSettings).isEqualTo(SALES_MOBILE_SETTINGS);
  }

  @Test
  public void testConvertToSalesMobileSettingsWithNullValues() {
    MobileSettings mobileSettings = new MobileSettings();
    mobileSettings.contractUrn = CONTRACT_URN.toString();
    SalesMobileSettings expectedSalesMobileSettings = new SalesMobileSettings().setContract(CONTRACT_URN).setSeat(SEAT_URN);
    SalesMobileSettings SalesMobileSettings = _salesMobileSettingsService.convertToSalesMobileSettings(mobileSettings, SEAT_URN);
    assertThat(SalesMobileSettings).isEqualTo(expectedSalesMobileSettings);
  }

  private MobileSettings createMobileSettings() {
    MobileSettings mobileSettings = new MobileSettings();
    mobileSettings.rateTheAppLastShowAt = LAST_RATE_TIME;
    mobileSettings.isCallLoggingEnabled = TRUE;
    mobileSettings.calendarSyncSettings = new com.linkedin.sales.espresso.CalendarSyncSettings();
    mobileSettings.calendarSyncSettings.isAllEventsForTodayShown = FALSE;
    mobileSettings.calendarSyncSettings.isEnabled = TRUE;
    mobileSettings.calendarSyncSettings.isEventWithoutAttendeesShown = FALSE;
    mobileSettings.calendarSyncSettings.syncedCalendars = ImmutableList.of(CALENDAR1, CALENDAR2);
    mobileSettings.contractUrn = CONTRACT_URN.toString();
    return mobileSettings;
  }

  private SalesMobileSettings createSalesMobileSettings() {
    SalesMobileSettings salesMobileSettings = new SalesMobileSettings();
    salesMobileSettings.setRateTheAppLastShowAt(LAST_RATE_TIME);
    salesMobileSettings.setCallLoggingEnabled(TRUE);
    salesMobileSettings.setCalendarSync(new CalendarSyncSettings()
        .setAllEventsForTodayShown(FALSE)
        .setEnabled(TRUE)
        .setEventWithoutAttendeesShown(FALSE)
        .setSyncedCalendars(new StringArray(CALENDAR1, CALENDAR2)));
    salesMobileSettings.setContract(CONTRACT_URN);
    salesMobileSettings.setSeat(SEAT_URN);
    return salesMobileSettings;
  }
}
