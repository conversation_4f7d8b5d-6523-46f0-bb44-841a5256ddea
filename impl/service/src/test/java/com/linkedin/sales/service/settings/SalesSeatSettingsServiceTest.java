package com.linkedin.sales.service.settings;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.crm.SalesConnectedCrmSettingType;
import com.linkedin.data.template.BooleanMap;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSeatSettingDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.AIMessageType;
import com.linkedin.sales.espresso.AIMessagingPreferences;
import com.linkedin.sales.espresso.AccountDashboardColumnSettings;
import com.linkedin.sales.espresso.AccountDashboardSettings;
import com.linkedin.sales.espresso.CrmAutoSaveSettingType;
import com.linkedin.sales.espresso.CrmAutoSaveSettings;
import com.linkedin.sales.espresso.SeatSetting;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.salesseatpreference.AccountDashboardColumnSettingsArray;
import com.linkedin.salesseatpreference.CrmSeatSettingArray;
import com.linkedin.salesseatpreference.SalesSeatSetting;
import com.linkedin.salesseatpreference.VisibilityAsProfileViewer;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import org.testng.annotations.Test;

import static java.lang.Boolean.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


public class SalesSeatSettingsServiceTest extends ServiceUnitTest {

  private static final ContractUrn CONTRACT_URN = new ContractUrn(101L);
  private static final ContractUrn DUMMY_CONTRACT_URN = new ContractUrn(0L);
  private static final SeatUrn SEAT_URN_1 = new SeatUrn(2001L);
  private static final SeatUrn SEAT_URN_2 = new SeatUrn(2002L);

  private static final String ATTR1 = "attr1";
  private static final String ATTR2 = "attr2";
  private static final Map<CharSequence, Boolean>
      EMAIL_PREFERENCES_DB_FORMAT = ImmutableMap.of(ATTR1, TRUE, ATTR2, FALSE);
  private static final Map<String, Boolean>
      MAP = ImmutableMap.of(ATTR1, TRUE, ATTR2, FALSE);
  private static final BooleanMap EMAIL_PREFERENCES_CLIENT_FORMAT = new BooleanMap(MAP);
  private static final String SIGNATURE_TEXT = "Best, XX";
  private static final long LAST_LOGGED_IN_TIME = 1L;
  private static final long DISMISSED_BUYER_INTENT_CARD_TIME = 987654321987654321L;
  private static final long DISMISSED_ACCOUNT_IQ_CARD_TIME = 987654321987654411L;
  private static final long ONBOARDING_COMPLETED_TIME = 1L;
  private static final long LAST_VIEWED_LIST_ID = 1000L;
  private static final String COLUMN1 = "BUYER_INTENT";
  private static final String COLUMN2 = "NOTES";
  private static final  long TRANSFORM_ONBOARDING_COMPLETE_TIME = 987654321123456789L;

  private final String LAST_PRODUCT_URN = "urn:li:fs_salesProduct:123";
  private final SalesSeatSetting SALES_SEAT_SETTING1 = createSalesSeatSetting1();
  private final SalesSeatSetting SALES_SEAT_SETTING2 = createSalesSeatSetting2();

  private final SeatSetting SEAT_SETTING_1 = createSeatSetting1();
  private final SeatSetting SEAT_SETTING_2 = createSeatSetting2();


  private SalesSeatSettingsService _salesSeatSettingsService;

  @Mock
  private LssSeatSettingDB _lssSeatSettingDB;

  @BeforeTest(alwaysRun = true)
  public void setup() {
    MockitoAnnotations.initMocks(this);
    _salesSeatSettingsService = new SalesSeatSettingsService(_lssSeatSettingDB);
  }

  @Test
  public void testCreateSalesSeatSettingSuccess() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateSeatSetting(any(), any());
    PatchRequest<SalesSeatSetting> patchRequest = PatchGenerator.diffEmpty(SALES_SEAT_SETTING1);
    UpdateResponse result =
        await(_salesSeatSettingsService.updateSeatSetting(SEAT_URN_1, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(result.getStatus());
  }

  @Test
  public void testUpdateSalesSeatSettingSuccess() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateSeatSetting(any(), any());
    // Partial update by setting only one value in salesSeatSetting
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting().setTeamlinkOptedOut(FALSE).
        setVisibilityAsProfileViewer(VisibilityAsProfileViewer.FULL);
    PatchRequest<SalesSeatSetting> patchRequest = PatchGenerator.diffEmpty(salesSeatSetting);
    UpdateResponse result =
        await(_salesSeatSettingsService.updateSeatSetting(SEAT_URN_1, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(result.getStatus());
  }

  @Test
  public void testUpdateSalesSeatSettingVisibilityUnknown() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateSeatSetting(any(), any());
    // Partial update by setting only one value in salesSeatSetting
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting().setVisibilityAsProfileViewer(
        VisibilityAsProfileViewer.$UNKNOWN);
    PatchRequest<SalesSeatSetting> patchRequest = PatchGenerator.diffEmpty(salesSeatSetting);
    UpdateResponse result =
        await(_salesSeatSettingsService.updateSeatSetting(SEAT_URN_1, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(result.getStatus());
  }

  @Test
  public void testUpdateEnableMessageLearningSalesSeatSettingSuccess() {
    Mockito.doReturn(Task.value(HttpStatus.S_200_OK)).when(_lssSeatSettingDB).updateSeatSetting(any(), any());
    // Partial update by setting only one value in salesSeatSetting
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting().setMessageLearningEnabled(TRUE);
    PatchRequest<SalesSeatSetting> patchRequest = PatchGenerator.diffEmpty(salesSeatSetting);
    UpdateResponse result =
        await(_salesSeatSettingsService.updateSeatSetting(SEAT_URN_1, patchRequest));
    assertThat(HttpStatus.S_200_OK).isEqualTo(result.getStatus());
  }

  @Test
  public void testGetSalesSeatSettingSuccess() {
    Mockito.doReturn(Task.value(SEAT_SETTING_1)).when(_lssSeatSettingDB).getSeatSetting(SEAT_URN_1);
    SalesSeatSetting result =
        await(_salesSeatSettingsService.getSeatSetting(SEAT_URN_1));
    assertThat(result).isEqualTo(SALES_SEAT_SETTING1);
  }

  @Test
  public void testBatchGetSalesSeatSettingSuccess() {
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(SEAT_URN_1, SEAT_URN_2);
    Map<SeatUrn, SeatSetting> seatSettingsMap = ImmutableMap.of(SEAT_URN_1, SEAT_SETTING_1, SEAT_URN_2, SEAT_SETTING_2);
    Mockito.doReturn(Task.value(seatSettingsMap)).when(_lssSeatSettingDB).batchGetSeatSettings(seatUrnSet);
    Map<SeatUrn, SalesSeatSetting> salesSeatSettingsMap =
        await(_salesSeatSettingsService.batchGet(seatUrnSet));
    assertThat(salesSeatSettingsMap.size()).isEqualTo(2);
    for(SeatUrn seatUrn: seatUrnSet) {
      assertThat(salesSeatSettingsMap.containsKey(seatUrn)).isTrue();
      assertThat(salesSeatSettingsMap.get(seatUrn).hasOnboardingCompletedAt()).isTrue();
    }
  }

  @Test
  public void testBatchGetSalesSeatSettingOneSuccessOneNotFound() {
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(SEAT_URN_1, SEAT_URN_2);
    Map<SeatUrn, SeatSetting> seatSettingsMap = ImmutableMap.of(SEAT_URN_1, SEAT_SETTING_1);
    Mockito.doReturn(Task.value(seatSettingsMap)).when(_lssSeatSettingDB).batchGetSeatSettings(seatUrnSet);
    Map<SeatUrn, SalesSeatSetting> saleSeatSettingsMap =
        await(_salesSeatSettingsService.batchGet(seatUrnSet));
    assertThat(saleSeatSettingsMap.size()).isEqualTo(1);
    assertThat(saleSeatSettingsMap.containsKey(SEAT_URN_1)).isTrue();
    assertThat(saleSeatSettingsMap.get(SEAT_URN_1)).isEqualTo(SALES_SEAT_SETTING1);
  }

  @Test
  public void testBatchGetSalesSeatSettingNoneFound() {
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(SEAT_URN_1, SEAT_URN_2);
    Map<SeatUrn, SeatSetting> seatSettingsMap = ImmutableMap.of();
    Mockito.doReturn(Task.value(seatSettingsMap)).when(_lssSeatSettingDB).batchGetSeatSettings(seatUrnSet);
    Map<SeatUrn, SalesSeatSetting> saleSeatSettingsMap =
        await(_salesSeatSettingsService.batchGet(seatUrnSet));
    assertThat(saleSeatSettingsMap.size()).isEqualTo(0);
  }

  @Test
  public void testGetSalesSeatSettingNotFound() {
    Mockito.doReturn(Task.failure(new EntityNotFoundException(null, "test"))).when(_lssSeatSettingDB)
        .getSeatSetting(SEAT_URN_1);
    SalesSeatSetting result = await(_salesSeatSettingsService.getSeatSetting(SEAT_URN_1));
    assertThat(result).isEqualTo(new SalesSeatSetting().setSeat(SEAT_URN_1).setContract(DUMMY_CONTRACT_URN));
  }

  @Test
  public void testGetSalesSeatSettingFail() {
    Mockito.doReturn(Task.failure(new RuntimeException("test"))).when(_lssSeatSettingDB).getSeatSetting(SEAT_URN_1);
    assertThatExceptionOfType(PromiseException.class)
            .isThrownBy(() -> {await(_salesSeatSettingsService.getSeatSetting(SEAT_URN_1));})
            .withCauseInstanceOf(RuntimeException.class);
  }

  @Test
  public void testConvertToEspressoModel() {
    SeatSetting seatSetting = _salesSeatSettingsService.convertToEspressoModel(SALES_SEAT_SETTING1);
    assertThat(SEAT_SETTING_1.contractUrn).isEqualTo(seatSetting.contractUrn);
    assertThat(SEAT_SETTING_1.emailPreferences).isEqualTo(seatSetting.emailPreferences);
    assertThat(SEAT_SETTING_1.inMailMessageSignature).isEqualTo(seatSetting.inMailMessageSignature);
    assertThat(SEAT_SETTING_1.pushNotificationEnabled).isEqualTo(seatSetting.pushNotificationEnabled);
    assertThat(SEAT_SETTING_1.teamlinkOptedOut).isEqualTo(seatSetting.teamlinkOptedOut);
    assertThat(SEAT_SETTING_1.usageReportingFlagshipDataOptOut).isEqualTo(seatSetting.usageReportingFlagshipDataOptOut);
    assertThat(SEAT_SETTING_1.visibilityAsProfileViewer).isEqualTo(seatSetting.visibilityAsProfileViewer);
    assertThat(SEAT_SETTING_1.lastLoggedInTime).isEqualTo(seatSetting.lastLoggedInTime);
    assertThat(SEAT_SETTING_1.onboardingCompletedTime).isEqualTo(seatSetting.onboardingCompletedTime);
    assertThat(SEAT_SETTING_1.dismissedBuyerIntentCardTime).isEqualTo(seatSetting.dismissedBuyerIntentCardTime);
    assertThat(SEAT_SETTING_1.dismissedAccountIqCardTime).isEqualTo(seatSetting.dismissedAccountIqCardTime);
    assertThat(SEAT_SETTING_1.aiMessagingPreferences.lastUsedProductUrn).isEqualTo(seatSetting.aiMessagingPreferences.lastUsedProductUrn.toString());
    assertThat(SEAT_SETTING_1.aiMessagingPreferences.lastUsedMessageType.name()).isEqualTo(seatSetting.aiMessagingPreferences.lastUsedMessageType.name());
    assertThat(SEAT_SETTING_1.accountDashboardSettings.lastViewedList).isEqualTo(seatSetting.accountDashboardSettings.lastViewedList);
    assertThat(SEAT_SETTING_1.accountDashboardSettings.columns).isEqualTo(seatSetting.accountDashboardSettings.columns);
    assertThat(SEAT_SETTING_1.transformOnboardingCompletedTime).isEqualTo(seatSetting.transformOnboardingCompletedTime);
    assertThat(SEAT_SETTING_1.messageLearningEnabled).isEqualTo(seatSetting.messageLearningEnabled);
  }

  @Test
  public void testConvertToSalesSeatSetting() {
    SalesSeatSetting salesSeatSetting = _salesSeatSettingsService.convertToSalesSeatSetting(SEAT_SETTING_1, SEAT_URN_1);
    assertThat(salesSeatSetting).isEqualTo(SALES_SEAT_SETTING1);
  }

  @Test
  public void testConvertToSalesSeatSettingWithNullValues() {
    SeatSetting seatSetting = new SeatSetting();
    seatSetting.contractUrn = CONTRACT_URN.toString();
    SalesSeatSetting expectedSalesSeatSetting = new SalesSeatSetting().setContract(CONTRACT_URN).setSeat(SEAT_URN_1);
    SalesSeatSetting salesSeatSetting = _salesSeatSettingsService.convertToSalesSeatSetting(seatSetting, SEAT_URN_1);
    assertThat(salesSeatSetting).isEqualTo(expectedSalesSeatSetting);
  }

  private SeatSetting createSeatSetting1() {
    SeatSetting seatSetting = new SeatSetting();
    seatSetting.emailPreferences = EMAIL_PREFERENCES_DB_FORMAT;
    seatSetting.visibilityAsProfileViewer = com.linkedin.sales.espresso.VisibilityAsProfileViewer.ANONYMOUS;
    seatSetting.usageReportingFlagshipDataOptOut = TRUE;
    seatSetting.messageLearningEnabled = FALSE;
    seatSetting.teamlinkOptedOut = TRUE;
    seatSetting.pushNotificationEnabled = TRUE;
    seatSetting.inMailMessageSignature = SIGNATURE_TEXT;
    seatSetting.contractUrn = CONTRACT_URN.toString();
    seatSetting.onboardingCompletedTime = ONBOARDING_COMPLETED_TIME;
    seatSetting.lastLoggedInTime = LAST_LOGGED_IN_TIME;
    seatSetting.dismissedBuyerIntentCardTime = DISMISSED_BUYER_INTENT_CARD_TIME;
    seatSetting.dismissedAccountIqCardTime = DISMISSED_ACCOUNT_IQ_CARD_TIME;
    seatSetting.transformOnboardingCompletedTime = TRANSFORM_ONBOARDING_COMPLETE_TIME;
    ArrayList<CrmAutoSaveSettings> list = new ArrayList<>();
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED, true));
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_OWNED_ACCOUNTS_ENABLED, true));
    seatSetting.setCrmAutoSavePreferences(list);
    AIMessagingPreferences aiMessagingPreferences = new AIMessagingPreferences();
    aiMessagingPreferences.setLastUsedProductUrn(LAST_PRODUCT_URN);
    aiMessagingPreferences.setLastUsedMessageType(AIMessageType.INTRODUCTION);
    seatSetting.setAiMessagingPreferences(aiMessagingPreferences);
    AccountDashboardSettings accountDashboardSettings = new AccountDashboardSettings();
    accountDashboardSettings.setLastViewedList(LAST_VIEWED_LIST_ID);
    ArrayList<AccountDashboardColumnSettings> columnSettings = new ArrayList<>();
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN1, true));
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN2, false));
    accountDashboardSettings.setColumns(columnSettings);
    seatSetting.setAccountDashboardSettings(accountDashboardSettings);
    return seatSetting;
  }

  private SeatSetting createSeatSetting2() {
    SeatSetting seatSetting = new SeatSetting();
    seatSetting.emailPreferences = EMAIL_PREFERENCES_DB_FORMAT;
    seatSetting.visibilityAsProfileViewer = com.linkedin.sales.espresso.VisibilityAsProfileViewer.ANONYMOUS;
    seatSetting.usageReportingFlagshipDataOptOut = TRUE;
    seatSetting.messageLearningEnabled = FALSE;
    seatSetting.teamlinkOptedOut = TRUE;
    seatSetting.pushNotificationEnabled = TRUE;
    seatSetting.inMailMessageSignature = SIGNATURE_TEXT;
    seatSetting.contractUrn = CONTRACT_URN.toString();
    seatSetting.onboardingCompletedTime = ONBOARDING_COMPLETED_TIME;
    seatSetting.lastLoggedInTime = LAST_LOGGED_IN_TIME;
    AIMessagingPreferences aiMessagingPreferences = new AIMessagingPreferences();
    aiMessagingPreferences.setLastUsedProductUrn(LAST_PRODUCT_URN);
    aiMessagingPreferences.setLastUsedMessageType(AIMessageType.SALES);
    seatSetting.setAiMessagingPreferences(aiMessagingPreferences);
    AccountDashboardSettings accountDashboardSettings = new AccountDashboardSettings();
    accountDashboardSettings.setLastViewedList(LAST_VIEWED_LIST_ID);
    ArrayList<AccountDashboardColumnSettings> columnSettings = new ArrayList<>();
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN1, true));
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN2, false));
    accountDashboardSettings.setColumns(columnSettings);
    seatSetting.setAccountDashboardSettings(accountDashboardSettings);
    return seatSetting;
  }

  private SalesSeatSetting createSalesSeatSetting1() {
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting();
    salesSeatSetting.setEmailPreferences(EMAIL_PREFERENCES_CLIENT_FORMAT);
    salesSeatSetting.setVisibilityAsProfileViewer(VisibilityAsProfileViewer.ANONYMOUS);
    salesSeatSetting.setUsageReportingFlagshipDataOptOut(TRUE);
    salesSeatSetting.setMessageLearningEnabled(FALSE);
    salesSeatSetting.setTeamlinkOptedOut(TRUE);
    salesSeatSetting.setPushNotificationEnabled(TRUE);
    salesSeatSetting.setContract(CONTRACT_URN);
    salesSeatSetting.setInMailMessageSignature(SIGNATURE_TEXT);
    salesSeatSetting.setSeat(SEAT_URN_1);
    salesSeatSetting.setOnboardingCompletedAt(ONBOARDING_COMPLETED_TIME);
    salesSeatSetting.setLastLoggedInAt(LAST_LOGGED_IN_TIME);
    salesSeatSetting.setDismissedBuyerIntentCardAt(DISMISSED_BUYER_INTENT_CARD_TIME);
    salesSeatSetting.setDismissedAccountIqCardAt(DISMISSED_ACCOUNT_IQ_CARD_TIME);
    salesSeatSetting.setTransformOnboardingCompletedAt(TRANSFORM_ONBOARDING_COMPLETE_TIME);
    salesSeatSetting.setCrmAutoSaveSettings(new CrmSeatSettingArray(
        new com.linkedin.salesseatpreference.CrmSeatSetting().setType(
        SalesConnectedCrmSettingType.AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED).setValue(true),
        new com.linkedin.salesseatpreference.CrmSeatSetting().setType(
            SalesConnectedCrmSettingType.AUTO_SAVE_OWNED_ACCOUNTS_ENABLED).setValue(true))
    );
    try {
      salesSeatSetting.setAiMessagingPreferences(new com.linkedin.salesseatpreference.AIMessagingPreferences().setLastUsedProduct(
          Urn.createFromString(LAST_PRODUCT_URN)).setLastUsedAIMessageType(
          com.linkedin.salesseatpreference.AIMessageType.INTRODUCTION));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }

    salesSeatSetting.setAccountDashboardSettings(new com.linkedin.salesseatpreference.AccountDashboardSettings()
        .setLastViewedList(LAST_VIEWED_LIST_ID)
        .setColumns(new AccountDashboardColumnSettingsArray(
            new com.linkedin.salesseatpreference.AccountDashboardColumnSettings().setColumn(COLUMN1).setUserSelected(true),
            new com.linkedin.salesseatpreference.AccountDashboardColumnSettings().setColumn(COLUMN2).setUserSelected(false)
        )));
    return salesSeatSetting;
  }

  private SalesSeatSetting createSalesSeatSetting2() {
    SalesSeatSetting salesSeatSetting = new SalesSeatSetting();
    salesSeatSetting.setEmailPreferences(EMAIL_PREFERENCES_CLIENT_FORMAT);
    salesSeatSetting.setVisibilityAsProfileViewer(VisibilityAsProfileViewer.ANONYMOUS);
    salesSeatSetting.setUsageReportingFlagshipDataOptOut(TRUE);
    salesSeatSetting.setTeamlinkOptedOut(TRUE);
    salesSeatSetting.setPushNotificationEnabled(TRUE);
    salesSeatSetting.setContract(CONTRACT_URN);
    salesSeatSetting.setInMailMessageSignature(SIGNATURE_TEXT);
    salesSeatSetting.setSeat(SEAT_URN_2);
    salesSeatSetting.setOnboardingCompletedAt(ONBOARDING_COMPLETED_TIME);
    salesSeatSetting.setLastLoggedInAt(LAST_LOGGED_IN_TIME);
    try {
      salesSeatSetting.setAiMessagingPreferences(new com.linkedin.salesseatpreference.AIMessagingPreferences().setLastUsedProduct(
          Urn.createFromString(LAST_PRODUCT_URN)).setLastUsedAIMessageType(
          com.linkedin.salesseatpreference.AIMessageType.SALES));
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
    salesSeatSetting.setAccountDashboardSettings(new com.linkedin.salesseatpreference.AccountDashboardSettings()
        .setLastViewedList(LAST_VIEWED_LIST_ID)
        .setColumns(new AccountDashboardColumnSettingsArray(
            new com.linkedin.salesseatpreference.AccountDashboardColumnSettings().setColumn(COLUMN1).setUserSelected(true),
            new com.linkedin.salesseatpreference.AccountDashboardColumnSettings().setColumn(COLUMN2).setUserSelected(false)
        )));
    return salesSeatSetting;
  }
}
