package com.linkedin.sales.service.sharing;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.client.util.PatchGenerator;
import com.linkedin.restli.common.CompoundKey;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.restli.server.UpdateResponse;
import com.linkedin.sales.ds.db.LssSharingDB;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.ResourcePolicyView;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.sales.service.acl.AclServiceDispatcher;
import com.linkedin.sales.service.common.ServiceUnitTest;
import com.linkedin.sales.service.utils.ServiceConstants;
import com.linkedin.salessharing.AccessAction;
import com.linkedin.salessharing.AccessDecision;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.salessharing.SharingRole;
import com.linkedin.util.collections.list.PaginatedList;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.assertj.core.util.Sets;
import org.mockito.Mockito;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;


/**
 * unit test class for SalesSharingServiceTest
 * <AUTHOR>
 */
public class SalesSharingServiceTest extends ServiceUnitTest {
  private static final long CONTRACT_ID = 100L;
  private static final long CONTRACT_ID1 = 101L;
  private static final long SEAT_ID = 2000L;
  private static final long SEAT_ID2 = 2001L;
  private static final long SEAT_ID3 = 2002L;
  private static final long SEAT_ID4 = 2003L;
  private static final long SALES_LIST_ID = 1L;
  private static final long SALES_LIST_ID2 = 2L;
  private static final long MEMBER_ID = 1L;
  private static final long NOTE_ID = 123456L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(SEAT_ID2);
  private static final SeatUrn SEAT_URN3 = new SeatUrn(SEAT_ID3);
  private static final SeatUrn SEAT_URN4 = new SeatUrn(SEAT_ID4);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final ContractUrn CONTRACT_URN1 = new ContractUrn(CONTRACT_ID1);
  private static final Urn SALES_LIST_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);
  private static final Urn SALES_LIST_URN2 = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID2);
  private static final MemberUrn MEMBER_URN = new MemberUrn(MEMBER_ID);
  private static final Policy.ResourceContext RESOURCE_CONTEXT = new Policy.ResourceContext();
  private static final Urn SALES_NOTE_URN = Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, SEAT_ID, MEMBER_ID, NOTE_ID);
  private static final PolicyKey POLICY_KEY1 = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
      .setResource(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, 123))
      .setSubject(Urn.createFromTuple(SeatUrn.ENTITY_TYPE, 1000));
  private static final PolicyKey POLICY_KEY2 = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
      .setResource(Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, 124))
      .setSubject(Urn.createFromTuple(SeatUrn.ENTITY_TYPE, 1000));
  private static SubjectPolicy READER_SUBJECT_POLICY = new SubjectPolicy();
  private static SubjectPolicy OWNER_SUBJECT_POLICY = new SubjectPolicy();
  private static final long METRICS_REPORT_ID = 100L;
  private static final long METRICS_REPORT_ID2 = 200L;
  private static final long ENTERPRISE_ACCOUNT_ID = 2000L;
  private static final long ENTERPRISE_APPLICATION_INSTANCE_ID = 2000L;
  private static final long ENTERPRISE_PROFILE_ID = 1L;
  private static final long ENTERPRISE_PROFILE_ID2 = 2L;
  private static final Urn METRICS_REPORT_URN =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID);
  private static final Urn METRICS_REPORT_URN2 =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID2);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN2 =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID2);
  private static final Urn ENTERPRISE_APPLICATION_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID, ENTERPRISE_PROFILE_ID);


  static {
    READER_SUBJECT_POLICY.role = ShareRole.READER;
    OWNER_SUBJECT_POLICY.role = ShareRole.OWNER;
    RESOURCE_CONTEXT.setMember(MEMBER_URN);
  }

  private LssSharingDB _lssSharingDB;

  private AclServiceDispatcher _aclServiceDispatcher;

  private SalesSharingService _salesSharingService;

  @BeforeMethod
  public void setup() {
    _lssSharingDB = Mockito.mock(LssSharingDB.class);
    _aclServiceDispatcher = Mockito.mock(AclServiceDispatcher.class);
    _salesSharingService = new SalesSharingService(_lssSharingDB, _aclServiceDispatcher);
  }

  @Test
  public void testBatchGetPoliciesSucceed() {
    when(_lssSharingDB.getSubjectPolicy(POLICY_KEY1.getSubject(), POLICY_KEY1.getPolicyType().toString(),
        POLICY_KEY1.getResource())).thenReturn(Task.value(READER_SUBJECT_POLICY));
    when(_lssSharingDB.getSubjectPolicy(POLICY_KEY2.getSubject(), POLICY_KEY2.getPolicyType().toString(),
        POLICY_KEY2.getResource())).thenReturn(Task.value(OWNER_SUBJECT_POLICY));

    Map<PolicyKey, Policy> policyMap =
        await(_salesSharingService.batchGet(Sets.newHashSet(Arrays.asList(POLICY_KEY1, POLICY_KEY2))));
    assertThat(policyMap.size()).isEqualTo(2);
    assertThat(policyMap.containsKey(POLICY_KEY1)).isTrue();
    assertThat(policyMap.containsKey(POLICY_KEY2)).isTrue();
  }

  @Test
  public void testBatchGetPoliciesPartialFailure() {
    when(_lssSharingDB.getSubjectPolicy(POLICY_KEY1.getSubject(), POLICY_KEY1.getPolicyType().toString(),
        POLICY_KEY1.getResource())).thenReturn(Task.value(READER_SUBJECT_POLICY));
    when(_lssSharingDB.getSubjectPolicy(POLICY_KEY2.getSubject(), POLICY_KEY2.getPolicyType().toString(),
        POLICY_KEY2.getResource())).thenReturn(Task.failure(new EntityNotFoundException(null, "")));

    Map<PolicyKey, Policy> policyMap =
        await(_salesSharingService.batchGet(Sets.newHashSet(Arrays.asList(POLICY_KEY1, POLICY_KEY2))));
    assertThat(policyMap.size()).isEqualTo(1);
    assertThat(policyMap.containsKey(POLICY_KEY1)).isTrue();
  }

  @Test
  public void testBatchUpdateEmptyPolicyListSucceed() {
    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(Collections.emptyList(), SEAT_URN, CONTRACT_URN));
    assertThat(result.isEmpty()).isEqualTo(true);
  }

  @Test
  public void testBatchUpdateMultipleResourcesFail() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN2)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN)))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            "Batch update only supports a single resource and policy type"));
  }

  @Test
  public void testBatchUpdateMultiplePolicyTypesFail() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN2)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.ACCOUNT_LIST)
        .setSubject(SEAT_URN2));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN)))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            "Batch update only supports a single resource and policy type"));
  }

  @Test
  public void testBatchUpdateNotCollaboratorFail() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN3));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.DENIED));

    RestLiServiceException exception =
        runAndWaitException(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN),
            RestLiServiceException.class);
    assertThat(exception.getStatus()).isEqualTo(HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testBatchUpdateSubjectIsRequesterSucceed() {
    Policy policy1 = new Policy()
        .setResource(SALES_LIST_URN)
        .setRole(SharingRole.WRITER)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setSubject(SEAT_URN);
    Policy policy2 = new Policy()
        .setResource(SALES_LIST_URN)
        .setRole(SharingRole.WRITER)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setSubject(SEAT_URN3);

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(Pair.make(SEAT_URN2, ShareRole.OWNER)), 0, 0, 0)));
    when(_lssSharingDB.updateSubjectPolicy(any(), any())).thenReturn(Task.value(HttpStatus.S_200_OK));

    Map<CompoundKey, UpdateResponse> response =
        await(_salesSharingService.batchPartialUpdatePolicies(Arrays.asList(policy1, policy2), SEAT_URN, CONTRACT_URN));
    assertThat(response).hasSize(2);
  }

  @Test
  public void testBatchUpdateNoOwnerSucceed() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN3));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST,
        READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(
        Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));

    PolicyKey policyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2);
    PolicyKey policyKey2 =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3);
    PolicyKey ownerPolicyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.role = ShareRole.READER;

    SubjectPolicy ownerSubjectPolicy = new SubjectPolicy();
    ownerSubjectPolicy.contractUrn = CONTRACT_URN.toString();
    ownerSubjectPolicy.role = ShareRole.OWNER;

    when(_lssSharingDB.updateSubjectPolicy(ownerPolicyKey, ownerSubjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey2, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN));

    assertThat(result.size()).isEqualTo(2);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    verify(_lssSharingDB, times(3)).updateSubjectPolicy(any(), any());
  }

  @Test
  /*
  Policies are created to share within the contract. The policy shares across the same contract as the owner of
  the resource.
   */ public void testBatchUpdateShareWithinContractSucceed() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_NOTE_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.NOTE)
        .setSubject(CONTRACT_URN));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.NOTE, SALES_NOTE_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_NOTE_URN, PolicyType.NOTE, READER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));

    PolicyKey policyKey =
        new PolicyKey().setResource(SALES_NOTE_URN).setPolicyType(PolicyType.NOTE).setSubject(CONTRACT_URN);
    PolicyKey ownerPolicyKey =
        new PolicyKey().setResource(SALES_NOTE_URN).setPolicyType(PolicyType.NOTE).setSubject(SEAT_URN);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.role = ShareRole.READER;

    SubjectPolicy ownerSubjectPolicy = new SubjectPolicy();
    ownerSubjectPolicy.contractUrn = CONTRACT_URN.toString();
    ownerSubjectPolicy.role = ShareRole.OWNER;

    when(_lssSharingDB.updateSubjectPolicy(ownerPolicyKey, ownerSubjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN));

    assertThat(result.size()).isEqualTo(1);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, CONTRACT_URN)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.NOTE)
        .append(RESOURCE_COMPOUND_KEY, SALES_NOTE_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    verify(_lssSharingDB, times(2)).updateSubjectPolicy(any(), any());
  }

  @Test
  public void testBatchUpdateSimpleSharingPoliciesSucceed() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.PEOPLE_SEARCH)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.PEOPLE_SEARCH)
        .setSubject(SEAT_URN3));

    PolicyKey policyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.PEOPLE_SEARCH).setSubject(SEAT_URN2);
    PolicyKey policyKey2 =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.PEOPLE_SEARCH).setSubject(SEAT_URN3);
    PolicyKey ownerPolicyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.PEOPLE_SEARCH).setSubject(SEAT_URN);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.role = ShareRole.READER;

    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey2, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN));

    assertThat(result.size()).isEqualTo(2);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.PEOPLE_SEARCH)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.PEOPLE_SEARCH)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    verify(_lssSharingDB, times(2)).updateSubjectPolicy(any(), any());
  }

  @Test
  public void testBatchUpdateIsWriterSucceed() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN3));

    PaginatedList<Pair<Urn, ShareRole>> ownerList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SEAT_URN, ShareRole.OWNER)), 0, 1, 1);

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST,
        READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(ownerList));

    PolicyKey policyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2);
    PolicyKey policyKey2 =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.role = ShareRole.READER;

    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey2, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN));

    assertThat(result.size()).isEqualTo(2);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    verify(_lssSharingDB, times(2)).updateSubjectPolicy(any(), any());
  }

  @Test
  public void testBatchUpdateTwoFailuresOneSucceed() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN2), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN3), new Policy().setResource(SALES_LIST_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LEAD_LIST)
        .setSubject(SEAT_URN4));

    PaginatedList<Pair<Urn, ShareRole>> ownerList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SEAT_URN, ShareRole.OWNER)), 0, 1, 1);

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST,
        READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(ownerList));

    PolicyKey policyKey =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2);
    PolicyKey policyKey2 =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3);
    PolicyKey policyKey3 =
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN4);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.role = ShareRole.READER;

    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.updateSubjectPolicy(policyKey2, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_422_UNPROCESSABLE_ENTITY));
    when(_lssSharingDB.updateSubjectPolicy(policyKey3, subjectPolicy)).thenReturn(Task.failure(new IOException()));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, SEAT_URN, CONTRACT_URN));

    assertThat(result.size()).isEqualTo(3);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey3 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN4)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_200_OK);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_422_UNPROCESSABLE_ENTITY);
    assertThat(result.get(compoundKey3).getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    verify(_lssSharingDB, times(3)).updateSubjectPolicy(any(), any());
  }

  @Test
  public void testBatchDeleteEmptyPolicyListSucceed() {
    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchDeletePolicies(Collections.emptyList(), SEAT_URN));
    assertThat(result.isEmpty()).isEqualTo(true);
  }

  @Test
  public void testBatchDeleteMultipleResourcesFail() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN2).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN)))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            "Batch delete only supports a single resource and policy type"));
  }

  @Test
  public void testBatchDeleteMultiplePolicyTypesFail() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.ACCOUNT_LIST).setSubject(SEAT_URN2));

    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN)))
        .withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST,
            "Batch delete only supports a single resource and policy type"));
  }

  @Test
  public void testBatchDeleteNotSubjectAndNotWriterFail() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.DENIED));

    RestLiServiceException exception =
        runAndWaitException(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN), RestLiServiceException.class);
    assertThat(exception.getStatus()).isEqualTo(HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testBatchDeleteIsSubjectSucceed() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN4));

    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(1))).thenReturn(Task.value(true));

    // SEAT_URN3 is the requester, also is the subject to one of the policy keys
    Map<CompoundKey, UpdateResponse> result = await(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN3));

    assertThat(result.size()).isEqualTo(3);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey3 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN4)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);

    // Only the policies with subject SEAT_URN3 are deleted, for other polices, you don't have the access to delete them.
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_403_FORBIDDEN);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
    assertThat(result.get(compoundKey3).getStatus()).isEqualTo(HttpStatus.S_403_FORBIDDEN);
    verify(_lssSharingDB, times(1)).deleteSubjectPolicy(any());
  }

  @Test
  public void testBatchDeleteIsOwnerSucceed() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(0))).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(1))).thenReturn(Task.value(true));

    Map<CompoundKey, UpdateResponse> result = await(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN));

    assertThat(result.size()).isEqualTo(2);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
    verify(_lssSharingDB, times(2)).deleteSubjectPolicy(any());
  }

  @Test
  public void testBatchDeleteTwoFailuresOneSucceed() {
    List<PolicyKey> policyKeys = ImmutableList.of(
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN2),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN3),
        new PolicyKey().setResource(SALES_LIST_URN).setPolicyType(PolicyType.LEAD_LIST).setSubject(SEAT_URN4));

    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));

    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(0))).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(1))).thenReturn(Task.value(false));
    when(_lssSharingDB.deleteSubjectPolicy(policyKeys.get(2))).thenReturn(Task.failure(new IOException()));

    Map<CompoundKey, UpdateResponse> result = await(_salesSharingService.batchDeletePolicies(policyKeys, SEAT_URN));

    assertThat(result.size()).isEqualTo(3);

    CompoundKey compoundKey = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN2)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey2 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN3)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    CompoundKey compoundKey3 = new CompoundKey().append(SUBJECT_COMPOUND_KEY, SEAT_URN4)
        .append(POLICY_TYPE_COMPOUND_KEY, PolicyType.LEAD_LIST)
        .append(RESOURCE_COMPOUND_KEY, SALES_LIST_URN);
    assertThat(result.get(compoundKey).getStatus()).isEqualTo(HttpStatus.S_204_NO_CONTENT);
    assertThat(result.get(compoundKey2).getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    assertThat(result.get(compoundKey3).getStatus()).isEqualTo(HttpStatus.S_500_INTERNAL_SERVER_ERROR);
    verify(_lssSharingDB, times(3)).deleteSubjectPolicy(any());
  }

  @Test
  public void testFindPoliciesByResourceSucceed() {
    ResourcePolicyView policyView = new ResourcePolicyView();
    policyView.setRole(ShareRole.READER);
    PaginatedList<Pair<Urn, ResourcePolicyView>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SEAT_URN, policyView)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPolicyViewsByResource(SALES_LIST_URN, PolicyType.LEAD_LIST,
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedList));

    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, Collections.singleton(SharingRole.READER), SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getSubject()).isEqualTo(SEAT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
  }

  @Test
  public void testFindPoliciesByResourceSucceedWithNullRoles() {
    ResourcePolicyView policyView = new ResourcePolicyView();
    policyView.setRole(ShareRole.READER);
    PaginatedList<Pair<Urn, ResourcePolicyView>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SEAT_URN, policyView)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPolicyViewsByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, null, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedList));

    PaginatedList<Policy> result =
        await(_salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, null, SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getSubject()).isEqualTo(SEAT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
  }

  @Test
  public void testFindPoliciesByResourceFailWithEmptyRoles() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, Collections.emptySet(),
          SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
  }

  @Test
  public void testFindPoliciesByResourceReturnEmptyWhenFindPoliciesDBException() {
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, Collections.singleton(ShareRole.READER),
        0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.failure(new RuntimeException("fail to get policies")));

    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, Collections.singleton(SharingRole.READER), SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(0);
    assertThat(result.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testFindPoliciesByResourceReturnEmptyWhenAclServiceException() {
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ)).thenReturn(Task.failure(new RuntimeException("fail to check permission")));

    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, Collections.singleton(SharingRole.READER), SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(0);
    assertThat(result.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testFindPoliciesByResourceFailWithNoPermission() {
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.LEAD_LIST, SALES_LIST_URN, AccessAction.READ)).thenReturn(Task.value(AccessDecision.DENIED));

    String errorMsg = String.format("seat:%s does not have permission to find policies:%s by resource:%s", SEAT_URN,
        PolicyType.LEAD_LIST, SALES_LIST_URN);
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesSharingService.findPoliciesByResource(SALES_LIST_URN, PolicyType.LEAD_LIST, null, SEAT_URN, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    }).withCause(new RestLiServiceException(HttpStatus.S_403_FORBIDDEN, errorMsg));
  }

  @Test
  public void testFindPoliciesBySubjectSucceed() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SALES_LIST_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);

    when(_lssSharingDB.getPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST.toString(),
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(paginatedList));

    PaginatedList<Policy> result = await(_salesSharingService.findPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST,
        Collections.singleton(SharingRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getResource()).isEqualTo(SALES_LIST_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
  }

  @Test
  public void testFindPoliciesBySubjectSucceedWithNullRoles() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SALES_LIST_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);

    when(_lssSharingDB.getPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST.toString(), null, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(
        Task.value(paginatedList));

    PaginatedList<Policy> result =
        await(_salesSharingService.findPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST, null, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getResource()).isEqualTo(SALES_LIST_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
  }

  @Test
  public void testFindPoliciesBySubjectFailWithEmptyRoles() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesSharingService.findPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST, Collections.emptySet(), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
  }

  @Test
  public void testFindPoliciesBySubjectReturnEmptyWhenDBException() {
    when(_lssSharingDB.getPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST.toString(),
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(
        Task.failure(new RuntimeException("fail to get policies")));

    PaginatedList<Policy> result = await(_salesSharingService.findPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST,
        Collections.singleton(SharingRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(0);
    assertThat(result.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testFindPoliciesByResourceContextContractAsSubjectSucceed() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.resourceContext = MEMBER_URN.toString();
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SALES_NOTE_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(CONTRACT_URN, RESOURCE_CONTEXT, PolicyType.NOTE,
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).
        thenReturn(Task.value(paginatedList));
    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResourceContext(MEMBER_URN, PolicyType.NOTE,
            Collections.singleton(SharingRole.READER), SEAT_URN, CONTRACT_URN));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getSubject()).isEqualTo(CONTRACT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
    assertThat(result.getResult().get(0).getResourceContext().getMember()).isEqualTo(MEMBER_URN);
  }


  @Test
  public void testFindPoliciesByResourceContextSeatAsSubjectFailure() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.resourceContext = MEMBER_URN.toString();
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SALES_NOTE_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(CONTRACT_URN, RESOURCE_CONTEXT, PolicyType.NOTE,
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).
        thenReturn(Task.value(PaginatedList.createForPage(Collections.emptyList(), 0, 0, 0)));
    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResourceContext(MEMBER_URN, PolicyType.NOTE,
            Collections.singleton(SharingRole.READER), SEAT_URN, CONTRACT_URN));
    assertThat(result.getTotal()).isEqualTo(0);
  }

  @Test
  public void testFindPoliciesByResourceContextSucceedWithNullRoles() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    subjectPolicy.contractUrn = CONTRACT_URN.toString();
    subjectPolicy.resourceContext = MEMBER_URN.toString();
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(SALES_NOTE_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(CONTRACT_URN, RESOURCE_CONTEXT, PolicyType.NOTE,
        null, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).
        thenReturn(Task.value(paginatedList));
    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResourceContext(MEMBER_URN, PolicyType.NOTE, null, SEAT_URN,
            CONTRACT_URN));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getSubject()).isEqualTo(CONTRACT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.READER);
    assertThat(result.getResult().get(0).getResourceContext().getMember()).isEqualTo(MEMBER_URN);
  }

  @Test
  public void testFindPoliciesByResourceContextFailWithEmptyRoles() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_salesSharingService.findPoliciesByResourceContext(MEMBER_URN, PolicyType.NOTE, Collections.EMPTY_SET,
          SEAT_URN, CONTRACT_URN));
    }).withCause(new RestLiServiceException(HttpStatus.S_400_BAD_REQUEST, "roles can not be empty"));
  }

  @Test
  public void testFindPoliciesByResourceContextReturnEmptyWhenFindPoliciesDBException() {
    when(_lssSharingDB.getPoliciesByResourceContextAndSubject(CONTRACT_URN, RESOURCE_CONTEXT, PolicyType.NOTE,
        Collections.singleton(ShareRole.READER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).
        thenReturn(Task.failure(new RuntimeException("fail to get policies")));
    PaginatedList<Policy> result = await(
        _salesSharingService.findPoliciesByResourceContext(MEMBER_URN, PolicyType.NOTE,
            Collections.singleton(SharingRole.READER), SEAT_URN, CONTRACT_URN));
    assertThat(result.getTotal()).isEqualTo(0);
    assertThat(result.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testFindPoliciesBySubjectSucceedWithNoContract() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    PaginatedList<Pair<Urn, SubjectPolicy>> paginatedList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(METRICS_REPORT_URN, subjectPolicy)), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT, 1);
    Set<ShareRole> permittedRoles = Collections.singleton(ShareRole.OWNER);
    when(_lssSharingDB.getPoliciesBySubject(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT.toString(), permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(
        Task.value(paginatedList));

    PaginatedList<Policy> result =
        await(_salesSharingService.findPoliciesBySubject(ENTERPRISE_PROFILE_INSTANCE_URN,
            PolicyType.LSI_METRICS_REPORT, Collections.singleton(SharingRole.OWNER), 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getResource()).isEqualTo(METRICS_REPORT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.OWNER);
  }

  @Test
  public void testFindPoliciesByResourceSucceedWithNoContract() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    ResourcePolicyView policyView = new ResourcePolicyView();
    policyView.setRole(ShareRole.OWNER);
    PaginatedList<Pair<Urn, ResourcePolicyView>> ownerList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN, policyView)), 0, 1, 1);
    Set<ShareRole> permittedRoles = Collections.singleton(ShareRole.OWNER);
    when(_aclServiceDispatcher.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.READ)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPolicyViewsByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT,
        permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(ownerList));

    PaginatedList<Policy> result =
        await(_salesSharingService.findPoliciesByResource(METRICS_REPORT_URN,
            PolicyType.LSI_METRICS_REPORT, Collections.singleton(SharingRole.OWNER), ENTERPRISE_PROFILE_INSTANCE_URN,
            0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT));
    assertThat(result.getTotal()).isEqualTo(1);
    assertThat(result.getResult().size()).isEqualTo(1);
    assertThat(result.getResult().get(0).getResource()).isEqualTo(METRICS_REPORT_URN);
    assertThat(result.getResult().get(0).getRole()).isEqualTo(SharingRole.OWNER);
  }

  @Test
  public void testBatchUpdatePoliciesWithNoContract() {
    List<Policy> policies = ImmutableList.of(new Policy().setResource(METRICS_REPORT_URN)
        .setRole(SharingRole.READER)
        .setPolicyType(PolicyType.LSI_METRICS_REPORT)
        .setSubject(ENTERPRISE_APPLICATION_INSTANCE_URN));

    PolicyKey policyKey =
        new PolicyKey().setSubject(ENTERPRISE_APPLICATION_INSTANCE_URN)
            .setPolicyType(PolicyType.LSI_METRICS_REPORT)
            .setResource(METRICS_REPORT_URN);
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING.get(SharingRole.READER);

    PaginatedList<Pair<Urn, ShareRole>> ownerList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN, ShareRole.OWNER)), 0, 1, 1);
    when(_aclServiceDispatcher.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT,
        READER_ROLES, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(ownerList));
    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_204_NO_CONTENT));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchPartialUpdatePolicies(policies, ENTERPRISE_PROFILE_INSTANCE_URN, null));

    CompoundKey compoundKey = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, ENTERPRISE_APPLICATION_INSTANCE_URN)
        .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, PolicyType.LSI_METRICS_REPORT)
        .append(ServiceConstants.RESOURCE_COMPOUND_KEY, METRICS_REPORT_URN);

    assertThat(result.size()).isEqualTo(1);
    assertThat(result.containsKey(compoundKey));
  }

  @Test
  public void testBatchDeletePoliciesAsOwnerWithNoContract() {
    PolicyKey policyKey1 = new PolicyKey()
        .setResource(METRICS_REPORT_URN)
        .setPolicyType(PolicyType.LSI_METRICS_REPORT)
        .setSubject(ENTERPRISE_APPLICATION_INSTANCE_URN);

    PolicyKey policyKey2 = new PolicyKey()
        .setResource(METRICS_REPORT_URN)
        .setPolicyType(PolicyType.LSI_METRICS_REPORT)
        .setSubject(ENTERPRISE_PROFILE_INSTANCE_URN);

    List<PolicyKey> policyKeys = ImmutableList.of(policyKey1, policyKey2);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING.get(SharingRole.READER);

    Set<ShareRole> permittedRoles = Collections.singleton(ShareRole.OWNER);
    when(_aclServiceDispatcher.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));

    PaginatedList<Pair<Urn, ShareRole>> ownerList =
        PaginatedList.createForPage(ImmutableList.of(new Pair<>(ENTERPRISE_PROFILE_INSTANCE_URN, ShareRole.OWNER)), 0, 1, 1);
    when(_lssSharingDB.getPoliciesByResource(METRICS_REPORT_URN, PolicyType.LSI_METRICS_REPORT,
        permittedRoles, 0, ServiceConstants.SHARING_POLICY_GET_ALL_COUNT)).thenReturn(Task.value(ownerList));

    when(_lssSharingDB.deleteSubjectPolicy(policyKey1)).thenReturn(Task.value(true));
    when(_lssSharingDB.deleteSubjectPolicy(policyKey2)).thenReturn(Task.value(true));

    Map<CompoundKey, UpdateResponse> result =
        await(_salesSharingService.batchDeletePolicies(policyKeys, ENTERPRISE_PROFILE_INSTANCE_URN));

    CompoundKey compoundKey1 = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, ENTERPRISE_APPLICATION_INSTANCE_URN)
        .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, PolicyType.LSI_METRICS_REPORT)
        .append(ServiceConstants.RESOURCE_COMPOUND_KEY, METRICS_REPORT_URN);

    CompoundKey compoundKey2 = new CompoundKey().append(ServiceConstants.SUBJECT_COMPOUND_KEY, ENTERPRISE_PROFILE_INSTANCE_URN)
        .append(ServiceConstants.POLICY_TYPE_COMPOUND_KEY, PolicyType.LSI_METRICS_REPORT)
        .append(ServiceConstants.RESOURCE_COMPOUND_KEY, METRICS_REPORT_URN);

    assertThat(result.size()).isEqualTo(2);
    assertThat(result.containsKey(compoundKey1));
    assertThat(result.containsKey(compoundKey2));
  }

  @Test
  public void testBatchDeletePoliciesAsSubjectWithNoContract() {
    PolicyKey policyKey1 = new PolicyKey()
        .setResource(METRICS_REPORT_URN)
        .setPolicyType(PolicyType.LSI_METRICS_REPORT)
        .setSubject(ENTERPRISE_APPLICATION_INSTANCE_URN);

    PolicyKey policyKey2 = new PolicyKey()
        .setResource(METRICS_REPORT_URN)
        .setPolicyType(PolicyType.LSI_METRICS_REPORT)
        .setSubject(ENTERPRISE_PROFILE_INSTANCE_URN);

    List<PolicyKey> policyKeys = ImmutableList.of(policyKey1, policyKey2);

    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ServiceConstants.SHARE_ROLE_SERVICE_TO_ESPRESSO_MAPPING.get(SharingRole.READER);

    when(_aclServiceDispatcher.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN2,
        PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.DENIED));

    RestLiServiceException exception =
        runAndWaitException(_salesSharingService.batchDeletePolicies(policyKeys, ENTERPRISE_PROFILE_INSTANCE_URN2),
            RestLiServiceException.class);
    assertThat(exception.getStatus()).isEqualTo(HttpStatus.S_403_FORBIDDEN);
  }

  @Test
  public void testCheckAccessDecision() {
    when(_aclServiceDispatcher.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT, METRICS_REPORT_URN, AccessAction.UPDATE)).thenReturn(Task.value(AccessDecision.ALLOWED));

    AccessDecision decision = await(_salesSharingService.checkAccessDecision(ENTERPRISE_PROFILE_INSTANCE_URN, PolicyType.LSI_METRICS_REPORT,
        METRICS_REPORT_URN, AccessAction.UPDATE));

    assertThat(decision.equals(AccessDecision.ALLOWED));
  }

  @Test
  public void testPartialUpdate() {
    PolicyKey policyKey = new PolicyKey()
        .setSubject(SEAT_URN)
        .setPolicyType(PolicyType.PEOPLE_SEARCH)
        .setResource(SALES_LIST_URN);
    Policy policy = new Policy()
        .setLastViewedAt(1234L)
        .setSubscribed(true)
        .setCreatedAt(111L)
        .setAcceptedAt(222L)
        .setCreator(SEAT_URN2);
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.setIsSubscribed(policy.isSubscribed());
    subjectPolicy.setLastViewedTime(policy.getLastViewedAt());
    subjectPolicy.setCreatedTime(policy.getCreatedAt());
    subjectPolicy.setAcceptedTime(policy.getAcceptedAt());
    subjectPolicy.setCreatorSeatUrn(SEAT_URN2.toString());
    when(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy)).thenReturn(Task.value(HttpStatus.S_200_OK));
    UpdateResponse updateResponse =
        runAndWait(_salesSharingService.partialUpdatePolicy(policyKey, PatchGenerator.diffEmpty(policy), SEAT_URN));

    verify(_lssSharingDB).updateSubjectPolicy(policyKey, subjectPolicy);
    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testPartialUpdateAccountMapPolicy_requesterIsReader() {
    PolicyKey policyKey = new PolicyKey()
        .setSubject(SEAT_URN)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setResource(SALES_LIST_URN);
    Policy policy = new Policy()
        .setResource(SALES_LIST_URN)
        .setRole(SharingRole.WRITER)
        .setPolicyType(PolicyType.ACCOUNT_MAP);
    SubjectPolicy requesterSubjectPolicy = new SubjectPolicy();
    requesterSubjectPolicy.setRole(ShareRole.READER);

    when(_lssSharingDB.getSubjectPolicy(SEAT_URN, PolicyType.ACCOUNT_MAP.name(), SALES_LIST_URN))
        .thenReturn(Task.value(requesterSubjectPolicy));
    when(_lssSharingDB.updateSubjectPolicy(any(), any())).thenReturn(Task.value(HttpStatus.S_200_OK));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(Pair.make(SEAT_URN2, ShareRole.OWNER)), 0, 0, 0)));

    UpdateResponse updateResponse =
        runAndWait(_salesSharingService.partialUpdatePolicy(policyKey, PatchGenerator.diffEmpty(policy), SEAT_URN));

    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_200_OK);

    verifyNoInteractions(_aclServiceDispatcher);
    verify(_lssSharingDB, times(1)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
  }

  @Test
  public void testPartialUpdateAccountMapPolicy_requesterHasNoPolicy() {
    PolicyKey policyKey = new PolicyKey()
        .setSubject(SEAT_URN)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setResource(SALES_LIST_URN);
    Policy policy = new Policy()
        .setResource(SALES_LIST_URN)
        .setRole(SharingRole.WRITER)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setSubject(SEAT_URN);

    when(_lssSharingDB.getSubjectPolicy(SEAT_URN, PolicyType.ACCOUNT_MAP.name(), SALES_LIST_URN))
        .thenReturn(Task.failure(new EntityNotFoundException(null, "No policy found for requester")));
    when(_aclServiceDispatcher.checkAccessDecision(SEAT_URN, PolicyType.ACCOUNT_MAP, SALES_LIST_URN, AccessAction.ADMIN)).thenReturn(Task.value(AccessDecision.ALLOWED));
    when(_lssSharingDB.getPoliciesByResource(SALES_LIST_URN, PolicyType.ACCOUNT_MAP, READER_ROLES, 0,
        ServiceConstants.SHARING_POLICY_GET_ALL_COUNT))
        .thenReturn(Task.value(PaginatedList.createForPage(Collections.singletonList(Pair.make(SEAT_URN2, ShareRole.OWNER)), 0, 0, 0)));
    when(_lssSharingDB.updateSubjectPolicy(any(), any())).thenReturn(Task.value(HttpStatus.S_200_OK));

    UpdateResponse updateResponse =
        runAndWait(_salesSharingService.partialUpdatePolicy(policyKey, PatchGenerator.diffEmpty(policy), SEAT_URN));

    assertThat(updateResponse.getStatus()).isEqualTo(HttpStatus.S_200_OK);
    verify(_aclServiceDispatcher, times(1)).checkAccessDecision(any(), any(), any(), any());
  }

  @Test
  public void testPartialUpdateAccountMapPolicy_failedToGetRequesterPolicy() {
    PolicyKey policyKey = new PolicyKey()
        .setSubject(SEAT_URN)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setResource(SALES_LIST_URN);
    Policy policy = new Policy()
        .setResource(SALES_LIST_URN)
        .setRole(SharingRole.WRITER)
        .setPolicyType(PolicyType.ACCOUNT_MAP)
        .setSubject(SEAT_URN);

    when(_lssSharingDB.getSubjectPolicy(SEAT_URN, PolicyType.ACCOUNT_MAP.name(), SALES_LIST_URN))
        .thenReturn(Task.failure(new RuntimeException("failed to get requester policy")));

    runAndWaitException(_salesSharingService.partialUpdatePolicy(policyKey, PatchGenerator.diffEmpty(policy), SEAT_URN), RuntimeException.class);

    verifyNoInteractions(_aclServiceDispatcher);
    verify(_lssSharingDB, times(1)).getSubjectPolicy(any(), any(), any());
    verify(_lssSharingDB, times(0)).getPoliciesByResource(any(), any(), any(), anyInt(), anyInt());
    verify(_lssSharingDB, times(0)).updateSubjectPolicy(any(), any());
  }
}
