package com.linkedin.sales.service.utils;

import java.util.Locale;
import org.testng.Assert;
import org.testng.annotations.Test;


public class LocaleUtilsTest {
  @Test
  public void testGetLocale() {
    Assert.assertEquals(LocaleUtils.getJavaLocaleOrDefault(null), Locale.US);
    com.linkedin.common.Locale localeFR = new com.linkedin.common.Locale().setCountry("FR").setLanguage("fr");
    Assert.assertEquals(LocaleUtils.getJavaLocaleOrDefault(localeFR), Locale.FRANCE);
    com.linkedin.common.Locale incompleteLocale = new com.linkedin.common.Locale().setCountry("FR");
    Assert.assertEquals(LocaleUtils.getJavaLocaleOrDefault(incompleteLocale), Locale.US);
  }
}
