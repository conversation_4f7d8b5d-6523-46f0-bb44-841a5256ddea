package com.linkedin.sales.service.utils;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.data.DataMap;
import com.linkedin.data.template.StringArray;
import com.linkedin.restli.common.PatchRequest;
import com.linkedin.salescustomfilterview.PinnedFilters;
import com.linkedin.salescustomfilterview.SearchType;
import com.linkedin.util.Pair;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.testng.PowerMockTestCase;
import org.testng.annotations.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.doCallRealMethod;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest(PinnedFiltersUtils.class)
public class PinnedFiltersUtilsTest extends PowerMockTestCase {

  @Test(description = "Test forward compatibility adjustments")
  public void testForwardsCompatibility() throws Exception {
    new Mocks();
    Map<String, List<String>> forwardsCompatibilityMap = new ImmutableMap.Builder<String, List<String>>()
        .put("CURRENT_TITLE", ImmutableList.of("TITLE"))
        .build();
    List<String> originalFilterList = ImmutableList.of("COMPANY", "TITLE", "INDUSTRY");
    Set<String> expectedFilterSet = ImmutableSet.of("COMPANY", "INDUSTRY", "CURRENT_TITLE");
    List<String> filterList = ImmutableList.of("CURRENT_TITLE");
    when(PinnedFiltersUtils.getForwardsCompatibilityFilterMap()).thenReturn(forwardsCompatibilityMap);
    doCallRealMethod().when(PinnedFiltersUtils.class, "getBackwardsCompatibilityFilterMap");
    doCallRealMethod().when(PinnedFiltersUtils.class, "adjustFilterListForCompatibility",
        originalFilterList, filterList);
    Set<String> adjustedFilterSet = PinnedFiltersUtils.adjustFilterListForCompatibility(originalFilterList,filterList);
    assertThat(adjustedFilterSet).isEqualTo(expectedFilterSet);
  }

  @Test(description = "Test backward compatibility adjustments")
  public void testBackwardsCompatibility() throws Exception {
    new Mocks();
    Map<String, List<String>> backwardsCompatibilityMap = new ImmutableMap.Builder<String, List<String>>()
        .put("TITLE", ImmutableList.of("CURRENT_TITLE", "PAST_TITLE"))
        .build();
    List<String> originalFilterList = ImmutableList.of("COMPANY", "CURRENT_TITLE", "PAST_TITLE", "INDUSTRY");
    Set<String> expectedFilterSet = ImmutableSet.of("COMPANY", "INDUSTRY", "TITLE");
    List<String> filterList = ImmutableList.of("TITLE");
    when(PinnedFiltersUtils.getBackwardsCompatibilityFilterMap()).thenReturn(backwardsCompatibilityMap);
    doCallRealMethod().when(PinnedFiltersUtils.class, "getForwardsCompatibilityFilterMap");
    doCallRealMethod().when(PinnedFiltersUtils.class, "adjustFilterListForCompatibility",
        originalFilterList, filterList);
    Set<String> adjustedFilterSet = PinnedFiltersUtils.adjustFilterListForCompatibility(originalFilterList,filterList);
    assertThat(adjustedFilterSet).isEqualTo(expectedFilterSet);
  }

  @Test(description = "Test translating with set values")
  public void testSetPatchRequest() throws Exception {
    new Mocks();
    doCallRealMethod().when(PinnedFiltersUtils.class, "translatePatchDocument", any(PatchRequest.class));
    Pair<List<String>, List<String>> expectedPair = new Pair<>(ImmutableList.of("CURRENT_TITLE"), new ArrayList<>());
    assertThat(PinnedFiltersUtils.translatePatchDocument(getSetPatchRequest())).isEqualTo(expectedPair);
  }

  @Test(description = "Test translating with unset values")
  public void testUnsetPatchRequest() throws Exception {
    new Mocks();
    doCallRealMethod().when(PinnedFiltersUtils.class, "translatePatchDocument", any(PatchRequest.class));
    Pair<List<String>, List<String>> expectedPair = new Pair<>(ImmutableList.of(), ImmutableList.of("CURRENT_TITLE"));
    assertThat(PinnedFiltersUtils.translatePatchDocument(getUnsetPatchRequest())).isEqualTo(expectedPair);
  }

  @Test(description = "Test translating with empty patch")
  public void testEmptyPatchRequest() throws Exception {
    new Mocks();
    doCallRealMethod().when(PinnedFiltersUtils.class, "translatePatchDocument", any(PatchRequest.class));
    Pair<List<String>, List<String>> expectedPair = new Pair<>(ImmutableList.of(), ImmutableList.of());
    assertThat(PinnedFiltersUtils.translatePatchDocument(PatchRequest.createFromEmptyPatchDocument())).isEqualTo(expectedPair);
  }

  @Test (description="Test that getDefaultPinnedFilters returns expected values for mobile account search type")
  public void getDefaultPinnedFiltersWithMobileAccount() {
    assertThat(PinnedFiltersUtils.getDefaultPinnedFilters(SearchType.MOBILE_ACCOUNT, false)).isEqualTo(
        ImmutableList.of("INDUSTRY", "COMPANY_HEADCOUNT"));
  }

  @Test (description="Test that getDefaultPinnedFilters returns expected values for mobile lead search type")
  public void getDefaultPinnedFiltersWithMobileLead() {
    assertThat(PinnedFiltersUtils.getDefaultPinnedFilters(SearchType.MOBILE_LEAD, false)).isEqualTo(
        ImmutableList.of("GEOGRAPHY", "CURRENT_COMPANY", "SENIORITY_LEVEL", "CURRENT_TITLE"));
  }

  @Test (description="Test that getDefaultPinnedFilters returns expected values for web lead search type")
  public void getDefaultPinnedFiltersWithLead() {
    assertThat(PinnedFiltersUtils.getDefaultPinnedFilters(SearchType.LEAD, false)).isEqualTo(
        ImmutableList.of("RELATIONSHIP", "GEOGRAPHY", "INDUSTRY", "YEARS_OF_EXPERIENCE", "COMPANY_HEADCOUNT", "CURRENT_TITLE",
            "PAST_TITLE", "LEAD_HIGHLIGHTS", "LEADS_IN_CRM", "LEAD_LIST"));
  }

  @Test (description="Test that getDefaultPinnedFilters returns expected values for web lead search type with sales intelligence")
  public void getDefaultPinnedFiltersWithLeadSI() {
    assertThat(PinnedFiltersUtils.getDefaultPinnedFilters(SearchType.LEAD, true)).isEqualTo(
        ImmutableList.of("CURRENT_COMPANY", "COMPANY_HEADCOUNT", "SENIORITY_LEVEL", "FUNCTION", "CURRENT_TITLE",
            "GEOGRAPHY", "INDUSTRY", "BUYER_INTENT_RELATION", "RELATIONSHIP", "RECENTLY_CHANGED_JOBS", "POSTED_ON_LINKEDIN"));
  }

  private PatchRequest<PinnedFilters> getSetPatchRequest() {
    DataMap patchData = new DataMap();
    com.linkedin.salescustomfilterview.PinnedFilters setPinnedFilters = new com.linkedin.salescustomfilterview.PinnedFilters();
    setPinnedFilters.setFilters(new StringArray(ImmutableList.of("CURRENT_TITLE")));
    patchData.put("$set", setPinnedFilters.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  private PatchRequest<com.linkedin.salescustomfilterview.PinnedFilters> getUnsetPatchRequest() {
    DataMap patchData = new DataMap();
    com.linkedin.salescustomfilterview.PinnedFilters setPinnedFilters = new com.linkedin.salescustomfilterview.PinnedFilters();
    setPinnedFilters.setFilters(new StringArray(ImmutableList.of("CURRENT_TITLE")));
    patchData.put("$unset", setPinnedFilters.data());
    return PatchRequest.createFromPatchDocument(patchData);
  }

  static class Mocks {
    Mocks() {
      mockStatic(PinnedFiltersUtils.class);
    }
  }
}
