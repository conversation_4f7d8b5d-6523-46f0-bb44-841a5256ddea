package com.linkedin.sales.service.utils;

import com.linkedin.data.DataMap;
import org.testng.Assert;
import org.testng.annotations.Test;


public class RestliUtilsTest {

  @Test
  public void testDeserializeToDataMap() {
    String inputJson = "{\"keywords\":\"Test Engineer\"}";
    DataMap dataMap = RestliUtils.deserializeToDataMap(inputJson);
    Assert.assertEquals("Test Engineer", dataMap.get("keywords"));
  }

  @Test
  public void testDeserializeToDataMapInternationalCharacters() {
    String inputJson = "{\"keywords\":\"Cécile\"}";
    DataMap dataMap = RestliUtils.deserializeToDataMap(inputJson);
    Assert.assertEquals("Cécile", dataMap.get("keywords"));
  }
}
