package com.linkedin.sales.service.utils;

import com.linkedin.analytics.ColumnInfo;
import com.linkedin.analytics.ColumnInfoArray;
import com.linkedin.analytics.ColumnType;
import com.linkedin.analytics.QueryMetadata;
import com.linkedin.analytics.QueryResponse;
import com.linkedin.analytics.Row;
import com.linkedin.analytics.RowArray;
import com.linkedin.common.urn.ContractUrn;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;
import org.testng.Assert;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class SalesAnalyticsPinotUtilsTest {

  private SalesAnalyticsPinotUtils _salesAnalyticsPinotUtils;

  @BeforeMethod(alwaysRun = true)
  public void setUp() {
    _salesAnalyticsPinotUtils = new SalesAnalyticsPinotUtils();
  }

  private QueryResponse generateMockQueryResponse(String columnName, List<String> results) {
    QueryResponse queryResponse = new QueryResponse();
    RowArray rows = new RowArray();
    for (String result : results) {
      Row.ValueArray values = new Row.ValueArray();
      values.add(0, Row.Value.createWithDouble(Double.parseDouble(result)));
      Row row = new Row();
      row.setValue(values);
      rows.add(row);
    }
    queryResponse.setRows(rows);

    QueryMetadata queryMetadata = new QueryMetadata();
    ColumnInfoArray schema = new ColumnInfoArray();
    schema.add(new ColumnInfo().setColumnName(columnName).setColumnType(ColumnType.DOUBLE));
    queryMetadata.setSchema(schema);
    queryResponse.setQueryMetadata(queryMetadata);
    return queryResponse;
  }

  private QueryResponse generateMockTimeStampQueryResponse() {
    String columnName = "max(timestamp)";
    List<String> results = Collections.singletonList("1529877029000");
    return generateMockQueryResponse(columnName, results);
  }

  @Test
  public void testGetLongFromQueryResponse() {
    Long target = 1529877029000L;
    QueryResponse queryResponse = generateMockTimeStampQueryResponse();
    Long result = _salesAnalyticsPinotUtils.retrieveLongFromQueryResponse(queryResponse, "max(timestamp)");
    Assert.assertEquals(result, target);
  }

  @Test
  public void testContractIdExtraction() {
    ContractUrn contractUrn = new ContractUrn(12345678L);
    String contractIdStr = contractUrn.getContractIdEntity().toString();
    Assert.assertEquals("12345678", contractIdStr);
  }

  @Test
  public void testMinTimestampQueryConstruction() {
    ContractUrn contractUrn = new ContractUrn(12345678L);
    String SEAT_TABLE = "snapSeat";
    String GET_MIN_TIMESTAMP_QUERY = new StringJoiner(" ")
        .add("SELECT MIN(\"timestamp\") FROM")
        .add("%s")
        .add("WHERE contract_id =")
        .add("%s")
        .toString();

    String query = String.format(GET_MIN_TIMESTAMP_QUERY, SEAT_TABLE, contractUrn.getContractIdEntity());
    Assert.assertEquals("SELECT MIN(\"timestamp\") FROM snapSeat WHERE contract_id = 12345678", query);
  }
}
