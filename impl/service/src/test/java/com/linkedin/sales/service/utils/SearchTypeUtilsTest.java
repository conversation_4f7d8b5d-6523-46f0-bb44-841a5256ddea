package com.linkedin.sales.service.utils;

import com.linkedin.salescustomfilterview.SearchType;
import org.testng.annotations.Test;

import static org.testng.Assert.*;


public class SearchTypeUtilsTest {

  @Test (description = "Verify isMobileSearchType returns the expected values")
  public void testIsMobileSearchType() {
    assertFalse(SearchTypeUtils.isMobileSearchType(SearchType.LEAD));
    assertFalse(SearchTypeUtils.isMobileSearchType(SearchType.ACCOUNT));

    assertTrue(SearchTypeUtils.isMobileSearchType(SearchType.MOBILE_LEAD));
    assertTrue(SearchTypeUtils.isMobileSearchType(SearchType.MOBILE_ACCOUNT));
  }

}
