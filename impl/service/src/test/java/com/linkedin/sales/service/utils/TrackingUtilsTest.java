package com.linkedin.sales.service.utils;

import org.testng.Assert;
import org.testng.annotations.Test;
import proto.com.linkedin.common.TrackingId;


public class TrackingUtilsTest {

  @Test
  public void testConvertEspressoToProtoTrackingId() {
    // Create a mock Espresso TrackingId
    com.linkedin.sales.espresso.TrackingId espressoTrackingId = new com.linkedin.sales.espresso.TrackingId("test-tracking-id".getBytes());

    // Call the method to be tested
    TrackingId protoTrackingId = TrackingUtils.convertEspressoToProtoTrackingId(espressoTrackingId);

    // Verify the result
    Assert.assertNotNull(protoTrackingId);
    Assert.assertEquals(protoTrackingId.getFixedValue().toStringUtf8(), "test-tracking-id");
  }
}
