package com.linkedin.sales.service.utils;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.FunctionUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SalesIdentityUrn;
import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.common.urn.Urn;
import java.util.Collections;
import org.testng.Assert;
import org.testng.annotations.Test;


public class UrnUtilsTest {

  @Test
  public void testIsMemberUrn_returnsTrue() {
    Urn memberUrn = MemberUrn.createFromTuple(MemberUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(true, UrnUtils.isMemberUrn(memberUrn));
  }

  @Test
  public void testIsMemberUrn_returnsFalse() {
    Urn memberUrn = MemberUrn.createFromTuple(SalesIdentityUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(false, UrnUtils.isMemberUrn(memberUrn));
  }

  @Test
  public void testIsMemberUrn_withNullMemberUrn_returnsFalse() {
    Urn memberUrn = null;
    Assert.assertEquals(false, UrnUtils.isMemberUrn(memberUrn));
  }

  @Test
  public void testIsSalesIdentityUrn_returnsTrue() {
    Urn salesIdentityUrn = SalesIdentityUrn.createFromTuple(SalesIdentityUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(true, UrnUtils.isSalesIdentityUrn(salesIdentityUrn));
  }

  @Test
  public void testIsSalesIdentityUrn_returnsFalse() {
    Urn salesIdentityUrn = SalesIdentityUrn.createFromTuple(MemberUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(false, UrnUtils.isSalesIdentityUrn(salesIdentityUrn));
  }

  @Test
  public void testIsSalesIdentityUrn_withNullMemberUrn_returnsFalse() {
    Urn salesIdentityUrn = null;
    Assert.assertEquals(false, UrnUtils.isSalesIdentityUrn(salesIdentityUrn));
  }

  @Test
  public void testCreateSeatUrnWithId() {
    SeatUrn seatUrn = UrnUtils.createSeatUrn(1L);
    Assert.assertEquals(seatUrn.getSeatIdEntity().longValue(), 1L);
  }

  @Test
  public void testCreateContractUrnWithId() {
    ContractUrn contractUrn = UrnUtils.createContractUrn(1L);
    Assert.assertEquals(contractUrn.getContractIdEntity().longValue(), 1L);
  }

  @Test
  public void testCreateContractUrn() {
    proto.com.linkedin.common.ContractUrn contractUrn = UrnUtils.createContractUrn("urn:li:contract:1");
    Assert.assertEquals(contractUrn.getContractId(), 1L);
  }

  @Test
  public void testCreateEnterpriseProfileApplicationInstanceUrn() {
    Urn urn = Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, 1L, 1L, 1L);
    Assert.assertEquals(UrnUtils.createEnterpriseProfileApplicationInstanceUrn(urn), urn);
  }

  @Test
  public void testCreateProtoSalesListUrnProtoWithString() {
    long listId = 12345L;
    String rawUrn = "urn:li:salesList:" + listId;
    proto.com.linkedin.common.SalesListUrn result = UrnUtils.createProtoSalesListUrn(rawUrn);

    Assert.assertEquals(result.getListId(), listId);
  }

  @Test
  public void testCreateOrganizationUrnWithString() {
    long organizationId = 54321L;
    String rawUrn = "urn:li:organization:" + organizationId;
    proto.com.linkedin.common.OrganizationUrn result = UrnUtils.createOrganizationUrn(rawUrn);

    Assert.assertEquals(result.getOrganizationId(), organizationId);
  }

  @Test
  public void testCreateSalesInsightsMetricsReportUrn() {
    Urn urn = Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(UrnUtils.createSalesInsightsMetricsReportUrn(urn), urn);
  }

  @Test
  public void testCreateEnterpriseAccountUrn() {
    Urn urn = Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(UrnUtils.createEnterpriseAccountUrn(1L), urn);
  }

  @Test
  public void testCreateEnterpriseApplicationInstanceUrn() {
    Urn urn = Urn.createFromTuple(EnterpriseApplicationInstanceUrn.ENTITY_TYPE,
        Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, 1L), 1L);
    Assert.assertEquals(UrnUtils.createEnterpriseApplicationInstanceUrn(urn), urn);
  }

  @Test
  public void testCreateEnterpriseProfileUrn() {
    Urn urn = Urn.createFromTuple(EnterpriseProfileUrn.ENTITY_TYPE,
        Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, 1L), 1L);
    Assert.assertEquals(UrnUtils.createEnterpriseProfileUrn(1, 1), urn);
  }

  @Test
  public void testCreateStandardizedProductCategoryUrn() {
    Urn urn = Urn.createFromTuple(StandardizedProductCategoryUrn.ENTITY_TYPE,  1L);
    Assert.assertEquals(UrnUtils.createStandardizedProductCategoryUrn("urn:li:standardizedProductCategory:1"), urn);
  }

  @Test
  public void testCreateStandardizedProductUrn() {
    Urn urn = Urn.createFromTuple(StandardizedProductUrn.ENTITY_TYPE,  1L);
    Assert.assertEquals(UrnUtils.createStandardizedProductUrn("urn:li:standardizedProduct:1"), urn);
  }

  @Test
  public void testFormatFunctionUrns() {
    Urn urn = Urn.createFromTuple(FunctionUrn.ENTITY_TYPE,  1L);
    Assert.assertEquals(UrnUtils.formatFunctionUrns(Collections.singletonList("urn:li:function:1")),
        Collections.singletonList(urn));
  }

  public void testIsSeatUrn_returnsFalse() {
    Urn seatUrn = SeatUrn.createFromTuple(MemberUrn.ENTITY_TYPE, 1L);
    Assert.assertEquals(false, UrnUtils.isSeatUrn(seatUrn));
  }

  @Test
  public void testIsSeatUrn_withNullUrn_returnsFalse() {
    Urn seatUrn = null;
    Assert.assertEquals(false, UrnUtils.isSeatUrn(seatUrn));
  }
}
