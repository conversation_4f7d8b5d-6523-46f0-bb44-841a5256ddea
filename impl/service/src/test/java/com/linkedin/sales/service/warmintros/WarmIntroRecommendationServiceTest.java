package com.linkedin.sales.service.warmintros;

import com.linkedin.parseq.BaseEngineParTest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.ds.db.LssWarmIntrosDB;
import java.util.List;
import java.util.Optional;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.annotations.BeforeTest;
import proto.com.linkedin.common.MemberUrn;
import proto.com.linkedin.common.SeatUrn;
import proto.com.linkedin.saleswarmintros.BatchCreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.BatchGetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.CreateWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.GetWarmIntroRecommendationResponse;
import proto.com.linkedin.saleswarmintros.IntroducerRecommendation;
import proto.com.linkedin.saleswarmintros.IntroducerRecommendationStatus;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendation;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendationKey;
import proto.com.linkedin.saleswarmintros.WarmIntroRecommendationResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class WarmIntroRecommendationServiceTest extends BaseEngineParTest {
  private static final long SEAT_ID_1 = 100L;
  private static final long LEAD_MEMBER_ID_1 = 1000L;
  private static final long SEAT_ID_2 = 200L;
  private static final long LEAD_MEMBER_ID_2 = 2000L;
  private static final long INTRODUCER_MEMBER_ID_1 = 1L;
  private static final String VARIANT = "V1";
  private static final int SCORE = 7;
  private static final String RATIONALE = "Strong connection strength";
  private static final long CREATED_TIME = 1633036800000L;
  private static final long MODIFIED_TIME = 1633123200000L;

  @Mock
  private LssWarmIntrosDB lssWarmIntrosDB;
  private WarmIntroRecommendationService warmIntroRecommendationService;

  @BeforeTest(alwaysRun = true)
  void setUp() {
    MockitoAnnotations.openMocks(this);
    warmIntroRecommendationService = new WarmIntroRecommendationService(lssWarmIntrosDB);
  }

  @org.testng.annotations.Test
  void testCreateWarmIntroRecommendationSuccess() {
    WarmIntroRecommendation inputRecommendation = buildWarmIntroRecommendation(SEAT_ID_1, LEAD_MEMBER_ID_1);
    ArgumentCaptor<com.linkedin.sales.espresso.WarmIntroRecommendation> warmIntroArgumentCaptor = ArgumentCaptor.forClass(
        com.linkedin.sales.espresso.WarmIntroRecommendation.class);

    when(lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(eq(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1)),
        eq(new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1)), warmIntroArgumentCaptor.capture()))
        .thenReturn(Task.value(HttpStatus.S_200_OK));

    Task<CreateWarmIntroRecommendationResponse> resultTask = warmIntroRecommendationService.create(inputRecommendation);
    CreateWarmIntroRecommendationResponse response = runAndWait(resultTask);

    assertNotNull(response);
    assertEquals(SEAT_ID_1, response.getKey().getSeatUrn().getSeatId());
    assertEquals(LEAD_MEMBER_ID_1, response.getKey().getLeadMemberUrn().getMemberId());

    com.linkedin.sales.espresso.IntroducerRecommendation introducerRecommendation = warmIntroArgumentCaptor.getValue()
        .getIntroducerRecommendations().get(0);
    assertEquals(new com.linkedin.common.urn.MemberUrn(INTRODUCER_MEMBER_ID_1).toString(), introducerRecommendation.getIntroducer().toString());
    assertEquals(RATIONALE, introducerRecommendation.getRationale());
    assertEquals(SCORE, introducerRecommendation.getScore());
    assertEquals(VARIANT, introducerRecommendation.getVariant());
    assertEquals(IntroducerRecommendationStatus.IntroducerRecommendationStatus_RECOMMENDED.toString(), introducerRecommendation.getStatus());
  }

  @org.testng.annotations.Test
  void testCreateWarmIntroRecommendationFailure() {
    WarmIntroRecommendation inputRecommendation = buildWarmIntroRecommendation(SEAT_ID_1, LEAD_MEMBER_ID_1);

    when(lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(eq(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1)),
        eq(new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1)), any()))
        .thenReturn(Task.failure(new RuntimeException("Create failed")));

    RuntimeException exception = assertThrows(RuntimeException.class,
        () -> runAndWait(warmIntroRecommendationService.create(inputRecommendation)));
    assertEquals("java.lang.RuntimeException: Create failed", exception.getMessage());
  }

  @org.testng.annotations.Test
  void testBatchCreateWarmIntroRecommendation() {
    WarmIntroRecommendation successRecommendation = buildWarmIntroRecommendation(SEAT_ID_1, LEAD_MEMBER_ID_1);
    WarmIntroRecommendation failureRecommendation = buildWarmIntroRecommendation(SEAT_ID_2, LEAD_MEMBER_ID_2);

    when(lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(
        eq(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1)),
        eq(new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1)),
        any())).thenReturn(Task.value(HttpStatus.S_200_OK));

    when(lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(
        eq(new com.linkedin.common.urn.SeatUrn(SEAT_ID_2)),
        eq(new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_2)),
        any())).thenReturn(Task.failure(new RuntimeException("Simulated failure")));

    Task<BatchCreateWarmIntroRecommendationResponse> resultTask =
        warmIntroRecommendationService.batchCreateWarmIntroRecommendation(List.of(successRecommendation, failureRecommendation));
    BatchCreateWarmIntroRecommendationResponse response = runAndWait(resultTask);

    assertNotNull(response);
    assertEquals(2, response.getResponsesCount());

    // Success response
    WarmIntroRecommendationResponse successResponse = response.getResponsesList().stream()
        .filter(r -> r.getKey().getSeatUrn().getSeatId() == SEAT_ID_1)
        .findFirst().orElseThrow();
    assertTrue(successResponse.hasValue());
    assertEquals(LEAD_MEMBER_ID_1, successResponse.getKey().getLeadMemberUrn().getMemberId());

    // Failure response
    WarmIntroRecommendationResponse failureResponse = response.getResponsesList().stream()
        .filter(r -> r.getKey().getSeatUrn().getSeatId() == SEAT_ID_2)
        .findFirst().orElseThrow();
    assertTrue(failureResponse.hasError());
    assertEquals(LEAD_MEMBER_ID_2, failureResponse.getKey().getLeadMemberUrn().getMemberId());
    String errorMsg = failureResponse.getError().getMessage();
    assertTrue(errorMsg.contains(Long.toString(SEAT_ID_2)));
    assertTrue(errorMsg.contains(Long.toString(LEAD_MEMBER_ID_2)));
    assertTrue(errorMsg.contains("Simulated failure"));
  }

  @org.testng.annotations.Test
  void testBatchCreateWarmIntroRecommendationEmpty() {
    Task<BatchCreateWarmIntroRecommendationResponse> resultTask =
        warmIntroRecommendationService.batchCreateWarmIntroRecommendation(List.of());
    BatchCreateWarmIntroRecommendationResponse response = runAndWait(resultTask);
    assertNotNull(response);
    assertEquals(0, response.getResponsesCount());
  }

  @org.testng.annotations.Test
  void testGetWarmIntroRecommendationSuccess() {
    SeatUrn seatUrn = SeatUrn.newBuilder().setSeatId(SEAT_ID_1).build();
    MemberUrn leadMemberUrn = MemberUrn.newBuilder().setMemberId(LEAD_MEMBER_ID_1).build();
    WarmIntroRecommendationKey key = buildWarmIntroRecommendationKey(seatUrn, leadMemberUrn);

    com.linkedin.sales.espresso.WarmIntroRecommendation dbRecommendation = buildEspressoWarmIntroRecommendation();

    when(lssWarmIntrosDB.getWarmIntroRecommendation(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1),
        new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1))).thenReturn(Task.value(Optional.of(dbRecommendation)));

    Task<GetWarmIntroRecommendationResponse> resultTask = warmIntroRecommendationService.getWarmIntroRecommendation(key);
    GetWarmIntroRecommendationResponse response = runAndWait(resultTask);

    assertNotNull(response);
    WarmIntroRecommendation result = response.getValue();
    assertEquals(SEAT_ID_1, result.getSeatUrn().getSeatId());
    assertEquals(LEAD_MEMBER_ID_1, result.getLeadMemberUrn().getMemberId());
    assertEquals(1, result.getIntroducerRecommendationsCount());
    assertEquals(INTRODUCER_MEMBER_ID_1, result.getIntroducerRecommendations(0).getMemberUrn().getMemberId());
    assertEquals(RATIONALE, result.getIntroducerRecommendations(0).getRationale());
    assertEquals(SCORE, result.getIntroducerRecommendations(0).getScore());
    assertEquals(VARIANT, result.getIntroducerRecommendations(0).getVariant());
    assertEquals(IntroducerRecommendationStatus.IntroducerRecommendationStatus_RECOMMENDED,
        result.getIntroducerRecommendations(0).getStatus());
    assertEquals(CREATED_TIME, result.getIntroducerRecommendations(0).getCreatedTime());
    assertEquals(MODIFIED_TIME, result.getIntroducerRecommendations(0).getModifiedTime());
  }

  @org.testng.annotations.Test
  void testGetWarmIntroRecommendationNotFound() {
    SeatUrn seatUrn = SeatUrn.newBuilder().setSeatId(SEAT_ID_1).build();
    MemberUrn leadMemberUrn = MemberUrn.newBuilder().setMemberId(LEAD_MEMBER_ID_1).build();
    WarmIntroRecommendationKey key = buildWarmIntroRecommendationKey(seatUrn, leadMemberUrn);

    when(lssWarmIntrosDB.getWarmIntroRecommendation(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1),
        new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1))).thenReturn(Task.value(Optional.empty()));

    try {
      runAndWait(warmIntroRecommendationService.getWarmIntroRecommendation(key));
    } catch (Exception e) {
      Throwable cause = (e.getCause() != null) ? e.getCause() : e;
      assertTrue(cause instanceof RestLiServiceException);
      RestLiServiceException restLiEx = (RestLiServiceException) cause;
      assertEquals(HttpStatus.S_404_NOT_FOUND, restLiEx.getStatus());
      assertEquals("WarmIntroRecommendation does not exist", restLiEx.getMessage());
    }
  }

  @org.testng.annotations.Test
  void testGetWarmIntroRecommendationFailure() {
    SeatUrn seatUrn = SeatUrn.newBuilder().setSeatId(SEAT_ID_1).build();
    MemberUrn leadMemberUrn = MemberUrn.newBuilder().setMemberId(LEAD_MEMBER_ID_1).build();
    WarmIntroRecommendationKey key = buildWarmIntroRecommendationKey(seatUrn, leadMemberUrn);
    when(lssWarmIntrosDB.getWarmIntroRecommendation(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1),
        new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1))).thenReturn(Task.failure(new RuntimeException("Get failed")));

    RuntimeException exception = assertThrows(RuntimeException.class,
        () -> runAndWait(warmIntroRecommendationService.getWarmIntroRecommendation(key)));
    assertEquals("java.lang.RuntimeException: Get failed", exception.getMessage());
  }

  @org.testng.annotations.Test
  void testBatchGetWarmIntroRecommendation() {
    SeatUrn seatUrn1 = SeatUrn.newBuilder().setSeatId(SEAT_ID_1).build();
    MemberUrn leadMemberUrn1 = MemberUrn.newBuilder().setMemberId(LEAD_MEMBER_ID_1).build();
    WarmIntroRecommendationKey key1 = buildWarmIntroRecommendationKey(seatUrn1, leadMemberUrn1);

    SeatUrn seatUrn2 = SeatUrn.newBuilder().setSeatId(SEAT_ID_2).build();
    MemberUrn leadMemberUrn2 = MemberUrn.newBuilder().setMemberId(LEAD_MEMBER_ID_2).build();
    WarmIntroRecommendationKey key2 = buildWarmIntroRecommendationKey(seatUrn2, leadMemberUrn2);

    com.linkedin.sales.espresso.WarmIntroRecommendation dbRecommendation = buildEspressoWarmIntroRecommendation();
    when(lssWarmIntrosDB.getWarmIntroRecommendation(new com.linkedin.common.urn.SeatUrn(SEAT_ID_1),
        new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_1)))
        .thenReturn(Task.value(Optional.of(dbRecommendation)));
    when(lssWarmIntrosDB.getWarmIntroRecommendation(new com.linkedin.common.urn.SeatUrn(SEAT_ID_2),
        new com.linkedin.common.urn.MemberUrn(LEAD_MEMBER_ID_2)))
        .thenReturn(Task.failure(new RuntimeException("Simulated failure")));

    Task<BatchGetWarmIntroRecommendationResponse> resultTask =
        warmIntroRecommendationService.batchGetWarmIntroRecommendation(List.of(key1, key2));
    BatchGetWarmIntroRecommendationResponse response = runAndWait(resultTask);

    assertNotNull(response);
    assertEquals(2, response.getResponsesCount());

    // Verify success response (key1)
    WarmIntroRecommendationResponse successResponse = response.getResponses(0);
    assertEquals(SEAT_ID_1, successResponse.getKey().getSeatUrn().getSeatId());
    assertEquals(LEAD_MEMBER_ID_1, successResponse.getKey().getLeadMemberUrn().getMemberId());
    assertTrue(successResponse.hasValue());
    assertEquals(1, successResponse.getValue().getIntroducerRecommendationsCount());

    // Verify error response (key2)
    WarmIntroRecommendationResponse failureResponse = response.getResponses(1);
    assertEquals(SEAT_ID_2, failureResponse.getKey().getSeatUrn().getSeatId());
    assertEquals(LEAD_MEMBER_ID_2, failureResponse.getKey().getLeadMemberUrn().getMemberId());
    assertTrue(failureResponse.hasError());
    String errorMsg = failureResponse.getError().getMessage();
    assertTrue(errorMsg.contains("Simulated failure"));
    assertTrue(errorMsg.contains(String.valueOf(SEAT_ID_2)));
    assertTrue(errorMsg.contains(String.valueOf(LEAD_MEMBER_ID_2)));
  }

  private WarmIntroRecommendation buildWarmIntroRecommendation(long seatId, long leadMemberId) {
    SeatUrn seatUrn = SeatUrn.newBuilder().setSeatId(seatId).build();
    MemberUrn leadMemberUrn = MemberUrn.newBuilder().setMemberId(leadMemberId).build();
    return WarmIntroRecommendation.newBuilder()
        .setSeatUrn(seatUrn)
        .setLeadMemberUrn(leadMemberUrn)
        .addIntroducerRecommendations(IntroducerRecommendation.newBuilder()
            .setMemberUrn(MemberUrn.newBuilder().setMemberId(INTRODUCER_MEMBER_ID_1).build())
            .setStatus(IntroducerRecommendationStatus.IntroducerRecommendationStatus_RECOMMENDED)
            .setRationale(RATIONALE)
            .setScore(SCORE)
            .setVariant(VARIANT)
            .build())
        .build();
  }

  private com.linkedin.sales.espresso.WarmIntroRecommendation buildEspressoWarmIntroRecommendation() {
    com.linkedin.sales.espresso.WarmIntroRecommendation dbRecommendation =
        new com.linkedin.sales.espresso.WarmIntroRecommendation();
    com.linkedin.sales.espresso.IntroducerRecommendation dbIntroducer =
        new com.linkedin.sales.espresso.IntroducerRecommendation();
    dbIntroducer.setIntroducer(new com.linkedin.common.urn.MemberUrn(INTRODUCER_MEMBER_ID_1).toString());
    dbIntroducer.setRationale(RATIONALE);
    dbIntroducer.setScore(SCORE);
    dbIntroducer.setVariant(VARIANT);
    dbIntroducer.setStatus(IntroducerRecommendationStatus.IntroducerRecommendationStatus_RECOMMENDED.toString());
    dbIntroducer.setCreatedTime(CREATED_TIME);
    dbIntroducer.setModifiedTime(MODIFIED_TIME);
    dbRecommendation.setIntroducerRecommendations(List.of(dbIntroducer));
    return dbRecommendation;
  }

  private WarmIntroRecommendationKey buildWarmIntroRecommendationKey(SeatUrn seatUrn, MemberUrn leadMemberUrn) {
    return WarmIntroRecommendationKey.newBuilder()
        .setSeatUrn(seatUrn)
        .setLeadMemberUrn(leadMemberUrn)
        .build();
  }
}
