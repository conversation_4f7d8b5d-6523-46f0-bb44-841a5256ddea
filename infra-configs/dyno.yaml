lss-mt:
- application: lss-mt
  controller: PID
  e8:
    e8-limits:
    enable-e8: false
    endpoints: []
  fabric: prod-lva1
  healthchecks:
  - RPN: []
    defs:
    - label: EnqueuedJettyThreads
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.EnqueueCountTotal.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 3000
    title: Enqueued Jetty threads
  - RPN: []
    defs:
    - label: CurrentJettyThreadCount
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.CurrentThreadCount.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 800
    title: Current Jetty thread count
  - RPN:
    - expression: oscpu@user@host,oscpu@system@host,+
      label: os_user_plus_system
    defs:
    - label: oscpu@user@host
      rrd: os/cpu.user.rrd
    - label: oscpu@system@host
      rrd: os/cpu.system.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 5
      consecutive-good: 1
      max: 1100
    title: CPU Utilization user plus system total
  - RPN:
    - expression: 100.0,oscpu@idle@host,oscpu@count@host,/,-
      label: oscpu
    defs:
    - label: oscpu@count@host
      rrd: os/cpu.count.rrd
    - label: oscpu@idle@host
      rrd: os/cpu.idle.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 45.0
    title: CPU Utilization Percentage
  instance: i001
  product_tag:
  schedules:
  - autorunner: true
    details:
    icmp: true
    odp_mode: None
    qos_mode: true
  settings:
    abandon_after_fail: 4
    custom_quota_url:
    custom_quota_url_for_disable:
    custom_quota_url_for_enable:
    disable_quota: true
    initial_qps:
    linkedout_plan_id:
    max_concurrent_analyses: 6
    max_test_duration: 39600
    max_traffic_percentage: 50.0
    nurse_context: '{}'
    nurse_template_id:
    odp_ramp_pct:
    qps_pattern:
    recipients:
    traffic_failin_terminate: false
    traffic_failout_terminate: true
- application: lss-mt
  controller: PID
  e8:
    e8-limits:
    enable-e8: false
    endpoints: []
  fabric: prod-lor1
  healthchecks:
  - RPN: []
    defs:
    - label: EnqueuedJettyThreads
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.EnqueueCountTotal.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 3000
    title: Enqueued Jetty threads
  - RPN: []
    defs:
    - label: CurrentJettyThreadCount
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.CurrentThreadCount.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 800
    title: Current Jetty thread count
  - RPN:
    - expression: oscpu@user@host,oscpu@system@host,+
      label: os_user_plus_system
    defs:
    - label: oscpu@user@host
      rrd: os/cpu.user.rrd
    - label: oscpu@system@host
      rrd: os/cpu.system.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 5
      consecutive-good: 1
      max: 1100
    title: CPU Utilization user plus system total
  - RPN:
    - expression: 100.0,oscpu@idle@host,oscpu@count@host,/,-
      label: oscpu
    defs:
    - label: oscpu@count@host
      rrd: os/cpu.count.rrd
    - label: oscpu@idle@host
      rrd: os/cpu.idle.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 45.0
    title: CPU Utilization Percentage
  instance: i001
  product_tag:
  schedules:
  - autorunner: true
    details:
    icmp: true
    odp_mode: None
    qos_mode: true
  settings:
    abandon_after_fail: 4
    custom_quota_url:
    custom_quota_url_for_disable:
    custom_quota_url_for_enable:
    disable_quota: true
    initial_qps:
    linkedout_plan_id:
    max_concurrent_analyses: 6
    max_test_duration: 39600
    max_traffic_percentage: 50.0
    nurse_context: '{}'
    nurse_template_id:
    odp_ramp_pct:
    qps_pattern:
    recipients:
    traffic_failin_terminate: false
    traffic_failout_terminate: true
- application: lss-mt
  controller: PID
  e8:
    e8-limits:
    enable-e8: false
    endpoints: []
  fabric: prod-ltx1
  healthchecks:
  - RPN: []
    defs:
    - label: EnqueuedJettyThreads
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.EnqueueCountTotal.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 3000
    title: Enqueued Jetty threads
  - RPN: []
    defs:
    - label: CurrentJettyThreadCount
      rrd: lss-mt/lss-mt-war.i001.Jetty_Thread_Pool_Sensor.Jetty_Thread_Pool_Sensor.CurrentThreadCount.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 800
    title: Current Jetty thread count
  - RPN:
    - expression: oscpu@user@host,oscpu@system@host,+
      label: os_user_plus_system
    defs:
    - label: oscpu@user@host
      rrd: os/cpu.user.rrd
    - label: oscpu@system@host
      rrd: os/cpu.system.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 5
      consecutive-good: 1
      max: 1100
    title: CPU Utilization user plus system total
  - RPN:
    - expression: 100.0,oscpu@idle@host,oscpu@count@host,/,-
      label: oscpu
    defs:
    - label: oscpu@count@host
      rrd: os/cpu.count.rrd
    - label: oscpu@idle@host
      rrd: os/cpu.idle.rrd
    state-check: threshold
    state-check-args:
      accept-nans: false
      consecutive-events: 2
      consecutive-good: 1
      max: 55.0
    title: CPU Utilization Percentage
  instance: i001
  product_tag:
  schedules:
  - autorunner: true
    details:
    icmp: true
    odp_mode: None
    qos_mode: true
  settings:
    abandon_after_fail: 4
    custom_quota_url:
    custom_quota_url_for_disable:
    custom_quota_url_for_enable:
    disable_quota: true
    initial_qps:
    linkedout_plan_id:
    max_concurrent_analyses: 6
    max_test_duration: 39600
    max_traffic_percentage: 50.0
    nurse_context: '{}'
    nurse_template_id:
    odp_ramp_pct:
    qps_pattern:
    recipients:
    traffic_failin_terminate: false
    traffic_failout_terminate: true
