<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE module PUBLIC "-//Checkstyle//DTD Checkstyle Configuration 1.3//EN" "https://checkstyle.org/dtds/configuration_1_3.dtd">

<!--
    Checkstyle-Configuration: LinkedIn Style
    Description:
LinkedIn Java style.
-->
<module name="Checker">
  <property name="severity" value="warning"/>
  <property name="fileExtensions" value="java"/>

  <module name="TreeWalker">
    <property name="tabWidth" value="2"/>
    <module name="SuppressWarningsHolder"/>

    <!-- Allow Checkstyle warnings to be suppressed using trailing comments -->
    <module name="SuppressWithNearbyCommentFilter"/>
    <!-- Allow Checkstyle warnings to be suppressed using block comments -->
    <module name="SuppressionCommentFilter"/>

    <!-- ANNOTATIONS -->

    <!-- No trailing empty parenthesis or commas -->
    <module name="AnnotationUseStyle">
      <property name="elementStyle" value="ignore"/>
    </module>
    <!-- Ensure @Override is present when {@inheritDoc} Javadoc tag is present -->
    <module name="MissingOverride"/>
    <!-- Package level annotations belong in package-info.java -->
    <module name="PackageAnnotation"/>

    <!-- BLOCKS -->

    <!-- No empty while, try, finally, do, if, else, for, initializer, switch, or synchronized blocks -->
    <module name="EmptyBlock"/>
    <!-- Block opening brace on same line -->
    <module name="LeftCurly">
      <property name="option" value="eol"/>
    </module>
    <!-- Block closing brace for else, catch, finally on same line -->
    <module name="RightCurly">
      <property name="option" value="same"/>
    </module>
    <!-- Always use braces even if optional -->
    <module name="NeedBraces"/>

    <!-- CLASS DESIGN -->

    <!-- A class with only private constructors must be declared final -->
    <module name="FinalClass"/>
    <!-- Classes containing only static methods should not have a public constructor -->
    <module name="HideUtilityClassConstructor"/>

    <!-- CODING -->

    <!-- Use Java style array declarations (e.g. String[] names), not C style (e.g. String names[]) -->
    <module name="ArrayTypeStyle"/>
    <!-- If covariant equals defined, standard equals must also be defined -->
    <module name="CovariantEquals"/>
    <!-- Switch 'default' case must appear last -->
    <module name="DefaultComesLast"/>
    <!-- Call equals on string literals to avoid an NPE -->
    <module name="EqualsAvoidNull"/>
    <!-- Override equals and hashCode together -->
    <module name="EqualsHashCode"/>
    <!-- No fall through in switch cases, even the last one -->
    <module name="FallThrough">
      <property name="checkLastCaseGroup" value="true"/>
    </module>
    <!-- Do not perform assignments embedded within expressions -->
    <module name="InnerAssignment"/>
    <!-- Switch statements must have a 'default' case -->
    <module name="MissingSwitchDefault"/>
    <!-- Do not modify the 'for' loop control variable -->
    <module name="ModifiedControlVariable"/>
    <!-- Each variable declaration must be on a separate line -->
    <module name="MultipleVariableDeclarations"/>
    <!-- Do not use finalize -->
    <module name="NoFinalizer"/>
    <!-- Each statement (i.e. code terminated by a semicolon) must be on a separate line -->
    <module name="OneStatementPerLine"/>
    <!-- Classes must have an explicit package declaration -->
    <module name="PackageDeclaration"/>
    <!-- Do not reassign method parameters -->
    <module name="ParameterAssignment"/>
    <!-- Do not test boolean expressions against the values true or false -->
    <module name="SimplifyBooleanExpression"/>
    <!-- Do not test for boolean conditions and return the values true or false -->
    <module name="SimplifyBooleanReturn"/>
    <!-- Do not use '==' to compare string against a literal; use 'equals' -->
    <module name="StringLiteralEquality"/>
    <!-- Use 'L' with long literals -->
    <module name="UpperEll"/>

    <!-- IMPORTS -->

    <!-- No imports statements using '*' notation except static imports -->
    <module name="AvoidStarImport">
      <property name="allowStaticMemberImports" value="true"/>
    </module>
    <!-- Do not import 'sun' packages -->
    <module name="IllegalImport"/>
    <!-- Do not duplicate import statements -->
    <module name="RedundantImport"/>
    <!-- Eliminate unused imports -->
    <module name="UnusedImports"/>

    <!-- JAVADOC COMMENTS -->

    <!-- If you have a Javadoc comment, make sure it is properly formed -->
    <module name="JavadocStyle">
      <property name="checkFirstSentence" value="false"/>
    </module>

    <!-- MISCELLANEOUS -->

    <!-- Ensure filename matches outer class name -->
    <module name="OuterTypeFilename"/>

    <!-- MODIFIERS -->

    <!-- Avoid redundant modifiers such as public in interface method decls, per the Java Language spec -->
    <module name="RedundantModifier"/>

    <!-- NAMING CONVENTIONS -->

    <!-- Generic parameters for a class must be uppercase letters separated by underscores (e.g. <V>, <NEW>, <KEY_T>) -->
    <module name="ClassTypeParameterName">
      <property name="format" value="^[A-Z]+(_[A-Z]+)*$"/>
    </module>
    <!-- Constants must be all uppercase letters separated by underscores -->
    <module name="ConstantName">
      <property name="format" value="^(_?log)|([A-Z][A-Z0-9]*(_[A-Z0-9]+)*)$"/>
    </module>
    <!-- Local variables must be camel case starting with lowercase letter -->
    <module name="LocalFinalVariableName"/>
    <module name="LocalVariableName"/>
    <!-- Member variables must be camel case starting with an underscore or lowercase letter -->
    <module name="MemberName">
      <property name="format" value="^[_a-z][a-zA-Z0-9]*$"/>
    </module>
    <!-- Method name must be camel case starting with a lowercase letter -->
    <module name="MethodName"/>
    <!-- Generic parameters for a method must be uppercase letters separated by underscores (e.g. <V>, <NEW>, <KEY_T>) -->
    <module name="MethodTypeParameterName">
      <property name="format" value="^[A-Z]+(_[A-Z]+)*$"/>
    </module>
    <!-- Package name must be all lowercase letters separated by periods -->
    <module name="PackageName">
      <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>
    </module>
    <!-- Parameters must be camel case starting with a lowercase letter -->
    <module name="ParameterName"/>
    <!-- Static variables must be camel case starting with an underscore or lowercase letter -->
    <module name="StaticVariableName">
      <property name="format" value="^[_a-z][a-zA-Z0-9]*$"/>
    </module>
    <!-- Type names must be camel case starting with an uppercase letter -->
    <module name="TypeName"/>

    <!-- WHITESPACE -->

    <module name="GenericWhitespace"/>
    <module name="MethodParamPad"/>
    <!-- Do not wrap package and import declarations -->
    <module name="NoLineWrap"/>
    <module name="NoWhitespaceAfter">
      <property name="tokens" value="BNOT,DEC,DOT,INC,LNOT,UNARY_MINUS,UNARY_PLUS"/>
    </module>
    <module name="NoWhitespaceBefore"/>
    <module name="OperatorWrap"/>
    <module name="ParenPad"/>
    <module name="TypecastParenPad">
      <property name="tokens" value="RPAREN,TYPECAST"/>
    </module>
    <module name="WhitespaceAfter"/>
    <module name="WhitespaceAround"/>

    <!-- Do not allow meaningless, IDE generated parameter names -->
    <module name="RegexpSinglelineJava">
      <property name="format" value="[\s]+arg[\d]+[,\)]"/>
      <property name="message" value="Replace argN with a meaningful parameter name"/>
    </module>
  </module>

  <!-- LENGTHS -->

  <!-- Desired line length is 120 but allow some overrun beyond that -->
  <module name="LineLength">
    <property name="max" value="160"/>
    <message key="maxLineLen" value="Line is longer than {0,number,integer} characters (found {1,number,integer}). Try to keep lines under 120 characters."/>
  </module>

  <!-- Do not allow tab characters in source files -->
  <module name="FileTabCharacter"/>

  <!-- Ensure parameter and exception names are present on @param and @throws tags -->
  <module name="RegexpSingleline">
    <property name="format" value="\*[\s]*@(throws|param)[\s]*$"/>
    <property name="message" value="Missing parameter or exception name"/>
  </module>
  <!-- IDE generated code must be reviewed by developer -->
  <module name="RegexpSingleline">
    <property name="format" value="\/\/[\s]*TODO[\s]+Auto-generated"/>
    <property name="message" value="Replace IDE generated code with real implementation"/>
  </module>
  <!-- Detect commonly misspelled Javadoc tags -->
  <module name="RegexpSingleline">
    <property name="format" value="\*[\s]*@(params|throw|returns)[\s]+"/>
    <property name="message" value="Correct misspelled Javadoc tag"/>
  </module>

  <!-- Read checker suppressions from a file -->
  <!-- Uncomment this to enable reading Checkstyle suppressions from the suppressions.xml file.
       Before uncommenting this please read the "Suppression File" section of http://go/checkstyle
       to prevent error events in IntelliJ IDEA.
  <module name="SuppressionFilter">
    <property name="file" value="${config_loc}/suppressions.xml"/>
  </module>
  -->

  <!-- Allow SuppressWarnings annotation to suppress Checkstyle issues -->
  <module name="SuppressWarningsFilter"/>
</module>
