<FindBugsFilter>
  <!--
    Allow a class to hold or return mutable objects. While this has obvious risks, it is much too
    common a pattern to treat as a bug.
  -->
  <Match>
    <Bug code="EI, EI2"/>
  </Match>

  <!-- Allow accessing by index as this is a common pattern when parsing strings like URN -->
  <Match>
    <Bug code="CLI"/>
  </Match>

  <!-- We often convert exceptions into RestException or subclasses -->
  <Match>
    <Bug code="EXS, LEST"/>
  </Match>

  <Match>
    <Bug pattern="MS_PKGPROTECT"/>
  </Match>

  <!-- While it's not ideal, we do store URNs as String in some of our objects -->
  <Match>
    <Bug code="STT"/>
  </Match>

  <!-- Skip generated classes -->
  <Match>
    <Or>
      <Package name="com.linkedin.sales.espresso" />
      <Package name="com.linkedin.sales.crm.avro.generated" />
    </Or>
  </Match>

  <Match>
    <Source name="~.*/generatedServiceInteropJava/.*" />
  </Match>

  <!-- False negative -->
  <Match>
    <Bug code="DMI" />
    <Class name="com.linkedin.sales.factory.services.integration.CrmDataValidationExportJobServiceFactory" />
  </Match>
  <Match>
    <Bug code="OCP" />
    <Or>
      <Class name="com.linkedin.sales.ds.espresso.utils.EspressoUtil" />
      <Class name="com.linkedin.sales.factory.common.OffspringBootListener" />
      <Class name="com.linkedin.sales.service.SalesNavigatorProfileAssociationService" />
    </Or>
  </Match>
  <Match>
    <Bug code="PCAIL" />
    <Class name="com.linkedin.sales.service.messaging.plugins.SalesMessagePostCreateNotifyPluginService" />
  </Match>

  <!-- This is primarily for proto files when trying to access GRPC stubs-->
  <Match>
    <Bug pattern="SSCU_SUSPICIOUS_SHADED_CLASS_USE" />
  </Match>
  <Match>
    <Bug pattern="PDP_POORLY_DEFINED_PARAMETER" />
  </Match>





  <!--
    ACTION REQUIRED:
    Spotbugs is stricter in Gradle 7 than it was in Gradle 6.
    Excluding these spotbugs bug patterns to unblock Gradle 7 migrations.
    MP Owners should remove these exclusions, and run spotbugsMain/spotbugsTest task to find the issues and fix them.
  -->
  <Match>
    <Or>
      <Bug pattern="BED_BOGUS_EXCEPTION_DECLARATION"/>
      <Bug pattern="BRPI_BACKPORT_REUSE_PUBLIC_IDENTIFIERS"/>
      <Bug pattern="CE_CLASS_ENVY"/>
      <Bug pattern="DCN_NULLPOINTER_EXCEPTION"/>
      <Bug pattern="DLS_DEAD_LOCAL_STORE"/>
      <Bug pattern="DMC_DUBIOUS_MAP_COLLECTION"/>
      <Bug pattern="FCBL_FIELD_COULD_BE_LOCAL"/>
      <Bug pattern="HES_EXECUTOR_NEVER_SHUTDOWN"/>
      <Bug pattern="IMC_IMMATURE_CLASS_PRINTSTACKTRACE"/>
      <Bug pattern="LSC_LITERAL_STRING_COMPARISON"/>
      <Bug pattern="LUI_USE_SINGLETON_LIST"/>
      <Bug pattern="MUI_CALLING_SIZE_ON_SUBCONTAINER"/>
      <Bug pattern="NAB_NEEDLESS_BOOLEAN_CONSTANT_CONVERSION"/>
      <Bug pattern="NAB_NEEDLESS_BOXING_VALUEOF"/>
      <Bug pattern="OCP_OVERLY_CONCRETE_PARAMETER"/>
      <Bug pattern="PCOA_PARTIALLY_CONSTRUCTED_OBJECT_ACCESS"/>
      <Bug pattern="PRMC_POSSIBLY_REDUNDANT_METHOD_CALLS"/>
      <Bug pattern="PSC_PRESIZE_COLLECTIONS"/>
      <Bug pattern="RV_RETURN_VALUE_IGNORED_NO_SIDE_EFFECT"/>
      <Bug pattern="SEC_SIDE_EFFECT_CONSTRUCTOR"/>
      <Bug pattern="SIC_INNER_SHOULD_BE_STATIC"/>
      <Bug pattern="SMII_STATIC_METHOD_INSTANCE_INVOCATION"/>
      <Bug pattern="SPP_EQUALS_ON_ENUM"/>
      <Bug pattern="SPP_STATIC_FORMAT_STRING"/>
      <Bug pattern="SPP_USE_ISEMPTY"/>
      <Bug pattern="SS_SHOULD_BE_STATIC"/>
      <Bug pattern="ST_WRITE_TO_STATIC_FROM_INSTANCE_METHOD"/>
      <Bug pattern="UC_USELESS_OBJECT"/>
      <Bug pattern="UEC_USE_ENUM_COLLECTIONS"/>
      <Bug pattern="UP_UNUSED_PARAMETER"/>
      <Bug pattern="USBR_UNNECESSARY_STORE_BEFORE_RETURN"/>
      <Bug pattern="UTAO_TESTNG_ASSERTION_ODDITIES_ACTUAL_CONSTANT"/>
      <Bug pattern="UTAO_TESTNG_ASSERTION_ODDITIES_BOOLEAN_ASSERT"/>
      <Bug pattern="WOC_WRITE_ONLY_COLLECTION_FIELD"/>
      <Bug pattern="WOC_WRITE_ONLY_COLLECTION_LOCAL"/>
    </Or>
  </Match>

  <!--
    ACTION REQUIRED:
    Spotbugs is stricter in Gradle 7 than it was in Gradle 6.
    Excluding these spotbugs bug patterns to unblock Gradle 7 migrations.
    MP Owners should remove these exclusions, and run spotbugsMain/spotbugsTest task to find the issues and fix them.
  -->
  <Match>
    <Class name="com.linkedin.sales.ds.db.TestLssCoachDB" />
    <Method name="testCreateUpdateGetChatHistory" />
    <Bug pattern="UTAO_JUNIT_ASSERTION_ODDITIES_ACTUAL_CONSTANT" />
  </Match>
  <Match>
    <Class name="com.linkedin.sales.service.buyerengagement.ContractSellerIdentityServiceTest$Mocks" />
    <Bug pattern="UR_UNINIT_READ" />
  </Match>
  <Match>
    <Class name="com.linkedin.sales.service.flagship.SalesPYMKRecommendationServiceTest" />
    <Method name="TestFindRecommendationsWithLixEnabled" />
    <Bug pattern="NM_METHOD_NAMING_CONVENTION" />
  </Match>
  <Match>
    <Class name="com.linkedin.sales.service.flagship.SalesPYMKRecommendationServiceTest" />
    <Method name="TestFindRecommendationsWithPremium" />
    <Bug pattern="NM_METHOD_NAMING_CONVENTION" />
  </Match>
  <Match>
    <Class name="com.linkedin.sales.service.autoprospecting.SearchCriteriaService" />
    <Method name="generateHashValue" />
    <Bug pattern="UCPM_USE_CHARACTER_PARAMETERIZED_METHOD" />
  </Match>
</FindBugsFilter>