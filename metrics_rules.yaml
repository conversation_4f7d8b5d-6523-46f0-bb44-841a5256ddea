# Refer to MTV on-boarding guide at go/mtv for more details on the setup.
# In case of an emergency, use "NOCODECOVERAGECHECK" in the git commit message to bypass coverage check,
# which will trigger an email alert to the product owners
---
metric_type:  COVERAGE
parser_type:  java

rules:
  -   metric:
        - all             # special keyword acts like "*", rules will apply to all
      exclude:            # Excluding the below package from checking against 80% coverage
        - by_package/com/linkedin/sales/service/utils
        - by_package/com/linkedin/sales/service/acl
        - by_package/com/linkedin/sales/service
        - by_module/root/by_lang/java/overall
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesleadaccountassociations
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesleads
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesaccounts
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salescustomlists
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesentitynotes
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/note
        - by_module/root/by_lang/java/by_package/proto/com/linkedin/salescolleagues
        - by_module/root/by_lang/java/by_package/proto/com/linkedin/salesgateway
        - by_module/root/by_lang/java/by_package/proto/com/linkedin/sales
        - by_module/root/by_lang/java/by_package/proto/com/linkedin/salesanalytics
      fail_if:            # metric value complies with this condition will trigger validation failure
        less_than: 0.80   # float, used for absolute value (as a threshold) comparison, for code coverage the value should be 0~1
        decrease_by_greater_than: 0.02   # relative value (against value from lkg) comparison, for code coverage the value should be 0~1
      skip_if:            # metric value complies with this condition will skip validation
        greater_than: 0.9

  # The following rules apply to packages which have less than 80% coverage. These rules should be removed once coverage increases above 80%
  -   metric:
        - by_package/com/linkedin/sales/service/utils
      fail_if:
        less_than: 0.59

  -   metric:
        - by_package/com/linkedin/sales/service/acl
      fail_if:
        less_than: 0.75

  -   metric:
        - by_package/com/linkedin/sales/service
      fail_if:
        less_than: 0.71

  # mtv run-validation is currently failing to account for newly added folders and test files
  # Because it is being deprecated, the only solution offered at the moment is to onboard to SonarCube and remove mtv
  # These rules are being added until mtv can be properly deprecated in a separate branch

  -   metric:
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesleadaccountassociations
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesleads
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesaccounts
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salescustomlists
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/seattransfer/salesentitynotes
      fail_if:
        less_than: 0.0
  -   metric:
        - by_module/root/by_lang/java/by_package/com/linkedin/sales/service/note
      fail_if:
        less_than: 0.8204

