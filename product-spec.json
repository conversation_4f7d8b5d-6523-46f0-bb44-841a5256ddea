{"name": "lss-mt", "group": "<EMAIL>@", "version": "21.0.*", "scmUrl": "ssh://git.corp.linkedin.com:29418/sales-solutions/lss-mt.git", "comment": "", "description": "This MP serves as a home for all mid-tier APIs serving frontends for LSS products, most notably Sales Navigator.", "build": {"commands": {"build": "./gradlew -Prelease=true -PallArtifacts build -x integTest", "clean": "./gradlew clean", "coverage": "./gradlew jacoco -x integTest", "debug-run": "mint build && mint build-cfg -f qei-ltx1 && mint deploy -f qei-ltx1 --debug-app", "dev-run": "mint build && mint build-cfg -f dev && mint deploy -f dev", "itTest": "mint-integration run", "pcl": "mint-integration run", "pre-publish": "./gradlew --info emitMCE", "precommit": "mint stylecheck && mint staticanalysis && mint coverage", "remote-run": "rexec -t 22001 -t 4429 'mint debug-run && cat'", "run": "mint build && mint build-cfg -f qei-ltx1 && mint deploy -f qei-ltx1", "setup": "./gradlew reinit", "gen-idl": "./gradlew publishRestliIdl publishRestliSnapshot", "snapshot": "./gradlew generateArtifactSpec --no-configuration-cache", "staticanalysis": "./gradlew spotbugs", "stylecheck": "./gradlew checkstyle pegasusLint", "submit": "mint stylecheck && git submit --async", "test": "./gradlew check -x integTest coverageByOwner", "pre-release": "./gradlew -Prelease=true -PallArtifacts generateArtifactSpec --no-configuration-cache"}, "toolchains": {"gradle": {"plugins": {"espresso-util-plugins": {"libraries": ["gradle-espresso-schema-validation"], "version": "1.1.58"}, "li-database": {"libraries": ["gradle-plugins"], "version": "17.0.146"}, "ligradle-core": {"libraries": ["jvm-core", "jvm-grpc-codegen", "jvm-internal-urns", "jvm-restli-codegen", "utils-plugins", "jvm-craft-baseline"], "version": "7.8.3"}, "metadata-utils": {"libraries": ["metadata-utils-plugin"], "version": "7.2.81"}, "sonarqube": "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.0.0.2929"}}}, "type": "gradle", "versions": {"java": "17.0.5-msft", "scala": "2.12.11", "log4j2": "2.16.0"}}, "runtime": {}, "review": {"coverage": true, "precommit": {"create_issue": true, "run": true}, "stylecheck": true, "wcTest": {"create_issue": true, "run": true}}, "topology": {"applications": {"epsilon-lss-mt": {"kind": "epsilon", "payloads": [{"name": "lss-mt-war", "context": "lss-mt", "expand": false}], "name": "epsilon-lss-mt", "instance": "i001", "meta": {"debug_port": 22001, "features": ["cfg2", "java", "jmx", "multi_master", "online"], "run_timeout": 14400, "start_timeout": 600, "stop_timeout": 600}}, "lss-mt": {"kind": "generic", "payloads": [{"name": "lss-mt-war", "context": "lss-mt", "expand": false}], "name": "lss-mt", "instance": "i001", "meta": {"debug_port": 22001, "features": ["cfg2", "java", "jmx", "multi_master", "online"], "start_timeout": 600, "stop_timeout": 600}}}}, "archive": {"artifacts": {}, "databases": {}, "images": {}, "repo_name": "MNY"}, "dependencyResolution": {}, "external": {"asm": "org.ow2.asm:asm:9.8", "assertj-core": "org.assertj:assertj-core:3.27.3", "avro": "org.apache.avro:avro:1.11.3", "avro-compiler": "org.apache.avro:avro-compiler:1.11.3", "avroBuilder": "com.linkedin.avroutil1:builder:0.4.30", "avroCompatHelper": "com.linkedin.avroutil1:helper-all:0.4.30", "cglib": "cglib:cglib:3.3.0", "data-avro-generator": "com.linkedin.pegasus:data-avro-generator:29.69.8", "disruptor": "com.lmax:disruptor:3.4.4", "grpc-protobuf": "io.grpc:grpc-protobuf:1.68.3", "grpc-stub": "io.grpc:grpc-stub:1.68.3", "jersey-client": "org.glassfish.jersey.core:jersey-client:2.25.1", "jersey-media-jaxb": "org.glassfish.jersey.media:jersey-media-jaxb:2.25.1", "jersey-media-multipart": "org.glassfish.jersey.media:jersey-media-multipart:2.25.1", "jooq": "org.jooq:jooq:3.16.21", "log4j1_2Api": "org.apache.logging.log4j:log4j-1.2-api:2.17.1", "log4j2-slf4j-impl": "org.apache.logging.log4j:log4j-slf4j-impl:2.17.1", "log4j2Api": "org.apache.logging.log4j:log4j-api:2.17.1", "log4j2Core": "org.apache.logging.log4j:log4j-core:2.17.1", "mockito-core": "org.mockito:mockito-core:5.17.0", "mockito-inline": "org.mockito:mockito-inline:3.12.4", "mockito-junit-jupiter": "org.mockito:mockito-junit-jupiter:3.12.4", "org-slf4j-slf4j-api": "org.slf4j:slf4j-api:1.7.36", "org-slf4j-slf4j-log4j12": "org.slf4j:slf4j-log4j12:1.7.36", "org-slf4j-slf4j-simple": "org.slf4j:slf4j-simple:1.7.36", "parseq-guava-interop": "com.linkedin.parseq:parseq-guava-interop:5.1.20", "parseq-test-api": "com.linkedin.parseq:parseq-test-api:5.1.20", "powermock-api-mockito2": "org.powermock:powermock-api-mockito2:2.0.9", "powermock-module-testng": "org.powermock:powermock-module-testng:2.0.9", "protobuf-java": "com.google.protobuf:protobuf-java:3.25.5", "protoc": "com.google.protobuf:protoc:3.25.5", "protoc-gen-grpc-java": "io.grpc:protoc-gen-grpc-java:1.68.3", "restli-client": "com.linkedin.pegasus:restli-client:29.69.8", "restli-client-parseq": "com.linkedin.pegasus:restli-client-parseq:29.69.8", "restli-client-testutils": "com.linkedin.pegasus:restli-client-testutils:29.69.8", "restli-common": "com.linkedin.pegasus:restli-common:29.69.8", "restli-server": "com.linkedin.pegasus:restli-server:29.69.8", "sb-contrib": "com.mebigfatguy.sb-contrib:sb-contrib:7.4.7", "spotbugs-annotations": "com.github.spotbugs:spotbugs-annotations:4.9.3", "testng": "org.testng:testng:7.10.2"}, "product": {"ambry-client": {"libraries": ["ambry-client-factory"], "version": "28.1.381"}, "application-infrastructure-common": {"libraries": ["application-testing", "application-utilities"], "version": "12.0.66"}, "autortf": {"libraries": ["autortf-injector"], "version": "24.0.289"}, "avro-schemas": {"libraries": ["avro-schemas-builder", "avro-schemas-tracking"], "version": "77.0.523"}, "azcli": {"libraries": ["a<PERSON><PERSON><PERSON>"], "version": "0.4.98"}, "badge-backend": {"libraries": [], "version": "3.0.790"}, "bam-api": {"libraries": [], "version": "90.0.213"}, "cloud-session-api": {"libraries": [{"key": "cloud-network-api-dataTemplate", "name": "cloud-network-api", "configuration": "dataTemplate"}, {"key": "cloud-network-api-restClient", "name": "cloud-network-api", "configuration": "restClient"}], "version": "17.0.164"}, "comms-rendering-mt": {"libraries": ["comms-rendering-mt-helpers", "comms-rendering-mt-helpers-factory", "comms-rendering-mt-test-helpers", "plugins-api", {"key": "pluginsApiDataTemplate", "name": "comms-rendering-mt-api", "configuration": "dataTemplate"}, {"key": "comms-rendering-mt-api-protoDataModel", "name": "comms-rendering-mt-api", "configuration": "protoDataModel"}, {"key": "comms-rendering-mt-api-protoImplementation", "name": "comms-rendering-mt-api", "configuration": "protoImplementation"}, {"key": "comms-rendering-mt-api-pegasusInteropImplementation", "name": "comms-rendering-mt-api", "configuration": "pegasusInteropImplementation"}], "version": "41.0.21"}, "container": {"libraries": [{"key": "container-infrastructure-factory-test", "name": "container-infrastructure-factory", "configuration": "testArtifacts"}, "healthcheck-factory", "pegasus-d2-client-factory", "pegasus-rest-client-factory", "pegasus-restli-server-factory", "container-logging-factory", "grpc-server-factory"], "version": "38.14.3"}, "container-core": {"libraries": ["container-jmx-servlet-api", "sensor-registry-factory", "testfwk-offspring"], "version": "1.3.506"}, "container-dds": {"libraries": ["util-auto-commit-executor-pooled-factory"], "version": "1.2.185"}, "container-deprecated": {"libraries": ["container-rpcExecutorService-factory"], "version": "3.0.364"}, "container-server-jetty": {"libraries": ["container-jvml"], "version": "9.4.1"}, "crm-api": {"libraries": [{"configuration": "dataTemplate", "key": "crm-backend-api-dataTemplate", "name": "crm-backend-api"}, {"configuration": "restClient", "key": "crm-backend-api-restClient", "name": "crm-backend-api"}, "crm-common"], "version": "53.0.200"}, "dataformats-infra": {"libraries": ["proto-schema", "proto-pegasus-interop"], "version": "27.9.39"}, "datavault-acl-service-api": {"libraries": [], "version": "18.0.48"}, "datavault-change-mgmt-api": {"libraries": [], "version": "0.1.218"}, "dev-apps-mgmt-mt": {"libraries": [{"name": "dev-apps-mgmt-mt-api", "key": "dev-apps-mgmt-mt-api-client", "configuration": "restClient"}, {"name": "dev-apps-mgmt-mt-api", "key": "dev-apps-mgmt-mt-api-dataTemplate", "configuration": "dataTemplate"}], "version": "3.1.94"}, "eis-backend-api": {"libraries": ["rest-api", {"configuration": "dataTemplate", "key": "rest-api-dataTemplate", "name": "rest-api"}, {"configuration": "restClient", "key": "rest-api-restClient", "name": "rest-api"}, "rest-models", {"configuration": "dataTemplate", "key": "rest-models-dataTemplate", "name": "rest-models"}, {"key": "rest-api-protoDataModel", "name": "rest-api", "configuration": "protoDataModel"}, {"key": "rest-api-protoImplementation", "name": "rest-api", "configuration": "protoImplementation"}, {"key": "rest-api-pegasusInteropImplementation", "name": "rest-api", "configuration": "pegasusInteropImplementation"}], "version": "174.0.3"}, "entitlements-api": {"libraries": [{"configuration": "dataTemplate", "key": "rest-client-api-dataTemplate", "name": "rest-client-api"}, {"configuration": "restClient", "key": "rest-client-api-restClient", "name": "rest-client-api"}], "version": "4.1.130"}, "ep-apps-connector": {"libraries": ["dataextension-plugin", {"configuration": "dataTemplate", "name": "dataextension-plugin", "key": "dataextension-plugin-dataTemplate"}, {"key": "dataextension-plugin-protoDataModel", "name": "dataextension-plugin", "configuration": "protoDataModel"}, {"key": "dataextension-plugin-protoImplementation", "name": "dataextension-plugin", "configuration": "protoImplementation"}, {"key": "dataextension-plugin-pegasusInteropImplementation", "name": "dataextension-plugin", "configuration": "pegasusInteropImplementation"}], "version": "4.1.483"}, "ep-bulk-plugin": {"libraries": ["ep-bulk-plugin-api", {"key": "ep-bulk-plugin-api-dataTemplate", "name": "ep-bulk-plugin-api", "configuration": "dataTemplate"}, {"key": "ep-bulk-plugin-api-protoDataModel", "name": "ep-bulk-plugin-api", "configuration": "protoDataModel"}, {"key": "ep-bulk-plugin-api-protoImplementation", "name": "ep-bulk-plugin-api", "configuration": "protoImplementation"}, {"key": "ep-bulk-plugin-api-pegasusInteropImplementation", "name": "ep-bulk-plugin-api", "configuration": "pegasusInteropImplementation"}], "version": "3.0.188"}, "espresso-pub": {"libraries": ["espresso-client-impl"], "version": "89.0.245"}, "flock": {"libraries": [{"configuration": "restClient", "name": "flock-api"}, {"key": "flock-api-dataTemplate", "name": "flock-api", "configuration": "dataTemplate"}], "version": "32.0.100"}, "frameworks": {"libraries": ["fwk-gui-impl", "url-factory-factory"], "version": "30.0.245"}, "gaap-api": {"libraries": [{"configuration": "restClient", "name": "script"}, {"configuration": "restClient", "key": "task-restClient", "name": "task"}], "version": "51.0.74"}, "gaap-tasks-client-java": {"libraries": ["polling-client", "polling-client-factory"], "version": "6.0.40"}, "global-keygen": {"libraries": [{"configuration": "restClient", "key": "global-keygen-api-restClient", "name": "global-keygen-api"}], "version": "0.6.311"}, "groot-lss-mt": {"libraries": ["groot-lss-mt-impl"], "version": "0.10.1"}, "grpc-infra": {"libraries": ["si-grpc-impl", "proto-pegasus-service-interop", {"key": "si-common-proto-protoDataModel", "name": "si-common-proto", "configuration": "protoDataModel"}, {"key": "si-common-proto-protoImplementation", "name": "si-common-proto", "configuration": "protoImplementation"}, {"key": "si-common-proto-pegasusInteropImplementation", "name": "si-common-proto", "configuration": "pegasusInteropImplementation"}], "version": "38.5.94"}, "handles-midtier-api": {"libraries": [{"configuration": "dataTemplate", "key": "handles-midtier-api-dataTemplate", "name": "handles-midtier-api"}, {"configuration": "restClient", "key": "handles-midtier-api-restClient", "name": "handles-midtier-api"}], "version": "4.0.230"}, "i18n-core": {"libraries": ["i18n-core-factory", "i18n-core-impl"], "version": "1.3.55"}, "ibrik": {"libraries": ["ibrik-client"], "version": "0.1.556"}, "identity": {"libraries": [{"configuration": "restClient", "key": "profile-rest-api-restClient", "name": "profile-rest-api"}], "version": "141.0.232"}, "interservice-auth": {"libraries": ["interservice-auth-datavault-authz-factory"], "version": "40.6.24"}, "li-database": {"libraries": ["core"], "version": "17.0.146"}, "li-mysql-connector-java": {"libraries": ["li-mysql-connector-java", "li-mysql-driver"], "version": "8.0.27.103"}, "lighthouse-bps": {"libraries": ["lighthouse-client-impl_2.12"], "version": "22.5.57"}, "lighthouse-bps-api": {"libraries": [{"configuration": "restClient", "key": "rest-api-restClient", "name": "rest-api"}], "version": "6.0.65"}, "linkedin-kafka-clients": {"libraries": ["tracker-processor-factory"], "version": "11.4.103"}, "linkedin-resolution-rules": {"libraries": ["grpc-readiness", "grpc-readiness-container"], "version": "1.0.33"}, "lix-client": {"libraries": ["lix-client-factory"], "version": "70.10.0"}, "lix-client-api": {"libraries": ["lix-client-api"], "version": "17.1.0"}, "login-server-api": {"libraries": [{"configuration": "restClient", "key": "oauth2-client-restClient", "name": "oauth2-client"}], "version": "38.0.66"}, "lsi-common": {"libraries": [{"key": "api-restClient", "name": "api", "configuration": "restClient"}, "lsi-factories_2.12"], "version": "115.0.246"}, "lss-admin-backend": {"libraries": [{"configuration": "restClient", "key": "lss-admin-backend-api-restClient", "name": "lss-admin-backend-api"}], "version": "26.0.116"}, "lss-buyer-data-schemas": {"libraries": ["lss-buyer-data-model-avro17"], "version": "3.0.163"}, "lss-common": {"libraries": ["common-utils", "crm-clients", "lss-salesleadaccount-common", "lss-workflow-common", "lss-search-common"], "version": "29.3.33"}, "lss-connect-mt": {"libraries": [{"key": "api-restClient", "name": "api", "configuration": "restClient"}], "version": "9.0.823"}, "lss-growth-samza": {"libraries": ["venice-avro-schema"], "version": "0.0.214"}, "lss-mt-api": {"libraries": [{"configuration": "dataTemplate", "key": "lss-mt-api-dataTemplate", "name": "lss-mt-api"}, {"configuration": "restClient", "key": "lss-mt-api-restClient", "name": "lss-mt-api"}, {"key": "lss-mt-api-protoDataModel", "name": "lss-mt-api", "configuration": "protoDataModel"}, {"key": "lss-mt-api-protoImplementation", "name": "lss-mt-api", "configuration": "protoImplementation"}, {"key": "lss-mt-api-pegasusInteropImplementation", "name": "lss-mt-api", "configuration": "pegasusInteropImplementation"}], "version": "32.0.196"}, "lss-reporting": {"libraries": [{"key": "lss-reporting-grpc-api-proto-data-model", "name": "lss-reporting-grpc-api", "configuration": "protoDataModel"}, {"key": "lss-reporting-grpc-api-protoImplementation", "name": "lss-reporting-grpc-api", "configuration": "protoImplementation"}], "version": "1.0.988"}, "models": {"libraries": ["models", {"configuration": "dataTemplate", "key": "models-dataTemplate", "name": "models"}, {"key": "models-protoDataModel", "name": "models", "configuration": "protoDataModel"}, {"key": "models-protoImplementation", "name": "models", "configuration": "protoImplementation"}, {"key": "models-pegasusInteropImplementation", "name": "models", "configuration": "pegasusInteropImplementation"}], "version": "54.6.631"}, "money": {"libraries": ["money-shared", "money-util"], "version": "38.0.166"}, "notifications-service": {"libraries": ["api", {"configuration": "dataTemplate", "key": "api-dataTemplate", "name": "api"}, {"key": "api-protoDataModel", "name": "api", "configuration": "protoDataModel"}, {"key": "api-protoImplementation", "name": "api", "configuration": "protoImplementation"}, {"key": "api-pegasusInteropImplementation", "name": "api", "configuration": "pegasusInteropImplementation"}], "version": "46.1.91"}, "omni-utils": {"libraries": ["omni-utils-common_2.11", "omni-utils-ambry-factories_2.11", "omni-utils-ambry_2.11"], "version": "42.0.40"}, "ownership-transfer-lib": {"libraries": [{"configuration": "dataTemplate", "key": "models-dataTemplate", "name": "models"}, "common", "core-engine", {"key": "models-protoDataModel", "name": "models", "configuration": "protoDataModel"}, {"key": "models-protoImplementation", "name": "models", "configuration": "protoImplementation"}, {"key": "models-pegasusInteropImplementation", "name": "models", "configuration": "pegasusInteropImplementation"}], "version": "0.0.191"}, "parseq-espresso-client": {"libraries": ["parseq-espresso-client-factory", "parseq-espresso-client-impl"], "version": "2.2.115"}, "parseq-rest-client": {"libraries": ["parseq-rest-client-factory"], "version": "3.0.265"}, "realtime-dispatcher-api": {"libraries": [{"key": "realtime-dispatcher-data-models-dataTemplate", "name": "realtime-dispatcher-data-models", "configuration": "dataTemplate"}, {"key": "realtime-dispatcher-api-restClient", "name": "realtime-dispatcher-api", "configuration": "restClient"}, {"key": "realtime-dispatcher-data-models-protoDataModel", "name": "realtime-dispatcher-data-models", "configuration": "protoDataModel"}, {"key": "realtime-dispatcher-data-models-protoImplementation", "name": "realtime-dispatcher-data-models", "configuration": "protoImplementation"}, {"key": "realtime-dispatcher-data-models-pegasusInteropImplementation", "name": "realtime-dispatcher-data-models", "configuration": "pegasusInteropImplementation"}], "version": "6.1.350"}, "resource-identity": {"libraries": ["gradle-urn-util", "resource-identity-urn-generator"], "version": "14.1.147"}, "restli-gateway-util": {"libraries": ["restli-gateway-util"], "version": "3.0.167"}, "samza-li": {"libraries": [], "version": "323.1085.2.52"}, "security-crypto": {"libraries": ["security-crypto", "security-crypto-factory"], "version": "15.0.66"}, "security-member-token": {"libraries": [], "version": "22.2.147"}, "subs-api": {"libraries": [{"configuration": "restClient", "key": "rest-client-api", "name": "rest-client-api"}], "version": "4.2.135"}, "subs-mt": {"libraries": [{"configuration": "restClient", "key": "subs-mt-api-restClient", "name": "subs-mt-api"}], "version": "6.1.2104"}, "talent": {"libraries": ["db-mapper", "decorator", "talent-common-pegasus"], "version": "26.1.381"}, "testfwk": {"libraries": ["testfwk-core", "testfwk-utils"], "version": "7.0.139"}, "third-party-authentication": {"libraries": ["third-party-authorizedscope-client"], "version": "35.0.465"}, "ucf": {"libraries": [{"configuration": "dataTemplate", "name": "ucf-client-api"}, "ucf-client-factory", "ucf-viewerinfo-util-factory"], "version": "35.0.32"}, "urls": {"libraries": ["adapters", "url-private-aliases"], "version": "352.19.0"}, "util": {"libraries": ["util-core", "util-core-factory", "util-factory", "util-factory-tools", "util-json", "util-servlet-factory"], "version": "28.4.53"}, "vector-oss": {"group": "com.linkedin.vector", "libraries": [{"key": "vector-common-data-templates-restClient", "name": "vector-common-data-templates", "configuration": "restClient"}], "version": "0.1.1473"}, "venice-thin-client": {"libraries": ["venice-thin-client"], "version": "6.0.49", "release-candidate": true}}, "trunkDev": {"autoRevert": true, "enableAutomatedDependencyUpgrades": true}, "extSCM": {"config/external": "ssh://<EMAIL>/linkedin-managed/global-config.git"}}