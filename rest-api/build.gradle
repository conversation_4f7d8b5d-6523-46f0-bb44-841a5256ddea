// [DO_NOT_REMOVE] Below property must be defined before li-pegasus2 plugin is applied
// Enables li-pegasus2 plugin to register IDL gradle tasks even if IDL dir is empty
project.ext.'rest.idl.processEmptyIdlDir'=true
apply plugin: 'li-pegasus2'

dependencies {
  dataModel spec.product.'lss-mt-api'.'lss-mt-api-dataTemplate'
  dataModel spec.product.'ownership-transfer-lib'.'models-dataTemplate'
  dataModel spec.product.'eis-backend-api'.'rest-api-dataTemplate'
  dataModel spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api-dataTemplate'
  dataModel spec.product.'ep-apps-connector'.'dataextension-plugin-dataTemplate'

  // For decorating and formatting notifications for Flagship (go/commsrenderingplugins):
  dataModel spec.product.'comms-rendering-mt'.'pluginsApiDataTemplate'
  dataModel spec.product.'notifications-service'.'api-dataTemplate'

  restClientCompile spec.external.'restli-client'
  dataModel spec.product.models.'models-dataTemplate'
  dataModel spec.product.'realtime-dispatcher-api'.'realtime-dispatcher-data-models-dataTemplate'
}
// BEGIN: grpc migration script changes
apply plugin: 'li-grpc-bridged'
dependencies {
  implementation spec.product.'dataformats-infra'.'proto-pegasus-interop'
  implementation spec.product.'grpc-infra'.'proto-pegasus-service-interop'
  implementation spec.external.'grpc-stub'
// datamodel/bridge dependencies migrated from dataModel dependency: lss-mt-api
  protoDataModel spec.product.'lss-mt-api'.'lss-mt-api-protoDataModel'
  protoImplementation spec.product.'lss-mt-api'.'lss-mt-api-protoImplementation'
  pegasusInteropImplementation spec.product.'lss-mt-api'.'lss-mt-api-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: models
  protoDataModel spec.product.'ownership-transfer-lib'.'models-protoDataModel'
  protoImplementation spec.product.'ownership-transfer-lib'.'models-protoImplementation'
  pegasusInteropImplementation spec.product.'ownership-transfer-lib'.'models-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: rest-api
  protoDataModel spec.product.'eis-backend-api'.'rest-api-protoDataModel'
  protoImplementation spec.product.'eis-backend-api'.'rest-api-protoImplementation'
  pegasusInteropImplementation spec.product.'eis-backend-api'.'rest-api-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: ep-bulk-plugin-api
  protoDataModel spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api-protoDataModel'
  protoImplementation spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api-protoImplementation'
  pegasusInteropImplementation spec.product.'ep-bulk-plugin'.'ep-bulk-plugin-api-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: dataextension-plugin
  protoDataModel spec.product.'ep-apps-connector'.'dataextension-plugin-protoDataModel'
  protoImplementation spec.product.'ep-apps-connector'.'dataextension-plugin-protoImplementation'
  pegasusInteropImplementation spec.product.'ep-apps-connector'.'dataextension-plugin-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: comms-rendering-mt-api
  protoDataModel spec.product.'comms-rendering-mt'.'comms-rendering-mt-api-protoDataModel'
  protoImplementation spec.product.'comms-rendering-mt'.'comms-rendering-mt-api-protoImplementation'
  pegasusInteropImplementation spec.product.'comms-rendering-mt'.'comms-rendering-mt-api-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: api
  protoDataModel spec.product.'notifications-service'.'api-protoDataModel'
  protoImplementation spec.product.'notifications-service'.'api-protoImplementation'
  pegasusInteropImplementation spec.product.'notifications-service'.'api-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: models
  protoDataModel spec.product.'models'.'models-protoDataModel'
  protoImplementation spec.product.'models'.'models-protoImplementation'
  pegasusInteropImplementation spec.product.'models'.'models-pegasusInteropImplementation'
// datamodel/bridge dependencies migrated from dataModel dependency: realtime-dispatcher-data-models
  protoDataModel spec.product.'realtime-dispatcher-api'.'realtime-dispatcher-data-models-protoDataModel'
  protoImplementation spec.product.'realtime-dispatcher-api'.'realtime-dispatcher-data-models-protoImplementation'
  pegasusInteropImplementation spec.product.'realtime-dispatcher-api'.'realtime-dispatcher-data-models-pegasusInteropImplementation'
  protoDataModel spec.product.'grpc-infra'.'si-common-proto-protoDataModel'
  protoImplementation spec.product.'grpc-infra'.'si-common-proto-protoImplementation'
  pegasusInteropImplementation spec.product.'grpc-infra'.'si-common-proto-pegasusInteropImplementation'
}
// END: grpc migration script changes

