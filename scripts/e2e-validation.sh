#!/bin/bash
# set -x
# set -e

# Delete yarn.lock and package.json to avoid conflicts
[ -f yarn.lock ] && rm -f yarn.lock
[ -f package.json ] && rm -f package.json

# Install checkli CLI
mint clone checkly-tools
./checkly-tools/scripts/install-checkli-cli.sh

# Run the e2e tests
checkly-tools-cache/bin/checkli --cachedir "$PWD"/.checkli trigger --env ENABLE_PROXY=true --env USER_GROUP_OVERRIDE=eu --env fabric="${FABRIC:5}" --tags canary:lss-mt,fabric:"${FABRIC:5}" --tags shz:feed,fabric:"${FABRIC:5}" --tags shz:pages,fabric:"${FABRIC:5}" --tags shz:jobs,fabric:"${FABRIC:5}" --private-location "${FABRIC}"-browsercheck --enable-kraken-proxy --reporter ci --timeout 1800 --host-override-config-path "$PWD"/scripts/host-overrides.yaml
