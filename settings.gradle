// go/liscalacrossbuildplugin

def parentModules = [
    'database',
    'rest-api',
    'impl',
    'war',
    'test'
]

def modulesToCrossBuild = [
    'impl:service',
    'impl:rest-impl'
]

def modulesToNotCrossBuild = []

parentModules.each { module ->
  if (!file(module).directory) {
    throw new GradleException("Module '$module' specified in the settings.gradle file must be a valid directory in the root project.")
  }
  file(module).eachDir { submodule ->
    if (!submodule.name.startsWith('.')) {
      def submoduleName = "${module}:${submodule.name}".toString()
      if (!modulesToCrossBuild.contains(submoduleName)) {
        modulesToNotCrossBuild += submoduleName
      }
    }
  }
}

scalaCrossBuild {
  defaultScalaVersion '2.12.11'
  targetScalaVersions '2.12.11'
  projectsToCrossBuild modulesToCrossBuild
  projectsToNotCrossBuild modulesToNotCrossBuild
}

rootProject.name = 'lss-mt_root'
include 'database:LssProspectingAgent-mysql'
