apply plugin: 'li-java'
apply plugin: 'li-offspring-test'
apply plugin: 'li-integ-test'


dependencies {
  api project(':impl:factory')
  api spec.external.testng
  runtimeOnly project(path: ':impl:factory', configuration: 'cmpt-def')
  api project(':impl:ds')
  api project(':test:test-fwk')
  api spec.external.'spotbugs-annotations'
  api spec.product.'container-core'.'testfwk-offspring'
  api spec.external.'parseq-test-api'
  api spec.product.testfwk.'testfwk-core'
  api spec.product.testfwk.'testfwk-utils'

  implementation spec.external.'log4j1_2Api'

  testImplementation spec.external.'assertj-core'
  testImplementation spec.external.'mockito-inline'
}

integTest {
  useTestNG () {

    //uncomment below line to run specific test and use 'ligradle integTest -Dgroups=restli-filter'
    //includeGroups System.properties['groups']

    // show standard out and standard error of the test JVM(s) on the console
    testLogging.showStandardStreams = true

  }
}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.memoryMaximumSize = '4g'
}
