package com.linkedin.sales.common;

import com.google.common.base.Throwables;
import com.linkedin.cfg.mgr.LoaderException;
import com.linkedin.espresso.client.EspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClient;
import com.linkedin.espresso.client.ParSeqEspressoClientFactory;
import com.linkedin.espresso.client.factory.EspressoClientFactory;
import com.linkedin.espresso.client.r2d2impl.R2D2EspressoClient;
import com.linkedin.parseq.Engine;
import com.linkedin.parseq.EngineBuilder;
import com.linkedin.parseq.Task;
import com.linkedin.parseq.factory.BatchingFactory;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.util.factory.OffspringIntegrationTest;
import com.linkedin.util.factory.OffspringTestHelper;
import com.linkedin.util.factory.Scope;
import com.linkedin.util.factory.annotations.ConfigOverride;
import com.linkedin.util.factory.annotations.Import;
import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.testng.annotations.AfterClass;
import test.fwk.util.core.BaseTestConfUtil;

import static org.testng.Assert.*;


/**
 * This class provides helpful integration tests hooks.
 */
public abstract class IntegrationTestBase implements OffspringIntegrationTest {
  private static final Duration TASK_TIMEOUT = Duration.ofSeconds(30);
  private static final int NUM_CORES = Runtime.getRuntime().availableProcessors();
  private static final String DEFAULT_PARSEQ_ESPRESSO_CLIENT = "defaultParSeqEspressoClient";
  private static final String PARSEQ_ESPRESSO_CLIENT = "parSeqEspressoClient";
  private static final String ESPRESSO_CLIENT = "espressoClient";
  private static final String ESPRESSO_URI = "http://localhost:11936";
  private static final int PAUSE_MILLIS = 500;  // Allow materialized view to get the change

  protected OffspringTestHelper _offspringTestHelper;
  protected EspressoClient _espressoClient;
  protected ParSeqEspressoClient _parSeqEspressoClient;
  private Engine _parSeqEngine;

  /**
   * Sets up the factories and creates offspring objects
   */
  @Import(clazz = ParSeqEspressoClientFactory.class, prefix = PARSEQ_ESPRESSO_CLIENT)
  @Import(clazz = BatchingFactory.class)
  @ConfigOverride(clazz = ParSeqEspressoClientFactory.class, prefix = PARSEQ_ESPRESSO_CLIENT)
  protected void setUp() throws Exception {
    _offspringTestHelper = new OffspringTestHelper();
    try {
      BaseTestConfUtil.loadCfg(_offspringTestHelper);
    } catch (IOException | LoaderException ex) {
      throw new RuntimeException("Exception configuring offspring test helper", ex);
    }

    R2D2EspressoClient.Config config = new R2D2EspressoClient.Config();
    config.setUriPrefix(ESPRESSO_URI);
    _espressoClient = new R2D2EspressoClient(config);

    _offspringTestHelper.injectFactoryBean(EspressoClientFactory.class,
        Scope.ROOT.child(DEFAULT_PARSEQ_ESPRESSO_CLIENT).child(ESPRESSO_CLIENT), _espressoClient);

    // After this point no mocks can be added to testHelper.
    _offspringTestHelper.finishConfiguration();
    // Get bean after finishing configuration

    EngineBuilder parSeqEngineBuilder = new EngineBuilder()
        .setTaskExecutor(Executors.newFixedThreadPool(NUM_CORES + 1))
        .setTimerScheduler(Executors.newSingleThreadScheduledExecutor());
    parSeqEngineBuilder.setPlanDeactivationListener(_offspringTestHelper.getBean(BatchingFactory.class));
    _parSeqEngine = parSeqEngineBuilder.build();

    ParSeqEspressoClientFactory.Cfg overrides = new ParSeqEspressoClientFactory.Cfg();
    overrides.espressoClient = _espressoClient;
    _parSeqEspressoClient = _offspringTestHelper.configOverrideAndGetBean(ParSeqEspressoClientFactory.class,
        Scope.ROOT.child(PARSEQ_ESPRESSO_CLIENT), overrides);
  }

  /**
   * Cleans up all the remaining instances
   */
  @AfterClass
  public void tearDown() throws Exception {
    _offspringTestHelper.stopGenerator();
    _parSeqEngine.shutdown();
  }

  /**
   * Runs the given task and awaits its result.
   *
   * @param task the task to run
   * @param <T> type of the value of the task
   * @return the result of the task when successful
   */
  protected <T> T await(Task<T> task) {
    _parSeqEngine.run(task);

    try {
      task.await(TASK_TIMEOUT.getSeconds(), TimeUnit.SECONDS);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }

    return task.get();
  }

  /**
   * Same as {@link #await(Task)} but delays for delayMillis before running the task. This method should be used when
   * reading from Espresso materialized views or materialized aggregates since updates to those tables happen asynchronously.
   */
  protected <T> T awaitWithInitialDelay(Task<T> task, int delayMillis) {
    try {
      Thread.sleep(delayMillis);
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
    }
    return await(task);
  }

  /**
   * @see #awaitWithInitialDelay(Task, int)
   */
  protected <T> T awaitWithDefaultInitialDelay(Task<T> task) {
    return awaitWithInitialDelay(task, PAUSE_MILLIS);
  }

  /**
   * Runs a task and verifies that it finishes with an error.
   * @param task task to run
   * @param exceptionClass expected exception class
   * @param <T> expected exception type
   * @return error returned by the task
   */
  public <T extends Throwable> T awaitException(Task<?> task, Class<T> exceptionClass) {
    try {
      await(task);
      fail("An exception is expected, but the task succeeded");
      // just to make the compiler happy, we will never get here
      return null;
    } catch (PromiseException pe) {
      Throwable cause = pe.getCause();
      assertEquals(cause.getClass(), exceptionClass);
      return exceptionClass.cast(cause);
    }
  }
}
