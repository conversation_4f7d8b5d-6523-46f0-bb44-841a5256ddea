package com.linkedin.sales.ds.db;

import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJob;
import com.linkedin.sales.crm.avro.generated.CrmDataValidationExportJobStatus;
import org.apache.avro.AvroTypeException;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


/**
 * Created by jiawang at 8/20/2018
 * Test class for {@link CrmDataValidationExportJobDB}
 */
public class TestCrmDataValidationExportJobDB extends IntegrationTestBase {
  private static final String CRM_INSTANCE_ID1 = "crmInstanceId1";
  private static final String CRM_INSTANCE_ID2 = "crmInstanceId2";

  private CrmDataValidationExportJobDB _crmDataValidationExportJobDB;
  private Long _jobId;
  private CrmDataValidationExportJob _exportJob;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _crmDataValidationExportJobDB = new CrmDataValidationExportJobDB(_parSeqEspressoClient);

    _offspringTestHelper.startGenerator();

    _exportJob = new CrmDataValidationExportJob();
    _exportJob.status = CrmDataValidationExportJobStatus.PROCESSING;
    _exportJob.exportStartTime = 1L;
    _exportJob.exportEndTime = 2L;
    _exportJob.createdTime = 1000L;
    _exportJob.executionId = 5L;
  }

  @AfterClass
  public void tearDown() throws Exception {
    _espressoClient.execute(
        DeleteRequest.builder(CrmDataValidationExportJobDB.DB, CrmDataValidationExportJobDB.TABLE, CRM_INSTANCE_ID1)
            .build());
    super.tearDown();
  }

  @Test
  public void testCreate() {
    _jobId = await(_crmDataValidationExportJobDB.create(CRM_INSTANCE_ID1, _exportJob));
    assertThat(_jobId).isNotNull();
  }

  @Test
  public void testCreate2() {
    CrmDataValidationExportJob exportJob = new CrmDataValidationExportJob();
    exportJob.exportStartTime = 1L;
    try {
      await(_crmDataValidationExportJobDB.create(CRM_INSTANCE_ID1, exportJob));
    } catch (Exception e) {
      assertThat(e.getClass()).isEqualTo(AvroTypeException.class);
    }
  }

  @Test(dependsOnMethods = "testCreate")
  public void testGet() {
    CrmDataValidationExportJob actual = await(_crmDataValidationExportJobDB.get(CRM_INSTANCE_ID1, _jobId));
    assertThat(actual).isEqualTo(_exportJob);
  }

  @Test
  public void testGetNotFound() {
    RestLiServiceException e = awaitException(_crmDataValidationExportJobDB.get(CRM_INSTANCE_ID2, 1L),
        RestLiServiceException.class);
    assertThat(e.getStatus()).isEqualTo(HttpStatus.S_404_NOT_FOUND);
  }

  @Test(dependsOnMethods = "testCreate")
  public void testFind() {
    CrmDataValidationExportJob actual = await(_crmDataValidationExportJobDB.find(CRM_INSTANCE_ID1)).get(0);
    assertThat(actual).isEqualTo(_exportJob);
  }
}
