package com.linkedin.sales.ds.db;

import com.linkedin.common.AuditStamp;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.Address;
import com.linkedin.sales.espresso.Email;
import com.linkedin.sales.espresso.LeadEditableContactInfo;
import com.linkedin.sales.espresso.LeadProfileUnlockInfo;
import com.linkedin.sales.espresso.PhoneNumberType;
import com.linkedin.sales.espresso.SocialHandle;
import com.linkedin.sales.espresso.SocialHandleProvider;
import com.linkedin.sales.espresso.TypedPhoneNumber;
import com.linkedin.sales.espresso.Website;
import com.linkedin.sales.espresso.WebsiteCategory;
import com.linkedin.salesleadaccount.SalesLeadEditableContactInfoKey;
import com.linkedin.salesleadaccount.SalesLeadProfileUnlockInfo;
import java.net.URISyntaxException;
import java.util.Collections;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


public class TestLeadExtendedInfoDB extends IntegrationTestBase {

  private LssLeadExtendedInfoDB _lssLeadExtendedInfoDB;
  private static final SeatUrn SEAT_URN = new SeatUrn(111L);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(123L);
  private static final Long MEMBER_ID = 1111L;
  private static final Long MEMBER_ID_1 = 2222L;
  private static final MemberUrn MEMBER_URN;
  private static final MemberUrn MEMBER_URN_1;
  private static final String FULL_ADDRESS = "full address";
  private static final String USER_NAME = "test test";
  private static final String PHONE_NUMBER = "********";
  private static final long CREATED_TIME = 11111L;
  private static final String EMAIL = "<EMAIL>";
  static {
    try {
      MEMBER_URN =
          MemberUrn.createFromUrn(Urn.createFromTuple("member", MEMBER_ID));
      MEMBER_URN_1 = MemberUrn.createFromUrn(Urn.createFromTuple("member", MEMBER_ID_1));
    } catch (URISyntaxException e) {
      throw new RuntimeException();
    }
  }
  private static final SalesLeadEditableContactInfoKey key = new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN);
  private static final SalesLeadEditableContactInfoKey key1 = new SalesLeadEditableContactInfoKey().setContract(CONTRACT_URN).setMember(MEMBER_URN_1);

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssLeadExtendedInfoDB = new LssLeadExtendedInfoDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssLeadExtendedInfoDB.DB_LSS_LEAD_EXTENDED_INFO,
        LssLeadExtendedInfoDB.TABLE_LEAD_EDITABLE_CONTACT_INFO, MEMBER_URN.toString()).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssLeadExtendedInfoDB.DB_LSS_LEAD_EXTENDED_INFO,
        LssLeadExtendedInfoDB.TABLE_LEAD_EDITABLE_CONTACT_INFO, MEMBER_URN_1.toString()).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssLeadExtendedInfoDB.DB_LSS_LEAD_EXTENDED_INFO,
        LssLeadExtendedInfoDB.TABLE_LEAD_PROFILE_UNLOCK_INFO, MEMBER_URN.toString()).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssLeadExtendedInfoDB.DB_LSS_LEAD_EXTENDED_INFO,
        LssLeadExtendedInfoDB.TABLE_LEAD_PROFILE_UNLOCK_INFO, MEMBER_URN_1.toString()).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateAndGetLeadEditableContactInfoSuccess() {
    createLeadEditableContactInfoRecord();
    LeadEditableContactInfo leadEditableContactInfo = await(_lssLeadExtendedInfoDB.getLeadEditableContactInfo(key));
    assertThat(leadEditableContactInfo).isEqualTo(createSalesLeadeditableContactInfo());
  }

  @Test
  public void testCreateLeadEditableContactInfo() {
    HttpStatus httpStatus = createLeadEditableContactInfoRecord();
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testUpdateLeadEditableContactInfo() {
    createLeadEditableContactInfoRecord();
    HttpStatus httpStatus = createLeadEditableContactInfoRecord();
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testCreateLeadProfileUnlockInfo() {
    HttpStatus httpStatus = createSalesLeadProfileUnlockRecord();
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testGetLeadProfileUnlockInfo() {
    createSalesLeadProfileUnlockRecord();
    LeadProfileUnlockInfo leadProfileUnlockInfo = new LeadProfileUnlockInfo();
    leadProfileUnlockInfo.unlockedTime = 111L;
    leadProfileUnlockInfo.unlockedBySeatUrn = SEAT_URN.toString();
    LeadProfileUnlockInfo leadProfileUnlockInfoRes = await(_lssLeadExtendedInfoDB.getLeadProfileUnlockInfo(MEMBER_URN, CONTRACT_URN));
    assertThat(leadProfileUnlockInfoRes).isEqualTo(leadProfileUnlockInfo);
  }

  @Test
  public void testGetLeadEditableContactInfoFailNotFound() {
    createLeadEditableContactInfoRecord();
    assertThatExceptionOfType(PromiseException.class)
        .isThrownBy(() -> {await(_lssLeadExtendedInfoDB.getLeadEditableContactInfo(key1));})
        .withCauseInstanceOf(EntityNotFoundException.class);
  }
  @Test
  public void testGetLeadEditableContactInfoSuccess() {
    createLeadEditableContactInfoRecord();
    LeadEditableContactInfo leadEditableContactInfo = await(_lssLeadExtendedInfoDB.getLeadEditableContactInfo(key));
    assertThat(leadEditableContactInfo).isEqualTo(createLeadEditableContactInfo());
  }

  private HttpStatus createLeadEditableContactInfoRecord() {
    HttpStatus status = await(_lssLeadExtendedInfoDB.upsertLeadEditableContactInfo(key, createLeadEditableContactInfo()));
    return status;
  }

  private LeadEditableContactInfo createSalesLeadeditableContactInfo() {
    LeadEditableContactInfo leadEditableContactInfo = new LeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN.toString();
    Address address = new Address();
    address.fullAddress = FULL_ADDRESS;
    SocialHandle socialHandle = new SocialHandle();
    socialHandle.username = USER_NAME;
    socialHandle.provider = SocialHandleProvider.QQ;
    TypedPhoneNumber phoneNumber = new TypedPhoneNumber();
    phoneNumber.type = PhoneNumberType.FAX;
    phoneNumber.number = PHONE_NUMBER;
    Website website = new Website();
    website.url = "www.test.com";
    website.category = WebsiteCategory.BLOG;
    Email email = new Email();
    email.email = EMAIL;
    leadEditableContactInfo.addresses = Collections.singletonList(address);
    leadEditableContactInfo.socialHandles = Collections.singletonList(socialHandle);
    leadEditableContactInfo.lastModifiedBySeatUrn = SEAT_URN.toString();
    leadEditableContactInfo.typedPhoneNumbers = Collections.singletonList(phoneNumber);
    leadEditableContactInfo.websites = Collections.singletonList(website);
    leadEditableContactInfo.emails = Collections.singletonList(email);
    leadEditableContactInfo.lastModifiedTime = CREATED_TIME;
    leadEditableContactInfo.createdTime = CREATED_TIME;
    return leadEditableContactInfo;
  }

  private LeadEditableContactInfo createLeadEditableContactInfo () {
    LeadEditableContactInfo leadEditableContactInfo = new LeadEditableContactInfo();
    leadEditableContactInfo.creatorSeatUrn = SEAT_URN.toString();
    Address address = new Address();
    address.fullAddress = FULL_ADDRESS;
    SocialHandle socialHandle = new SocialHandle();
    socialHandle.username = USER_NAME;
    socialHandle.provider = SocialHandleProvider.QQ;
    TypedPhoneNumber phoneNumber = new TypedPhoneNumber();
    phoneNumber.type = PhoneNumberType.FAX;
    phoneNumber.number = PHONE_NUMBER;
    Website website = new Website();
    website.url = "www.test.com";
    website.category = WebsiteCategory.BLOG;
    Email email = new Email();
    email.email = EMAIL;
    leadEditableContactInfo.addresses = Collections.singletonList(address);
    leadEditableContactInfo.socialHandles = Collections.singletonList(socialHandle);
    leadEditableContactInfo.lastModifiedBySeatUrn = SEAT_URN.toString();
    leadEditableContactInfo.typedPhoneNumbers = Collections.singletonList(phoneNumber);
    leadEditableContactInfo.websites = Collections.singletonList(website);
    leadEditableContactInfo.emails = Collections.singletonList(email);
    leadEditableContactInfo.lastModifiedTime = CREATED_TIME;
    leadEditableContactInfo.createdTime = CREATED_TIME;
    return leadEditableContactInfo;
  }
  private HttpStatus createSalesLeadProfileUnlockRecord () {
    LeadProfileUnlockInfo leadProfileUnlockInfo = new LeadProfileUnlockInfo();
    leadProfileUnlockInfo.unlockedBySeatUrn = SEAT_URN.toString();
    leadProfileUnlockInfo.unlockedTime = 111L;
    HttpStatus status = await(_lssLeadExtendedInfoDB.createLeadProfileUnlockInfo(MEMBER_URN, CONTRACT_URN, leadProfileUnlockInfo));
    return status;
  }

  private SalesLeadProfileUnlockInfo createSalesLeadProfileUnlockInfo() {
    AuditStamp auditStamp = new AuditStamp();
    auditStamp.setTime(111L);
    auditStamp.setActor(SEAT_URN);
    return new SalesLeadProfileUnlockInfo()
        .setMember(MEMBER_URN)
        .setUnlockedAt(auditStamp)
        .setContract(CONTRACT_URN);
  }
}