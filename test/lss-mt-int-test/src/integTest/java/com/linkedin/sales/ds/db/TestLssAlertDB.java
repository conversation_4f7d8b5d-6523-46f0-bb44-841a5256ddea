package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.EntityAlert;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


public class TestLssAlertDB extends IntegrationTestBase {
  private LssAlertDB _lssAlertDB;

  private static EntityAlert alert = new EntityAlert();

  private static final String CONTENT_URN = "urn:li:ingestedContent:1002";
  private static final String TYPE_1 = "ACCOUNT_MENTIONED_IN_THE_NEWS";
  private static final String TYPE_2 = "ACCOUNT_SHARED_UPDATE";
  private static final String TYPE_3 = "ACCOUNT_RAISED_MONEY";
  private static final String SERIALIZED_ENTITY_URN = "urn:li:organization:123";
  private static final Urn ENTITY_URN;
  static {
    try {
      ENTITY_URN = OrganizationUrn.deserialize(SERIALIZED_ENTITY_URN);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssAlertDB = new LssAlertDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();

  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAlertDB.DB_LSS_ALERT, LssAlertDB.TABLE_ENTITY_ALERT, SERIALIZED_ENTITY_URN).build()));
    alert.alertType = TYPE_1;
    alert.contentUrn = CONTENT_URN;
    alert.createdTime = System.currentTimeMillis();
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateAndDeleteAlert() {
    HttpStatus status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + 10000000L));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    List<Pair<Long, EntityAlert>> results =
        await(_lssAlertDB.findByCriteria(ENTITY_URN, Collections.emptySet(), 0, 10));
    assertThat(results.size()).isEqualTo(1);

    Long alertId = results.get(0).getFirst();
    status = await(_lssAlertDB.deleteAlert(ENTITY_URN, alertId));
    assertThat(status).isEqualTo(HttpStatus.S_204_NO_CONTENT);
    results = await(_lssAlertDB.findByCriteria(ENTITY_URN, Collections.emptySet(), 0, 10));
    assertThat(results.size()).isEqualTo(0);
  }

  @Test
  public void testFindAlertWithExpiration() throws InterruptedException{
    HttpStatus status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + 10000000L));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    // the first one (of type1) is without expiration, while the second one (type2) is with expiration
    alert.alertType = TYPE_2;
    Long sleepTime = 1500L;
    status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + sleepTime));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    List<Pair<Long, EntityAlert>> results =
        await(_lssAlertDB.findByCriteria(ENTITY_URN, Collections.emptySet(), 0, 10));
    assertThat(results.size()).isEqualTo(2);

    Thread.sleep(sleepTime);
    results = await(_lssAlertDB.findByCriteria(ENTITY_URN, Collections.emptySet(), 0, 10));
    assertThat(results.size()).isEqualTo(1);
    EntityAlert result = results.get(0).getSecond();
    assertThat(result.alertType.toString()).isEqualTo(TYPE_1);
    assertThat(result.contentUrn.toString()).isEqualTo(CONTENT_URN);
  }

  @Test
  public void testFindAlertWithTypeFilter() throws InterruptedException {
    HttpStatus status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + 10000000L));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    // the second one inserted has a larger timestamp, and it should be ranked before the first one
    alert.createdTime = System.currentTimeMillis();
    status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + 10000000L));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    // insert another alert with a different alertType
    alert.alertType = TYPE_2;
    status = await(_lssAlertDB.createAlert(ENTITY_URN, alert, alert.createdTime + 10000000L));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    List<Pair<Long, EntityAlert>> results =
        await(_lssAlertDB.findByCriteria(ENTITY_URN, ImmutableSet.of(TYPE_1, TYPE_2, TYPE_3), 0, 10));
    assertThat(results.size()).isEqualTo(3);

    results = await(_lssAlertDB.findByCriteria(ENTITY_URN, ImmutableSet.of(TYPE_1), 0, 10));
    assertThat(results.size()).isEqualTo(2);
    EntityAlert result1 = results.get(0).getSecond();
    assertThat(result1.alertType.toString()).isEqualTo(TYPE_1);
    assertThat(result1.createdTime).isEqualTo(alert.createdTime);
    EntityAlert result2 = results.get(1).getSecond();
    assertThat(result2.alertType.toString()).isEqualTo(TYPE_1);
    assertThat(result1.createdTime).isGreaterThan(result2.createdTime);
  }
}
