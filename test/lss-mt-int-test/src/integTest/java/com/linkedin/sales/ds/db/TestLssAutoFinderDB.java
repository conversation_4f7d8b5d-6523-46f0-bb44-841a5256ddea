package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.AccountPlay;
import com.linkedin.sales.espresso.AccountPlaysMetadata;
import com.linkedin.sales.espresso.AccountPlaysMetadataV2;
import com.linkedin.sales.service.utils.UrnUtils;
import com.linkedin.util.Pair;

import java.util.*;

import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;
import static org.testng.AssertJUnit.assertTrue;


public class TestLssAutoFinderDB extends IntegrationTestBase {
  private LssAutoFinderDB _lssAutoFinderDB;
  private static final SeatUrn SEAT_URN = new SeatUrn(1231L);
  private static final OrganizationUrn ORGANIZATION_URN_1 = UrnUtils.createOrganizationUrn(456L);
  private static final OrganizationUrn ORGANIZATION_URN_2 = UrnUtils.createOrganizationUrn(789L);
  private static final MemberUrn MEMBER_URN_1 = new MemberUrn(1000L);
  private static final MemberUrn MEMBER_URN_2 = new MemberUrn(2000L);
  private static final MemberUrn MEMBER_URN_3 = new MemberUrn(3000L);
  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssAutoFinderDB = new LssAutoFinderDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoFinderDB.DB_LSS_AUTO_FINDER, LssAutoFinderDB.TABLE_ACCOUNT_PLAYS_METADATA, SEAT_URN.toString(),
        ORGANIZATION_URN_1.toString()).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoFinderDB.DB_LSS_AUTO_FINDER, LssAutoFinderDB.TABLE_ACCOUNT_PLAYS_METADATA_V2, SEAT_URN.toString(),
        ORGANIZATION_URN_1.toString()).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoFinderDB.DB_LSS_AUTO_FINDER, LssAutoFinderDB.TABLE_ACCOUNT_PLAY, SEAT_URN.toString(),
        ORGANIZATION_URN_1.toString()).build()));
  }

  @Test
  public void testCreateAndUpdateAccountPlaysMetadata() {
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    long timeStamp = System.currentTimeMillis();
    accountPlaysMetadata.setLastRunTimestamp(timeStamp);
    accountPlaysMetadata.setDismissedLeads(ImmutableList.of(10L));
    HttpStatus status = await(_lssAutoFinderDB.createAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1, accountPlaysMetadata));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    AccountPlaysMetadata actualAccountPlaysMetadata = await(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadata.getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(actualAccountPlaysMetadata.getDismissedLeads()).containsExactlyInAnyOrderElementsOf(ImmutableList.of(10L));
    assertThat(actualAccountPlaysMetadata.getLastRunTimestamp()).isEqualTo(timeStamp);
    assertThat(actualAccountPlaysMetadata.getLastModifiedTime()).isGreaterThanOrEqualTo(timeStamp);
    long createdTime = actualAccountPlaysMetadata.getCreatedTime();

    // Create duplicate fail
    RestLiServiceException restLiServiceException =
        awaitException(_lssAutoFinderDB.createAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1, accountPlaysMetadata),
            RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);

    // Update dismissed leads only
    accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setDismissedLeads(ImmutableList.of(10L, 20L));
    status = await(_lssAutoFinderDB.partialUpdateAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1, accountPlaysMetadata));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);

    actualAccountPlaysMetadata = await(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadata.getCreatedTime()).isEqualTo(createdTime);
    assertThat(actualAccountPlaysMetadata.getDismissedLeads()).containsExactlyInAnyOrderElementsOf(ImmutableList.of(10L, 20L));
    assertThat(actualAccountPlaysMetadata.getLastRunTimestamp()).isEqualTo(timeStamp);
    assertThat(actualAccountPlaysMetadata.getLastModifiedTime()).isGreaterThanOrEqualTo(createdTime);
    long lastModifiedTime = actualAccountPlaysMetadata.getLastModifiedTime();

    // Update last run timestamp only
    accountPlaysMetadata = new AccountPlaysMetadata();
    accountPlaysMetadata.setLastRunTimestamp(timeStamp + 10);
    status = await(_lssAutoFinderDB.partialUpdateAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1, accountPlaysMetadata));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);

    actualAccountPlaysMetadata = await(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadata.getCreatedTime()).isEqualTo(createdTime);
    assertThat(actualAccountPlaysMetadata.getDismissedLeads()).containsExactlyInAnyOrderElementsOf(ImmutableList.of(10L, 20L));
    assertThat(actualAccountPlaysMetadata.getLastRunTimestamp()).isEqualTo(timeStamp + 10);
    assertThat(actualAccountPlaysMetadata.getLastModifiedTime()).isGreaterThanOrEqualTo(lastModifiedTime);

    // Update non-existing seat account
    restLiServiceException = awaitException(_lssAutoFinderDB.partialUpdateAccountPlaysMetadata(new SeatUrn(9999L), ORGANIZATION_URN_1, accountPlaysMetadata),
        RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testCreateAndDeleteAccountPlaysMetadataV2() {
    long timeStamp = System.currentTimeMillis();
    HttpStatus status = await(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_1));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    status = await(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_2));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    List<Pair<MemberUrn, AccountPlaysMetadataV2>> actualAccountPlaysMetadataV2s =
        await(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadataV2s.size()).isEqualTo(2);
    List<MemberUrn> expectedMemberUrns = ImmutableList.of(MEMBER_URN_1, MEMBER_URN_2);
    assertTrue(expectedMemberUrns.contains(actualAccountPlaysMetadataV2s.get(0).getFirst()));
    assertThat(actualAccountPlaysMetadataV2s.get(0).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertTrue(expectedMemberUrns.contains(actualAccountPlaysMetadataV2s.get(0).getFirst()));
    assertThat(actualAccountPlaysMetadataV2s.get(1).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);

    // Create duplicate fail
    RestLiServiceException restLiServiceException =
        awaitException(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_1),
            RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);

    // Delete
    status = await(_lssAutoFinderDB.deleteAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_1));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);
    actualAccountPlaysMetadataV2s = await(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadataV2s.size()).isEqualTo(1);
  }

  @Test
  public void testCreateAndUpdateAccountPlay() {
    long createTime = System.currentTimeMillis();
    AccountPlay accountPlay1 = new AccountPlay();
    accountPlay1.setPlayType("play1");
    accountPlay1.setCurrentLeads(ImmutableList.of(1L, 2L, 3L));
    accountPlay1.setPastLeads(ImmutableList.of(4L, 5L, 6L));
    accountPlay1.setPersonaLocalId(35);

    AccountPlay accountPlay2 = new AccountPlay();
    accountPlay2.setPlayType("play2");
    accountPlay2.setCurrentLeads(ImmutableList.of(11L, 22L, 33L));
    accountPlay2.setPastLeads(Collections.emptyList());
    Map<Integer, AccountPlay> accountPlayMap = new HashMap<>();
    Integer id1 = await(_lssAutoFinderDB.createAccountPlay(SEAT_URN, ORGANIZATION_URN_1, accountPlay1));
    accountPlayMap.put(id1, accountPlay1);
    Integer id2 = await(_lssAutoFinderDB.createAccountPlay(SEAT_URN, ORGANIZATION_URN_1, accountPlay2));
    accountPlayMap.put(id2, accountPlay2);
    List<Pair<Integer, AccountPlay>> actual = await(_lssAutoFinderDB.
        getAccountPlays(SEAT_URN, ORGANIZATION_URN_1, 0, 5));

    actual.forEach(pair -> {
      AccountPlay expectedPlay = accountPlayMap.get(pair.getKey());
      AccountPlay actualAccountPlay = pair.getValue();
      assertAccountPlay(expectedPlay, actualAccountPlay, createTime, createTime);
    });

    accountPlay1.setPastLeads(Collections.emptyList());
    accountPlay2.setPastLeads(ImmutableList.of(11L, 22L, 33L));
    accountPlay2.setCurrentLeads(ImmutableList.of(44L, 55L, 66L));
    long lastModTime = System.currentTimeMillis();
    HttpStatus update1 = await(_lssAutoFinderDB.partialUpdateAccountPlay(SEAT_URN, ORGANIZATION_URN_1, id1, accountPlay1));
    HttpStatus update2 = await(_lssAutoFinderDB.partialUpdateAccountPlay(SEAT_URN, ORGANIZATION_URN_1, id2, accountPlay2));
    assertThat(update1).isEqualTo(HttpStatus.S_200_OK);
    assertThat(update2).isEqualTo(HttpStatus.S_200_OK);

    actual = await(_lssAutoFinderDB.getAccountPlays(SEAT_URN, ORGANIZATION_URN_1, 0, 5));
    actual.forEach(pair -> {
      AccountPlay expectedPlay = accountPlayMap.get(pair.getKey());
      AccountPlay actualAccountPlay = pair.getValue();
      assertAccountPlay(expectedPlay, actualAccountPlay, createTime, lastModTime);
    });

    // Update non-existing account play
    RestLiServiceException updateFail =
        awaitException(_lssAutoFinderDB.partialUpdateAccountPlay(SEAT_URN, ORGANIZATION_URN_1, 9999, accountPlay1),
            RestLiServiceException.class);
    assertThat(updateFail.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testGetAccountPlaysMetadataFailNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() ->
        await(_lssAutoFinderDB.getAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1)))
        .withCause(new EntityNotFoundException(null, String.format("Cannot find AccountPlaysMetadata for seat %s and company %s",
        SEAT_URN, ORGANIZATION_URN_1)));
  }

  @Test
  public void testGetAccountPlaysEmptyList() {
    List<Pair<Integer, AccountPlay>> actual = await(_lssAutoFinderDB.
        getAccountPlays(SEAT_URN, ORGANIZATION_URN_1, 0, 5));
    assertThat(actual).isEmpty();
  }

  @Test
  public void testGetAccountPlaysMetadataV2EmptyList() {
    List<Pair<MemberUrn, AccountPlaysMetadataV2>> actual = await(_lssAutoFinderDB.
        getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actual).isEmpty();
  }

  @Test
  public void testCreateAccountPlaysMetadataV2WithMultiPut() {
    long timeStamp = System.currentTimeMillis();
    // Create with MultiPut
    Map<OrganizationUrn, Set<MemberUrn>> organizationUrnSetMap = new HashMap<>();
    organizationUrnSetMap.put(ORGANIZATION_URN_1, ImmutableSet.of(MEMBER_URN_1, MEMBER_URN_2));
    Map<OrganizationUrn, Map<MemberUrn, HttpStatus>> statusMap =
        await(_lssAutoFinderDB.createAccountPlaysMetadataV2s(SEAT_URN, organizationUrnSetMap));
    assertThat(statusMap.size()).isEqualTo(1);
    assertThat(statusMap.get(ORGANIZATION_URN_1).size()).isEqualTo(2);
    assertThat(statusMap.get(ORGANIZATION_URN_1).get(MEMBER_URN_1)).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(statusMap.get(ORGANIZATION_URN_1).get(MEMBER_URN_1)).isEqualTo(HttpStatus.S_201_CREATED);

    List<Pair<MemberUrn, AccountPlaysMetadataV2>> actualAccountPlaysMetadataV2s =
        await(_lssAutoFinderDB.getAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1));
    assertThat(actualAccountPlaysMetadataV2s.size()).isEqualTo(2);
    List<MemberUrn> expectedMemberUrns = ImmutableList.of(MEMBER_URN_1, MEMBER_URN_2);
    assertTrue(expectedMemberUrns.contains(actualAccountPlaysMetadataV2s.get(0).getFirst()));
    assertThat(actualAccountPlaysMetadataV2s.get(0).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertTrue(expectedMemberUrns.contains(actualAccountPlaysMetadataV2s.get(0).getFirst()));
    assertThat(actualAccountPlaysMetadataV2s.get(1).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
  }

  @Test
  public void testGetAccountPlaysMetadataForSeat() {
    // Create multiple AccountPlaysMetadata for different organizations
    AccountPlaysMetadata accountPlaysMetadata = new AccountPlaysMetadata();
    long timeStamp = System.currentTimeMillis();
    accountPlaysMetadata.setLastRunTimestamp(timeStamp);
    accountPlaysMetadata.setDismissedLeads(ImmutableList.of(10L));
    HttpStatus status = await(_lssAutoFinderDB.createAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_1, accountPlaysMetadata));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    accountPlaysMetadata.setDismissedLeads(ImmutableList.of(10L, 20L));
    status = await(_lssAutoFinderDB.createAccountPlaysMetadata(SEAT_URN, ORGANIZATION_URN_2, accountPlaysMetadata));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    // Get AccountPlaysMetadatas for seat
    Map<OrganizationUrn, AccountPlaysMetadata> resultMap = await(_lssAutoFinderDB.getAccountPlaysMetadataForSeat(SEAT_URN));
    assertThat(resultMap.size()).isEqualTo(2);
    assertTrue(resultMap.containsKey(ORGANIZATION_URN_1));
    assertTrue(resultMap.containsKey(ORGANIZATION_URN_2));

    assertThat(resultMap.get(ORGANIZATION_URN_1).getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_2).getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_1).getLastRunTimestamp()).isEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_2).getLastRunTimestamp()).isEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_1).getLastModifiedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_2).getLastModifiedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_1).getDismissedLeads()).containsExactlyInAnyOrderElementsOf(ImmutableList.of(10L));
    assertThat(resultMap.get(ORGANIZATION_URN_2).getDismissedLeads()).containsExactlyInAnyOrderElementsOf(ImmutableList.of(10L, 20L));
  }

  @Test
  public void testGetAccountPlaysMetadataV2ForSeat() {
    // Create multiple AccountPlaysMetadataV2 for different organizations
    AccountPlaysMetadataV2 accountPlaysMetadataV2 = new AccountPlaysMetadataV2();
    long timeStamp = System.currentTimeMillis();
    accountPlaysMetadataV2.setCreatedTime(timeStamp);
    HttpStatus status = await(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_1));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    status = await(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_2, MEMBER_URN_2));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
    status = await(_lssAutoFinderDB.createAccountPlaysMetadataV2(SEAT_URN, ORGANIZATION_URN_1, MEMBER_URN_3));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    // Get AccountPlaysMetadataV2s for seat
    Map<OrganizationUrn, List<Pair<MemberUrn, AccountPlaysMetadataV2>>> resultMap = await(_lssAutoFinderDB.getAccountPlaysMetadataV2ForSeat(SEAT_URN));
    assertThat(resultMap.size()).isEqualTo(2);
    assertTrue(resultMap.containsKey(ORGANIZATION_URN_1));
    assertTrue(resultMap.containsKey(ORGANIZATION_URN_2));

    assertThat(resultMap.get(ORGANIZATION_URN_1).size()).isEqualTo(2);
    assertThat(resultMap.get(ORGANIZATION_URN_2).size()).isEqualTo(1);
    assertThat(resultMap.get(ORGANIZATION_URN_1).get(0).getFirst()).isIn(Arrays.asList(MEMBER_URN_1, MEMBER_URN_3));
    assertThat(resultMap.get(ORGANIZATION_URN_1).get(0).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_1).get(1).getFirst()).isIn(Arrays.asList(MEMBER_URN_1, MEMBER_URN_3));
    assertThat(resultMap.get(ORGANIZATION_URN_1).get(1).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
    assertThat(resultMap.get(ORGANIZATION_URN_2).get(0).getFirst()).isEqualTo(MEMBER_URN_2);
    assertThat(resultMap.get(ORGANIZATION_URN_2).get(0).getSecond().getCreatedTime()).isGreaterThanOrEqualTo(timeStamp);
  }

  private void assertAccountPlay(AccountPlay expectedAccountPlay, AccountPlay actualAccountPlay,
      long createTime, long lastModifiedTime) {
    assertThat(actualAccountPlay.getCreatedTime()).isGreaterThanOrEqualTo(createTime);
    assertThat(actualAccountPlay.getLastModifiedTime()).isGreaterThanOrEqualTo(lastModifiedTime);
    assertThat(actualAccountPlay.getPlayType().toString()).isEqualTo(expectedAccountPlay.getPlayType().toString());
    assertThat(actualAccountPlay.getPersonaLocalId()).isEqualTo(expectedAccountPlay.getPersonaLocalId());
    assertThat(actualAccountPlay.getCurrentLeads()).isEqualTo(expectedAccountPlay.getCurrentLeads());
    assertThat(actualAccountPlay.getPastLeads()).isEqualTo(expectedAccountPlay.getPastLeads());
  }
}
