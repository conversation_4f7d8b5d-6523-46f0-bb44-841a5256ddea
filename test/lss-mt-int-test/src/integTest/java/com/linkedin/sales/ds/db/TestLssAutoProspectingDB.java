package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.restli.server.RestLiServiceException;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.Campaign;
import com.linkedin.parseq.Task;
import com.linkedin.sales.espresso.LeadFindingRunError;
import com.linkedin.sales.espresso.LeadFindingRun;
import com.linkedin.sales.espresso.LeadFindingRunLead;
import pegasus.com.linkedin.salesautoprospecting.LeadFindingRunLeadKey;
import com.linkedin.util.Pair;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.linkedin.sales.espresso.SearchCriteriaV1;
import joptsimple.internal.Strings;
import org.junit.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import pegasus.com.linkedin.salesautoprospecting.LeadFindingRunKey;

import static org.assertj.core.api.Assertions.*;
import static org.junit.Assert.*;

/**
 * Test class for LssAutoProspectingDB
 */
public class TestLssAutoProspectingDB extends IntegrationTestBase {
  private static final SeatUrn SEAT_URN = new SeatUrn(123L);
  private final static String RAW_CRITERIA = "Must locate in the US";
  private final static String HASHED_CRITERIA_1 = "testHashedCriteria1";
  private final static String HASHED_CRITERIA_2 = "testHashedCriteria2";
  private final static String FACET_SELECTIONS_1 = "facetSelections=[{valuesWithSelections=[{value=123, selected=true}]}]";
  private final static String FACET_SELECTIONS_2 = "facetSelections=[{valuesWithSelections=[{value=123, selected=false}]}]";
  private final static String EBR_SEARCH_QUERIES_1 = "Someone is known for good buyer";
  private final static String EBR_SEARCH_QUERIES_2 = "Lead must have written a book";
  private static final ContractUrn CONTRACT_URN = new ContractUrn(456L);
  private static final Long CAMPAIGN_ID_1 = 567L;
  private static final Long CAMPAIGN_ID_2 = 678L;
  private static final long runId = 1L;
  private static final MemberUrn memberUrn = new MemberUrn(1234L);

  private LssAutoProspectingDB _lssAutoProspectingDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssAutoProspectingDB = new LssAutoProspectingDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoProspectingDB.DB_LSS_AUTO_PROSPECTING, LssAutoProspectingDB.TABLE_SEARCH_CRITERIA_V1,
        SEAT_URN.toString(), HASHED_CRITERIA_1).build()));

    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoProspectingDB.DB_LSS_AUTO_PROSPECTING, LssAutoProspectingDB.TABLE_LEAD_FINDING_RUN,
        SEAT_URN.toString(), CAMPAIGN_ID_1.toString()).build()));

    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoProspectingDB.DB_LSS_AUTO_PROSPECTING, LssAutoProspectingDB.TABLE_CAMPAIGN,
        SEAT_URN.toString()).build()));

    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssAutoProspectingDB.DB_LSS_AUTO_PROSPECTING, LssAutoProspectingDB.TABLE_LEAD_FINDING_RUN_LEAD,
        String.valueOf(runId), memberUrn.toString()).build()));
  }

  @Test
  public void testCreateAndPartialUpdateSearchCriteria() {
    // Create SearchCriteria
    SearchCriteriaV1 searchCriteria1 = new SearchCriteriaV1();
    searchCriteria1.setRawCriteria(RAW_CRITERIA);
    searchCriteria1.setFacetSelections(FACET_SELECTIONS_1);
    searchCriteria1.setEbrSearchQuery(EBR_SEARCH_QUERIES_1);
    searchCriteria1.setContractUrn(CONTRACT_URN.toString());

    long createTimestamp = System.currentTimeMillis();
    HttpStatus status = await(_lssAutoProspectingDB.createSearchCriteria(SEAT_URN, HASHED_CRITERIA_1, searchCriteria1));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    Optional<SearchCriteriaV1> optionalSearchCriteria = await(_lssAutoProspectingDB.getSearchCriteria(SEAT_URN,
        HASHED_CRITERIA_1));
    assertTrue(optionalSearchCriteria.isPresent());
    SearchCriteriaV1 searchCriteria = optionalSearchCriteria.get();
    assertThat(searchCriteria.getCreatedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertThat(searchCriteria.getLastModifiedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertSearchCriteria(searchCriteria, searchCriteria1);

    // Create fail due to duplicate record
    RestLiServiceException restLiServiceException =
        awaitException(_lssAutoProspectingDB.createSearchCriteria(SEAT_URN, HASHED_CRITERIA_1, searchCriteria1),
            RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);

    // Update SearchCriteria
    SearchCriteriaV1 searchCriteria2 = new SearchCriteriaV1();
    searchCriteria2.setRawCriteria(RAW_CRITERIA);
    searchCriteria2.setFacetSelections(FACET_SELECTIONS_2);
    searchCriteria2.setEbrSearchQuery(EBR_SEARCH_QUERIES_2);
    searchCriteria2.setContractUrn(CONTRACT_URN.toString());

    long updateTimestamp = System.currentTimeMillis();
    status = await(_lssAutoProspectingDB.partialUpdateSearchCriteria(SEAT_URN, HASHED_CRITERIA_1, searchCriteria2));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);

    optionalSearchCriteria = await(_lssAutoProspectingDB.getSearchCriteria(SEAT_URN, HASHED_CRITERIA_1));
    assertTrue(optionalSearchCriteria.isPresent());
    searchCriteria = optionalSearchCriteria.get();
    assertThat(searchCriteria.getCreatedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertThat(searchCriteria.getLastModifiedTime()).isGreaterThanOrEqualTo(updateTimestamp);
    assertSearchCriteria(searchCriteria, searchCriteria2);

    // Update fail due to record does not exist
    restLiServiceException =
        awaitException(_lssAutoProspectingDB.partialUpdateSearchCriteria(SEAT_URN, HASHED_CRITERIA_2, searchCriteria2),
            RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test (description = "Test get SearchCriteria but record does not exist")
  public void testGetSearchCriteriaNotFound() {
    Optional<SearchCriteriaV1> optionalSearchCriteria = await(_lssAutoProspectingDB.getSearchCriteria(SEAT_URN, HASHED_CRITERIA_1));
    Assert.assertFalse(optionalSearchCriteria.isPresent());
  }

  @Test (description = "Test get SearchCriteria but record does not exist")
  public void testCreateSearchCriteriaWithLongPrimaryKey() {
    SearchCriteriaV1 searchCriteria1 = new SearchCriteriaV1();
    searchCriteria1.setRawCriteria(RAW_CRITERIA);
    searchCriteria1.setFacetSelections(FACET_SELECTIONS_1);
    searchCriteria1.setEbrSearchQuery(EBR_SEARCH_QUERIES_1);
    searchCriteria1.setContractUrn(CONTRACT_URN.toString());

    long createTimestamp = System.currentTimeMillis();
    HttpStatus status = await(_lssAutoProspectingDB.createSearchCriteria(SEAT_URN, Strings.repeat('a', 70), searchCriteria1));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);

    Optional<SearchCriteriaV1> optionalSearchCriteria = await(_lssAutoProspectingDB.getSearchCriteria(SEAT_URN,
        Strings.repeat('a', 70)));
    assertTrue(optionalSearchCriteria.isPresent());
    SearchCriteriaV1 searchCriteria = optionalSearchCriteria.get();
    assertThat(searchCriteria.getCreatedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertThat(searchCriteria.getLastModifiedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertSearchCriteria(searchCriteria, searchCriteria1);
  }

  private void assertSearchCriteria(SearchCriteriaV1 actualSearchCriteria, SearchCriteriaV1 expectedSearchCriteria) {
    assertThat(actualSearchCriteria.getRawCriteria().toString()).isEqualTo(expectedSearchCriteria.getRawCriteria().toString());
    assertThat(actualSearchCriteria.getFacetSelections().toString()).isEqualTo(expectedSearchCriteria.getFacetSelections().toString());
    assertThat(actualSearchCriteria.getEbrSearchQuery().toString()).isEqualTo(expectedSearchCriteria.getEbrSearchQuery().toString());
    assertThat(actualSearchCriteria.getContractUrn().toString()).isEqualTo(expectedSearchCriteria.getContractUrn().toString());
  }

  @Test(description = "Test get Campaign but record does not exist")
  public void testGetCampaignNotFound() {
    awaitException(_lssAutoProspectingDB.getCampaign(SEAT_URN, CAMPAIGN_ID_1), EntityNotFoundException.class);
  }

  @Test
  public void testCreateAndPartialUpdateCampaign() {
    // Create Campaign
    Campaign campaign = new Campaign();
    campaign.setContractUrn(CONTRACT_URN.toString());
    campaign.setProductId("testProduct123");

    // Set up account list URNs
    List<CharSequence> accountListUrns = new ArrayList<>();
    accountListUrns.add("urn:li:salesList:789");
    campaign.setAccountListUrns(accountListUrns);

    // Set up account URNs
    List<CharSequence> accountUrns = new ArrayList<>();
    accountUrns.add("urn:li:organization:101112");
    campaign.setAccountUrns(accountUrns);

    long createTimestamp = System.currentTimeMillis();
    Long campaignId = await(_lssAutoProspectingDB.createCampaign(SEAT_URN, campaign));

    // Verify campaign ID is returned
    assertThat(campaignId).isNotNull();
    assertThat(campaignId).isGreaterThan(0L);

    // Verify campaign was created correctly
    Campaign retrievedCampaign = await(_lssAutoProspectingDB.getCampaign(SEAT_URN, campaignId));

    // Verify campaign fields
    assertThat(retrievedCampaign.getContractUrn().toString()).isEqualTo(campaign.getContractUrn().toString());
    assertThat(retrievedCampaign.getProductId().toString()).isEqualTo(campaign.getProductId().toString());
    assertThat(retrievedCampaign.getCreatedTime()).isGreaterThanOrEqualTo(createTimestamp);
    assertThat(retrievedCampaign.getLastModifiedTime()).isGreaterThanOrEqualTo(createTimestamp);

    // Verify lists
    assertThat(retrievedCampaign.getAccountListUrns().size()).isEqualTo(campaign.getAccountListUrns().size());
    assertThat(retrievedCampaign.getAccountListUrns().get(0).toString()).isEqualTo(
        campaign.getAccountListUrns().get(0).toString());
    assertThat(retrievedCampaign.getAccountUrns().size()).isEqualTo(campaign.getAccountUrns().size());
    assertThat(retrievedCampaign.getAccountUrns().get(0).toString()).isEqualTo(
        campaign.getAccountUrns().get(0).toString());

    // Now test partial update
    Campaign updatedCampaign = new Campaign();
    updatedCampaign.setProductId("updatedProduct456");

    // Update account list URNs
    List<CharSequence> updatedAccountListUrns = new ArrayList<>();
    updatedAccountListUrns.add("urn:li:salesList:789");
    updatedAccountListUrns.add("urn:li:salesList:101112");
    updatedCampaign.setAccountListUrns(updatedAccountListUrns);

    // Remove accountUrns
    updatedCampaign.setAccountUrns(new ArrayList<>());

    long updateTimestamp = System.currentTimeMillis();
    Campaign writtenCampaign =
        await(_lssAutoProspectingDB.partialUpdateCampaign(SEAT_URN, campaignId, updatedCampaign));
    Campaign actualWrittenCampaign = await(_lssAutoProspectingDB.getCampaign(SEAT_URN, campaignId));
    assertThat(writtenCampaign).isEqualTo(actualWrittenCampaign);

    // Verify campaign was updated correctly
    retrievedCampaign = await(_lssAutoProspectingDB.getCampaign(SEAT_URN, campaignId));

    // Verify updated fields
    assertThat(retrievedCampaign.getProductId().toString()).isEqualTo(updatedCampaign.getProductId().toString());
    assertThat(retrievedCampaign.getLastModifiedTime()).isGreaterThanOrEqualTo(updateTimestamp);

    // Verify lists were updated
    assertThat(retrievedCampaign.getAccountListUrns().size()).isEqualTo(updatedCampaign.getAccountListUrns().size());
    assertThat(retrievedCampaign.getAccountListUrns().get(0).toString()).isEqualTo(
        updatedCampaign.getAccountListUrns().get(0).toString());
    assertThat(retrievedCampaign.getAccountListUrns().get(1).toString()).isEqualTo(
        updatedCampaign.getAccountListUrns().get(1).toString());

    // Verify accountUrns were removed
    assertThat(retrievedCampaign.getAccountUrns()).isEmpty();

    // Verify original fields remain unchanged
    assertThat(retrievedCampaign.getContractUrn().toString()).isEqualTo(campaign.getContractUrn().toString());
    assertThat(retrievedCampaign.getCreatedTime()).isGreaterThanOrEqualTo(createTimestamp);

    // Test update with non-existent campaign ID
    RestLiServiceException restLiServiceException =
        awaitException(_lssAutoProspectingDB.partialUpdateCampaign(SEAT_URN, 999999L, updatedCampaign),
            RestLiServiceException.class);
    assertThat(restLiServiceException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testFindCampaignsBySeat() {
    // Setup: Create two campaigns for the same seat
    Campaign campaign1 = new Campaign();
    campaign1.setContractUrn(CONTRACT_URN.toString());
    campaign1.setProductId("testProduct123");
    List<CharSequence> accountListUrns1 = new ArrayList<>();
    accountListUrns1.add("urn:li:salesList:789");
    campaign1.setAccountListUrns(accountListUrns1);
    campaign1.setAccountUrns(new ArrayList<>());

    Campaign campaign2 = new Campaign();
    campaign2.setContractUrn(CONTRACT_URN.toString());
    campaign2.setProductId("testProduct456");
    List<CharSequence> accountListUrns2 = new ArrayList<>();
    accountListUrns2.add("urn:li:salesList:101112");
    campaign2.setAccountListUrns(accountListUrns2);
    campaign2.setAccountUrns(new ArrayList<>());

    // Create both campaigns in the database
    Long campaignId1 = await(_lssAutoProspectingDB.createCampaign(SEAT_URN, campaign1));
    Long campaignId2 = await(_lssAutoProspectingDB.createCampaign(SEAT_URN, campaign2));

    assertThat(campaignId1).isNotNull();
    assertThat(campaignId2).isNotNull();

    // Test 1: Find campaigns for the seat that has campaigns
    List<Pair<Long, Campaign>> campaigns = await(_lssAutoProspectingDB.findCampaignsBySeat(SEAT_URN));

    // Verify that both campaigns are returned
    assertThat(campaigns).isNotNull();
    assertThat(campaigns.size()).isEqualTo(2);

    // Verify the campaign IDs match what we created
    List<Long> campaignIds = new ArrayList<>();
    for (Pair<Long, Campaign> pair : campaigns) {
      campaignIds.add(pair.getFirst());
    }
    assertThat(campaignIds).contains(campaignId1, campaignId2);

    // Verify campaign details
    Map<Long, Campaign> campaignMap = new HashMap<>();
    for (Pair<Long, Campaign> pair : campaigns) {
      campaignMap.put(pair.getFirst(), pair.getSecond());
    }

    Campaign retrievedCampaign1 = campaignMap.get(campaignId1);
    assertThat(retrievedCampaign1.getProductId().toString()).isEqualTo(campaign1.getProductId().toString());
    assertThat(retrievedCampaign1.getContractUrn().toString()).isEqualTo(campaign1.getContractUrn().toString());

    Campaign retrievedCampaign2 = campaignMap.get(campaignId2);
    assertThat(retrievedCampaign2.getProductId().toString()).isEqualTo(campaign2.getProductId().toString());
    assertThat(retrievedCampaign2.getContractUrn().toString()).isEqualTo(campaign2.getContractUrn().toString());

    // Test 2: Find campaigns for a seat that does not have any campaigns
    SeatUrn nonExistingSeatUrn = new SeatUrn(999L);
    List<Pair<Long, Campaign>> emptyCampaigns = await(_lssAutoProspectingDB.findCampaignsBySeat(nonExistingSeatUrn));

    // Verify an empty list is returned
    assertThat(emptyCampaigns).isNotNull();
    assertThat(emptyCampaigns).isEmpty();
  }

  @Test(description = "Test create and update the lead finding run")
  public void testCreateAndUpdateLeadFindingRun() {
    // create a record
    LeadFindingRun leadFindingRun = new LeadFindingRun();
    leadFindingRun.setContractUrn(CONTRACT_URN.toString());
    leadFindingRun.setType("ON_DEMAND");
    leadFindingRun.setStatus("NOT_STARTED");
    Long runId = await(_lssAutoProspectingDB.createLeadFindingRun(SEAT_URN, CAMPAIGN_ID_1, leadFindingRun));

    assertTrue(runId > 0);

    // get this record
    LeadFindingRun record = await(_lssAutoProspectingDB.getLeadFindingRun(SEAT_URN, CAMPAIGN_ID_1, runId));
    assertEquals(record.getContractUrn().toString(), CONTRACT_URN.toString());
    assertEquals(record.getType().toString(), "ON_DEMAND");
    assertEquals(record.getStatus().toString(), "NOT_STARTED");
    assertTrue(record.getCreatedTime() > 0);
    assertTrue(record.getLastModifiedTime() > 0);

    // update the record
    LeadFindingRun toUpdate = new LeadFindingRun();
    toUpdate.setStatus("FAILED");
    LeadFindingRunError error = new LeadFindingRunError();
    error.setCode("429");
    error.setMessage("Rate limit exceeded");
    toUpdate.setError(error);
    Long time = System.currentTimeMillis();
    toUpdate.setCompletionTime(time);
    LeadFindingRun updatedRecord = await(_lssAutoProspectingDB.partialUpdateLeadFindingRun(SEAT_URN, CAMPAIGN_ID_1, runId, toUpdate));
    assertEquals(updatedRecord.getContractUrn().toString(), CONTRACT_URN.toString());
    assertEquals(updatedRecord.getType().toString(), "ON_DEMAND");
    assertEquals(updatedRecord.getStatus().toString(), "FAILED");
    assertEquals(updatedRecord.getError().getCode().toString(), "429");
    assertEquals(updatedRecord.getError().getMessage().toString(), "Rate limit exceeded");
    assertEquals(updatedRecord.getCompletionTime(), time);
  }

  @Test (description = "Test find the lead finding run records")
  public void testFindLeadFindingRun() {
    // create records
    LeadFindingRun record1 = new LeadFindingRun();
    record1.setContractUrn(CONTRACT_URN.toString());
    record1.setType("ON_DEMAND");
    record1.setStatus("NOT_STARTED");
    Long runId1 = await(_lssAutoProspectingDB.createLeadFindingRun(SEAT_URN, CAMPAIGN_ID_1, record1));
    assertTrue(runId1 > 0);

    LeadFindingRun record2 = new LeadFindingRun();
    record2.setContractUrn(CONTRACT_URN.toString());
    record2.setType("SCHEDULED");
    record2.setStatus("NOT_STARTED");
    Long runId2 = await(_lssAutoProspectingDB.createLeadFindingRun(SEAT_URN, CAMPAIGN_ID_1, record2));
    assertTrue(runId2 > 0);

    LeadFindingRun record3 = new LeadFindingRun();
    record3.setContractUrn(CONTRACT_URN.toString());
    record3.setType("SCHEDULED");
    record3.setStatus("NOT_STARTED");
    Long runId3 = await(_lssAutoProspectingDB.createLeadFindingRun(SEAT_URN, CAMPAIGN_ID_2, record3));
    assertTrue(runId3 > 0);

    // 1. find by seat only
    List<Pair<LeadFindingRunKey, LeadFindingRun>> results = await(_lssAutoProspectingDB.findLeadFindingRunByQuery(
        SEAT_URN, null, null, null, 0, 10));
    assertEquals(3, results.size());
    // verify the records are sorted by the created time in descending order
    assertEquals(results.get(0).getFirst().getRunId(), runId3);
    assertEquals(results.get(1).getFirst().getRunId(), runId2);
    assertEquals(results.get(2).getFirst().getRunId(), runId1);

    // 2. find by seat and campaign
    results = await(_lssAutoProspectingDB.findLeadFindingRunByQuery(SEAT_URN, CAMPAIGN_ID_1, null, null, 0, 10));
    assertEquals(2, results.size());
    assertEquals(results.get(0).getFirst().getRunId(), runId2);
    assertEquals(results.get(1).getFirst().getRunId(), runId1);

  }

  @Test (description = "Tests error scenarios for LeadFindingRunLead, including duplicate creation and updating non-existent records")
  public void testLeadFindingRunLeadErrors() {
    // Step 1: Clean up existing data
    cleanAllData();

    // Step 2: Create a new LeadFindingRunLead
    LeadFindingRunLead leadFindingRunLead = createTestLeadFindingRunLead();
    HttpStatus createStatus = await(_lssAutoProspectingDB.createLeadFindingRunLead(runId, memberUrn, leadFindingRunLead));
    assertThat(createStatus).isEqualTo(HttpStatus.S_201_CREATED);

    // Step 3: Retrieve the created record
    Optional<LeadFindingRunLead> retrievedLead = await(_lssAutoProspectingDB.getLeadByLeadFindingRunIdAndMemberUrn(runId, memberUrn));
    assertTrue(retrievedLead.isPresent());
    assertThat(retrievedLead.get().getRationale().toString()).isEqualTo(leadFindingRunLead.getRationale());
    assertThat(retrievedLead.get().getVariant().toString()).isEqualTo(leadFindingRunLead.getVariant());
    assertThat(retrievedLead.get().getStatus().toString()).isEqualTo(leadFindingRunLead.getStatus());

    // Step 4: Attempt to create a duplicate record (should fail)
    RestLiServiceException duplicateException = awaitException(
        _lssAutoProspectingDB.createLeadFindingRunLead(runId, memberUrn, leadFindingRunLead),
        RestLiServiceException.class
    );
    assertThat(duplicateException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);

    // Step 7: Attempt to update a non-existent record (should fail)
    MemberUrn nonExistentMemberUrn = new MemberUrn(9999L);
    RestLiServiceException updateException = awaitException(
        _lssAutoProspectingDB.partialUpdateLeadFindingRunLead(runId, nonExistentMemberUrn, leadFindingRunLead),
        RestLiServiceException.class
    );
    assertThat(updateException.getStatus()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test (description = "Tests the successful partial update of a LeadFindingRunLead record")
  public void testPartialUpdateLeadFindingRunLead_Success() {
    // Step 1: Create the record
    LeadFindingRunLead leadFindingRunLead = createTestLeadFindingRunLead();
    HttpStatus createStatus = await(_lssAutoProspectingDB.createLeadFindingRunLead(runId, memberUrn, leadFindingRunLead));
    assertEquals(createStatus, HttpStatus.S_201_CREATED);

    // Step 2: Update the record
    leadFindingRunLead.setRationale("Updated Rationale");
    Task<LeadFindingRunLead> resultTask = _lssAutoProspectingDB.partialUpdateLeadFindingRunLead(runId, memberUrn, leadFindingRunLead);
    LeadFindingRunLead updatedLead = await(resultTask);

    assertNotNull(updatedLead);
    assertEquals("Updated Rationale", updatedLead.getRationale().toString());

  }

  @Test (description = "Tests the successful retrieval of multiple LeadFindingRunLead records by runId and pagination")
  public void testCreateMultipleAndFindLeadFindingRunLeadByParams_Success() {
    deleteLeadFindingRunLeads(runId);
    int start = 0;
    int count = 10;

    // Step 1: Create multiple records with the same runId but unique memberUrns
    List<LeadFindingRunLead> leads = Arrays.asList(
        createTestLeadFindingRunLead(),
        createTestLeadFindingRunLead(),
        createTestLeadFindingRunLead()
    );

    List<Long> memberIds = Arrays.asList(1000L, 1001L, 1002L);

    for (int i = 0; i < leads.size(); i++) {
      MemberUrn memberUrn = new MemberUrn(memberIds.get(i)); // Increment memberId for each lead
      HttpStatus createStatus = await(_lssAutoProspectingDB.createLeadFindingRunLead(runId, memberUrn, leads.get(i)));
      assertEquals(createStatus, HttpStatus.S_201_CREATED);
    }

    // Step 2: Fetch all records
    Task<List<Pair<LeadFindingRunLeadKey, LeadFindingRunLead>>> resultTask =
        _lssAutoProspectingDB.findLeadFindingRunLeadByParams(runId, start, count);
    List<Pair<LeadFindingRunLeadKey, LeadFindingRunLead>> result = await(resultTask);

    // Step 3: Verify
    assertNotNull(result);
    assertEquals(result.size(), leads.size());
    assertEquals(result.get(0).getFirst().getMemberUrn().getMemberIdEntity(), memberIds.get(0));
    assertEquals(result.get(1).getFirst().getMemberUrn().getMemberIdEntity(), memberIds.get(1));
    assertEquals(result.get(2).getFirst().getMemberUrn().getMemberIdEntity(), memberIds.get(2));
  }

  private LeadFindingRunLead createTestLeadFindingRunLead() {
    LeadFindingRunLead leadFindingRunLead = new LeadFindingRunLead();
    leadFindingRunLead.setRationale("Test Rationale");
    leadFindingRunLead.setVariant("Test Variant");
    leadFindingRunLead.setStatus("ACCEPTED_MANUALLY");
    leadFindingRunLead.setContractUrn(CONTRACT_URN.toString());
    leadFindingRunLead.setRelevantPositionIds(Arrays.asList(123L, 456L));
    leadFindingRunLead.setKeyStrengths(Arrays.asList("KeyStrength1", "KeyStrength2"));
    leadFindingRunLead.setCreatedTime(System.currentTimeMillis());
    leadFindingRunLead.setModifiedTime(System.currentTimeMillis());
    leadFindingRunLead.setScore(5);
    return leadFindingRunLead;
  }
  private void deleteLeadFindingRunLeads(long runId) {
    List<MemberUrn> memberUrns = Arrays.asList(
        new MemberUrn(1000L),
        new MemberUrn(1001L),
        new MemberUrn(1002L)
    );
    for (MemberUrn memberUrn : memberUrns) {
      await(_parSeqEspressoClient.execute(DeleteRequest.builder(
          LssAutoProspectingDB.DB_LSS_AUTO_PROSPECTING, LssAutoProspectingDB.TABLE_LEAD_FINDING_RUN_LEAD,
          String.valueOf(runId), memberUrn.toString()).build()));
    }
  }
}