package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.NotificationV2Urn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.Bookmark;
import com.linkedin.sales.espresso.BookmarkType;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.List;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


/**
 * Tests for {@link LssBookmarkDB}
 */
public class TestLssBookmarkDB extends IntegrationTestBase {
  private static final long BOOKMARK_ID_1 = 1L;
  private static final long BOOKMARK_ID_2 = 2L;
  private static final long BOOKMARK_ID_3 = 3L;
  private static final String MEMBER_CONTRACT_URN_STRING =
      "urn:li:memberContract:(urn:li:member:123,urn:li:contract:456)";
  private static final String CONTRACT_URN_STRING = "urn:li:contract:234";
  private static Urn CONTENT_URN;
  private static final SeatUrn SEAT_URN = new SeatUrn(1231L);
  private static final BookmarkType BOOKMARK_TYPE = BookmarkType.ALERT;

  static {
    try {
      CONTENT_URN = NotificationV2Urn.createFromUrn(Urn.createFromTuple(NotificationV2Urn.ENTITY_TYPE,
          MEMBER_CONTRACT_URN_STRING,
          "SALES_INSIGHTS_EXPORT_LIST", MEMBER_CONTRACT_URN_STRING));
    } catch (URISyntaxException e) {
      e.printStackTrace();
    }
  }

  private LssBookmarkDB _lssBookmarkDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssBookmarkDB = new LssBookmarkDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBookmarkDB.DB_LSS_BOOKMARK, LssBookmarkDB.TABLE_BOOKMARK, String.valueOf(BOOKMARK_ID_1)).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBookmarkDB.DB_LSS_BOOKMARK, LssBookmarkDB.TABLE_BOOKMARK, String.valueOf(BOOKMARK_ID_2)).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBookmarkDB.DB_LSS_BOOKMARK, LssBookmarkDB.TABLE_BOOKMARK, String.valueOf(BOOKMARK_ID_3)).build()));

  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testGetAndDeleteBookmark() {
    long id = createBookmark(BOOKMARK_ID_3);
    Bookmark bookmark = await(_lssBookmarkDB.getBookmark(id));
    assertThat(bookmark.contentUrn.toString()).isEqualTo(CONTENT_URN.toString());
    HttpStatus status = await(_lssBookmarkDB.deleteBookmark(id));
    assertThat(status).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testGetBySeatAndType() throws InterruptedException {
    createBookmark(BOOKMARK_ID_1);
    // Give time for materialized view table replicate data
    Thread.sleep(500L);
    List<Pair<Long, Bookmark>> result = await(_lssBookmarkDB.getBySeatAndType(SEAT_URN, BOOKMARK_TYPE, 0, 10));
    assertThat(result.size()).isGreaterThan(0);
  }

  @Test
  public void testGetBySeatAndTypeAndContent() throws InterruptedException {
    createBookmark(BOOKMARK_ID_2);
    // Give time for materialized view table replicate data
    Thread.sleep(500L);
    List<Pair<Long, Bookmark>> result =
        await(_lssBookmarkDB.getBySeatAndTypeAndContent(SEAT_URN, BOOKMARK_TYPE, CONTENT_URN.toString()));
    assertThat(result.size()).isGreaterThan(0);
  }

  private long createBookmark(long id) {
    long currentTime = System.currentTimeMillis();
    Bookmark espressoBookmark = new Bookmark();
    espressoBookmark.contentUrn = CONTENT_URN.toString();
    espressoBookmark.contractUrn = CONTRACT_URN_STRING;
    espressoBookmark.createdTime = currentTime;
    espressoBookmark.seatUrn = SEAT_URN.toString();
    espressoBookmark.type = BOOKMARK_TYPE;

    return await(_lssBookmarkDB.createBookmark(espressoBookmark, null, id));
  }
}
