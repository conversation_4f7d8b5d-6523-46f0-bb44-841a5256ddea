package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.StandardizedProductCategoryUrn;
import com.linkedin.common.urn.StandardizedProductUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.ProductCategoryInterest;
import com.linkedin.sales.espresso.SeatSellerIdentity;
import com.linkedin.sales.espresso.SellerIdentityPosition;
import com.linkedin.sales.espresso.SellerIdentityProduct;
import com.linkedin.sales.espresso.SellerIdentityService;
import com.linkedin.sales.espresso.TargetType;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;

/**
 * Created by guanwang at 10/2022
 * Test class for {@link LssBuyerDB}
 */
public class TestLssBuyerDB extends IntegrationTestBase {
  private LssBuyerDB _lssBuyerDB;

  private static final ProductCategoryInterest interest1 = new ProductCategoryInterest();
  private static final ProductCategoryInterest interest2 = new ProductCategoryInterest();
  private static final SeatSellerIdentity identity1 = new SeatSellerIdentity();
  private static final SeatSellerIdentity identity2 = new SeatSellerIdentity();
  private static final SeatSellerIdentity identity3 = new SeatSellerIdentity();
  private static final SellerIdentityPosition position1 = new SellerIdentityPosition();
  private static final SellerIdentityPosition position2 = new SellerIdentityPosition();
  private static final SellerIdentityProduct product1 = new SellerIdentityProduct();
  private static final SellerIdentityProduct product2 = new SellerIdentityProduct();
  private static final SellerIdentityService service = new SellerIdentityService();
  private static final String SEAT_URN_STR = "urn:li:seat:101";
  private static final String SEAT_URN_STR_1 = "urn:li:seat:102";

  private static final String PRODUCT_ID_1 = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6b8";
  private static final String PRODUCT_ID_2 = "1d350179-bfa0-4861-8c7e-9f8ac2e7a6b8";
  private static final String PRODUCT_NAME_1 = "product_1";
  private static final String PRODUCT_NAME_2 = "product_2";
  private static final String INVALID_PRODUCT_URN_STR = "urn:li:standardizedProduct:-1";
  private static final String PRODUCT_URN_10_STR = "urn:li:standardizedProduct:10";
  private static final String PRODUCT_URL = "product_url";
  private static final String CATEGORY_NAME_1 = "category_1";
  private static final String CATEGORY_NAME_2 = "category_2";
  private static final String INVALID_CATEGORY_URN_STR = "urn:li:standardizedProductCategory:-1";
  private static final String CATEGORY_URN_1_STR = "urn:li:standardizedProductCategory:1";
  private static final String CONTRACT_URN_STR = "urn:li:contract:9999";
  private static final Long POSITION_ID = 333L;
  private static final String TITLE = "CEO";
  private static final String COMPANY_NAME = "The company";
  private static final String ORG_URN_STR = "urn:li:organization:123";
  private static final String SERVICE_NAME = "The service";
  private static final String SERVICE_URL = "service_url";
  private static final SeatUrn SEAT_URN;
  private static final SeatUrn SEAT_URN_1;
  private static final ContractUrn CONTRACT_URN;
  private static final StandardizedProductUrn INVALID_PRODUCT_URN;
  private static final StandardizedProductUrn PRODUCT_URN_10;
  private static final StandardizedProductCategoryUrn INVALID_CATEGORY_URN;
  private static final StandardizedProductCategoryUrn CATEGORY_URN_1;
  static {
    try {
      SEAT_URN = SeatUrn.deserialize(SEAT_URN_STR);
      SEAT_URN_1 = SeatUrn.deserialize(SEAT_URN_STR_1);
      INVALID_PRODUCT_URN = StandardizedProductUrn.deserialize(INVALID_PRODUCT_URN_STR);
      PRODUCT_URN_10 = StandardizedProductUrn.deserialize(PRODUCT_URN_10_STR);
      INVALID_CATEGORY_URN = StandardizedProductCategoryUrn.deserialize(INVALID_CATEGORY_URN_STR);
      CATEGORY_URN_1 = StandardizedProductCategoryUrn.deserialize(CATEGORY_URN_1_STR);
      CONTRACT_URN = ContractUrn.deserialize(CONTRACT_URN_STR);
    } catch (URISyntaxException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssBuyerDB = new LssBuyerDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBuyerDB.DB_LSS_BUYER, LssBuyerDB.TABLE_PCI, SEAT_URN_STR).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBuyerDB.DB_LSS_BUYER, LssBuyerDB.TABLE_SELLER_IDENTITY, CONTRACT_URN_STR, SEAT_URN_STR).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssBuyerDB.DB_LSS_BUYER, LssBuyerDB.TABLE_SELLER_IDENTITY, CONTRACT_URN_STR, SEAT_URN_STR_1).build()));
    long currentTime = System.currentTimeMillis();
    // set up an interest record with all valid fields
    interest1.setProductName(PRODUCT_NAME_1);
    interest1.setStandardizedProductUrn(PRODUCT_URN_10_STR);
    interest1.setProductUrl(PRODUCT_URL);
    interest1.setCategoryName(CATEGORY_NAME_1);
    interest1.setStandardizedProductCategoryUrn(CATEGORY_URN_1_STR);
    interest1.setCreatedTime(currentTime);
    interest1.setLastModifiedTime(currentTime);
    // set up an interest record with null field and with invalid string values
    interest2.setProductName(PRODUCT_NAME_2);
    interest2.setStandardizedProductUrn(INVALID_PRODUCT_URN_STR);
    interest2.setCategoryName(CATEGORY_NAME_2);
    interest2.setStandardizedProductCategoryUrn(INVALID_CATEGORY_URN_STR);
    interest2.setCreatedTime(currentTime);
    interest2.setLastModifiedTime(currentTime);

    //seller identity
    position1.setPositionId(POSITION_ID);
    position2.setTitle(TITLE);
    position2.setCompanyName(COMPANY_NAME);
    position2.setOrganizationUrn(ORG_URN_STR);

    product1.setProductName(PRODUCT_NAME_1);
    product1.setProductId(PRODUCT_ID_1);
    product1.setProductUrl(PRODUCT_URL);
    product1.setProductCategoryName(CATEGORY_NAME_1);
    product2.setProductUrn(PRODUCT_URN_10_STR);
    product2.setProductId(PRODUCT_ID_2);

    service.setServiceName(SERVICE_NAME);
    service.setServiceUrl(SERVICE_URL);

    //full record
    identity1.setTargetType(TargetType.ACCOUNT);
    identity1.setCurrentPosition(position1);
    identity1.setProducts(ImmutableList.of(product1, product2));
    identity1.setServices(ImmutableList.of(service));

    //partial record
    identity2.setTargetType(TargetType.LEAD);
    identity2.setCurrentPosition(position2);
    identity2.setProducts(Collections.emptyList());
    identity2.setServices(Collections.emptyList());

    //update record
    identity3.setTargetType(TargetType.ACCOUNT);
    identity3.setCurrentPosition(position1);
    identity3.setProducts(ImmutableList.of(product1));
    identity3.setDefaultProductId(PRODUCT_ID_1);
    identity3.setServices(ImmutableList.of(service));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateAndFindInterests() {
    // create 2 interests
    Long interestId1 = await(_lssBuyerDB.createProductCategoryInterest(SEAT_URN, interest1));
    assertThat(interestId1).isNotNegative();
    Long interestId2 = await(_lssBuyerDB.createProductCategoryInterest(SEAT_URN, interest2));
    assertThat(interestId2).isNotNegative();

    // call finder without category to get all
    List<Pair<Long, ProductCategoryInterest>> results = await(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[0], 0, 100));
    assertThat(results.size()).isEqualTo(2);
    results.sort(Comparator.comparing(Pair::getFirst));
    assertThat(results.get(0).getSecond()).isEqualTo(interest1);
    assertThat(results.get(1).getSecond()).isEqualTo(interest2);
  }

  @Test
  public void testDeleteInterest() {
    // create 2 interests
    Long interestId1 = await(_lssBuyerDB.createProductCategoryInterest(SEAT_URN, interest1));
    Long interestId2 = await(_lssBuyerDB.createProductCategoryInterest(SEAT_URN, interest2));

    // delete interest 2. success
    HttpStatus httpStatus = await(_lssBuyerDB.deleteProductCategoryInterest(SEAT_URN, interestId2));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_204_NO_CONTENT);

    // delete the same interest Id again. return 404
    httpStatus = await(_lssBuyerDB.deleteProductCategoryInterest(SEAT_URN, interestId2));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_404_NOT_FOUND);

    // get the remaining interest
    List<Pair<Long, ProductCategoryInterest>> results = await(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[0], 0, 100));
    assertThat(results.size()).isEqualTo(1);
    assertThat(results.get(0).getSecond()).isEqualTo(interest1);
  }

  @Test
  public void testUpdateAndGetInterest() {
    // create an interest, and update
    Long interestId1 = await(_lssBuyerDB.createProductCategoryInterest(SEAT_URN, interest1));
    HttpStatus httpStatus = await(_lssBuyerDB.updateProductCategoryInterest(SEAT_URN, interestId1, interest2));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);

    // attempt to update an non-exist record
    Long interestId2 = interestId1 + 1;
    httpStatus = await(_lssBuyerDB.updateProductCategoryInterest(SEAT_URN, interestId2, interest2));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);

    // get interest1 directly with Id
    ProductCategoryInterest interest = await(_lssBuyerDB.getProductCategoryInterest(SEAT_URN, interestId1));
    assertThat(interest).isEqualTo(interest2);

    // call finder without category to get both
    List<Pair<Long, ProductCategoryInterest>> results = await(_lssBuyerDB.findProductCategoryInterests(SEAT_URN, new String[0], 0, 100));
    assertThat(results.size()).isEqualTo(2);
    results.sort(Comparator.comparing(Pair::getFirst));
    // the first record's Id must equal to interestId1, but document equal to interest2.
    assertThat(results.get(0).getFirst()).isEqualTo(interestId1);
    assertThat(results.get(0).getSecond()).isEqualTo(interest2);
    // second one's Id is not necessarily equal to interestId2
    assertThat(results.get(1).getSecond()).isEqualTo(interest2);
  }

  @Test
  public void testCreateUpdateAndFindSellerIdentity() {
    //create identity 1
    HttpStatus httpStatus = await(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN, SEAT_URN, identity1));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);
    //create identity 2
    httpStatus = await(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN, SEAT_URN_1, identity2));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);

    //find seller identities
    List<Pair<SeatUrn, SeatSellerIdentity>> identities = await(_lssBuyerDB.findSellerIdentities(CONTRACT_URN, null, 0, 10));
    assertThat(identities.size()).isEqualTo(2);
    identities.sort(Comparator.comparing(pair -> pair.getFirst().getSeatIdEntity()));
    assertThat(identities.get(0).getSecond()).isEqualTo(identity1);
    assertThat(identities.get(1).getSecond()).isEqualTo(identity2);

    //update identity 2 using identity 3
    httpStatus = await(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN, SEAT_URN_1, identity3));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);

    //get identity 2 (using count 2 here to verify get record with contract + seat key will always return 1 record)
    SeatSellerIdentity identity = await(_lssBuyerDB.getSellerIdentity(CONTRACT_URN, SEAT_URN_1));
    assertThat(identity).isEqualTo(identity3);

    //try updating field back to null
    identity3.setCurrentPosition(null);
    httpStatus = await(_lssBuyerDB.updateSeatSellerIdentity(CONTRACT_URN, SEAT_URN_1, identity3));
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
    identity = await(_lssBuyerDB.getSellerIdentity(CONTRACT_URN, SEAT_URN_1));
    assertThat(identity).isEqualTo(identity3);
  }
}
