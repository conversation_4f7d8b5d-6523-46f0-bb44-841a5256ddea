package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.AuthorType;
import com.linkedin.sales.espresso.ChatMessage;
import com.linkedin.sales.espresso.CoachConversationHistory;
import com.linkedin.sales.espresso.Metadata;
import com.linkedin.sales.espresso.UseCaseType;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class TestLssCoachDB extends IntegrationTestBase {

  private LssCoachDB _lssCoachDB;
  private static final String serializedSeatUrn1 = "urn:li:seat:1231";
  private static final SeatUrn seatUrn1 = new SeatUrn(1231L);
  private static final String serializedSeatUrn2 = "urn:li:seat:1232";
  private static final SeatUrn seatUrn2 = new SeatUrn(1232L);
  private static final String sessionId1 = "sessionId1";
  private static final String sessionId2 = "sessionId2";

  private static final String summarizedContent1 = "A member asked to find leads in Bay Area with CEO titles. And SN Coach responded with 400 leads.";

  private static final String summarizedIntent1 = "A member asking to find leads in Bay Area with CEO titles.";
  private static final String summarizedIntent2 = "Updated: A member asking to find leads in Bay Area with CEO titles.";

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssCoachDB = new LssCoachDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssCoachDB.DB_LSS_COACH, LssCoachDB.TABLE_COACH_CONVERSATION_HISTORY, serializedSeatUrn1).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssCoachDB.DB_LSS_COACH, LssCoachDB.TABLE_COACH_CONVERSATION_HISTORY, serializedSeatUrn2).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateUpdateGetChatHistory() {
    // Create a new chat history
    CoachConversationHistory coachConversationHistory = constructDefaultCoachConversationHistory();
    Boolean isCreationSuccessful = await(_lssCoachDB.upsertCoachConversationHistory(seatUrn1, sessionId1, coachConversationHistory));
    Assert.assertTrue(isCreationSuccessful);

    // Get the newly created chat history
    Optional<CoachConversationHistory> optionalCoachConversationHistory = await(_lssCoachDB.getCoachConversationHistory(seatUrn1, sessionId1));
    Assert.assertTrue(optionalCoachConversationHistory.isPresent());

    // Verify the newly created chat history
    CoachConversationHistory oldCoachConversationHistory = optionalCoachConversationHistory.get();
    Assert.assertEquals(summarizedIntent1, oldCoachConversationHistory.metadata.summarizedIntent.toString());
    Assert.assertEquals(summarizedContent1, oldCoachConversationHistory.metadata.summarizedContent.toString());
    Assert.assertEquals(UseCaseType.LEAD_SEARCH.toString(), oldCoachConversationHistory.metadata.useCase.toString());
    Assert.assertEquals(5, oldCoachConversationHistory.chatContent.size());

    // Update the chat history
    oldCoachConversationHistory.metadata.summarizedIntent = summarizedIntent2;
    oldCoachConversationHistory.chatContent.add(new ChatMessage("That's awesome you got what you were looking for!", AuthorType.SYSTEM, 6L, null, null));

    // Push the updated chat history
    Boolean isUpdateSuccessful = await(_lssCoachDB.upsertCoachConversationHistory(seatUrn1, sessionId1, oldCoachConversationHistory));
    Assert.assertTrue(isUpdateSuccessful);

    // Get the updated chat history
    Optional<CoachConversationHistory> updatedCoachConversationHistoryOptional = await(_lssCoachDB.getCoachConversationHistory(seatUrn1, sessionId1));
    Assert.assertTrue(updatedCoachConversationHistoryOptional.isPresent());

    // Verify the updated chat history
    CoachConversationHistory updatedCoachConversationHistory = updatedCoachConversationHistoryOptional.get();
    Assert.assertEquals(updatedCoachConversationHistory.metadata.summarizedIntent.toString(), summarizedIntent2);
    Assert.assertEquals(6, updatedCoachConversationHistory.chatContent.size());
  }

  @Test
  public void testGetChatHistoryNotAvailableSucceeded() {
    Optional<CoachConversationHistory> optionalCoachConversationHistory = await(_lssCoachDB.getCoachConversationHistory(seatUrn2, sessionId2));
    Assert.assertFalse(optionalCoachConversationHistory.isPresent());
  }

  private CoachConversationHistory constructDefaultCoachConversationHistory() {

    Metadata metadata = new Metadata();
    metadata.summarizedContent = summarizedContent1;
    metadata.summarizedIntent = summarizedIntent1;
    metadata.useCase = UseCaseType.LEAD_SEARCH;

    List<ChatMessage> chatMessages = new ArrayList<>();

    chatMessages.add(new ChatMessage("Hi, I need help?", AuthorType.MEMBER, 1L, null, null));
    chatMessages.add(new ChatMessage("Hello! Happy to do that, how can I help?", AuthorType.SYSTEM, 2L, null, null));
    chatMessages.add(new ChatMessage("I need to find CEOs that work in Bay Area?", AuthorType.MEMBER, 3L, null, null));
    chatMessages.add(new ChatMessage("Sure, there are 400 CEOs that I can find in Bay Area.", AuthorType.SYSTEM, 4L, null, null));
    chatMessages.add(new ChatMessage("Cool, that's awesome, thank you!", AuthorType.MEMBER, 5L, null, null));

    CoachConversationHistory coachConversationHistory = new CoachConversationHistory();
    coachConversationHistory.metadata = metadata;
    coachConversationHistory.updatedTime = System.currentTimeMillis();
    coachConversationHistory.chatContent = chatMessages;

    return coachConversationHistory;
  }
}
