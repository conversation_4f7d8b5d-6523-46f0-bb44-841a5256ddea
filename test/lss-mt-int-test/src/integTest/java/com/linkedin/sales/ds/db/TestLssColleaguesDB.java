package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.ColleagueRelationship;
import com.linkedin.sales.espresso.ColleagueRelationshipHistory;
import com.linkedin.sales.espresso.State;
import com.linkedin.salescolleagues.RelationshipType;
import java.util.List;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


/**
 * Created by aewong
 * Test class for {@link LssColleaguesDB}
 */
public class TestLssColleaguesDB extends IntegrationTestBase {

  private static final long FROM_MEMBER_ID = 1L;
  private static final long FROM_MEMBER_ID_2 = 2L;
  private static final long TO_MEMBER_ID = 11L;
  private static final long TO_MEMBER_ID_2 = 12L;
  private static final long SEAT_ID = 200L;
  private static final long CONTRACT_ID = 100L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);

  private LssColleaguesDB _lssColleaguesDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssColleaguesDB = new LssColleaguesDB(_parSeqEspressoClient);

    _offspringTestHelper.startGenerator();
    // clean up first
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP, String.valueOf(FROM_MEMBER_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP_HISTORY, String.valueOf(FROM_MEMBER_ID)).build()));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP, String.valueOf(FROM_MEMBER_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP_HISTORY, String.valueOf(FROM_MEMBER_ID)).build()));
  }

  @Test
  public void testAddAndGetManagerRelationshipSucceed() {
    addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    List<Pair<Pair<String, Long>, ColleagueRelationship>> list = await(_lssColleaguesDB.getColleagueRelationships(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), 0, 1));
    assertThat(list.size()).isEqualTo(1);
    Pair<Pair<String, Long>, ColleagueRelationship> returnObject = list.get(0);
    assertThat(returnObject.getFirst().getFirst()).isEqualTo(RelationshipType.REPORTS_TO.name());
    assertThat(returnObject.getFirst().getSecond()).isEqualTo(TO_MEMBER_ID);
    assertThat(returnObject.getSecond().state).isEqualTo(State.ADDED);
  }

  @Test
  public void testAddAndRemoveAndGetManagerRelationshipSucceed() {
    addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    removeManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    List<Pair<Pair<String, Long>, ColleagueRelationship>> list = await(_lssColleaguesDB.getColleagueRelationships(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), 0, 1));
    assertThat(list.size()).isEqualTo(1);
    Pair<Pair<String, Long>, ColleagueRelationship> returnObject = list.get(0);
    assertThat(returnObject.getFirst().getFirst()).isEqualTo(RelationshipType.REPORTS_TO.name());
    assertThat(returnObject.getFirst().getSecond()).isEqualTo(TO_MEMBER_ID);
    assertThat(returnObject.getSecond().state).isEqualTo(State.REMOVED);
  }

  @Test
  public void testAddAndRemoveAndGetHistorySucceed() {
    addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    removeManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    addManagerRelationship(FROM_MEMBER_ID_2, CONTRACT_ID, TO_MEMBER_ID); // Shouldn't get this history entry
    List<Pair<Pair<String, Long>, ColleagueRelationshipHistory>> list = await(_lssColleaguesDB.getColleagueRelationshipHistory(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), 0, -1));
    assertThat(list.size()).isEqualTo(2);
    Pair<Pair<String, Long>, ColleagueRelationshipHistory> returnObject1 = list.get(0);
    Pair<Pair<String, Long>, ColleagueRelationshipHistory> returnObject2 = list.get(1);
    assertThat(returnObject1.getFirst().getFirst()).isEqualTo(RelationshipType.REPORTS_TO.name());
    assertThat(returnObject1.getFirst().getSecond()).isEqualTo(TO_MEMBER_ID);
    assertThat(returnObject1.getSecond().state).isEqualTo(State.REMOVED);
    assertThat(returnObject2.getFirst().getFirst()).isEqualTo(RelationshipType.REPORTS_TO.name());
    assertThat(returnObject2.getFirst().getSecond()).isEqualTo(TO_MEMBER_ID);
    assertThat(returnObject2.getSecond().state).isEqualTo(State.ADDED);
    assertThat(returnObject1.getSecond().updatedTime).isGreaterThan(returnObject2.getSecond().updatedTime);
  }

  @Test
  public void testGetNoRelationshipSucceed() {
    List<Pair<Pair<String, Long>, ColleagueRelationship>> list = await(_lssColleaguesDB.getColleagueRelationships(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), 0, 1));
    assertThat(list.size()).isEqualTo(0);
  }

  @Test
  public void testGetNoHistorySucceed() {
    List<Pair<Pair<String, Long>, ColleagueRelationshipHistory>> list = await(_lssColleaguesDB.getColleagueRelationshipHistory(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name(), 0, -1));
    assertThat(list.size()).isEqualTo(0);
  }

  @Test
  public void testAddAndLoadToMemberIdForLatestAdded() {
    addManagerRelationship(FROM_MEMBER_ID, CONTRACT_ID, TO_MEMBER_ID);
    Long returnedToMemberId = await(_lssColleaguesDB.loadToMemberIdForLatestAdded(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()));
    assertThat(returnedToMemberId).isEqualTo(TO_MEMBER_ID);
  }

  @Test
  public void testLoadToMemberIdForLatestAddedEmpty() {
    Long returnedToMemberId = await(_lssColleaguesDB.loadToMemberIdForLatestAdded(
        FROM_MEMBER_ID, CONTRACT_ID, RelationshipType.REPORTS_TO.name()));
    assertThat(returnedToMemberId).isEqualTo(null);
  }

  private Void addManagerRelationship(Long fromMemberId, Long contractId, Long toMemberId) {
    long currentTime = System.currentTimeMillis();
    com.linkedin.sales.espresso.ColleagueRelationship espressoColleagueRelationship =
        new com.linkedin.sales.espresso.ColleagueRelationship();
    espressoColleagueRelationship.state = State.ADDED;
    espressoColleagueRelationship.createdBySeatUrn = SEAT_URN.toString();
    espressoColleagueRelationship.updatedTime = currentTime;
    return await(_lssColleaguesDB.addColleagueRelationshipEntry(fromMemberId, contractId, toMemberId,
        RelationshipType.REPORTS_TO.name(), espressoColleagueRelationship));
  }

  private Void removeManagerRelationship(Long fromMemberId, Long contractId, Long toMemberId) {
    long currentTime = System.currentTimeMillis();
    com.linkedin.sales.espresso.ColleagueRelationship espressoColleagueRelationship =
        new com.linkedin.sales.espresso.ColleagueRelationship();
    espressoColleagueRelationship.state = State.REMOVED;
    espressoColleagueRelationship.createdBySeatUrn = SEAT_URN.toString();
    espressoColleagueRelationship.updatedTime = currentTime;
    return await(_lssColleaguesDB.addColleagueRelationshipEntry(fromMemberId, contractId, toMemberId,
        RelationshipType.REPORTS_TO.name(), espressoColleagueRelationship));
  }

  @AfterClass
  public void tearDown() throws Exception {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP, String.valueOf(FROM_MEMBER_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssColleaguesDB.DB_LSS_COLLEAGUES, LssColleaguesDB.TABLE_COLLEAGUE_RELATIONSHIP_HISTORY, String.valueOf(FROM_MEMBER_ID)).build()));

    super.tearDown();
  }
}
