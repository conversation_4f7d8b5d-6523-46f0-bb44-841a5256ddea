package com.linkedin.sales.ds.db;

import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.FilterLayout;
import com.linkedin.sales.espresso.FilterLayoutConfig;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import com.linkedin.sales.espresso.PinnedFilters;

import static org.assertj.core.api.Assertions.*;


public class TestLssCustomFilterViewDB extends IntegrationTestBase {

  private LssCustomFilterViewDB _lssCustomFilterViewDB;
  private static final String serializedSeatUrn = "urn:li:seat:123";
  private static final String contractUrn = "urn:li:contract:234";
  private static final String searchType = "LEAD";
  private static final String searchViewType = "COLLAPSED_LEAD";

  private static final String pinnedFilter = "SampleFilter";
  private static final String filterEntity = "Filter1";
  private static final String filterGroupEntity = "FilterGroup1";

  @BeforeClass
  public void beforeClass() throws Exception {
    super.setUp();
    _lssCustomFilterViewDB = new LssCustomFilterViewDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
    reset();
  }

  @BeforeMethod
  public void beforeMethod() {
    reset();
  }

  @AfterClass
  public void afterClass() throws Exception {
    reset();
    super.tearDown();
  }

  @Test(description = "Test successful creation of pinnedFilters record")
  public void createPinnedFiltersSucceeded() {
    assertThat(createPinnedFilters()).isTrue();
  }

  @Test(description = "Test successful creation of filterLayout record")
  public void createFilterLayoutSucceeded() {
    assertThat(createFilterLayoutRecord()).isTrue();
  }

  @Test(description = "Test successful get of pinnedFilters record")
  public void getPinnedFiltersSucceeded() {
    createPinnedFilters();
    PinnedFilters pinnedFilters = await(_lssCustomFilterViewDB.getPinnedFilters(serializedSeatUrn, searchType));
    List<CharSequence> expectedPinnedFilter = new ArrayList<>();
    expectedPinnedFilter.add(pinnedFilter);
    assertThat(pinnedFilters.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(pinnedFilters.getPinnedFilters().get(0)).isEqualToIgnoringCase(expectedPinnedFilter.get(0));
    assertThat(pinnedFilters.getPinnedFilters().size()).isEqualTo(expectedPinnedFilter.size());
  }

  @Test(description = "Test successful get of filterLayout record")
  public void getFilterLayoutSucceeded() {
    createFilterLayoutRecord();
    FilterLayout filterLayout = await(_lssCustomFilterViewDB.getFilterLayout(serializedSeatUrn, searchViewType));
    assertThat(filterLayout.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(filterLayout.getFilterOrder().toString()).isEqualToIgnoringCase(createFilterLayout().getFilterOrder().toString());
  }

  @Test(description = "Test successful update of pinnedFilters record")
  public void updatePinnedFilterSucceeded() {
    createPinnedFilters();
    PinnedFilters pinnedFilters = new PinnedFilters();
    List<CharSequence> pinnedFiltersList = new ArrayList<>();
    pinnedFiltersList.add("NewPinnedFilter");
    pinnedFilters.setPinnedFilters(pinnedFiltersList);
    pinnedFilters.setContractUrn(contractUrn);
    List<CharSequence> expectedPinnedFilter = new ArrayList<>();
    expectedPinnedFilter.add(pinnedFilter);
    PinnedFilters oldPinnedFilters = await(_lssCustomFilterViewDB.getPinnedFilters(serializedSeatUrn, searchType));
    assertThat(oldPinnedFilters.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(oldPinnedFilters.getPinnedFilters().get(0)).isEqualToIgnoringCase(expectedPinnedFilter.get(0));
    assertThat(oldPinnedFilters.getPinnedFilters().size()).isEqualTo(expectedPinnedFilter.size());
    await(_lssCustomFilterViewDB.upsertPinnedFilters(serializedSeatUrn, searchType, pinnedFilters));
    PinnedFilters newPinnedFilters = await(_lssCustomFilterViewDB.getPinnedFilters(serializedSeatUrn, searchType));
    assertThat(newPinnedFilters.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(newPinnedFilters.getPinnedFilters().get(0)).isEqualToIgnoringCase(pinnedFiltersList.get(0));
    assertThat(newPinnedFilters.getPinnedFilters().size()).isEqualTo(pinnedFiltersList.size());
  }

  @Test(description = "Test successful update of filterLayout record")
  public void updateFilterLayoutSucceeded() {
    createFilterLayoutRecord();
    FilterLayout filterLayout = new FilterLayout();
    filterLayout.setContractUrn(contractUrn);
    FilterLayoutConfig filterLayoutConfig = new FilterLayoutConfig();
    List<CharSequence> entityNames = new ArrayList<>();
    entityNames.add("newFilterName");
    filterLayoutConfig.setEntityColumn(1);
    filterLayoutConfig.setEntityNames(entityNames);
    Map<CharSequence, FilterLayoutConfig> filterOrderMap = new HashMap<>();
    filterOrderMap.put("newFilterGroup", filterLayoutConfig);
    filterLayout.setFilterOrder(filterOrderMap);
    FilterLayout oldFilterLayout = await(_lssCustomFilterViewDB.getFilterLayout(serializedSeatUrn, searchViewType));
    assertThat(oldFilterLayout.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(oldFilterLayout.getFilterOrder().toString()).isEqualToIgnoringCase(createFilterLayout().getFilterOrder().toString());
    await(_lssCustomFilterViewDB.upsertFilterLayout(serializedSeatUrn, searchViewType, filterLayout));
    FilterLayout newFilterLayout = await(_lssCustomFilterViewDB.getFilterLayout(serializedSeatUrn, searchViewType));
    assertThat(newFilterLayout.getContractUrn()).isEqualToIgnoringCase(contractUrn);
    assertThat(newFilterLayout.getFilterOrder().toString()).isEqualToIgnoringCase(filterLayout.getFilterOrder().toString());
  }

  @Test(description = "Test successful deletion of pinnedFilters record")
  public void deletePinnedFiltersSucceeded() {
    createPinnedFilters();
    assertThat(await(_lssCustomFilterViewDB.deletePinnedFilters(serializedSeatUrn, searchType))).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test(description = "Test successful deletion of filterLayout record")
  public void deleteFilterLayoutSucceeded() {
    createFilterLayoutRecord();
    assertThat(await(_lssCustomFilterViewDB.deleteFilterLayout(serializedSeatUrn, searchViewType))).isEqualTo(HttpStatus.S_200_OK);
  }

  /*** HELPER FUNCTIONS ***/
  private boolean createPinnedFilters() {
    List<CharSequence> expectedPinnedFilter = new ArrayList<>();
    expectedPinnedFilter.add(pinnedFilter);
    PinnedFilters pinnedFilters = new PinnedFilters();
    pinnedFilters.setPinnedFilters(expectedPinnedFilter);
    pinnedFilters.setContractUrn(contractUrn);
    return await(_lssCustomFilterViewDB.upsertPinnedFilters(serializedSeatUrn, searchType, pinnedFilters));
  }

  private FilterLayout createFilterLayout() {
    FilterLayout filterLayout = new FilterLayout();
    filterLayout.setContractUrn(contractUrn);
    List<CharSequence> newEntityNames = new ArrayList<>();
    newEntityNames.add(filterEntity);
    FilterLayoutConfig filterLayoutConfig = new FilterLayoutConfig();
    filterLayoutConfig.setEntityColumn(0);
    filterLayoutConfig.setEntityNames(newEntityNames);
    Map<CharSequence, FilterLayoutConfig> filterOrderMap = new HashMap<>();
    filterOrderMap.put(filterGroupEntity, filterLayoutConfig);
    filterLayout.setFilterOrder(filterOrderMap);
    filterLayout.setFilterOrder(filterOrderMap);
    return filterLayout;
  }

  private boolean createFilterLayoutRecord() {
    return await(_lssCustomFilterViewDB.upsertFilterLayout(serializedSeatUrn, searchViewType, createFilterLayout()));
  }

  private void reset() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssCustomFilterViewDB.DB_CUSTOM_FILTER_VIEW,
        LssCustomFilterViewDB.TABLE_FILTER_LAYOUT, serializedSeatUrn, searchViewType).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(LssCustomFilterViewDB.DB_CUSTOM_FILTER_VIEW,
        LssCustomFilterViewDB.TABLE_PINNED_FILTERS, serializedSeatUrn, searchType).build()));
  }
}
