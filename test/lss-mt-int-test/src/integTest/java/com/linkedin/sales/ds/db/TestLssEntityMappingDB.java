package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.ManualEmailToMemberMapping;
import com.linkedin.util.Pair;
import java.util.List;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class TestLssEntityMappingDB extends IntegrationTestBase {

  private LssEntityMappingDB _lssEntityMappingDB;

  private static final String samepleHashedEmail = "sampleEmail";
  private static final String sampleHashedEmail2 = "sampleEmail2";
  private static final String sampleHashedEmail3 = "sampleEmail3";
  private static final ContractUrn contractUrn = new ContractUrn(123L);

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssEntityMappingDB = new LssEntityMappingDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssEntityMappingDB.DB_NAME, LssEntityMappingDB.TABLE_NAME, samepleHashedEmail, contractUrn.toString()).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssEntityMappingDB.DB_NAME, LssEntityMappingDB.TABLE_NAME, sampleHashedEmail2, contractUrn.toString()).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testUpsert() {
    HttpStatus result = createMapping(samepleHashedEmail);
    Assert.assertEquals(result, HttpStatus.S_201_CREATED);
  }

  @Test
  public void testDelete() {
    createMapping(samepleHashedEmail);
    HttpStatus result = await(_lssEntityMappingDB.deleteManualEmailMapping(samepleHashedEmail, contractUrn));
    Assert.assertEquals(result, HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteFailure() {
    // when record is not created, delete should fail
    HttpStatus result = await(_lssEntityMappingDB.deleteManualEmailMapping(samepleHashedEmail, contractUrn));
    Assert.assertEquals(result, HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testBatchGet() {
    createMapping(samepleHashedEmail);
    createMapping(sampleHashedEmail2);
    List<Pair<String, ContractUrn>> keys = ImmutableList.of(new Pair<>(samepleHashedEmail, contractUrn),
        new Pair<>(sampleHashedEmail2, contractUrn), new Pair<>(sampleHashedEmail3, contractUrn));
    List<Pair<Pair<String, ContractUrn>, ManualEmailToMemberMapping>> result =
        await(_lssEntityMappingDB.batchGetManualEmailMappings(keys));

    // Since sampleHashedEmail3 is not created, it should not be returned
    Assert.assertEquals(result.size(), 2);
    Assert.assertEquals(result.get(0).getFirst().getFirst(), samepleHashedEmail);
    Assert.assertEquals(result.get(1).getFirst().getFirst(), sampleHashedEmail2);
    Assert.assertEquals(result.get(0).getFirst().getSecond(), contractUrn);
    Assert.assertEquals(result.get(1).getFirst().getSecond(), contractUrn);
  }

  private HttpStatus createMapping(String hashedEmail) {
    Task<HttpStatus> result =
        _lssEntityMappingDB.upsertManualEmailToMemberMapping(hashedEmail, contractUrn, buildManualMappingRecord());
    return await(result);
  }

  private ManualEmailToMemberMapping buildManualMappingRecord() {
    long currentTime = System.currentTimeMillis();
    ManualEmailToMemberMapping manualEmailToMemberMapping = new ManualEmailToMemberMapping();
    manualEmailToMemberMapping.memberUrn = "urn:li:member:123";
    manualEmailToMemberMapping.seatUrn = "urn:li:seat:123";
    manualEmailToMemberMapping.createdAt = currentTime;
    manualEmailToMemberMapping.lastModifiedAt = currentTime;

    return manualEmailToMemberMapping;
  }
}
