package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.ProfileView;
import com.linkedin.sales.service.utils.UrnUtils;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


public class TestLssEntityViewDB extends IntegrationTestBase {

  private LssEntityViewDB _lssEntityViewDB;
  private static final String serializedSeatUrn = "urn:li:seat:123";
  private static final SeatUrn seatUrn = new SeatUrn(123L);
  private static final ContractUrn contractUrn = new ContractUrn(123L);
  private static final Long leadMemberId = 111L;
  private static final MemberUrn leadMemberUrn = new MemberUrn(leadMemberId);

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssEntityViewDB = new LssEntityViewDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
    //clean first
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssEntityViewDB.DB_LSS_ENTITY_VIEW, LssEntityViewDB.TABLE_PROFILE_VIEW, serializedSeatUrn)
            .build()));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssEntityViewDB.DB_LSS_ENTITY_VIEW, LssEntityViewDB.TABLE_PROFILE_VIEW, serializedSeatUrn)
            .build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateAndGetProfileViewSuccess() {
    Long lastViewedTime = System.currentTimeMillis();
    HttpStatus httpStatus = upsertProfileViewRecord(lastViewedTime);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
    ProfileView profileView = await(_lssEntityViewDB.getProfileView(seatUrn, leadMemberUrn));
    assertThat(UrnUtils.createContractUrn(profileView.getContractUrn())).isEqualTo(contractUrn);
    assertThat(profileView.getLastViewedTime()).isEqualTo(lastViewedTime);
  }

  @Test
  public void testUpdateProfileView() {
    Long lastViewedTime1 = System.currentTimeMillis();
    upsertProfileViewRecord(lastViewedTime1);
    Long lastViewedTime2 = System.currentTimeMillis() + 1000L;
    HttpStatus httpStatus = upsertProfileViewRecord(lastViewedTime2);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
    ProfileView profileView = await(_lssEntityViewDB.getProfileView(seatUrn, leadMemberUrn));
    assertThat(UrnUtils.createContractUrn(profileView.getContractUrn())).isEqualTo(contractUrn);
    assertThat(profileView.getLastViewedTime()).isEqualTo(lastViewedTime2);
  }

  private HttpStatus upsertProfileViewRecord(long lastViewedTime) {
    ProfileView profileView = new ProfileView();
    profileView.setLastViewedTime(lastViewedTime);
    profileView.setContractUrn(contractUrn.toString());
    return await(_lssEntityViewDB.upsertProfileView(seatUrn, leadMemberUrn, profileView));
  }
}
