package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.CsvImportTaskUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.AccountToListMapping;
import com.linkedin.sales.espresso.ChangeTypes;
import com.linkedin.sales.espresso.CreatorToListCsvImportView;
import com.linkedin.sales.espresso.DefaultList;
import com.linkedin.sales.espresso.ImportTaskToListCsvImportView;
import com.linkedin.sales.espresso.LeadManager;
import com.linkedin.sales.espresso.LeadOwner;
import com.linkedin.sales.espresso.LeadRelationshipStrength;
import com.linkedin.sales.espresso.LeadRole;
import com.linkedin.sales.espresso.LeadText;
import com.linkedin.sales.espresso.ListCsvImport;
import com.linkedin.sales.espresso.ListCsvImportState;
import com.linkedin.sales.espresso.ListEntity;
import com.linkedin.sales.espresso.ListSource;
import com.linkedin.sales.espresso.ListToListCsvImportView;
import com.linkedin.sales.espresso.ListType;
import com.linkedin.sales.espresso.RelationshipMapChangeLog;
import com.linkedin.sales.espresso.RelationshipStrengthType;
import com.linkedin.sales.espresso.RoleType;
import com.linkedin.sales.espresso.SeatList;
import com.linkedin.sales.espresso.SeatListRole;
import com.linkedin.sales.service.utils.UrnUtils;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.collections.Maps;

import static org.assertj.core.api.Assertions.*;
import static org.testng.Assert.*;
import static java.lang.Boolean.*;


/**
 * Created by hacao at 6/13/2018
 * Test class for {@link LssListDB}
 */
public class TestLssListDB extends IntegrationTestBase {

  private static final long LIST_ID = 3L;
  private static final long LIST_ID_2 = 4L;
  private static final long CONTRACT_ID = 100L;
  private static final long SEAT_ID = 200L;
  private static final long SEAT_ID_2 = 201L;
  private static final String ORAGNIZATION_URN_STR = "urn:li:organization:300";
  private static final Urn ENTITY_URN = new MemberUrn(1L);

  private static final Urn PLACEHOLDER_ENTITY_URN = UrnUtils.createSalesListEntityPlaceholderUrn("urn:li:salesListEntityPlaceholder:(urn:li:salesList:6635593670571,7140828854431928320)");

  private static final Urn ENTITY_URN_2 = new MemberUrn(2L);
  private static final Urn ENTITY_URN_3 = new MemberUrn(3L);
  private static final long LAST_MODIFIED_TIME = 2000L;
  private static final long CSV_IMPORT_TASK_ID = 55L;
  private static final long CSV_IMPORT_TASK_ID_2 = 56L;
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final SalesListUrn SALES_LIST_URN = UrnUtils.createSalesListUrn(LIST_ID);
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);

  private final String LIST_NAME = "test";

  private final Set<Long> _listCsvImportCleanupRegistry = ConcurrentHashMap.newKeySet();

  private LssListDB _lssListDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssListDB = new LssListDB(_parSeqEspressoClient);

    _offspringTestHelper.startGenerator();
    // clean up first
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST_ENTITY, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_SEAT_LIST,
            new String[]{String.valueOf(SEAT_ID), ListType.LEAD.toString(), String.valueOf(LIST_ID)}).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_ACCOUNT_TO_LIST_MAPPING,
            UrnUtils.createSeatUrn(SEAT_ID).toString()).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_DEFAULT_LIST,
            UrnUtils.createSeatUrn(SEAT_ID).toString(), ListType.ACCOUNT.toString()).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_RELATIONSHIP_MAP_CHANGE_LOG,
            String.valueOf(LIST_ID)).build()));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_SEAT_LIST,
            new String[]{String.valueOf(SEAT_ID), ListType.LEAD.toString(), String.valueOf(LIST_ID)}).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST_ENTITY, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_ACCOUNT_TO_LIST_MAPPING,
            UrnUtils.createSeatUrn(SEAT_ID).toString()).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_DEFAULT_LIST,
            UrnUtils.createSeatUrn(SEAT_ID).toString(), ListType.ACCOUNT.toString()).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_RELATIONSHIP_MAP_CHANGE_LOG,
            String.valueOf(LIST_ID)).build()));
  }

  @AfterMethod
  public void cleanDataAfter() {
    _listCsvImportCleanupRegistry.forEach(listCsvImportId ->
        await(_parSeqEspressoClient.execute(
            DeleteRequest
                .builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST_CSV_IMPORT, String.valueOf(listCsvImportId))
                .build())));
    _listCsvImportCleanupRegistry.clear();
  }

  @Test
  public void testCreateListSucceed() {
    long listId = createList();
    assertThat(listId).isEqualTo(LIST_ID);
  }

  @Test
  public void testCreateListFailCreateTwice() {
    createList();
    // second create
    assertThatExceptionOfType(PromiseException.class).isThrownBy(this::createList)
        .withMessageContaining(String.format("Unexpected response code for call to to create list for %d:", LIST_ID))
        .withCauseInstanceOf(RuntimeException.class);
  }

  @Test
  public void testGetListSucceed() {
    createList();
    com.linkedin.sales.espresso.List list = await(_lssListDB.getList(LIST_ID));
    assertThat(list.creatorSeatId).isEqualTo(SEAT_ID);
    assertThat(list.name.toString()).isEqualTo(LIST_NAME);
    assertThat(list.listType).isEqualTo(ListType.LEAD);
    assertThat(list.contractId).isEqualTo(CONTRACT_ID);
  }

  @Test
  public void testGetListFailNotFound() {
    createList();
    final long ANOTHER_LIST_ID = 999L;
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getList(ANOTHER_LIST_ID));
    }).withCause(new EntityNotFoundException(null, String.format("can not find list:%d", ANOTHER_LIST_ID)));
  }

  @Test
  public void testUpdateListSucceed() {
    createList();
    String updateName = "updateName";
    String lastModifiedBySeatUrn = "urn:li:seat:12345";

    // update nothing
    long currentLastModifiedTime = await(_lssListDB.getList(LIST_ID)).lastModifiedTime;
    com.linkedin.sales.espresso.List espressoList = new com.linkedin.sales.espresso.List();
    Boolean isSuccessful = await(_lssListDB.updateList(LIST_ID, espressoList));
    assertThat(isSuccessful).isTrue();
    long updatedLastModifiedTime = await(_lssListDB.getList(LIST_ID)).lastModifiedTime;
    assertThat(updatedLastModifiedTime).isEqualTo(currentLastModifiedTime);

    // update name only
    currentLastModifiedTime = await(_lssListDB.getList(LIST_ID)).lastModifiedTime;
    espressoList = new com.linkedin.sales.espresso.List();
    espressoList.name = updateName;
    isSuccessful = await(_lssListDB.updateList(LIST_ID, espressoList));
    assertThat(isSuccessful).isTrue();
    com.linkedin.sales.espresso.List list = await(_lssListDB.getList(LIST_ID));
    assertThat(list.name.toString()).isEqualTo(updateName);
    assertThat(list.lastModifiedTime).isNotEqualTo(currentLastModifiedTime);

    // update specified last modified time and last modifier
    espressoList = new com.linkedin.sales.espresso.List();
    espressoList.lastModifiedTime = LAST_MODIFIED_TIME;
    espressoList.lastModifiedBySeatUrn = lastModifiedBySeatUrn;
    isSuccessful = await(_lssListDB.updateList(LIST_ID, espressoList));
    assertThat(isSuccessful).isTrue();
    list = await(_lssListDB.getList(LIST_ID));
    assertThat(list.lastModifiedTime).isEqualTo(LAST_MODIFIED_TIME);
    assertThat(list.lastModifiedBySeatUrn.toString()).isEqualTo(lastModifiedBySeatUrn);

    // update name, specified last modified time and modifier
    espressoList = new com.linkedin.sales.espresso.List();
    espressoList.name = updateName;
    espressoList.lastModifiedTime = LAST_MODIFIED_TIME;
    espressoList.lastModifiedBySeatUrn = lastModifiedBySeatUrn;
    isSuccessful = await(_lssListDB.updateList(LIST_ID, espressoList));
    assertThat(isSuccessful).isTrue();
    list = await(_lssListDB.getList(LIST_ID));
    assertThat(list.name.toString()).isEqualTo(updateName);
    assertThat(list.lastModifiedTime).isEqualTo(LAST_MODIFIED_TIME);
    assertThat(list.lastModifiedBySeatUrn.toString()).isEqualTo(lastModifiedBySeatUrn);
  }

  @Test
  public void testUpdateListFailNotFound() {
    final long ANOTHER_LIST_ID = 999L;
    com.linkedin.sales.espresso.List espressoList = new com.linkedin.sales.espresso.List();
    espressoList.name = "updateName";
    Boolean isSuccessful = await(_lssListDB.updateList(ANOTHER_LIST_ID, espressoList));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testDeleteListSucceed() {
    createList();
    Boolean isSuccessful = await(_lssListDB.deleteList(LIST_ID));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteListFailNotFound() {
    Boolean isSuccessful = await(_lssListDB.deleteList(LIST_ID));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testCreateSeatListSucceed() {
    Boolean isSuccessful = createSeatList();
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testCreateSeatListFailCreateTwice() {
    createSeatList();
    Boolean isSuccessful = createSeatList();
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testGetSeatListSucceed() {
    createSeatList();
    SeatList seatList = await(_lssListDB.getSeatList(SEAT_ID, LIST_ID, ListType.LEAD));
    assertThat(seatList.contractId).isEqualTo(CONTRACT_ID);
    assertThat(seatList.role).isEqualTo(SeatListRole.OWNER);
    assertThat(seatList.ownedByMe).isTrue();
  }

  @Test
  public void testGetSeatListFailNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getSeatList(SEAT_ID, LIST_ID, ListType.LEAD));
    })
        .withCause(new EntityNotFoundException(null,
            String.format("can not find seatList for seat:%d, list:%d, listType:%s", SEAT_ID, LIST_ID,
                ListType.LEAD.toString())));
  }

  @Test
  public void testGetSeatListsSucceed() {
    createSeatList();
    Pair<Integer, List<Pair<Long, SeatList>>> totalHitsAndPairs = await(_lssListDB.getSeatLists(SEAT_ID, ListType.LEAD, 0, 10));
    assertThat(totalHitsAndPairs.getFirst()).isEqualTo(1);
    Pair<Long, SeatList> pair = totalHitsAndPairs.getSecond().get(0);
    long listId = pair.getFirst();
    SeatList seatList = pair.getSecond();
    assertThat(listId).isEqualTo(LIST_ID);
    assertThat(seatList.contractId).isEqualTo(CONTRACT_ID);
    assertThat(seatList.role).isEqualTo(SeatListRole.OWNER);
    assertThat(seatList.ownedByMe).isTrue();
  }

  @Test
  public void testGetSeatListsFailNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getSeatLists(SEAT_ID, ListType.LEAD, 0, 10));
    }).withCause(new EntityNotFoundException(null, String.format("can not find seatLists for seat:%d", SEAT_ID)));
  }

  @Test
  public void testGetListIdsForEntitySucceed() {
    createListEntity();
    List<Long> listIds = awaitWithDefaultInitialDelay(_lssListDB.getListIdsForEntity(ENTITY_URN, CONTRACT_ID, SEAT_ID));
    assertThat(listIds.size()).isEqualTo(1);
    assertThat(listIds.get(0)).isEqualTo(LIST_ID);
  }

  @Test
  public void testGetListIdsForEntitySucceedWithNoSeatId() {
    createListEntity();
    List<Long> listIds = awaitWithDefaultInitialDelay(_lssListDB.getListIdsForEntity(ENTITY_URN, CONTRACT_ID, null));
    assertThat(listIds.size()).isEqualTo(1);
    assertThat(listIds.get(0)).isEqualTo(LIST_ID);
  }


  @Test
  public void testGetListIdsForEntityFailNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getListIdsForEntity(ENTITY_URN, CONTRACT_ID, SEAT_ID));
    }).withCause(new EntityNotFoundException(null,
        String.format("can not find listIds for entity:%s, contract: %d, ownerSeat: %d", ENTITY_URN.toString(), CONTRACT_ID, SEAT_ID)));
  }

  @Test
  public void testUpdateSeatListSucceed() {
    createSeatList();
    long updateLastViewedTime = System.currentTimeMillis();
    SeatList espressoSeatList = new SeatList();
    espressoSeatList.lastViewedTime = updateLastViewedTime;
    Boolean isSuccessful =
        await(_lssListDB.updateSeatList(SEAT_ID, LIST_ID, ListType.LEAD, espressoSeatList));
    assertThat(isSuccessful).isTrue();
    SeatList seatList = await(_lssListDB.getSeatList(SEAT_ID, LIST_ID, ListType.LEAD));
    assertThat(seatList.lastViewedTime).isEqualTo(updateLastViewedTime);
  }

  @Test
  public void testUpdateSeatListFailNotFound() {
    long updateLastViewedTime = System.currentTimeMillis();
    SeatList espressoSeatList = new SeatList();
    espressoSeatList.lastViewedTime = updateLastViewedTime;
    Boolean isSuccessful =
        await(_lssListDB.updateSeatList(SEAT_ID, LIST_ID, ListType.LEAD, espressoSeatList));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testDeleteSeatListSucceed() {
    createSeatList();
    Boolean isSuccessful = await(_lssListDB.deleteSeatList(SEAT_ID, LIST_ID, ListType.LEAD));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteSeatListFailNotFound() {
    Boolean isSuccessful = await(_lssListDB.deleteSeatList(SEAT_ID, LIST_ID, ListType.LEAD));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testGetListEntityCountSucceed() {
    createListEntity();
    long count = await(_lssListDB.getListEntityCount(LIST_ID));
    assertThat(count).isEqualTo(1);
  }

  @Test
  public void testGetListEntityCountFailNotFound() {
    final long ANOTHER_LIST_ID = 999L;
    long count = await(_lssListDB.getListEntityCount(ANOTHER_LIST_ID));
    assertThat(count).isEqualTo(0); // return 0 even if no such list exist
  }

  @Test
  public void testCreateListEntity() {
    ListEntity listEntity = new ListEntity();
    listEntity.contractId = CONTRACT_ID;
    listEntity.createdTime = System.currentTimeMillis();
    listEntity.lastModifiedTime = System.currentTimeMillis();
    listEntity.ownerSeatId = SEAT_ID;
    listEntity.sortOrder = listEntity.createdTime;
    listEntity.saved = false;
    Boolean booleanResult = await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, listEntity));
    assertThat(booleanResult).isTrue();
  }

  @Test
  public void testCreatePlaceholderListEntity() {
    ListEntity listEntity = new ListEntity();
    listEntity.contractId = CONTRACT_ID;
    listEntity.createdTime = System.currentTimeMillis();
    listEntity.lastModifiedTime = System.currentTimeMillis();
    listEntity.ownerSeatId = SEAT_ID;
    listEntity.sortOrder = listEntity.createdTime;
    listEntity.saved = false;
    Boolean booleanResult = await(_lssListDB.createListEntity(LIST_ID, PLACEHOLDER_ENTITY_URN, listEntity));
    assertThat(booleanResult).isTrue();
  }

  @Test
  public void testCreateListEntities() throws URISyntaxException {
    ListEntity listEntity1 = new ListEntity();
    listEntity1.contractId = CONTRACT_ID;
    listEntity1.createdTime = System.currentTimeMillis();
    listEntity1.lastModifiedTime = System.currentTimeMillis();
    listEntity1.ownerSeatId = SEAT_ID;
    listEntity1.sortOrder = listEntity1.createdTime;
    listEntity1.saved = false;

    ListEntity listEntity2 = new ListEntity();
    listEntity2.contractId = CONTRACT_ID;
    listEntity2.createdTime = System.currentTimeMillis();
    listEntity2.lastModifiedTime = System.currentTimeMillis();
    listEntity2.ownerSeatId = SEAT_ID;
    listEntity2.sortOrder = listEntity2.createdTime;
    listEntity2.saved = false;

    Map<Urn, ListEntity> entityUrnToListEntityMap = Maps.newHashMap();
    entityUrnToListEntityMap.put(ENTITY_URN, listEntity1);
    entityUrnToListEntityMap.put(ENTITY_URN_2, listEntity2);

    Map<Urn, HttpStatus> resMap =
        await(_lssListDB.upsertListEntities(UrnUtils.createSalesListUrn(LIST_ID), entityUrnToListEntityMap, false));

    assertEquals(resMap.get(ENTITY_URN), HttpStatus.S_201_CREATED);
    assertEquals(resMap.get(ENTITY_URN_2), HttpStatus.S_201_CREATED);
  }

  @Test
  public void testGetListEntities() {
    createListEntity();
    Pair<Integer, List<Pair<Urn, ListEntity>>> resultPair =
        await(_lssListDB.getListEntities(LIST_ID, 0, 10, SortOrder.DESCENDING));
    assertThat(resultPair.getFirst()).isEqualTo(1);
    List<Pair<Urn, ListEntity>> listEntityPair = resultPair.getSecond();
    assertThat(listEntityPair.size()).isEqualTo(1);
    assertThat(listEntityPair.get(0).getFirst()).isEqualToComparingFieldByField(ENTITY_URN);
  }

  @Test
  public void testGetListEntitiesWithTier() {
    createListEntityWithTier();
    Pair<Integer, List<Pair<Urn, ListEntity>>> resultPair =
        await(_lssListDB.getListEntities(LIST_ID, 0, 10, SortOrder.DESCENDING));
    assertThat(resultPair.getFirst()).isEqualTo(1);
    List<Pair<Urn, ListEntity>> listEntityPair = resultPair.getSecond();
    assertThat(listEntityPair.size()).isEqualTo(1);
    assertThat(listEntityPair.get(0).getFirst()).isEqualToComparingFieldByField(ENTITY_URN);
    assertThat(listEntityPair.get(0).getSecond().tier).isEqualTo(1);
  }

  @Test
  public void testBatchGetListEntities() throws URISyntaxException {
    await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, createListEntityObject()));
    await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN_2, createListEntityObject()));
    await(_lssListDB.createListEntity(LIST_ID_2, ENTITY_URN_3, createListEntityObject()));

    SalesListUrn listUrn1 = UrnUtils.createSalesListUrn(LIST_ID);
    SalesListUrn listUrn2 = UrnUtils.createSalesListUrn(LIST_ID_2);

    List<Pair<SalesListUrn, Urn>> keyList =
        ImmutableList.of(new Pair<>(listUrn1, ENTITY_URN), new Pair<>(listUrn1, ENTITY_URN_2),
            new Pair<>(listUrn2, ENTITY_URN_3));

    Map<Pair<SalesListUrn, Urn>, ListEntity> entityMap =
        await(_lssListDB.batchGetListEntities(keyList));

    assertThat(entityMap.size()).isEqualTo(3);

    for (Pair<SalesListUrn, Urn> key : keyList) {
      assertThat(entityMap.containsKey(new Pair<>(key.getFirst(), key.getSecond()))).isTrue();
    }
  }

  @Test
  public void testGetEntityUrnsForListEntitiesForMultipleLists() {
    createMultipleListEntity();
    Set<Long> listIds = new HashSet<>();
    listIds.add(LIST_ID);
    listIds.add(LIST_ID_2);
    List<Urn> resultUrns =
        await(_lssListDB.getEntityUrnsForListEntitiesForMultipleLists(listIds, 0, 10));
    assertThat(resultUrns.size()).isEqualTo(3);
    assertThat(resultUrns.contains(ENTITY_URN)).isTrue();
    assertThat(resultUrns.contains(ENTITY_URN_2)).isTrue();
    assertThat(resultUrns.contains(ENTITY_URN_3)).isTrue();
  }

  @Test
  public void testGetEntityUrnsForListEntitiesForEmptyListOfListIds() {
    createMultipleListEntity();
    Set<Long> listIds = new HashSet<>();
    List<Urn> resultUrns =
        await(_lssListDB.getEntityUrnsForListEntitiesForMultipleLists(listIds, 0, 10));
    assertThat(resultUrns.size()).isEqualTo(0);
  }

  @Test
  public void testGetListEntity() {
    createListEntity();
    ListEntity listEntity = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN));
    assertThat(listEntity.contractId).isEqualTo(CONTRACT_ID);
    assertThat(listEntity.ownerSeatId).isEqualTo(SEAT_ID);
  }

  @Test
  public void testUpdateListEntitySucceed() {
    createListEntity();
    final long currentLastModifiedTime = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN)).lastModifiedTime;

    // update nothing
    ListEntity listEntity = new ListEntity();
    Boolean response = await(_lssListDB.updateListEntity(LIST_ID, ENTITY_URN, listEntity));
    assertThat(response).isTrue();
    long newLastModifiedTime = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN)).lastModifiedTime;
    assertThat(newLastModifiedTime).isEqualTo(currentLastModifiedTime);

    // update last modified time
    listEntity = new ListEntity();
    listEntity.lastModifiedTime = LAST_MODIFIED_TIME;
    response = await(_lssListDB.updateListEntity(LIST_ID, ENTITY_URN, listEntity));
    assertThat(response).isTrue();
    listEntity = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN));
    assertThat(listEntity.lastModifiedTime).isEqualTo(LAST_MODIFIED_TIME);
  }

  @Test
  public void testUpdateListEntityFailed() {
    final long ANOTHER_LIST_ID = 999L;
    ListEntity listEntity = new ListEntity();
    listEntity.lastModifiedTime = System.currentTimeMillis();
    Boolean response = await(_lssListDB.updateListEntity(ANOTHER_LIST_ID, ENTITY_URN, listEntity));
    assertThat(response).isFalse();
  }

  @Test
  public void testGetListEntityCounts() {
    createListEntity();
    Map<Urn, Integer> entityCountMap =
        awaitWithDefaultInitialDelay(_lssListDB.getEntityCounts(Collections.singleton(ENTITY_URN), CONTRACT_ID, SEAT_ID));

    assertThat(entityCountMap.size()).isEqualTo(1);
    assertThat(entityCountMap.containsKey(ENTITY_URN)).isTrue();
    assertThat(entityCountMap.get(ENTITY_URN)).isEqualTo(1);
  }

  @Test
  public void testDeleteListentity() {
    createListEntity();
    Boolean response = await(_lssListDB.deleteListEntity(LIST_ID, ENTITY_URN, false));
    assertThat(response).isTrue();
  }

  @Test
  public void testDeleteListEntities() {
    createListEntity();
    Boolean response = await(_lssListDB.deleteAllListEntities(LIST_ID));
    assertThat(response).isTrue();
  }

  //Use this method to test adding new fields. Move previously added fields to createListEntityObject method.
  @Test
  public void testCreateListEntityWithNewFields() {
    ListEntity listEntity = createListEntityObject();
    listEntity.setLeadManager(new LeadManager(null, 1001L, "urn:li:seat:10", "urn:li:salesListEntityPlaceholder:(urn:li:salesList:01, 1001)"));
    listEntity.setLeadOwner(new LeadOwner("urn:li:seat:20", 1001L, "urn:li:seat:10"));
    listEntity.setLeadRelationshipStrength(new LeadRelationshipStrength(RelationshipStrengthType.STRONG, 1001L, "urn:li:seat:10"));
    listEntity.setLeadRole(new LeadRole(RoleType.EVALUATOR, 1001L, "urn:li:seat:10"));
    listEntity.setLeadText(new LeadText("User Input", 1001L, "urn:li:seat:10"));

    boolean res = await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, listEntity));

    assertThat(res).isTrue();

    ListEntity createdListEntity = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN));
    assertEquals(createdListEntity, listEntity);
  }

  @Test
  public void testPartialUpdateOfRelationshipMapFields() {
    boolean isCreated = createListEntity();
    assertTrue(isCreated);
    ListEntity createdListEntity = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN));
    createdListEntity.setLeadManager(new LeadManager("urn:li:member:01", 1001L, "urn:li:seat:10", null));
    createdListEntity.setLeadOwner(new LeadOwner("urn:li:seat:20", 1001L, "urn:li:seat:10"));
    createdListEntity.setLeadRelationshipStrength(new LeadRelationshipStrength(RelationshipStrengthType.STRONG, 1001L, "urn:li:seat:10"));
    createdListEntity.setLeadRole(new LeadRole(RoleType.EVALUATOR, 1001L, "urn:li:seat:10"));
    Map<Urn, ListEntity> updateEntityMap = Maps.newHashMap();
    updateEntityMap.put(ENTITY_URN, createdListEntity);
    Map<Urn, HttpStatus> response = await(_lssListDB.upsertListEntities(SALES_LIST_URN, updateEntityMap, true));
    assertThat(response.get(ENTITY_URN)).isEqualTo(HttpStatus.S_200_OK);

    ListEntity updatedListEntity = await(_lssListDB.getListEntity(LIST_ID, ENTITY_URN));
    assertEquals(updatedListEntity, createdListEntity);
  }

  @Test
  public void testCreateListCsvImportSucceed() {
    long listCsvImportId = createListCsvImport();
    assertNotEquals(listCsvImportId, 0);
  }

  @Test
  public void testGetListCsvImportSucceed() {
    long listCsvImportId = createListCsvImport();
    ListCsvImport listCsvImport = await(_lssListDB.getListCsvImport(listCsvImportId));
    assertThat(listCsvImport.state).isEqualTo(ListCsvImportState.IN_PROGRESS);
    assertThat(listCsvImport.creatorSeatUrn.toString()).isEqualTo(UrnUtils.createSeatUrn(SEAT_ID).toString());
    assertThat(listCsvImport.contractUrn.toString()).isEqualTo(UrnUtils.createContractUrn(CONTRACT_ID).toString());
    assertThat(listCsvImport.csvImportTaskUrn.toString()).isEqualTo(UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID).toString());
    assertThat(listCsvImport.listName.toString()).isEqualTo(LIST_NAME);
    assertThat(listCsvImport.isImportingToExistingList).isEqualTo(FALSE);
  }

  @Test
  public void testGetListCsvImportFailNotFound() {
    final long ANOTHER_LIST_CSV_IMPORT_ID = 999L;
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getListCsvImport(ANOTHER_LIST_CSV_IMPORT_ID));
    }).withCause(new EntityNotFoundException(null, String.format("can not find list csv import:%d", ANOTHER_LIST_CSV_IMPORT_ID)));
  }

  @Test
  public void testUpdateListCsvImportSucceed() {
    long listCsvImportId = createListCsvImport();
    ListCsvImportState newState = ListCsvImportState.SUCCEEDED;

    // update nothing
    long currentLastModifiedTime = await(_lssListDB.getListCsvImport(listCsvImportId)).lastModifiedTime;
    ListCsvImport espressoListCsvImport = new ListCsvImport();
    boolean isSuccessful = await(_lssListDB.updateListCsvImport(listCsvImportId, espressoListCsvImport));
    assertThat(isSuccessful).isTrue();
    long updatedLastModifiedTime = await(_lssListDB.getListCsvImport(listCsvImportId)).lastModifiedTime;
    assertThat(updatedLastModifiedTime).isEqualTo(currentLastModifiedTime);

    // update state only
    currentLastModifiedTime = await(_lssListDB.getListCsvImport(listCsvImportId)).lastModifiedTime;
    espressoListCsvImport = new ListCsvImport();
    espressoListCsvImport.state = newState;
    isSuccessful = await(_lssListDB.updateListCsvImport(listCsvImportId, espressoListCsvImport));
    assertThat(isSuccessful).isTrue();
    ListCsvImport listCsvImport = await(_lssListDB.getListCsvImport(listCsvImportId));
    assertThat(listCsvImport.state).isEqualTo(newState);
    assertThat(listCsvImport.lastModifiedTime).isEqualTo(currentLastModifiedTime);

    // update state and last modifier
    espressoListCsvImport = new ListCsvImport();
    espressoListCsvImport.lastModifiedTime = LAST_MODIFIED_TIME;
    espressoListCsvImport.state = newState;
    isSuccessful = await(_lssListDB.updateListCsvImport(listCsvImportId, espressoListCsvImport));
    assertThat(isSuccessful).isTrue();
    listCsvImport = await(_lssListDB.getListCsvImport(listCsvImportId));
    assertThat(listCsvImport.state).isEqualTo(newState);
    assertThat(listCsvImport.lastModifiedTime).isEqualTo(LAST_MODIFIED_TIME);
  }

  @Test
  public void testUpdateListCsvImportFailNotFound() {
    final long ANOTHER_LIST_CSV_IMPORT_ID = 999L;
    ListCsvImport espressoListCsvImport = new ListCsvImport();
    espressoListCsvImport.state = ListCsvImportState.SUCCEEDED;
    Boolean isSuccessful = await(_lssListDB.updateListCsvImport(ANOTHER_LIST_CSV_IMPORT_ID, espressoListCsvImport));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testDeleteListCsvImportSucceed() {
    long listCsvImportId = createListCsvImport();
    Boolean isSuccessful = await(_lssListDB.deleteListCsvImport(listCsvImportId));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteListCsvImportFailNotFound() {
    final long ANOTHER_LIST_CSV_IMPORT_ID = 999L;
    Boolean isSuccessful = await(_lssListDB.deleteListCsvImport(ANOTHER_LIST_CSV_IMPORT_ID));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testGetListCsvImportsByCreator() {
    SeatUrn creator = UrnUtils.createSeatUrn(SEAT_ID);
    createListCsvImport();
    // Allow time for the write to the materialized view
    Pair<Integer, List<Pair<Long, CreatorToListCsvImportView>>> totalHitsAndPairs = awaitWithDefaultInitialDelay(
        _lssListDB.getListCsvImportsByCreator(creator, 0, 10, new com.linkedin.saleslist.ListCsvImportState[]{}));
    assertThat(totalHitsAndPairs.getFirst()).isEqualTo(1);
    Pair<Long, CreatorToListCsvImportView> pair = totalHitsAndPairs.getSecond().get(0);
    long listCsvImportId = pair.getFirst();
    CreatorToListCsvImportView creatorToListCsvImportView = pair.getSecond();
    assertThat(listCsvImportId).isNotNull();
    assertThat(creatorToListCsvImportView.csvImportTaskUrn.toString()).isEqualTo(UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID).toString());
    assertThat(creatorToListCsvImportView.listId).isEqualTo(LIST_ID);
  }

  @Test
  public void testGetListCsvImportsByCreatorFilteredByState() {
    SeatUrn creator = UrnUtils.createSeatUrn(SEAT_ID);
    createListCsvImport(ListCsvImportState.IN_PROGRESS);
    createListCsvImport(ListCsvImportState.SUCCEEDED);
    com.linkedin.saleslist.ListCsvImportState[] states = new com.linkedin.saleslist.ListCsvImportState[] {
        com.linkedin.saleslist.ListCsvImportState.IN_PROGRESS
    };
    // Allow time for the write to the materialized view
    Pair<Integer, List<Pair<Long, CreatorToListCsvImportView>>> totalHitsAndPairs = awaitWithDefaultInitialDelay(
        _lssListDB.getListCsvImportsByCreator(creator, 0, 10, states));
    assertThat(totalHitsAndPairs.getFirst()).isEqualTo(1);
    Pair<Long, CreatorToListCsvImportView> pair = totalHitsAndPairs.getSecond().get(0);
    long listCsvImportId = pair.getFirst();
    CreatorToListCsvImportView creatorToListCsvImportView = pair.getSecond();
    assertThat(listCsvImportId).isNotNull();
    assertThat(creatorToListCsvImportView.csvImportTaskUrn.toString()).isEqualTo(UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID).toString());
    assertThat(creatorToListCsvImportView.listId).isEqualTo(LIST_ID);
  }

  @Test
  public void testGetListCsvImportsByCreatorOrdering() {
    SeatUrn creator = UrnUtils.createSeatUrn(SEAT_ID);
    long listId0 = createListCsvImport();
    long listId1 = createListCsvImport();
    // Allow time for the write to the materialized view
    Pair<Integer, List<Pair<Long, CreatorToListCsvImportView>>> totalHitsAndPairs = awaitWithDefaultInitialDelay(
        _lssListDB.getListCsvImportsByCreator(creator, 0, 10, new com.linkedin.saleslist.ListCsvImportState[]{}));
    assertThat(totalHitsAndPairs.getFirst()).isEqualTo(2);
    long returnedId0 = totalHitsAndPairs.getSecond().get(0).getFirst();
    Assert.assertEquals(returnedId0, listId1);
    long returnedId1 = totalHitsAndPairs.getSecond().get(1).getFirst();
    Assert.assertEquals(returnedId1, listId0);
  }

  @Test
  public void testGetListCsvImportsByCreatorFailNotFound() {
    SeatUrn creator = UrnUtils.createSeatUrn(SEAT_ID_2);
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getListCsvImportsByCreator(creator, 0, 10, new com.linkedin.saleslist.ListCsvImportState[]{}));
    }).withCause(new EntityNotFoundException(null, String.format("can not find CreatorToListCsvImportView for creator:%s", creator)));
  }

  @Test
  public void testGetListCsvImportsByCsvImportTask() {
    CsvImportTaskUrn importTaskUrn = UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID);
    createListCsvImport();
    // Allow time for the write to the materialized view
    ImportTaskToListCsvImportView importTaskToListCsvImportView = awaitWithDefaultInitialDelay(
        _lssListDB.getListCsvImportsByCsvImportTask(importTaskUrn));
    assertThat(importTaskToListCsvImportView).isNotNull();
    assertThat(importTaskToListCsvImportView.listId).isEqualTo(LIST_ID);
  }

  @Test
  public void testGetListCsvImportsByCsvImportTaskFailNotFound() {
    CsvImportTaskUrn importTaskUrn = UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID_2);
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getListCsvImportsByCsvImportTask(importTaskUrn));
    }).withCause(new EntityNotFoundException(null, String.format("can not find ImportTaskToListCsvImportView for import task:%s", importTaskUrn)));
  }

  @Test
  public void testGetListCsvImportsByList() {
    createListCsvImport();
    // Allow time for the write to the materialized view
    ListToListCsvImportView listToListCsvImportView = awaitWithDefaultInitialDelay(
        _lssListDB.getListCsvImportsBySalesList(LIST_ID));
    assertThat(listToListCsvImportView).isNotNull();
    assertThat(listToListCsvImportView.csvImportTaskUrn.toString()).isEqualTo(UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID).toString());
  }

  @Test
  public void testGetListCsvImportsByListFailNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssListDB.getListCsvImportsBySalesList(LIST_ID_2));
    }).withCause(new EntityNotFoundException(null, String.format("can not find ListToListCsvImportView for list:%d", LIST_ID_2)));
  }

  @Test
  public void testGetListsForSeatToListViewWhenListNotFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(
        () -> await(_lssListDB.getListsForSeatToListView(SEAT_ID, ListType.LEAD, ListSource.TAGS_MIGRATION, 0, 10)))
        .withCause(new EntityNotFoundException(null,
            String.format("cannot find lists from seatToListView for seat:%d", SEAT_ID)));
  }


  @AfterClass
  public void tearDown() throws Exception {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_LIST_ENTITY, String.valueOf(LIST_ID)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssListDB.DB_LSS_LIST, LssListDB.TABLE_SEAT_LIST,
            new String[]{String.valueOf(SEAT_ID), ListType.LEAD.toString(), String.valueOf(LIST_ID)}).build()));
    super.tearDown();
  }

  private long createList() {
    long currentTime = System.currentTimeMillis();
    com.linkedin.sales.espresso.List espressoList = new com.linkedin.sales.espresso.List();
    espressoList.creatorSeatId = SEAT_ID;
    espressoList.listType = ListType.LEAD;
    espressoList.createdTime = currentTime;
    espressoList.lastModifiedTime = currentTime;
    espressoList.contractId = CONTRACT_ID;
    espressoList.name = LIST_NAME;

    return await(_lssListDB.createList(LIST_ID, espressoList));
  }

  private Boolean createSeatList() {
    SeatList espressoSeatList = new SeatList();
    espressoSeatList.contractId = CONTRACT_ID;
    espressoSeatList.ownedByMe = true;
    espressoSeatList.lastViewedTime = System.currentTimeMillis();
    espressoSeatList.role = SeatListRole.OWNER;

    return await(_lssListDB.createSeatList(SEAT_ID, LIST_ID, ListType.LEAD, espressoSeatList));
  }

  private Boolean createListEntity() {
    return await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, createListEntityObject()));
  }

  private void createMultipleListEntity() {
    await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, createListEntityObject()));
    await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN_2, createListEntityObject()));
    await(_lssListDB.createListEntity(LIST_ID_2, ENTITY_URN_3, createListEntityObject()));
  }


  private ListEntity createListEntityObject() {
    ListEntity listEntity = new ListEntity();
    listEntity.contractId = CONTRACT_ID;
    listEntity.ownerSeatId = SEAT_ID;
    listEntity.createdTime = System.currentTimeMillis();
    listEntity.lastModifiedTime = listEntity.createdTime;
    listEntity.sortOrder = listEntity.createdTime;
    listEntity.saved = true;
    return listEntity;
  }

  private ListEntity createListEntityObjectWithTier(int tier) {
    ListEntity listEntity = createListEntityObject();
    listEntity.tier = tier;
    return listEntity;
  }

  private Boolean createListEntityWithTier() {
    return await(_lssListDB.createListEntity(LIST_ID, ENTITY_URN, createListEntityObjectWithTier(1)));
  }

  private long createListCsvImport() {
    return createListCsvImport(ListCsvImportState.IN_PROGRESS);
  }

  private long createListCsvImport(ListCsvImportState listState) {
    long currentTime = System.currentTimeMillis();
    ListCsvImport espressoListCsvImport = new ListCsvImport();
    espressoListCsvImport.state = listState;
    espressoListCsvImport.creatorSeatUrn = UrnUtils.createSeatUrn(SEAT_ID).toString();
    espressoListCsvImport.contractUrn = UrnUtils.createContractUrn(CONTRACT_ID).toString();
    espressoListCsvImport.csvImportTaskUrn = UrnUtils.createCsvImportTaskUrn(CSV_IMPORT_TASK_ID).toString();
    espressoListCsvImport.listId = LIST_ID;
    espressoListCsvImport.listName = LIST_NAME;
    espressoListCsvImport.createdTime = currentTime;
    espressoListCsvImport.lastModifiedTime = currentTime;
    espressoListCsvImport.isImportingToExistingList = false;

    return await(_lssListDB.createListCsvImport(espressoListCsvImport)
        .andThen(_listCsvImportCleanupRegistry::add));
  }

  @Test
  public void testCreateAccountToListMappingSucceed() throws URISyntaxException {
    assertThat(createAccountToListMapping()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateAccountToListMappingFailCreateTwice() throws URISyntaxException {
    assertThat(createAccountToListMapping()).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(createAccountToListMapping()).isEqualTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testDeleteAccountToListMappingSucceed() throws URISyntaxException {
    createAccountToListMapping();
    Boolean isSuccessful = await(_lssListDB.deleteAccountToListMapping(UrnUtils.createSeatUrn(SEAT_ID),
        OrganizationUrn.createFromString(ORAGNIZATION_URN_STR), LIST_ID));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteAccountToListMappingFailNotFound() throws URISyntaxException {
    Boolean isSuccessful = await(_lssListDB.deleteAccountToListMapping(UrnUtils.createSeatUrn(SEAT_ID),
        OrganizationUrn.createFromString(ORAGNIZATION_URN_STR), LIST_ID));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testGetAccountToListMappingSucceed() throws URISyntaxException {
    createAccountToListMapping();
    List<Pair<Long, AccountToListMapping>> resultList = await(_lssListDB.getAccountMapListIdsForGivenSeatAndAccount(UrnUtils.createSeatUrn(SEAT_ID),
        OrganizationUrn.createFromString(ORAGNIZATION_URN_STR), 0, 10));
    assertThat(resultList.size()).isEqualTo(1);
    assertThat(resultList.get(0).getFirst()).isEqualTo(LIST_ID);
    assertThat(resultList.get(0).getSecond().contractUrn.toString()).isEqualTo(CONTRACT_URN.toString());
  }

  @Test
  public void testGetAccountToListMappingFailNotFound() throws URISyntaxException {
    List<Pair<Long, AccountToListMapping>> resultList = await(_lssListDB.getAccountMapListIdsForGivenSeatAndAccount(UrnUtils.createSeatUrn(SEAT_ID),
        OrganizationUrn.createFromString(ORAGNIZATION_URN_STR), 0, 10));
    assertThat(resultList.size()).isEqualTo(0);
  }

  private HttpStatus createAccountToListMapping() throws URISyntaxException {
    AccountToListMapping accountToListMapping = new AccountToListMapping();
    accountToListMapping.contractUrn = CONTRACT_URN.toString();
    return await(_lssListDB.createAccountToListMapping(UrnUtils.createSeatUrn(SEAT_ID),
        OrganizationUrn.createFromString(ORAGNIZATION_URN_STR), LIST_ID, accountToListMapping));
  }

  // Default List Tests
  @Test
  public void testUpsertDefaultList_succeeded() {
    assertThat(upsertDefaultList()).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testUpsertDefaultList_createTwiceSucceeded() {
    assertThat(upsertDefaultList()).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(upsertDefaultList()).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testDeleteDefaultList_succeeded() {
    upsertDefaultList();
    Boolean isSuccessful = await(_lssListDB.deleteDefaultList(UrnUtils.createSeatUrn(SEAT_ID), ListType.ACCOUNT));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteDefaultList_failed() {
    Boolean isSuccessful = await(_lssListDB.deleteDefaultList(UrnUtils.createSeatUrn(SEAT_ID), ListType.ACCOUNT));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void testGetDefaultList_succeeded() {
    upsertDefaultList();
    DefaultList resultList = await(_lssListDB.getDefaultList(UrnUtils.createSeatUrn(SEAT_ID), ListType.ACCOUNT));
    assertThat(resultList.getListUrn().toString()).isEqualTo(SALES_LIST_URN.toString());
    assertThat(resultList.getContractUrn().toString()).isEqualTo(CONTRACT_URN.toString());
  }

  @Test
  public void testGetDefaultList_notFound() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
          await(_lssListDB.getDefaultList(UrnUtils.createSeatUrn(SEAT_ID), ListType.ACCOUNT));
        })
        .withCause(new EntityNotFoundException(null,
            String.format("Default list not found for seat: %s and list type: %s", UrnUtils.createSeatUrn(SEAT_ID),
                ListType.ACCOUNT)));
  }

  private HttpStatus upsertDefaultList() {
    DefaultList defaultList = new DefaultList();
    defaultList.contractUrn = CONTRACT_URN.toString();
    defaultList.listUrn = SALES_LIST_URN.toString();
    return await(_lssListDB.upsertDefaultList(UrnUtils.createSeatUrn(SEAT_ID), ListType.ACCOUNT, defaultList));
  }
  //Change Log Test Cases:

  @Test
  public void testCreateChangeLog() {
    assertThat(upsertChangeLog()).isNotNull();
  }

  @Test(description = "test we never overwrite the change log.")
  public void testCreateMultipleChangeLog() {
    assertThat(upsertChangeLog()).isNotNull();
    assertThat(upsertChangeLog()).isNotNull();
    assertThat(upsertChangeLog()).isNotNull();
  }

  @Test
  public void testInvalidCreateChangeLog() {
    RelationshipMapChangeLog changeLog = new RelationshipMapChangeLog();
    changeLog.setActorContractUrn(CONTRACT_URN.toString());
    changeLog.setChangeType(ChangeTypes.EDIT_LEAD);
    changeLog.setTargetMemberUrn(new MemberUrn(11L).toString());
    changeLog.setEventTime(100L);
    changeLog.setChangeValue(new MemberUrn(10L).toString());
    changeLog.setPreviousChangeValue(new MemberUrn(9L).toString());
    assertThrows(() -> await(_lssListDB.createRelationshipMapChangeLog(LIST_ID, changeLog)));
  }

  @Test
  public void deletedAllChangeLogsSuccess() {
    upsertChangeLog();
    upsertChangeLog();
    upsertChangeLog();
    assertThat(await(_lssListDB.deleteAllRelationshipMapChangeLogsForListId(LIST_ID))).isTrue();
  }

  @Test
  public void deletedAllChangeLogsFail() {
    assertThat(await(_lssListDB.deleteAllRelationshipMapChangeLogsForListId(LIST_ID))).isFalse();
  }

  @Test
  public void testE2ESingleChangeLogCreationSuccess() {
    await(_lssListDB.deleteAllRelationshipMapChangeLogsForListId(LIST_ID));
    long id = upsertChangeLog();
    Pair<Integer, List<Pair<Long, RelationshipMapChangeLog>>> res =
        await(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(LIST_ID, 0L, 120L, 0, 10));
    assertThat(res.getFirst()).isEqualTo(1);
    assertThat(res.getSecond().size()).isEqualTo(1);
    assertThat(res.getSecond().get(0).getFirst()).isEqualTo(id);
  }

  @Test
  public void testGetAllChangeLogsByIdSuccess() {
    upsertChangeLog();
    upsertChangeLog();
    upsertChangeLog();
    Pair<Integer, List<Pair<Long, RelationshipMapChangeLog>>> res =
        await(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(LIST_ID, 0L, 120L, 0, 10));
    assertThat(res.getFirst()).isEqualTo(3);
    assertThat(res.getSecond().size()).isEqualTo(3);
    assertThat(res.getSecond().get(0).getSecond().getActorSeatUrn().toString()).isEqualTo(SEAT_URN.toString());
  }

  @Test
  public void testGetAllChangeLogsByIdFailure() {
    upsertChangeLog();
    upsertChangeLog();
    upsertChangeLog();

    Pair<Integer, List<Pair<Long, RelationshipMapChangeLog>>> res =
        await(_lssListDB.findRelationshipMapChangeLogsByListIdAndEventTime(LIST_ID, 120L, 200L, 0, 10));
    assertThat(res.getFirst()).isEqualTo(0);
    assertThat(res.getSecond().size()).isEqualTo(0);
  }

  private Long upsertChangeLog() {
    RelationshipMapChangeLog changeLog = new RelationshipMapChangeLog();
    changeLog.setActorContractUrn(CONTRACT_URN.toString());
    changeLog.setChangeType(ChangeTypes.EDIT_LEAD);
    changeLog.setActorSeatUrn(SEAT_URN.toString());
    changeLog.setTargetMemberUrn(new MemberUrn(11L).toString());
    changeLog.setEventTime(100L);
    changeLog.setChangeValue(new MemberUrn(10L).toString());
    changeLog.setPreviousChangeValue(new MemberUrn(9L).toString());
    return await(_lssListDB.createRelationshipMapChangeLog(LIST_ID, changeLog));
  }
}
