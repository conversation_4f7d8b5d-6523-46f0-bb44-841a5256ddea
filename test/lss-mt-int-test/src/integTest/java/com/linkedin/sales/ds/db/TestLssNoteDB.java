package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesSharedSearchUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.AttributedText;
import com.linkedin.sales.espresso.Note;
import com.linkedin.salesnote.AnnotatableEntityUrn;
import com.linkedin.salesnote.NoteKey;
import com.linkedin.util.Pair;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


public class TestLssNoteDB extends IntegrationTestBase {

  private LssNoteDB _lssNoteDB;
  private static final String serializedSeatUrn1 = "urn:li:seat:1231";
  private static final SeatUrn seatUrn1 = new SeatUrn(1231L);
  private  AnnotatableEntityUrn entityUrn1 = new AnnotatableEntityUrn();
  private static final String serializedSeatUrn2 = "urn:li:seat:1232";
  private static final SeatUrn seatUrn2 = new SeatUrn(1232L);
  private  AnnotatableEntityUrn entityUrn2 = new AnnotatableEntityUrn();
  private static final String serializedContractUrn = "urn:li:contract:234";
  private static final String bodyText = "123abc";
  private static final long incorrectNoteId = -100;
  private static final Long RETENTION_DAYS = 9999L;
  private static final String memberEspressoData = "urn:li:member:1234";
  private static final String memberUrn = "urn:li:member:1234";
  private static final String organizationEspressoData = "urn:li:organization:5678";
  private static final String organizationUrn = "urn:li:organization:5678";
  private static final String salesSharedSearchEspressoData = "urn:li:salesSharedSearch:(urn:li:seat:111,PEOPLE_SEARCH_SHARE,33)";
  private static final String salesSharedSearchUrn = "urn:li:salesSharedSearch:(urn:li:seat:111,PEOPLE_SEARCH_SHARE,33)";

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssNoteDB = new LssNoteDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
    entityUrn1.setMemberUrn(new MemberUrn(4671L));
    entityUrn2.setMemberUrn(new MemberUrn(4672L));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssNoteDB.DB_LSS_NOTE, LssNoteDB.TABLE_NOTE, serializedSeatUrn1).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssNoteDB.DB_LSS_NOTE, LssNoteDB.TABLE_NOTE, serializedSeatUrn2).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateNoteSucceeded() {
    long id = createNote(seatUrn1, entityUrn1);
    System.out.println("test id is" + id);
    assertThat(id).isPositive();
  }

  @Test
  public void testUpdateNoteSucceeded() {
    Long noteId = createNote(seatUrn1, entityUrn1);
    Note note = new Note();
    note.lastModifiedTime = System.currentTimeMillis();
    AttributedText attributedText = new AttributedText();
    attributedText.attributes = Collections.emptyList();
    attributedText.text = "updateText";
    note.body = attributedText;
    Boolean success = await(_lssNoteDB.updateNote(seatUrn1, entityUrn1, noteId, note));
    assertThat(success).isTrue();
  }

  @Test
  public void testDeleteNoteSucceed() {
    long noteId = createNote(seatUrn1, entityUrn1);
    Boolean isSuccessful = await(_lssNoteDB.deleteNote(seatUrn1, entityUrn1, noteId));
    assertThat(isSuccessful).isTrue();
  }

  @Test
  public void testDeleteNoteFailNotFound() {
    Boolean isSuccessful = await(_lssNoteDB.deleteNote(seatUrn1, entityUrn1, incorrectNoteId));
    assertThat(isSuccessful).isFalse();
  }

  @Test
  public void findNotesBySeatAndEntity() {
    long noteId1 = createNote(seatUrn1, entityUrn1);
    long noteId2 = createNote(seatUrn1, entityUrn1);//Created by the same seatUrn and entityUrn as noteId1
    long noteId3 = createNote(seatUrn1, entityUrn2);
    long noteId4 = createNote(seatUrn2, entityUrn1);
    long noteId5 = createNote(seatUrn2, entityUrn2);
    List<Pair<NoteKey, Note>> result = await(_lssNoteDB.findBySeatAndEntity(seatUrn1, entityUrn1, 0, 10));
    assertThat(result.size()).isEqualTo(2);
    // noteId2 should belong to the more recent note
    Note note2 = result.get(0).getSecond();
    assertThat(note2.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(result.get(0).getFirst().getNoteId()).isEqualTo(noteId2);
    Note note1 = result.get(1).getSecond();
    assertThat(note1.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(result.get(1).getFirst().getNoteId()).isEqualTo(noteId1);
  }

  @Test
  public void findNotesEmpty() {
    List<Pair<NoteKey, Note>> result = await(_lssNoteDB.findBySeatAndEntity(seatUrn1, entityUrn1, 0, 10));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void findNotesBySeat() {
    long noteId1 = createNote(seatUrn1, entityUrn1);
    long noteId2 = createNote(seatUrn1, entityUrn1);//Created by the same seatUrn and entityUrn as noteId1
    long noteId3 = createNote(seatUrn1, entityUrn2);
    long noteId4 = createNote(seatUrn2, entityUrn1);
    long noteId5 = createNote(seatUrn2, entityUrn2);
    List<Pair<NoteKey, Note>> result = await(_lssNoteDB.findBySeat(seatUrn1, 0, 1000));
    assertThat(result.size()).isEqualTo(3);
    NoteKey noteKey1 = new NoteKey().setNoteId(noteId1).setSeat(seatUrn1).setEntity(entityUrn1);
    NoteKey noteKey2 = new NoteKey().setNoteId(noteId2).setSeat(seatUrn1).setEntity(entityUrn1);
    NoteKey noteKey3 = new NoteKey().setNoteId(noteId3).setSeat(seatUrn1).setEntity(entityUrn2);
    Set<NoteKey> noteKeySet = ImmutableSet.of(noteKey1, noteKey2, noteKey3);
    assertThat(result.stream().map(Pair::getFirst).collect(Collectors.toSet())).isEqualTo(noteKeySet);
  }

  @Test
  public void testGetNoteSucceeded() {
    Long noteId = createNote(seatUrn1, entityUrn1);
    NoteKey noteKey = new NoteKey().setNoteId(noteId).setSeat(seatUrn1).setEntity(entityUrn1);
    Note note = await(_lssNoteDB.getNote(noteKey));
    assertThat(note.body.text.toString()).isEqualTo(bodyText);
  }

  @Test
  public void testBatchGetNotesSucceeded() {
    Long noteId1 = createNote(seatUrn1, entityUrn1);
    Long noteId2 = createNote(seatUrn2, entityUrn2);
    NoteKey noteKey1 = new NoteKey().setNoteId(noteId1).setSeat(seatUrn1).setEntity(entityUrn1);
    NoteKey noteKey2 = new NoteKey().setNoteId(noteId2).setSeat(seatUrn2).setEntity(entityUrn2);
    List<Pair<NoteKey, Note>> notes = await(_lssNoteDB.batchGetNotes(ImmutableList.of(noteKey1, noteKey2)));
    Note note1 = notes.get(0).getSecond();
    Note note2 = notes.get(1).getSecond();
    assertThat(note1.body.text.toString()).isEqualTo(bodyText);
    assertThat(note2.body.text.toString()).isEqualTo(bodyText);
    assertThat(notes.size()).isEqualTo(2);
  }

  @Test
  public void testBatchGetNotesPartialFailure() {
    //Find noteKey2 will return failure since it is note created
    Long noteId1 = createNote(seatUrn1, entityUrn1);
    Long noteId2 = 10L;
    NoteKey noteKey1 = new NoteKey().setNoteId(noteId1).setSeat(seatUrn1).setEntity(entityUrn1);
    NoteKey noteKey2 = new NoteKey().setNoteId(noteId2).setSeat(seatUrn2).setEntity(entityUrn2);
    List<Pair<NoteKey, Note>> notes = await(_lssNoteDB.batchGetNotes(ImmutableList.of(noteKey1, noteKey2)));
    assertThat(notes.size()).isEqualTo(1);
    Note note1 = notes.get(0).getSecond();
    assertThat(note1.body.text.toString()).isEqualTo(bodyText);
  }

  @Test
  public void testConvertStringToAnnotatableEntityUrnMemberUrn() throws URISyntaxException {
    AnnotatableEntityUrn annotatableEntityUrn = _lssNoteDB.convertStringToAnnotatableEntityUrn(memberEspressoData);
    AnnotatableEntityUrn expected = new AnnotatableEntityUrn();
    expected.setMemberUrn(MemberUrn.deserialize(memberUrn));
    Assert.assertEquals(expected, annotatableEntityUrn);
  }

  @Test
  public void testConvertStringToAnnotatableEntityUrnOrganizationUrn() throws URISyntaxException {
    AnnotatableEntityUrn annotatableEntityUrn =
        _lssNoteDB.convertStringToAnnotatableEntityUrn(organizationEspressoData);
    AnnotatableEntityUrn expected = new AnnotatableEntityUrn();
    expected.setOrganizationUrn(OrganizationUrn.deserialize(organizationUrn));
    Assert.assertEquals(expected, annotatableEntityUrn);
  }

  @Test
  public void testConvertStringToAnnotatableEntityUrnSalesSharedSearchUrn() throws URISyntaxException {
    AnnotatableEntityUrn annotatableEntityUrn =
        _lssNoteDB.convertStringToAnnotatableEntityUrn(salesSharedSearchEspressoData);
    AnnotatableEntityUrn expected = new AnnotatableEntityUrn();
    expected.setSalesSharedSearchUrn(SalesSharedSearchUrn.deserialize(salesSharedSearchUrn));
    Assert.assertEquals(expected, annotatableEntityUrn);
  }

  @Test
  public void testConvertAnnotatableEntityUrnToGeneralUrn() throws URISyntaxException {
    AnnotatableEntityUrn entity1 = new AnnotatableEntityUrn();
    MemberUrn member = MemberUrn.deserialize(memberUrn);
    entity1.setMemberUrn(member);
    Assert.assertEquals(member, _lssNoteDB.convertAnnotatableEntityUrnToGeneralUrn(entity1));
    AnnotatableEntityUrn entity2 = new AnnotatableEntityUrn();
    OrganizationUrn organization = OrganizationUrn.deserialize(organizationUrn);
    entity2.setOrganizationUrn(organization);
    Assert.assertEquals(organization, _lssNoteDB.convertAnnotatableEntityUrnToGeneralUrn(entity2));
    AnnotatableEntityUrn entity3 = new AnnotatableEntityUrn();
    SalesSharedSearchUrn sharedSearch = SalesSharedSearchUrn.deserialize(salesSharedSearchUrn);
    entity3.setSalesSharedSearchUrn(sharedSearch);
    Assert.assertEquals(sharedSearch, _lssNoteDB.convertAnnotatableEntityUrnToGeneralUrn(entity3));
  }

  private long createNote(SeatUrn seatUrn, AnnotatableEntityUrn entityUrn) {
    long currentTime = System.currentTimeMillis();
    AttributedText body = new AttributedText();
    body.text = bodyText;
    body.attributes = Collections.emptyList();

    Note note = new Note();
    note.contractUrn = serializedContractUrn;
    note.createdTime = currentTime;
    note.lastModifiedTime = currentTime;
    note.body = body;
    return await(_lssNoteDB.createNote(seatUrn, entityUrn, note, RETENTION_DAYS));
  }
}