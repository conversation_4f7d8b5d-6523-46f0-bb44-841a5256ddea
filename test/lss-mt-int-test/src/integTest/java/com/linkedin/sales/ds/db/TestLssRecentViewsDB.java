package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.RecentSearchesV2;
import com.linkedin.sales.espresso.RecentViewEntity;
import com.linkedin.sales.espresso.RecentViews;
import com.linkedin.salesrecentactivities.RecentActivityType;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


public class TestLssRecentViewsDB extends IntegrationTestBase {

  private LssRecentViewsDB _lssRecentViewsDB;
  private static final String serializedSeatUrn = "urn:li:seat:123";
  private static final SeatUrn seatUrn = new SeatUrn(123L);
  private static final String contractUrn = "urn:li:contract:234";
  private static final String contractUrn2 = "urn:li:contract:345";
  private static final String profileEntityType = RecentActivityType.PROFILE.name();
  private static final String searchType = RecentActivityType.PEOPLE_SEARCH.name();
  private static final String peopleSearchQuery = "{query:{keywords:'test',facets:[1,2,3]}}";
  private static final String peopleSearchQuery2 = "{query:{keywords:'engineer',facets:[1,2]}}";
  private static final String profileEntityString = "urn:li:member:111";
  private static final String profileEntityString2 = "urn:li:member:222";

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssRecentViewsDB = new LssRecentViewsDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_VIEW, serializedSeatUrn, profileEntityType).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_SEARCHES, serializedSeatUrn, searchType).build()));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_VIEW, serializedSeatUrn, profileEntityType).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_SEARCHES, serializedSeatUrn, searchType).build()));
  }

  private boolean createRecentView() {
    long currentTime = System.currentTimeMillis();
    RecentViews recentViews = new RecentViews();
    RecentViewEntity recentViewEntity = new RecentViewEntity();
    recentViewEntity.entity = profileEntityString;
    recentViewEntity.lastViewedTime = currentTime;
    List<RecentViewEntity> list = new ArrayList<>();
    list.add(recentViewEntity);
    recentViews.contractUrn = contractUrn;
    recentViews.entities = list;
    return await(_lssRecentViewsDB.createRecentView(serializedSeatUrn, profileEntityType, recentViews));
  }

  private long createRecentSearch(String currentPeopleSearchQuery) {
    long currentTime = System.currentTimeMillis();
    RecentSearchesV2 espressoRecentSearch = new RecentSearchesV2();
    espressoRecentSearch.searchQuery = currentPeopleSearchQuery;
    espressoRecentSearch.lastSearchedTime = currentTime;
    espressoRecentSearch.contractUrn = contractUrn;
    return await(_lssRecentViewsDB.createRecentSearch(seatUrn, espressoRecentSearch, searchType));
  }

  @Test
  public void createRecentViewSucceeded() {
    boolean result = createRecentView();
    assertThat(result).isTrue();
  }

  @Test
  public void createRecentSearchSucceeded() {
    long id = createRecentSearch(peopleSearchQuery);
    assertThat(id).isPositive();
    RecentSearchesV2 resultRecentSearch = await(_lssRecentViewsDB.getRecentSearch(id, seatUrn, searchType));
    assertThat(resultRecentSearch.contractUrn).isEqualToIgnoringCase(contractUrn);
    assertThat(resultRecentSearch.searchQuery).isEqualToIgnoringCase(peopleSearchQuery);
  }

  @Test
  public void getRecentViewSucceeded() {
    createRecentView();
    RecentViews recentViews = await(_lssRecentViewsDB.getRecentViews(serializedSeatUrn, profileEntityType));
    assertThat(recentViews.contractUrn).isEqualToIgnoringCase(contractUrn);
    assertThat(recentViews.entities.get(0).entity).isEqualToIgnoringCase(profileEntityString);
  }

  @Test
  public void getRecentSearchSucceeded() {
    long id = createRecentSearch(peopleSearchQuery);
    RecentSearchesV2 recentSearch = await(_lssRecentViewsDB.getRecentSearch(id, seatUrn, searchType));
    assertThat(recentSearch.contractUrn).isEqualToIgnoringCase(contractUrn);
    assertThat(recentSearch.searchQuery).isEqualToIgnoringCase(peopleSearchQuery);
  }

  @Test
  public void deleteRecentViewSucceeded() {
    createRecentView();
    Boolean deleteRecentView = await(_lssRecentViewsDB.deleteRecentViews(serializedSeatUrn, profileEntityType));
    assertThat(deleteRecentView).isTrue();
  }

  @Test
  public void deleteRecentSearchSucceeded() {
    createRecentSearch(peopleSearchQuery);
    Boolean deleteRecentSearch = await(_lssRecentViewsDB.deleteRecentSearches(serializedSeatUrn, searchType));
    assertThat(deleteRecentSearch).isTrue();
  }

  @Test
  public void getRecentSearchesListSucceeded() {
    createRecentSearch(peopleSearchQuery);
    createRecentSearch(peopleSearchQuery2);
    List<Pair<Pair<Long, String>, RecentSearchesV2>> recentSearches = await(_lssRecentViewsDB.getRecentSearches(seatUrn,
        searchType, 0, 5));
    assertThat(recentSearches.size()).isEqualTo(2);
    // The first result is the most recent searchQuery
    assertThat(recentSearches.get(0).getSecond().searchQuery).isEqualToIgnoringCase(peopleSearchQuery2);
    // The second result is the second most recent searchQuery
    assertThat(recentSearches.get(1).getSecond().searchQuery).isEqualToIgnoringCase(peopleSearchQuery);
    }

  @Test
  public void updateRecentViewRecordSucceeded() {
    long uuid = UUID.randomUUID().getLeastSignificantBits();
    RecentViews recentViews = new RecentViews();
    RecentViewEntity recentViewEntity = new RecentViewEntity();
    createRecentView();
    long currentTime = System.currentTimeMillis();
    recentViewEntity.entity = profileEntityString2;
    recentViewEntity.lastViewedTime = currentTime;
    List<RecentViewEntity> list = new ArrayList<>();
    list.add(recentViewEntity);
    recentViews.contractUrn = contractUrn2;
    recentViews.entities = list;
    boolean result = await(_lssRecentViewsDB.updateRecentViews(serializedSeatUrn, profileEntityType, recentViews));
    RecentViews resultRecentViews = await(_lssRecentViewsDB.getRecentViews(serializedSeatUrn, profileEntityType));
    assertThat(result).isTrue();
    assertThat(resultRecentViews.contractUrn).isEqualToIgnoringCase(contractUrn2);
    assertThat(resultRecentViews.entities.size()).isEqualTo(1);
    assertThat(resultRecentViews.entities.get(0).entity).isEqualToIgnoringCase(profileEntityString2);
  }

  @Test
  public void updateRecentSearchSucceeded() {
    long id = createRecentSearch(peopleSearchQuery);
    long currentTime = System.currentTimeMillis();
    RecentSearchesV2 recentSearch = new RecentSearchesV2();
    recentSearch.contractUrn = contractUrn2;
    recentSearch.searchQuery = peopleSearchQuery2;
    recentSearch.lastSearchedTime = currentTime;
    long updateId = await(_lssRecentViewsDB.updateRecentSearch(seatUrn, recentSearch, searchType, id));
    assertThat(updateId).isEqualTo(id);
    RecentSearchesV2 resultRecentSearch = await(_lssRecentViewsDB.getRecentSearch(updateId, seatUrn, searchType));
    assertThat(resultRecentSearch.contractUrn).isEqualToIgnoringCase(contractUrn2);
    assertThat(resultRecentSearch.searchQuery).isEqualToIgnoringCase(peopleSearchQuery2);
  }

  @AfterClass
  public void tearDown() throws Exception {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_VIEW, serializedSeatUrn, profileEntityType).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssRecentViewsDB.DB_RECENT_VIEW, LssRecentViewsDB.TABLE_RECENT_SEARCHES, serializedSeatUrn, searchType).build()));
    super.tearDown();
  }
}
