package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableSet;
import com.linkedin.common.SortOrder;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.AccountDataSource;
import com.linkedin.sales.espresso.AccountToLeadAssociationView;
import com.linkedin.sales.espresso.LeadDataSource;
import com.linkedin.sales.espresso.LeadToAccountAssociation;
import com.linkedin.sales.espresso.SavedAccount;
import com.linkedin.sales.espresso.SavedLead;
import com.linkedin.salesleadaccount.SalesAccountFilter;
import com.linkedin.salesleadaccount.SalesAccountSortCriteria;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static com.linkedin.sales.service.utils.ServiceConstants.*;
import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;


public class TestLssSavedLeadAccountDB extends IntegrationTestBase {

  private LssSavedLeadAccountDB _lssSavedLeadAccountDB;
  private static final String serializedSeatUrn = "urn:li:seat:123";
  private static final SeatUrn seatUrn = new SeatUrn(123L);
  private static final ContractUrn contractUrn = new ContractUrn(123L);
  private static final Long leadMemberId1 = 1233L;
  private static final Long leadMemberId2 = 111L;
  private static final Long accountOrganizationId1 = 123L;
  private static final Long accountOrganizationId2 = 1234L;
  private static final Long accountOrganizationId3 = 12345L;
  private static final MemberUrn leadMemberUrn1 = new MemberUrn(leadMemberId1);
  private static final OrganizationUrn accountOrganizationUrn1;
  private static final OrganizationUrn accountOrganizationUrn2;

  static {
    try {
      accountOrganizationUrn1 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", accountOrganizationId1));
      accountOrganizationUrn2 =
          OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", accountOrganizationId2));
    } catch (URISyntaxException e) {
      throw new RuntimeException();
    }
  }

  private static final int PAUSE_MILLIS = 1000; // allow materialized view to get the change

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssSavedLeadAccountDB = new LssSavedLeadAccountDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
    //clean first
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_LEAD_TO_ACCOUNT_ASSOCIATION, serializedSeatUrn).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_SAVED_ACCOUNT, serializedSeatUrn).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_SAVED_LEAD, serializedSeatUrn).build()));
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_LEAD_TO_ACCOUNT_ASSOCIATION, serializedSeatUrn).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_SAVED_ACCOUNT, serializedSeatUrn).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSavedLeadAccountDB.DB_LSS_SAVED_LEAD_ACCOUNT,
            LssSavedLeadAccountDB.TABLE_SAVED_LEAD, serializedSeatUrn).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateLeadAccountAssociationSucceeded() throws URISyntaxException {
    HttpStatus creationStatus = createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    assertThat(creationStatus).isEqualByComparingTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateLeadAccountAssociationSucceededWhenListHasCreated() throws URISyntaxException {
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    HttpStatus creationStatus = createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    assertThat(creationStatus).isEqualByComparingTo(HttpStatus.S_412_PRECONDITION_FAILED);
  }

  @Test
  public void testDeleteLeadAccountAssociationSucceeded() throws URISyntaxException {
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    Boolean booleanResult = await(
        _lssSavedLeadAccountDB.deleteLeadAccountAssociation(accountOrganizationUrn1, leadMemberUrn1, seatUrn));
    assertThat(booleanResult).isTrue();
  }

  @Test
  public void testDeleteLeadAccountAssociationSucceededWhenEntityNoFound() {
    Boolean booleanResult = await(
        _lssSavedLeadAccountDB.deleteLeadAccountAssociation(accountOrganizationUrn1, leadMemberUrn1, seatUrn));
    assertThat(booleanResult).isFalse();
  }

  @Test
  public void testFindLeadAccountAssociationForGiveLead() throws URISyntaxException {
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    createLeadAccountAssociation(accountOrganizationId2, leadMemberId1);
    java.util.List<Pair<OrganizationUrn, LeadToAccountAssociation>> pairs =
    await(
        _lssSavedLeadAccountDB.getAccountsAssociatedWithGivenLead(leadMemberUrn1, seatUrn, 0, DEFAULT_COUNT));
    assertThat(pairs.size()).isEqualTo(2);
  }

  @Test
  public void testFindLeadAccountAssociationForGiveAccount() throws URISyntaxException, InterruptedException {
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId2);
    Thread.sleep(PAUSE_MILLIS);
    java.util.List<Pair<MemberUrn, AccountToLeadAssociationView>> pairs =
        await(
            _lssSavedLeadAccountDB.getLeadsAssociatedWithGivenAccount(accountOrganizationUrn1, seatUrn, 0, -1));
    assertThat(pairs.size()).isEqualTo(2);
  }

  @Test
  public void testGetAssociatedLeadCounts() throws  URISyntaxException, InterruptedException{
    OrganizationUrn organizationUrn1 = OrganizationUrn.createFromUrn(Urn.createFromString("urn:li:organization:123"));
    OrganizationUrn organizationUrn2 = OrganizationUrn.createFromUrn(Urn.createFromString("urn:li:organization:1234"));
    createLeadAccountAssociation(accountOrganizationId1, leadMemberId1);
    Thread.sleep(PAUSE_MILLIS);

    Map<OrganizationUrn, Integer> resultMap =
        await(_lssSavedLeadAccountDB.getAssociatedLeadCounts(ImmutableSet.of(organizationUrn1, organizationUrn2), seatUrn));
    assertThat(resultMap.size()).isEqualTo(2);
    assertThat(resultMap.containsKey(organizationUrn1)).isTrue();
    assertThat(resultMap.get(organizationUrn1)).isEqualTo(1);
    assertThat(resultMap.containsKey(organizationUrn2)).isTrue();
    assertThat(resultMap.get(organizationUrn2)).isEqualTo(0);
  }

  @Test
  public void testCreateSavedAccountSucceeded() throws URISyntaxException {
    HttpStatus result = createSavedAccount(accountOrganizationId1);
    assertThat(result).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateSavedAccountSucceededWithExistingResource() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    HttpStatus result = createSavedAccount(accountOrganizationId1);
    assertThat(result).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testDeleteSavedAccountSucceeded() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    HttpStatus result = await(_lssSavedLeadAccountDB.deleteSavedAccount(seatUrn, accountOrganizationUrn1));
    assertThat(result).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testCreateSavedAccountsWithMultiPutSucceeded() throws URISyntaxException {
    createSavedAccount(accountOrganizationId2);
    SavedAccount savedAccount1 = buildSavedAccount(contractUrn);
    SavedAccount savedAccount2 = buildSavedAccount(contractUrn);
    Map<OrganizationUrn, SavedAccount> map = new HashMap<>();
    map.put(accountOrganizationUrn1, savedAccount1);
    map.put(accountOrganizationUrn2, savedAccount2);

    Map<OrganizationUrn, HttpStatus> result = await(_lssSavedLeadAccountDB.createSavedAccounts(seatUrn, map));
    assertThat(result.size()).isEqualTo(2);
    assertThat(result.containsKey(accountOrganizationUrn1)).isTrue();
    assertThat(result.get(accountOrganizationUrn1)).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(result.containsKey(accountOrganizationUrn2)).isTrue();
    assertThat(result.get(accountOrganizationUrn2)).isEqualTo(HttpStatus.S_200_OK);
    deleteSavedAccount(accountOrganizationId2);
  }

  @Test
  public void testCreateLeadAccountAssoociationsWithMultiPutSucceeded() {
    LeadToAccountAssociation leadToAccountAssociation1 = buildLeadToAccountAssociation(contractUrn);
    LeadToAccountAssociation leadToAccountAssociation2 = buildLeadToAccountAssociation(contractUrn);
    Pair<MemberUrn, OrganizationUrn> pair1 = new Pair<>(leadMemberUrn1, accountOrganizationUrn1);
    Pair<MemberUrn, OrganizationUrn> pair2 = new Pair<>(leadMemberUrn1, accountOrganizationUrn2);
    Map<Pair<MemberUrn, OrganizationUrn>, LeadToAccountAssociation> map = new HashMap<>();
    map.put(pair1, leadToAccountAssociation1);
    map.put(pair2, leadToAccountAssociation2);
    Map<Pair<MemberUrn, OrganizationUrn>, HttpStatus> result = await(_lssSavedLeadAccountDB.createLeadAccountAssociations(map, seatUrn));
    assertThat(result.size()).isEqualTo(2);
    assertThat(result.containsKey(pair1)).isTrue();
    assertThat(result.get(pair1)).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(result.containsKey(pair2)).isTrue();
    assertThat(result.get(pair2)).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testDeleteSavedAccountWithEntityNotFound() {
    HttpStatus result = await(_lssSavedLeadAccountDB.deleteSavedAccount(seatUrn, accountOrganizationUrn1));
    assertThat(result).isEqualTo(HttpStatus.S_404_NOT_FOUND);
  }

  @Test
  public void testGetSavedAccountByGivenOwnerSucceeded() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    createSavedAccount(accountOrganizationId2);
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, null, 0, 1, null, null, null));
    assertThat(result.size()).isEqualTo(1);
  }

  @Test
  public void testGetSavedAccountByGivenOwnerAndOrgSucceeded() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, accountOrganizationUrn1, 0, 10, null, null, null));
    Pair<OrganizationUrn, SavedAccount> pair = result.get(0);
    assertThat(pair.getFirst().getIdAsLong()).isEqualTo(accountOrganizationId1);
    SavedAccount savedAccount = pair.getSecond();
    assertThat(savedAccount.contractUrn.toString()).isEqualTo(contractUrn.toString());
    assertThat(savedAccount.dataSource).isEqualTo(AccountDataSource.USER_GENERATED);
  }

  @Test
  public void testGetSavedAccountByOwnerNotFound() {
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, null, 0, 10, null, null, null));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void testGetSavedAccountByOwnerAndOrgNotFound() {
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, accountOrganizationUrn1, 0, 10, null, null, null));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void testGetSavedAccountCount() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    createSavedAccount(accountOrganizationId2);
    Integer result = await(_lssSavedLeadAccountDB.getSavedAccountCountForSeat(seatUrn));
    assertThat(result).isEqualTo(2);
  }

  @Test
  public void testCreateSavedLeadSuccess() {
    HttpStatus httpStatus = createSavedLead(leadMemberId1);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testCreateSavedLeadsWithMultiPutSucceeded() {
    createSavedLead(leadMemberId1);
    Map<MemberUrn, SavedLead> map = new HashMap<>();
    map.put(new MemberUrn(leadMemberId1), generateSavedLeadDocument());
    map.put(new MemberUrn(leadMemberId2), generateSavedLeadDocument());

    Map<MemberUrn, HttpStatus> result = await(_lssSavedLeadAccountDB.createSavedLeads(seatUrn, map));
    assertThat(result.size()).isEqualTo(2);
    assertThat(result.containsKey(new MemberUrn(leadMemberId1))).isTrue();
    assertThat(result.get(new MemberUrn(leadMemberId1))).isEqualTo(HttpStatus.S_200_OK);
    assertThat(result.containsKey(new MemberUrn(leadMemberId2))).isTrue();
    assertThat(result.get(new MemberUrn(leadMemberId2))).isEqualTo(HttpStatus.S_201_CREATED);
    deleteSavedLead(leadMemberId1);
  }

  @Test
  public void testCreateSavedLeadDuplicateKey() {
    createSavedLead(leadMemberId1);
    HttpStatus httpStatus = createSavedLead(leadMemberId1);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testDeleteSavedLeadSuccess() {
    createSavedLead(leadMemberId1);
    HttpStatus httpStatus = deleteSavedLead(leadMemberId1);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_204_NO_CONTENT);
  }

  @Test
  public void testDeleteSavedLeadNonExistence() {
    HttpStatus httpStatus = deleteSavedLead(leadMemberId1);
    assertThat(httpStatus).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testGetSavedLeadByOwnerSucceeded() {
    createSavedLead(leadMemberId1);
    createSavedLead(leadMemberId2);
    List<Pair<MemberUrn, SavedLead>>
        result = await(_lssSavedLeadAccountDB.getSavedLeads(seatUrn, null, 0, 1));
    assertThat(result.size()).isEqualTo(1);
  }

  @Test
  public void testGetSavedLeadByOwnerNotFound() {
    List<Pair<MemberUrn, SavedLead>>
        result = await(_lssSavedLeadAccountDB.getSavedLeads(seatUrn, null, 0, 10));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void testGetSavedLeadByOwnerAndLeadSucceeded() {
    createSavedLead(leadMemberId1);
    List<Pair<MemberUrn, SavedLead>>
        result = await(_lssSavedLeadAccountDB.getSavedLeads(seatUrn, leadMemberUrn1, 0, 10));
    assertThat(result.size()).isEqualTo(1);
    Pair<MemberUrn, SavedLead> pair = result.get(0);
    assertThat(pair.getFirst().getIdAsLong()).isEqualTo(leadMemberId1);
    SavedLead savedLead = pair.getSecond();
    assertThat(savedLead.contractUrn.toString()).isEqualTo(contractUrn.toString());
    assertThat(savedLead.dataSource).isEqualTo(LeadDataSource.USER_GENERATED);
  }

  @Test
  public void testGetSavedLeadByOwnerAndLeadNotFound() throws URISyntaxException {
    List<Pair<MemberUrn, SavedLead>>
        result = await(_lssSavedLeadAccountDB.getSavedLeads(seatUrn, leadMemberUrn1, 0, 10));
    assertThat(result.size()).isEqualTo(0);
  }

  @Test
  public void testGetSavedLeadCount() {
    createSavedLead(leadMemberId1);
    createSavedLead(leadMemberId2);
    Integer result = await(_lssSavedLeadAccountDB.getSavedLeadCountForSeat(seatUrn));
    assertThat(result).isEqualTo(2);
  }

  @Test
  public void testGetStarredAccount() throws URISyntaxException {
    createStarredAccount(accountOrganizationId1, true, 222L);
    createStarredAccount(accountOrganizationId2, true, 111L);
    createStarredAccount(accountOrganizationId3, false, 333L);
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, null, 0, 10, SalesAccountFilter.STAR_ONLY, SalesAccountSortCriteria.STAR_LAST_MODIFIED, SortOrder.DESCENDING));
    assertThat(result.size()).isEqualTo(2);
    Pair<OrganizationUrn, SavedAccount> pair = result.get(0);
    assertThat(pair.getFirst().getIdAsLong()).isEqualTo(accountOrganizationId1);
    SavedAccount savedAccount = pair.getSecond();
    assertThat(savedAccount.starred).isEqualTo(true);
  }

  @Test
  public void testGetUnStarredAccount() throws URISyntaxException {
    createStarredAccount(accountOrganizationId1, true, 222L);
    createStarredAccount(accountOrganizationId2, true, 111L);
    createStarredAccount(accountOrganizationId3, false, 333L);
    List<Pair<OrganizationUrn, SavedAccount>>
        result = await(_lssSavedLeadAccountDB.getSavedAccounts(seatUrn, null, 0, 10, SalesAccountFilter.UN_STAR_ONLY, SalesAccountSortCriteria.CREATED, SortOrder.DESCENDING));
    assertThat(result.size()).isEqualTo(1);
    Pair<OrganizationUrn, SavedAccount> pair = result.get(0);
    assertThat(pair.getFirst().getIdAsLong()).isEqualTo(accountOrganizationId3);
  }

  @Test
  public void testStarAccountSuccess() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    SavedAccount patch = new SavedAccount();
    patch.starred = true;
    patch.starLastModifiedTime = 111L;
    Boolean result = updateSavedAccount(accountOrganizationId1, patch);
    assertThat(result).isEqualTo(true);
  }

  @Test
  public void testUnStarAccountSuccess() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    SavedAccount patch = new SavedAccount();
    patch.starred = false;
    patch.starLastModifiedTime = 222L;
    Boolean result = updateSavedAccount(accountOrganizationId1, patch);
    assertThat(result).isEqualTo(true);
  }

  @Test
  public void testStarAccountNotFound() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    SavedAccount patch = new SavedAccount();
    patch.starred = true;
    patch.starLastModifiedTime = 111L;
    Boolean result = updateSavedAccount(accountOrganizationId2, patch);
    assertThat(result).isEqualTo(false);
  }

  @Test
  public void testStarAccountNoChange() throws URISyntaxException {
    createSavedAccount(accountOrganizationId1);
    SavedAccount patch = new SavedAccount();
    Boolean result = updateSavedAccount(accountOrganizationId1, patch);
    assertThat(result).isEqualTo(true);
  }

  private LeadToAccountAssociation generateLeadToAccountAssociationDocument() {
    LeadToAccountAssociation leadToAccountAssociation = new LeadToAccountAssociation();
    leadToAccountAssociation.contractUrn = contractUrn.toString();
    leadToAccountAssociation.createdTime = System.currentTimeMillis();
    leadToAccountAssociation.lastModifiedTime = System.currentTimeMillis();
    return leadToAccountAssociation;
  }

  private SavedLead generateSavedLeadDocument() {
    SavedLead savedLead = new SavedLead();
    savedLead.contractUrn = contractUrn.toString();
    savedLead.createdTime = System.currentTimeMillis();
    savedLead.dataSource = LeadDataSource.USER_GENERATED;
    return savedLead;
  }

  private HttpStatus createLeadAccountAssociation(Long organizationId, Long memberId) throws URISyntaxException {
    OrganizationUrn organizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", organizationId));
    return await(
        _lssSavedLeadAccountDB.createLeadAccountAssociation(organizationUrn, new MemberUrn(memberId), seatUrn,
            generateLeadToAccountAssociationDocument()));
  }

  private HttpStatus createSavedLead(Long memberId){
    return await(
        _lssSavedLeadAccountDB.createSavedLead(seatUrn, new MemberUrn(memberId), generateSavedLeadDocument()));
  }

  private HttpStatus deleteSavedLead(Long memberId){
    return await(
        _lssSavedLeadAccountDB.deleteSavedLead(seatUrn, new MemberUrn(memberId)));
  }

  private HttpStatus createSavedAccount(Long organizationId) throws URISyntaxException {
    SavedAccount savedAccount = buildSavedAccount(contractUrn);
    OrganizationUrn organizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", organizationId));

    return await(_lssSavedLeadAccountDB.createSavedAccount(seatUrn, organizationUrn, savedAccount));
  }

  private HttpStatus createStarredAccount(Long organizationId, boolean starred, Long starLastModifiedTime) throws URISyntaxException {
    SavedAccount savedAccount = buildStarredAccount(starred, starLastModifiedTime);
    OrganizationUrn organizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", organizationId));

    return await(_lssSavedLeadAccountDB.createSavedAccount(seatUrn, organizationUrn, savedAccount));
  }

  private Boolean updateSavedAccount(Long organizationId, SavedAccount savedAccount) throws URISyntaxException {
    OrganizationUrn organizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", organizationId));
    return await(_lssSavedLeadAccountDB.updateSavedAccount(seatUrn, organizationUrn, savedAccount));
  }

  private HttpStatus deleteSavedAccount(Long organizationId) throws URISyntaxException {
    OrganizationUrn organizationUrn =
        OrganizationUrn.createFromUrn(Urn.createFromTuple("organization", organizationId));

    return await(_lssSavedLeadAccountDB.deleteSavedAccount(seatUrn, organizationUrn));
  }

  private SavedAccount buildSavedAccount(ContractUrn contractUrn) {
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = contractUrn.toString();
    savedAccount.createdTime = System.currentTimeMillis();
    savedAccount.dataSource = AccountDataSource.USER_GENERATED;

    return savedAccount;
  }

  private SavedAccount buildStarredAccount(boolean starred, Long starModifiedTime) {
    SavedAccount savedAccount = new SavedAccount();
    savedAccount.contractUrn = contractUrn.toString();
    savedAccount.createdTime = System.currentTimeMillis();
    savedAccount.dataSource = AccountDataSource.USER_GENERATED;
    savedAccount.starred = starred;
    savedAccount.starLastModifiedTime = starModifiedTime;

    return savedAccount;
  }

  private SavedLead buildSavedLead(ContractUrn contractUrn) {
    SavedLead savedLead = new SavedLead();
    savedLead.contractUrn = contractUrn.toString();
    savedLead.createdTime = System.currentTimeMillis();
    savedLead.dataSource = LeadDataSource.USER_GENERATED;

    return savedLead;
  }

  private LeadToAccountAssociation buildLeadToAccountAssociation(ContractUrn contractUrn) {
    LeadToAccountAssociation leadToAccountAssociation = new LeadToAccountAssociation();
    leadToAccountAssociation.contractUrn = contractUrn.toString();
    leadToAccountAssociation.createdTime = System.currentTimeMillis();
    leadToAccountAssociation.lastModifiedTime = 111L;
    return leadToAccountAssociation;
  }
}