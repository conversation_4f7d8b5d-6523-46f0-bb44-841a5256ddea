package com.linkedin.sales.ds.db;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.AIMessageType;
import com.linkedin.sales.espresso.AIMessagingPreferences;
import com.linkedin.sales.espresso.AccountDashboardColumnSettings;
import com.linkedin.sales.espresso.AccountDashboardSettings;
import com.linkedin.sales.espresso.CalendarSyncSettings;
import com.linkedin.sales.espresso.CrmAutoSaveSettingType;
import com.linkedin.sales.espresso.CrmAutoSaveSettings;
import com.linkedin.sales.espresso.MobileSettings;
import com.linkedin.sales.espresso.SeatSetting;
import com.linkedin.sales.espresso.VisibilityAsProfileViewer;
import java.util.ArrayList;
import java.util.Map;
import java.util.Set;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static java.lang.Boolean.*;
import static org.assertj.core.api.Assertions.*;


public class TestLssSeatSettingDB extends IntegrationTestBase {

  private LssSeatSettingDB _lssSeatSettingDB;
  private static final String serializedSeatUrn = "urn:li:seat:123";
  private static final SeatUrn seatUrn1 = new SeatUrn(123L);
  private static final SeatUrn seatUrn2 = new SeatUrn(124L);
  private static final SeatUrn seatUrn3 = new SeatUrn(125L);
  private static final SeatUrn seatUrn4 = new SeatUrn(126L);
  private static final String serializedContractUrn = "urn:li:contract:234";
  private static final ContractUrn contractUrn = new ContractUrn(234L);
  private static final String signatureText = "Best, XX";
  private static final String attr1 = "attr1";
  private static final String attr2 = "attr2";
  private static final long onboardingCompleteTime = 1L;
  private static final long lastLoginTime = 1L;
  private static final long dismissedBuyerIntentCardTime = 987654321987654321L;
  private static final long dismissedAccountIqCardTime = 987654321987654411L;
  private static final String lastProductUrn = "urn:li:fs_salesProduct:123";
  private static final long LAST_VIEWED_LIST_ID = 1000L;
  private static final String COLUMN1 = "BUYER_INTENT";
  private static final String COLUMN2 = "NOTES";

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssSeatSettingDB = new LssSeatSettingDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssSeatSettingDB.DB_LSS_SEAT_SETTING, LssSeatSettingDB.TABLE_SEAT_SETTING, serializedSeatUrn).build()));
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
            LssSeatSettingDB.DB_LSS_SEAT_SETTING, LssSeatSettingDB.TABLE_MOBILE_SETTINGS, serializedSeatUrn).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testCreateAndGetSeatSettingSucceed() {
    createSeatSetting();
    SeatSetting setting = await(_lssSeatSettingDB.getSeatSetting(seatUrn1));
    assertThat(setting.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(setting.visibilityAsProfileViewer).isEqualTo(null);
    assertThat(setting.pushNotificationEnabled).isEqualTo(null);
    assertThat(setting.emailPreferences.get(attr1)).isEqualTo(TRUE);
    assertThat(setting.emailPreferences.get(attr2)).isEqualTo(FALSE);
    assertThat(setting.emailPreferences.size()).isEqualTo(2);
    assertThat(setting.inMailMessageSignature.toString()).isEqualTo(signatureText);
    assertThat(setting.usageReportingFlagshipDataOptOut).isEqualTo(null);
    assertThat(setting.teamlinkOptedOut).isEqualTo(null);
    assertThat(setting.messageLearningEnabled).isEqualTo(null);
    assertThat(setting.onboardingCompletedTime).isEqualTo(null);
    assertThat(setting.lastLoggedInTime).isEqualTo(null);
    assertThat(setting.dismissedBuyerIntentCardTime).isEqualTo(null);
    assertThat(setting.dismissedAccountIqCardTime).isEqualTo(null);
    assertThat(setting.aiMessagingPreferences).isEqualTo(null);
    assertThat(setting.accountDashboardSettings).isEqualTo(null);
  }

  @Test
  public void testUpdateAndGetSeatSettingSucceed() {
    createSeatSetting();
    SeatSetting update = new SeatSetting();
    update.contractUrn = serializedContractUrn;
    update.teamlinkOptedOut = TRUE;
    update.visibilityAsProfileViewer = VisibilityAsProfileViewer.FULL;
    update.emailPreferences = ImmutableMap.of(attr1, FALSE);
    update.onboardingCompletedTime = onboardingCompleteTime;
    update.lastLoggedInTime = lastLoginTime;
    update.dismissedBuyerIntentCardTime = dismissedBuyerIntentCardTime;
    update.dismissedAccountIqCardTime = dismissedAccountIqCardTime;
    update.messageLearningEnabled = TRUE;
    AIMessagingPreferences aiMessagingPreferences = new AIMessagingPreferences();
    aiMessagingPreferences.setLastUsedProductUrn(lastProductUrn);
    aiMessagingPreferences.setLastUsedMessageType(AIMessageType.SALES);
    update.aiMessagingPreferences = aiMessagingPreferences;
    AccountDashboardSettings accountDashboardSettings = new AccountDashboardSettings();
    accountDashboardSettings.setLastViewedList(LAST_VIEWED_LIST_ID);
    ArrayList<AccountDashboardColumnSettings> columnSettings = new ArrayList<>();
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN1, true));
    columnSettings.add(new AccountDashboardColumnSettings(COLUMN2, false));
    accountDashboardSettings.setColumns(columnSettings);
    update.accountDashboardSettings = accountDashboardSettings;

    HttpStatus status = await(_lssSeatSettingDB.updateSeatSetting(seatUrn1, update));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);

    SeatSetting setting = await(_lssSeatSettingDB.getSeatSetting(seatUrn1));
    assertThat(setting.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(setting.visibilityAsProfileViewer).isEqualTo(VisibilityAsProfileViewer.FULL);
    assertThat(setting.pushNotificationEnabled).isEqualTo(null);
    assertThat(setting.emailPreferences.get(attr1)).isEqualTo(FALSE);
    assertThat(setting.emailPreferences.size()).isEqualTo(1);
    assertThat(setting.inMailMessageSignature.toString()).isEqualTo(signatureText);
    assertThat(setting.usageReportingFlagshipDataOptOut).isEqualTo(null);
    assertThat(setting.teamlinkOptedOut).isEqualTo(TRUE);
    assertThat(setting.onboardingCompletedTime).isEqualTo(onboardingCompleteTime);
    assertThat(setting.lastLoggedInTime).isEqualTo(lastLoginTime);
    assertThat(setting.dismissedBuyerIntentCardTime).isEqualTo(dismissedBuyerIntentCardTime);
    assertThat(setting.dismissedAccountIqCardTime).isEqualTo(dismissedAccountIqCardTime);
    assertThat(setting.aiMessagingPreferences.lastUsedProductUrn.toString()).isEqualTo(lastProductUrn);
    assertThat(setting.aiMessagingPreferences.lastUsedMessageType.toString()).isEqualTo(AIMessageType.SALES.name());
    assertThat(setting.accountDashboardSettings.lastViewedList).isEqualTo(LAST_VIEWED_LIST_ID);
    assertThat(setting.accountDashboardSettings.columns).isEqualTo(columnSettings);
    assertThat(setting.messageLearningEnabled).isEqualTo(TRUE);
  }

  @Test
  public void testBatchGetSeatSettingSucceed() {
    createMultipleSeatSetting();
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(seatUrn1, seatUrn2);
    Map<SeatUrn, SeatSetting> seatSettingsMap = await(_lssSeatSettingDB.batchGetSeatSettings(seatUrnSet));
    assertThat(seatSettingsMap.size()).isEqualTo(2);
    for(SeatUrn seatUrn: seatUrnSet){
      assertThat(seatSettingsMap.containsKey(seatUrn)).isTrue();
      assertThat(seatSettingsMap.get(seatUrn).onboardingCompletedTime).isEqualTo(onboardingCompleteTime);
      assertThat(seatSettingsMap.get(seatUrn).emailPreferences.get(attr1)).isEqualTo(TRUE);
      assertThat(seatSettingsMap.get(seatUrn).emailPreferences.size()).isEqualTo(2);
      assertThat(seatSettingsMap.get(seatUrn).crmAutoSavePreferences.size()).isEqualTo(2);

    }
  }

  @Test
  public void testBatchGetSeatSettingOneFound() {
    createSeatSetting();
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(seatUrn1, seatUrn3);
    Map<SeatUrn, SeatSetting> seatSettingsMap = await(_lssSeatSettingDB.batchGetSeatSettings(seatUrnSet));
    assertThat(seatSettingsMap.size()).isEqualTo(1);
    assertThat(seatSettingsMap.containsKey(seatUrn1)).isEqualTo(true);
    assertThat(seatSettingsMap.get(seatUrn1).inMailMessageSignature.toString()).isEqualTo(signatureText);
  }

  @Test
  public void testBatchGetSeatSettingNoneFound() {
    Set<SeatUrn> seatUrnSet = ImmutableSet.of(seatUrn3, seatUrn4);
    Map<SeatUrn, SeatSetting> seatSettingsMap = await(_lssSeatSettingDB.batchGetSeatSettings(seatUrnSet));
    assertThat(seatSettingsMap.size()).isEqualTo(0);
  }

  @Test
  public void testGetSeatSettingFailNotFound() {
    createSeatSetting();
    assertThatExceptionOfType(PromiseException.class)
        .isThrownBy(() -> {await(_lssSeatSettingDB.getSeatSetting(seatUrn3));})
        .withCauseInstanceOf(EntityNotFoundException.class);
  }

  private void createSeatSetting() {
    SeatSetting setting = new SeatSetting();
    setting.contractUrn = serializedContractUrn;
    setting.emailPreferences = ImmutableMap.of(attr1, TRUE, attr2, FALSE);
    ArrayList<CrmAutoSaveSettings> list = new ArrayList<>();
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED, true));
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_OWNED_ACCOUNTS_ENABLED, false));
    setting.setCrmAutoSavePreferences(list);
    setting.inMailMessageSignature = signatureText;
    HttpStatus status = await(_lssSeatSettingDB.updateSeatSetting(seatUrn1, setting));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
  }

  private void createMultipleSeatSetting() {
    SeatSetting setting = new SeatSetting();
    setting.contractUrn = serializedContractUrn;
    setting.emailPreferences = ImmutableMap.of(attr1, TRUE, attr2, FALSE);
    ArrayList<CrmAutoSaveSettings> list = new ArrayList<>();
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_ACCOUNTS_WITH_ACCOUNT_TEAMS_ENABLED, true));
    list.add(new CrmAutoSaveSettings(CrmAutoSaveSettingType.AUTO_SAVE_OWNED_ACCOUNTS_ENABLED, false));
    setting.setCrmAutoSavePreferences(list);
    setting.inMailMessageSignature = signatureText;
    setting.onboardingCompletedTime = onboardingCompleteTime;
    AIMessagingPreferences aiMessagingPreferences = new AIMessagingPreferences();
    aiMessagingPreferences.lastUsedProductUrn = lastProductUrn;
    aiMessagingPreferences.lastUsedMessageType = AIMessageType.SALES;
    setting.aiMessagingPreferences = aiMessagingPreferences;
    HttpStatus status1 = await(_lssSeatSettingDB.updateSeatSetting(seatUrn1, setting));
    HttpStatus status2 = await(_lssSeatSettingDB.updateSeatSetting(seatUrn2, setting));

    assertThat(status1).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(status2).isEqualTo(HttpStatus.S_201_CREATED);

  }

  @Test
  public void testCreateAndGetMobileSettingSucceed() {
    createMobileSetting();
    MobileSettings setting = await(_lssSeatSettingDB.getMobileSetting(seatUrn1));
    assertThat(setting.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(setting.calendarSyncSettings).isEqualTo(null);
    assertThat(setting.isCallLoggingEnabled).isEqualTo(FALSE);
    assertThat(setting.rateTheAppLastShowAt).isEqualTo(1234L);
  }

  @Test
  public void testUpdateAndGetMobileSettingSucceed() {
    createMobileSetting();
    MobileSettings update = new MobileSettings();
    update.contractUrn = serializedContractUrn;
    update.calendarSyncSettings = new CalendarSyncSettings();
    update.calendarSyncSettings.isEnabled = TRUE;
    update.calendarSyncSettings.isAllEventsForTodayShown = FALSE;
    update.calendarSyncSettings.isEventWithoutAttendeesShown = TRUE;
    update.calendarSyncSettings.syncedCalendars = ImmutableList.of("asdasd", "fsddfd");
    update.isCallLoggingEnabled = null;
    update.rateTheAppLastShowAt = 2345L;
    HttpStatus status = await(_lssSeatSettingDB.updateMobileSetting(seatUrn1, update));
    assertThat(status).isEqualTo(HttpStatus.S_200_OK);

    MobileSettings setting = await(_lssSeatSettingDB.getMobileSetting(seatUrn1));
    assertThat(setting.contractUrn.toString()).isEqualTo(serializedContractUrn);
    assertThat(setting.calendarSyncSettings.isEnabled).isEqualTo(TRUE);
    assertThat(setting.calendarSyncSettings.isAllEventsForTodayShown).isEqualTo(FALSE);
    assertThat(setting.calendarSyncSettings.isEventWithoutAttendeesShown).isEqualTo(TRUE);
    assertThat(setting.calendarSyncSettings.syncedCalendars.get(0).toString()).isEqualTo("asdasd");
    assertThat(setting.calendarSyncSettings.syncedCalendars.get(1).toString()).isEqualTo("fsddfd");
    assertThat(setting.isCallLoggingEnabled).isEqualTo(FALSE);
    assertThat(setting.rateTheAppLastShowAt).isEqualTo(2345L);
  }

  @Test
  public void testGetMobileSettingFailNotFound() {
    createSeatSetting();
    assertThatExceptionOfType(PromiseException.class)
            .isThrownBy(() -> {await(_lssSeatSettingDB.getSeatSetting(seatUrn3));})
            .withCauseInstanceOf(EntityNotFoundException.class);
  }

  private void createMobileSetting() {
    MobileSettings setting = new MobileSettings();
    setting.contractUrn = serializedContractUrn;
    setting.calendarSyncSettings = null;
    setting.isCallLoggingEnabled = FALSE;
    setting.rateTheAppLastShowAt = 1234L;
    HttpStatus status = await(_lssSeatSettingDB.updateMobileSetting(seatUrn1, setting));
    assertThat(status).isEqualTo(HttpStatus.S_201_CREATED);
  }
}