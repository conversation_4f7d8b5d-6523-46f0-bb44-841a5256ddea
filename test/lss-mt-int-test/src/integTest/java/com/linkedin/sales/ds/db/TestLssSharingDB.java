package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.EnterpriseAccountUrn;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseProfileApplicationInstanceUrn;
import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.ContractUrn;
import com.linkedin.common.urn.OrganizationUrn;
import com.linkedin.common.urn.SalesInsightsMetricsReportUrn;
import com.linkedin.common.urn.SalesListUrn;
import com.linkedin.common.urn.SalesNoteUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.espresso.common.util.Pair;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.promise.PromiseException;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.ds.db.exception.EntityNotFoundException;
import com.linkedin.sales.espresso.ShareRole;
import com.linkedin.sales.espresso.SubjectPolicy;
import com.linkedin.salessharing.Policy;
import com.linkedin.salessharing.PolicyKey;
import com.linkedin.salessharing.PolicyType;
import com.linkedin.util.collections.list.PaginatedList;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.URISyntaxException;
import java.util.Collections;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import static org.assertj.core.api.Assertions.*;


/**
 * Created by aewong
 * Test class for {@link LssSharingDB}
 */
public class TestLssSharingDB extends IntegrationTestBase {

  private static final long SEAT_ID = 200L;
  private static final long SEAT_ID2 = 201L;
  private static final long CONTRACT_ID = 100L;
  private static final long SALES_LIST_ID = 3000L;
  private static final long MEMBER_ID1 = 40L;
  private static final long MEMBER_ID2 = 50L;
  private static final long NOTE_ID = 55L;
  private static final long METRICS_REPORT_ID = 100L;
  private static final long ENTERPRISE_ACCOUNT_ID = 2000L;
  private static final long ENTERPRISE_APPLICATION_INSTANCE_ID = 2000L;
  private static final long ENTERPRISE_PROFILE_ID = 1L;
  private static final long ENTERPRISE_PROFILE_ID2 = 2L;
  private static final SeatUrn SEAT_URN = new SeatUrn(SEAT_ID);
  private static final SeatUrn SEAT_URN2 = new SeatUrn(SEAT_ID2);
  private static final ContractUrn CONTRACT_URN = new ContractUrn(CONTRACT_ID);
  private static final MemberUrn MEMBER_URN = new MemberUrn(301L);
  private static final Policy.ResourceContext RESOURCE_CONTEXT = new Policy.ResourceContext();
  private static final Urn RESOURCE_URN = Urn.createFromTuple(SalesListUrn.ENTITY_TYPE, SALES_LIST_ID);
  private static final Urn NOTE_URN = Urn.createFromTuple(SalesNoteUrn.ENTITY_TYPE, SEAT_ID, MEMBER_ID2, NOTE_ID);
  private static final Urn METRICS_REPORT_URN =
      Urn.createFromTuple(SalesInsightsMetricsReportUrn.ENTITY_TYPE, METRICS_REPORT_ID);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID);
  private static final Urn ENTERPRISE_PROFILE_INSTANCE_URN2 =
      Urn.createFromTuple(EnterpriseProfileApplicationInstanceUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID,
          ENTERPRISE_APPLICATION_INSTANCE_ID, ENTERPRISE_PROFILE_ID2);
  private static final Urn ENTERPRISE_APPLICATION_INSTANCE_URN =
      Urn.createFromTuple(EnterpriseApplicationInstanceUrn.ENTITY_TYPE,
          Urn.createFromTuple(EnterpriseAccountUrn.ENTITY_TYPE, ENTERPRISE_ACCOUNT_ID), ENTERPRISE_APPLICATION_INSTANCE_ID);
  private static final PolicyKey DEFAULT_KEY = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
      .setResource(RESOURCE_URN)
      .setSubject(SEAT_URN);
  private static final PolicyKey ACCOUNT_MAP_POLICY_KEY_1 = new PolicyKey().setPolicyType(PolicyType.ACCOUNT_MAP)
      .setResource(RESOURCE_URN)
      .setSubject(SEAT_URN);
  private static final PolicyKey ACCOUNT_MAP_POLICY_KEY_2 = new PolicyKey().setPolicyType(PolicyType.ACCOUNT_MAP)
      .setResource(RESOURCE_URN)
      .setSubject(SEAT_URN2);
  private static final PolicyKey NOTE_SHARE_WITHIN_CONTRACT_KEY = new PolicyKey().setPolicyType(PolicyType.NOTE )
      .setResource(NOTE_URN)
      .setSubject(CONTRACT_URN);
  private static final PolicyKey NOTE_SHARE_WITH_SEAT_KEY = new PolicyKey().setPolicyType(PolicyType.NOTE)
      .setResource(NOTE_URN)
      .setSubject(SEAT_URN);
  private static final PolicyKey LSI_METRICS_REPORT_SHARE_WITH_ENTERPRISE_APP_INSTANCE_KEY =
      new PolicyKey().setPolicyType(PolicyType.LSI_METRICS_REPORT)
      .setResource(METRICS_REPORT_URN)
      .setSubject(ENTERPRISE_APPLICATION_INSTANCE_URN);
  private static final PolicyKey LSI_METRICS_REPORT_SHARE_WITH_ENTERPRISE_PROFILE_APP_INSTANCE_KEY =
      new PolicyKey().setPolicyType(PolicyType.LSI_METRICS_REPORT)
      .setResource(METRICS_REPORT_URN)
      .setSubject(ENTERPRISE_PROFILE_INSTANCE_URN);

  private static final SubjectPolicy DEFAULT_SUBJECT_POLICY;

  static {
    DEFAULT_SUBJECT_POLICY = new SubjectPolicy();
    DEFAULT_SUBJECT_POLICY.role = ShareRole.READER;
    DEFAULT_SUBJECT_POLICY.contractUrn = CONTRACT_URN.toString();
    RESOURCE_CONTEXT.setMember(MEMBER_URN);
    DEFAULT_SUBJECT_POLICY.resourceContext = RESOURCE_CONTEXT.toString();
  }

  private LssSharingDB _lssSharingDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssSharingDB = new LssSharingDB(_parSeqEspressoClient);

    _offspringTestHelper.startGenerator();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSharingDB.DB_LSS_SHARING, LssSharingDB.TABLE_SUBJECT_POLICY, String.valueOf(SEAT_URN)).build()));
    await(_parSeqEspressoClient.execute(
        DeleteRequest.builder(LssSharingDB.DB_LSS_SHARING, LssSharingDB.TABLE_SUBJECT_POLICY, String.valueOf(SEAT_URN2)).build()));
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @Test
  public void testAddAndUpdateSubjectPolicySucceed() {
    HttpStatus success = createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    assertThat(success).isEqualTo(HttpStatus.S_201_CREATED);

    SubjectPolicy subjectPolicy2 = new SubjectPolicy();
    subjectPolicy2.role = ShareRole.WRITER;
    subjectPolicy2.contractUrn = new ContractUrn(CONTRACT_ID).toString();
    HttpStatus success2 = createSubjectPolicy(DEFAULT_KEY, subjectPolicy2);
    assertThat(success2).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testAddAndUpdatePartialSubjectPolicySucceed() {
    HttpStatus success = createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    assertThat(success).isEqualTo(HttpStatus.S_201_CREATED);

    SubjectPolicy subjectPolicy2 = new SubjectPolicy();
    subjectPolicy2.role = ShareRole.WRITER;
    HttpStatus success2 = createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    assertThat(success2).isEqualTo(HttpStatus.S_200_OK);
  }

  @Test
  public void testAddAndUpdateEmptySubjectPolicyFail() {
    HttpStatus success = createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    assertThat(success).isEqualTo(HttpStatus.S_201_CREATED);

    SubjectPolicy subjectPolicy2 = new SubjectPolicy();
    HttpStatus success2 = createSubjectPolicy(DEFAULT_KEY, subjectPolicy2);
    assertThat(success2).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testAddPartialSubjectPolicySucceed() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.READER;
    HttpStatus success = createSubjectPolicy(DEFAULT_KEY, subjectPolicy);
    assertThat(success).isEqualTo(HttpStatus.S_201_CREATED);
  }

  @Test
  public void testAddEmptySubjectPolicyFail() {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    HttpStatus responseCode = createSubjectPolicy(DEFAULT_KEY, subjectPolicy);
    assertThat(responseCode).isEqualTo(HttpStatus.S_400_BAD_REQUEST);
  }

  @Test
  public void testDeleteWithSuccess() {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    boolean isSuccessful = await(_lssSharingDB.deleteSubjectPolicy(DEFAULT_KEY));
    assertThat(isSuccessful).isEqualTo(true);
    // Also return success when deleting an already deleted entity
    isSuccessful = await(_lssSharingDB.deleteSubjectPolicy(DEFAULT_KEY));
    assertThat(isSuccessful).isEqualTo(true);
  }

  @Test
  public void testGetSubjectPolicySucceed() {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);

    SubjectPolicy subjectPolicy = await(_lssSharingDB.getSubjectPolicy(SEAT_URN, PolicyType.LEAD_LIST.toString(), RESOURCE_URN));
    assertThat(subjectPolicy.contractUrn.toString()).isEqualTo(new ContractUrn(CONTRACT_ID).toString());
    assertThat(subjectPolicy.role).isEqualTo(ShareRole.READER);
  }

  @Test
  public void testGetSubjectPolicySucceedWithoutContract() {
    SubjectPolicy testSubjectPolicy = new SubjectPolicy();
    testSubjectPolicy.role = ShareRole.OWNER;

    createSubjectPolicy(LSI_METRICS_REPORT_SHARE_WITH_ENTERPRISE_PROFILE_APP_INSTANCE_KEY, testSubjectPolicy);

    SubjectPolicy subjectPolicy = await(_lssSharingDB.getSubjectPolicy(ENTERPRISE_PROFILE_INSTANCE_URN,
        PolicyType.LSI_METRICS_REPORT.toString(), METRICS_REPORT_URN));
    assertThat(subjectPolicy.role).isEqualTo(ShareRole.OWNER);
  }

  @Test
  public void testGetSubjectPolicyFailNotFound() {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    Urn anotherSeatUrn = new SeatUrn(1L);

    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssSharingDB.getSubjectPolicy(anotherSeatUrn, PolicyType.LEAD_LIST.toString(), RESOURCE_URN));
    }).withCause(new EntityNotFoundException(null, String.format("can not find subjectPolicy for subjectUrn:%s, resourceUrn:%s, policyType:%s",
        anotherSeatUrn, RESOURCE_URN, PolicyType.LEAD_LIST)));
  }

  @Test
  public void testGetSubjectsByResourceEmptySucceed() throws InterruptedException {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    Thread.sleep(500L);
    PaginatedList<Pair<Urn, ShareRole>> resultList =
        await(_lssSharingDB.getPoliciesByResource(RESOURCE_URN, PolicyType.LEAD_LIST, Collections.singleton(ShareRole.OWNER), 0, -1));
    // Since there was no owner added, should be empty
    assertThat(resultList.getTotal()).isEqualTo(0);
    assertThat(resultList.getResult().isEmpty()).isEqualTo(true);
  }

  @Test
  public void testgetResourceSharingPolicyTotal() throws InterruptedException {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    Thread.sleep(500L);
    int resourceSharingTotal = await(_lssSharingDB.getResourceSharingPolicyTotal(RESOURCE_URN, PolicyType.LEAD_LIST));
    assertThat(resourceSharingTotal).isEqualTo(1);
  }

  @Test
  public void testgetResourceSharingPolicyTotal_unsharedAccountMap() throws InterruptedException {
    createSubjectPolicy(ACCOUNT_MAP_POLICY_KEY_1, DEFAULT_SUBJECT_POLICY);
    Thread.sleep(500L);
    int resourceSharingTotal = await(_lssSharingDB.getResourceSharingPolicyTotal(RESOURCE_URN, PolicyType.ACCOUNT_MAP));
    assertThat(resourceSharingTotal).isEqualTo(1);
  }

  @Test
  public void testGetSubjectsByResourceSingleSucceed() throws InterruptedException {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    subjectPolicy.contractUrn = new ContractUrn(CONTRACT_ID).toString();
    PolicyKey key = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
        .setResource(RESOURCE_URN)
        .setSubject(SEAT_URN2);
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    createSubjectPolicy(key, subjectPolicy);
    Thread.sleep(500L);
    PaginatedList<Pair<Urn, ShareRole>> resultList =
        await(_lssSharingDB.getPoliciesByResource(RESOURCE_URN, PolicyType.LEAD_LIST, Collections.singleton(ShareRole.OWNER), 0, -1));
    // Since there was only 1 owner added, should be 1
    assertThat(resultList.getTotal()).isEqualTo(1);
    assertThat(resultList.getResult().size()).isEqualTo(1);
    assertThat(resultList.getResult().get(0).getFirst()).isEqualTo(SEAT_URN2);
    assertThat(resultList.getResult().get(0).getSecond()).isEqualTo(ShareRole.OWNER);
  }

  @Test
  public void testGetSubjectsByResourceMultipleSucceed() throws InterruptedException {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    subjectPolicy.contractUrn = new ContractUrn(CONTRACT_ID).toString();
    PolicyKey key = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
        .setResource(RESOURCE_URN)
        .setSubject(SEAT_URN2);
    Pair<Urn, ShareRole> defaultPolicy = new Pair<>(SEAT_URN, ShareRole.OWNER);
    Pair<Urn, ShareRole> additionalPolicy = new Pair<>(SEAT_URN2, ShareRole.OWNER);
    assertThat(createSubjectPolicy(DEFAULT_KEY, subjectPolicy)).isEqualTo(HttpStatus.S_201_CREATED);
    assertThat(createSubjectPolicy(key, subjectPolicy)).isEqualTo(HttpStatus.S_201_CREATED);
    Thread.sleep(500L);
    PaginatedList<Pair<Urn, ShareRole>> resultList =
        await(_lssSharingDB.getPoliciesByResource(RESOURCE_URN, PolicyType.LEAD_LIST, Collections.singleton(ShareRole.OWNER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(2);
    assertThat(resultList.getResult().size()).isEqualTo(2);
    assertThat(resultList.getResult()).containsExactlyInAnyOrder(defaultPolicy, additionalPolicy);
  }

  @Test
  public void testGetSubjectsByResourceNullRoleMultipleSucceed() throws InterruptedException {
    SubjectPolicy subjectPolicy = new SubjectPolicy();
    subjectPolicy.role = ShareRole.OWNER;
    subjectPolicy.contractUrn = new ContractUrn(CONTRACT_ID).toString();
    PolicyKey key = new PolicyKey().setPolicyType(PolicyType.LEAD_LIST)
        .setResource(RESOURCE_URN)
        .setSubject(SEAT_URN2);
    Pair<Urn, ShareRole> defaultPolicy = new Pair<>(SEAT_URN, ShareRole.READER);
    Pair<Urn, ShareRole> additionalPolicy = new Pair<>(SEAT_URN2, ShareRole.OWNER);
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    createSubjectPolicy(key, subjectPolicy);
    Thread.sleep(500L);
    PaginatedList<Pair<Urn, ShareRole>> resultList =
        await(_lssSharingDB.getPoliciesByResource(RESOURCE_URN, PolicyType.LEAD_LIST, null, 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(2);
    assertThat(resultList.getResult().size()).isEqualTo(2);
    assertThat(resultList.getResult()).containsExactlyInAnyOrder(defaultPolicy, additionalPolicy);
  }

  @Test
  public void testGetSubjectsByResourceEmptyRoleFail() {
    assertThatExceptionOfType(PromiseException.class).isThrownBy(() -> {
      await(_lssSharingDB.getPoliciesByResource(RESOURCE_URN, PolicyType.LEAD_LIST, Collections.emptySet(), 0, -1));
    }).withCause(new RuntimeException("roles can not be empty"));
  }

  @Test
  public void testGetPoliciesBySubjectEmptySucceed() {
    PaginatedList<Pair<Urn, SubjectPolicy>> resultList = await(_lssSharingDB.getPoliciesBySubject(SEAT_URN,
        PolicyType.LEAD_LIST.toString(), Collections.singleton(ShareRole.READER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(0);
    assertThat(resultList.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testGetPoliciesBySubjectSingleSucceed() {
    createSubjectPolicy(DEFAULT_KEY, DEFAULT_SUBJECT_POLICY);
    PaginatedList<Pair<Urn, SubjectPolicy>> resultList = await(
        _lssSharingDB.getPoliciesBySubject(SEAT_URN, PolicyType.LEAD_LIST.toString(), Collections.singleton(ShareRole.READER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(1);
    assertThat(resultList.getResult().size()).isEqualTo(1);
    Pair<Urn, SubjectPolicy> urnSubjectPolicyPair = resultList.getResult().get(0);
    assertThat(urnSubjectPolicyPair.getFirst()).isEqualTo(RESOURCE_URN);
    assertThat(urnSubjectPolicyPair.getSecond().role).isEqualTo(ShareRole.READER);
  }

  @Test
  public void testGetPoliciesByResourceContextAndSubjectEmptySucceed() {
    PaginatedList<Pair<Urn, SubjectPolicy>> resultList = await(_lssSharingDB.getPoliciesByResourceContextAndSubject(
        SEAT_URN, RESOURCE_CONTEXT, PolicyType.NOTE, Collections.singleton(ShareRole.READER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(0);
    assertThat(resultList.getResult().size()).isEqualTo(0);
  }

  @Test
  public void testGetPoliciesByResourceContextAndSubjectShareContractAsSubjectSucceed() {
    createSubjectPolicy(NOTE_SHARE_WITHIN_CONTRACT_KEY, DEFAULT_SUBJECT_POLICY);
    PaginatedList<Pair<Urn, SubjectPolicy>> resultList = await(
        _lssSharingDB.getPoliciesByResourceContextAndSubject(
            CONTRACT_URN, RESOURCE_CONTEXT, PolicyType.NOTE, Collections.singleton(ShareRole.READER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(1);
    assertThat(resultList.getResult().size()).isEqualTo(1);
    Pair<Urn, SubjectPolicy> urnSubjectPolicyPair = resultList.getResult().get(0);
    assertThat(urnSubjectPolicyPair.getFirst()).isEqualTo(NOTE_URN);
    assertThat(urnSubjectPolicyPair.getSecond().role).isEqualTo(ShareRole.READER);
    assertThat(urnSubjectPolicyPair.getSecond().resourceContext.toString()).isEqualTo(RESOURCE_CONTEXT.toString());
  }

  @Test
  public void testGetPoliciesByResourceContextAndSubjectShareSeatAsSubjectSucceed() {
    createSubjectPolicy(NOTE_SHARE_WITH_SEAT_KEY, DEFAULT_SUBJECT_POLICY);
    PaginatedList<Pair<Urn, SubjectPolicy>> resultList = await(
        _lssSharingDB.getPoliciesByResourceContextAndSubject(
            SEAT_URN, RESOURCE_CONTEXT, PolicyType.NOTE, Collections.singleton(ShareRole.READER), 0, -1));
    assertThat(resultList.getTotal()).isEqualTo(1);
    assertThat(resultList.getResult().size()).isEqualTo(1);
    Pair<Urn, SubjectPolicy> urnSubjectPolicyPair = resultList.getResult().get(0);
    assertThat(urnSubjectPolicyPair.getFirst()).isEqualTo(NOTE_URN);
    assertThat(urnSubjectPolicyPair.getSecond().role).isEqualTo(ShareRole.READER);
    assertThat(urnSubjectPolicyPair.getSecond().resourceContext.toString()).isEqualTo(RESOURCE_CONTEXT.toString());
  }

  @Test
  public void testCovertResourcePolicyToQueryMemberUrn() {
    Policy.ResourceContext resourceContext = new Policy.ResourceContext();
    resourceContext.setMember(new MemberUrn(MEMBER_ID1));
    String result = _lssSharingDB.convertResourceContextToQueryString(resourceContext);
    assertThat(result).isEqualTo("resourceContext:\\{member\\=urn\\:li\\:member\\:" + MEMBER_ID1 + "\\}");
  }

  @Test
  public void testCovertResourcePolicyToQueryOrganizationUrn() throws URISyntaxException {
    Policy.ResourceContext resourceContext = new Policy.ResourceContext();
    resourceContext.setOrganization(OrganizationUrn.deserialize("urn:li:organization:3001"));

    String result = _lssSharingDB.convertResourceContextToQueryString(resourceContext);
    assertThat(result).isEqualTo("resourceContext:\\{organization\\=urn\\:li\\:organization\\:3001\\}");
  }

  /**
   * create a subject policy in database
   * @param policyKey document key for subject policy
   * @param subjectPolicy document for subject policy
   * @return true if creation succeed
   */
  private HttpStatus createSubjectPolicy(@NonNull PolicyKey policyKey, @NonNull SubjectPolicy subjectPolicy) {
    return await(_lssSharingDB.updateSubjectPolicy(policyKey, subjectPolicy));
  }
}
