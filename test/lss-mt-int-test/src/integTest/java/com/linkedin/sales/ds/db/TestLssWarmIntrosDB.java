package com.linkedin.sales.ds.db;

import com.linkedin.common.urn.MemberUrn;
import com.linkedin.common.urn.SeatUrn;
import com.linkedin.espresso.pub.operations.DeleteRequest;
import com.linkedin.parseq.Task;
import com.linkedin.restli.common.HttpStatus;
import com.linkedin.sales.common.IntegrationTestBase;
import com.linkedin.sales.espresso.WarmIntroRecommendation;
import java.util.List;
import java.util.Optional;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import proto.com.linkedin.saleswarmintros.IntroducerRecommendationStatus;

import static org.junit.Assert.*;


/**
 * Test class for LssWarmIntrosDB
 */
public class TestLssWarmIntrosDB extends IntegrationTestBase {

  private static final MemberUrn leadMemberUrn = new MemberUrn(111L);
  private static final MemberUrn introducerMemberUrn = new MemberUrn(222L);
  private static final SeatUrn seatUrn = new SeatUrn(123L);

  private LssWarmIntrosDB _lssWarmIntrosDB;

  @BeforeClass
  public void setUp() throws Exception {
    super.setUp();
    _lssWarmIntrosDB = new LssWarmIntrosDB(_parSeqEspressoClient);
    _offspringTestHelper.startGenerator();
  }

  @AfterClass
  public void tearDown() throws Exception {
    cleanAllData();
    super.tearDown();
  }

  @BeforeMethod
  public void cleanAllData() {
    await(_parSeqEspressoClient.execute(DeleteRequest.builder(
        LssWarmIntrosDB.DB_LSS_WARM_INTRO_RECOMMENDATION, LssWarmIntrosDB.TABLE_WARM_INTRO_RECOMMENDATION,
        seatUrn.toString(), leadMemberUrn.toString()).build()));
  }

  @Test
  public void testCreateAndGetWarmIntroRecommendation() {
    cleanAllData();

    // Step 1: Create warm intro recommendation
    WarmIntroRecommendation espressoWarmIntroRecommendation = buildEspressoWarmIntroRecommendation();
    HttpStatus createStatus = await(_lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(seatUrn, leadMemberUrn, espressoWarmIntroRecommendation));
    assertEquals(HttpStatus.S_201_CREATED, createStatus);

    // Step 2: Fetch record
    Task<Optional<WarmIntroRecommendation>> resultTask = _lssWarmIntrosDB.getWarmIntroRecommendation(seatUrn, leadMemberUrn);
    Optional<WarmIntroRecommendation> result = await(resultTask);

    // Step 3: Verify
    assertNotNull(result);
    assertTrue(result.isPresent());
    assertEquals(1, result.get().getIntroducerRecommendations().size());
    assertEquals(7, result.get().getIntroducerRecommendations().get(0).getScore());

    // Step 4: Update warm intro recommendation
    WarmIntroRecommendation updatedRecommendation = buildEspressoWarmIntroRecommendation();
    updatedRecommendation.getIntroducerRecommendations().get(0).setScore(9);
    updatedRecommendation.getIntroducerRecommendations().get(0).setRationale("UPDATED_RATIONALE");
    HttpStatus updateStatus = await(_lssWarmIntrosDB.createOrUpdateWarmIntroRecommendation(seatUrn, leadMemberUrn, updatedRecommendation));
    assertEquals(HttpStatus.S_200_OK, updateStatus);

    // Step 5: Fetch updated record and verify
    Task<Optional<WarmIntroRecommendation>> updatedResultTask = _lssWarmIntrosDB.getWarmIntroRecommendation(seatUrn, leadMemberUrn);
    Optional<WarmIntroRecommendation> updatedResult = await(updatedResultTask);
    assertNotNull(updatedResult);
    assertTrue(updatedResult.isPresent());
    assertEquals(1, updatedResult.get().getIntroducerRecommendations().size());
    assertEquals(9, updatedResult.get().getIntroducerRecommendations().get(0).getScore());
    assertEquals("UPDATED_RATIONALE", updatedResult.get().getIntroducerRecommendations().get(0).getRationale().toString());
  }

  private com.linkedin.sales.espresso.WarmIntroRecommendation buildEspressoWarmIntroRecommendation() {
    com.linkedin.sales.espresso.WarmIntroRecommendation dbRecommendation =
        new com.linkedin.sales.espresso.WarmIntroRecommendation();
    com.linkedin.sales.espresso.IntroducerRecommendation dbIntroducer =
        new com.linkedin.sales.espresso.IntroducerRecommendation();
    dbIntroducer.setIntroducer(introducerMemberUrn.toString());
    dbIntroducer.setRationale("RATIONALE");
    dbIntroducer.setScore(7);
    dbIntroducer.setVariant("VARIANT");
    dbIntroducer.setStatus(IntroducerRecommendationStatus.IntroducerRecommendationStatus_RECOMMENDED.toString());
    dbIntroducer.setCreatedTime(12345L);
    dbIntroducer.setModifiedTime(23456L);
    dbRecommendation.setIntroducerRecommendations(List.of(dbIntroducer));
    return dbRecommendation;
  }
}
