import com.github.spotbugs.snom.SpotBugsTask


apply plugin: 'li-java'
apply plugin: 'li-integ-test'

dependencies {
  api project(':impl:factory')
  api spec.external.testng
  api spec.external.'spotbugs-annotations'
  api spec.external.'parseq-test-api'

  testImplementation spec.external.'assertj-core'
  api spec.external.'mockito-inline'
}

tasks.withType(JavaCompile).configureEach {
  options.fork = true
  options.forkOptions.memoryMaximumSize = '4g'
}

tasks.withType(SpotBugsTask).configureEach {
  maxHeapSize = '4g'
}

