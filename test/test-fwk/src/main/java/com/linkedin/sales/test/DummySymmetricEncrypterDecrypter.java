package com.linkedin.sales.test;

import com.linkedin.security.crypto.CryptoException;
import com.linkedin.security.crypto.ISymmetricEncrypterDecrypter;
import com.linkedin.security.crypto.secretmgmt.SecretAlias;
import com.ning.http.util.Base64;
import org.xeril.util.clock.Timespan;


public class DummySymmetricEncrypterDecrypter implements ISymmetricEncrypterDecrypter {

  @Override
  public byte[] encrypt(byte[] plainText) throws CryptoException {
    return plainText;
  }

  @Override
  public String encryptToBase64(byte[] plainText) throws CryptoException {
    return Base64.encode(plainText);
  }

  @Override
  public byte[] decrypt(byte[] cipherText) throws CryptoException {
    return cipherText;
  }

  @Override
  public byte[] decrypt(byte[] cipherText, Timespan ttl) throws CryptoException {
    return cipherText;
  }

  @Override
  public byte[] decryptFromBase64(String cipherText) throws CryptoException {
    return Base64.decode(cipherText);
  }

  @Override
  public byte[] decryptFromBase64(String cipherText, Timespan ttl) throws CryptoException {
    return Base64.decode(cipherText);
  }

  @Override
  public void setSecretAlias(SecretAlias secretAlias) {
  }
}
