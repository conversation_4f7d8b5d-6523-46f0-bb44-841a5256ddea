package com.linkedin.sales.test;

import com.linkedin.common.url.Url;
import com.linkedin.common.urn.EnterpriseApplicationInstanceUrn;
import com.linkedin.common.urn.EnterpriseApplicationUrn;
import com.linkedin.common.urn.EnterpriseApplicationUsageUrn;
import com.linkedin.common.urn.EnterpriseProfileUrn;
import com.linkedin.common.urn.Urn;
import com.linkedin.enterprise.account.ApplicationInstance;
import com.linkedin.enterprise.identity.ActivationLink;
import com.linkedin.enterprise.identity.Profile;
import com.linkedin.enterprise.license.LicenseAssignment;
import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Response;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.sales.admin.SalesAccount;
import com.linkedin.sales.admin.SalesContract;
import com.linkedin.sales.admin.SalesSeat;
import com.linkedin.sales.client.EmailClient;
import com.linkedin.sales.client.common.SalesAccountsV2Client;
import com.linkedin.sales.client.common.SalesSeatClient;
import com.linkedin.sales.client.ep.EnterprisePlatformClient;
import com.linkedin.sales.client.ep.EnterpriseProfileActivationLinksClient;
import com.linkedin.sales.service.SalesContractService;
import com.linkedin.sales.service.SalesCryptoService;
import com.linkedin.sales.service.SalesInviteRegisterUrlService;
import com.linkedin.util.Pair;
import java.util.List;
import java.util.Optional;
import org.mockito.Mockito;

import static com.linkedin.sales.service.SalesNavigatorEmailService.*;


public final class MockUtils {
  private MockUtils() {
  }


  public static ParSeqRestClient mockParSeqRestClientCreateTaskWithAny(Response<IdResponse<String>> response) {
    ParSeqRestClient parSeqRestClient = Mockito.mock(ParSeqRestClient.class);
    Mockito.doReturn(Task.value(response))
        .when(parSeqRestClient).createTask(Mockito.any());
    return parSeqRestClient;
  }


  public static <T> ParSeqRestClient mockParSeqRestClientCreateTaskWithAnyCustomResponse(Response<T> response) {
    ParSeqRestClient parSeqRestClient = Mockito.mock(ParSeqRestClient.class);
    Mockito.doReturn(Task.value(response))
        .when(parSeqRestClient).createTask(Mockito.any());
    return parSeqRestClient;
  }

  public static SalesAccountsV2Client mockSalesAccountClientGetAccountByIdForWelcomeEmail(SalesAccount account) {
    SalesAccountsV2Client client = Mockito.mock(SalesAccountsV2Client.class);
    Mockito.doReturn(Task.value(account))
        .when(client).getAccount(account.getId(), new EnterpriseApplicationUsageUrn(
            new EnterpriseApplicationUrn("salesNavigator"), "sendWelcomeEmail"), ACCOUNT_V2_PATH_SPECS);
    return client;
  }

  public static SalesAccountsV2Client mockSalesAccountClientGetAccountByIdForInviteEmail(SalesAccount account) {
    SalesAccountsV2Client client = Mockito.mock(SalesAccountsV2Client.class);
    Mockito.doReturn(Task.value(account))
        .when(client).getAccount(account.getId(), new EnterpriseApplicationUsageUrn(
            new EnterpriseApplicationUrn("salesNavigator"), "sendInvitationEmail"), ACCOUNT_V2_PATH_SPECS);
    return client;
  }

  public static SalesContractService mockSalesContractServiceGetContractByIdWithAny(SalesContract contract) {
    SalesContractService scService = Mockito.mock(SalesContractService.class);
    Mockito.doReturn(Task.value(contract))
        .when(scService).getSalesContractById(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    Mockito.doReturn(Task.value(contract))
        .when(scService).getSalesContractById(Mockito.any(), Mockito.any());
    return scService;
  }

  public static SalesContractService mockSalesContractServiceGetContractByIdFailed() {
    SalesContractService scService = Mockito.mock(SalesContractService.class);
    Mockito.doReturn(Task.failure(new RuntimeException("test mockSalesContractServiceGetContractByIdFailed")))
        .when(scService).getSalesContractById(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    return scService;
  }

  public static SalesSeatClient mockSeatClientGetSeatWithAny(SalesSeat salesSeat) {
    SalesSeatClient seatClient = Mockito.mock(SalesSeatClient.class);

    Mockito.doReturn(Task.value(salesSeat))
        .when(seatClient)
        .getSeat(Mockito.any(), Mockito.any(), Mockito.any(EnterpriseApplicationUsageUrn.class), Mockito.any());

    return seatClient;
  }

  public static EnterpriseProfileActivationLinksClient mockActivationLinkClientWithGenerateActivationLinkAny(ActivationLink link) {
    EnterpriseProfileActivationLinksClient activationLinksClient = Mockito.mock(EnterpriseProfileActivationLinksClient.class);

    Mockito.doReturn(Task.value(link))
        .when(activationLinksClient)
        .generateCheckpointEnterpriseLoginUrl(Mockito.any(
            EnterpriseApplicationUrn.class),
            Mockito.any(EnterpriseApplicationInstanceUrn.class),
            Mockito.any(EnterpriseProfileUrn.class),
            Mockito.any(Url.class),
            Mockito.any(),
            Mockito.any(Urn.class));

    return activationLinksClient;
  }

  public static SalesInviteRegisterUrlService mockInviteRegisterUrlServiceByUrlWithAny(String urlStr) {
    SalesInviteRegisterUrlService service = Mockito.mock(SalesInviteRegisterUrlService.class);

    Mockito.doReturn(urlStr)
        .when(service).generateLighthouseFrontendRegistrationUrl(Mockito.any(), Mockito.any());

    return service;
  }

  public static SalesCryptoService getSimpleSalesCryptoService() {
    return new SalesCryptoService(new DummySymmetricEncrypterDecrypter());
  }

  /**
   *
   */
  public static class EnterprisePlatformClientMockBuilder {
    private final EnterprisePlatformClient _enterprisePlatformClient = Mockito.mock(EnterprisePlatformClient.class);


    //mock EnterprisePlatformClient::getEnterpriseProfile()
    public EnterprisePlatformClientMockBuilder mockGetEnterpriseProfileByAny(Task<Profile> returnedVal) {
      Mockito.doReturn(returnedVal)
          .when(_enterprisePlatformClient).getEnterpriseProfile(Mockito.any(), Mockito.any(), Mockito.any());
      return this;
    }


    //mock EnterprisePlatformClient::getEnterpriseProfile()
    @SafeVarargs
    public final EnterprisePlatformClientMockBuilder mockGetEnterpriseProfile(
        Pair<EnterpriseProfileUrn, Task<Profile>>... kvs) {

      for (Pair<EnterpriseProfileUrn, Task<Profile>> pair : kvs) {
        EnterpriseProfileUrn urn = pair.getFirst();
        Task<Profile> returnedVal = pair.getSecond();
        Mockito.doReturn(returnedVal)
            .when(_enterprisePlatformClient).getEnterpriseProfile(Mockito.eq(urn), Mockito.any(), Mockito.any());
      }
      return this;
    }

    //mock EnterprisePlatformClient::findLicenseAssignments()
    public EnterprisePlatformClientMockBuilder mockFindLicenseAssignments(Task<List<LicenseAssignment>> returnedVal) {
      Mockito.doReturn(returnedVal)
          .when(_enterprisePlatformClient).findLicenseAssignments(Mockito.any(), Mockito.any());
      return this;
    }

    public EnterprisePlatformClientMockBuilder mockGetEpMappedContractUrn(Task<Optional<ApplicationInstance>> returnedVal) {
      Mockito.doReturn(returnedVal)
          .when(_enterprisePlatformClient).getAppInstanceWithMappedContract(Mockito.any(), Mockito.any());
      return this;
    }

    public EnterprisePlatformClientMockBuilder mockGetEpMappedContractUrnFailed() {
      Mockito.doReturn(Task.failure(new RuntimeException("test mockGetEpMappedContractUrnFailed")))
          .when(_enterprisePlatformClient).getAppInstanceWithMappedContract(Mockito.any(), Mockito.any());
      return this;
    }

    public EnterprisePlatformClient build() {
      return _enterprisePlatformClient;
    }
  }
  public static EnterprisePlatformClientMockBuilder getEnterpriseMappingClientMockBuilder() {
    return new EnterprisePlatformClientMockBuilder();
  }

  /**
   *
   */
  public static class EmailClientMockBuilder {
    private final EmailClient _emailClient = Mockito.mock(EmailClient.class);

    public EmailClient build() {
      return _emailClient;
    }
  }


}


