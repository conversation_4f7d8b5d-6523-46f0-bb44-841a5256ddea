package com.linkedin.sales.test;

import com.linkedin.parseq.Task;
import com.linkedin.restli.client.ParSeqRestClient;
import com.linkedin.restli.client.Request;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;


public class ParSeqRestClientMockBuilder {

  public static ParSeqRestClientMockBuilder getInstance() {
    return new ParSeqRestClientMockBuilder();
  }

  private final ParSeqRestClient parSeqRestClient = Mockito.mock(ParSeqRestClient.class);

  public <T extends Request, R> ParSeqRestClientMockBuilder mockCreateTask(Class<T> requestClass, Task<R> returnedVal) {
    Mockito.doReturn(returnedVal)
        .when(parSeqRestClient)
        .createTask(ArgumentMatchers.nullable(requestClass));
    return this;
  }


  public ParSeqRestClient build() {
    return parSeqRestClient;
  }
}


