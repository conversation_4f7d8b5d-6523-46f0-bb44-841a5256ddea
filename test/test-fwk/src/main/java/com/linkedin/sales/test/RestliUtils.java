package com.linkedin.sales.test;

import com.linkedin.restli.client.Response;
import com.linkedin.restli.client.RestLiResponseException;
import com.linkedin.restli.common.IdResponse;
import com.linkedin.restli.internal.client.ResponseImpl;
import edu.umd.cs.findbugs.annotations.NonNull;
import java.net.HttpCookie;
import java.util.Collections;


public final class RestliUtils {
  private RestliUtils() {
  }

  public static <T> IdResponse<T> newSuccessIdResponse(@NonNull T key) {
    return new IdResponse<>(key);
  }

  public static <T> Response<T> newSuccessResponse(@NonNull T entity) {
    return newResponse(200, entity, null);
  }

  public static <T> Response<T> newFailureResponse(int status, @NonNull RestLiResponseException error) {
    return newResponse(status, null, error);
  }

  public static <T> Response<T> newResponse(int status, T entity, RestLiResponseException error) {
    return new ResponseImpl<T>(
        status,
        Collections.<String, String>emptyMap(),
        Collections.<HttpCookie>emptyList(),
        entity,
        error
    );
  }

  public static Response<Void> newVoidResponse(int status, RestLiResponseException error) {
    return new ResponseImpl<Void>(
        status,
        Collections.<String, String>emptyMap(),
        Collections.<HttpCookie>emptyList(),
        null,
        error
    );
  }
}
