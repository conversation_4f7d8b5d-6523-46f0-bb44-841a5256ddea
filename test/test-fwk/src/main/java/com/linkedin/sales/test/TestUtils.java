package com.linkedin.sales.test;

import com.linkedin.data.DataMap;
import com.linkedin.data.schema.validation.RequiredMode;
import com.linkedin.data.schema.validation.ValidateDataAgainstSchema;
import com.linkedin.data.schema.validation.ValidationOptions;
import com.linkedin.data.schema.validation.ValidationResult;
import com.linkedin.data.template.DataTemplateUtil;
import com.linkedin.data.template.RecordTemplate;
import com.linkedin.restli.internal.server.util.DataMapUtils;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public final class TestUtils {

  private TestUtils() {
  }

  /**
   * Converts a list of record templates to a map keyed by their id field
   * @param results list of record template objects containing an id field
   * @param <T> record template that must have an id field
   * @return map keyed by an id field
   */
  public static <T extends RecordTemplate> Map<Object, T> listToMap(Collection<T> results) {
    return results.stream().collect(Collectors.toMap(rt -> rt.data().get("id"), Function.identity()));
  }

  public static <V extends RecordTemplate> V loadFromJsonResource(String resourceDataPath, Class<V> clazz) {
    DataMap dataMap = DataMapUtils.readMap(TestUtils.class.getResourceAsStream(resourceDataPath));
    ValidationResult validationResult = ValidateDataAgainstSchema.validate(dataMap, DataTemplateUtil.getSchema(clazz),
        new ValidationOptions(RequiredMode.IGNORE));

    return DataTemplateUtil.wrap(validationResult.getFixed(), clazz);
  }
}
