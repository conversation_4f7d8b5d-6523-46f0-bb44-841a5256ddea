{"e2e-canary-validation": {"commands": ["bash ./scripts/e2e-validation.sh"], "variant": {"r_os": "linux", "os_release": "mariner2"}, "triggers": [{"type": "on_deploy", "fabrics": ["prod-ltx1", "prod-lor1", "prod-lva1"], "matchOn": {"action": "DEPLOY_CANARY"}}], "task_properties": {"deployment_execution_priority": 2, "task_timeout": 7200}, "archive_globs": ["e2e-tests/test-results/**/*"], "env_vars": [{"name": "CI", "value": "true"}, {"name": "TMC", "value": "true"}], "notification": [{"type": "iris", "plan": "checkly-canary-validation-alerts-lss-pages-oncall"}], "execute_options": {"allow_concurrent_executions": false}}}