apply plugin: 'li-jetty'
apply plugin: 'li-epsilon-plugin'

dependencies {
  api project(':database:LssProspectingAgent-mysql')
  runtimeOnly project(':impl:factory')
  runtimeOnly project(path: ':impl:factory', configuration: 'cmpt-def')
  runtimeOnly project(":impl:rest-impl$scalaSuffix")
  runtimeOnly spec.product.'interservice-auth'.'interservice-auth-datavault-authz-factory'
  runtimeOnly spec.product.'container-core'.'container-jmx-servlet-api'
  runtimeOnly spec.external.log4j2Api
  runtimeOnly spec.external.log4j2Core
  runtimeOnly spec.external.disruptor
}

liWar { isOffspring = true }
liWar { loggingLibrary = 'log4j2' }
war {
  webInf {
    from '../../database'
    include '**/*.avsc'
  }
}
