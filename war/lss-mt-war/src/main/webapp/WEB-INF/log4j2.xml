<?xml version="1.0" encoding="UTF-8"?>
<Configuration strict="true" packages="com.linkedin.util.log.log4j2.plugins">
  <Filters>
    @log4j2.filters.effective_level@
  </Filters>
  <Appenders>
    @log4j2.appenders.webapp@
    @log4j2.appenders.public_access@
  </Appenders>
  <Loggers>
    @log4j2.loggers.public_access@
    @log4j2.loggers.spring@
    @log4j2.loggers.root@
  </Loggers>
</Configuration>