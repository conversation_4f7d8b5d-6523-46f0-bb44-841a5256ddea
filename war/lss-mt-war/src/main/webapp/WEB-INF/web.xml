<?xml version="1.0" encoding="UTF-8"?><web-app>
  <display-name>Sample Restli Server</display-name>
  <description>Sample Restli Server to demonstrate offspring usage</description>

  <!-- ******************************************************************* -->
  <!-- * Context params * -->
  <!-- ******************************************************************* -->

  <context-param>
    <param-name>bootListeners</param-name>
    <param-value>com.linkedin.sales.factory.common.BridgedGrpcServiceBootListener</param-value>
  </context-param>

  <!-- ******************************************************************* -->
  <!-- * Listeners * -->
  <!-- ******************************************************************* -->

  <!-- Initializes log4j2 -->
  <listener>
    <listener-class>com.linkedin.util.servlet.log4j2.Log4j2Initializer</listener-class>
  </listener>

  <!-- Main Listener which uses the 'components' context param to initialize itself -->
  <listener>
    <listener-class>com.linkedin.offspring.servlet.OffspringServletContextListener</listener-class>
  </listener>

  <!-- servlet definitions -->

  <servlet>
    <servlet-name>AdminServlet</servlet-name>
    <servlet-class>com.linkedin.spring.servlet.admin.ServerAdminServlet</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet>
    <servlet-name>RestliServlet</servlet-name>
    <servlet-class>com.linkedin.restli.server.factory.RestliFactoryServlet</servlet-class>
    <load-on-startup>1</load-on-startup>
  </servlet>

  <servlet>
    <servlet-name>JMXServlet</servlet-name>
    <servlet-class>com.linkedin.container.jmx.servlet.JMXServlet</servlet-class>
  </servlet>

  <!-- servlet mappings -->

  <servlet-mapping>
    <servlet-name>AdminServlet</servlet-name>
    <url-pattern>/admin</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>RestliServlet</servlet-name>
    <url-pattern>/*</url-pattern>
  </servlet-mapping>

  <servlet-mapping>
    <servlet-name>JMXServlet</servlet-name>
    <url-pattern>/jmx/*</url-pattern>
  </servlet-mapping>

</web-app>